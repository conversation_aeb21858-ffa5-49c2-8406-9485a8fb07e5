package model

import (
	"context"
	"database/sql"
	"fmt"
	"phoenix/service/saas/model/ent"
	"phoenix/service/saas/model/ent/hook"
	"phoenix/service/saas/utils"

	entsql "entgo.io/ent/dialect/sql"
	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

type DBConfig struct {
	Username     string
	Password     string
	Host         string
	Port         int
	DBName       string
	Type         string
	MaxOpenConns int
}

// newEntDB
func NewEntDB(c DBConfig) *ent.Client {
	dsn := fmt.Sprintf("%s:%s@tcp(%s:%v)/%s?charset=utf8&parseTime=True&loc=Local",
		c.Username, c.Password,
		c.Host, c.Port,
		c.DBName)
	db, err := sql.Open(c.Type, dsn)
	if err != nil {
		panic(err)
	}

	db.SetMaxOpenConns(c.<PERSON>on<PERSON>)
	driver := entsql.OpenDB(c.Type, db)

	client := ent.NewClient(
		ent.Log(logx.Info), // logger
		ent.Driver(driver),
		ent.Debug(), // debug mode
	)

	// user表加入拦截器、钩子
	// 处理敏感字段加密、解密
	client.User.Intercept(ent.InterceptFunc(func(next ent.Querier) ent.Querier {
		return ent.QuerierFunc(func(ctx context.Context, query ent.Query) (ent.Value, error) {
			// Do something before the query execution.
			value, err := next.Query(ctx, query)
			// Do something after the query execution.
			switch value.(type) {
			case *ent.User:
				sm4Tool := utils.NewSm4Tool()
				user := value.(ent.User)
				b, err := sm4Tool.Decrypt(user.Username)
				if err != nil {
					return value, nil
				}
				user.Username = string(b)

				b, err = sm4Tool.Decrypt(user.Mobile)
				if err != nil {
					return value, nil
				}
				user.Mobile = string(b)
			case []*ent.User:
				sm4Tool := utils.NewSm4Tool()
				users := value.([]*ent.User)
				for _, u := range users {
					b, err := sm4Tool.Decrypt(u.Username)
					if err != nil {
						continue
					}
					u.Username = string(b)

					b, err = sm4Tool.Decrypt(u.Mobile)
					if err != nil {
						continue
					}
					u.Mobile = string(b)
				}

			}

			return value, err
		})
	}),
	)

	client.User.Use(hook.On(func(next ent.Mutator) ent.Mutator {
		// Use the "<project>/ent/hook" to get the concrete type of the mutation.
		return hook.UserFunc(func(ctx context.Context, m *ent.UserMutation) (ent.Value, error) {
			// 加密username
			username, ok := encryptField(ctx, "username", m)
			if ok {
				m.SetUsername(username)
			}

			// 加密mobile
			mobile, ok := encryptField(ctx, "mobile", m)
			if ok {
				m.SetMobile(mobile)
			}

			return next.Mutate(ctx, m)
		})
	}, ent.OpCreate|ent.OpUpdateOne|ent.OpUpdate))

	return client
}

func encryptField(ctx context.Context, field string, m *ent.UserMutation) (string, bool) {
	fieldValue, ok := m.Field(field)
	if !ok {
		return "", false
	}
	var value string
	if value, ok = fieldValue.(string); !ok {
		return "", false
	}
	encryptedValue, err := utils.NewSm4Tool().Encrypt([]byte(value))
	if err != nil {
		return "", false
	}
	return string(encryptedValue), true
}

func NewGormDB(c DBConfig) (result *gorm.DB) {

	dsn := fmt.Sprintf("%s:%s@tcp(%s:%v)/%s?charset=utf8&parseTime=True&loc=Local",
		c.Username, c.Password,
		c.Host, c.Port,
		c.DBName)

	db, err := gorm.Open(mysql.New(mysql.Config{
		DSN:                       dsn,
		DisableDatetimePrecision:  true,
		DontSupportRenameIndex:    true,
		DontSupportRenameColumn:   true,
		SkipInitializeWithVersion: false,
	}))
	if err != nil {
		panic("init db fail:" + err.Error())
	}
	// 维护连接池
	sqlDB, err := db.DB()
	if err != nil {
		panic("init db fail:" + err.Error())
	}

	// SetMaxOpenConns 设置打开数据库连接的最大数量。
	sqlDB.SetMaxOpenConns(c.MaxOpenConns)

	return db
}
