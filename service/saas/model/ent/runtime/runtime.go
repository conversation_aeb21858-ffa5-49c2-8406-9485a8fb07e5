// Code generated by ent, DO NOT EDIT.

package runtime

import (
	"phoenix/service/saas/model/ent/api"
	"phoenix/service/saas/model/ent/application"
	"phoenix/service/saas/model/ent/button"
	"phoenix/service/saas/model/ent/file"
	"phoenix/service/saas/model/ent/group"
	"phoenix/service/saas/model/ent/grouptype"
	"phoenix/service/saas/model/ent/menu"
	"phoenix/service/saas/model/ent/organization"
	"phoenix/service/saas/model/ent/organizationuserinfo"
	"phoenix/service/saas/model/ent/position"
	"phoenix/service/saas/model/ent/role"
	"phoenix/service/saas/model/ent/schema"
	"phoenix/service/saas/model/ent/tenant"
	"phoenix/service/saas/model/ent/tenantuserinfo"
	"phoenix/service/saas/model/ent/token"
	"phoenix/service/saas/model/ent/user"
	"time"

	uuid "github.com/gofrs/uuid/v5"
)

// The init function reads all schema descriptors with runtime code
// (default values, validators, hooks and policies) and stitches it
// to their package variables.
func init() {
	apiMixin := schema.API{}.Mixin()
	apiMixinHooks3 := apiMixin[3].Hooks()
	api.Hooks[0] = apiMixinHooks3[0]
	apiMixinInters3 := apiMixin[3].Interceptors()
	api.Interceptors[0] = apiMixinInters3[0]
	apiMixinFields0 := apiMixin[0].Fields()
	_ = apiMixinFields0
	apiMixinFields1 := apiMixin[1].Fields()
	_ = apiMixinFields1
	apiMixinFields2 := apiMixin[2].Fields()
	_ = apiMixinFields2
	apiFields := schema.API{}.Fields()
	_ = apiFields
	// apiDescCreatedAt is the schema descriptor for created_at field.
	apiDescCreatedAt := apiMixinFields1[0].Descriptor()
	// api.DefaultCreatedAt holds the default value on creation for the created_at field.
	api.DefaultCreatedAt = apiDescCreatedAt.Default.(func() time.Time)
	// apiDescUpdatedAt is the schema descriptor for updated_at field.
	apiDescUpdatedAt := apiMixinFields1[1].Descriptor()
	// api.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	api.DefaultUpdatedAt = apiDescUpdatedAt.Default.(func() time.Time)
	// api.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	api.UpdateDefaultUpdatedAt = apiDescUpdatedAt.UpdateDefault.(func() time.Time)
	// apiDescStatus is the schema descriptor for status field.
	apiDescStatus := apiMixinFields2[0].Descriptor()
	// api.DefaultStatus holds the default value on creation for the status field.
	api.DefaultStatus = apiDescStatus.Default.(bool)
	// apiDescMethod is the schema descriptor for method field.
	apiDescMethod := apiFields[3].Descriptor()
	// api.DefaultMethod holds the default value on creation for the method field.
	api.DefaultMethod = apiDescMethod.Default.(string)
	// apiDescID is the schema descriptor for id field.
	apiDescID := apiMixinFields0[0].Descriptor()
	// api.DefaultID holds the default value on creation for the id field.
	api.DefaultID = apiDescID.Default.(func() string)
	applicationMixin := schema.Application{}.Mixin()
	applicationMixinHooks4 := applicationMixin[4].Hooks()
	applicationMixinHooks5 := applicationMixin[5].Hooks()
	application.Hooks[0] = applicationMixinHooks4[0]
	application.Hooks[1] = applicationMixinHooks5[0]
	applicationMixinInters4 := applicationMixin[4].Interceptors()
	applicationMixinInters5 := applicationMixin[5].Interceptors()
	application.Interceptors[0] = applicationMixinInters4[0]
	application.Interceptors[1] = applicationMixinInters5[0]
	applicationMixinFields0 := applicationMixin[0].Fields()
	_ = applicationMixinFields0
	applicationMixinFields1 := applicationMixin[1].Fields()
	_ = applicationMixinFields1
	applicationMixinFields2 := applicationMixin[2].Fields()
	_ = applicationMixinFields2
	applicationMixinFields3 := applicationMixin[3].Fields()
	_ = applicationMixinFields3
	applicationFields := schema.Application{}.Fields()
	_ = applicationFields
	// applicationDescCreatedAt is the schema descriptor for created_at field.
	applicationDescCreatedAt := applicationMixinFields1[0].Descriptor()
	// application.DefaultCreatedAt holds the default value on creation for the created_at field.
	application.DefaultCreatedAt = applicationDescCreatedAt.Default.(func() time.Time)
	// applicationDescUpdatedAt is the schema descriptor for updated_at field.
	applicationDescUpdatedAt := applicationMixinFields1[1].Descriptor()
	// application.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	application.DefaultUpdatedAt = applicationDescUpdatedAt.Default.(func() time.Time)
	// application.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	application.UpdateDefaultUpdatedAt = applicationDescUpdatedAt.UpdateDefault.(func() time.Time)
	// applicationDescStatus is the schema descriptor for status field.
	applicationDescStatus := applicationMixinFields2[0].Descriptor()
	// application.DefaultStatus holds the default value on creation for the status field.
	application.DefaultStatus = applicationDescStatus.Default.(bool)
	// applicationDescSort is the schema descriptor for sort field.
	applicationDescSort := applicationMixinFields3[0].Descriptor()
	// application.DefaultSort holds the default value on creation for the sort field.
	application.DefaultSort = applicationDescSort.Default.(uint32)
	// applicationDescName is the schema descriptor for name field.
	applicationDescName := applicationFields[0].Descriptor()
	// application.DefaultName holds the default value on creation for the name field.
	application.DefaultName = applicationDescName.Default.(string)
	// applicationDescAppID is the schema descriptor for app_id field.
	applicationDescAppID := applicationFields[1].Descriptor()
	// application.DefaultAppID holds the default value on creation for the app_id field.
	application.DefaultAppID = applicationDescAppID.Default.(func() string)
	// applicationDescSecret is the schema descriptor for secret field.
	applicationDescSecret := applicationFields[2].Descriptor()
	// application.DefaultSecret holds the default value on creation for the secret field.
	application.DefaultSecret = applicationDescSecret.Default.(func() string)
	// applicationDescRemark is the schema descriptor for remark field.
	applicationDescRemark := applicationFields[3].Descriptor()
	// application.DefaultRemark holds the default value on creation for the remark field.
	application.DefaultRemark = applicationDescRemark.Default.(string)
	// applicationDescID is the schema descriptor for id field.
	applicationDescID := applicationMixinFields0[0].Descriptor()
	// application.DefaultID holds the default value on creation for the id field.
	application.DefaultID = applicationDescID.Default.(func() string)
	buttonMixin := schema.Button{}.Mixin()
	buttonMixinHooks3 := buttonMixin[3].Hooks()
	button.Hooks[0] = buttonMixinHooks3[0]
	buttonMixinInters3 := buttonMixin[3].Interceptors()
	button.Interceptors[0] = buttonMixinInters3[0]
	buttonMixinFields0 := buttonMixin[0].Fields()
	_ = buttonMixinFields0
	buttonMixinFields1 := buttonMixin[1].Fields()
	_ = buttonMixinFields1
	buttonMixinFields2 := buttonMixin[2].Fields()
	_ = buttonMixinFields2
	buttonFields := schema.Button{}.Fields()
	_ = buttonFields
	// buttonDescCreatedAt is the schema descriptor for created_at field.
	buttonDescCreatedAt := buttonMixinFields1[0].Descriptor()
	// button.DefaultCreatedAt holds the default value on creation for the created_at field.
	button.DefaultCreatedAt = buttonDescCreatedAt.Default.(func() time.Time)
	// buttonDescUpdatedAt is the schema descriptor for updated_at field.
	buttonDescUpdatedAt := buttonMixinFields1[1].Descriptor()
	// button.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	button.DefaultUpdatedAt = buttonDescUpdatedAt.Default.(func() time.Time)
	// button.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	button.UpdateDefaultUpdatedAt = buttonDescUpdatedAt.UpdateDefault.(func() time.Time)
	// buttonDescSort is the schema descriptor for sort field.
	buttonDescSort := buttonMixinFields2[0].Descriptor()
	// button.DefaultSort holds the default value on creation for the sort field.
	button.DefaultSort = buttonDescSort.Default.(uint32)
	// buttonDescName is the schema descriptor for name field.
	buttonDescName := buttonFields[0].Descriptor()
	// button.DefaultName holds the default value on creation for the name field.
	button.DefaultName = buttonDescName.Default.(string)
	// buttonDescID is the schema descriptor for id field.
	buttonDescID := buttonMixinFields0[0].Descriptor()
	// button.DefaultID holds the default value on creation for the id field.
	button.DefaultID = buttonDescID.Default.(func() string)
	fileMixin := schema.File{}.Mixin()
	fileMixinHooks4 := fileMixin[4].Hooks()
	fileMixinHooks5 := fileMixin[5].Hooks()
	file.Hooks[0] = fileMixinHooks4[0]
	file.Hooks[1] = fileMixinHooks5[0]
	fileMixinInters4 := fileMixin[4].Interceptors()
	fileMixinInters5 := fileMixin[5].Interceptors()
	file.Interceptors[0] = fileMixinInters4[0]
	file.Interceptors[1] = fileMixinInters5[0]
	fileMixinFields0 := fileMixin[0].Fields()
	_ = fileMixinFields0
	fileMixinFields1 := fileMixin[1].Fields()
	_ = fileMixinFields1
	fileMixinFields2 := fileMixin[2].Fields()
	_ = fileMixinFields2
	fileMixinFields3 := fileMixin[3].Fields()
	_ = fileMixinFields3
	fileFields := schema.File{}.Fields()
	_ = fileFields
	// fileDescCreatedAt is the schema descriptor for created_at field.
	fileDescCreatedAt := fileMixinFields1[0].Descriptor()
	// file.DefaultCreatedAt holds the default value on creation for the created_at field.
	file.DefaultCreatedAt = fileDescCreatedAt.Default.(func() time.Time)
	// fileDescUpdatedAt is the schema descriptor for updated_at field.
	fileDescUpdatedAt := fileMixinFields1[1].Descriptor()
	// file.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	file.DefaultUpdatedAt = fileDescUpdatedAt.Default.(func() time.Time)
	// file.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	file.UpdateDefaultUpdatedAt = fileDescUpdatedAt.UpdateDefault.(func() time.Time)
	// fileDescStatus is the schema descriptor for status field.
	fileDescStatus := fileMixinFields2[0].Descriptor()
	// file.DefaultStatus holds the default value on creation for the status field.
	file.DefaultStatus = fileDescStatus.Default.(bool)
	// fileDescSort is the schema descriptor for sort field.
	fileDescSort := fileMixinFields3[0].Descriptor()
	// file.DefaultSort holds the default value on creation for the sort field.
	file.DefaultSort = fileDescSort.Default.(uint32)
	// fileDescOpenStatus is the schema descriptor for open_status field.
	fileDescOpenStatus := fileFields[8].Descriptor()
	// file.DefaultOpenStatus holds the default value on creation for the open_status field.
	file.DefaultOpenStatus = fileDescOpenStatus.Default.(uint8)
	// fileDescID is the schema descriptor for id field.
	fileDescID := fileMixinFields0[0].Descriptor()
	// file.DefaultID holds the default value on creation for the id field.
	file.DefaultID = fileDescID.Default.(func() string)
	groupMixin := schema.Group{}.Mixin()
	groupMixinHooks4 := groupMixin[4].Hooks()
	groupMixinHooks5 := groupMixin[5].Hooks()
	group.Hooks[0] = groupMixinHooks4[0]
	group.Hooks[1] = groupMixinHooks5[0]
	groupMixinInters4 := groupMixin[4].Interceptors()
	groupMixinInters5 := groupMixin[5].Interceptors()
	group.Interceptors[0] = groupMixinInters4[0]
	group.Interceptors[1] = groupMixinInters5[0]
	groupMixinFields0 := groupMixin[0].Fields()
	_ = groupMixinFields0
	groupMixinFields1 := groupMixin[1].Fields()
	_ = groupMixinFields1
	groupMixinFields2 := groupMixin[2].Fields()
	_ = groupMixinFields2
	groupMixinFields3 := groupMixin[3].Fields()
	_ = groupMixinFields3
	groupFields := schema.Group{}.Fields()
	_ = groupFields
	// groupDescCreatedAt is the schema descriptor for created_at field.
	groupDescCreatedAt := groupMixinFields1[0].Descriptor()
	// group.DefaultCreatedAt holds the default value on creation for the created_at field.
	group.DefaultCreatedAt = groupDescCreatedAt.Default.(func() time.Time)
	// groupDescUpdatedAt is the schema descriptor for updated_at field.
	groupDescUpdatedAt := groupMixinFields1[1].Descriptor()
	// group.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	group.DefaultUpdatedAt = groupDescUpdatedAt.Default.(func() time.Time)
	// group.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	group.UpdateDefaultUpdatedAt = groupDescUpdatedAt.UpdateDefault.(func() time.Time)
	// groupDescStatus is the schema descriptor for status field.
	groupDescStatus := groupMixinFields2[0].Descriptor()
	// group.DefaultStatus holds the default value on creation for the status field.
	group.DefaultStatus = groupDescStatus.Default.(bool)
	// groupDescSort is the schema descriptor for sort field.
	groupDescSort := groupMixinFields3[0].Descriptor()
	// group.DefaultSort holds the default value on creation for the sort field.
	group.DefaultSort = groupDescSort.Default.(uint32)
	// groupDescRemark is the schema descriptor for remark field.
	groupDescRemark := groupFields[3].Descriptor()
	// group.DefaultRemark holds the default value on creation for the remark field.
	group.DefaultRemark = groupDescRemark.Default.(string)
	// groupDescID is the schema descriptor for id field.
	groupDescID := groupMixinFields0[0].Descriptor()
	// group.DefaultID holds the default value on creation for the id field.
	group.DefaultID = groupDescID.Default.(func() string)
	grouptypeMixin := schema.GroupType{}.Mixin()
	grouptypeMixinHooks4 := grouptypeMixin[4].Hooks()
	grouptypeMixinHooks5 := grouptypeMixin[5].Hooks()
	grouptype.Hooks[0] = grouptypeMixinHooks4[0]
	grouptype.Hooks[1] = grouptypeMixinHooks5[0]
	grouptypeMixinInters4 := grouptypeMixin[4].Interceptors()
	grouptypeMixinInters5 := grouptypeMixin[5].Interceptors()
	grouptype.Interceptors[0] = grouptypeMixinInters4[0]
	grouptype.Interceptors[1] = grouptypeMixinInters5[0]
	grouptypeMixinFields0 := grouptypeMixin[0].Fields()
	_ = grouptypeMixinFields0
	grouptypeMixinFields1 := grouptypeMixin[1].Fields()
	_ = grouptypeMixinFields1
	grouptypeMixinFields2 := grouptypeMixin[2].Fields()
	_ = grouptypeMixinFields2
	grouptypeMixinFields3 := grouptypeMixin[3].Fields()
	_ = grouptypeMixinFields3
	grouptypeFields := schema.GroupType{}.Fields()
	_ = grouptypeFields
	// grouptypeDescCreatedAt is the schema descriptor for created_at field.
	grouptypeDescCreatedAt := grouptypeMixinFields1[0].Descriptor()
	// grouptype.DefaultCreatedAt holds the default value on creation for the created_at field.
	grouptype.DefaultCreatedAt = grouptypeDescCreatedAt.Default.(func() time.Time)
	// grouptypeDescUpdatedAt is the schema descriptor for updated_at field.
	grouptypeDescUpdatedAt := grouptypeMixinFields1[1].Descriptor()
	// grouptype.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	grouptype.DefaultUpdatedAt = grouptypeDescUpdatedAt.Default.(func() time.Time)
	// grouptype.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	grouptype.UpdateDefaultUpdatedAt = grouptypeDescUpdatedAt.UpdateDefault.(func() time.Time)
	// grouptypeDescStatus is the schema descriptor for status field.
	grouptypeDescStatus := grouptypeMixinFields2[0].Descriptor()
	// grouptype.DefaultStatus holds the default value on creation for the status field.
	grouptype.DefaultStatus = grouptypeDescStatus.Default.(bool)
	// grouptypeDescSort is the schema descriptor for sort field.
	grouptypeDescSort := grouptypeMixinFields3[0].Descriptor()
	// grouptype.DefaultSort holds the default value on creation for the sort field.
	grouptype.DefaultSort = grouptypeDescSort.Default.(uint32)
	// grouptypeDescRemark is the schema descriptor for remark field.
	grouptypeDescRemark := grouptypeFields[2].Descriptor()
	// grouptype.DefaultRemark holds the default value on creation for the remark field.
	grouptype.DefaultRemark = grouptypeDescRemark.Default.(string)
	// grouptypeDescID is the schema descriptor for id field.
	grouptypeDescID := grouptypeMixinFields0[0].Descriptor()
	// grouptype.DefaultID holds the default value on creation for the id field.
	grouptype.DefaultID = grouptypeDescID.Default.(func() string)
	menuMixin := schema.Menu{}.Mixin()
	menuMixinHooks3 := menuMixin[3].Hooks()
	menu.Hooks[0] = menuMixinHooks3[0]
	menuMixinInters3 := menuMixin[3].Interceptors()
	menu.Interceptors[0] = menuMixinInters3[0]
	menuMixinFields0 := menuMixin[0].Fields()
	_ = menuMixinFields0
	menuMixinFields1 := menuMixin[1].Fields()
	_ = menuMixinFields1
	menuMixinFields2 := menuMixin[2].Fields()
	_ = menuMixinFields2
	menuFields := schema.Menu{}.Fields()
	_ = menuFields
	// menuDescCreatedAt is the schema descriptor for created_at field.
	menuDescCreatedAt := menuMixinFields1[0].Descriptor()
	// menu.DefaultCreatedAt holds the default value on creation for the created_at field.
	menu.DefaultCreatedAt = menuDescCreatedAt.Default.(func() time.Time)
	// menuDescUpdatedAt is the schema descriptor for updated_at field.
	menuDescUpdatedAt := menuMixinFields1[1].Descriptor()
	// menu.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	menu.DefaultUpdatedAt = menuDescUpdatedAt.Default.(func() time.Time)
	// menu.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	menu.UpdateDefaultUpdatedAt = menuDescUpdatedAt.UpdateDefault.(func() time.Time)
	// menuDescSort is the schema descriptor for sort field.
	menuDescSort := menuMixinFields2[0].Descriptor()
	// menu.DefaultSort holds the default value on creation for the sort field.
	menu.DefaultSort = menuDescSort.Default.(uint32)
	// menuDescURL is the schema descriptor for url field.
	menuDescURL := menuFields[5].Descriptor()
	// menu.DefaultURL holds the default value on creation for the url field.
	menu.DefaultURL = menuDescURL.Default.(string)
	// menuDescRedirect is the schema descriptor for redirect field.
	menuDescRedirect := menuFields[6].Descriptor()
	// menu.DefaultRedirect holds the default value on creation for the redirect field.
	menu.DefaultRedirect = menuDescRedirect.Default.(string)
	// menuDescComponent is the schema descriptor for component field.
	menuDescComponent := menuFields[7].Descriptor()
	// menu.DefaultComponent holds the default value on creation for the component field.
	menu.DefaultComponent = menuDescComponent.Default.(string)
	// menuDescIsActive is the schema descriptor for is_active field.
	menuDescIsActive := menuFields[8].Descriptor()
	// menu.DefaultIsActive holds the default value on creation for the is_active field.
	menu.DefaultIsActive = menuDescIsActive.Default.(bool)
	// menuDescHidden is the schema descriptor for hidden field.
	menuDescHidden := menuFields[9].Descriptor()
	// menu.DefaultHidden holds the default value on creation for the hidden field.
	menu.DefaultHidden = menuDescHidden.Default.(bool)
	// menuDescHiddenInTab is the schema descriptor for hidden_in_tab field.
	menuDescHiddenInTab := menuFields[10].Descriptor()
	// menu.DefaultHiddenInTab holds the default value on creation for the hidden_in_tab field.
	menu.DefaultHiddenInTab = menuDescHiddenInTab.Default.(bool)
	// menuDescFixed is the schema descriptor for fixed field.
	menuDescFixed := menuFields[11].Descriptor()
	// menu.DefaultFixed holds the default value on creation for the fixed field.
	menu.DefaultFixed = menuDescFixed.Default.(bool)
	// menuDescRemark is the schema descriptor for remark field.
	menuDescRemark := menuFields[12].Descriptor()
	// menu.DefaultRemark holds the default value on creation for the remark field.
	menu.DefaultRemark = menuDescRemark.Default.(string)
	// menuDescMeta is the schema descriptor for meta field.
	menuDescMeta := menuFields[13].Descriptor()
	// menu.DefaultMeta holds the default value on creation for the meta field.
	menu.DefaultMeta = menuDescMeta.Default.(string)
	// menuDescIsFullPage is the schema descriptor for is_full_page field.
	menuDescIsFullPage := menuFields[14].Descriptor()
	// menu.DefaultIsFullPage holds the default value on creation for the is_full_page field.
	menu.DefaultIsFullPage = menuDescIsFullPage.Default.(bool)
	// menuDescID is the schema descriptor for id field.
	menuDescID := menuMixinFields0[0].Descriptor()
	// menu.DefaultID holds the default value on creation for the id field.
	menu.DefaultID = menuDescID.Default.(func() string)
	organizationMixin := schema.Organization{}.Mixin()
	organizationMixinHooks4 := organizationMixin[4].Hooks()
	organizationMixinHooks5 := organizationMixin[5].Hooks()
	organization.Hooks[0] = organizationMixinHooks4[0]
	organization.Hooks[1] = organizationMixinHooks5[0]
	organizationMixinInters4 := organizationMixin[4].Interceptors()
	organizationMixinInters5 := organizationMixin[5].Interceptors()
	organization.Interceptors[0] = organizationMixinInters4[0]
	organization.Interceptors[1] = organizationMixinInters5[0]
	organizationMixinFields0 := organizationMixin[0].Fields()
	_ = organizationMixinFields0
	organizationMixinFields1 := organizationMixin[1].Fields()
	_ = organizationMixinFields1
	organizationMixinFields2 := organizationMixin[2].Fields()
	_ = organizationMixinFields2
	organizationMixinFields3 := organizationMixin[3].Fields()
	_ = organizationMixinFields3
	organizationFields := schema.Organization{}.Fields()
	_ = organizationFields
	// organizationDescCreatedAt is the schema descriptor for created_at field.
	organizationDescCreatedAt := organizationMixinFields1[0].Descriptor()
	// organization.DefaultCreatedAt holds the default value on creation for the created_at field.
	organization.DefaultCreatedAt = organizationDescCreatedAt.Default.(func() time.Time)
	// organizationDescUpdatedAt is the schema descriptor for updated_at field.
	organizationDescUpdatedAt := organizationMixinFields1[1].Descriptor()
	// organization.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	organization.DefaultUpdatedAt = organizationDescUpdatedAt.Default.(func() time.Time)
	// organization.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	organization.UpdateDefaultUpdatedAt = organizationDescUpdatedAt.UpdateDefault.(func() time.Time)
	// organizationDescStatus is the schema descriptor for status field.
	organizationDescStatus := organizationMixinFields2[0].Descriptor()
	// organization.DefaultStatus holds the default value on creation for the status field.
	organization.DefaultStatus = organizationDescStatus.Default.(bool)
	// organizationDescSort is the schema descriptor for sort field.
	organizationDescSort := organizationMixinFields3[0].Descriptor()
	// organization.DefaultSort holds the default value on creation for the sort field.
	organization.DefaultSort = organizationDescSort.Default.(uint32)
	// organizationDescID is the schema descriptor for id field.
	organizationDescID := organizationMixinFields0[0].Descriptor()
	// organization.DefaultID holds the default value on creation for the id field.
	organization.DefaultID = organizationDescID.Default.(func() string)
	organizationuserinfoMixin := schema.OrganizationUserInfo{}.Mixin()
	organizationuserinfoMixinHooks3 := organizationuserinfoMixin[3].Hooks()
	organizationuserinfo.Hooks[0] = organizationuserinfoMixinHooks3[0]
	organizationuserinfoMixinInters3 := organizationuserinfoMixin[3].Interceptors()
	organizationuserinfo.Interceptors[0] = organizationuserinfoMixinInters3[0]
	organizationuserinfoMixinFields0 := organizationuserinfoMixin[0].Fields()
	_ = organizationuserinfoMixinFields0
	organizationuserinfoMixinFields1 := organizationuserinfoMixin[1].Fields()
	_ = organizationuserinfoMixinFields1
	organizationuserinfoMixinFields2 := organizationuserinfoMixin[2].Fields()
	_ = organizationuserinfoMixinFields2
	organizationuserinfoFields := schema.OrganizationUserInfo{}.Fields()
	_ = organizationuserinfoFields
	// organizationuserinfoDescCreatedAt is the schema descriptor for created_at field.
	organizationuserinfoDescCreatedAt := organizationuserinfoMixinFields1[0].Descriptor()
	// organizationuserinfo.DefaultCreatedAt holds the default value on creation for the created_at field.
	organizationuserinfo.DefaultCreatedAt = organizationuserinfoDescCreatedAt.Default.(func() time.Time)
	// organizationuserinfoDescUpdatedAt is the schema descriptor for updated_at field.
	organizationuserinfoDescUpdatedAt := organizationuserinfoMixinFields1[1].Descriptor()
	// organizationuserinfo.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	organizationuserinfo.DefaultUpdatedAt = organizationuserinfoDescUpdatedAt.Default.(func() time.Time)
	// organizationuserinfo.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	organizationuserinfo.UpdateDefaultUpdatedAt = organizationuserinfoDescUpdatedAt.UpdateDefault.(func() time.Time)
	// organizationuserinfoDescSort is the schema descriptor for sort field.
	organizationuserinfoDescSort := organizationuserinfoMixinFields2[0].Descriptor()
	// organizationuserinfo.DefaultSort holds the default value on creation for the sort field.
	organizationuserinfo.DefaultSort = organizationuserinfoDescSort.Default.(uint32)
	// organizationuserinfoDescExtra is the schema descriptor for extra field.
	organizationuserinfoDescExtra := organizationuserinfoFields[2].Descriptor()
	// organizationuserinfo.DefaultExtra holds the default value on creation for the extra field.
	organizationuserinfo.DefaultExtra = organizationuserinfoDescExtra.Default.(string)
	// organizationuserinfoDescIsLeader is the schema descriptor for is_leader field.
	organizationuserinfoDescIsLeader := organizationuserinfoFields[3].Descriptor()
	// organizationuserinfo.DefaultIsLeader holds the default value on creation for the is_leader field.
	organizationuserinfo.DefaultIsLeader = organizationuserinfoDescIsLeader.Default.(bool)
	// organizationuserinfoDescIsAdmin is the schema descriptor for is_admin field.
	organizationuserinfoDescIsAdmin := organizationuserinfoFields[4].Descriptor()
	// organizationuserinfo.DefaultIsAdmin holds the default value on creation for the is_admin field.
	organizationuserinfo.DefaultIsAdmin = organizationuserinfoDescIsAdmin.Default.(bool)
	// organizationuserinfoDescID is the schema descriptor for id field.
	organizationuserinfoDescID := organizationuserinfoMixinFields0[0].Descriptor()
	// organizationuserinfo.DefaultID holds the default value on creation for the id field.
	organizationuserinfo.DefaultID = organizationuserinfoDescID.Default.(func() string)
	positionMixin := schema.Position{}.Mixin()
	positionMixinHooks4 := positionMixin[4].Hooks()
	positionMixinHooks5 := positionMixin[5].Hooks()
	position.Hooks[0] = positionMixinHooks4[0]
	position.Hooks[1] = positionMixinHooks5[0]
	positionMixinInters4 := positionMixin[4].Interceptors()
	positionMixinInters5 := positionMixin[5].Interceptors()
	position.Interceptors[0] = positionMixinInters4[0]
	position.Interceptors[1] = positionMixinInters5[0]
	positionMixinFields0 := positionMixin[0].Fields()
	_ = positionMixinFields0
	positionMixinFields1 := positionMixin[1].Fields()
	_ = positionMixinFields1
	positionMixinFields2 := positionMixin[2].Fields()
	_ = positionMixinFields2
	positionMixinFields3 := positionMixin[3].Fields()
	_ = positionMixinFields3
	positionFields := schema.Position{}.Fields()
	_ = positionFields
	// positionDescCreatedAt is the schema descriptor for created_at field.
	positionDescCreatedAt := positionMixinFields1[0].Descriptor()
	// position.DefaultCreatedAt holds the default value on creation for the created_at field.
	position.DefaultCreatedAt = positionDescCreatedAt.Default.(func() time.Time)
	// positionDescUpdatedAt is the schema descriptor for updated_at field.
	positionDescUpdatedAt := positionMixinFields1[1].Descriptor()
	// position.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	position.DefaultUpdatedAt = positionDescUpdatedAt.Default.(func() time.Time)
	// position.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	position.UpdateDefaultUpdatedAt = positionDescUpdatedAt.UpdateDefault.(func() time.Time)
	// positionDescStatus is the schema descriptor for status field.
	positionDescStatus := positionMixinFields2[0].Descriptor()
	// position.DefaultStatus holds the default value on creation for the status field.
	position.DefaultStatus = positionDescStatus.Default.(bool)
	// positionDescSort is the schema descriptor for sort field.
	positionDescSort := positionMixinFields3[0].Descriptor()
	// position.DefaultSort holds the default value on creation for the sort field.
	position.DefaultSort = positionDescSort.Default.(uint32)
	// positionDescRemark is the schema descriptor for remark field.
	positionDescRemark := positionFields[3].Descriptor()
	// position.DefaultRemark holds the default value on creation for the remark field.
	position.DefaultRemark = positionDescRemark.Default.(string)
	// positionDescID is the schema descriptor for id field.
	positionDescID := positionMixinFields0[0].Descriptor()
	// position.DefaultID holds the default value on creation for the id field.
	position.DefaultID = positionDescID.Default.(func() string)
	roleMixin := schema.Role{}.Mixin()
	roleMixinHooks4 := roleMixin[4].Hooks()
	roleMixinHooks5 := roleMixin[5].Hooks()
	role.Hooks[0] = roleMixinHooks4[0]
	role.Hooks[1] = roleMixinHooks5[0]
	roleMixinInters4 := roleMixin[4].Interceptors()
	roleMixinInters5 := roleMixin[5].Interceptors()
	role.Interceptors[0] = roleMixinInters4[0]
	role.Interceptors[1] = roleMixinInters5[0]
	roleMixinFields0 := roleMixin[0].Fields()
	_ = roleMixinFields0
	roleMixinFields1 := roleMixin[1].Fields()
	_ = roleMixinFields1
	roleMixinFields2 := roleMixin[2].Fields()
	_ = roleMixinFields2
	roleMixinFields3 := roleMixin[3].Fields()
	_ = roleMixinFields3
	roleFields := schema.Role{}.Fields()
	_ = roleFields
	// roleDescCreatedAt is the schema descriptor for created_at field.
	roleDescCreatedAt := roleMixinFields1[0].Descriptor()
	// role.DefaultCreatedAt holds the default value on creation for the created_at field.
	role.DefaultCreatedAt = roleDescCreatedAt.Default.(func() time.Time)
	// roleDescUpdatedAt is the schema descriptor for updated_at field.
	roleDescUpdatedAt := roleMixinFields1[1].Descriptor()
	// role.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	role.DefaultUpdatedAt = roleDescUpdatedAt.Default.(func() time.Time)
	// role.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	role.UpdateDefaultUpdatedAt = roleDescUpdatedAt.UpdateDefault.(func() time.Time)
	// roleDescStatus is the schema descriptor for status field.
	roleDescStatus := roleMixinFields2[0].Descriptor()
	// role.DefaultStatus holds the default value on creation for the status field.
	role.DefaultStatus = roleDescStatus.Default.(bool)
	// roleDescSort is the schema descriptor for sort field.
	roleDescSort := roleMixinFields3[0].Descriptor()
	// role.DefaultSort holds the default value on creation for the sort field.
	role.DefaultSort = roleDescSort.Default.(uint32)
	// roleDescName is the schema descriptor for name field.
	roleDescName := roleFields[0].Descriptor()
	// role.DefaultName holds the default value on creation for the name field.
	role.DefaultName = roleDescName.Default.(string)
	// roleDescUID is the schema descriptor for uid field.
	roleDescUID := roleFields[2].Descriptor()
	// role.DefaultUID holds the default value on creation for the uid field.
	role.DefaultUID = roleDescUID.Default.(func() uuid.UUID)
	// roleDescDefaultRouter is the schema descriptor for default_router field.
	roleDescDefaultRouter := roleFields[3].Descriptor()
	// role.DefaultDefaultRouter holds the default value on creation for the default_router field.
	role.DefaultDefaultRouter = roleDescDefaultRouter.Default.(string)
	// roleDescRemark is the schema descriptor for remark field.
	roleDescRemark := roleFields[4].Descriptor()
	// role.DefaultRemark holds the default value on creation for the remark field.
	role.DefaultRemark = roleDescRemark.Default.(string)
	// roleDescOrganizationID is the schema descriptor for organization_id field.
	roleDescOrganizationID := roleFields[5].Descriptor()
	// role.DefaultOrganizationID holds the default value on creation for the organization_id field.
	role.DefaultOrganizationID = roleDescOrganizationID.Default.(string)
	// roleDescID is the schema descriptor for id field.
	roleDescID := roleMixinFields0[0].Descriptor()
	// role.DefaultID holds the default value on creation for the id field.
	role.DefaultID = roleDescID.Default.(func() string)
	tenantMixin := schema.Tenant{}.Mixin()
	tenantMixinHooks5 := tenantMixin[5].Hooks()
	tenant.Hooks[0] = tenantMixinHooks5[0]
	tenantMixinInters5 := tenantMixin[5].Interceptors()
	tenant.Interceptors[0] = tenantMixinInters5[0]
	tenantMixinFields0 := tenantMixin[0].Fields()
	_ = tenantMixinFields0
	tenantMixinFields1 := tenantMixin[1].Fields()
	_ = tenantMixinFields1
	tenantMixinFields2 := tenantMixin[2].Fields()
	_ = tenantMixinFields2
	tenantMixinFields3 := tenantMixin[3].Fields()
	_ = tenantMixinFields3
	tenantMixinFields4 := tenantMixin[4].Fields()
	_ = tenantMixinFields4
	tenantFields := schema.Tenant{}.Fields()
	_ = tenantFields
	// tenantDescCreatedAt is the schema descriptor for created_at field.
	tenantDescCreatedAt := tenantMixinFields1[0].Descriptor()
	// tenant.DefaultCreatedAt holds the default value on creation for the created_at field.
	tenant.DefaultCreatedAt = tenantDescCreatedAt.Default.(func() time.Time)
	// tenantDescUpdatedAt is the schema descriptor for updated_at field.
	tenantDescUpdatedAt := tenantMixinFields1[1].Descriptor()
	// tenant.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	tenant.DefaultUpdatedAt = tenantDescUpdatedAt.Default.(func() time.Time)
	// tenant.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	tenant.UpdateDefaultUpdatedAt = tenantDescUpdatedAt.UpdateDefault.(func() time.Time)
	// tenantDescUUID is the schema descriptor for uuid field.
	tenantDescUUID := tenantMixinFields2[0].Descriptor()
	// tenant.DefaultUUID holds the default value on creation for the uuid field.
	tenant.DefaultUUID = tenantDescUUID.Default.(func() uuid.UUID)
	// tenantDescKey is the schema descriptor for key field.
	tenantDescKey := tenantMixinFields3[0].Descriptor()
	// tenant.DefaultKey holds the default value on creation for the key field.
	tenant.DefaultKey = tenantDescKey.Default.(func() string)
	// tenantDescSecret is the schema descriptor for secret field.
	tenantDescSecret := tenantMixinFields3[1].Descriptor()
	// tenant.DefaultSecret holds the default value on creation for the secret field.
	tenant.DefaultSecret = tenantDescSecret.Default.(func() string)
	// tenantDescStatus is the schema descriptor for status field.
	tenantDescStatus := tenantMixinFields4[0].Descriptor()
	// tenant.DefaultStatus holds the default value on creation for the status field.
	tenant.DefaultStatus = tenantDescStatus.Default.(bool)
	// tenantDescName is the schema descriptor for name field.
	tenantDescName := tenantFields[0].Descriptor()
	// tenant.DefaultName holds the default value on creation for the name field.
	tenant.DefaultName = tenantDescName.Default.(string)
	// tenantDescIsSuper is the schema descriptor for is_super field.
	tenantDescIsSuper := tenantFields[1].Descriptor()
	// tenant.DefaultIsSuper holds the default value on creation for the is_super field.
	tenant.DefaultIsSuper = tenantDescIsSuper.Default.(bool)
	// tenantDescAiStatus is the schema descriptor for ai_status field.
	tenantDescAiStatus := tenantFields[15].Descriptor()
	// tenant.DefaultAiStatus holds the default value on creation for the ai_status field.
	tenant.DefaultAiStatus = tenantDescAiStatus.Default.(bool)
	// tenantDescID is the schema descriptor for id field.
	tenantDescID := tenantMixinFields0[0].Descriptor()
	// tenant.DefaultID holds the default value on creation for the id field.
	tenant.DefaultID = tenantDescID.Default.(func() string)
	tenantuserinfoMixin := schema.TenantUserInfo{}.Mixin()
	tenantuserinfoMixinHooks3 := tenantuserinfoMixin[3].Hooks()
	tenantuserinfo.Hooks[0] = tenantuserinfoMixinHooks3[0]
	tenantuserinfoMixinInters3 := tenantuserinfoMixin[3].Interceptors()
	tenantuserinfo.Interceptors[0] = tenantuserinfoMixinInters3[0]
	tenantuserinfoMixinFields0 := tenantuserinfoMixin[0].Fields()
	_ = tenantuserinfoMixinFields0
	tenantuserinfoMixinFields1 := tenantuserinfoMixin[1].Fields()
	_ = tenantuserinfoMixinFields1
	tenantuserinfoMixinFields2 := tenantuserinfoMixin[2].Fields()
	_ = tenantuserinfoMixinFields2
	tenantuserinfoFields := schema.TenantUserInfo{}.Fields()
	_ = tenantuserinfoFields
	// tenantuserinfoDescCreatedAt is the schema descriptor for created_at field.
	tenantuserinfoDescCreatedAt := tenantuserinfoMixinFields1[0].Descriptor()
	// tenantuserinfo.DefaultCreatedAt holds the default value on creation for the created_at field.
	tenantuserinfo.DefaultCreatedAt = tenantuserinfoDescCreatedAt.Default.(func() time.Time)
	// tenantuserinfoDescUpdatedAt is the schema descriptor for updated_at field.
	tenantuserinfoDescUpdatedAt := tenantuserinfoMixinFields1[1].Descriptor()
	// tenantuserinfo.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	tenantuserinfo.DefaultUpdatedAt = tenantuserinfoDescUpdatedAt.Default.(func() time.Time)
	// tenantuserinfo.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	tenantuserinfo.UpdateDefaultUpdatedAt = tenantuserinfoDescUpdatedAt.UpdateDefault.(func() time.Time)
	// tenantuserinfoDescSort is the schema descriptor for sort field.
	tenantuserinfoDescSort := tenantuserinfoMixinFields2[0].Descriptor()
	// tenantuserinfo.DefaultSort holds the default value on creation for the sort field.
	tenantuserinfo.DefaultSort = tenantuserinfoDescSort.Default.(uint32)
	// tenantuserinfoDescExtra is the schema descriptor for extra field.
	tenantuserinfoDescExtra := tenantuserinfoFields[2].Descriptor()
	// tenantuserinfo.DefaultExtra holds the default value on creation for the extra field.
	tenantuserinfo.DefaultExtra = tenantuserinfoDescExtra.Default.(string)
	// tenantuserinfoDescID is the schema descriptor for id field.
	tenantuserinfoDescID := tenantuserinfoMixinFields0[0].Descriptor()
	// tenantuserinfo.DefaultID holds the default value on creation for the id field.
	tenantuserinfo.DefaultID = tenantuserinfoDescID.Default.(func() string)
	tokenMixin := schema.Token{}.Mixin()
	tokenMixinFields0 := tokenMixin[0].Fields()
	_ = tokenMixinFields0
	tokenMixinFields1 := tokenMixin[1].Fields()
	_ = tokenMixinFields1
	tokenMixinFields2 := tokenMixin[2].Fields()
	_ = tokenMixinFields2
	tokenFields := schema.Token{}.Fields()
	_ = tokenFields
	// tokenDescCreatedAt is the schema descriptor for created_at field.
	tokenDescCreatedAt := tokenMixinFields1[0].Descriptor()
	// token.DefaultCreatedAt holds the default value on creation for the created_at field.
	token.DefaultCreatedAt = tokenDescCreatedAt.Default.(func() time.Time)
	// tokenDescUpdatedAt is the schema descriptor for updated_at field.
	tokenDescUpdatedAt := tokenMixinFields1[1].Descriptor()
	// token.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	token.DefaultUpdatedAt = tokenDescUpdatedAt.Default.(func() time.Time)
	// token.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	token.UpdateDefaultUpdatedAt = tokenDescUpdatedAt.UpdateDefault.(func() time.Time)
	// tokenDescStatus is the schema descriptor for status field.
	tokenDescStatus := tokenMixinFields2[0].Descriptor()
	// token.DefaultStatus holds the default value on creation for the status field.
	token.DefaultStatus = tokenDescStatus.Default.(bool)
	// tokenDescID is the schema descriptor for id field.
	tokenDescID := tokenMixinFields0[0].Descriptor()
	// token.DefaultID holds the default value on creation for the id field.
	token.DefaultID = tokenDescID.Default.(func() string)
	userMixin := schema.User{}.Mixin()
	userMixinHooks3 := userMixin[3].Hooks()
	userHooks := schema.User{}.Hooks()
	user.Hooks[0] = userMixinHooks3[0]
	user.Hooks[1] = userHooks[0]
	userMixinInters3 := userMixin[3].Interceptors()
	user.Interceptors[0] = userMixinInters3[0]
	userMixinFields0 := userMixin[0].Fields()
	_ = userMixinFields0
	userMixinFields1 := userMixin[1].Fields()
	_ = userMixinFields1
	userMixinFields2 := userMixin[2].Fields()
	_ = userMixinFields2
	userFields := schema.User{}.Fields()
	_ = userFields
	// userDescCreatedAt is the schema descriptor for created_at field.
	userDescCreatedAt := userMixinFields1[0].Descriptor()
	// user.DefaultCreatedAt holds the default value on creation for the created_at field.
	user.DefaultCreatedAt = userDescCreatedAt.Default.(func() time.Time)
	// userDescUpdatedAt is the schema descriptor for updated_at field.
	userDescUpdatedAt := userMixinFields1[1].Descriptor()
	// user.DefaultUpdatedAt holds the default value on creation for the updated_at field.
	user.DefaultUpdatedAt = userDescUpdatedAt.Default.(func() time.Time)
	// user.UpdateDefaultUpdatedAt holds the default value on update for the updated_at field.
	user.UpdateDefaultUpdatedAt = userDescUpdatedAt.UpdateDefault.(func() time.Time)
	// userDescStatus is the schema descriptor for status field.
	userDescStatus := userMixinFields2[0].Descriptor()
	// user.DefaultStatus holds the default value on creation for the status field.
	user.DefaultStatus = userDescStatus.Default.(bool)
	// userDescNickname is the schema descriptor for nickname field.
	userDescNickname := userFields[2].Descriptor()
	// user.DefaultNickname holds the default value on creation for the nickname field.
	user.DefaultNickname = userDescNickname.Default.(string)
	// userDescPost is the schema descriptor for post field.
	userDescPost := userFields[6].Descriptor()
	// user.DefaultPost holds the default value on creation for the post field.
	user.DefaultPost = userDescPost.Default.(string)
	// userDescIsSuperuser is the schema descriptor for is_superuser field.
	userDescIsSuperuser := userFields[7].Descriptor()
	// user.DefaultIsSuperuser holds the default value on creation for the is_superuser field.
	user.DefaultIsSuperuser = userDescIsSuperuser.Default.(bool)
	// userDescAvatarID is the schema descriptor for avatar_id field.
	userDescAvatarID := userFields[9].Descriptor()
	// user.DefaultAvatarID holds the default value on creation for the avatar_id field.
	user.DefaultAvatarID = userDescAvatarID.Default.(string)
	// userDescDeviceNo is the schema descriptor for device_no field.
	userDescDeviceNo := userFields[10].Descriptor()
	// user.DefaultDeviceNo holds the default value on creation for the device_no field.
	user.DefaultDeviceNo = userDescDeviceNo.Default.(string)
	// userDescKind is the schema descriptor for kind field.
	userDescKind := userFields[11].Descriptor()
	// user.DefaultKind holds the default value on creation for the kind field.
	user.DefaultKind = userDescKind.Default.(string)
	// userDescID is the schema descriptor for id field.
	userDescID := userMixinFields0[0].Descriptor()
	// user.DefaultID holds the default value on creation for the id field.
	user.DefaultID = userDescID.Default.(func() string)
}

const (
	Version = "v0.13.1"                                         // Version of ent codegen.
	Sum     = "h1:uD8QwN1h6SNphdCCzmkMN3feSUzNnVvV/WIkHKMbzOE=" // Sum of ent codegen.
)
