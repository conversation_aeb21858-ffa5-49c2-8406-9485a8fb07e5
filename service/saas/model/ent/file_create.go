// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/file"
	"phoenix/service/saas/model/ent/tenant"
	"phoenix/service/saas/model/ent/user"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// FileCreate is the builder for creating a File entity.
type FileCreate struct {
	config
	mutation *FileMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (fc *FileCreate) SetCreatedAt(t time.Time) *FileCreate {
	fc.mutation.SetCreatedAt(t)
	return fc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (fc *FileCreate) SetNillableCreatedAt(t *time.Time) *FileCreate {
	if t != nil {
		fc.SetCreatedAt(*t)
	}
	return fc
}

// SetUpdatedAt sets the "updated_at" field.
func (fc *FileCreate) SetUpdatedAt(t time.Time) *FileCreate {
	fc.mutation.SetUpdatedAt(t)
	return fc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (fc *FileCreate) SetNillableUpdatedAt(t *time.Time) *FileCreate {
	if t != nil {
		fc.SetUpdatedAt(*t)
	}
	return fc
}

// SetStatus sets the "status" field.
func (fc *FileCreate) SetStatus(b bool) *FileCreate {
	fc.mutation.SetStatus(b)
	return fc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (fc *FileCreate) SetNillableStatus(b *bool) *FileCreate {
	if b != nil {
		fc.SetStatus(*b)
	}
	return fc
}

// SetSort sets the "sort" field.
func (fc *FileCreate) SetSort(u uint32) *FileCreate {
	fc.mutation.SetSort(u)
	return fc
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (fc *FileCreate) SetNillableSort(u *uint32) *FileCreate {
	if u != nil {
		fc.SetSort(*u)
	}
	return fc
}

// SetTenantID sets the "tenant_id" field.
func (fc *FileCreate) SetTenantID(s string) *FileCreate {
	fc.mutation.SetTenantID(s)
	return fc
}

// SetDeletedAt sets the "deleted_at" field.
func (fc *FileCreate) SetDeletedAt(t time.Time) *FileCreate {
	fc.mutation.SetDeletedAt(t)
	return fc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (fc *FileCreate) SetNillableDeletedAt(t *time.Time) *FileCreate {
	if t != nil {
		fc.SetDeletedAt(*t)
	}
	return fc
}

// SetUUID sets the "uuid" field.
func (fc *FileCreate) SetUUID(s string) *FileCreate {
	fc.mutation.SetUUID(s)
	return fc
}

// SetName sets the "name" field.
func (fc *FileCreate) SetName(s string) *FileCreate {
	fc.mutation.SetName(s)
	return fc
}

// SetOriginName sets the "origin_name" field.
func (fc *FileCreate) SetOriginName(s string) *FileCreate {
	fc.mutation.SetOriginName(s)
	return fc
}

// SetFileType sets the "file_type" field.
func (fc *FileCreate) SetFileType(u uint8) *FileCreate {
	fc.mutation.SetFileType(u)
	return fc
}

// SetSize sets the "size" field.
func (fc *FileCreate) SetSize(u uint64) *FileCreate {
	fc.mutation.SetSize(u)
	return fc
}

// SetPath sets the "path" field.
func (fc *FileCreate) SetPath(s string) *FileCreate {
	fc.mutation.SetPath(s)
	return fc
}

// SetUserID sets the "user_id" field.
func (fc *FileCreate) SetUserID(s string) *FileCreate {
	fc.mutation.SetUserID(s)
	return fc
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (fc *FileCreate) SetNillableUserID(s *string) *FileCreate {
	if s != nil {
		fc.SetUserID(*s)
	}
	return fc
}

// SetHash sets the "hash" field.
func (fc *FileCreate) SetHash(s string) *FileCreate {
	fc.mutation.SetHash(s)
	return fc
}

// SetOpenStatus sets the "open_status" field.
func (fc *FileCreate) SetOpenStatus(u uint8) *FileCreate {
	fc.mutation.SetOpenStatus(u)
	return fc
}

// SetNillableOpenStatus sets the "open_status" field if the given value is not nil.
func (fc *FileCreate) SetNillableOpenStatus(u *uint8) *FileCreate {
	if u != nil {
		fc.SetOpenStatus(*u)
	}
	return fc
}

// SetID sets the "id" field.
func (fc *FileCreate) SetID(s string) *FileCreate {
	fc.mutation.SetID(s)
	return fc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (fc *FileCreate) SetNillableID(s *string) *FileCreate {
	if s != nil {
		fc.SetID(*s)
	}
	return fc
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (fc *FileCreate) SetTenant(t *Tenant) *FileCreate {
	return fc.SetTenantID(t.ID)
}

// SetUser sets the "user" edge to the User entity.
func (fc *FileCreate) SetUser(u *User) *FileCreate {
	return fc.SetUserID(u.ID)
}

// AddAvatarUserIDs adds the "avatar_users" edge to the User entity by IDs.
func (fc *FileCreate) AddAvatarUserIDs(ids ...string) *FileCreate {
	fc.mutation.AddAvatarUserIDs(ids...)
	return fc
}

// AddAvatarUsers adds the "avatar_users" edges to the User entity.
func (fc *FileCreate) AddAvatarUsers(u ...*User) *FileCreate {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return fc.AddAvatarUserIDs(ids...)
}

// Mutation returns the FileMutation object of the builder.
func (fc *FileCreate) Mutation() *FileMutation {
	return fc.mutation
}

// Save creates the File in the database.
func (fc *FileCreate) Save(ctx context.Context) (*File, error) {
	if err := fc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, fc.sqlSave, fc.mutation, fc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (fc *FileCreate) SaveX(ctx context.Context) *File {
	v, err := fc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (fc *FileCreate) Exec(ctx context.Context) error {
	_, err := fc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (fc *FileCreate) ExecX(ctx context.Context) {
	if err := fc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (fc *FileCreate) defaults() error {
	if _, ok := fc.mutation.CreatedAt(); !ok {
		if file.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized file.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := file.DefaultCreatedAt()
		fc.mutation.SetCreatedAt(v)
	}
	if _, ok := fc.mutation.UpdatedAt(); !ok {
		if file.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized file.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := file.DefaultUpdatedAt()
		fc.mutation.SetUpdatedAt(v)
	}
	if _, ok := fc.mutation.Status(); !ok {
		v := file.DefaultStatus
		fc.mutation.SetStatus(v)
	}
	if _, ok := fc.mutation.Sort(); !ok {
		v := file.DefaultSort
		fc.mutation.SetSort(v)
	}
	if _, ok := fc.mutation.OpenStatus(); !ok {
		v := file.DefaultOpenStatus
		fc.mutation.SetOpenStatus(v)
	}
	if _, ok := fc.mutation.ID(); !ok {
		if file.DefaultID == nil {
			return fmt.Errorf("ent: uninitialized file.DefaultID (forgotten import ent/runtime?)")
		}
		v := file.DefaultID()
		fc.mutation.SetID(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (fc *FileCreate) check() error {
	if _, ok := fc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "File.created_at"`)}
	}
	if _, ok := fc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "File.updated_at"`)}
	}
	if _, ok := fc.mutation.Sort(); !ok {
		return &ValidationError{Name: "sort", err: errors.New(`ent: missing required field "File.sort"`)}
	}
	if _, ok := fc.mutation.TenantID(); !ok {
		return &ValidationError{Name: "tenant_id", err: errors.New(`ent: missing required field "File.tenant_id"`)}
	}
	if _, ok := fc.mutation.UUID(); !ok {
		return &ValidationError{Name: "uuid", err: errors.New(`ent: missing required field "File.uuid"`)}
	}
	if _, ok := fc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "File.name"`)}
	}
	if _, ok := fc.mutation.OriginName(); !ok {
		return &ValidationError{Name: "origin_name", err: errors.New(`ent: missing required field "File.origin_name"`)}
	}
	if _, ok := fc.mutation.FileType(); !ok {
		return &ValidationError{Name: "file_type", err: errors.New(`ent: missing required field "File.file_type"`)}
	}
	if _, ok := fc.mutation.Size(); !ok {
		return &ValidationError{Name: "size", err: errors.New(`ent: missing required field "File.size"`)}
	}
	if _, ok := fc.mutation.Path(); !ok {
		return &ValidationError{Name: "path", err: errors.New(`ent: missing required field "File.path"`)}
	}
	if _, ok := fc.mutation.Hash(); !ok {
		return &ValidationError{Name: "hash", err: errors.New(`ent: missing required field "File.hash"`)}
	}
	if _, ok := fc.mutation.TenantID(); !ok {
		return &ValidationError{Name: "tenant", err: errors.New(`ent: missing required edge "File.tenant"`)}
	}
	return nil
}

func (fc *FileCreate) sqlSave(ctx context.Context) (*File, error) {
	if err := fc.check(); err != nil {
		return nil, err
	}
	_node, _spec := fc.createSpec()
	if err := sqlgraph.CreateNode(ctx, fc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected File.ID type: %T", _spec.ID.Value)
		}
	}
	fc.mutation.id = &_node.ID
	fc.mutation.done = true
	return _node, nil
}

func (fc *FileCreate) createSpec() (*File, *sqlgraph.CreateSpec) {
	var (
		_node = &File{config: fc.config}
		_spec = sqlgraph.NewCreateSpec(file.Table, sqlgraph.NewFieldSpec(file.FieldID, field.TypeString))
	)
	_spec.OnConflict = fc.conflict
	if id, ok := fc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := fc.mutation.CreatedAt(); ok {
		_spec.SetField(file.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := fc.mutation.UpdatedAt(); ok {
		_spec.SetField(file.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := fc.mutation.Status(); ok {
		_spec.SetField(file.FieldStatus, field.TypeBool, value)
		_node.Status = value
	}
	if value, ok := fc.mutation.Sort(); ok {
		_spec.SetField(file.FieldSort, field.TypeUint32, value)
		_node.Sort = value
	}
	if value, ok := fc.mutation.DeletedAt(); ok {
		_spec.SetField(file.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := fc.mutation.UUID(); ok {
		_spec.SetField(file.FieldUUID, field.TypeString, value)
		_node.UUID = value
	}
	if value, ok := fc.mutation.Name(); ok {
		_spec.SetField(file.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := fc.mutation.OriginName(); ok {
		_spec.SetField(file.FieldOriginName, field.TypeString, value)
		_node.OriginName = value
	}
	if value, ok := fc.mutation.FileType(); ok {
		_spec.SetField(file.FieldFileType, field.TypeUint8, value)
		_node.FileType = value
	}
	if value, ok := fc.mutation.Size(); ok {
		_spec.SetField(file.FieldSize, field.TypeUint64, value)
		_node.Size = value
	}
	if value, ok := fc.mutation.Path(); ok {
		_spec.SetField(file.FieldPath, field.TypeString, value)
		_node.Path = value
	}
	if value, ok := fc.mutation.Hash(); ok {
		_spec.SetField(file.FieldHash, field.TypeString, value)
		_node.Hash = value
	}
	if value, ok := fc.mutation.OpenStatus(); ok {
		_spec.SetField(file.FieldOpenStatus, field.TypeUint8, value)
		_node.OpenStatus = value
	}
	if nodes := fc.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   file.TenantTable,
			Columns: []string{file.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TenantID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := fc.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   file.UserTable,
			Columns: []string{file.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.UserID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := fc.mutation.AvatarUsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   file.AvatarUsersTable,
			Columns: []string{file.AvatarUsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.File.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.FileUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (fc *FileCreate) OnConflict(opts ...sql.ConflictOption) *FileUpsertOne {
	fc.conflict = opts
	return &FileUpsertOne{
		create: fc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.File.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (fc *FileCreate) OnConflictColumns(columns ...string) *FileUpsertOne {
	fc.conflict = append(fc.conflict, sql.ConflictColumns(columns...))
	return &FileUpsertOne{
		create: fc,
	}
}

type (
	// FileUpsertOne is the builder for "upsert"-ing
	//  one File node.
	FileUpsertOne struct {
		create *FileCreate
	}

	// FileUpsert is the "OnConflict" setter.
	FileUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *FileUpsert) SetUpdatedAt(v time.Time) *FileUpsert {
	u.Set(file.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *FileUpsert) UpdateUpdatedAt() *FileUpsert {
	u.SetExcluded(file.FieldUpdatedAt)
	return u
}

// SetStatus sets the "status" field.
func (u *FileUpsert) SetStatus(v bool) *FileUpsert {
	u.Set(file.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *FileUpsert) UpdateStatus() *FileUpsert {
	u.SetExcluded(file.FieldStatus)
	return u
}

// ClearStatus clears the value of the "status" field.
func (u *FileUpsert) ClearStatus() *FileUpsert {
	u.SetNull(file.FieldStatus)
	return u
}

// SetSort sets the "sort" field.
func (u *FileUpsert) SetSort(v uint32) *FileUpsert {
	u.Set(file.FieldSort, v)
	return u
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *FileUpsert) UpdateSort() *FileUpsert {
	u.SetExcluded(file.FieldSort)
	return u
}

// AddSort adds v to the "sort" field.
func (u *FileUpsert) AddSort(v uint32) *FileUpsert {
	u.Add(file.FieldSort, v)
	return u
}

// SetTenantID sets the "tenant_id" field.
func (u *FileUpsert) SetTenantID(v string) *FileUpsert {
	u.Set(file.FieldTenantID, v)
	return u
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *FileUpsert) UpdateTenantID() *FileUpsert {
	u.SetExcluded(file.FieldTenantID)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *FileUpsert) SetDeletedAt(v time.Time) *FileUpsert {
	u.Set(file.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *FileUpsert) UpdateDeletedAt() *FileUpsert {
	u.SetExcluded(file.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *FileUpsert) ClearDeletedAt() *FileUpsert {
	u.SetNull(file.FieldDeletedAt)
	return u
}

// SetUUID sets the "uuid" field.
func (u *FileUpsert) SetUUID(v string) *FileUpsert {
	u.Set(file.FieldUUID, v)
	return u
}

// UpdateUUID sets the "uuid" field to the value that was provided on create.
func (u *FileUpsert) UpdateUUID() *FileUpsert {
	u.SetExcluded(file.FieldUUID)
	return u
}

// SetName sets the "name" field.
func (u *FileUpsert) SetName(v string) *FileUpsert {
	u.Set(file.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *FileUpsert) UpdateName() *FileUpsert {
	u.SetExcluded(file.FieldName)
	return u
}

// SetOriginName sets the "origin_name" field.
func (u *FileUpsert) SetOriginName(v string) *FileUpsert {
	u.Set(file.FieldOriginName, v)
	return u
}

// UpdateOriginName sets the "origin_name" field to the value that was provided on create.
func (u *FileUpsert) UpdateOriginName() *FileUpsert {
	u.SetExcluded(file.FieldOriginName)
	return u
}

// SetFileType sets the "file_type" field.
func (u *FileUpsert) SetFileType(v uint8) *FileUpsert {
	u.Set(file.FieldFileType, v)
	return u
}

// UpdateFileType sets the "file_type" field to the value that was provided on create.
func (u *FileUpsert) UpdateFileType() *FileUpsert {
	u.SetExcluded(file.FieldFileType)
	return u
}

// AddFileType adds v to the "file_type" field.
func (u *FileUpsert) AddFileType(v uint8) *FileUpsert {
	u.Add(file.FieldFileType, v)
	return u
}

// SetSize sets the "size" field.
func (u *FileUpsert) SetSize(v uint64) *FileUpsert {
	u.Set(file.FieldSize, v)
	return u
}

// UpdateSize sets the "size" field to the value that was provided on create.
func (u *FileUpsert) UpdateSize() *FileUpsert {
	u.SetExcluded(file.FieldSize)
	return u
}

// AddSize adds v to the "size" field.
func (u *FileUpsert) AddSize(v uint64) *FileUpsert {
	u.Add(file.FieldSize, v)
	return u
}

// SetPath sets the "path" field.
func (u *FileUpsert) SetPath(v string) *FileUpsert {
	u.Set(file.FieldPath, v)
	return u
}

// UpdatePath sets the "path" field to the value that was provided on create.
func (u *FileUpsert) UpdatePath() *FileUpsert {
	u.SetExcluded(file.FieldPath)
	return u
}

// SetUserID sets the "user_id" field.
func (u *FileUpsert) SetUserID(v string) *FileUpsert {
	u.Set(file.FieldUserID, v)
	return u
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *FileUpsert) UpdateUserID() *FileUpsert {
	u.SetExcluded(file.FieldUserID)
	return u
}

// ClearUserID clears the value of the "user_id" field.
func (u *FileUpsert) ClearUserID() *FileUpsert {
	u.SetNull(file.FieldUserID)
	return u
}

// SetHash sets the "hash" field.
func (u *FileUpsert) SetHash(v string) *FileUpsert {
	u.Set(file.FieldHash, v)
	return u
}

// UpdateHash sets the "hash" field to the value that was provided on create.
func (u *FileUpsert) UpdateHash() *FileUpsert {
	u.SetExcluded(file.FieldHash)
	return u
}

// SetOpenStatus sets the "open_status" field.
func (u *FileUpsert) SetOpenStatus(v uint8) *FileUpsert {
	u.Set(file.FieldOpenStatus, v)
	return u
}

// UpdateOpenStatus sets the "open_status" field to the value that was provided on create.
func (u *FileUpsert) UpdateOpenStatus() *FileUpsert {
	u.SetExcluded(file.FieldOpenStatus)
	return u
}

// AddOpenStatus adds v to the "open_status" field.
func (u *FileUpsert) AddOpenStatus(v uint8) *FileUpsert {
	u.Add(file.FieldOpenStatus, v)
	return u
}

// ClearOpenStatus clears the value of the "open_status" field.
func (u *FileUpsert) ClearOpenStatus() *FileUpsert {
	u.SetNull(file.FieldOpenStatus)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.File.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(file.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *FileUpsertOne) UpdateNewValues() *FileUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(file.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(file.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.File.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *FileUpsertOne) Ignore() *FileUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *FileUpsertOne) DoNothing() *FileUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the FileCreate.OnConflict
// documentation for more info.
func (u *FileUpsertOne) Update(set func(*FileUpsert)) *FileUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&FileUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *FileUpsertOne) SetUpdatedAt(v time.Time) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateUpdatedAt() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetStatus sets the "status" field.
func (u *FileUpsertOne) SetStatus(v bool) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateStatus() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *FileUpsertOne) ClearStatus() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.ClearStatus()
	})
}

// SetSort sets the "sort" field.
func (u *FileUpsertOne) SetSort(v uint32) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetSort(v)
	})
}

// AddSort adds v to the "sort" field.
func (u *FileUpsertOne) AddSort(v uint32) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.AddSort(v)
	})
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateSort() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateSort()
	})
}

// SetTenantID sets the "tenant_id" field.
func (u *FileUpsertOne) SetTenantID(v string) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateTenantID() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateTenantID()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *FileUpsertOne) SetDeletedAt(v time.Time) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateDeletedAt() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *FileUpsertOne) ClearDeletedAt() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.ClearDeletedAt()
	})
}

// SetUUID sets the "uuid" field.
func (u *FileUpsertOne) SetUUID(v string) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetUUID(v)
	})
}

// UpdateUUID sets the "uuid" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateUUID() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateUUID()
	})
}

// SetName sets the "name" field.
func (u *FileUpsertOne) SetName(v string) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateName() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateName()
	})
}

// SetOriginName sets the "origin_name" field.
func (u *FileUpsertOne) SetOriginName(v string) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetOriginName(v)
	})
}

// UpdateOriginName sets the "origin_name" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateOriginName() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateOriginName()
	})
}

// SetFileType sets the "file_type" field.
func (u *FileUpsertOne) SetFileType(v uint8) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetFileType(v)
	})
}

// AddFileType adds v to the "file_type" field.
func (u *FileUpsertOne) AddFileType(v uint8) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.AddFileType(v)
	})
}

// UpdateFileType sets the "file_type" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateFileType() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateFileType()
	})
}

// SetSize sets the "size" field.
func (u *FileUpsertOne) SetSize(v uint64) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetSize(v)
	})
}

// AddSize adds v to the "size" field.
func (u *FileUpsertOne) AddSize(v uint64) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.AddSize(v)
	})
}

// UpdateSize sets the "size" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateSize() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateSize()
	})
}

// SetPath sets the "path" field.
func (u *FileUpsertOne) SetPath(v string) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetPath(v)
	})
}

// UpdatePath sets the "path" field to the value that was provided on create.
func (u *FileUpsertOne) UpdatePath() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdatePath()
	})
}

// SetUserID sets the "user_id" field.
func (u *FileUpsertOne) SetUserID(v string) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateUserID() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateUserID()
	})
}

// ClearUserID clears the value of the "user_id" field.
func (u *FileUpsertOne) ClearUserID() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.ClearUserID()
	})
}

// SetHash sets the "hash" field.
func (u *FileUpsertOne) SetHash(v string) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetHash(v)
	})
}

// UpdateHash sets the "hash" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateHash() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateHash()
	})
}

// SetOpenStatus sets the "open_status" field.
func (u *FileUpsertOne) SetOpenStatus(v uint8) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.SetOpenStatus(v)
	})
}

// AddOpenStatus adds v to the "open_status" field.
func (u *FileUpsertOne) AddOpenStatus(v uint8) *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.AddOpenStatus(v)
	})
}

// UpdateOpenStatus sets the "open_status" field to the value that was provided on create.
func (u *FileUpsertOne) UpdateOpenStatus() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.UpdateOpenStatus()
	})
}

// ClearOpenStatus clears the value of the "open_status" field.
func (u *FileUpsertOne) ClearOpenStatus() *FileUpsertOne {
	return u.Update(func(s *FileUpsert) {
		s.ClearOpenStatus()
	})
}

// Exec executes the query.
func (u *FileUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for FileCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *FileUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *FileUpsertOne) ID(ctx context.Context) (id string, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: FileUpsertOne.ID is not supported by MySQL driver. Use FileUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *FileUpsertOne) IDX(ctx context.Context) string {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// FileCreateBulk is the builder for creating many File entities in bulk.
type FileCreateBulk struct {
	config
	err      error
	builders []*FileCreate
	conflict []sql.ConflictOption
}

// Save creates the File entities in the database.
func (fcb *FileCreateBulk) Save(ctx context.Context) ([]*File, error) {
	if fcb.err != nil {
		return nil, fcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(fcb.builders))
	nodes := make([]*File, len(fcb.builders))
	mutators := make([]Mutator, len(fcb.builders))
	for i := range fcb.builders {
		func(i int, root context.Context) {
			builder := fcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*FileMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, fcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = fcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, fcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, fcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (fcb *FileCreateBulk) SaveX(ctx context.Context) []*File {
	v, err := fcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (fcb *FileCreateBulk) Exec(ctx context.Context) error {
	_, err := fcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (fcb *FileCreateBulk) ExecX(ctx context.Context) {
	if err := fcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.File.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.FileUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (fcb *FileCreateBulk) OnConflict(opts ...sql.ConflictOption) *FileUpsertBulk {
	fcb.conflict = opts
	return &FileUpsertBulk{
		create: fcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.File.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (fcb *FileCreateBulk) OnConflictColumns(columns ...string) *FileUpsertBulk {
	fcb.conflict = append(fcb.conflict, sql.ConflictColumns(columns...))
	return &FileUpsertBulk{
		create: fcb,
	}
}

// FileUpsertBulk is the builder for "upsert"-ing
// a bulk of File nodes.
type FileUpsertBulk struct {
	create *FileCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.File.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(file.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *FileUpsertBulk) UpdateNewValues() *FileUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(file.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(file.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.File.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *FileUpsertBulk) Ignore() *FileUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *FileUpsertBulk) DoNothing() *FileUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the FileCreateBulk.OnConflict
// documentation for more info.
func (u *FileUpsertBulk) Update(set func(*FileUpsert)) *FileUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&FileUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *FileUpsertBulk) SetUpdatedAt(v time.Time) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateUpdatedAt() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetStatus sets the "status" field.
func (u *FileUpsertBulk) SetStatus(v bool) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateStatus() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *FileUpsertBulk) ClearStatus() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.ClearStatus()
	})
}

// SetSort sets the "sort" field.
func (u *FileUpsertBulk) SetSort(v uint32) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetSort(v)
	})
}

// AddSort adds v to the "sort" field.
func (u *FileUpsertBulk) AddSort(v uint32) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.AddSort(v)
	})
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateSort() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateSort()
	})
}

// SetTenantID sets the "tenant_id" field.
func (u *FileUpsertBulk) SetTenantID(v string) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateTenantID() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateTenantID()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *FileUpsertBulk) SetDeletedAt(v time.Time) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateDeletedAt() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *FileUpsertBulk) ClearDeletedAt() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.ClearDeletedAt()
	})
}

// SetUUID sets the "uuid" field.
func (u *FileUpsertBulk) SetUUID(v string) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetUUID(v)
	})
}

// UpdateUUID sets the "uuid" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateUUID() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateUUID()
	})
}

// SetName sets the "name" field.
func (u *FileUpsertBulk) SetName(v string) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateName() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateName()
	})
}

// SetOriginName sets the "origin_name" field.
func (u *FileUpsertBulk) SetOriginName(v string) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetOriginName(v)
	})
}

// UpdateOriginName sets the "origin_name" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateOriginName() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateOriginName()
	})
}

// SetFileType sets the "file_type" field.
func (u *FileUpsertBulk) SetFileType(v uint8) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetFileType(v)
	})
}

// AddFileType adds v to the "file_type" field.
func (u *FileUpsertBulk) AddFileType(v uint8) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.AddFileType(v)
	})
}

// UpdateFileType sets the "file_type" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateFileType() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateFileType()
	})
}

// SetSize sets the "size" field.
func (u *FileUpsertBulk) SetSize(v uint64) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetSize(v)
	})
}

// AddSize adds v to the "size" field.
func (u *FileUpsertBulk) AddSize(v uint64) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.AddSize(v)
	})
}

// UpdateSize sets the "size" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateSize() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateSize()
	})
}

// SetPath sets the "path" field.
func (u *FileUpsertBulk) SetPath(v string) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetPath(v)
	})
}

// UpdatePath sets the "path" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdatePath() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdatePath()
	})
}

// SetUserID sets the "user_id" field.
func (u *FileUpsertBulk) SetUserID(v string) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateUserID() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateUserID()
	})
}

// ClearUserID clears the value of the "user_id" field.
func (u *FileUpsertBulk) ClearUserID() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.ClearUserID()
	})
}

// SetHash sets the "hash" field.
func (u *FileUpsertBulk) SetHash(v string) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetHash(v)
	})
}

// UpdateHash sets the "hash" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateHash() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateHash()
	})
}

// SetOpenStatus sets the "open_status" field.
func (u *FileUpsertBulk) SetOpenStatus(v uint8) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.SetOpenStatus(v)
	})
}

// AddOpenStatus adds v to the "open_status" field.
func (u *FileUpsertBulk) AddOpenStatus(v uint8) *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.AddOpenStatus(v)
	})
}

// UpdateOpenStatus sets the "open_status" field to the value that was provided on create.
func (u *FileUpsertBulk) UpdateOpenStatus() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.UpdateOpenStatus()
	})
}

// ClearOpenStatus clears the value of the "open_status" field.
func (u *FileUpsertBulk) ClearOpenStatus() *FileUpsertBulk {
	return u.Update(func(s *FileUpsert) {
		s.ClearOpenStatus()
	})
}

// Exec executes the query.
func (u *FileUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the FileCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for FileCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *FileUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
