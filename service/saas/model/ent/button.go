// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"phoenix/service/saas/model/ent/button"
	"phoenix/service/saas/model/ent/menu"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// But<PERSON> is the model entity for the Button schema.
type Button struct {
	config `json:"-"`
	// ID of the ent.
	// Snowflake ID | 全局唯一ID
	ID string `json:"id,omitempty"`
	// Created Time | 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// Updated Time | 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// Sort number | 排序编号
	Sort uint32 `json:"sort,omitempty"`
	// Deleted Time | 删除时间（软删除标识）
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// name | 按钮名称
	Name string `json:"name,omitempty"`
	// code | 按钮CODE
	Code string `json:"code,omitempty"`
	// Menu ID | 菜单ID
	MenuID string `json:"menu_id,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the ButtonQuery when eager-loading is set.
	Edges          ButtonEdges `json:"edges"`
	tenant_buttons *string
	selectValues   sql.SelectValues
}

// ButtonEdges holds the relations/edges for other nodes in the graph.
type ButtonEdges struct {
	// Roles holds the value of the roles edge.
	Roles []*Role `json:"roles,omitempty"`
	// Menu holds the value of the menu edge.
	Menu *Menu `json:"menu,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// RolesOrErr returns the Roles value or an error if the edge
// was not loaded in eager-loading.
func (e ButtonEdges) RolesOrErr() ([]*Role, error) {
	if e.loadedTypes[0] {
		return e.Roles, nil
	}
	return nil, &NotLoadedError{edge: "roles"}
}

// MenuOrErr returns the Menu value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e ButtonEdges) MenuOrErr() (*Menu, error) {
	if e.Menu != nil {
		return e.Menu, nil
	} else if e.loadedTypes[1] {
		return nil, &NotFoundError{label: menu.Label}
	}
	return nil, &NotLoadedError{edge: "menu"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Button) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case button.FieldSort:
			values[i] = new(sql.NullInt64)
		case button.FieldID, button.FieldName, button.FieldCode, button.FieldMenuID:
			values[i] = new(sql.NullString)
		case button.FieldCreatedAt, button.FieldUpdatedAt, button.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		case button.ForeignKeys[0]: // tenant_buttons
			values[i] = new(sql.NullString)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Button fields.
func (b *Button) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case button.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				b.ID = value.String
			}
		case button.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				b.CreatedAt = value.Time
			}
		case button.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				b.UpdatedAt = value.Time
			}
		case button.FieldSort:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field sort", values[i])
			} else if value.Valid {
				b.Sort = uint32(value.Int64)
			}
		case button.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				b.DeletedAt = value.Time
			}
		case button.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				b.Name = value.String
			}
		case button.FieldCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field code", values[i])
			} else if value.Valid {
				b.Code = value.String
			}
		case button.FieldMenuID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field menu_id", values[i])
			} else if value.Valid {
				b.MenuID = value.String
			}
		case button.ForeignKeys[0]:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_buttons", values[i])
			} else if value.Valid {
				b.tenant_buttons = new(string)
				*b.tenant_buttons = value.String
			}
		default:
			b.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Button.
// This includes values selected through modifiers, order, etc.
func (b *Button) Value(name string) (ent.Value, error) {
	return b.selectValues.Get(name)
}

// QueryRoles queries the "roles" edge of the Button entity.
func (b *Button) QueryRoles() *RoleQuery {
	return NewButtonClient(b.config).QueryRoles(b)
}

// QueryMenu queries the "menu" edge of the Button entity.
func (b *Button) QueryMenu() *MenuQuery {
	return NewButtonClient(b.config).QueryMenu(b)
}

// Update returns a builder for updating this Button.
// Note that you need to call Button.Unwrap() before calling this method if this Button
// was returned from a transaction, and the transaction was committed or rolled back.
func (b *Button) Update() *ButtonUpdateOne {
	return NewButtonClient(b.config).UpdateOne(b)
}

// Unwrap unwraps the Button entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (b *Button) Unwrap() *Button {
	_tx, ok := b.config.driver.(*txDriver)
	if !ok {
		panic("ent: Button is not a transactional entity")
	}
	b.config.driver = _tx.drv
	return b
}

// String implements the fmt.Stringer.
func (b *Button) String() string {
	var builder strings.Builder
	builder.WriteString("Button(")
	builder.WriteString(fmt.Sprintf("id=%v, ", b.ID))
	builder.WriteString("created_at=")
	builder.WriteString(b.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(b.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("sort=")
	builder.WriteString(fmt.Sprintf("%v", b.Sort))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(b.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(b.Name)
	builder.WriteString(", ")
	builder.WriteString("code=")
	builder.WriteString(b.Code)
	builder.WriteString(", ")
	builder.WriteString("menu_id=")
	builder.WriteString(b.MenuID)
	builder.WriteByte(')')
	return builder.String()
}

// Buttons is a parsable slice of Button.
type Buttons []*Button
