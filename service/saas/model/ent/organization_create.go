// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/organization"
	"phoenix/service/saas/model/ent/organizationuserinfo"
	"phoenix/service/saas/model/ent/tenant"
	"phoenix/service/saas/model/ent/user"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// OrganizationCreate is the builder for creating a Organization entity.
type OrganizationCreate struct {
	config
	mutation *OrganizationMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (oc *OrganizationCreate) SetCreatedAt(t time.Time) *OrganizationCreate {
	oc.mutation.SetCreatedAt(t)
	return oc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (oc *OrganizationCreate) SetNillableCreatedAt(t *time.Time) *OrganizationCreate {
	if t != nil {
		oc.SetCreatedAt(*t)
	}
	return oc
}

// SetUpdatedAt sets the "updated_at" field.
func (oc *OrganizationCreate) SetUpdatedAt(t time.Time) *OrganizationCreate {
	oc.mutation.SetUpdatedAt(t)
	return oc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (oc *OrganizationCreate) SetNillableUpdatedAt(t *time.Time) *OrganizationCreate {
	if t != nil {
		oc.SetUpdatedAt(*t)
	}
	return oc
}

// SetStatus sets the "status" field.
func (oc *OrganizationCreate) SetStatus(b bool) *OrganizationCreate {
	oc.mutation.SetStatus(b)
	return oc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (oc *OrganizationCreate) SetNillableStatus(b *bool) *OrganizationCreate {
	if b != nil {
		oc.SetStatus(*b)
	}
	return oc
}

// SetSort sets the "sort" field.
func (oc *OrganizationCreate) SetSort(u uint32) *OrganizationCreate {
	oc.mutation.SetSort(u)
	return oc
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (oc *OrganizationCreate) SetNillableSort(u *uint32) *OrganizationCreate {
	if u != nil {
		oc.SetSort(*u)
	}
	return oc
}

// SetTenantID sets the "tenant_id" field.
func (oc *OrganizationCreate) SetTenantID(s string) *OrganizationCreate {
	oc.mutation.SetTenantID(s)
	return oc
}

// SetDeletedAt sets the "deleted_at" field.
func (oc *OrganizationCreate) SetDeletedAt(t time.Time) *OrganizationCreate {
	oc.mutation.SetDeletedAt(t)
	return oc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (oc *OrganizationCreate) SetNillableDeletedAt(t *time.Time) *OrganizationCreate {
	if t != nil {
		oc.SetDeletedAt(*t)
	}
	return oc
}

// SetName sets the "name" field.
func (oc *OrganizationCreate) SetName(s string) *OrganizationCreate {
	oc.mutation.SetName(s)
	return oc
}

// SetAncestors sets the "ancestors" field.
func (oc *OrganizationCreate) SetAncestors(s string) *OrganizationCreate {
	oc.mutation.SetAncestors(s)
	return oc
}

// SetCode sets the "code" field.
func (oc *OrganizationCreate) SetCode(s string) *OrganizationCreate {
	oc.mutation.SetCode(s)
	return oc
}

// SetNodeType sets the "node_type" field.
func (oc *OrganizationCreate) SetNodeType(u uint32) *OrganizationCreate {
	oc.mutation.SetNodeType(u)
	return oc
}

// SetLeader sets the "leader" field.
func (oc *OrganizationCreate) SetLeader(s string) *OrganizationCreate {
	oc.mutation.SetLeader(s)
	return oc
}

// SetPhone sets the "phone" field.
func (oc *OrganizationCreate) SetPhone(s string) *OrganizationCreate {
	oc.mutation.SetPhone(s)
	return oc
}

// SetEmail sets the "email" field.
func (oc *OrganizationCreate) SetEmail(s string) *OrganizationCreate {
	oc.mutation.SetEmail(s)
	return oc
}

// SetRemark sets the "remark" field.
func (oc *OrganizationCreate) SetRemark(s string) *OrganizationCreate {
	oc.mutation.SetRemark(s)
	return oc
}

// SetParentID sets the "parent_id" field.
func (oc *OrganizationCreate) SetParentID(s string) *OrganizationCreate {
	oc.mutation.SetParentID(s)
	return oc
}

// SetNillableParentID sets the "parent_id" field if the given value is not nil.
func (oc *OrganizationCreate) SetNillableParentID(s *string) *OrganizationCreate {
	if s != nil {
		oc.SetParentID(*s)
	}
	return oc
}

// SetID sets the "id" field.
func (oc *OrganizationCreate) SetID(s string) *OrganizationCreate {
	oc.mutation.SetID(s)
	return oc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (oc *OrganizationCreate) SetNillableID(s *string) *OrganizationCreate {
	if s != nil {
		oc.SetID(*s)
	}
	return oc
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (oc *OrganizationCreate) SetTenant(t *Tenant) *OrganizationCreate {
	return oc.SetTenantID(t.ID)
}

// SetParent sets the "parent" edge to the Organization entity.
func (oc *OrganizationCreate) SetParent(o *Organization) *OrganizationCreate {
	return oc.SetParentID(o.ID)
}

// AddChildIDs adds the "children" edge to the Organization entity by IDs.
func (oc *OrganizationCreate) AddChildIDs(ids ...string) *OrganizationCreate {
	oc.mutation.AddChildIDs(ids...)
	return oc
}

// AddChildren adds the "children" edges to the Organization entity.
func (oc *OrganizationCreate) AddChildren(o ...*Organization) *OrganizationCreate {
	ids := make([]string, len(o))
	for i := range o {
		ids[i] = o[i].ID
	}
	return oc.AddChildIDs(ids...)
}

// AddUserIDs adds the "users" edge to the User entity by IDs.
func (oc *OrganizationCreate) AddUserIDs(ids ...string) *OrganizationCreate {
	oc.mutation.AddUserIDs(ids...)
	return oc
}

// AddUsers adds the "users" edges to the User entity.
func (oc *OrganizationCreate) AddUsers(u ...*User) *OrganizationCreate {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return oc.AddUserIDs(ids...)
}

// AddOrganizationInfoIDs adds the "organization_infos" edge to the OrganizationUserInfo entity by IDs.
func (oc *OrganizationCreate) AddOrganizationInfoIDs(ids ...string) *OrganizationCreate {
	oc.mutation.AddOrganizationInfoIDs(ids...)
	return oc
}

// AddOrganizationInfos adds the "organization_infos" edges to the OrganizationUserInfo entity.
func (oc *OrganizationCreate) AddOrganizationInfos(o ...*OrganizationUserInfo) *OrganizationCreate {
	ids := make([]string, len(o))
	for i := range o {
		ids[i] = o[i].ID
	}
	return oc.AddOrganizationInfoIDs(ids...)
}

// Mutation returns the OrganizationMutation object of the builder.
func (oc *OrganizationCreate) Mutation() *OrganizationMutation {
	return oc.mutation
}

// Save creates the Organization in the database.
func (oc *OrganizationCreate) Save(ctx context.Context) (*Organization, error) {
	if err := oc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, oc.sqlSave, oc.mutation, oc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (oc *OrganizationCreate) SaveX(ctx context.Context) *Organization {
	v, err := oc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (oc *OrganizationCreate) Exec(ctx context.Context) error {
	_, err := oc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (oc *OrganizationCreate) ExecX(ctx context.Context) {
	if err := oc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (oc *OrganizationCreate) defaults() error {
	if _, ok := oc.mutation.CreatedAt(); !ok {
		if organization.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized organization.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := organization.DefaultCreatedAt()
		oc.mutation.SetCreatedAt(v)
	}
	if _, ok := oc.mutation.UpdatedAt(); !ok {
		if organization.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized organization.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := organization.DefaultUpdatedAt()
		oc.mutation.SetUpdatedAt(v)
	}
	if _, ok := oc.mutation.Status(); !ok {
		v := organization.DefaultStatus
		oc.mutation.SetStatus(v)
	}
	if _, ok := oc.mutation.Sort(); !ok {
		v := organization.DefaultSort
		oc.mutation.SetSort(v)
	}
	if _, ok := oc.mutation.ID(); !ok {
		if organization.DefaultID == nil {
			return fmt.Errorf("ent: uninitialized organization.DefaultID (forgotten import ent/runtime?)")
		}
		v := organization.DefaultID()
		oc.mutation.SetID(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (oc *OrganizationCreate) check() error {
	if _, ok := oc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Organization.created_at"`)}
	}
	if _, ok := oc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Organization.updated_at"`)}
	}
	if _, ok := oc.mutation.Sort(); !ok {
		return &ValidationError{Name: "sort", err: errors.New(`ent: missing required field "Organization.sort"`)}
	}
	if _, ok := oc.mutation.TenantID(); !ok {
		return &ValidationError{Name: "tenant_id", err: errors.New(`ent: missing required field "Organization.tenant_id"`)}
	}
	if _, ok := oc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Organization.name"`)}
	}
	if _, ok := oc.mutation.Ancestors(); !ok {
		return &ValidationError{Name: "ancestors", err: errors.New(`ent: missing required field "Organization.ancestors"`)}
	}
	if _, ok := oc.mutation.Code(); !ok {
		return &ValidationError{Name: "code", err: errors.New(`ent: missing required field "Organization.code"`)}
	}
	if _, ok := oc.mutation.NodeType(); !ok {
		return &ValidationError{Name: "node_type", err: errors.New(`ent: missing required field "Organization.node_type"`)}
	}
	if _, ok := oc.mutation.Leader(); !ok {
		return &ValidationError{Name: "leader", err: errors.New(`ent: missing required field "Organization.leader"`)}
	}
	if _, ok := oc.mutation.Phone(); !ok {
		return &ValidationError{Name: "phone", err: errors.New(`ent: missing required field "Organization.phone"`)}
	}
	if _, ok := oc.mutation.Email(); !ok {
		return &ValidationError{Name: "email", err: errors.New(`ent: missing required field "Organization.email"`)}
	}
	if _, ok := oc.mutation.Remark(); !ok {
		return &ValidationError{Name: "remark", err: errors.New(`ent: missing required field "Organization.remark"`)}
	}
	if _, ok := oc.mutation.TenantID(); !ok {
		return &ValidationError{Name: "tenant", err: errors.New(`ent: missing required edge "Organization.tenant"`)}
	}
	return nil
}

func (oc *OrganizationCreate) sqlSave(ctx context.Context) (*Organization, error) {
	if err := oc.check(); err != nil {
		return nil, err
	}
	_node, _spec := oc.createSpec()
	if err := sqlgraph.CreateNode(ctx, oc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected Organization.ID type: %T", _spec.ID.Value)
		}
	}
	oc.mutation.id = &_node.ID
	oc.mutation.done = true
	return _node, nil
}

func (oc *OrganizationCreate) createSpec() (*Organization, *sqlgraph.CreateSpec) {
	var (
		_node = &Organization{config: oc.config}
		_spec = sqlgraph.NewCreateSpec(organization.Table, sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString))
	)
	_spec.OnConflict = oc.conflict
	if id, ok := oc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := oc.mutation.CreatedAt(); ok {
		_spec.SetField(organization.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := oc.mutation.UpdatedAt(); ok {
		_spec.SetField(organization.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := oc.mutation.Status(); ok {
		_spec.SetField(organization.FieldStatus, field.TypeBool, value)
		_node.Status = value
	}
	if value, ok := oc.mutation.Sort(); ok {
		_spec.SetField(organization.FieldSort, field.TypeUint32, value)
		_node.Sort = value
	}
	if value, ok := oc.mutation.DeletedAt(); ok {
		_spec.SetField(organization.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := oc.mutation.Name(); ok {
		_spec.SetField(organization.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := oc.mutation.Ancestors(); ok {
		_spec.SetField(organization.FieldAncestors, field.TypeString, value)
		_node.Ancestors = value
	}
	if value, ok := oc.mutation.Code(); ok {
		_spec.SetField(organization.FieldCode, field.TypeString, value)
		_node.Code = value
	}
	if value, ok := oc.mutation.NodeType(); ok {
		_spec.SetField(organization.FieldNodeType, field.TypeUint32, value)
		_node.NodeType = value
	}
	if value, ok := oc.mutation.Leader(); ok {
		_spec.SetField(organization.FieldLeader, field.TypeString, value)
		_node.Leader = value
	}
	if value, ok := oc.mutation.Phone(); ok {
		_spec.SetField(organization.FieldPhone, field.TypeString, value)
		_node.Phone = value
	}
	if value, ok := oc.mutation.Email(); ok {
		_spec.SetField(organization.FieldEmail, field.TypeString, value)
		_node.Email = value
	}
	if value, ok := oc.mutation.Remark(); ok {
		_spec.SetField(organization.FieldRemark, field.TypeString, value)
		_node.Remark = value
	}
	if nodes := oc.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   organization.TenantTable,
			Columns: []string{organization.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TenantID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := oc.mutation.ParentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   organization.ParentTable,
			Columns: []string{organization.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.ParentID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := oc.mutation.ChildrenIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   organization.ChildrenTable,
			Columns: []string{organization.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := oc.mutation.UsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   organization.UsersTable,
			Columns: organization.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := oc.mutation.OrganizationInfosIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   organization.OrganizationInfosTable,
			Columns: []string{organization.OrganizationInfosColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organizationuserinfo.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Organization.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.OrganizationUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (oc *OrganizationCreate) OnConflict(opts ...sql.ConflictOption) *OrganizationUpsertOne {
	oc.conflict = opts
	return &OrganizationUpsertOne{
		create: oc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Organization.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (oc *OrganizationCreate) OnConflictColumns(columns ...string) *OrganizationUpsertOne {
	oc.conflict = append(oc.conflict, sql.ConflictColumns(columns...))
	return &OrganizationUpsertOne{
		create: oc,
	}
}

type (
	// OrganizationUpsertOne is the builder for "upsert"-ing
	//  one Organization node.
	OrganizationUpsertOne struct {
		create *OrganizationCreate
	}

	// OrganizationUpsert is the "OnConflict" setter.
	OrganizationUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *OrganizationUpsert) SetUpdatedAt(v time.Time) *OrganizationUpsert {
	u.Set(organization.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *OrganizationUpsert) UpdateUpdatedAt() *OrganizationUpsert {
	u.SetExcluded(organization.FieldUpdatedAt)
	return u
}

// SetStatus sets the "status" field.
func (u *OrganizationUpsert) SetStatus(v bool) *OrganizationUpsert {
	u.Set(organization.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *OrganizationUpsert) UpdateStatus() *OrganizationUpsert {
	u.SetExcluded(organization.FieldStatus)
	return u
}

// ClearStatus clears the value of the "status" field.
func (u *OrganizationUpsert) ClearStatus() *OrganizationUpsert {
	u.SetNull(organization.FieldStatus)
	return u
}

// SetSort sets the "sort" field.
func (u *OrganizationUpsert) SetSort(v uint32) *OrganizationUpsert {
	u.Set(organization.FieldSort, v)
	return u
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *OrganizationUpsert) UpdateSort() *OrganizationUpsert {
	u.SetExcluded(organization.FieldSort)
	return u
}

// AddSort adds v to the "sort" field.
func (u *OrganizationUpsert) AddSort(v uint32) *OrganizationUpsert {
	u.Add(organization.FieldSort, v)
	return u
}

// SetTenantID sets the "tenant_id" field.
func (u *OrganizationUpsert) SetTenantID(v string) *OrganizationUpsert {
	u.Set(organization.FieldTenantID, v)
	return u
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *OrganizationUpsert) UpdateTenantID() *OrganizationUpsert {
	u.SetExcluded(organization.FieldTenantID)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *OrganizationUpsert) SetDeletedAt(v time.Time) *OrganizationUpsert {
	u.Set(organization.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *OrganizationUpsert) UpdateDeletedAt() *OrganizationUpsert {
	u.SetExcluded(organization.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *OrganizationUpsert) ClearDeletedAt() *OrganizationUpsert {
	u.SetNull(organization.FieldDeletedAt)
	return u
}

// SetName sets the "name" field.
func (u *OrganizationUpsert) SetName(v string) *OrganizationUpsert {
	u.Set(organization.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *OrganizationUpsert) UpdateName() *OrganizationUpsert {
	u.SetExcluded(organization.FieldName)
	return u
}

// SetAncestors sets the "ancestors" field.
func (u *OrganizationUpsert) SetAncestors(v string) *OrganizationUpsert {
	u.Set(organization.FieldAncestors, v)
	return u
}

// UpdateAncestors sets the "ancestors" field to the value that was provided on create.
func (u *OrganizationUpsert) UpdateAncestors() *OrganizationUpsert {
	u.SetExcluded(organization.FieldAncestors)
	return u
}

// SetCode sets the "code" field.
func (u *OrganizationUpsert) SetCode(v string) *OrganizationUpsert {
	u.Set(organization.FieldCode, v)
	return u
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *OrganizationUpsert) UpdateCode() *OrganizationUpsert {
	u.SetExcluded(organization.FieldCode)
	return u
}

// SetNodeType sets the "node_type" field.
func (u *OrganizationUpsert) SetNodeType(v uint32) *OrganizationUpsert {
	u.Set(organization.FieldNodeType, v)
	return u
}

// UpdateNodeType sets the "node_type" field to the value that was provided on create.
func (u *OrganizationUpsert) UpdateNodeType() *OrganizationUpsert {
	u.SetExcluded(organization.FieldNodeType)
	return u
}

// AddNodeType adds v to the "node_type" field.
func (u *OrganizationUpsert) AddNodeType(v uint32) *OrganizationUpsert {
	u.Add(organization.FieldNodeType, v)
	return u
}

// SetLeader sets the "leader" field.
func (u *OrganizationUpsert) SetLeader(v string) *OrganizationUpsert {
	u.Set(organization.FieldLeader, v)
	return u
}

// UpdateLeader sets the "leader" field to the value that was provided on create.
func (u *OrganizationUpsert) UpdateLeader() *OrganizationUpsert {
	u.SetExcluded(organization.FieldLeader)
	return u
}

// SetPhone sets the "phone" field.
func (u *OrganizationUpsert) SetPhone(v string) *OrganizationUpsert {
	u.Set(organization.FieldPhone, v)
	return u
}

// UpdatePhone sets the "phone" field to the value that was provided on create.
func (u *OrganizationUpsert) UpdatePhone() *OrganizationUpsert {
	u.SetExcluded(organization.FieldPhone)
	return u
}

// SetEmail sets the "email" field.
func (u *OrganizationUpsert) SetEmail(v string) *OrganizationUpsert {
	u.Set(organization.FieldEmail, v)
	return u
}

// UpdateEmail sets the "email" field to the value that was provided on create.
func (u *OrganizationUpsert) UpdateEmail() *OrganizationUpsert {
	u.SetExcluded(organization.FieldEmail)
	return u
}

// SetRemark sets the "remark" field.
func (u *OrganizationUpsert) SetRemark(v string) *OrganizationUpsert {
	u.Set(organization.FieldRemark, v)
	return u
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *OrganizationUpsert) UpdateRemark() *OrganizationUpsert {
	u.SetExcluded(organization.FieldRemark)
	return u
}

// SetParentID sets the "parent_id" field.
func (u *OrganizationUpsert) SetParentID(v string) *OrganizationUpsert {
	u.Set(organization.FieldParentID, v)
	return u
}

// UpdateParentID sets the "parent_id" field to the value that was provided on create.
func (u *OrganizationUpsert) UpdateParentID() *OrganizationUpsert {
	u.SetExcluded(organization.FieldParentID)
	return u
}

// ClearParentID clears the value of the "parent_id" field.
func (u *OrganizationUpsert) ClearParentID() *OrganizationUpsert {
	u.SetNull(organization.FieldParentID)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Organization.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(organization.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *OrganizationUpsertOne) UpdateNewValues() *OrganizationUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(organization.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(organization.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Organization.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *OrganizationUpsertOne) Ignore() *OrganizationUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *OrganizationUpsertOne) DoNothing() *OrganizationUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the OrganizationCreate.OnConflict
// documentation for more info.
func (u *OrganizationUpsertOne) Update(set func(*OrganizationUpsert)) *OrganizationUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&OrganizationUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *OrganizationUpsertOne) SetUpdatedAt(v time.Time) *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *OrganizationUpsertOne) UpdateUpdatedAt() *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetStatus sets the "status" field.
func (u *OrganizationUpsertOne) SetStatus(v bool) *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *OrganizationUpsertOne) UpdateStatus() *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *OrganizationUpsertOne) ClearStatus() *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.ClearStatus()
	})
}

// SetSort sets the "sort" field.
func (u *OrganizationUpsertOne) SetSort(v uint32) *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetSort(v)
	})
}

// AddSort adds v to the "sort" field.
func (u *OrganizationUpsertOne) AddSort(v uint32) *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.AddSort(v)
	})
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *OrganizationUpsertOne) UpdateSort() *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateSort()
	})
}

// SetTenantID sets the "tenant_id" field.
func (u *OrganizationUpsertOne) SetTenantID(v string) *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *OrganizationUpsertOne) UpdateTenantID() *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateTenantID()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *OrganizationUpsertOne) SetDeletedAt(v time.Time) *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *OrganizationUpsertOne) UpdateDeletedAt() *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *OrganizationUpsertOne) ClearDeletedAt() *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.ClearDeletedAt()
	})
}

// SetName sets the "name" field.
func (u *OrganizationUpsertOne) SetName(v string) *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *OrganizationUpsertOne) UpdateName() *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateName()
	})
}

// SetAncestors sets the "ancestors" field.
func (u *OrganizationUpsertOne) SetAncestors(v string) *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetAncestors(v)
	})
}

// UpdateAncestors sets the "ancestors" field to the value that was provided on create.
func (u *OrganizationUpsertOne) UpdateAncestors() *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateAncestors()
	})
}

// SetCode sets the "code" field.
func (u *OrganizationUpsertOne) SetCode(v string) *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetCode(v)
	})
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *OrganizationUpsertOne) UpdateCode() *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateCode()
	})
}

// SetNodeType sets the "node_type" field.
func (u *OrganizationUpsertOne) SetNodeType(v uint32) *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetNodeType(v)
	})
}

// AddNodeType adds v to the "node_type" field.
func (u *OrganizationUpsertOne) AddNodeType(v uint32) *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.AddNodeType(v)
	})
}

// UpdateNodeType sets the "node_type" field to the value that was provided on create.
func (u *OrganizationUpsertOne) UpdateNodeType() *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateNodeType()
	})
}

// SetLeader sets the "leader" field.
func (u *OrganizationUpsertOne) SetLeader(v string) *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetLeader(v)
	})
}

// UpdateLeader sets the "leader" field to the value that was provided on create.
func (u *OrganizationUpsertOne) UpdateLeader() *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateLeader()
	})
}

// SetPhone sets the "phone" field.
func (u *OrganizationUpsertOne) SetPhone(v string) *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetPhone(v)
	})
}

// UpdatePhone sets the "phone" field to the value that was provided on create.
func (u *OrganizationUpsertOne) UpdatePhone() *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdatePhone()
	})
}

// SetEmail sets the "email" field.
func (u *OrganizationUpsertOne) SetEmail(v string) *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetEmail(v)
	})
}

// UpdateEmail sets the "email" field to the value that was provided on create.
func (u *OrganizationUpsertOne) UpdateEmail() *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateEmail()
	})
}

// SetRemark sets the "remark" field.
func (u *OrganizationUpsertOne) SetRemark(v string) *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *OrganizationUpsertOne) UpdateRemark() *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateRemark()
	})
}

// SetParentID sets the "parent_id" field.
func (u *OrganizationUpsertOne) SetParentID(v string) *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetParentID(v)
	})
}

// UpdateParentID sets the "parent_id" field to the value that was provided on create.
func (u *OrganizationUpsertOne) UpdateParentID() *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateParentID()
	})
}

// ClearParentID clears the value of the "parent_id" field.
func (u *OrganizationUpsertOne) ClearParentID() *OrganizationUpsertOne {
	return u.Update(func(s *OrganizationUpsert) {
		s.ClearParentID()
	})
}

// Exec executes the query.
func (u *OrganizationUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for OrganizationCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *OrganizationUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *OrganizationUpsertOne) ID(ctx context.Context) (id string, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: OrganizationUpsertOne.ID is not supported by MySQL driver. Use OrganizationUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *OrganizationUpsertOne) IDX(ctx context.Context) string {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// OrganizationCreateBulk is the builder for creating many Organization entities in bulk.
type OrganizationCreateBulk struct {
	config
	err      error
	builders []*OrganizationCreate
	conflict []sql.ConflictOption
}

// Save creates the Organization entities in the database.
func (ocb *OrganizationCreateBulk) Save(ctx context.Context) ([]*Organization, error) {
	if ocb.err != nil {
		return nil, ocb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(ocb.builders))
	nodes := make([]*Organization, len(ocb.builders))
	mutators := make([]Mutator, len(ocb.builders))
	for i := range ocb.builders {
		func(i int, root context.Context) {
			builder := ocb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*OrganizationMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, ocb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = ocb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, ocb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, ocb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (ocb *OrganizationCreateBulk) SaveX(ctx context.Context) []*Organization {
	v, err := ocb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ocb *OrganizationCreateBulk) Exec(ctx context.Context) error {
	_, err := ocb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ocb *OrganizationCreateBulk) ExecX(ctx context.Context) {
	if err := ocb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Organization.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.OrganizationUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (ocb *OrganizationCreateBulk) OnConflict(opts ...sql.ConflictOption) *OrganizationUpsertBulk {
	ocb.conflict = opts
	return &OrganizationUpsertBulk{
		create: ocb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Organization.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (ocb *OrganizationCreateBulk) OnConflictColumns(columns ...string) *OrganizationUpsertBulk {
	ocb.conflict = append(ocb.conflict, sql.ConflictColumns(columns...))
	return &OrganizationUpsertBulk{
		create: ocb,
	}
}

// OrganizationUpsertBulk is the builder for "upsert"-ing
// a bulk of Organization nodes.
type OrganizationUpsertBulk struct {
	create *OrganizationCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Organization.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(organization.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *OrganizationUpsertBulk) UpdateNewValues() *OrganizationUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(organization.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(organization.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Organization.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *OrganizationUpsertBulk) Ignore() *OrganizationUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *OrganizationUpsertBulk) DoNothing() *OrganizationUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the OrganizationCreateBulk.OnConflict
// documentation for more info.
func (u *OrganizationUpsertBulk) Update(set func(*OrganizationUpsert)) *OrganizationUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&OrganizationUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *OrganizationUpsertBulk) SetUpdatedAt(v time.Time) *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *OrganizationUpsertBulk) UpdateUpdatedAt() *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetStatus sets the "status" field.
func (u *OrganizationUpsertBulk) SetStatus(v bool) *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *OrganizationUpsertBulk) UpdateStatus() *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *OrganizationUpsertBulk) ClearStatus() *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.ClearStatus()
	})
}

// SetSort sets the "sort" field.
func (u *OrganizationUpsertBulk) SetSort(v uint32) *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetSort(v)
	})
}

// AddSort adds v to the "sort" field.
func (u *OrganizationUpsertBulk) AddSort(v uint32) *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.AddSort(v)
	})
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *OrganizationUpsertBulk) UpdateSort() *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateSort()
	})
}

// SetTenantID sets the "tenant_id" field.
func (u *OrganizationUpsertBulk) SetTenantID(v string) *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *OrganizationUpsertBulk) UpdateTenantID() *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateTenantID()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *OrganizationUpsertBulk) SetDeletedAt(v time.Time) *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *OrganizationUpsertBulk) UpdateDeletedAt() *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *OrganizationUpsertBulk) ClearDeletedAt() *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.ClearDeletedAt()
	})
}

// SetName sets the "name" field.
func (u *OrganizationUpsertBulk) SetName(v string) *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *OrganizationUpsertBulk) UpdateName() *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateName()
	})
}

// SetAncestors sets the "ancestors" field.
func (u *OrganizationUpsertBulk) SetAncestors(v string) *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetAncestors(v)
	})
}

// UpdateAncestors sets the "ancestors" field to the value that was provided on create.
func (u *OrganizationUpsertBulk) UpdateAncestors() *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateAncestors()
	})
}

// SetCode sets the "code" field.
func (u *OrganizationUpsertBulk) SetCode(v string) *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetCode(v)
	})
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *OrganizationUpsertBulk) UpdateCode() *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateCode()
	})
}

// SetNodeType sets the "node_type" field.
func (u *OrganizationUpsertBulk) SetNodeType(v uint32) *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetNodeType(v)
	})
}

// AddNodeType adds v to the "node_type" field.
func (u *OrganizationUpsertBulk) AddNodeType(v uint32) *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.AddNodeType(v)
	})
}

// UpdateNodeType sets the "node_type" field to the value that was provided on create.
func (u *OrganizationUpsertBulk) UpdateNodeType() *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateNodeType()
	})
}

// SetLeader sets the "leader" field.
func (u *OrganizationUpsertBulk) SetLeader(v string) *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetLeader(v)
	})
}

// UpdateLeader sets the "leader" field to the value that was provided on create.
func (u *OrganizationUpsertBulk) UpdateLeader() *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateLeader()
	})
}

// SetPhone sets the "phone" field.
func (u *OrganizationUpsertBulk) SetPhone(v string) *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetPhone(v)
	})
}

// UpdatePhone sets the "phone" field to the value that was provided on create.
func (u *OrganizationUpsertBulk) UpdatePhone() *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdatePhone()
	})
}

// SetEmail sets the "email" field.
func (u *OrganizationUpsertBulk) SetEmail(v string) *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetEmail(v)
	})
}

// UpdateEmail sets the "email" field to the value that was provided on create.
func (u *OrganizationUpsertBulk) UpdateEmail() *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateEmail()
	})
}

// SetRemark sets the "remark" field.
func (u *OrganizationUpsertBulk) SetRemark(v string) *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *OrganizationUpsertBulk) UpdateRemark() *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateRemark()
	})
}

// SetParentID sets the "parent_id" field.
func (u *OrganizationUpsertBulk) SetParentID(v string) *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.SetParentID(v)
	})
}

// UpdateParentID sets the "parent_id" field to the value that was provided on create.
func (u *OrganizationUpsertBulk) UpdateParentID() *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.UpdateParentID()
	})
}

// ClearParentID clears the value of the "parent_id" field.
func (u *OrganizationUpsertBulk) ClearParentID() *OrganizationUpsertBulk {
	return u.Update(func(s *OrganizationUpsert) {
		s.ClearParentID()
	})
}

// Exec executes the query.
func (u *OrganizationUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the OrganizationCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for OrganizationCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *OrganizationUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
