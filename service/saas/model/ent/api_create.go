// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/api"
	"phoenix/service/saas/model/ent/role"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// APICreate is the builder for creating a API entity.
type APICreate struct {
	config
	mutation *APIMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (ac *APICreate) SetCreatedAt(t time.Time) *APICreate {
	ac.mutation.SetCreatedAt(t)
	return ac
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (ac *APICreate) SetNillableCreatedAt(t *time.Time) *APICreate {
	if t != nil {
		ac.SetCreatedAt(*t)
	}
	return ac
}

// SetUpdatedAt sets the "updated_at" field.
func (ac *APICreate) SetUpdatedAt(t time.Time) *APICreate {
	ac.mutation.SetUpdatedAt(t)
	return ac
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (ac *APICreate) SetNillableUpdatedAt(t *time.Time) *APICreate {
	if t != nil {
		ac.SetUpdatedAt(*t)
	}
	return ac
}

// SetStatus sets the "status" field.
func (ac *APICreate) SetStatus(b bool) *APICreate {
	ac.mutation.SetStatus(b)
	return ac
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ac *APICreate) SetNillableStatus(b *bool) *APICreate {
	if b != nil {
		ac.SetStatus(*b)
	}
	return ac
}

// SetDeletedAt sets the "deleted_at" field.
func (ac *APICreate) SetDeletedAt(t time.Time) *APICreate {
	ac.mutation.SetDeletedAt(t)
	return ac
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (ac *APICreate) SetNillableDeletedAt(t *time.Time) *APICreate {
	if t != nil {
		ac.SetDeletedAt(*t)
	}
	return ac
}

// SetPath sets the "path" field.
func (ac *APICreate) SetPath(s string) *APICreate {
	ac.mutation.SetPath(s)
	return ac
}

// SetDescription sets the "description" field.
func (ac *APICreate) SetDescription(s string) *APICreate {
	ac.mutation.SetDescription(s)
	return ac
}

// SetAPIGroup sets the "api_group" field.
func (ac *APICreate) SetAPIGroup(s string) *APICreate {
	ac.mutation.SetAPIGroup(s)
	return ac
}

// SetMethod sets the "method" field.
func (ac *APICreate) SetMethod(s string) *APICreate {
	ac.mutation.SetMethod(s)
	return ac
}

// SetNillableMethod sets the "method" field if the given value is not nil.
func (ac *APICreate) SetNillableMethod(s *string) *APICreate {
	if s != nil {
		ac.SetMethod(*s)
	}
	return ac
}

// SetKind sets the "kind" field.
func (ac *APICreate) SetKind(s string) *APICreate {
	ac.mutation.SetKind(s)
	return ac
}

// SetModule sets the "module" field.
func (ac *APICreate) SetModule(s string) *APICreate {
	ac.mutation.SetModule(s)
	return ac
}

// SetID sets the "id" field.
func (ac *APICreate) SetID(s string) *APICreate {
	ac.mutation.SetID(s)
	return ac
}

// SetNillableID sets the "id" field if the given value is not nil.
func (ac *APICreate) SetNillableID(s *string) *APICreate {
	if s != nil {
		ac.SetID(*s)
	}
	return ac
}

// AddRoleIDs adds the "roles" edge to the Role entity by IDs.
func (ac *APICreate) AddRoleIDs(ids ...string) *APICreate {
	ac.mutation.AddRoleIDs(ids...)
	return ac
}

// AddRoles adds the "roles" edges to the Role entity.
func (ac *APICreate) AddRoles(r ...*Role) *APICreate {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return ac.AddRoleIDs(ids...)
}

// Mutation returns the APIMutation object of the builder.
func (ac *APICreate) Mutation() *APIMutation {
	return ac.mutation
}

// Save creates the API in the database.
func (ac *APICreate) Save(ctx context.Context) (*API, error) {
	if err := ac.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, ac.sqlSave, ac.mutation, ac.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (ac *APICreate) SaveX(ctx context.Context) *API {
	v, err := ac.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ac *APICreate) Exec(ctx context.Context) error {
	_, err := ac.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ac *APICreate) ExecX(ctx context.Context) {
	if err := ac.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ac *APICreate) defaults() error {
	if _, ok := ac.mutation.CreatedAt(); !ok {
		if api.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized api.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := api.DefaultCreatedAt()
		ac.mutation.SetCreatedAt(v)
	}
	if _, ok := ac.mutation.UpdatedAt(); !ok {
		if api.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized api.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := api.DefaultUpdatedAt()
		ac.mutation.SetUpdatedAt(v)
	}
	if _, ok := ac.mutation.Status(); !ok {
		v := api.DefaultStatus
		ac.mutation.SetStatus(v)
	}
	if _, ok := ac.mutation.Method(); !ok {
		v := api.DefaultMethod
		ac.mutation.SetMethod(v)
	}
	if _, ok := ac.mutation.ID(); !ok {
		if api.DefaultID == nil {
			return fmt.Errorf("ent: uninitialized api.DefaultID (forgotten import ent/runtime?)")
		}
		v := api.DefaultID()
		ac.mutation.SetID(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (ac *APICreate) check() error {
	if _, ok := ac.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "API.created_at"`)}
	}
	if _, ok := ac.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "API.updated_at"`)}
	}
	if _, ok := ac.mutation.Path(); !ok {
		return &ValidationError{Name: "path", err: errors.New(`ent: missing required field "API.path"`)}
	}
	if _, ok := ac.mutation.Description(); !ok {
		return &ValidationError{Name: "description", err: errors.New(`ent: missing required field "API.description"`)}
	}
	if _, ok := ac.mutation.APIGroup(); !ok {
		return &ValidationError{Name: "api_group", err: errors.New(`ent: missing required field "API.api_group"`)}
	}
	if _, ok := ac.mutation.Method(); !ok {
		return &ValidationError{Name: "method", err: errors.New(`ent: missing required field "API.method"`)}
	}
	if _, ok := ac.mutation.Kind(); !ok {
		return &ValidationError{Name: "kind", err: errors.New(`ent: missing required field "API.kind"`)}
	}
	if _, ok := ac.mutation.Module(); !ok {
		return &ValidationError{Name: "module", err: errors.New(`ent: missing required field "API.module"`)}
	}
	return nil
}

func (ac *APICreate) sqlSave(ctx context.Context) (*API, error) {
	if err := ac.check(); err != nil {
		return nil, err
	}
	_node, _spec := ac.createSpec()
	if err := sqlgraph.CreateNode(ctx, ac.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected API.ID type: %T", _spec.ID.Value)
		}
	}
	ac.mutation.id = &_node.ID
	ac.mutation.done = true
	return _node, nil
}

func (ac *APICreate) createSpec() (*API, *sqlgraph.CreateSpec) {
	var (
		_node = &API{config: ac.config}
		_spec = sqlgraph.NewCreateSpec(api.Table, sqlgraph.NewFieldSpec(api.FieldID, field.TypeString))
	)
	_spec.OnConflict = ac.conflict
	if id, ok := ac.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := ac.mutation.CreatedAt(); ok {
		_spec.SetField(api.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := ac.mutation.UpdatedAt(); ok {
		_spec.SetField(api.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := ac.mutation.Status(); ok {
		_spec.SetField(api.FieldStatus, field.TypeBool, value)
		_node.Status = value
	}
	if value, ok := ac.mutation.DeletedAt(); ok {
		_spec.SetField(api.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := ac.mutation.Path(); ok {
		_spec.SetField(api.FieldPath, field.TypeString, value)
		_node.Path = value
	}
	if value, ok := ac.mutation.Description(); ok {
		_spec.SetField(api.FieldDescription, field.TypeString, value)
		_node.Description = value
	}
	if value, ok := ac.mutation.APIGroup(); ok {
		_spec.SetField(api.FieldAPIGroup, field.TypeString, value)
		_node.APIGroup = value
	}
	if value, ok := ac.mutation.Method(); ok {
		_spec.SetField(api.FieldMethod, field.TypeString, value)
		_node.Method = value
	}
	if value, ok := ac.mutation.Kind(); ok {
		_spec.SetField(api.FieldKind, field.TypeString, value)
		_node.Kind = value
	}
	if value, ok := ac.mutation.Module(); ok {
		_spec.SetField(api.FieldModule, field.TypeString, value)
		_node.Module = value
	}
	if nodes := ac.mutation.RolesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   api.RolesTable,
			Columns: api.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.API.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.APIUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (ac *APICreate) OnConflict(opts ...sql.ConflictOption) *APIUpsertOne {
	ac.conflict = opts
	return &APIUpsertOne{
		create: ac,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.API.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (ac *APICreate) OnConflictColumns(columns ...string) *APIUpsertOne {
	ac.conflict = append(ac.conflict, sql.ConflictColumns(columns...))
	return &APIUpsertOne{
		create: ac,
	}
}

type (
	// APIUpsertOne is the builder for "upsert"-ing
	//  one API node.
	APIUpsertOne struct {
		create *APICreate
	}

	// APIUpsert is the "OnConflict" setter.
	APIUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *APIUpsert) SetUpdatedAt(v time.Time) *APIUpsert {
	u.Set(api.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *APIUpsert) UpdateUpdatedAt() *APIUpsert {
	u.SetExcluded(api.FieldUpdatedAt)
	return u
}

// SetStatus sets the "status" field.
func (u *APIUpsert) SetStatus(v bool) *APIUpsert {
	u.Set(api.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *APIUpsert) UpdateStatus() *APIUpsert {
	u.SetExcluded(api.FieldStatus)
	return u
}

// ClearStatus clears the value of the "status" field.
func (u *APIUpsert) ClearStatus() *APIUpsert {
	u.SetNull(api.FieldStatus)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *APIUpsert) SetDeletedAt(v time.Time) *APIUpsert {
	u.Set(api.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *APIUpsert) UpdateDeletedAt() *APIUpsert {
	u.SetExcluded(api.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *APIUpsert) ClearDeletedAt() *APIUpsert {
	u.SetNull(api.FieldDeletedAt)
	return u
}

// SetPath sets the "path" field.
func (u *APIUpsert) SetPath(v string) *APIUpsert {
	u.Set(api.FieldPath, v)
	return u
}

// UpdatePath sets the "path" field to the value that was provided on create.
func (u *APIUpsert) UpdatePath() *APIUpsert {
	u.SetExcluded(api.FieldPath)
	return u
}

// SetDescription sets the "description" field.
func (u *APIUpsert) SetDescription(v string) *APIUpsert {
	u.Set(api.FieldDescription, v)
	return u
}

// UpdateDescription sets the "description" field to the value that was provided on create.
func (u *APIUpsert) UpdateDescription() *APIUpsert {
	u.SetExcluded(api.FieldDescription)
	return u
}

// SetAPIGroup sets the "api_group" field.
func (u *APIUpsert) SetAPIGroup(v string) *APIUpsert {
	u.Set(api.FieldAPIGroup, v)
	return u
}

// UpdateAPIGroup sets the "api_group" field to the value that was provided on create.
func (u *APIUpsert) UpdateAPIGroup() *APIUpsert {
	u.SetExcluded(api.FieldAPIGroup)
	return u
}

// SetMethod sets the "method" field.
func (u *APIUpsert) SetMethod(v string) *APIUpsert {
	u.Set(api.FieldMethod, v)
	return u
}

// UpdateMethod sets the "method" field to the value that was provided on create.
func (u *APIUpsert) UpdateMethod() *APIUpsert {
	u.SetExcluded(api.FieldMethod)
	return u
}

// SetKind sets the "kind" field.
func (u *APIUpsert) SetKind(v string) *APIUpsert {
	u.Set(api.FieldKind, v)
	return u
}

// UpdateKind sets the "kind" field to the value that was provided on create.
func (u *APIUpsert) UpdateKind() *APIUpsert {
	u.SetExcluded(api.FieldKind)
	return u
}

// SetModule sets the "module" field.
func (u *APIUpsert) SetModule(v string) *APIUpsert {
	u.Set(api.FieldModule, v)
	return u
}

// UpdateModule sets the "module" field to the value that was provided on create.
func (u *APIUpsert) UpdateModule() *APIUpsert {
	u.SetExcluded(api.FieldModule)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.API.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(api.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *APIUpsertOne) UpdateNewValues() *APIUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(api.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(api.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.API.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *APIUpsertOne) Ignore() *APIUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *APIUpsertOne) DoNothing() *APIUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the APICreate.OnConflict
// documentation for more info.
func (u *APIUpsertOne) Update(set func(*APIUpsert)) *APIUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&APIUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *APIUpsertOne) SetUpdatedAt(v time.Time) *APIUpsertOne {
	return u.Update(func(s *APIUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *APIUpsertOne) UpdateUpdatedAt() *APIUpsertOne {
	return u.Update(func(s *APIUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetStatus sets the "status" field.
func (u *APIUpsertOne) SetStatus(v bool) *APIUpsertOne {
	return u.Update(func(s *APIUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *APIUpsertOne) UpdateStatus() *APIUpsertOne {
	return u.Update(func(s *APIUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *APIUpsertOne) ClearStatus() *APIUpsertOne {
	return u.Update(func(s *APIUpsert) {
		s.ClearStatus()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *APIUpsertOne) SetDeletedAt(v time.Time) *APIUpsertOne {
	return u.Update(func(s *APIUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *APIUpsertOne) UpdateDeletedAt() *APIUpsertOne {
	return u.Update(func(s *APIUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *APIUpsertOne) ClearDeletedAt() *APIUpsertOne {
	return u.Update(func(s *APIUpsert) {
		s.ClearDeletedAt()
	})
}

// SetPath sets the "path" field.
func (u *APIUpsertOne) SetPath(v string) *APIUpsertOne {
	return u.Update(func(s *APIUpsert) {
		s.SetPath(v)
	})
}

// UpdatePath sets the "path" field to the value that was provided on create.
func (u *APIUpsertOne) UpdatePath() *APIUpsertOne {
	return u.Update(func(s *APIUpsert) {
		s.UpdatePath()
	})
}

// SetDescription sets the "description" field.
func (u *APIUpsertOne) SetDescription(v string) *APIUpsertOne {
	return u.Update(func(s *APIUpsert) {
		s.SetDescription(v)
	})
}

// UpdateDescription sets the "description" field to the value that was provided on create.
func (u *APIUpsertOne) UpdateDescription() *APIUpsertOne {
	return u.Update(func(s *APIUpsert) {
		s.UpdateDescription()
	})
}

// SetAPIGroup sets the "api_group" field.
func (u *APIUpsertOne) SetAPIGroup(v string) *APIUpsertOne {
	return u.Update(func(s *APIUpsert) {
		s.SetAPIGroup(v)
	})
}

// UpdateAPIGroup sets the "api_group" field to the value that was provided on create.
func (u *APIUpsertOne) UpdateAPIGroup() *APIUpsertOne {
	return u.Update(func(s *APIUpsert) {
		s.UpdateAPIGroup()
	})
}

// SetMethod sets the "method" field.
func (u *APIUpsertOne) SetMethod(v string) *APIUpsertOne {
	return u.Update(func(s *APIUpsert) {
		s.SetMethod(v)
	})
}

// UpdateMethod sets the "method" field to the value that was provided on create.
func (u *APIUpsertOne) UpdateMethod() *APIUpsertOne {
	return u.Update(func(s *APIUpsert) {
		s.UpdateMethod()
	})
}

// SetKind sets the "kind" field.
func (u *APIUpsertOne) SetKind(v string) *APIUpsertOne {
	return u.Update(func(s *APIUpsert) {
		s.SetKind(v)
	})
}

// UpdateKind sets the "kind" field to the value that was provided on create.
func (u *APIUpsertOne) UpdateKind() *APIUpsertOne {
	return u.Update(func(s *APIUpsert) {
		s.UpdateKind()
	})
}

// SetModule sets the "module" field.
func (u *APIUpsertOne) SetModule(v string) *APIUpsertOne {
	return u.Update(func(s *APIUpsert) {
		s.SetModule(v)
	})
}

// UpdateModule sets the "module" field to the value that was provided on create.
func (u *APIUpsertOne) UpdateModule() *APIUpsertOne {
	return u.Update(func(s *APIUpsert) {
		s.UpdateModule()
	})
}

// Exec executes the query.
func (u *APIUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for APICreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *APIUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *APIUpsertOne) ID(ctx context.Context) (id string, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: APIUpsertOne.ID is not supported by MySQL driver. Use APIUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *APIUpsertOne) IDX(ctx context.Context) string {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// APICreateBulk is the builder for creating many API entities in bulk.
type APICreateBulk struct {
	config
	err      error
	builders []*APICreate
	conflict []sql.ConflictOption
}

// Save creates the API entities in the database.
func (acb *APICreateBulk) Save(ctx context.Context) ([]*API, error) {
	if acb.err != nil {
		return nil, acb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(acb.builders))
	nodes := make([]*API, len(acb.builders))
	mutators := make([]Mutator, len(acb.builders))
	for i := range acb.builders {
		func(i int, root context.Context) {
			builder := acb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*APIMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, acb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = acb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, acb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, acb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (acb *APICreateBulk) SaveX(ctx context.Context) []*API {
	v, err := acb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (acb *APICreateBulk) Exec(ctx context.Context) error {
	_, err := acb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (acb *APICreateBulk) ExecX(ctx context.Context) {
	if err := acb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.API.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.APIUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (acb *APICreateBulk) OnConflict(opts ...sql.ConflictOption) *APIUpsertBulk {
	acb.conflict = opts
	return &APIUpsertBulk{
		create: acb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.API.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (acb *APICreateBulk) OnConflictColumns(columns ...string) *APIUpsertBulk {
	acb.conflict = append(acb.conflict, sql.ConflictColumns(columns...))
	return &APIUpsertBulk{
		create: acb,
	}
}

// APIUpsertBulk is the builder for "upsert"-ing
// a bulk of API nodes.
type APIUpsertBulk struct {
	create *APICreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.API.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(api.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *APIUpsertBulk) UpdateNewValues() *APIUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(api.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(api.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.API.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *APIUpsertBulk) Ignore() *APIUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *APIUpsertBulk) DoNothing() *APIUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the APICreateBulk.OnConflict
// documentation for more info.
func (u *APIUpsertBulk) Update(set func(*APIUpsert)) *APIUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&APIUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *APIUpsertBulk) SetUpdatedAt(v time.Time) *APIUpsertBulk {
	return u.Update(func(s *APIUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *APIUpsertBulk) UpdateUpdatedAt() *APIUpsertBulk {
	return u.Update(func(s *APIUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetStatus sets the "status" field.
func (u *APIUpsertBulk) SetStatus(v bool) *APIUpsertBulk {
	return u.Update(func(s *APIUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *APIUpsertBulk) UpdateStatus() *APIUpsertBulk {
	return u.Update(func(s *APIUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *APIUpsertBulk) ClearStatus() *APIUpsertBulk {
	return u.Update(func(s *APIUpsert) {
		s.ClearStatus()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *APIUpsertBulk) SetDeletedAt(v time.Time) *APIUpsertBulk {
	return u.Update(func(s *APIUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *APIUpsertBulk) UpdateDeletedAt() *APIUpsertBulk {
	return u.Update(func(s *APIUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *APIUpsertBulk) ClearDeletedAt() *APIUpsertBulk {
	return u.Update(func(s *APIUpsert) {
		s.ClearDeletedAt()
	})
}

// SetPath sets the "path" field.
func (u *APIUpsertBulk) SetPath(v string) *APIUpsertBulk {
	return u.Update(func(s *APIUpsert) {
		s.SetPath(v)
	})
}

// UpdatePath sets the "path" field to the value that was provided on create.
func (u *APIUpsertBulk) UpdatePath() *APIUpsertBulk {
	return u.Update(func(s *APIUpsert) {
		s.UpdatePath()
	})
}

// SetDescription sets the "description" field.
func (u *APIUpsertBulk) SetDescription(v string) *APIUpsertBulk {
	return u.Update(func(s *APIUpsert) {
		s.SetDescription(v)
	})
}

// UpdateDescription sets the "description" field to the value that was provided on create.
func (u *APIUpsertBulk) UpdateDescription() *APIUpsertBulk {
	return u.Update(func(s *APIUpsert) {
		s.UpdateDescription()
	})
}

// SetAPIGroup sets the "api_group" field.
func (u *APIUpsertBulk) SetAPIGroup(v string) *APIUpsertBulk {
	return u.Update(func(s *APIUpsert) {
		s.SetAPIGroup(v)
	})
}

// UpdateAPIGroup sets the "api_group" field to the value that was provided on create.
func (u *APIUpsertBulk) UpdateAPIGroup() *APIUpsertBulk {
	return u.Update(func(s *APIUpsert) {
		s.UpdateAPIGroup()
	})
}

// SetMethod sets the "method" field.
func (u *APIUpsertBulk) SetMethod(v string) *APIUpsertBulk {
	return u.Update(func(s *APIUpsert) {
		s.SetMethod(v)
	})
}

// UpdateMethod sets the "method" field to the value that was provided on create.
func (u *APIUpsertBulk) UpdateMethod() *APIUpsertBulk {
	return u.Update(func(s *APIUpsert) {
		s.UpdateMethod()
	})
}

// SetKind sets the "kind" field.
func (u *APIUpsertBulk) SetKind(v string) *APIUpsertBulk {
	return u.Update(func(s *APIUpsert) {
		s.SetKind(v)
	})
}

// UpdateKind sets the "kind" field to the value that was provided on create.
func (u *APIUpsertBulk) UpdateKind() *APIUpsertBulk {
	return u.Update(func(s *APIUpsert) {
		s.UpdateKind()
	})
}

// SetModule sets the "module" field.
func (u *APIUpsertBulk) SetModule(v string) *APIUpsertBulk {
	return u.Update(func(s *APIUpsert) {
		s.SetModule(v)
	})
}

// UpdateModule sets the "module" field to the value that was provided on create.
func (u *APIUpsertBulk) UpdateModule() *APIUpsertBulk {
	return u.Update(func(s *APIUpsert) {
		s.UpdateModule()
	})
}

// Exec executes the query.
func (u *APIUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the APICreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for APICreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *APIUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
