// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"log"
	"reflect"

	"phoenix/service/saas/model/ent/migrate"

	"phoenix/service/saas/model/ent/api"
	"phoenix/service/saas/model/ent/application"
	"phoenix/service/saas/model/ent/button"
	"phoenix/service/saas/model/ent/file"
	"phoenix/service/saas/model/ent/group"
	"phoenix/service/saas/model/ent/grouptype"
	"phoenix/service/saas/model/ent/menu"
	"phoenix/service/saas/model/ent/organization"
	"phoenix/service/saas/model/ent/organizationuserinfo"
	"phoenix/service/saas/model/ent/position"
	"phoenix/service/saas/model/ent/role"
	"phoenix/service/saas/model/ent/tenant"
	"phoenix/service/saas/model/ent/tenantuserinfo"
	"phoenix/service/saas/model/ent/token"
	"phoenix/service/saas/model/ent/user"

	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// Client is the client that holds all ent builders.
type Client struct {
	config
	// Schema is the client for creating, migrating and dropping schema.
	Schema *migrate.Schema
	// API is the client for interacting with the API builders.
	API *APIClient
	// Application is the client for interacting with the Application builders.
	Application *ApplicationClient
	// Button is the client for interacting with the Button builders.
	Button *ButtonClient
	// File is the client for interacting with the File builders.
	File *FileClient
	// Group is the client for interacting with the Group builders.
	Group *GroupClient
	// GroupType is the client for interacting with the GroupType builders.
	GroupType *GroupTypeClient
	// Menu is the client for interacting with the Menu builders.
	Menu *MenuClient
	// Organization is the client for interacting with the Organization builders.
	Organization *OrganizationClient
	// OrganizationUserInfo is the client for interacting with the OrganizationUserInfo builders.
	OrganizationUserInfo *OrganizationUserInfoClient
	// Position is the client for interacting with the Position builders.
	Position *PositionClient
	// Role is the client for interacting with the Role builders.
	Role *RoleClient
	// Tenant is the client for interacting with the Tenant builders.
	Tenant *TenantClient
	// TenantUserInfo is the client for interacting with the TenantUserInfo builders.
	TenantUserInfo *TenantUserInfoClient
	// Token is the client for interacting with the Token builders.
	Token *TokenClient
	// User is the client for interacting with the User builders.
	User *UserClient
}

// NewClient creates a new client configured with the given options.
func NewClient(opts ...Option) *Client {
	client := &Client{config: newConfig(opts...)}
	client.init()
	return client
}

func (c *Client) init() {
	c.Schema = migrate.NewSchema(c.driver)
	c.API = NewAPIClient(c.config)
	c.Application = NewApplicationClient(c.config)
	c.Button = NewButtonClient(c.config)
	c.File = NewFileClient(c.config)
	c.Group = NewGroupClient(c.config)
	c.GroupType = NewGroupTypeClient(c.config)
	c.Menu = NewMenuClient(c.config)
	c.Organization = NewOrganizationClient(c.config)
	c.OrganizationUserInfo = NewOrganizationUserInfoClient(c.config)
	c.Position = NewPositionClient(c.config)
	c.Role = NewRoleClient(c.config)
	c.Tenant = NewTenantClient(c.config)
	c.TenantUserInfo = NewTenantUserInfoClient(c.config)
	c.Token = NewTokenClient(c.config)
	c.User = NewUserClient(c.config)
}

type (
	// config is the configuration for the client and its builder.
	config struct {
		// driver used for executing database requests.
		driver dialect.Driver
		// debug enable a debug logging.
		debug bool
		// log used for logging on debug mode.
		log func(...any)
		// hooks to execute on mutations.
		hooks *hooks
		// interceptors to execute on queries.
		inters *inters
	}
	// Option function to configure the client.
	Option func(*config)
)

// newConfig creates a new config for the client.
func newConfig(opts ...Option) config {
	cfg := config{log: log.Println, hooks: &hooks{}, inters: &inters{}}
	cfg.options(opts...)
	return cfg
}

// options applies the options on the config object.
func (c *config) options(opts ...Option) {
	for _, opt := range opts {
		opt(c)
	}
	if c.debug {
		c.driver = dialect.Debug(c.driver, c.log)
	}
}

// Debug enables debug logging on the ent.Driver.
func Debug() Option {
	return func(c *config) {
		c.debug = true
	}
}

// Log sets the logging function for debug mode.
func Log(fn func(...any)) Option {
	return func(c *config) {
		c.log = fn
	}
}

// Driver configures the client driver.
func Driver(driver dialect.Driver) Option {
	return func(c *config) {
		c.driver = driver
	}
}

// Open opens a database/sql.DB specified by the driver name and
// the data source name, and returns a new client attached to it.
// Optional parameters can be added for configuring the client.
func Open(driverName, dataSourceName string, options ...Option) (*Client, error) {
	switch driverName {
	case dialect.MySQL, dialect.Postgres, dialect.SQLite:
		drv, err := sql.Open(driverName, dataSourceName)
		if err != nil {
			return nil, err
		}
		return NewClient(append(options, Driver(drv))...), nil
	default:
		return nil, fmt.Errorf("unsupported driver: %q", driverName)
	}
}

// ErrTxStarted is returned when trying to start a new transaction from a transactional client.
var ErrTxStarted = errors.New("ent: cannot start a transaction within a transaction")

// Tx returns a new transactional client. The provided context
// is used until the transaction is committed or rolled back.
func (c *Client) Tx(ctx context.Context) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, ErrTxStarted
	}
	tx, err := newTx(ctx, c.driver)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = tx
	return &Tx{
		ctx:                  ctx,
		config:               cfg,
		API:                  NewAPIClient(cfg),
		Application:          NewApplicationClient(cfg),
		Button:               NewButtonClient(cfg),
		File:                 NewFileClient(cfg),
		Group:                NewGroupClient(cfg),
		GroupType:            NewGroupTypeClient(cfg),
		Menu:                 NewMenuClient(cfg),
		Organization:         NewOrganizationClient(cfg),
		OrganizationUserInfo: NewOrganizationUserInfoClient(cfg),
		Position:             NewPositionClient(cfg),
		Role:                 NewRoleClient(cfg),
		Tenant:               NewTenantClient(cfg),
		TenantUserInfo:       NewTenantUserInfoClient(cfg),
		Token:                NewTokenClient(cfg),
		User:                 NewUserClient(cfg),
	}, nil
}

// BeginTx returns a transactional client with specified options.
func (c *Client) BeginTx(ctx context.Context, opts *sql.TxOptions) (*Tx, error) {
	if _, ok := c.driver.(*txDriver); ok {
		return nil, errors.New("ent: cannot start a transaction within a transaction")
	}
	tx, err := c.driver.(interface {
		BeginTx(context.Context, *sql.TxOptions) (dialect.Tx, error)
	}).BeginTx(ctx, opts)
	if err != nil {
		return nil, fmt.Errorf("ent: starting a transaction: %w", err)
	}
	cfg := c.config
	cfg.driver = &txDriver{tx: tx, drv: c.driver}
	return &Tx{
		ctx:                  ctx,
		config:               cfg,
		API:                  NewAPIClient(cfg),
		Application:          NewApplicationClient(cfg),
		Button:               NewButtonClient(cfg),
		File:                 NewFileClient(cfg),
		Group:                NewGroupClient(cfg),
		GroupType:            NewGroupTypeClient(cfg),
		Menu:                 NewMenuClient(cfg),
		Organization:         NewOrganizationClient(cfg),
		OrganizationUserInfo: NewOrganizationUserInfoClient(cfg),
		Position:             NewPositionClient(cfg),
		Role:                 NewRoleClient(cfg),
		Tenant:               NewTenantClient(cfg),
		TenantUserInfo:       NewTenantUserInfoClient(cfg),
		Token:                NewTokenClient(cfg),
		User:                 NewUserClient(cfg),
	}, nil
}

// Debug returns a new debug-client. It's used to get verbose logging on specific operations.
//
//	client.Debug().
//		API.
//		Query().
//		Count(ctx)
func (c *Client) Debug() *Client {
	if c.debug {
		return c
	}
	cfg := c.config
	cfg.driver = dialect.Debug(c.driver, c.log)
	client := &Client{config: cfg}
	client.init()
	return client
}

// Close closes the database connection and prevents new queries from starting.
func (c *Client) Close() error {
	return c.driver.Close()
}

// Use adds the mutation hooks to all the entity clients.
// In order to add hooks to a specific client, call: `client.Node.Use(...)`.
func (c *Client) Use(hooks ...Hook) {
	for _, n := range []interface{ Use(...Hook) }{
		c.API, c.Application, c.Button, c.File, c.Group, c.GroupType, c.Menu,
		c.Organization, c.OrganizationUserInfo, c.Position, c.Role, c.Tenant,
		c.TenantUserInfo, c.Token, c.User,
	} {
		n.Use(hooks...)
	}
}

// Intercept adds the query interceptors to all the entity clients.
// In order to add interceptors to a specific client, call: `client.Node.Intercept(...)`.
func (c *Client) Intercept(interceptors ...Interceptor) {
	for _, n := range []interface{ Intercept(...Interceptor) }{
		c.API, c.Application, c.Button, c.File, c.Group, c.GroupType, c.Menu,
		c.Organization, c.OrganizationUserInfo, c.Position, c.Role, c.Tenant,
		c.TenantUserInfo, c.Token, c.User,
	} {
		n.Intercept(interceptors...)
	}
}

// Mutate implements the ent.Mutator interface.
func (c *Client) Mutate(ctx context.Context, m Mutation) (Value, error) {
	switch m := m.(type) {
	case *APIMutation:
		return c.API.mutate(ctx, m)
	case *ApplicationMutation:
		return c.Application.mutate(ctx, m)
	case *ButtonMutation:
		return c.Button.mutate(ctx, m)
	case *FileMutation:
		return c.File.mutate(ctx, m)
	case *GroupMutation:
		return c.Group.mutate(ctx, m)
	case *GroupTypeMutation:
		return c.GroupType.mutate(ctx, m)
	case *MenuMutation:
		return c.Menu.mutate(ctx, m)
	case *OrganizationMutation:
		return c.Organization.mutate(ctx, m)
	case *OrganizationUserInfoMutation:
		return c.OrganizationUserInfo.mutate(ctx, m)
	case *PositionMutation:
		return c.Position.mutate(ctx, m)
	case *RoleMutation:
		return c.Role.mutate(ctx, m)
	case *TenantMutation:
		return c.Tenant.mutate(ctx, m)
	case *TenantUserInfoMutation:
		return c.TenantUserInfo.mutate(ctx, m)
	case *TokenMutation:
		return c.Token.mutate(ctx, m)
	case *UserMutation:
		return c.User.mutate(ctx, m)
	default:
		return nil, fmt.Errorf("ent: unknown mutation type %T", m)
	}
}

// APIClient is a client for the API schema.
type APIClient struct {
	config
}

// NewAPIClient returns a client for the API from the given config.
func NewAPIClient(c config) *APIClient {
	return &APIClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `api.Hooks(f(g(h())))`.
func (c *APIClient) Use(hooks ...Hook) {
	c.hooks.API = append(c.hooks.API, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `api.Intercept(f(g(h())))`.
func (c *APIClient) Intercept(interceptors ...Interceptor) {
	c.inters.API = append(c.inters.API, interceptors...)
}

// Create returns a builder for creating a API entity.
func (c *APIClient) Create() *APICreate {
	mutation := newAPIMutation(c.config, OpCreate)
	return &APICreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of API entities.
func (c *APIClient) CreateBulk(builders ...*APICreate) *APICreateBulk {
	return &APICreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *APIClient) MapCreateBulk(slice any, setFunc func(*APICreate, int)) *APICreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &APICreateBulk{err: fmt.Errorf("calling to APIClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*APICreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &APICreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for API.
func (c *APIClient) Update() *APIUpdate {
	mutation := newAPIMutation(c.config, OpUpdate)
	return &APIUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *APIClient) UpdateOne(a *API) *APIUpdateOne {
	mutation := newAPIMutation(c.config, OpUpdateOne, withAPI(a))
	return &APIUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *APIClient) UpdateOneID(id string) *APIUpdateOne {
	mutation := newAPIMutation(c.config, OpUpdateOne, withAPIID(id))
	return &APIUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for API.
func (c *APIClient) Delete() *APIDelete {
	mutation := newAPIMutation(c.config, OpDelete)
	return &APIDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *APIClient) DeleteOne(a *API) *APIDeleteOne {
	return c.DeleteOneID(a.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *APIClient) DeleteOneID(id string) *APIDeleteOne {
	builder := c.Delete().Where(api.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &APIDeleteOne{builder}
}

// Query returns a query builder for API.
func (c *APIClient) Query() *APIQuery {
	return &APIQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeAPI},
		inters: c.Interceptors(),
	}
}

// Get returns a API entity by its id.
func (c *APIClient) Get(ctx context.Context, id string) (*API, error) {
	return c.Query().Where(api.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *APIClient) GetX(ctx context.Context, id string) *API {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryRoles queries the roles edge of a API.
func (c *APIClient) QueryRoles(a *API) *RoleQuery {
	query := (&RoleClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := a.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(api.Table, api.FieldID, id),
			sqlgraph.To(role.Table, role.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, api.RolesTable, api.RolesPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(a.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *APIClient) Hooks() []Hook {
	hooks := c.hooks.API
	return append(hooks[:len(hooks):len(hooks)], api.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *APIClient) Interceptors() []Interceptor {
	inters := c.inters.API
	return append(inters[:len(inters):len(inters)], api.Interceptors[:]...)
}

func (c *APIClient) mutate(ctx context.Context, m *APIMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&APICreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&APIUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&APIUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&APIDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown API mutation op: %q", m.Op())
	}
}

// ApplicationClient is a client for the Application schema.
type ApplicationClient struct {
	config
}

// NewApplicationClient returns a client for the Application from the given config.
func NewApplicationClient(c config) *ApplicationClient {
	return &ApplicationClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `application.Hooks(f(g(h())))`.
func (c *ApplicationClient) Use(hooks ...Hook) {
	c.hooks.Application = append(c.hooks.Application, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `application.Intercept(f(g(h())))`.
func (c *ApplicationClient) Intercept(interceptors ...Interceptor) {
	c.inters.Application = append(c.inters.Application, interceptors...)
}

// Create returns a builder for creating a Application entity.
func (c *ApplicationClient) Create() *ApplicationCreate {
	mutation := newApplicationMutation(c.config, OpCreate)
	return &ApplicationCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Application entities.
func (c *ApplicationClient) CreateBulk(builders ...*ApplicationCreate) *ApplicationCreateBulk {
	return &ApplicationCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *ApplicationClient) MapCreateBulk(slice any, setFunc func(*ApplicationCreate, int)) *ApplicationCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &ApplicationCreateBulk{err: fmt.Errorf("calling to ApplicationClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*ApplicationCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &ApplicationCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Application.
func (c *ApplicationClient) Update() *ApplicationUpdate {
	mutation := newApplicationMutation(c.config, OpUpdate)
	return &ApplicationUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *ApplicationClient) UpdateOne(a *Application) *ApplicationUpdateOne {
	mutation := newApplicationMutation(c.config, OpUpdateOne, withApplication(a))
	return &ApplicationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *ApplicationClient) UpdateOneID(id string) *ApplicationUpdateOne {
	mutation := newApplicationMutation(c.config, OpUpdateOne, withApplicationID(id))
	return &ApplicationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Application.
func (c *ApplicationClient) Delete() *ApplicationDelete {
	mutation := newApplicationMutation(c.config, OpDelete)
	return &ApplicationDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *ApplicationClient) DeleteOne(a *Application) *ApplicationDeleteOne {
	return c.DeleteOneID(a.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *ApplicationClient) DeleteOneID(id string) *ApplicationDeleteOne {
	builder := c.Delete().Where(application.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &ApplicationDeleteOne{builder}
}

// Query returns a query builder for Application.
func (c *ApplicationClient) Query() *ApplicationQuery {
	return &ApplicationQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeApplication},
		inters: c.Interceptors(),
	}
}

// Get returns a Application entity by its id.
func (c *ApplicationClient) Get(ctx context.Context, id string) (*Application, error) {
	return c.Query().Where(application.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *ApplicationClient) GetX(ctx context.Context, id string) *Application {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenant queries the tenant edge of a Application.
func (c *ApplicationClient) QueryTenant(a *Application) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := a.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(application.Table, application.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, application.TenantTable, application.TenantColumn),
		)
		fromV = sqlgraph.Neighbors(a.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *ApplicationClient) Hooks() []Hook {
	hooks := c.hooks.Application
	return append(hooks[:len(hooks):len(hooks)], application.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *ApplicationClient) Interceptors() []Interceptor {
	inters := c.inters.Application
	return append(inters[:len(inters):len(inters)], application.Interceptors[:]...)
}

func (c *ApplicationClient) mutate(ctx context.Context, m *ApplicationMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&ApplicationCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&ApplicationUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&ApplicationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&ApplicationDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Application mutation op: %q", m.Op())
	}
}

// ButtonClient is a client for the Button schema.
type ButtonClient struct {
	config
}

// NewButtonClient returns a client for the Button from the given config.
func NewButtonClient(c config) *ButtonClient {
	return &ButtonClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `button.Hooks(f(g(h())))`.
func (c *ButtonClient) Use(hooks ...Hook) {
	c.hooks.Button = append(c.hooks.Button, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `button.Intercept(f(g(h())))`.
func (c *ButtonClient) Intercept(interceptors ...Interceptor) {
	c.inters.Button = append(c.inters.Button, interceptors...)
}

// Create returns a builder for creating a Button entity.
func (c *ButtonClient) Create() *ButtonCreate {
	mutation := newButtonMutation(c.config, OpCreate)
	return &ButtonCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Button entities.
func (c *ButtonClient) CreateBulk(builders ...*ButtonCreate) *ButtonCreateBulk {
	return &ButtonCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *ButtonClient) MapCreateBulk(slice any, setFunc func(*ButtonCreate, int)) *ButtonCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &ButtonCreateBulk{err: fmt.Errorf("calling to ButtonClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*ButtonCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &ButtonCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Button.
func (c *ButtonClient) Update() *ButtonUpdate {
	mutation := newButtonMutation(c.config, OpUpdate)
	return &ButtonUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *ButtonClient) UpdateOne(b *Button) *ButtonUpdateOne {
	mutation := newButtonMutation(c.config, OpUpdateOne, withButton(b))
	return &ButtonUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *ButtonClient) UpdateOneID(id string) *ButtonUpdateOne {
	mutation := newButtonMutation(c.config, OpUpdateOne, withButtonID(id))
	return &ButtonUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Button.
func (c *ButtonClient) Delete() *ButtonDelete {
	mutation := newButtonMutation(c.config, OpDelete)
	return &ButtonDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *ButtonClient) DeleteOne(b *Button) *ButtonDeleteOne {
	return c.DeleteOneID(b.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *ButtonClient) DeleteOneID(id string) *ButtonDeleteOne {
	builder := c.Delete().Where(button.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &ButtonDeleteOne{builder}
}

// Query returns a query builder for Button.
func (c *ButtonClient) Query() *ButtonQuery {
	return &ButtonQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeButton},
		inters: c.Interceptors(),
	}
}

// Get returns a Button entity by its id.
func (c *ButtonClient) Get(ctx context.Context, id string) (*Button, error) {
	return c.Query().Where(button.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *ButtonClient) GetX(ctx context.Context, id string) *Button {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryRoles queries the roles edge of a Button.
func (c *ButtonClient) QueryRoles(b *Button) *RoleQuery {
	query := (&RoleClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := b.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(button.Table, button.FieldID, id),
			sqlgraph.To(role.Table, role.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, button.RolesTable, button.RolesPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(b.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryMenu queries the menu edge of a Button.
func (c *ButtonClient) QueryMenu(b *Button) *MenuQuery {
	query := (&MenuClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := b.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(button.Table, button.FieldID, id),
			sqlgraph.To(menu.Table, menu.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, button.MenuTable, button.MenuColumn),
		)
		fromV = sqlgraph.Neighbors(b.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *ButtonClient) Hooks() []Hook {
	hooks := c.hooks.Button
	return append(hooks[:len(hooks):len(hooks)], button.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *ButtonClient) Interceptors() []Interceptor {
	inters := c.inters.Button
	return append(inters[:len(inters):len(inters)], button.Interceptors[:]...)
}

func (c *ButtonClient) mutate(ctx context.Context, m *ButtonMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&ButtonCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&ButtonUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&ButtonUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&ButtonDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Button mutation op: %q", m.Op())
	}
}

// FileClient is a client for the File schema.
type FileClient struct {
	config
}

// NewFileClient returns a client for the File from the given config.
func NewFileClient(c config) *FileClient {
	return &FileClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `file.Hooks(f(g(h())))`.
func (c *FileClient) Use(hooks ...Hook) {
	c.hooks.File = append(c.hooks.File, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `file.Intercept(f(g(h())))`.
func (c *FileClient) Intercept(interceptors ...Interceptor) {
	c.inters.File = append(c.inters.File, interceptors...)
}

// Create returns a builder for creating a File entity.
func (c *FileClient) Create() *FileCreate {
	mutation := newFileMutation(c.config, OpCreate)
	return &FileCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of File entities.
func (c *FileClient) CreateBulk(builders ...*FileCreate) *FileCreateBulk {
	return &FileCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *FileClient) MapCreateBulk(slice any, setFunc func(*FileCreate, int)) *FileCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &FileCreateBulk{err: fmt.Errorf("calling to FileClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*FileCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &FileCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for File.
func (c *FileClient) Update() *FileUpdate {
	mutation := newFileMutation(c.config, OpUpdate)
	return &FileUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *FileClient) UpdateOne(f *File) *FileUpdateOne {
	mutation := newFileMutation(c.config, OpUpdateOne, withFile(f))
	return &FileUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *FileClient) UpdateOneID(id string) *FileUpdateOne {
	mutation := newFileMutation(c.config, OpUpdateOne, withFileID(id))
	return &FileUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for File.
func (c *FileClient) Delete() *FileDelete {
	mutation := newFileMutation(c.config, OpDelete)
	return &FileDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *FileClient) DeleteOne(f *File) *FileDeleteOne {
	return c.DeleteOneID(f.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *FileClient) DeleteOneID(id string) *FileDeleteOne {
	builder := c.Delete().Where(file.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &FileDeleteOne{builder}
}

// Query returns a query builder for File.
func (c *FileClient) Query() *FileQuery {
	return &FileQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeFile},
		inters: c.Interceptors(),
	}
}

// Get returns a File entity by its id.
func (c *FileClient) Get(ctx context.Context, id string) (*File, error) {
	return c.Query().Where(file.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *FileClient) GetX(ctx context.Context, id string) *File {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenant queries the tenant edge of a File.
func (c *FileClient) QueryTenant(f *File) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := f.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(file.Table, file.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, file.TenantTable, file.TenantColumn),
		)
		fromV = sqlgraph.Neighbors(f.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUser queries the user edge of a File.
func (c *FileClient) QueryUser(f *File) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := f.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(file.Table, file.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, file.UserTable, file.UserColumn),
		)
		fromV = sqlgraph.Neighbors(f.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAvatarUsers queries the avatar_users edge of a File.
func (c *FileClient) QueryAvatarUsers(f *File) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := f.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(file.Table, file.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, file.AvatarUsersTable, file.AvatarUsersColumn),
		)
		fromV = sqlgraph.Neighbors(f.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *FileClient) Hooks() []Hook {
	hooks := c.hooks.File
	return append(hooks[:len(hooks):len(hooks)], file.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *FileClient) Interceptors() []Interceptor {
	inters := c.inters.File
	return append(inters[:len(inters):len(inters)], file.Interceptors[:]...)
}

func (c *FileClient) mutate(ctx context.Context, m *FileMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&FileCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&FileUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&FileUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&FileDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown File mutation op: %q", m.Op())
	}
}

// GroupClient is a client for the Group schema.
type GroupClient struct {
	config
}

// NewGroupClient returns a client for the Group from the given config.
func NewGroupClient(c config) *GroupClient {
	return &GroupClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `group.Hooks(f(g(h())))`.
func (c *GroupClient) Use(hooks ...Hook) {
	c.hooks.Group = append(c.hooks.Group, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `group.Intercept(f(g(h())))`.
func (c *GroupClient) Intercept(interceptors ...Interceptor) {
	c.inters.Group = append(c.inters.Group, interceptors...)
}

// Create returns a builder for creating a Group entity.
func (c *GroupClient) Create() *GroupCreate {
	mutation := newGroupMutation(c.config, OpCreate)
	return &GroupCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Group entities.
func (c *GroupClient) CreateBulk(builders ...*GroupCreate) *GroupCreateBulk {
	return &GroupCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *GroupClient) MapCreateBulk(slice any, setFunc func(*GroupCreate, int)) *GroupCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &GroupCreateBulk{err: fmt.Errorf("calling to GroupClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*GroupCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &GroupCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Group.
func (c *GroupClient) Update() *GroupUpdate {
	mutation := newGroupMutation(c.config, OpUpdate)
	return &GroupUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *GroupClient) UpdateOne(gr *Group) *GroupUpdateOne {
	mutation := newGroupMutation(c.config, OpUpdateOne, withGroup(gr))
	return &GroupUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *GroupClient) UpdateOneID(id string) *GroupUpdateOne {
	mutation := newGroupMutation(c.config, OpUpdateOne, withGroupID(id))
	return &GroupUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Group.
func (c *GroupClient) Delete() *GroupDelete {
	mutation := newGroupMutation(c.config, OpDelete)
	return &GroupDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *GroupClient) DeleteOne(gr *Group) *GroupDeleteOne {
	return c.DeleteOneID(gr.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *GroupClient) DeleteOneID(id string) *GroupDeleteOne {
	builder := c.Delete().Where(group.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &GroupDeleteOne{builder}
}

// Query returns a query builder for Group.
func (c *GroupClient) Query() *GroupQuery {
	return &GroupQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeGroup},
		inters: c.Interceptors(),
	}
}

// Get returns a Group entity by its id.
func (c *GroupClient) Get(ctx context.Context, id string) (*Group, error) {
	return c.Query().Where(group.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *GroupClient) GetX(ctx context.Context, id string) *Group {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenant queries the tenant edge of a Group.
func (c *GroupClient) QueryTenant(gr *Group) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := gr.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(group.Table, group.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, group.TenantTable, group.TenantColumn),
		)
		fromV = sqlgraph.Neighbors(gr.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUsers queries the users edge of a Group.
func (c *GroupClient) QueryUsers(gr *Group) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := gr.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(group.Table, group.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, group.UsersTable, group.UsersPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(gr.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryRoles queries the roles edge of a Group.
func (c *GroupClient) QueryRoles(gr *Group) *RoleQuery {
	query := (&RoleClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := gr.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(group.Table, group.FieldID, id),
			sqlgraph.To(role.Table, role.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, group.RolesTable, group.RolesPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(gr.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryGroupType queries the group_type edge of a Group.
func (c *GroupClient) QueryGroupType(gr *Group) *GroupTypeQuery {
	query := (&GroupTypeClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := gr.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(group.Table, group.FieldID, id),
			sqlgraph.To(grouptype.Table, grouptype.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, group.GroupTypeTable, group.GroupTypeColumn),
		)
		fromV = sqlgraph.Neighbors(gr.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *GroupClient) Hooks() []Hook {
	hooks := c.hooks.Group
	return append(hooks[:len(hooks):len(hooks)], group.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *GroupClient) Interceptors() []Interceptor {
	inters := c.inters.Group
	return append(inters[:len(inters):len(inters)], group.Interceptors[:]...)
}

func (c *GroupClient) mutate(ctx context.Context, m *GroupMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&GroupCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&GroupUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&GroupUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&GroupDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Group mutation op: %q", m.Op())
	}
}

// GroupTypeClient is a client for the GroupType schema.
type GroupTypeClient struct {
	config
}

// NewGroupTypeClient returns a client for the GroupType from the given config.
func NewGroupTypeClient(c config) *GroupTypeClient {
	return &GroupTypeClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `grouptype.Hooks(f(g(h())))`.
func (c *GroupTypeClient) Use(hooks ...Hook) {
	c.hooks.GroupType = append(c.hooks.GroupType, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `grouptype.Intercept(f(g(h())))`.
func (c *GroupTypeClient) Intercept(interceptors ...Interceptor) {
	c.inters.GroupType = append(c.inters.GroupType, interceptors...)
}

// Create returns a builder for creating a GroupType entity.
func (c *GroupTypeClient) Create() *GroupTypeCreate {
	mutation := newGroupTypeMutation(c.config, OpCreate)
	return &GroupTypeCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of GroupType entities.
func (c *GroupTypeClient) CreateBulk(builders ...*GroupTypeCreate) *GroupTypeCreateBulk {
	return &GroupTypeCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *GroupTypeClient) MapCreateBulk(slice any, setFunc func(*GroupTypeCreate, int)) *GroupTypeCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &GroupTypeCreateBulk{err: fmt.Errorf("calling to GroupTypeClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*GroupTypeCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &GroupTypeCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for GroupType.
func (c *GroupTypeClient) Update() *GroupTypeUpdate {
	mutation := newGroupTypeMutation(c.config, OpUpdate)
	return &GroupTypeUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *GroupTypeClient) UpdateOne(gt *GroupType) *GroupTypeUpdateOne {
	mutation := newGroupTypeMutation(c.config, OpUpdateOne, withGroupType(gt))
	return &GroupTypeUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *GroupTypeClient) UpdateOneID(id string) *GroupTypeUpdateOne {
	mutation := newGroupTypeMutation(c.config, OpUpdateOne, withGroupTypeID(id))
	return &GroupTypeUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for GroupType.
func (c *GroupTypeClient) Delete() *GroupTypeDelete {
	mutation := newGroupTypeMutation(c.config, OpDelete)
	return &GroupTypeDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *GroupTypeClient) DeleteOne(gt *GroupType) *GroupTypeDeleteOne {
	return c.DeleteOneID(gt.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *GroupTypeClient) DeleteOneID(id string) *GroupTypeDeleteOne {
	builder := c.Delete().Where(grouptype.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &GroupTypeDeleteOne{builder}
}

// Query returns a query builder for GroupType.
func (c *GroupTypeClient) Query() *GroupTypeQuery {
	return &GroupTypeQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeGroupType},
		inters: c.Interceptors(),
	}
}

// Get returns a GroupType entity by its id.
func (c *GroupTypeClient) Get(ctx context.Context, id string) (*GroupType, error) {
	return c.Query().Where(grouptype.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *GroupTypeClient) GetX(ctx context.Context, id string) *GroupType {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenant queries the tenant edge of a GroupType.
func (c *GroupTypeClient) QueryTenant(gt *GroupType) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := gt.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(grouptype.Table, grouptype.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, grouptype.TenantTable, grouptype.TenantColumn),
		)
		fromV = sqlgraph.Neighbors(gt.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryGroups queries the groups edge of a GroupType.
func (c *GroupTypeClient) QueryGroups(gt *GroupType) *GroupQuery {
	query := (&GroupClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := gt.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(grouptype.Table, grouptype.FieldID, id),
			sqlgraph.To(group.Table, group.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, grouptype.GroupsTable, grouptype.GroupsColumn),
		)
		fromV = sqlgraph.Neighbors(gt.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *GroupTypeClient) Hooks() []Hook {
	hooks := c.hooks.GroupType
	return append(hooks[:len(hooks):len(hooks)], grouptype.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *GroupTypeClient) Interceptors() []Interceptor {
	inters := c.inters.GroupType
	return append(inters[:len(inters):len(inters)], grouptype.Interceptors[:]...)
}

func (c *GroupTypeClient) mutate(ctx context.Context, m *GroupTypeMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&GroupTypeCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&GroupTypeUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&GroupTypeUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&GroupTypeDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown GroupType mutation op: %q", m.Op())
	}
}

// MenuClient is a client for the Menu schema.
type MenuClient struct {
	config
}

// NewMenuClient returns a client for the Menu from the given config.
func NewMenuClient(c config) *MenuClient {
	return &MenuClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `menu.Hooks(f(g(h())))`.
func (c *MenuClient) Use(hooks ...Hook) {
	c.hooks.Menu = append(c.hooks.Menu, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `menu.Intercept(f(g(h())))`.
func (c *MenuClient) Intercept(interceptors ...Interceptor) {
	c.inters.Menu = append(c.inters.Menu, interceptors...)
}

// Create returns a builder for creating a Menu entity.
func (c *MenuClient) Create() *MenuCreate {
	mutation := newMenuMutation(c.config, OpCreate)
	return &MenuCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Menu entities.
func (c *MenuClient) CreateBulk(builders ...*MenuCreate) *MenuCreateBulk {
	return &MenuCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *MenuClient) MapCreateBulk(slice any, setFunc func(*MenuCreate, int)) *MenuCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &MenuCreateBulk{err: fmt.Errorf("calling to MenuClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*MenuCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &MenuCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Menu.
func (c *MenuClient) Update() *MenuUpdate {
	mutation := newMenuMutation(c.config, OpUpdate)
	return &MenuUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *MenuClient) UpdateOne(m *Menu) *MenuUpdateOne {
	mutation := newMenuMutation(c.config, OpUpdateOne, withMenu(m))
	return &MenuUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *MenuClient) UpdateOneID(id string) *MenuUpdateOne {
	mutation := newMenuMutation(c.config, OpUpdateOne, withMenuID(id))
	return &MenuUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Menu.
func (c *MenuClient) Delete() *MenuDelete {
	mutation := newMenuMutation(c.config, OpDelete)
	return &MenuDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *MenuClient) DeleteOne(m *Menu) *MenuDeleteOne {
	return c.DeleteOneID(m.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *MenuClient) DeleteOneID(id string) *MenuDeleteOne {
	builder := c.Delete().Where(menu.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &MenuDeleteOne{builder}
}

// Query returns a query builder for Menu.
func (c *MenuClient) Query() *MenuQuery {
	return &MenuQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeMenu},
		inters: c.Interceptors(),
	}
}

// Get returns a Menu entity by its id.
func (c *MenuClient) Get(ctx context.Context, id string) (*Menu, error) {
	return c.Query().Where(menu.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *MenuClient) GetX(ctx context.Context, id string) *Menu {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryRoles queries the roles edge of a Menu.
func (c *MenuClient) QueryRoles(m *Menu) *RoleQuery {
	query := (&RoleClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := m.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(menu.Table, menu.FieldID, id),
			sqlgraph.To(role.Table, role.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, menu.RolesTable, menu.RolesPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(m.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryButtons queries the buttons edge of a Menu.
func (c *MenuClient) QueryButtons(m *Menu) *ButtonQuery {
	query := (&ButtonClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := m.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(menu.Table, menu.FieldID, id),
			sqlgraph.To(button.Table, button.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, menu.ButtonsTable, menu.ButtonsColumn),
		)
		fromV = sqlgraph.Neighbors(m.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryParent queries the parent edge of a Menu.
func (c *MenuClient) QueryParent(m *Menu) *MenuQuery {
	query := (&MenuClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := m.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(menu.Table, menu.FieldID, id),
			sqlgraph.To(menu.Table, menu.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, menu.ParentTable, menu.ParentColumn),
		)
		fromV = sqlgraph.Neighbors(m.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryChildren queries the children edge of a Menu.
func (c *MenuClient) QueryChildren(m *Menu) *MenuQuery {
	query := (&MenuClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := m.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(menu.Table, menu.FieldID, id),
			sqlgraph.To(menu.Table, menu.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, menu.ChildrenTable, menu.ChildrenColumn),
		)
		fromV = sqlgraph.Neighbors(m.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *MenuClient) Hooks() []Hook {
	hooks := c.hooks.Menu
	return append(hooks[:len(hooks):len(hooks)], menu.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *MenuClient) Interceptors() []Interceptor {
	inters := c.inters.Menu
	return append(inters[:len(inters):len(inters)], menu.Interceptors[:]...)
}

func (c *MenuClient) mutate(ctx context.Context, m *MenuMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&MenuCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&MenuUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&MenuUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&MenuDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Menu mutation op: %q", m.Op())
	}
}

// OrganizationClient is a client for the Organization schema.
type OrganizationClient struct {
	config
}

// NewOrganizationClient returns a client for the Organization from the given config.
func NewOrganizationClient(c config) *OrganizationClient {
	return &OrganizationClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `organization.Hooks(f(g(h())))`.
func (c *OrganizationClient) Use(hooks ...Hook) {
	c.hooks.Organization = append(c.hooks.Organization, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `organization.Intercept(f(g(h())))`.
func (c *OrganizationClient) Intercept(interceptors ...Interceptor) {
	c.inters.Organization = append(c.inters.Organization, interceptors...)
}

// Create returns a builder for creating a Organization entity.
func (c *OrganizationClient) Create() *OrganizationCreate {
	mutation := newOrganizationMutation(c.config, OpCreate)
	return &OrganizationCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Organization entities.
func (c *OrganizationClient) CreateBulk(builders ...*OrganizationCreate) *OrganizationCreateBulk {
	return &OrganizationCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *OrganizationClient) MapCreateBulk(slice any, setFunc func(*OrganizationCreate, int)) *OrganizationCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &OrganizationCreateBulk{err: fmt.Errorf("calling to OrganizationClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*OrganizationCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &OrganizationCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Organization.
func (c *OrganizationClient) Update() *OrganizationUpdate {
	mutation := newOrganizationMutation(c.config, OpUpdate)
	return &OrganizationUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *OrganizationClient) UpdateOne(o *Organization) *OrganizationUpdateOne {
	mutation := newOrganizationMutation(c.config, OpUpdateOne, withOrganization(o))
	return &OrganizationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *OrganizationClient) UpdateOneID(id string) *OrganizationUpdateOne {
	mutation := newOrganizationMutation(c.config, OpUpdateOne, withOrganizationID(id))
	return &OrganizationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Organization.
func (c *OrganizationClient) Delete() *OrganizationDelete {
	mutation := newOrganizationMutation(c.config, OpDelete)
	return &OrganizationDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *OrganizationClient) DeleteOne(o *Organization) *OrganizationDeleteOne {
	return c.DeleteOneID(o.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *OrganizationClient) DeleteOneID(id string) *OrganizationDeleteOne {
	builder := c.Delete().Where(organization.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &OrganizationDeleteOne{builder}
}

// Query returns a query builder for Organization.
func (c *OrganizationClient) Query() *OrganizationQuery {
	return &OrganizationQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeOrganization},
		inters: c.Interceptors(),
	}
}

// Get returns a Organization entity by its id.
func (c *OrganizationClient) Get(ctx context.Context, id string) (*Organization, error) {
	return c.Query().Where(organization.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *OrganizationClient) GetX(ctx context.Context, id string) *Organization {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenant queries the tenant edge of a Organization.
func (c *OrganizationClient) QueryTenant(o *Organization) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := o.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(organization.Table, organization.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, organization.TenantTable, organization.TenantColumn),
		)
		fromV = sqlgraph.Neighbors(o.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryParent queries the parent edge of a Organization.
func (c *OrganizationClient) QueryParent(o *Organization) *OrganizationQuery {
	query := (&OrganizationClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := o.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(organization.Table, organization.FieldID, id),
			sqlgraph.To(organization.Table, organization.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, organization.ParentTable, organization.ParentColumn),
		)
		fromV = sqlgraph.Neighbors(o.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryChildren queries the children edge of a Organization.
func (c *OrganizationClient) QueryChildren(o *Organization) *OrganizationQuery {
	query := (&OrganizationClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := o.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(organization.Table, organization.FieldID, id),
			sqlgraph.To(organization.Table, organization.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, organization.ChildrenTable, organization.ChildrenColumn),
		)
		fromV = sqlgraph.Neighbors(o.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUsers queries the users edge of a Organization.
func (c *OrganizationClient) QueryUsers(o *Organization) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := o.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(organization.Table, organization.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, organization.UsersTable, organization.UsersPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(o.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryOrganizationInfos queries the organization_infos edge of a Organization.
func (c *OrganizationClient) QueryOrganizationInfos(o *Organization) *OrganizationUserInfoQuery {
	query := (&OrganizationUserInfoClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := o.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(organization.Table, organization.FieldID, id),
			sqlgraph.To(organizationuserinfo.Table, organizationuserinfo.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, organization.OrganizationInfosTable, organization.OrganizationInfosColumn),
		)
		fromV = sqlgraph.Neighbors(o.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *OrganizationClient) Hooks() []Hook {
	hooks := c.hooks.Organization
	return append(hooks[:len(hooks):len(hooks)], organization.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *OrganizationClient) Interceptors() []Interceptor {
	inters := c.inters.Organization
	return append(inters[:len(inters):len(inters)], organization.Interceptors[:]...)
}

func (c *OrganizationClient) mutate(ctx context.Context, m *OrganizationMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&OrganizationCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&OrganizationUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&OrganizationUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&OrganizationDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Organization mutation op: %q", m.Op())
	}
}

// OrganizationUserInfoClient is a client for the OrganizationUserInfo schema.
type OrganizationUserInfoClient struct {
	config
}

// NewOrganizationUserInfoClient returns a client for the OrganizationUserInfo from the given config.
func NewOrganizationUserInfoClient(c config) *OrganizationUserInfoClient {
	return &OrganizationUserInfoClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `organizationuserinfo.Hooks(f(g(h())))`.
func (c *OrganizationUserInfoClient) Use(hooks ...Hook) {
	c.hooks.OrganizationUserInfo = append(c.hooks.OrganizationUserInfo, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `organizationuserinfo.Intercept(f(g(h())))`.
func (c *OrganizationUserInfoClient) Intercept(interceptors ...Interceptor) {
	c.inters.OrganizationUserInfo = append(c.inters.OrganizationUserInfo, interceptors...)
}

// Create returns a builder for creating a OrganizationUserInfo entity.
func (c *OrganizationUserInfoClient) Create() *OrganizationUserInfoCreate {
	mutation := newOrganizationUserInfoMutation(c.config, OpCreate)
	return &OrganizationUserInfoCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of OrganizationUserInfo entities.
func (c *OrganizationUserInfoClient) CreateBulk(builders ...*OrganizationUserInfoCreate) *OrganizationUserInfoCreateBulk {
	return &OrganizationUserInfoCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *OrganizationUserInfoClient) MapCreateBulk(slice any, setFunc func(*OrganizationUserInfoCreate, int)) *OrganizationUserInfoCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &OrganizationUserInfoCreateBulk{err: fmt.Errorf("calling to OrganizationUserInfoClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*OrganizationUserInfoCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &OrganizationUserInfoCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for OrganizationUserInfo.
func (c *OrganizationUserInfoClient) Update() *OrganizationUserInfoUpdate {
	mutation := newOrganizationUserInfoMutation(c.config, OpUpdate)
	return &OrganizationUserInfoUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *OrganizationUserInfoClient) UpdateOne(oui *OrganizationUserInfo) *OrganizationUserInfoUpdateOne {
	mutation := newOrganizationUserInfoMutation(c.config, OpUpdateOne, withOrganizationUserInfo(oui))
	return &OrganizationUserInfoUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *OrganizationUserInfoClient) UpdateOneID(id string) *OrganizationUserInfoUpdateOne {
	mutation := newOrganizationUserInfoMutation(c.config, OpUpdateOne, withOrganizationUserInfoID(id))
	return &OrganizationUserInfoUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for OrganizationUserInfo.
func (c *OrganizationUserInfoClient) Delete() *OrganizationUserInfoDelete {
	mutation := newOrganizationUserInfoMutation(c.config, OpDelete)
	return &OrganizationUserInfoDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *OrganizationUserInfoClient) DeleteOne(oui *OrganizationUserInfo) *OrganizationUserInfoDeleteOne {
	return c.DeleteOneID(oui.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *OrganizationUserInfoClient) DeleteOneID(id string) *OrganizationUserInfoDeleteOne {
	builder := c.Delete().Where(organizationuserinfo.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &OrganizationUserInfoDeleteOne{builder}
}

// Query returns a query builder for OrganizationUserInfo.
func (c *OrganizationUserInfoClient) Query() *OrganizationUserInfoQuery {
	return &OrganizationUserInfoQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeOrganizationUserInfo},
		inters: c.Interceptors(),
	}
}

// Get returns a OrganizationUserInfo entity by its id.
func (c *OrganizationUserInfoClient) Get(ctx context.Context, id string) (*OrganizationUserInfo, error) {
	return c.Query().Where(organizationuserinfo.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *OrganizationUserInfoClient) GetX(ctx context.Context, id string) *OrganizationUserInfo {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryOrganization queries the organization edge of a OrganizationUserInfo.
func (c *OrganizationUserInfoClient) QueryOrganization(oui *OrganizationUserInfo) *OrganizationQuery {
	query := (&OrganizationClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := oui.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(organizationuserinfo.Table, organizationuserinfo.FieldID, id),
			sqlgraph.To(organization.Table, organization.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, organizationuserinfo.OrganizationTable, organizationuserinfo.OrganizationColumn),
		)
		fromV = sqlgraph.Neighbors(oui.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUser queries the user edge of a OrganizationUserInfo.
func (c *OrganizationUserInfoClient) QueryUser(oui *OrganizationUserInfo) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := oui.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(organizationuserinfo.Table, organizationuserinfo.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, organizationuserinfo.UserTable, organizationuserinfo.UserColumn),
		)
		fromV = sqlgraph.Neighbors(oui.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *OrganizationUserInfoClient) Hooks() []Hook {
	hooks := c.hooks.OrganizationUserInfo
	return append(hooks[:len(hooks):len(hooks)], organizationuserinfo.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *OrganizationUserInfoClient) Interceptors() []Interceptor {
	inters := c.inters.OrganizationUserInfo
	return append(inters[:len(inters):len(inters)], organizationuserinfo.Interceptors[:]...)
}

func (c *OrganizationUserInfoClient) mutate(ctx context.Context, m *OrganizationUserInfoMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&OrganizationUserInfoCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&OrganizationUserInfoUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&OrganizationUserInfoUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&OrganizationUserInfoDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown OrganizationUserInfo mutation op: %q", m.Op())
	}
}

// PositionClient is a client for the Position schema.
type PositionClient struct {
	config
}

// NewPositionClient returns a client for the Position from the given config.
func NewPositionClient(c config) *PositionClient {
	return &PositionClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `position.Hooks(f(g(h())))`.
func (c *PositionClient) Use(hooks ...Hook) {
	c.hooks.Position = append(c.hooks.Position, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `position.Intercept(f(g(h())))`.
func (c *PositionClient) Intercept(interceptors ...Interceptor) {
	c.inters.Position = append(c.inters.Position, interceptors...)
}

// Create returns a builder for creating a Position entity.
func (c *PositionClient) Create() *PositionCreate {
	mutation := newPositionMutation(c.config, OpCreate)
	return &PositionCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Position entities.
func (c *PositionClient) CreateBulk(builders ...*PositionCreate) *PositionCreateBulk {
	return &PositionCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *PositionClient) MapCreateBulk(slice any, setFunc func(*PositionCreate, int)) *PositionCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &PositionCreateBulk{err: fmt.Errorf("calling to PositionClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*PositionCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &PositionCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Position.
func (c *PositionClient) Update() *PositionUpdate {
	mutation := newPositionMutation(c.config, OpUpdate)
	return &PositionUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *PositionClient) UpdateOne(po *Position) *PositionUpdateOne {
	mutation := newPositionMutation(c.config, OpUpdateOne, withPosition(po))
	return &PositionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *PositionClient) UpdateOneID(id string) *PositionUpdateOne {
	mutation := newPositionMutation(c.config, OpUpdateOne, withPositionID(id))
	return &PositionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Position.
func (c *PositionClient) Delete() *PositionDelete {
	mutation := newPositionMutation(c.config, OpDelete)
	return &PositionDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *PositionClient) DeleteOne(po *Position) *PositionDeleteOne {
	return c.DeleteOneID(po.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *PositionClient) DeleteOneID(id string) *PositionDeleteOne {
	builder := c.Delete().Where(position.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &PositionDeleteOne{builder}
}

// Query returns a query builder for Position.
func (c *PositionClient) Query() *PositionQuery {
	return &PositionQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypePosition},
		inters: c.Interceptors(),
	}
}

// Get returns a Position entity by its id.
func (c *PositionClient) Get(ctx context.Context, id string) (*Position, error) {
	return c.Query().Where(position.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *PositionClient) GetX(ctx context.Context, id string) *Position {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenant queries the tenant edge of a Position.
func (c *PositionClient) QueryTenant(po *Position) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := po.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(position.Table, position.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, position.TenantTable, position.TenantColumn),
		)
		fromV = sqlgraph.Neighbors(po.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUsers queries the users edge of a Position.
func (c *PositionClient) QueryUsers(po *Position) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := po.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(position.Table, position.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, position.UsersTable, position.UsersPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(po.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *PositionClient) Hooks() []Hook {
	hooks := c.hooks.Position
	return append(hooks[:len(hooks):len(hooks)], position.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *PositionClient) Interceptors() []Interceptor {
	inters := c.inters.Position
	return append(inters[:len(inters):len(inters)], position.Interceptors[:]...)
}

func (c *PositionClient) mutate(ctx context.Context, m *PositionMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&PositionCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&PositionUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&PositionUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&PositionDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Position mutation op: %q", m.Op())
	}
}

// RoleClient is a client for the Role schema.
type RoleClient struct {
	config
}

// NewRoleClient returns a client for the Role from the given config.
func NewRoleClient(c config) *RoleClient {
	return &RoleClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `role.Hooks(f(g(h())))`.
func (c *RoleClient) Use(hooks ...Hook) {
	c.hooks.Role = append(c.hooks.Role, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `role.Intercept(f(g(h())))`.
func (c *RoleClient) Intercept(interceptors ...Interceptor) {
	c.inters.Role = append(c.inters.Role, interceptors...)
}

// Create returns a builder for creating a Role entity.
func (c *RoleClient) Create() *RoleCreate {
	mutation := newRoleMutation(c.config, OpCreate)
	return &RoleCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Role entities.
func (c *RoleClient) CreateBulk(builders ...*RoleCreate) *RoleCreateBulk {
	return &RoleCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *RoleClient) MapCreateBulk(slice any, setFunc func(*RoleCreate, int)) *RoleCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &RoleCreateBulk{err: fmt.Errorf("calling to RoleClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*RoleCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &RoleCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Role.
func (c *RoleClient) Update() *RoleUpdate {
	mutation := newRoleMutation(c.config, OpUpdate)
	return &RoleUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *RoleClient) UpdateOne(r *Role) *RoleUpdateOne {
	mutation := newRoleMutation(c.config, OpUpdateOne, withRole(r))
	return &RoleUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *RoleClient) UpdateOneID(id string) *RoleUpdateOne {
	mutation := newRoleMutation(c.config, OpUpdateOne, withRoleID(id))
	return &RoleUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Role.
func (c *RoleClient) Delete() *RoleDelete {
	mutation := newRoleMutation(c.config, OpDelete)
	return &RoleDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *RoleClient) DeleteOne(r *Role) *RoleDeleteOne {
	return c.DeleteOneID(r.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *RoleClient) DeleteOneID(id string) *RoleDeleteOne {
	builder := c.Delete().Where(role.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &RoleDeleteOne{builder}
}

// Query returns a query builder for Role.
func (c *RoleClient) Query() *RoleQuery {
	return &RoleQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeRole},
		inters: c.Interceptors(),
	}
}

// Get returns a Role entity by its id.
func (c *RoleClient) Get(ctx context.Context, id string) (*Role, error) {
	return c.Query().Where(role.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *RoleClient) GetX(ctx context.Context, id string) *Role {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenant queries the tenant edge of a Role.
func (c *RoleClient) QueryTenant(r *Role) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := r.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(role.Table, role.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, role.TenantTable, role.TenantColumn),
		)
		fromV = sqlgraph.Neighbors(r.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryButtons queries the buttons edge of a Role.
func (c *RoleClient) QueryButtons(r *Role) *ButtonQuery {
	query := (&ButtonClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := r.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(role.Table, role.FieldID, id),
			sqlgraph.To(button.Table, button.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, role.ButtonsTable, role.ButtonsPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(r.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryMenus queries the menus edge of a Role.
func (c *RoleClient) QueryMenus(r *Role) *MenuQuery {
	query := (&MenuClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := r.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(role.Table, role.FieldID, id),
			sqlgraph.To(menu.Table, menu.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, role.MenusTable, role.MenusPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(r.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryApis queries the apis edge of a Role.
func (c *RoleClient) QueryApis(r *Role) *APIQuery {
	query := (&APIClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := r.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(role.Table, role.FieldID, id),
			sqlgraph.To(api.Table, api.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, role.ApisTable, role.ApisPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(r.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryGroups queries the groups edge of a Role.
func (c *RoleClient) QueryGroups(r *Role) *GroupQuery {
	query := (&GroupClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := r.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(role.Table, role.FieldID, id),
			sqlgraph.To(group.Table, group.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, role.GroupsTable, role.GroupsPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(r.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryUsers queries the users edge of a Role.
func (c *RoleClient) QueryUsers(r *Role) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := r.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(role.Table, role.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, role.UsersTable, role.UsersPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(r.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryParent queries the parent edge of a Role.
func (c *RoleClient) QueryParent(r *Role) *RoleQuery {
	query := (&RoleClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := r.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(role.Table, role.FieldID, id),
			sqlgraph.To(role.Table, role.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, role.ParentTable, role.ParentColumn),
		)
		fromV = sqlgraph.Neighbors(r.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryChildren queries the children edge of a Role.
func (c *RoleClient) QueryChildren(r *Role) *RoleQuery {
	query := (&RoleClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := r.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(role.Table, role.FieldID, id),
			sqlgraph.To(role.Table, role.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, role.ChildrenTable, role.ChildrenColumn),
		)
		fromV = sqlgraph.Neighbors(r.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *RoleClient) Hooks() []Hook {
	hooks := c.hooks.Role
	return append(hooks[:len(hooks):len(hooks)], role.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *RoleClient) Interceptors() []Interceptor {
	inters := c.inters.Role
	return append(inters[:len(inters):len(inters)], role.Interceptors[:]...)
}

func (c *RoleClient) mutate(ctx context.Context, m *RoleMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&RoleCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&RoleUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&RoleUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&RoleDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Role mutation op: %q", m.Op())
	}
}

// TenantClient is a client for the Tenant schema.
type TenantClient struct {
	config
}

// NewTenantClient returns a client for the Tenant from the given config.
func NewTenantClient(c config) *TenantClient {
	return &TenantClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `tenant.Hooks(f(g(h())))`.
func (c *TenantClient) Use(hooks ...Hook) {
	c.hooks.Tenant = append(c.hooks.Tenant, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `tenant.Intercept(f(g(h())))`.
func (c *TenantClient) Intercept(interceptors ...Interceptor) {
	c.inters.Tenant = append(c.inters.Tenant, interceptors...)
}

// Create returns a builder for creating a Tenant entity.
func (c *TenantClient) Create() *TenantCreate {
	mutation := newTenantMutation(c.config, OpCreate)
	return &TenantCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Tenant entities.
func (c *TenantClient) CreateBulk(builders ...*TenantCreate) *TenantCreateBulk {
	return &TenantCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TenantClient) MapCreateBulk(slice any, setFunc func(*TenantCreate, int)) *TenantCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TenantCreateBulk{err: fmt.Errorf("calling to TenantClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TenantCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TenantCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Tenant.
func (c *TenantClient) Update() *TenantUpdate {
	mutation := newTenantMutation(c.config, OpUpdate)
	return &TenantUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TenantClient) UpdateOne(t *Tenant) *TenantUpdateOne {
	mutation := newTenantMutation(c.config, OpUpdateOne, withTenant(t))
	return &TenantUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TenantClient) UpdateOneID(id string) *TenantUpdateOne {
	mutation := newTenantMutation(c.config, OpUpdateOne, withTenantID(id))
	return &TenantUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Tenant.
func (c *TenantClient) Delete() *TenantDelete {
	mutation := newTenantMutation(c.config, OpDelete)
	return &TenantDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TenantClient) DeleteOne(t *Tenant) *TenantDeleteOne {
	return c.DeleteOneID(t.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TenantClient) DeleteOneID(id string) *TenantDeleteOne {
	builder := c.Delete().Where(tenant.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TenantDeleteOne{builder}
}

// Query returns a query builder for Tenant.
func (c *TenantClient) Query() *TenantQuery {
	return &TenantQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTenant},
		inters: c.Interceptors(),
	}
}

// Get returns a Tenant entity by its id.
func (c *TenantClient) Get(ctx context.Context, id string) (*Tenant, error) {
	return c.Query().Where(tenant.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TenantClient) GetX(ctx context.Context, id string) *Tenant {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryUsers queries the users edge of a Tenant.
func (c *TenantClient) QueryUsers(t *Tenant) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(tenant.Table, tenant.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, tenant.UsersTable, tenant.UsersPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryMenus queries the menus edge of a Tenant.
func (c *TenantClient) QueryMenus(t *Tenant) *MenuQuery {
	query := (&MenuClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(tenant.Table, tenant.FieldID, id),
			sqlgraph.To(menu.Table, menu.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, tenant.MenusTable, tenant.MenusColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryApis queries the apis edge of a Tenant.
func (c *TenantClient) QueryApis(t *Tenant) *APIQuery {
	query := (&APIClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(tenant.Table, tenant.FieldID, id),
			sqlgraph.To(api.Table, api.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, tenant.ApisTable, tenant.ApisColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryButtons queries the buttons edge of a Tenant.
func (c *TenantClient) QueryButtons(t *Tenant) *ButtonQuery {
	query := (&ButtonClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := t.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(tenant.Table, tenant.FieldID, id),
			sqlgraph.To(button.Table, button.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, tenant.ButtonsTable, tenant.ButtonsColumn),
		)
		fromV = sqlgraph.Neighbors(t.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *TenantClient) Hooks() []Hook {
	hooks := c.hooks.Tenant
	return append(hooks[:len(hooks):len(hooks)], tenant.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *TenantClient) Interceptors() []Interceptor {
	inters := c.inters.Tenant
	return append(inters[:len(inters):len(inters)], tenant.Interceptors[:]...)
}

func (c *TenantClient) mutate(ctx context.Context, m *TenantMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TenantCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TenantUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TenantUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TenantDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Tenant mutation op: %q", m.Op())
	}
}

// TenantUserInfoClient is a client for the TenantUserInfo schema.
type TenantUserInfoClient struct {
	config
}

// NewTenantUserInfoClient returns a client for the TenantUserInfo from the given config.
func NewTenantUserInfoClient(c config) *TenantUserInfoClient {
	return &TenantUserInfoClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `tenantuserinfo.Hooks(f(g(h())))`.
func (c *TenantUserInfoClient) Use(hooks ...Hook) {
	c.hooks.TenantUserInfo = append(c.hooks.TenantUserInfo, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `tenantuserinfo.Intercept(f(g(h())))`.
func (c *TenantUserInfoClient) Intercept(interceptors ...Interceptor) {
	c.inters.TenantUserInfo = append(c.inters.TenantUserInfo, interceptors...)
}

// Create returns a builder for creating a TenantUserInfo entity.
func (c *TenantUserInfoClient) Create() *TenantUserInfoCreate {
	mutation := newTenantUserInfoMutation(c.config, OpCreate)
	return &TenantUserInfoCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of TenantUserInfo entities.
func (c *TenantUserInfoClient) CreateBulk(builders ...*TenantUserInfoCreate) *TenantUserInfoCreateBulk {
	return &TenantUserInfoCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TenantUserInfoClient) MapCreateBulk(slice any, setFunc func(*TenantUserInfoCreate, int)) *TenantUserInfoCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TenantUserInfoCreateBulk{err: fmt.Errorf("calling to TenantUserInfoClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TenantUserInfoCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TenantUserInfoCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for TenantUserInfo.
func (c *TenantUserInfoClient) Update() *TenantUserInfoUpdate {
	mutation := newTenantUserInfoMutation(c.config, OpUpdate)
	return &TenantUserInfoUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TenantUserInfoClient) UpdateOne(tui *TenantUserInfo) *TenantUserInfoUpdateOne {
	mutation := newTenantUserInfoMutation(c.config, OpUpdateOne, withTenantUserInfo(tui))
	return &TenantUserInfoUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TenantUserInfoClient) UpdateOneID(id string) *TenantUserInfoUpdateOne {
	mutation := newTenantUserInfoMutation(c.config, OpUpdateOne, withTenantUserInfoID(id))
	return &TenantUserInfoUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for TenantUserInfo.
func (c *TenantUserInfoClient) Delete() *TenantUserInfoDelete {
	mutation := newTenantUserInfoMutation(c.config, OpDelete)
	return &TenantUserInfoDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TenantUserInfoClient) DeleteOne(tui *TenantUserInfo) *TenantUserInfoDeleteOne {
	return c.DeleteOneID(tui.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TenantUserInfoClient) DeleteOneID(id string) *TenantUserInfoDeleteOne {
	builder := c.Delete().Where(tenantuserinfo.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TenantUserInfoDeleteOne{builder}
}

// Query returns a query builder for TenantUserInfo.
func (c *TenantUserInfoClient) Query() *TenantUserInfoQuery {
	return &TenantUserInfoQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeTenantUserInfo},
		inters: c.Interceptors(),
	}
}

// Get returns a TenantUserInfo entity by its id.
func (c *TenantUserInfoClient) Get(ctx context.Context, id string) (*TenantUserInfo, error) {
	return c.Query().Where(tenantuserinfo.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TenantUserInfoClient) GetX(ctx context.Context, id string) *TenantUserInfo {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenantUserInfoTenant queries the tenant_user_info_tenant edge of a TenantUserInfo.
func (c *TenantUserInfoClient) QueryTenantUserInfoTenant(tui *TenantUserInfo) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := tui.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(tenantuserinfo.Table, tenantuserinfo.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, tenantuserinfo.TenantUserInfoTenantTable, tenantuserinfo.TenantUserInfoTenantColumn),
		)
		fromV = sqlgraph.Neighbors(tui.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTenantUserInfoUser queries the tenant_user_info_user edge of a TenantUserInfo.
func (c *TenantUserInfoClient) QueryTenantUserInfoUser(tui *TenantUserInfo) *UserQuery {
	query := (&UserClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := tui.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(tenantuserinfo.Table, tenantuserinfo.FieldID, id),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, tenantuserinfo.TenantUserInfoUserTable, tenantuserinfo.TenantUserInfoUserColumn),
		)
		fromV = sqlgraph.Neighbors(tui.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *TenantUserInfoClient) Hooks() []Hook {
	hooks := c.hooks.TenantUserInfo
	return append(hooks[:len(hooks):len(hooks)], tenantuserinfo.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *TenantUserInfoClient) Interceptors() []Interceptor {
	inters := c.inters.TenantUserInfo
	return append(inters[:len(inters):len(inters)], tenantuserinfo.Interceptors[:]...)
}

func (c *TenantUserInfoClient) mutate(ctx context.Context, m *TenantUserInfoMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TenantUserInfoCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TenantUserInfoUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TenantUserInfoUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TenantUserInfoDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown TenantUserInfo mutation op: %q", m.Op())
	}
}

// TokenClient is a client for the Token schema.
type TokenClient struct {
	config
}

// NewTokenClient returns a client for the Token from the given config.
func NewTokenClient(c config) *TokenClient {
	return &TokenClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `token.Hooks(f(g(h())))`.
func (c *TokenClient) Use(hooks ...Hook) {
	c.hooks.Token = append(c.hooks.Token, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `token.Intercept(f(g(h())))`.
func (c *TokenClient) Intercept(interceptors ...Interceptor) {
	c.inters.Token = append(c.inters.Token, interceptors...)
}

// Create returns a builder for creating a Token entity.
func (c *TokenClient) Create() *TokenCreate {
	mutation := newTokenMutation(c.config, OpCreate)
	return &TokenCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of Token entities.
func (c *TokenClient) CreateBulk(builders ...*TokenCreate) *TokenCreateBulk {
	return &TokenCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *TokenClient) MapCreateBulk(slice any, setFunc func(*TokenCreate, int)) *TokenCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &TokenCreateBulk{err: fmt.Errorf("calling to TokenClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*TokenCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &TokenCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for Token.
func (c *TokenClient) Update() *TokenUpdate {
	mutation := newTokenMutation(c.config, OpUpdate)
	return &TokenUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *TokenClient) UpdateOne(t *Token) *TokenUpdateOne {
	mutation := newTokenMutation(c.config, OpUpdateOne, withToken(t))
	return &TokenUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *TokenClient) UpdateOneID(id string) *TokenUpdateOne {
	mutation := newTokenMutation(c.config, OpUpdateOne, withTokenID(id))
	return &TokenUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for Token.
func (c *TokenClient) Delete() *TokenDelete {
	mutation := newTokenMutation(c.config, OpDelete)
	return &TokenDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *TokenClient) DeleteOne(t *Token) *TokenDeleteOne {
	return c.DeleteOneID(t.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *TokenClient) DeleteOneID(id string) *TokenDeleteOne {
	builder := c.Delete().Where(token.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &TokenDeleteOne{builder}
}

// Query returns a query builder for Token.
func (c *TokenClient) Query() *TokenQuery {
	return &TokenQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeToken},
		inters: c.Interceptors(),
	}
}

// Get returns a Token entity by its id.
func (c *TokenClient) Get(ctx context.Context, id string) (*Token, error) {
	return c.Query().Where(token.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *TokenClient) GetX(ctx context.Context, id string) *Token {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// Hooks returns the client hooks.
func (c *TokenClient) Hooks() []Hook {
	return c.hooks.Token
}

// Interceptors returns the client interceptors.
func (c *TokenClient) Interceptors() []Interceptor {
	return c.inters.Token
}

func (c *TokenClient) mutate(ctx context.Context, m *TokenMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&TokenCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&TokenUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&TokenUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&TokenDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown Token mutation op: %q", m.Op())
	}
}

// UserClient is a client for the User schema.
type UserClient struct {
	config
}

// NewUserClient returns a client for the User from the given config.
func NewUserClient(c config) *UserClient {
	return &UserClient{config: c}
}

// Use adds a list of mutation hooks to the hooks stack.
// A call to `Use(f, g, h)` equals to `user.Hooks(f(g(h())))`.
func (c *UserClient) Use(hooks ...Hook) {
	c.hooks.User = append(c.hooks.User, hooks...)
}

// Intercept adds a list of query interceptors to the interceptors stack.
// A call to `Intercept(f, g, h)` equals to `user.Intercept(f(g(h())))`.
func (c *UserClient) Intercept(interceptors ...Interceptor) {
	c.inters.User = append(c.inters.User, interceptors...)
}

// Create returns a builder for creating a User entity.
func (c *UserClient) Create() *UserCreate {
	mutation := newUserMutation(c.config, OpCreate)
	return &UserCreate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// CreateBulk returns a builder for creating a bulk of User entities.
func (c *UserClient) CreateBulk(builders ...*UserCreate) *UserCreateBulk {
	return &UserCreateBulk{config: c.config, builders: builders}
}

// MapCreateBulk creates a bulk creation builder from the given slice. For each item in the slice, the function creates
// a builder and applies setFunc on it.
func (c *UserClient) MapCreateBulk(slice any, setFunc func(*UserCreate, int)) *UserCreateBulk {
	rv := reflect.ValueOf(slice)
	if rv.Kind() != reflect.Slice {
		return &UserCreateBulk{err: fmt.Errorf("calling to UserClient.MapCreateBulk with wrong type %T, need slice", slice)}
	}
	builders := make([]*UserCreate, rv.Len())
	for i := 0; i < rv.Len(); i++ {
		builders[i] = c.Create()
		setFunc(builders[i], i)
	}
	return &UserCreateBulk{config: c.config, builders: builders}
}

// Update returns an update builder for User.
func (c *UserClient) Update() *UserUpdate {
	mutation := newUserMutation(c.config, OpUpdate)
	return &UserUpdate{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOne returns an update builder for the given entity.
func (c *UserClient) UpdateOne(u *User) *UserUpdateOne {
	mutation := newUserMutation(c.config, OpUpdateOne, withUser(u))
	return &UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// UpdateOneID returns an update builder for the given id.
func (c *UserClient) UpdateOneID(id string) *UserUpdateOne {
	mutation := newUserMutation(c.config, OpUpdateOne, withUserID(id))
	return &UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// Delete returns a delete builder for User.
func (c *UserClient) Delete() *UserDelete {
	mutation := newUserMutation(c.config, OpDelete)
	return &UserDelete{config: c.config, hooks: c.Hooks(), mutation: mutation}
}

// DeleteOne returns a builder for deleting the given entity.
func (c *UserClient) DeleteOne(u *User) *UserDeleteOne {
	return c.DeleteOneID(u.ID)
}

// DeleteOneID returns a builder for deleting the given entity by its id.
func (c *UserClient) DeleteOneID(id string) *UserDeleteOne {
	builder := c.Delete().Where(user.ID(id))
	builder.mutation.id = &id
	builder.mutation.op = OpDeleteOne
	return &UserDeleteOne{builder}
}

// Query returns a query builder for User.
func (c *UserClient) Query() *UserQuery {
	return &UserQuery{
		config: c.config,
		ctx:    &QueryContext{Type: TypeUser},
		inters: c.Interceptors(),
	}
}

// Get returns a User entity by its id.
func (c *UserClient) Get(ctx context.Context, id string) (*User, error) {
	return c.Query().Where(user.ID(id)).Only(ctx)
}

// GetX is like Get, but panics if an error occurs.
func (c *UserClient) GetX(ctx context.Context, id string) *User {
	obj, err := c.Get(ctx, id)
	if err != nil {
		panic(err)
	}
	return obj
}

// QueryTenants queries the tenants edge of a User.
func (c *UserClient) QueryTenants(u *User) *TenantQuery {
	query := (&TenantClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, user.TenantsTable, user.TenantsPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryOrganizations queries the organizations edge of a User.
func (c *UserClient) QueryOrganizations(u *User) *OrganizationQuery {
	query := (&OrganizationClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(organization.Table, organization.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, user.OrganizationsTable, user.OrganizationsPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryPositions queries the positions edge of a User.
func (c *UserClient) QueryPositions(u *User) *PositionQuery {
	query := (&PositionClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(position.Table, position.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, user.PositionsTable, user.PositionsPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryGroups queries the groups edge of a User.
func (c *UserClient) QueryGroups(u *User) *GroupQuery {
	query := (&GroupClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(group.Table, group.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, user.GroupsTable, user.GroupsPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryRoles queries the roles edge of a User.
func (c *UserClient) QueryRoles(u *User) *RoleQuery {
	query := (&RoleClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(role.Table, role.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, user.RolesTable, user.RolesPrimaryKey...),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryOrganizationInfos queries the organization_infos edge of a User.
func (c *UserClient) QueryOrganizationInfos(u *User) *OrganizationUserInfoQuery {
	query := (&OrganizationUserInfoClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(organizationuserinfo.Table, organizationuserinfo.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, user.OrganizationInfosTable, user.OrganizationInfosColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryTenantInfos queries the tenant_infos edge of a User.
func (c *UserClient) QueryTenantInfos(u *User) *TenantUserInfoQuery {
	query := (&TenantUserInfoClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(tenantuserinfo.Table, tenantuserinfo.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, user.TenantInfosTable, user.TenantInfosColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryFiles queries the files edge of a User.
func (c *UserClient) QueryFiles(u *User) *FileQuery {
	query := (&FileClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(file.Table, file.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, user.FilesTable, user.FilesColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// QueryAvatar queries the avatar edge of a User.
func (c *UserClient) QueryAvatar(u *User) *FileQuery {
	query := (&FileClient{config: c.config}).Query()
	query.path = func(context.Context) (fromV *sql.Selector, _ error) {
		id := u.ID
		step := sqlgraph.NewStep(
			sqlgraph.From(user.Table, user.FieldID, id),
			sqlgraph.To(file.Table, file.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, user.AvatarTable, user.AvatarColumn),
		)
		fromV = sqlgraph.Neighbors(u.driver.Dialect(), step)
		return fromV, nil
	}
	return query
}

// Hooks returns the client hooks.
func (c *UserClient) Hooks() []Hook {
	hooks := c.hooks.User
	return append(hooks[:len(hooks):len(hooks)], user.Hooks[:]...)
}

// Interceptors returns the client interceptors.
func (c *UserClient) Interceptors() []Interceptor {
	inters := c.inters.User
	return append(inters[:len(inters):len(inters)], user.Interceptors[:]...)
}

func (c *UserClient) mutate(ctx context.Context, m *UserMutation) (Value, error) {
	switch m.Op() {
	case OpCreate:
		return (&UserCreate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdate:
		return (&UserUpdate{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpUpdateOne:
		return (&UserUpdateOne{config: c.config, hooks: c.Hooks(), mutation: m}).Save(ctx)
	case OpDelete, OpDeleteOne:
		return (&UserDelete{config: c.config, hooks: c.Hooks(), mutation: m}).Exec(ctx)
	default:
		return nil, fmt.Errorf("ent: unknown User mutation op: %q", m.Op())
	}
}

// hooks and interceptors per client, for fast access.
type (
	hooks struct {
		API, Application, Button, File, Group, GroupType, Menu, Organization,
		OrganizationUserInfo, Position, Role, Tenant, TenantUserInfo, Token,
		User []ent.Hook
	}
	inters struct {
		API, Application, Button, File, Group, GroupType, Menu, Organization,
		OrganizationUserInfo, Position, Role, Tenant, TenantUserInfo, Token,
		User []ent.Interceptor
	}
)
