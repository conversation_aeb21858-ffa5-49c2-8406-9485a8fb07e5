// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"phoenix/service/saas/model/ent/menu"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// <PERSON>u is the model entity for the Menu schema.
type Menu struct {
	config `json:"-"`
	// ID of the ent.
	// Snowflake ID | 全局唯一ID
	ID string `json:"id,omitempty"`
	// Created Time | 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// Updated Time | 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// Sort number | 排序编号
	Sort uint32 `json:"sort,omitempty"`
	// Deleted Time | 删除时间（软删除标识）
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// menu name | 菜单名称
	Name string `json:"name,omitempty"`
	// menu title | 菜单标题
	Title string `json:"title,omitempty"`
	// menu icon | 菜单图标
	Icon string `json:"icon,omitempty"`
	// parent menu ID | 父菜单ID
	ParentID string `json:"parent_id,omitempty"`
	// menu type | 菜单类型 （菜单或目录）0 目录 1 菜单
	MenuType uint32 `json:"menu_type,omitempty"`
	// index path | 菜单路由路径
	URL string `json:"url,omitempty"`
	// redirect path | 跳转路径 （外链）
	Redirect string `json:"redirect,omitempty"`
	// the path of vue file | 组件路径
	Component string `json:"component,omitempty"`
	// is_active | 是否激活
	IsActive bool `json:"is_active,omitempty"`
	// hidden | 是否隐藏
	Hidden bool `json:"hidden,omitempty"`
	// hidden_in_tab | 是否隐藏标签
	HiddenInTab bool `json:"hidden_in_tab,omitempty"`
	// fixed | Fixed
	Fixed bool `json:"fixed,omitempty"`
	// remark | 备注
	Remark string `json:"remark,omitempty"`
	// meta data | 菜单Meta信息
	Meta string `json:"meta,omitempty"`
	// is_full_page | 是否全屏
	IsFullPage bool `json:"is_full_page,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the MenuQuery when eager-loading is set.
	Edges        MenuEdges `json:"edges"`
	tenant_menus *string
	selectValues sql.SelectValues
}

// MenuEdges holds the relations/edges for other nodes in the graph.
type MenuEdges struct {
	// Roles holds the value of the roles edge.
	Roles []*Role `json:"roles,omitempty"`
	// Buttons holds the value of the buttons edge.
	Buttons []*Button `json:"buttons,omitempty"`
	// Parent holds the value of the parent edge.
	Parent *Menu `json:"parent,omitempty"`
	// Children holds the value of the children edge.
	Children []*Menu `json:"children,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [4]bool
}

// RolesOrErr returns the Roles value or an error if the edge
// was not loaded in eager-loading.
func (e MenuEdges) RolesOrErr() ([]*Role, error) {
	if e.loadedTypes[0] {
		return e.Roles, nil
	}
	return nil, &NotLoadedError{edge: "roles"}
}

// ButtonsOrErr returns the Buttons value or an error if the edge
// was not loaded in eager-loading.
func (e MenuEdges) ButtonsOrErr() ([]*Button, error) {
	if e.loadedTypes[1] {
		return e.Buttons, nil
	}
	return nil, &NotLoadedError{edge: "buttons"}
}

// ParentOrErr returns the Parent value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e MenuEdges) ParentOrErr() (*Menu, error) {
	if e.Parent != nil {
		return e.Parent, nil
	} else if e.loadedTypes[2] {
		return nil, &NotFoundError{label: menu.Label}
	}
	return nil, &NotLoadedError{edge: "parent"}
}

// ChildrenOrErr returns the Children value or an error if the edge
// was not loaded in eager-loading.
func (e MenuEdges) ChildrenOrErr() ([]*Menu, error) {
	if e.loadedTypes[3] {
		return e.Children, nil
	}
	return nil, &NotLoadedError{edge: "children"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Menu) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case menu.FieldIsActive, menu.FieldHidden, menu.FieldHiddenInTab, menu.FieldFixed, menu.FieldIsFullPage:
			values[i] = new(sql.NullBool)
		case menu.FieldSort, menu.FieldMenuType:
			values[i] = new(sql.NullInt64)
		case menu.FieldID, menu.FieldName, menu.FieldTitle, menu.FieldIcon, menu.FieldParentID, menu.FieldURL, menu.FieldRedirect, menu.FieldComponent, menu.FieldRemark, menu.FieldMeta:
			values[i] = new(sql.NullString)
		case menu.FieldCreatedAt, menu.FieldUpdatedAt, menu.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		case menu.ForeignKeys[0]: // tenant_menus
			values[i] = new(sql.NullString)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Menu fields.
func (m *Menu) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case menu.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				m.ID = value.String
			}
		case menu.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				m.CreatedAt = value.Time
			}
		case menu.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				m.UpdatedAt = value.Time
			}
		case menu.FieldSort:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field sort", values[i])
			} else if value.Valid {
				m.Sort = uint32(value.Int64)
			}
		case menu.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				m.DeletedAt = value.Time
			}
		case menu.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				m.Name = value.String
			}
		case menu.FieldTitle:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field title", values[i])
			} else if value.Valid {
				m.Title = value.String
			}
		case menu.FieldIcon:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field icon", values[i])
			} else if value.Valid {
				m.Icon = value.String
			}
		case menu.FieldParentID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field parent_id", values[i])
			} else if value.Valid {
				m.ParentID = value.String
			}
		case menu.FieldMenuType:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field menu_type", values[i])
			} else if value.Valid {
				m.MenuType = uint32(value.Int64)
			}
		case menu.FieldURL:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field url", values[i])
			} else if value.Valid {
				m.URL = value.String
			}
		case menu.FieldRedirect:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field redirect", values[i])
			} else if value.Valid {
				m.Redirect = value.String
			}
		case menu.FieldComponent:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field component", values[i])
			} else if value.Valid {
				m.Component = value.String
			}
		case menu.FieldIsActive:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_active", values[i])
			} else if value.Valid {
				m.IsActive = value.Bool
			}
		case menu.FieldHidden:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field hidden", values[i])
			} else if value.Valid {
				m.Hidden = value.Bool
			}
		case menu.FieldHiddenInTab:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field hidden_in_tab", values[i])
			} else if value.Valid {
				m.HiddenInTab = value.Bool
			}
		case menu.FieldFixed:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field fixed", values[i])
			} else if value.Valid {
				m.Fixed = value.Bool
			}
		case menu.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				m.Remark = value.String
			}
		case menu.FieldMeta:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field meta", values[i])
			} else if value.Valid {
				m.Meta = value.String
			}
		case menu.FieldIsFullPage:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_full_page", values[i])
			} else if value.Valid {
				m.IsFullPage = value.Bool
			}
		case menu.ForeignKeys[0]:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_menus", values[i])
			} else if value.Valid {
				m.tenant_menus = new(string)
				*m.tenant_menus = value.String
			}
		default:
			m.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Menu.
// This includes values selected through modifiers, order, etc.
func (m *Menu) Value(name string) (ent.Value, error) {
	return m.selectValues.Get(name)
}

// QueryRoles queries the "roles" edge of the Menu entity.
func (m *Menu) QueryRoles() *RoleQuery {
	return NewMenuClient(m.config).QueryRoles(m)
}

// QueryButtons queries the "buttons" edge of the Menu entity.
func (m *Menu) QueryButtons() *ButtonQuery {
	return NewMenuClient(m.config).QueryButtons(m)
}

// QueryParent queries the "parent" edge of the Menu entity.
func (m *Menu) QueryParent() *MenuQuery {
	return NewMenuClient(m.config).QueryParent(m)
}

// QueryChildren queries the "children" edge of the Menu entity.
func (m *Menu) QueryChildren() *MenuQuery {
	return NewMenuClient(m.config).QueryChildren(m)
}

// Update returns a builder for updating this Menu.
// Note that you need to call Menu.Unwrap() before calling this method if this Menu
// was returned from a transaction, and the transaction was committed or rolled back.
func (m *Menu) Update() *MenuUpdateOne {
	return NewMenuClient(m.config).UpdateOne(m)
}

// Unwrap unwraps the Menu entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (m *Menu) Unwrap() *Menu {
	_tx, ok := m.config.driver.(*txDriver)
	if !ok {
		panic("ent: Menu is not a transactional entity")
	}
	m.config.driver = _tx.drv
	return m
}

// String implements the fmt.Stringer.
func (m *Menu) String() string {
	var builder strings.Builder
	builder.WriteString("Menu(")
	builder.WriteString(fmt.Sprintf("id=%v, ", m.ID))
	builder.WriteString("created_at=")
	builder.WriteString(m.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(m.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("sort=")
	builder.WriteString(fmt.Sprintf("%v", m.Sort))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(m.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(m.Name)
	builder.WriteString(", ")
	builder.WriteString("title=")
	builder.WriteString(m.Title)
	builder.WriteString(", ")
	builder.WriteString("icon=")
	builder.WriteString(m.Icon)
	builder.WriteString(", ")
	builder.WriteString("parent_id=")
	builder.WriteString(m.ParentID)
	builder.WriteString(", ")
	builder.WriteString("menu_type=")
	builder.WriteString(fmt.Sprintf("%v", m.MenuType))
	builder.WriteString(", ")
	builder.WriteString("url=")
	builder.WriteString(m.URL)
	builder.WriteString(", ")
	builder.WriteString("redirect=")
	builder.WriteString(m.Redirect)
	builder.WriteString(", ")
	builder.WriteString("component=")
	builder.WriteString(m.Component)
	builder.WriteString(", ")
	builder.WriteString("is_active=")
	builder.WriteString(fmt.Sprintf("%v", m.IsActive))
	builder.WriteString(", ")
	builder.WriteString("hidden=")
	builder.WriteString(fmt.Sprintf("%v", m.Hidden))
	builder.WriteString(", ")
	builder.WriteString("hidden_in_tab=")
	builder.WriteString(fmt.Sprintf("%v", m.HiddenInTab))
	builder.WriteString(", ")
	builder.WriteString("fixed=")
	builder.WriteString(fmt.Sprintf("%v", m.Fixed))
	builder.WriteString(", ")
	builder.WriteString("remark=")
	builder.WriteString(m.Remark)
	builder.WriteString(", ")
	builder.WriteString("meta=")
	builder.WriteString(m.Meta)
	builder.WriteString(", ")
	builder.WriteString("is_full_page=")
	builder.WriteString(fmt.Sprintf("%v", m.IsFullPage))
	builder.WriteByte(')')
	return builder.String()
}

// Menus is a parsable slice of Menu.
type Menus []*Menu
