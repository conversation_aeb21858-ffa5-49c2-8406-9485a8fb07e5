// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"phoenix/service/saas/model/ent/grouptype"
	"phoenix/service/saas/model/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// GroupTypeDelete is the builder for deleting a GroupType entity.
type GroupTypeDelete struct {
	config
	hooks    []Hook
	mutation *GroupTypeMutation
}

// Where appends a list predicates to the GroupTypeDelete builder.
func (gtd *GroupTypeDelete) Where(ps ...predicate.GroupType) *GroupTypeDelete {
	gtd.mutation.Where(ps...)
	return gtd
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (gtd *GroupTypeDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, gtd.sqlExec, gtd.mutation, gtd.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (gtd *GroupTypeDelete) ExecX(ctx context.Context) int {
	n, err := gtd.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (gtd *GroupTypeDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(grouptype.Table, sqlgraph.NewFieldSpec(grouptype.FieldID, field.TypeString))
	if ps := gtd.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, gtd.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	gtd.mutation.done = true
	return affected, err
}

// GroupTypeDeleteOne is the builder for deleting a single GroupType entity.
type GroupTypeDeleteOne struct {
	gtd *GroupTypeDelete
}

// Where appends a list predicates to the GroupTypeDelete builder.
func (gtdo *GroupTypeDeleteOne) Where(ps ...predicate.GroupType) *GroupTypeDeleteOne {
	gtdo.gtd.mutation.Where(ps...)
	return gtdo
}

// Exec executes the deletion query.
func (gtdo *GroupTypeDeleteOne) Exec(ctx context.Context) error {
	n, err := gtdo.gtd.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{grouptype.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (gtdo *GroupTypeDeleteOne) ExecX(ctx context.Context) {
	if err := gtdo.Exec(ctx); err != nil {
		panic(err)
	}
}
