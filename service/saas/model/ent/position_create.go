// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/position"
	"phoenix/service/saas/model/ent/tenant"
	"phoenix/service/saas/model/ent/user"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// PositionCreate is the builder for creating a Position entity.
type PositionCreate struct {
	config
	mutation *PositionMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (pc *PositionCreate) SetCreatedAt(t time.Time) *PositionCreate {
	pc.mutation.SetCreatedAt(t)
	return pc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (pc *PositionCreate) SetNillableCreatedAt(t *time.Time) *PositionCreate {
	if t != nil {
		pc.SetCreatedAt(*t)
	}
	return pc
}

// SetUpdatedAt sets the "updated_at" field.
func (pc *PositionCreate) SetUpdatedAt(t time.Time) *PositionCreate {
	pc.mutation.SetUpdatedAt(t)
	return pc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (pc *PositionCreate) SetNillableUpdatedAt(t *time.Time) *PositionCreate {
	if t != nil {
		pc.SetUpdatedAt(*t)
	}
	return pc
}

// SetStatus sets the "status" field.
func (pc *PositionCreate) SetStatus(b bool) *PositionCreate {
	pc.mutation.SetStatus(b)
	return pc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (pc *PositionCreate) SetNillableStatus(b *bool) *PositionCreate {
	if b != nil {
		pc.SetStatus(*b)
	}
	return pc
}

// SetSort sets the "sort" field.
func (pc *PositionCreate) SetSort(u uint32) *PositionCreate {
	pc.mutation.SetSort(u)
	return pc
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (pc *PositionCreate) SetNillableSort(u *uint32) *PositionCreate {
	if u != nil {
		pc.SetSort(*u)
	}
	return pc
}

// SetTenantID sets the "tenant_id" field.
func (pc *PositionCreate) SetTenantID(s string) *PositionCreate {
	pc.mutation.SetTenantID(s)
	return pc
}

// SetDeletedAt sets the "deleted_at" field.
func (pc *PositionCreate) SetDeletedAt(t time.Time) *PositionCreate {
	pc.mutation.SetDeletedAt(t)
	return pc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (pc *PositionCreate) SetNillableDeletedAt(t *time.Time) *PositionCreate {
	if t != nil {
		pc.SetDeletedAt(*t)
	}
	return pc
}

// SetName sets the "name" field.
func (pc *PositionCreate) SetName(s string) *PositionCreate {
	pc.mutation.SetName(s)
	return pc
}

// SetCode sets the "code" field.
func (pc *PositionCreate) SetCode(s string) *PositionCreate {
	pc.mutation.SetCode(s)
	return pc
}

// SetOrganizationID sets the "organization_id" field.
func (pc *PositionCreate) SetOrganizationID(s string) *PositionCreate {
	pc.mutation.SetOrganizationID(s)
	return pc
}

// SetRemark sets the "remark" field.
func (pc *PositionCreate) SetRemark(s string) *PositionCreate {
	pc.mutation.SetRemark(s)
	return pc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (pc *PositionCreate) SetNillableRemark(s *string) *PositionCreate {
	if s != nil {
		pc.SetRemark(*s)
	}
	return pc
}

// SetID sets the "id" field.
func (pc *PositionCreate) SetID(s string) *PositionCreate {
	pc.mutation.SetID(s)
	return pc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (pc *PositionCreate) SetNillableID(s *string) *PositionCreate {
	if s != nil {
		pc.SetID(*s)
	}
	return pc
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (pc *PositionCreate) SetTenant(t *Tenant) *PositionCreate {
	return pc.SetTenantID(t.ID)
}

// AddUserIDs adds the "users" edge to the User entity by IDs.
func (pc *PositionCreate) AddUserIDs(ids ...string) *PositionCreate {
	pc.mutation.AddUserIDs(ids...)
	return pc
}

// AddUsers adds the "users" edges to the User entity.
func (pc *PositionCreate) AddUsers(u ...*User) *PositionCreate {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return pc.AddUserIDs(ids...)
}

// Mutation returns the PositionMutation object of the builder.
func (pc *PositionCreate) Mutation() *PositionMutation {
	return pc.mutation
}

// Save creates the Position in the database.
func (pc *PositionCreate) Save(ctx context.Context) (*Position, error) {
	if err := pc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, pc.sqlSave, pc.mutation, pc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (pc *PositionCreate) SaveX(ctx context.Context) *Position {
	v, err := pc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (pc *PositionCreate) Exec(ctx context.Context) error {
	_, err := pc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pc *PositionCreate) ExecX(ctx context.Context) {
	if err := pc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (pc *PositionCreate) defaults() error {
	if _, ok := pc.mutation.CreatedAt(); !ok {
		if position.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized position.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := position.DefaultCreatedAt()
		pc.mutation.SetCreatedAt(v)
	}
	if _, ok := pc.mutation.UpdatedAt(); !ok {
		if position.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized position.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := position.DefaultUpdatedAt()
		pc.mutation.SetUpdatedAt(v)
	}
	if _, ok := pc.mutation.Status(); !ok {
		v := position.DefaultStatus
		pc.mutation.SetStatus(v)
	}
	if _, ok := pc.mutation.Sort(); !ok {
		v := position.DefaultSort
		pc.mutation.SetSort(v)
	}
	if _, ok := pc.mutation.Remark(); !ok {
		v := position.DefaultRemark
		pc.mutation.SetRemark(v)
	}
	if _, ok := pc.mutation.ID(); !ok {
		if position.DefaultID == nil {
			return fmt.Errorf("ent: uninitialized position.DefaultID (forgotten import ent/runtime?)")
		}
		v := position.DefaultID()
		pc.mutation.SetID(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (pc *PositionCreate) check() error {
	if _, ok := pc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Position.created_at"`)}
	}
	if _, ok := pc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Position.updated_at"`)}
	}
	if _, ok := pc.mutation.Sort(); !ok {
		return &ValidationError{Name: "sort", err: errors.New(`ent: missing required field "Position.sort"`)}
	}
	if _, ok := pc.mutation.TenantID(); !ok {
		return &ValidationError{Name: "tenant_id", err: errors.New(`ent: missing required field "Position.tenant_id"`)}
	}
	if _, ok := pc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Position.name"`)}
	}
	if _, ok := pc.mutation.Code(); !ok {
		return &ValidationError{Name: "code", err: errors.New(`ent: missing required field "Position.code"`)}
	}
	if _, ok := pc.mutation.OrganizationID(); !ok {
		return &ValidationError{Name: "organization_id", err: errors.New(`ent: missing required field "Position.organization_id"`)}
	}
	if _, ok := pc.mutation.TenantID(); !ok {
		return &ValidationError{Name: "tenant", err: errors.New(`ent: missing required edge "Position.tenant"`)}
	}
	return nil
}

func (pc *PositionCreate) sqlSave(ctx context.Context) (*Position, error) {
	if err := pc.check(); err != nil {
		return nil, err
	}
	_node, _spec := pc.createSpec()
	if err := sqlgraph.CreateNode(ctx, pc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected Position.ID type: %T", _spec.ID.Value)
		}
	}
	pc.mutation.id = &_node.ID
	pc.mutation.done = true
	return _node, nil
}

func (pc *PositionCreate) createSpec() (*Position, *sqlgraph.CreateSpec) {
	var (
		_node = &Position{config: pc.config}
		_spec = sqlgraph.NewCreateSpec(position.Table, sqlgraph.NewFieldSpec(position.FieldID, field.TypeString))
	)
	_spec.OnConflict = pc.conflict
	if id, ok := pc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := pc.mutation.CreatedAt(); ok {
		_spec.SetField(position.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := pc.mutation.UpdatedAt(); ok {
		_spec.SetField(position.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := pc.mutation.Status(); ok {
		_spec.SetField(position.FieldStatus, field.TypeBool, value)
		_node.Status = value
	}
	if value, ok := pc.mutation.Sort(); ok {
		_spec.SetField(position.FieldSort, field.TypeUint32, value)
		_node.Sort = value
	}
	if value, ok := pc.mutation.DeletedAt(); ok {
		_spec.SetField(position.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := pc.mutation.Name(); ok {
		_spec.SetField(position.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := pc.mutation.Code(); ok {
		_spec.SetField(position.FieldCode, field.TypeString, value)
		_node.Code = value
	}
	if value, ok := pc.mutation.OrganizationID(); ok {
		_spec.SetField(position.FieldOrganizationID, field.TypeString, value)
		_node.OrganizationID = value
	}
	if value, ok := pc.mutation.Remark(); ok {
		_spec.SetField(position.FieldRemark, field.TypeString, value)
		_node.Remark = value
	}
	if nodes := pc.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   position.TenantTable,
			Columns: []string{position.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TenantID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := pc.mutation.UsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   position.UsersTable,
			Columns: position.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Position.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.PositionUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (pc *PositionCreate) OnConflict(opts ...sql.ConflictOption) *PositionUpsertOne {
	pc.conflict = opts
	return &PositionUpsertOne{
		create: pc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Position.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (pc *PositionCreate) OnConflictColumns(columns ...string) *PositionUpsertOne {
	pc.conflict = append(pc.conflict, sql.ConflictColumns(columns...))
	return &PositionUpsertOne{
		create: pc,
	}
}

type (
	// PositionUpsertOne is the builder for "upsert"-ing
	//  one Position node.
	PositionUpsertOne struct {
		create *PositionCreate
	}

	// PositionUpsert is the "OnConflict" setter.
	PositionUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *PositionUpsert) SetUpdatedAt(v time.Time) *PositionUpsert {
	u.Set(position.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *PositionUpsert) UpdateUpdatedAt() *PositionUpsert {
	u.SetExcluded(position.FieldUpdatedAt)
	return u
}

// SetStatus sets the "status" field.
func (u *PositionUpsert) SetStatus(v bool) *PositionUpsert {
	u.Set(position.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *PositionUpsert) UpdateStatus() *PositionUpsert {
	u.SetExcluded(position.FieldStatus)
	return u
}

// ClearStatus clears the value of the "status" field.
func (u *PositionUpsert) ClearStatus() *PositionUpsert {
	u.SetNull(position.FieldStatus)
	return u
}

// SetSort sets the "sort" field.
func (u *PositionUpsert) SetSort(v uint32) *PositionUpsert {
	u.Set(position.FieldSort, v)
	return u
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *PositionUpsert) UpdateSort() *PositionUpsert {
	u.SetExcluded(position.FieldSort)
	return u
}

// AddSort adds v to the "sort" field.
func (u *PositionUpsert) AddSort(v uint32) *PositionUpsert {
	u.Add(position.FieldSort, v)
	return u
}

// SetTenantID sets the "tenant_id" field.
func (u *PositionUpsert) SetTenantID(v string) *PositionUpsert {
	u.Set(position.FieldTenantID, v)
	return u
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *PositionUpsert) UpdateTenantID() *PositionUpsert {
	u.SetExcluded(position.FieldTenantID)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *PositionUpsert) SetDeletedAt(v time.Time) *PositionUpsert {
	u.Set(position.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *PositionUpsert) UpdateDeletedAt() *PositionUpsert {
	u.SetExcluded(position.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *PositionUpsert) ClearDeletedAt() *PositionUpsert {
	u.SetNull(position.FieldDeletedAt)
	return u
}

// SetName sets the "name" field.
func (u *PositionUpsert) SetName(v string) *PositionUpsert {
	u.Set(position.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *PositionUpsert) UpdateName() *PositionUpsert {
	u.SetExcluded(position.FieldName)
	return u
}

// SetCode sets the "code" field.
func (u *PositionUpsert) SetCode(v string) *PositionUpsert {
	u.Set(position.FieldCode, v)
	return u
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *PositionUpsert) UpdateCode() *PositionUpsert {
	u.SetExcluded(position.FieldCode)
	return u
}

// SetOrganizationID sets the "organization_id" field.
func (u *PositionUpsert) SetOrganizationID(v string) *PositionUpsert {
	u.Set(position.FieldOrganizationID, v)
	return u
}

// UpdateOrganizationID sets the "organization_id" field to the value that was provided on create.
func (u *PositionUpsert) UpdateOrganizationID() *PositionUpsert {
	u.SetExcluded(position.FieldOrganizationID)
	return u
}

// SetRemark sets the "remark" field.
func (u *PositionUpsert) SetRemark(v string) *PositionUpsert {
	u.Set(position.FieldRemark, v)
	return u
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *PositionUpsert) UpdateRemark() *PositionUpsert {
	u.SetExcluded(position.FieldRemark)
	return u
}

// ClearRemark clears the value of the "remark" field.
func (u *PositionUpsert) ClearRemark() *PositionUpsert {
	u.SetNull(position.FieldRemark)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Position.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(position.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *PositionUpsertOne) UpdateNewValues() *PositionUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(position.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(position.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Position.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *PositionUpsertOne) Ignore() *PositionUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *PositionUpsertOne) DoNothing() *PositionUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the PositionCreate.OnConflict
// documentation for more info.
func (u *PositionUpsertOne) Update(set func(*PositionUpsert)) *PositionUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&PositionUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *PositionUpsertOne) SetUpdatedAt(v time.Time) *PositionUpsertOne {
	return u.Update(func(s *PositionUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *PositionUpsertOne) UpdateUpdatedAt() *PositionUpsertOne {
	return u.Update(func(s *PositionUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetStatus sets the "status" field.
func (u *PositionUpsertOne) SetStatus(v bool) *PositionUpsertOne {
	return u.Update(func(s *PositionUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *PositionUpsertOne) UpdateStatus() *PositionUpsertOne {
	return u.Update(func(s *PositionUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *PositionUpsertOne) ClearStatus() *PositionUpsertOne {
	return u.Update(func(s *PositionUpsert) {
		s.ClearStatus()
	})
}

// SetSort sets the "sort" field.
func (u *PositionUpsertOne) SetSort(v uint32) *PositionUpsertOne {
	return u.Update(func(s *PositionUpsert) {
		s.SetSort(v)
	})
}

// AddSort adds v to the "sort" field.
func (u *PositionUpsertOne) AddSort(v uint32) *PositionUpsertOne {
	return u.Update(func(s *PositionUpsert) {
		s.AddSort(v)
	})
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *PositionUpsertOne) UpdateSort() *PositionUpsertOne {
	return u.Update(func(s *PositionUpsert) {
		s.UpdateSort()
	})
}

// SetTenantID sets the "tenant_id" field.
func (u *PositionUpsertOne) SetTenantID(v string) *PositionUpsertOne {
	return u.Update(func(s *PositionUpsert) {
		s.SetTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *PositionUpsertOne) UpdateTenantID() *PositionUpsertOne {
	return u.Update(func(s *PositionUpsert) {
		s.UpdateTenantID()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *PositionUpsertOne) SetDeletedAt(v time.Time) *PositionUpsertOne {
	return u.Update(func(s *PositionUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *PositionUpsertOne) UpdateDeletedAt() *PositionUpsertOne {
	return u.Update(func(s *PositionUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *PositionUpsertOne) ClearDeletedAt() *PositionUpsertOne {
	return u.Update(func(s *PositionUpsert) {
		s.ClearDeletedAt()
	})
}

// SetName sets the "name" field.
func (u *PositionUpsertOne) SetName(v string) *PositionUpsertOne {
	return u.Update(func(s *PositionUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *PositionUpsertOne) UpdateName() *PositionUpsertOne {
	return u.Update(func(s *PositionUpsert) {
		s.UpdateName()
	})
}

// SetCode sets the "code" field.
func (u *PositionUpsertOne) SetCode(v string) *PositionUpsertOne {
	return u.Update(func(s *PositionUpsert) {
		s.SetCode(v)
	})
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *PositionUpsertOne) UpdateCode() *PositionUpsertOne {
	return u.Update(func(s *PositionUpsert) {
		s.UpdateCode()
	})
}

// SetOrganizationID sets the "organization_id" field.
func (u *PositionUpsertOne) SetOrganizationID(v string) *PositionUpsertOne {
	return u.Update(func(s *PositionUpsert) {
		s.SetOrganizationID(v)
	})
}

// UpdateOrganizationID sets the "organization_id" field to the value that was provided on create.
func (u *PositionUpsertOne) UpdateOrganizationID() *PositionUpsertOne {
	return u.Update(func(s *PositionUpsert) {
		s.UpdateOrganizationID()
	})
}

// SetRemark sets the "remark" field.
func (u *PositionUpsertOne) SetRemark(v string) *PositionUpsertOne {
	return u.Update(func(s *PositionUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *PositionUpsertOne) UpdateRemark() *PositionUpsertOne {
	return u.Update(func(s *PositionUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *PositionUpsertOne) ClearRemark() *PositionUpsertOne {
	return u.Update(func(s *PositionUpsert) {
		s.ClearRemark()
	})
}

// Exec executes the query.
func (u *PositionUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for PositionCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *PositionUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *PositionUpsertOne) ID(ctx context.Context) (id string, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: PositionUpsertOne.ID is not supported by MySQL driver. Use PositionUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *PositionUpsertOne) IDX(ctx context.Context) string {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// PositionCreateBulk is the builder for creating many Position entities in bulk.
type PositionCreateBulk struct {
	config
	err      error
	builders []*PositionCreate
	conflict []sql.ConflictOption
}

// Save creates the Position entities in the database.
func (pcb *PositionCreateBulk) Save(ctx context.Context) ([]*Position, error) {
	if pcb.err != nil {
		return nil, pcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(pcb.builders))
	nodes := make([]*Position, len(pcb.builders))
	mutators := make([]Mutator, len(pcb.builders))
	for i := range pcb.builders {
		func(i int, root context.Context) {
			builder := pcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*PositionMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, pcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = pcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, pcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, pcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (pcb *PositionCreateBulk) SaveX(ctx context.Context) []*Position {
	v, err := pcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (pcb *PositionCreateBulk) Exec(ctx context.Context) error {
	_, err := pcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (pcb *PositionCreateBulk) ExecX(ctx context.Context) {
	if err := pcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Position.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.PositionUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (pcb *PositionCreateBulk) OnConflict(opts ...sql.ConflictOption) *PositionUpsertBulk {
	pcb.conflict = opts
	return &PositionUpsertBulk{
		create: pcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Position.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (pcb *PositionCreateBulk) OnConflictColumns(columns ...string) *PositionUpsertBulk {
	pcb.conflict = append(pcb.conflict, sql.ConflictColumns(columns...))
	return &PositionUpsertBulk{
		create: pcb,
	}
}

// PositionUpsertBulk is the builder for "upsert"-ing
// a bulk of Position nodes.
type PositionUpsertBulk struct {
	create *PositionCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Position.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(position.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *PositionUpsertBulk) UpdateNewValues() *PositionUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(position.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(position.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Position.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *PositionUpsertBulk) Ignore() *PositionUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *PositionUpsertBulk) DoNothing() *PositionUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the PositionCreateBulk.OnConflict
// documentation for more info.
func (u *PositionUpsertBulk) Update(set func(*PositionUpsert)) *PositionUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&PositionUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *PositionUpsertBulk) SetUpdatedAt(v time.Time) *PositionUpsertBulk {
	return u.Update(func(s *PositionUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *PositionUpsertBulk) UpdateUpdatedAt() *PositionUpsertBulk {
	return u.Update(func(s *PositionUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetStatus sets the "status" field.
func (u *PositionUpsertBulk) SetStatus(v bool) *PositionUpsertBulk {
	return u.Update(func(s *PositionUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *PositionUpsertBulk) UpdateStatus() *PositionUpsertBulk {
	return u.Update(func(s *PositionUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *PositionUpsertBulk) ClearStatus() *PositionUpsertBulk {
	return u.Update(func(s *PositionUpsert) {
		s.ClearStatus()
	})
}

// SetSort sets the "sort" field.
func (u *PositionUpsertBulk) SetSort(v uint32) *PositionUpsertBulk {
	return u.Update(func(s *PositionUpsert) {
		s.SetSort(v)
	})
}

// AddSort adds v to the "sort" field.
func (u *PositionUpsertBulk) AddSort(v uint32) *PositionUpsertBulk {
	return u.Update(func(s *PositionUpsert) {
		s.AddSort(v)
	})
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *PositionUpsertBulk) UpdateSort() *PositionUpsertBulk {
	return u.Update(func(s *PositionUpsert) {
		s.UpdateSort()
	})
}

// SetTenantID sets the "tenant_id" field.
func (u *PositionUpsertBulk) SetTenantID(v string) *PositionUpsertBulk {
	return u.Update(func(s *PositionUpsert) {
		s.SetTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *PositionUpsertBulk) UpdateTenantID() *PositionUpsertBulk {
	return u.Update(func(s *PositionUpsert) {
		s.UpdateTenantID()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *PositionUpsertBulk) SetDeletedAt(v time.Time) *PositionUpsertBulk {
	return u.Update(func(s *PositionUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *PositionUpsertBulk) UpdateDeletedAt() *PositionUpsertBulk {
	return u.Update(func(s *PositionUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *PositionUpsertBulk) ClearDeletedAt() *PositionUpsertBulk {
	return u.Update(func(s *PositionUpsert) {
		s.ClearDeletedAt()
	})
}

// SetName sets the "name" field.
func (u *PositionUpsertBulk) SetName(v string) *PositionUpsertBulk {
	return u.Update(func(s *PositionUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *PositionUpsertBulk) UpdateName() *PositionUpsertBulk {
	return u.Update(func(s *PositionUpsert) {
		s.UpdateName()
	})
}

// SetCode sets the "code" field.
func (u *PositionUpsertBulk) SetCode(v string) *PositionUpsertBulk {
	return u.Update(func(s *PositionUpsert) {
		s.SetCode(v)
	})
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *PositionUpsertBulk) UpdateCode() *PositionUpsertBulk {
	return u.Update(func(s *PositionUpsert) {
		s.UpdateCode()
	})
}

// SetOrganizationID sets the "organization_id" field.
func (u *PositionUpsertBulk) SetOrganizationID(v string) *PositionUpsertBulk {
	return u.Update(func(s *PositionUpsert) {
		s.SetOrganizationID(v)
	})
}

// UpdateOrganizationID sets the "organization_id" field to the value that was provided on create.
func (u *PositionUpsertBulk) UpdateOrganizationID() *PositionUpsertBulk {
	return u.Update(func(s *PositionUpsert) {
		s.UpdateOrganizationID()
	})
}

// SetRemark sets the "remark" field.
func (u *PositionUpsertBulk) SetRemark(v string) *PositionUpsertBulk {
	return u.Update(func(s *PositionUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *PositionUpsertBulk) UpdateRemark() *PositionUpsertBulk {
	return u.Update(func(s *PositionUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *PositionUpsertBulk) ClearRemark() *PositionUpsertBulk {
	return u.Update(func(s *PositionUpsert) {
		s.ClearRemark()
	})
}

// Exec executes the query.
func (u *PositionUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the PositionCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for PositionCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *PositionUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
