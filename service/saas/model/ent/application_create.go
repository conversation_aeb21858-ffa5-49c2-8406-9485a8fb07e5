// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/application"
	"phoenix/service/saas/model/ent/tenant"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ApplicationCreate is the builder for creating a Application entity.
type ApplicationCreate struct {
	config
	mutation *ApplicationMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (ac *ApplicationCreate) SetCreatedAt(t time.Time) *ApplicationCreate {
	ac.mutation.SetCreatedAt(t)
	return ac
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (ac *ApplicationCreate) SetNillableCreatedAt(t *time.Time) *ApplicationCreate {
	if t != nil {
		ac.SetCreatedAt(*t)
	}
	return ac
}

// SetUpdatedAt sets the "updated_at" field.
func (ac *ApplicationCreate) SetUpdatedAt(t time.Time) *ApplicationCreate {
	ac.mutation.SetUpdatedAt(t)
	return ac
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (ac *ApplicationCreate) SetNillableUpdatedAt(t *time.Time) *ApplicationCreate {
	if t != nil {
		ac.SetUpdatedAt(*t)
	}
	return ac
}

// SetStatus sets the "status" field.
func (ac *ApplicationCreate) SetStatus(b bool) *ApplicationCreate {
	ac.mutation.SetStatus(b)
	return ac
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ac *ApplicationCreate) SetNillableStatus(b *bool) *ApplicationCreate {
	if b != nil {
		ac.SetStatus(*b)
	}
	return ac
}

// SetSort sets the "sort" field.
func (ac *ApplicationCreate) SetSort(u uint32) *ApplicationCreate {
	ac.mutation.SetSort(u)
	return ac
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (ac *ApplicationCreate) SetNillableSort(u *uint32) *ApplicationCreate {
	if u != nil {
		ac.SetSort(*u)
	}
	return ac
}

// SetTenantID sets the "tenant_id" field.
func (ac *ApplicationCreate) SetTenantID(s string) *ApplicationCreate {
	ac.mutation.SetTenantID(s)
	return ac
}

// SetDeletedAt sets the "deleted_at" field.
func (ac *ApplicationCreate) SetDeletedAt(t time.Time) *ApplicationCreate {
	ac.mutation.SetDeletedAt(t)
	return ac
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (ac *ApplicationCreate) SetNillableDeletedAt(t *time.Time) *ApplicationCreate {
	if t != nil {
		ac.SetDeletedAt(*t)
	}
	return ac
}

// SetName sets the "name" field.
func (ac *ApplicationCreate) SetName(s string) *ApplicationCreate {
	ac.mutation.SetName(s)
	return ac
}

// SetNillableName sets the "name" field if the given value is not nil.
func (ac *ApplicationCreate) SetNillableName(s *string) *ApplicationCreate {
	if s != nil {
		ac.SetName(*s)
	}
	return ac
}

// SetAppID sets the "app_id" field.
func (ac *ApplicationCreate) SetAppID(s string) *ApplicationCreate {
	ac.mutation.SetAppID(s)
	return ac
}

// SetNillableAppID sets the "app_id" field if the given value is not nil.
func (ac *ApplicationCreate) SetNillableAppID(s *string) *ApplicationCreate {
	if s != nil {
		ac.SetAppID(*s)
	}
	return ac
}

// SetSecret sets the "secret" field.
func (ac *ApplicationCreate) SetSecret(s string) *ApplicationCreate {
	ac.mutation.SetSecret(s)
	return ac
}

// SetNillableSecret sets the "secret" field if the given value is not nil.
func (ac *ApplicationCreate) SetNillableSecret(s *string) *ApplicationCreate {
	if s != nil {
		ac.SetSecret(*s)
	}
	return ac
}

// SetRemark sets the "remark" field.
func (ac *ApplicationCreate) SetRemark(s string) *ApplicationCreate {
	ac.mutation.SetRemark(s)
	return ac
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (ac *ApplicationCreate) SetNillableRemark(s *string) *ApplicationCreate {
	if s != nil {
		ac.SetRemark(*s)
	}
	return ac
}

// SetID sets the "id" field.
func (ac *ApplicationCreate) SetID(s string) *ApplicationCreate {
	ac.mutation.SetID(s)
	return ac
}

// SetNillableID sets the "id" field if the given value is not nil.
func (ac *ApplicationCreate) SetNillableID(s *string) *ApplicationCreate {
	if s != nil {
		ac.SetID(*s)
	}
	return ac
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (ac *ApplicationCreate) SetTenant(t *Tenant) *ApplicationCreate {
	return ac.SetTenantID(t.ID)
}

// Mutation returns the ApplicationMutation object of the builder.
func (ac *ApplicationCreate) Mutation() *ApplicationMutation {
	return ac.mutation
}

// Save creates the Application in the database.
func (ac *ApplicationCreate) Save(ctx context.Context) (*Application, error) {
	if err := ac.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, ac.sqlSave, ac.mutation, ac.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (ac *ApplicationCreate) SaveX(ctx context.Context) *Application {
	v, err := ac.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ac *ApplicationCreate) Exec(ctx context.Context) error {
	_, err := ac.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ac *ApplicationCreate) ExecX(ctx context.Context) {
	if err := ac.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ac *ApplicationCreate) defaults() error {
	if _, ok := ac.mutation.CreatedAt(); !ok {
		if application.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized application.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := application.DefaultCreatedAt()
		ac.mutation.SetCreatedAt(v)
	}
	if _, ok := ac.mutation.UpdatedAt(); !ok {
		if application.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized application.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := application.DefaultUpdatedAt()
		ac.mutation.SetUpdatedAt(v)
	}
	if _, ok := ac.mutation.Status(); !ok {
		v := application.DefaultStatus
		ac.mutation.SetStatus(v)
	}
	if _, ok := ac.mutation.Sort(); !ok {
		v := application.DefaultSort
		ac.mutation.SetSort(v)
	}
	if _, ok := ac.mutation.Name(); !ok {
		v := application.DefaultName
		ac.mutation.SetName(v)
	}
	if _, ok := ac.mutation.AppID(); !ok {
		if application.DefaultAppID == nil {
			return fmt.Errorf("ent: uninitialized application.DefaultAppID (forgotten import ent/runtime?)")
		}
		v := application.DefaultAppID()
		ac.mutation.SetAppID(v)
	}
	if _, ok := ac.mutation.Secret(); !ok {
		if application.DefaultSecret == nil {
			return fmt.Errorf("ent: uninitialized application.DefaultSecret (forgotten import ent/runtime?)")
		}
		v := application.DefaultSecret()
		ac.mutation.SetSecret(v)
	}
	if _, ok := ac.mutation.Remark(); !ok {
		v := application.DefaultRemark
		ac.mutation.SetRemark(v)
	}
	if _, ok := ac.mutation.ID(); !ok {
		if application.DefaultID == nil {
			return fmt.Errorf("ent: uninitialized application.DefaultID (forgotten import ent/runtime?)")
		}
		v := application.DefaultID()
		ac.mutation.SetID(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (ac *ApplicationCreate) check() error {
	if _, ok := ac.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Application.created_at"`)}
	}
	if _, ok := ac.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Application.updated_at"`)}
	}
	if _, ok := ac.mutation.Sort(); !ok {
		return &ValidationError{Name: "sort", err: errors.New(`ent: missing required field "Application.sort"`)}
	}
	if _, ok := ac.mutation.TenantID(); !ok {
		return &ValidationError{Name: "tenant_id", err: errors.New(`ent: missing required field "Application.tenant_id"`)}
	}
	if _, ok := ac.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Application.name"`)}
	}
	if _, ok := ac.mutation.AppID(); !ok {
		return &ValidationError{Name: "app_id", err: errors.New(`ent: missing required field "Application.app_id"`)}
	}
	if _, ok := ac.mutation.Secret(); !ok {
		return &ValidationError{Name: "secret", err: errors.New(`ent: missing required field "Application.secret"`)}
	}
	if _, ok := ac.mutation.Remark(); !ok {
		return &ValidationError{Name: "remark", err: errors.New(`ent: missing required field "Application.remark"`)}
	}
	if _, ok := ac.mutation.TenantID(); !ok {
		return &ValidationError{Name: "tenant", err: errors.New(`ent: missing required edge "Application.tenant"`)}
	}
	return nil
}

func (ac *ApplicationCreate) sqlSave(ctx context.Context) (*Application, error) {
	if err := ac.check(); err != nil {
		return nil, err
	}
	_node, _spec := ac.createSpec()
	if err := sqlgraph.CreateNode(ctx, ac.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected Application.ID type: %T", _spec.ID.Value)
		}
	}
	ac.mutation.id = &_node.ID
	ac.mutation.done = true
	return _node, nil
}

func (ac *ApplicationCreate) createSpec() (*Application, *sqlgraph.CreateSpec) {
	var (
		_node = &Application{config: ac.config}
		_spec = sqlgraph.NewCreateSpec(application.Table, sqlgraph.NewFieldSpec(application.FieldID, field.TypeString))
	)
	_spec.OnConflict = ac.conflict
	if id, ok := ac.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := ac.mutation.CreatedAt(); ok {
		_spec.SetField(application.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := ac.mutation.UpdatedAt(); ok {
		_spec.SetField(application.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := ac.mutation.Status(); ok {
		_spec.SetField(application.FieldStatus, field.TypeBool, value)
		_node.Status = value
	}
	if value, ok := ac.mutation.Sort(); ok {
		_spec.SetField(application.FieldSort, field.TypeUint32, value)
		_node.Sort = value
	}
	if value, ok := ac.mutation.DeletedAt(); ok {
		_spec.SetField(application.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := ac.mutation.Name(); ok {
		_spec.SetField(application.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := ac.mutation.AppID(); ok {
		_spec.SetField(application.FieldAppID, field.TypeString, value)
		_node.AppID = value
	}
	if value, ok := ac.mutation.Secret(); ok {
		_spec.SetField(application.FieldSecret, field.TypeString, value)
		_node.Secret = value
	}
	if value, ok := ac.mutation.Remark(); ok {
		_spec.SetField(application.FieldRemark, field.TypeString, value)
		_node.Remark = value
	}
	if nodes := ac.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   application.TenantTable,
			Columns: []string{application.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TenantID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Application.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.ApplicationUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (ac *ApplicationCreate) OnConflict(opts ...sql.ConflictOption) *ApplicationUpsertOne {
	ac.conflict = opts
	return &ApplicationUpsertOne{
		create: ac,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Application.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (ac *ApplicationCreate) OnConflictColumns(columns ...string) *ApplicationUpsertOne {
	ac.conflict = append(ac.conflict, sql.ConflictColumns(columns...))
	return &ApplicationUpsertOne{
		create: ac,
	}
}

type (
	// ApplicationUpsertOne is the builder for "upsert"-ing
	//  one Application node.
	ApplicationUpsertOne struct {
		create *ApplicationCreate
	}

	// ApplicationUpsert is the "OnConflict" setter.
	ApplicationUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *ApplicationUpsert) SetUpdatedAt(v time.Time) *ApplicationUpsert {
	u.Set(application.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *ApplicationUpsert) UpdateUpdatedAt() *ApplicationUpsert {
	u.SetExcluded(application.FieldUpdatedAt)
	return u
}

// SetStatus sets the "status" field.
func (u *ApplicationUpsert) SetStatus(v bool) *ApplicationUpsert {
	u.Set(application.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *ApplicationUpsert) UpdateStatus() *ApplicationUpsert {
	u.SetExcluded(application.FieldStatus)
	return u
}

// ClearStatus clears the value of the "status" field.
func (u *ApplicationUpsert) ClearStatus() *ApplicationUpsert {
	u.SetNull(application.FieldStatus)
	return u
}

// SetSort sets the "sort" field.
func (u *ApplicationUpsert) SetSort(v uint32) *ApplicationUpsert {
	u.Set(application.FieldSort, v)
	return u
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *ApplicationUpsert) UpdateSort() *ApplicationUpsert {
	u.SetExcluded(application.FieldSort)
	return u
}

// AddSort adds v to the "sort" field.
func (u *ApplicationUpsert) AddSort(v uint32) *ApplicationUpsert {
	u.Add(application.FieldSort, v)
	return u
}

// SetTenantID sets the "tenant_id" field.
func (u *ApplicationUpsert) SetTenantID(v string) *ApplicationUpsert {
	u.Set(application.FieldTenantID, v)
	return u
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *ApplicationUpsert) UpdateTenantID() *ApplicationUpsert {
	u.SetExcluded(application.FieldTenantID)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *ApplicationUpsert) SetDeletedAt(v time.Time) *ApplicationUpsert {
	u.Set(application.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *ApplicationUpsert) UpdateDeletedAt() *ApplicationUpsert {
	u.SetExcluded(application.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *ApplicationUpsert) ClearDeletedAt() *ApplicationUpsert {
	u.SetNull(application.FieldDeletedAt)
	return u
}

// SetName sets the "name" field.
func (u *ApplicationUpsert) SetName(v string) *ApplicationUpsert {
	u.Set(application.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *ApplicationUpsert) UpdateName() *ApplicationUpsert {
	u.SetExcluded(application.FieldName)
	return u
}

// SetSecret sets the "secret" field.
func (u *ApplicationUpsert) SetSecret(v string) *ApplicationUpsert {
	u.Set(application.FieldSecret, v)
	return u
}

// UpdateSecret sets the "secret" field to the value that was provided on create.
func (u *ApplicationUpsert) UpdateSecret() *ApplicationUpsert {
	u.SetExcluded(application.FieldSecret)
	return u
}

// SetRemark sets the "remark" field.
func (u *ApplicationUpsert) SetRemark(v string) *ApplicationUpsert {
	u.Set(application.FieldRemark, v)
	return u
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *ApplicationUpsert) UpdateRemark() *ApplicationUpsert {
	u.SetExcluded(application.FieldRemark)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Application.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(application.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *ApplicationUpsertOne) UpdateNewValues() *ApplicationUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(application.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(application.FieldCreatedAt)
		}
		if _, exists := u.create.mutation.AppID(); exists {
			s.SetIgnore(application.FieldAppID)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Application.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *ApplicationUpsertOne) Ignore() *ApplicationUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *ApplicationUpsertOne) DoNothing() *ApplicationUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the ApplicationCreate.OnConflict
// documentation for more info.
func (u *ApplicationUpsertOne) Update(set func(*ApplicationUpsert)) *ApplicationUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&ApplicationUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *ApplicationUpsertOne) SetUpdatedAt(v time.Time) *ApplicationUpsertOne {
	return u.Update(func(s *ApplicationUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *ApplicationUpsertOne) UpdateUpdatedAt() *ApplicationUpsertOne {
	return u.Update(func(s *ApplicationUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetStatus sets the "status" field.
func (u *ApplicationUpsertOne) SetStatus(v bool) *ApplicationUpsertOne {
	return u.Update(func(s *ApplicationUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *ApplicationUpsertOne) UpdateStatus() *ApplicationUpsertOne {
	return u.Update(func(s *ApplicationUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *ApplicationUpsertOne) ClearStatus() *ApplicationUpsertOne {
	return u.Update(func(s *ApplicationUpsert) {
		s.ClearStatus()
	})
}

// SetSort sets the "sort" field.
func (u *ApplicationUpsertOne) SetSort(v uint32) *ApplicationUpsertOne {
	return u.Update(func(s *ApplicationUpsert) {
		s.SetSort(v)
	})
}

// AddSort adds v to the "sort" field.
func (u *ApplicationUpsertOne) AddSort(v uint32) *ApplicationUpsertOne {
	return u.Update(func(s *ApplicationUpsert) {
		s.AddSort(v)
	})
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *ApplicationUpsertOne) UpdateSort() *ApplicationUpsertOne {
	return u.Update(func(s *ApplicationUpsert) {
		s.UpdateSort()
	})
}

// SetTenantID sets the "tenant_id" field.
func (u *ApplicationUpsertOne) SetTenantID(v string) *ApplicationUpsertOne {
	return u.Update(func(s *ApplicationUpsert) {
		s.SetTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *ApplicationUpsertOne) UpdateTenantID() *ApplicationUpsertOne {
	return u.Update(func(s *ApplicationUpsert) {
		s.UpdateTenantID()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *ApplicationUpsertOne) SetDeletedAt(v time.Time) *ApplicationUpsertOne {
	return u.Update(func(s *ApplicationUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *ApplicationUpsertOne) UpdateDeletedAt() *ApplicationUpsertOne {
	return u.Update(func(s *ApplicationUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *ApplicationUpsertOne) ClearDeletedAt() *ApplicationUpsertOne {
	return u.Update(func(s *ApplicationUpsert) {
		s.ClearDeletedAt()
	})
}

// SetName sets the "name" field.
func (u *ApplicationUpsertOne) SetName(v string) *ApplicationUpsertOne {
	return u.Update(func(s *ApplicationUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *ApplicationUpsertOne) UpdateName() *ApplicationUpsertOne {
	return u.Update(func(s *ApplicationUpsert) {
		s.UpdateName()
	})
}

// SetSecret sets the "secret" field.
func (u *ApplicationUpsertOne) SetSecret(v string) *ApplicationUpsertOne {
	return u.Update(func(s *ApplicationUpsert) {
		s.SetSecret(v)
	})
}

// UpdateSecret sets the "secret" field to the value that was provided on create.
func (u *ApplicationUpsertOne) UpdateSecret() *ApplicationUpsertOne {
	return u.Update(func(s *ApplicationUpsert) {
		s.UpdateSecret()
	})
}

// SetRemark sets the "remark" field.
func (u *ApplicationUpsertOne) SetRemark(v string) *ApplicationUpsertOne {
	return u.Update(func(s *ApplicationUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *ApplicationUpsertOne) UpdateRemark() *ApplicationUpsertOne {
	return u.Update(func(s *ApplicationUpsert) {
		s.UpdateRemark()
	})
}

// Exec executes the query.
func (u *ApplicationUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for ApplicationCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *ApplicationUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *ApplicationUpsertOne) ID(ctx context.Context) (id string, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: ApplicationUpsertOne.ID is not supported by MySQL driver. Use ApplicationUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *ApplicationUpsertOne) IDX(ctx context.Context) string {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// ApplicationCreateBulk is the builder for creating many Application entities in bulk.
type ApplicationCreateBulk struct {
	config
	err      error
	builders []*ApplicationCreate
	conflict []sql.ConflictOption
}

// Save creates the Application entities in the database.
func (acb *ApplicationCreateBulk) Save(ctx context.Context) ([]*Application, error) {
	if acb.err != nil {
		return nil, acb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(acb.builders))
	nodes := make([]*Application, len(acb.builders))
	mutators := make([]Mutator, len(acb.builders))
	for i := range acb.builders {
		func(i int, root context.Context) {
			builder := acb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*ApplicationMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, acb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = acb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, acb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, acb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (acb *ApplicationCreateBulk) SaveX(ctx context.Context) []*Application {
	v, err := acb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (acb *ApplicationCreateBulk) Exec(ctx context.Context) error {
	_, err := acb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (acb *ApplicationCreateBulk) ExecX(ctx context.Context) {
	if err := acb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Application.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.ApplicationUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (acb *ApplicationCreateBulk) OnConflict(opts ...sql.ConflictOption) *ApplicationUpsertBulk {
	acb.conflict = opts
	return &ApplicationUpsertBulk{
		create: acb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Application.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (acb *ApplicationCreateBulk) OnConflictColumns(columns ...string) *ApplicationUpsertBulk {
	acb.conflict = append(acb.conflict, sql.ConflictColumns(columns...))
	return &ApplicationUpsertBulk{
		create: acb,
	}
}

// ApplicationUpsertBulk is the builder for "upsert"-ing
// a bulk of Application nodes.
type ApplicationUpsertBulk struct {
	create *ApplicationCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Application.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(application.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *ApplicationUpsertBulk) UpdateNewValues() *ApplicationUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(application.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(application.FieldCreatedAt)
			}
			if _, exists := b.mutation.AppID(); exists {
				s.SetIgnore(application.FieldAppID)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Application.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *ApplicationUpsertBulk) Ignore() *ApplicationUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *ApplicationUpsertBulk) DoNothing() *ApplicationUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the ApplicationCreateBulk.OnConflict
// documentation for more info.
func (u *ApplicationUpsertBulk) Update(set func(*ApplicationUpsert)) *ApplicationUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&ApplicationUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *ApplicationUpsertBulk) SetUpdatedAt(v time.Time) *ApplicationUpsertBulk {
	return u.Update(func(s *ApplicationUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *ApplicationUpsertBulk) UpdateUpdatedAt() *ApplicationUpsertBulk {
	return u.Update(func(s *ApplicationUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetStatus sets the "status" field.
func (u *ApplicationUpsertBulk) SetStatus(v bool) *ApplicationUpsertBulk {
	return u.Update(func(s *ApplicationUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *ApplicationUpsertBulk) UpdateStatus() *ApplicationUpsertBulk {
	return u.Update(func(s *ApplicationUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *ApplicationUpsertBulk) ClearStatus() *ApplicationUpsertBulk {
	return u.Update(func(s *ApplicationUpsert) {
		s.ClearStatus()
	})
}

// SetSort sets the "sort" field.
func (u *ApplicationUpsertBulk) SetSort(v uint32) *ApplicationUpsertBulk {
	return u.Update(func(s *ApplicationUpsert) {
		s.SetSort(v)
	})
}

// AddSort adds v to the "sort" field.
func (u *ApplicationUpsertBulk) AddSort(v uint32) *ApplicationUpsertBulk {
	return u.Update(func(s *ApplicationUpsert) {
		s.AddSort(v)
	})
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *ApplicationUpsertBulk) UpdateSort() *ApplicationUpsertBulk {
	return u.Update(func(s *ApplicationUpsert) {
		s.UpdateSort()
	})
}

// SetTenantID sets the "tenant_id" field.
func (u *ApplicationUpsertBulk) SetTenantID(v string) *ApplicationUpsertBulk {
	return u.Update(func(s *ApplicationUpsert) {
		s.SetTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *ApplicationUpsertBulk) UpdateTenantID() *ApplicationUpsertBulk {
	return u.Update(func(s *ApplicationUpsert) {
		s.UpdateTenantID()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *ApplicationUpsertBulk) SetDeletedAt(v time.Time) *ApplicationUpsertBulk {
	return u.Update(func(s *ApplicationUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *ApplicationUpsertBulk) UpdateDeletedAt() *ApplicationUpsertBulk {
	return u.Update(func(s *ApplicationUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *ApplicationUpsertBulk) ClearDeletedAt() *ApplicationUpsertBulk {
	return u.Update(func(s *ApplicationUpsert) {
		s.ClearDeletedAt()
	})
}

// SetName sets the "name" field.
func (u *ApplicationUpsertBulk) SetName(v string) *ApplicationUpsertBulk {
	return u.Update(func(s *ApplicationUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *ApplicationUpsertBulk) UpdateName() *ApplicationUpsertBulk {
	return u.Update(func(s *ApplicationUpsert) {
		s.UpdateName()
	})
}

// SetSecret sets the "secret" field.
func (u *ApplicationUpsertBulk) SetSecret(v string) *ApplicationUpsertBulk {
	return u.Update(func(s *ApplicationUpsert) {
		s.SetSecret(v)
	})
}

// UpdateSecret sets the "secret" field to the value that was provided on create.
func (u *ApplicationUpsertBulk) UpdateSecret() *ApplicationUpsertBulk {
	return u.Update(func(s *ApplicationUpsert) {
		s.UpdateSecret()
	})
}

// SetRemark sets the "remark" field.
func (u *ApplicationUpsertBulk) SetRemark(v string) *ApplicationUpsertBulk {
	return u.Update(func(s *ApplicationUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *ApplicationUpsertBulk) UpdateRemark() *ApplicationUpsertBulk {
	return u.Update(func(s *ApplicationUpsert) {
		s.UpdateRemark()
	})
}

// Exec executes the query.
func (u *ApplicationUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the ApplicationCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for ApplicationCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *ApplicationUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
