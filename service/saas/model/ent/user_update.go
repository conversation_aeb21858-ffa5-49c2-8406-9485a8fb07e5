// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/enums"
	"phoenix/service/saas/model/ent/file"
	"phoenix/service/saas/model/ent/group"
	"phoenix/service/saas/model/ent/organization"
	"phoenix/service/saas/model/ent/organizationuserinfo"
	"phoenix/service/saas/model/ent/position"
	"phoenix/service/saas/model/ent/predicate"
	"phoenix/service/saas/model/ent/role"
	"phoenix/service/saas/model/ent/tenant"
	"phoenix/service/saas/model/ent/tenantuserinfo"
	"phoenix/service/saas/model/ent/user"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// UserUpdate is the builder for updating User entities.
type UserUpdate struct {
	config
	hooks    []Hook
	mutation *UserMutation
}

// Where appends a list predicates to the UserUpdate builder.
func (uu *UserUpdate) Where(ps ...predicate.User) *UserUpdate {
	uu.mutation.Where(ps...)
	return uu
}

// SetUpdatedAt sets the "updated_at" field.
func (uu *UserUpdate) SetUpdatedAt(t time.Time) *UserUpdate {
	uu.mutation.SetUpdatedAt(t)
	return uu
}

// SetStatus sets the "status" field.
func (uu *UserUpdate) SetStatus(b bool) *UserUpdate {
	uu.mutation.SetStatus(b)
	return uu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (uu *UserUpdate) SetNillableStatus(b *bool) *UserUpdate {
	if b != nil {
		uu.SetStatus(*b)
	}
	return uu
}

// ClearStatus clears the value of the "status" field.
func (uu *UserUpdate) ClearStatus() *UserUpdate {
	uu.mutation.ClearStatus()
	return uu
}

// SetDeletedAt sets the "deleted_at" field.
func (uu *UserUpdate) SetDeletedAt(t time.Time) *UserUpdate {
	uu.mutation.SetDeletedAt(t)
	return uu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (uu *UserUpdate) SetNillableDeletedAt(t *time.Time) *UserUpdate {
	if t != nil {
		uu.SetDeletedAt(*t)
	}
	return uu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (uu *UserUpdate) ClearDeletedAt() *UserUpdate {
	uu.mutation.ClearDeletedAt()
	return uu
}

// SetUsername sets the "username" field.
func (uu *UserUpdate) SetUsername(s string) *UserUpdate {
	uu.mutation.SetUsername(s)
	return uu
}

// SetNillableUsername sets the "username" field if the given value is not nil.
func (uu *UserUpdate) SetNillableUsername(s *string) *UserUpdate {
	if s != nil {
		uu.SetUsername(*s)
	}
	return uu
}

// SetPassword sets the "password" field.
func (uu *UserUpdate) SetPassword(s string) *UserUpdate {
	uu.mutation.SetPassword(s)
	return uu
}

// SetNillablePassword sets the "password" field if the given value is not nil.
func (uu *UserUpdate) SetNillablePassword(s *string) *UserUpdate {
	if s != nil {
		uu.SetPassword(*s)
	}
	return uu
}

// SetNickname sets the "nickname" field.
func (uu *UserUpdate) SetNickname(s string) *UserUpdate {
	uu.mutation.SetNickname(s)
	return uu
}

// SetNillableNickname sets the "nickname" field if the given value is not nil.
func (uu *UserUpdate) SetNillableNickname(s *string) *UserUpdate {
	if s != nil {
		uu.SetNickname(*s)
	}
	return uu
}

// ClearNickname clears the value of the "nickname" field.
func (uu *UserUpdate) ClearNickname() *UserUpdate {
	uu.mutation.ClearNickname()
	return uu
}

// SetMobile sets the "mobile" field.
func (uu *UserUpdate) SetMobile(s string) *UserUpdate {
	uu.mutation.SetMobile(s)
	return uu
}

// SetNillableMobile sets the "mobile" field if the given value is not nil.
func (uu *UserUpdate) SetNillableMobile(s *string) *UserUpdate {
	if s != nil {
		uu.SetMobile(*s)
	}
	return uu
}

// ClearMobile clears the value of the "mobile" field.
func (uu *UserUpdate) ClearMobile() *UserUpdate {
	uu.mutation.ClearMobile()
	return uu
}

// SetEmail sets the "email" field.
func (uu *UserUpdate) SetEmail(s string) *UserUpdate {
	uu.mutation.SetEmail(s)
	return uu
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (uu *UserUpdate) SetNillableEmail(s *string) *UserUpdate {
	if s != nil {
		uu.SetEmail(*s)
	}
	return uu
}

// ClearEmail clears the value of the "email" field.
func (uu *UserUpdate) ClearEmail() *UserUpdate {
	uu.mutation.ClearEmail()
	return uu
}

// SetGender sets the "gender" field.
func (uu *UserUpdate) SetGender(e enums.Gender) *UserUpdate {
	uu.mutation.SetGender(e)
	return uu
}

// SetNillableGender sets the "gender" field if the given value is not nil.
func (uu *UserUpdate) SetNillableGender(e *enums.Gender) *UserUpdate {
	if e != nil {
		uu.SetGender(*e)
	}
	return uu
}

// SetPost sets the "post" field.
func (uu *UserUpdate) SetPost(s string) *UserUpdate {
	uu.mutation.SetPost(s)
	return uu
}

// SetNillablePost sets the "post" field if the given value is not nil.
func (uu *UserUpdate) SetNillablePost(s *string) *UserUpdate {
	if s != nil {
		uu.SetPost(*s)
	}
	return uu
}

// ClearPost clears the value of the "post" field.
func (uu *UserUpdate) ClearPost() *UserUpdate {
	uu.mutation.ClearPost()
	return uu
}

// SetIsSuperuser sets the "is_superuser" field.
func (uu *UserUpdate) SetIsSuperuser(b bool) *UserUpdate {
	uu.mutation.SetIsSuperuser(b)
	return uu
}

// SetNillableIsSuperuser sets the "is_superuser" field if the given value is not nil.
func (uu *UserUpdate) SetNillableIsSuperuser(b *bool) *UserUpdate {
	if b != nil {
		uu.SetIsSuperuser(*b)
	}
	return uu
}

// SetDefaultTenantID sets the "default_tenant_id" field.
func (uu *UserUpdate) SetDefaultTenantID(s string) *UserUpdate {
	uu.mutation.SetDefaultTenantID(s)
	return uu
}

// SetNillableDefaultTenantID sets the "default_tenant_id" field if the given value is not nil.
func (uu *UserUpdate) SetNillableDefaultTenantID(s *string) *UserUpdate {
	if s != nil {
		uu.SetDefaultTenantID(*s)
	}
	return uu
}

// ClearDefaultTenantID clears the value of the "default_tenant_id" field.
func (uu *UserUpdate) ClearDefaultTenantID() *UserUpdate {
	uu.mutation.ClearDefaultTenantID()
	return uu
}

// SetAvatarID sets the "avatar_id" field.
func (uu *UserUpdate) SetAvatarID(s string) *UserUpdate {
	uu.mutation.SetAvatarID(s)
	return uu
}

// SetNillableAvatarID sets the "avatar_id" field if the given value is not nil.
func (uu *UserUpdate) SetNillableAvatarID(s *string) *UserUpdate {
	if s != nil {
		uu.SetAvatarID(*s)
	}
	return uu
}

// ClearAvatarID clears the value of the "avatar_id" field.
func (uu *UserUpdate) ClearAvatarID() *UserUpdate {
	uu.mutation.ClearAvatarID()
	return uu
}

// SetDeviceNo sets the "device_no" field.
func (uu *UserUpdate) SetDeviceNo(s string) *UserUpdate {
	uu.mutation.SetDeviceNo(s)
	return uu
}

// SetNillableDeviceNo sets the "device_no" field if the given value is not nil.
func (uu *UserUpdate) SetNillableDeviceNo(s *string) *UserUpdate {
	if s != nil {
		uu.SetDeviceNo(*s)
	}
	return uu
}

// ClearDeviceNo clears the value of the "device_no" field.
func (uu *UserUpdate) ClearDeviceNo() *UserUpdate {
	uu.mutation.ClearDeviceNo()
	return uu
}

// SetKind sets the "kind" field.
func (uu *UserUpdate) SetKind(s string) *UserUpdate {
	uu.mutation.SetKind(s)
	return uu
}

// SetNillableKind sets the "kind" field if the given value is not nil.
func (uu *UserUpdate) SetNillableKind(s *string) *UserUpdate {
	if s != nil {
		uu.SetKind(*s)
	}
	return uu
}

// ClearKind clears the value of the "kind" field.
func (uu *UserUpdate) ClearKind() *UserUpdate {
	uu.mutation.ClearKind()
	return uu
}

// SetImei sets the "imei" field.
func (uu *UserUpdate) SetImei(s string) *UserUpdate {
	uu.mutation.SetImei(s)
	return uu
}

// SetNillableImei sets the "imei" field if the given value is not nil.
func (uu *UserUpdate) SetNillableImei(s *string) *UserUpdate {
	if s != nil {
		uu.SetImei(*s)
	}
	return uu
}

// AddTenantIDs adds the "tenants" edge to the Tenant entity by IDs.
func (uu *UserUpdate) AddTenantIDs(ids ...string) *UserUpdate {
	uu.mutation.AddTenantIDs(ids...)
	return uu
}

// AddTenants adds the "tenants" edges to the Tenant entity.
func (uu *UserUpdate) AddTenants(t ...*Tenant) *UserUpdate {
	ids := make([]string, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return uu.AddTenantIDs(ids...)
}

// AddOrganizationIDs adds the "organizations" edge to the Organization entity by IDs.
func (uu *UserUpdate) AddOrganizationIDs(ids ...string) *UserUpdate {
	uu.mutation.AddOrganizationIDs(ids...)
	return uu
}

// AddOrganizations adds the "organizations" edges to the Organization entity.
func (uu *UserUpdate) AddOrganizations(o ...*Organization) *UserUpdate {
	ids := make([]string, len(o))
	for i := range o {
		ids[i] = o[i].ID
	}
	return uu.AddOrganizationIDs(ids...)
}

// AddPositionIDs adds the "positions" edge to the Position entity by IDs.
func (uu *UserUpdate) AddPositionIDs(ids ...string) *UserUpdate {
	uu.mutation.AddPositionIDs(ids...)
	return uu
}

// AddPositions adds the "positions" edges to the Position entity.
func (uu *UserUpdate) AddPositions(p ...*Position) *UserUpdate {
	ids := make([]string, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return uu.AddPositionIDs(ids...)
}

// AddGroupIDs adds the "groups" edge to the Group entity by IDs.
func (uu *UserUpdate) AddGroupIDs(ids ...string) *UserUpdate {
	uu.mutation.AddGroupIDs(ids...)
	return uu
}

// AddGroups adds the "groups" edges to the Group entity.
func (uu *UserUpdate) AddGroups(g ...*Group) *UserUpdate {
	ids := make([]string, len(g))
	for i := range g {
		ids[i] = g[i].ID
	}
	return uu.AddGroupIDs(ids...)
}

// AddRoleIDs adds the "roles" edge to the Role entity by IDs.
func (uu *UserUpdate) AddRoleIDs(ids ...string) *UserUpdate {
	uu.mutation.AddRoleIDs(ids...)
	return uu
}

// AddRoles adds the "roles" edges to the Role entity.
func (uu *UserUpdate) AddRoles(r ...*Role) *UserUpdate {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return uu.AddRoleIDs(ids...)
}

// AddOrganizationInfoIDs adds the "organization_infos" edge to the OrganizationUserInfo entity by IDs.
func (uu *UserUpdate) AddOrganizationInfoIDs(ids ...string) *UserUpdate {
	uu.mutation.AddOrganizationInfoIDs(ids...)
	return uu
}

// AddOrganizationInfos adds the "organization_infos" edges to the OrganizationUserInfo entity.
func (uu *UserUpdate) AddOrganizationInfos(o ...*OrganizationUserInfo) *UserUpdate {
	ids := make([]string, len(o))
	for i := range o {
		ids[i] = o[i].ID
	}
	return uu.AddOrganizationInfoIDs(ids...)
}

// AddTenantInfoIDs adds the "tenant_infos" edge to the TenantUserInfo entity by IDs.
func (uu *UserUpdate) AddTenantInfoIDs(ids ...string) *UserUpdate {
	uu.mutation.AddTenantInfoIDs(ids...)
	return uu
}

// AddTenantInfos adds the "tenant_infos" edges to the TenantUserInfo entity.
func (uu *UserUpdate) AddTenantInfos(t ...*TenantUserInfo) *UserUpdate {
	ids := make([]string, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return uu.AddTenantInfoIDs(ids...)
}

// AddFileIDs adds the "files" edge to the File entity by IDs.
func (uu *UserUpdate) AddFileIDs(ids ...string) *UserUpdate {
	uu.mutation.AddFileIDs(ids...)
	return uu
}

// AddFiles adds the "files" edges to the File entity.
func (uu *UserUpdate) AddFiles(f ...*File) *UserUpdate {
	ids := make([]string, len(f))
	for i := range f {
		ids[i] = f[i].ID
	}
	return uu.AddFileIDs(ids...)
}

// SetAvatar sets the "avatar" edge to the File entity.
func (uu *UserUpdate) SetAvatar(f *File) *UserUpdate {
	return uu.SetAvatarID(f.ID)
}

// Mutation returns the UserMutation object of the builder.
func (uu *UserUpdate) Mutation() *UserMutation {
	return uu.mutation
}

// ClearTenants clears all "tenants" edges to the Tenant entity.
func (uu *UserUpdate) ClearTenants() *UserUpdate {
	uu.mutation.ClearTenants()
	return uu
}

// RemoveTenantIDs removes the "tenants" edge to Tenant entities by IDs.
func (uu *UserUpdate) RemoveTenantIDs(ids ...string) *UserUpdate {
	uu.mutation.RemoveTenantIDs(ids...)
	return uu
}

// RemoveTenants removes "tenants" edges to Tenant entities.
func (uu *UserUpdate) RemoveTenants(t ...*Tenant) *UserUpdate {
	ids := make([]string, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return uu.RemoveTenantIDs(ids...)
}

// ClearOrganizations clears all "organizations" edges to the Organization entity.
func (uu *UserUpdate) ClearOrganizations() *UserUpdate {
	uu.mutation.ClearOrganizations()
	return uu
}

// RemoveOrganizationIDs removes the "organizations" edge to Organization entities by IDs.
func (uu *UserUpdate) RemoveOrganizationIDs(ids ...string) *UserUpdate {
	uu.mutation.RemoveOrganizationIDs(ids...)
	return uu
}

// RemoveOrganizations removes "organizations" edges to Organization entities.
func (uu *UserUpdate) RemoveOrganizations(o ...*Organization) *UserUpdate {
	ids := make([]string, len(o))
	for i := range o {
		ids[i] = o[i].ID
	}
	return uu.RemoveOrganizationIDs(ids...)
}

// ClearPositions clears all "positions" edges to the Position entity.
func (uu *UserUpdate) ClearPositions() *UserUpdate {
	uu.mutation.ClearPositions()
	return uu
}

// RemovePositionIDs removes the "positions" edge to Position entities by IDs.
func (uu *UserUpdate) RemovePositionIDs(ids ...string) *UserUpdate {
	uu.mutation.RemovePositionIDs(ids...)
	return uu
}

// RemovePositions removes "positions" edges to Position entities.
func (uu *UserUpdate) RemovePositions(p ...*Position) *UserUpdate {
	ids := make([]string, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return uu.RemovePositionIDs(ids...)
}

// ClearGroups clears all "groups" edges to the Group entity.
func (uu *UserUpdate) ClearGroups() *UserUpdate {
	uu.mutation.ClearGroups()
	return uu
}

// RemoveGroupIDs removes the "groups" edge to Group entities by IDs.
func (uu *UserUpdate) RemoveGroupIDs(ids ...string) *UserUpdate {
	uu.mutation.RemoveGroupIDs(ids...)
	return uu
}

// RemoveGroups removes "groups" edges to Group entities.
func (uu *UserUpdate) RemoveGroups(g ...*Group) *UserUpdate {
	ids := make([]string, len(g))
	for i := range g {
		ids[i] = g[i].ID
	}
	return uu.RemoveGroupIDs(ids...)
}

// ClearRoles clears all "roles" edges to the Role entity.
func (uu *UserUpdate) ClearRoles() *UserUpdate {
	uu.mutation.ClearRoles()
	return uu
}

// RemoveRoleIDs removes the "roles" edge to Role entities by IDs.
func (uu *UserUpdate) RemoveRoleIDs(ids ...string) *UserUpdate {
	uu.mutation.RemoveRoleIDs(ids...)
	return uu
}

// RemoveRoles removes "roles" edges to Role entities.
func (uu *UserUpdate) RemoveRoles(r ...*Role) *UserUpdate {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return uu.RemoveRoleIDs(ids...)
}

// ClearOrganizationInfos clears all "organization_infos" edges to the OrganizationUserInfo entity.
func (uu *UserUpdate) ClearOrganizationInfos() *UserUpdate {
	uu.mutation.ClearOrganizationInfos()
	return uu
}

// RemoveOrganizationInfoIDs removes the "organization_infos" edge to OrganizationUserInfo entities by IDs.
func (uu *UserUpdate) RemoveOrganizationInfoIDs(ids ...string) *UserUpdate {
	uu.mutation.RemoveOrganizationInfoIDs(ids...)
	return uu
}

// RemoveOrganizationInfos removes "organization_infos" edges to OrganizationUserInfo entities.
func (uu *UserUpdate) RemoveOrganizationInfos(o ...*OrganizationUserInfo) *UserUpdate {
	ids := make([]string, len(o))
	for i := range o {
		ids[i] = o[i].ID
	}
	return uu.RemoveOrganizationInfoIDs(ids...)
}

// ClearTenantInfos clears all "tenant_infos" edges to the TenantUserInfo entity.
func (uu *UserUpdate) ClearTenantInfos() *UserUpdate {
	uu.mutation.ClearTenantInfos()
	return uu
}

// RemoveTenantInfoIDs removes the "tenant_infos" edge to TenantUserInfo entities by IDs.
func (uu *UserUpdate) RemoveTenantInfoIDs(ids ...string) *UserUpdate {
	uu.mutation.RemoveTenantInfoIDs(ids...)
	return uu
}

// RemoveTenantInfos removes "tenant_infos" edges to TenantUserInfo entities.
func (uu *UserUpdate) RemoveTenantInfos(t ...*TenantUserInfo) *UserUpdate {
	ids := make([]string, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return uu.RemoveTenantInfoIDs(ids...)
}

// ClearFiles clears all "files" edges to the File entity.
func (uu *UserUpdate) ClearFiles() *UserUpdate {
	uu.mutation.ClearFiles()
	return uu
}

// RemoveFileIDs removes the "files" edge to File entities by IDs.
func (uu *UserUpdate) RemoveFileIDs(ids ...string) *UserUpdate {
	uu.mutation.RemoveFileIDs(ids...)
	return uu
}

// RemoveFiles removes "files" edges to File entities.
func (uu *UserUpdate) RemoveFiles(f ...*File) *UserUpdate {
	ids := make([]string, len(f))
	for i := range f {
		ids[i] = f[i].ID
	}
	return uu.RemoveFileIDs(ids...)
}

// ClearAvatar clears the "avatar" edge to the File entity.
func (uu *UserUpdate) ClearAvatar() *UserUpdate {
	uu.mutation.ClearAvatar()
	return uu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (uu *UserUpdate) Save(ctx context.Context) (int, error) {
	if err := uu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, uu.sqlSave, uu.mutation, uu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (uu *UserUpdate) SaveX(ctx context.Context) int {
	affected, err := uu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (uu *UserUpdate) Exec(ctx context.Context) error {
	_, err := uu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uu *UserUpdate) ExecX(ctx context.Context) {
	if err := uu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uu *UserUpdate) defaults() error {
	if _, ok := uu.mutation.UpdatedAt(); !ok {
		if user.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized user.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := user.UpdateDefaultUpdatedAt()
		uu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (uu *UserUpdate) check() error {
	if v, ok := uu.mutation.Gender(); ok {
		if err := user.GenderValidator(v); err != nil {
			return &ValidationError{Name: "gender", err: fmt.Errorf(`ent: validator failed for field "User.gender": %w`, err)}
		}
	}
	return nil
}

func (uu *UserUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := uu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(user.Table, user.Columns, sqlgraph.NewFieldSpec(user.FieldID, field.TypeString))
	if ps := uu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := uu.mutation.UpdatedAt(); ok {
		_spec.SetField(user.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := uu.mutation.Status(); ok {
		_spec.SetField(user.FieldStatus, field.TypeBool, value)
	}
	if uu.mutation.StatusCleared() {
		_spec.ClearField(user.FieldStatus, field.TypeBool)
	}
	if value, ok := uu.mutation.DeletedAt(); ok {
		_spec.SetField(user.FieldDeletedAt, field.TypeTime, value)
	}
	if uu.mutation.DeletedAtCleared() {
		_spec.ClearField(user.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := uu.mutation.Username(); ok {
		_spec.SetField(user.FieldUsername, field.TypeString, value)
	}
	if value, ok := uu.mutation.Password(); ok {
		_spec.SetField(user.FieldPassword, field.TypeString, value)
	}
	if value, ok := uu.mutation.Nickname(); ok {
		_spec.SetField(user.FieldNickname, field.TypeString, value)
	}
	if uu.mutation.NicknameCleared() {
		_spec.ClearField(user.FieldNickname, field.TypeString)
	}
	if value, ok := uu.mutation.Mobile(); ok {
		_spec.SetField(user.FieldMobile, field.TypeString, value)
	}
	if uu.mutation.MobileCleared() {
		_spec.ClearField(user.FieldMobile, field.TypeString)
	}
	if value, ok := uu.mutation.Email(); ok {
		_spec.SetField(user.FieldEmail, field.TypeString, value)
	}
	if uu.mutation.EmailCleared() {
		_spec.ClearField(user.FieldEmail, field.TypeString)
	}
	if value, ok := uu.mutation.Gender(); ok {
		_spec.SetField(user.FieldGender, field.TypeEnum, value)
	}
	if value, ok := uu.mutation.Post(); ok {
		_spec.SetField(user.FieldPost, field.TypeString, value)
	}
	if uu.mutation.PostCleared() {
		_spec.ClearField(user.FieldPost, field.TypeString)
	}
	if value, ok := uu.mutation.IsSuperuser(); ok {
		_spec.SetField(user.FieldIsSuperuser, field.TypeBool, value)
	}
	if value, ok := uu.mutation.DefaultTenantID(); ok {
		_spec.SetField(user.FieldDefaultTenantID, field.TypeString, value)
	}
	if uu.mutation.DefaultTenantIDCleared() {
		_spec.ClearField(user.FieldDefaultTenantID, field.TypeString)
	}
	if value, ok := uu.mutation.DeviceNo(); ok {
		_spec.SetField(user.FieldDeviceNo, field.TypeString, value)
	}
	if uu.mutation.DeviceNoCleared() {
		_spec.ClearField(user.FieldDeviceNo, field.TypeString)
	}
	if value, ok := uu.mutation.Kind(); ok {
		_spec.SetField(user.FieldKind, field.TypeString, value)
	}
	if uu.mutation.KindCleared() {
		_spec.ClearField(user.FieldKind, field.TypeString)
	}
	if value, ok := uu.mutation.Imei(); ok {
		_spec.SetField(user.FieldImei, field.TypeString, value)
	}
	if uu.mutation.TenantsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   user.TenantsTable,
			Columns: user.TenantsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedTenantsIDs(); len(nodes) > 0 && !uu.mutation.TenantsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   user.TenantsTable,
			Columns: user.TenantsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.TenantsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   user.TenantsTable,
			Columns: user.TenantsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.OrganizationsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   user.OrganizationsTable,
			Columns: user.OrganizationsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedOrganizationsIDs(); len(nodes) > 0 && !uu.mutation.OrganizationsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   user.OrganizationsTable,
			Columns: user.OrganizationsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.OrganizationsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   user.OrganizationsTable,
			Columns: user.OrganizationsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.PositionsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   user.PositionsTable,
			Columns: user.PositionsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(position.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedPositionsIDs(); len(nodes) > 0 && !uu.mutation.PositionsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   user.PositionsTable,
			Columns: user.PositionsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(position.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.PositionsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   user.PositionsTable,
			Columns: user.PositionsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(position.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.GroupsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   user.GroupsTable,
			Columns: user.GroupsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(group.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedGroupsIDs(); len(nodes) > 0 && !uu.mutation.GroupsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   user.GroupsTable,
			Columns: user.GroupsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(group.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.GroupsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   user.GroupsTable,
			Columns: user.GroupsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(group.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.RolesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   user.RolesTable,
			Columns: user.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedRolesIDs(); len(nodes) > 0 && !uu.mutation.RolesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   user.RolesTable,
			Columns: user.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RolesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   user.RolesTable,
			Columns: user.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.OrganizationInfosCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.OrganizationInfosTable,
			Columns: []string{user.OrganizationInfosColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organizationuserinfo.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedOrganizationInfosIDs(); len(nodes) > 0 && !uu.mutation.OrganizationInfosCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.OrganizationInfosTable,
			Columns: []string{user.OrganizationInfosColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organizationuserinfo.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.OrganizationInfosIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.OrganizationInfosTable,
			Columns: []string{user.OrganizationInfosColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organizationuserinfo.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.TenantInfosCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.TenantInfosTable,
			Columns: []string{user.TenantInfosColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenantuserinfo.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedTenantInfosIDs(); len(nodes) > 0 && !uu.mutation.TenantInfosCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.TenantInfosTable,
			Columns: []string{user.TenantInfosColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenantuserinfo.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.TenantInfosIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.TenantInfosTable,
			Columns: []string{user.TenantInfosColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenantuserinfo.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.FilesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.FilesTable,
			Columns: []string{user.FilesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(file.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.RemovedFilesIDs(); len(nodes) > 0 && !uu.mutation.FilesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.FilesTable,
			Columns: []string{user.FilesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(file.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.FilesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.FilesTable,
			Columns: []string{user.FilesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(file.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uu.mutation.AvatarCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   user.AvatarTable,
			Columns: []string{user.AvatarColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(file.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uu.mutation.AvatarIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   user.AvatarTable,
			Columns: []string{user.AvatarColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(file.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, uu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{user.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	uu.mutation.done = true
	return n, nil
}

// UserUpdateOne is the builder for updating a single User entity.
type UserUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *UserMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (uuo *UserUpdateOne) SetUpdatedAt(t time.Time) *UserUpdateOne {
	uuo.mutation.SetUpdatedAt(t)
	return uuo
}

// SetStatus sets the "status" field.
func (uuo *UserUpdateOne) SetStatus(b bool) *UserUpdateOne {
	uuo.mutation.SetStatus(b)
	return uuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableStatus(b *bool) *UserUpdateOne {
	if b != nil {
		uuo.SetStatus(*b)
	}
	return uuo
}

// ClearStatus clears the value of the "status" field.
func (uuo *UserUpdateOne) ClearStatus() *UserUpdateOne {
	uuo.mutation.ClearStatus()
	return uuo
}

// SetDeletedAt sets the "deleted_at" field.
func (uuo *UserUpdateOne) SetDeletedAt(t time.Time) *UserUpdateOne {
	uuo.mutation.SetDeletedAt(t)
	return uuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableDeletedAt(t *time.Time) *UserUpdateOne {
	if t != nil {
		uuo.SetDeletedAt(*t)
	}
	return uuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (uuo *UserUpdateOne) ClearDeletedAt() *UserUpdateOne {
	uuo.mutation.ClearDeletedAt()
	return uuo
}

// SetUsername sets the "username" field.
func (uuo *UserUpdateOne) SetUsername(s string) *UserUpdateOne {
	uuo.mutation.SetUsername(s)
	return uuo
}

// SetNillableUsername sets the "username" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableUsername(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetUsername(*s)
	}
	return uuo
}

// SetPassword sets the "password" field.
func (uuo *UserUpdateOne) SetPassword(s string) *UserUpdateOne {
	uuo.mutation.SetPassword(s)
	return uuo
}

// SetNillablePassword sets the "password" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillablePassword(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetPassword(*s)
	}
	return uuo
}

// SetNickname sets the "nickname" field.
func (uuo *UserUpdateOne) SetNickname(s string) *UserUpdateOne {
	uuo.mutation.SetNickname(s)
	return uuo
}

// SetNillableNickname sets the "nickname" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableNickname(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetNickname(*s)
	}
	return uuo
}

// ClearNickname clears the value of the "nickname" field.
func (uuo *UserUpdateOne) ClearNickname() *UserUpdateOne {
	uuo.mutation.ClearNickname()
	return uuo
}

// SetMobile sets the "mobile" field.
func (uuo *UserUpdateOne) SetMobile(s string) *UserUpdateOne {
	uuo.mutation.SetMobile(s)
	return uuo
}

// SetNillableMobile sets the "mobile" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableMobile(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetMobile(*s)
	}
	return uuo
}

// ClearMobile clears the value of the "mobile" field.
func (uuo *UserUpdateOne) ClearMobile() *UserUpdateOne {
	uuo.mutation.ClearMobile()
	return uuo
}

// SetEmail sets the "email" field.
func (uuo *UserUpdateOne) SetEmail(s string) *UserUpdateOne {
	uuo.mutation.SetEmail(s)
	return uuo
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableEmail(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetEmail(*s)
	}
	return uuo
}

// ClearEmail clears the value of the "email" field.
func (uuo *UserUpdateOne) ClearEmail() *UserUpdateOne {
	uuo.mutation.ClearEmail()
	return uuo
}

// SetGender sets the "gender" field.
func (uuo *UserUpdateOne) SetGender(e enums.Gender) *UserUpdateOne {
	uuo.mutation.SetGender(e)
	return uuo
}

// SetNillableGender sets the "gender" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableGender(e *enums.Gender) *UserUpdateOne {
	if e != nil {
		uuo.SetGender(*e)
	}
	return uuo
}

// SetPost sets the "post" field.
func (uuo *UserUpdateOne) SetPost(s string) *UserUpdateOne {
	uuo.mutation.SetPost(s)
	return uuo
}

// SetNillablePost sets the "post" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillablePost(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetPost(*s)
	}
	return uuo
}

// ClearPost clears the value of the "post" field.
func (uuo *UserUpdateOne) ClearPost() *UserUpdateOne {
	uuo.mutation.ClearPost()
	return uuo
}

// SetIsSuperuser sets the "is_superuser" field.
func (uuo *UserUpdateOne) SetIsSuperuser(b bool) *UserUpdateOne {
	uuo.mutation.SetIsSuperuser(b)
	return uuo
}

// SetNillableIsSuperuser sets the "is_superuser" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableIsSuperuser(b *bool) *UserUpdateOne {
	if b != nil {
		uuo.SetIsSuperuser(*b)
	}
	return uuo
}

// SetDefaultTenantID sets the "default_tenant_id" field.
func (uuo *UserUpdateOne) SetDefaultTenantID(s string) *UserUpdateOne {
	uuo.mutation.SetDefaultTenantID(s)
	return uuo
}

// SetNillableDefaultTenantID sets the "default_tenant_id" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableDefaultTenantID(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetDefaultTenantID(*s)
	}
	return uuo
}

// ClearDefaultTenantID clears the value of the "default_tenant_id" field.
func (uuo *UserUpdateOne) ClearDefaultTenantID() *UserUpdateOne {
	uuo.mutation.ClearDefaultTenantID()
	return uuo
}

// SetAvatarID sets the "avatar_id" field.
func (uuo *UserUpdateOne) SetAvatarID(s string) *UserUpdateOne {
	uuo.mutation.SetAvatarID(s)
	return uuo
}

// SetNillableAvatarID sets the "avatar_id" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableAvatarID(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetAvatarID(*s)
	}
	return uuo
}

// ClearAvatarID clears the value of the "avatar_id" field.
func (uuo *UserUpdateOne) ClearAvatarID() *UserUpdateOne {
	uuo.mutation.ClearAvatarID()
	return uuo
}

// SetDeviceNo sets the "device_no" field.
func (uuo *UserUpdateOne) SetDeviceNo(s string) *UserUpdateOne {
	uuo.mutation.SetDeviceNo(s)
	return uuo
}

// SetNillableDeviceNo sets the "device_no" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableDeviceNo(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetDeviceNo(*s)
	}
	return uuo
}

// ClearDeviceNo clears the value of the "device_no" field.
func (uuo *UserUpdateOne) ClearDeviceNo() *UserUpdateOne {
	uuo.mutation.ClearDeviceNo()
	return uuo
}

// SetKind sets the "kind" field.
func (uuo *UserUpdateOne) SetKind(s string) *UserUpdateOne {
	uuo.mutation.SetKind(s)
	return uuo
}

// SetNillableKind sets the "kind" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableKind(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetKind(*s)
	}
	return uuo
}

// ClearKind clears the value of the "kind" field.
func (uuo *UserUpdateOne) ClearKind() *UserUpdateOne {
	uuo.mutation.ClearKind()
	return uuo
}

// SetImei sets the "imei" field.
func (uuo *UserUpdateOne) SetImei(s string) *UserUpdateOne {
	uuo.mutation.SetImei(s)
	return uuo
}

// SetNillableImei sets the "imei" field if the given value is not nil.
func (uuo *UserUpdateOne) SetNillableImei(s *string) *UserUpdateOne {
	if s != nil {
		uuo.SetImei(*s)
	}
	return uuo
}

// AddTenantIDs adds the "tenants" edge to the Tenant entity by IDs.
func (uuo *UserUpdateOne) AddTenantIDs(ids ...string) *UserUpdateOne {
	uuo.mutation.AddTenantIDs(ids...)
	return uuo
}

// AddTenants adds the "tenants" edges to the Tenant entity.
func (uuo *UserUpdateOne) AddTenants(t ...*Tenant) *UserUpdateOne {
	ids := make([]string, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return uuo.AddTenantIDs(ids...)
}

// AddOrganizationIDs adds the "organizations" edge to the Organization entity by IDs.
func (uuo *UserUpdateOne) AddOrganizationIDs(ids ...string) *UserUpdateOne {
	uuo.mutation.AddOrganizationIDs(ids...)
	return uuo
}

// AddOrganizations adds the "organizations" edges to the Organization entity.
func (uuo *UserUpdateOne) AddOrganizations(o ...*Organization) *UserUpdateOne {
	ids := make([]string, len(o))
	for i := range o {
		ids[i] = o[i].ID
	}
	return uuo.AddOrganizationIDs(ids...)
}

// AddPositionIDs adds the "positions" edge to the Position entity by IDs.
func (uuo *UserUpdateOne) AddPositionIDs(ids ...string) *UserUpdateOne {
	uuo.mutation.AddPositionIDs(ids...)
	return uuo
}

// AddPositions adds the "positions" edges to the Position entity.
func (uuo *UserUpdateOne) AddPositions(p ...*Position) *UserUpdateOne {
	ids := make([]string, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return uuo.AddPositionIDs(ids...)
}

// AddGroupIDs adds the "groups" edge to the Group entity by IDs.
func (uuo *UserUpdateOne) AddGroupIDs(ids ...string) *UserUpdateOne {
	uuo.mutation.AddGroupIDs(ids...)
	return uuo
}

// AddGroups adds the "groups" edges to the Group entity.
func (uuo *UserUpdateOne) AddGroups(g ...*Group) *UserUpdateOne {
	ids := make([]string, len(g))
	for i := range g {
		ids[i] = g[i].ID
	}
	return uuo.AddGroupIDs(ids...)
}

// AddRoleIDs adds the "roles" edge to the Role entity by IDs.
func (uuo *UserUpdateOne) AddRoleIDs(ids ...string) *UserUpdateOne {
	uuo.mutation.AddRoleIDs(ids...)
	return uuo
}

// AddRoles adds the "roles" edges to the Role entity.
func (uuo *UserUpdateOne) AddRoles(r ...*Role) *UserUpdateOne {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return uuo.AddRoleIDs(ids...)
}

// AddOrganizationInfoIDs adds the "organization_infos" edge to the OrganizationUserInfo entity by IDs.
func (uuo *UserUpdateOne) AddOrganizationInfoIDs(ids ...string) *UserUpdateOne {
	uuo.mutation.AddOrganizationInfoIDs(ids...)
	return uuo
}

// AddOrganizationInfos adds the "organization_infos" edges to the OrganizationUserInfo entity.
func (uuo *UserUpdateOne) AddOrganizationInfos(o ...*OrganizationUserInfo) *UserUpdateOne {
	ids := make([]string, len(o))
	for i := range o {
		ids[i] = o[i].ID
	}
	return uuo.AddOrganizationInfoIDs(ids...)
}

// AddTenantInfoIDs adds the "tenant_infos" edge to the TenantUserInfo entity by IDs.
func (uuo *UserUpdateOne) AddTenantInfoIDs(ids ...string) *UserUpdateOne {
	uuo.mutation.AddTenantInfoIDs(ids...)
	return uuo
}

// AddTenantInfos adds the "tenant_infos" edges to the TenantUserInfo entity.
func (uuo *UserUpdateOne) AddTenantInfos(t ...*TenantUserInfo) *UserUpdateOne {
	ids := make([]string, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return uuo.AddTenantInfoIDs(ids...)
}

// AddFileIDs adds the "files" edge to the File entity by IDs.
func (uuo *UserUpdateOne) AddFileIDs(ids ...string) *UserUpdateOne {
	uuo.mutation.AddFileIDs(ids...)
	return uuo
}

// AddFiles adds the "files" edges to the File entity.
func (uuo *UserUpdateOne) AddFiles(f ...*File) *UserUpdateOne {
	ids := make([]string, len(f))
	for i := range f {
		ids[i] = f[i].ID
	}
	return uuo.AddFileIDs(ids...)
}

// SetAvatar sets the "avatar" edge to the File entity.
func (uuo *UserUpdateOne) SetAvatar(f *File) *UserUpdateOne {
	return uuo.SetAvatarID(f.ID)
}

// Mutation returns the UserMutation object of the builder.
func (uuo *UserUpdateOne) Mutation() *UserMutation {
	return uuo.mutation
}

// ClearTenants clears all "tenants" edges to the Tenant entity.
func (uuo *UserUpdateOne) ClearTenants() *UserUpdateOne {
	uuo.mutation.ClearTenants()
	return uuo
}

// RemoveTenantIDs removes the "tenants" edge to Tenant entities by IDs.
func (uuo *UserUpdateOne) RemoveTenantIDs(ids ...string) *UserUpdateOne {
	uuo.mutation.RemoveTenantIDs(ids...)
	return uuo
}

// RemoveTenants removes "tenants" edges to Tenant entities.
func (uuo *UserUpdateOne) RemoveTenants(t ...*Tenant) *UserUpdateOne {
	ids := make([]string, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return uuo.RemoveTenantIDs(ids...)
}

// ClearOrganizations clears all "organizations" edges to the Organization entity.
func (uuo *UserUpdateOne) ClearOrganizations() *UserUpdateOne {
	uuo.mutation.ClearOrganizations()
	return uuo
}

// RemoveOrganizationIDs removes the "organizations" edge to Organization entities by IDs.
func (uuo *UserUpdateOne) RemoveOrganizationIDs(ids ...string) *UserUpdateOne {
	uuo.mutation.RemoveOrganizationIDs(ids...)
	return uuo
}

// RemoveOrganizations removes "organizations" edges to Organization entities.
func (uuo *UserUpdateOne) RemoveOrganizations(o ...*Organization) *UserUpdateOne {
	ids := make([]string, len(o))
	for i := range o {
		ids[i] = o[i].ID
	}
	return uuo.RemoveOrganizationIDs(ids...)
}

// ClearPositions clears all "positions" edges to the Position entity.
func (uuo *UserUpdateOne) ClearPositions() *UserUpdateOne {
	uuo.mutation.ClearPositions()
	return uuo
}

// RemovePositionIDs removes the "positions" edge to Position entities by IDs.
func (uuo *UserUpdateOne) RemovePositionIDs(ids ...string) *UserUpdateOne {
	uuo.mutation.RemovePositionIDs(ids...)
	return uuo
}

// RemovePositions removes "positions" edges to Position entities.
func (uuo *UserUpdateOne) RemovePositions(p ...*Position) *UserUpdateOne {
	ids := make([]string, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return uuo.RemovePositionIDs(ids...)
}

// ClearGroups clears all "groups" edges to the Group entity.
func (uuo *UserUpdateOne) ClearGroups() *UserUpdateOne {
	uuo.mutation.ClearGroups()
	return uuo
}

// RemoveGroupIDs removes the "groups" edge to Group entities by IDs.
func (uuo *UserUpdateOne) RemoveGroupIDs(ids ...string) *UserUpdateOne {
	uuo.mutation.RemoveGroupIDs(ids...)
	return uuo
}

// RemoveGroups removes "groups" edges to Group entities.
func (uuo *UserUpdateOne) RemoveGroups(g ...*Group) *UserUpdateOne {
	ids := make([]string, len(g))
	for i := range g {
		ids[i] = g[i].ID
	}
	return uuo.RemoveGroupIDs(ids...)
}

// ClearRoles clears all "roles" edges to the Role entity.
func (uuo *UserUpdateOne) ClearRoles() *UserUpdateOne {
	uuo.mutation.ClearRoles()
	return uuo
}

// RemoveRoleIDs removes the "roles" edge to Role entities by IDs.
func (uuo *UserUpdateOne) RemoveRoleIDs(ids ...string) *UserUpdateOne {
	uuo.mutation.RemoveRoleIDs(ids...)
	return uuo
}

// RemoveRoles removes "roles" edges to Role entities.
func (uuo *UserUpdateOne) RemoveRoles(r ...*Role) *UserUpdateOne {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return uuo.RemoveRoleIDs(ids...)
}

// ClearOrganizationInfos clears all "organization_infos" edges to the OrganizationUserInfo entity.
func (uuo *UserUpdateOne) ClearOrganizationInfos() *UserUpdateOne {
	uuo.mutation.ClearOrganizationInfos()
	return uuo
}

// RemoveOrganizationInfoIDs removes the "organization_infos" edge to OrganizationUserInfo entities by IDs.
func (uuo *UserUpdateOne) RemoveOrganizationInfoIDs(ids ...string) *UserUpdateOne {
	uuo.mutation.RemoveOrganizationInfoIDs(ids...)
	return uuo
}

// RemoveOrganizationInfos removes "organization_infos" edges to OrganizationUserInfo entities.
func (uuo *UserUpdateOne) RemoveOrganizationInfos(o ...*OrganizationUserInfo) *UserUpdateOne {
	ids := make([]string, len(o))
	for i := range o {
		ids[i] = o[i].ID
	}
	return uuo.RemoveOrganizationInfoIDs(ids...)
}

// ClearTenantInfos clears all "tenant_infos" edges to the TenantUserInfo entity.
func (uuo *UserUpdateOne) ClearTenantInfos() *UserUpdateOne {
	uuo.mutation.ClearTenantInfos()
	return uuo
}

// RemoveTenantInfoIDs removes the "tenant_infos" edge to TenantUserInfo entities by IDs.
func (uuo *UserUpdateOne) RemoveTenantInfoIDs(ids ...string) *UserUpdateOne {
	uuo.mutation.RemoveTenantInfoIDs(ids...)
	return uuo
}

// RemoveTenantInfos removes "tenant_infos" edges to TenantUserInfo entities.
func (uuo *UserUpdateOne) RemoveTenantInfos(t ...*TenantUserInfo) *UserUpdateOne {
	ids := make([]string, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return uuo.RemoveTenantInfoIDs(ids...)
}

// ClearFiles clears all "files" edges to the File entity.
func (uuo *UserUpdateOne) ClearFiles() *UserUpdateOne {
	uuo.mutation.ClearFiles()
	return uuo
}

// RemoveFileIDs removes the "files" edge to File entities by IDs.
func (uuo *UserUpdateOne) RemoveFileIDs(ids ...string) *UserUpdateOne {
	uuo.mutation.RemoveFileIDs(ids...)
	return uuo
}

// RemoveFiles removes "files" edges to File entities.
func (uuo *UserUpdateOne) RemoveFiles(f ...*File) *UserUpdateOne {
	ids := make([]string, len(f))
	for i := range f {
		ids[i] = f[i].ID
	}
	return uuo.RemoveFileIDs(ids...)
}

// ClearAvatar clears the "avatar" edge to the File entity.
func (uuo *UserUpdateOne) ClearAvatar() *UserUpdateOne {
	uuo.mutation.ClearAvatar()
	return uuo
}

// Where appends a list predicates to the UserUpdate builder.
func (uuo *UserUpdateOne) Where(ps ...predicate.User) *UserUpdateOne {
	uuo.mutation.Where(ps...)
	return uuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (uuo *UserUpdateOne) Select(field string, fields ...string) *UserUpdateOne {
	uuo.fields = append([]string{field}, fields...)
	return uuo
}

// Save executes the query and returns the updated User entity.
func (uuo *UserUpdateOne) Save(ctx context.Context) (*User, error) {
	if err := uuo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, uuo.sqlSave, uuo.mutation, uuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (uuo *UserUpdateOne) SaveX(ctx context.Context) *User {
	node, err := uuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (uuo *UserUpdateOne) Exec(ctx context.Context) error {
	_, err := uuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uuo *UserUpdateOne) ExecX(ctx context.Context) {
	if err := uuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uuo *UserUpdateOne) defaults() error {
	if _, ok := uuo.mutation.UpdatedAt(); !ok {
		if user.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized user.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := user.UpdateDefaultUpdatedAt()
		uuo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (uuo *UserUpdateOne) check() error {
	if v, ok := uuo.mutation.Gender(); ok {
		if err := user.GenderValidator(v); err != nil {
			return &ValidationError{Name: "gender", err: fmt.Errorf(`ent: validator failed for field "User.gender": %w`, err)}
		}
	}
	return nil
}

func (uuo *UserUpdateOne) sqlSave(ctx context.Context) (_node *User, err error) {
	if err := uuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(user.Table, user.Columns, sqlgraph.NewFieldSpec(user.FieldID, field.TypeString))
	id, ok := uuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "User.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := uuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, user.FieldID)
		for _, f := range fields {
			if !user.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != user.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := uuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := uuo.mutation.UpdatedAt(); ok {
		_spec.SetField(user.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := uuo.mutation.Status(); ok {
		_spec.SetField(user.FieldStatus, field.TypeBool, value)
	}
	if uuo.mutation.StatusCleared() {
		_spec.ClearField(user.FieldStatus, field.TypeBool)
	}
	if value, ok := uuo.mutation.DeletedAt(); ok {
		_spec.SetField(user.FieldDeletedAt, field.TypeTime, value)
	}
	if uuo.mutation.DeletedAtCleared() {
		_spec.ClearField(user.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := uuo.mutation.Username(); ok {
		_spec.SetField(user.FieldUsername, field.TypeString, value)
	}
	if value, ok := uuo.mutation.Password(); ok {
		_spec.SetField(user.FieldPassword, field.TypeString, value)
	}
	if value, ok := uuo.mutation.Nickname(); ok {
		_spec.SetField(user.FieldNickname, field.TypeString, value)
	}
	if uuo.mutation.NicknameCleared() {
		_spec.ClearField(user.FieldNickname, field.TypeString)
	}
	if value, ok := uuo.mutation.Mobile(); ok {
		_spec.SetField(user.FieldMobile, field.TypeString, value)
	}
	if uuo.mutation.MobileCleared() {
		_spec.ClearField(user.FieldMobile, field.TypeString)
	}
	if value, ok := uuo.mutation.Email(); ok {
		_spec.SetField(user.FieldEmail, field.TypeString, value)
	}
	if uuo.mutation.EmailCleared() {
		_spec.ClearField(user.FieldEmail, field.TypeString)
	}
	if value, ok := uuo.mutation.Gender(); ok {
		_spec.SetField(user.FieldGender, field.TypeEnum, value)
	}
	if value, ok := uuo.mutation.Post(); ok {
		_spec.SetField(user.FieldPost, field.TypeString, value)
	}
	if uuo.mutation.PostCleared() {
		_spec.ClearField(user.FieldPost, field.TypeString)
	}
	if value, ok := uuo.mutation.IsSuperuser(); ok {
		_spec.SetField(user.FieldIsSuperuser, field.TypeBool, value)
	}
	if value, ok := uuo.mutation.DefaultTenantID(); ok {
		_spec.SetField(user.FieldDefaultTenantID, field.TypeString, value)
	}
	if uuo.mutation.DefaultTenantIDCleared() {
		_spec.ClearField(user.FieldDefaultTenantID, field.TypeString)
	}
	if value, ok := uuo.mutation.DeviceNo(); ok {
		_spec.SetField(user.FieldDeviceNo, field.TypeString, value)
	}
	if uuo.mutation.DeviceNoCleared() {
		_spec.ClearField(user.FieldDeviceNo, field.TypeString)
	}
	if value, ok := uuo.mutation.Kind(); ok {
		_spec.SetField(user.FieldKind, field.TypeString, value)
	}
	if uuo.mutation.KindCleared() {
		_spec.ClearField(user.FieldKind, field.TypeString)
	}
	if value, ok := uuo.mutation.Imei(); ok {
		_spec.SetField(user.FieldImei, field.TypeString, value)
	}
	if uuo.mutation.TenantsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   user.TenantsTable,
			Columns: user.TenantsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedTenantsIDs(); len(nodes) > 0 && !uuo.mutation.TenantsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   user.TenantsTable,
			Columns: user.TenantsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.TenantsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   user.TenantsTable,
			Columns: user.TenantsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.OrganizationsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   user.OrganizationsTable,
			Columns: user.OrganizationsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedOrganizationsIDs(); len(nodes) > 0 && !uuo.mutation.OrganizationsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   user.OrganizationsTable,
			Columns: user.OrganizationsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.OrganizationsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   user.OrganizationsTable,
			Columns: user.OrganizationsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.PositionsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   user.PositionsTable,
			Columns: user.PositionsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(position.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedPositionsIDs(); len(nodes) > 0 && !uuo.mutation.PositionsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   user.PositionsTable,
			Columns: user.PositionsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(position.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.PositionsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   user.PositionsTable,
			Columns: user.PositionsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(position.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.GroupsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   user.GroupsTable,
			Columns: user.GroupsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(group.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedGroupsIDs(); len(nodes) > 0 && !uuo.mutation.GroupsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   user.GroupsTable,
			Columns: user.GroupsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(group.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.GroupsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   user.GroupsTable,
			Columns: user.GroupsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(group.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.RolesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   user.RolesTable,
			Columns: user.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedRolesIDs(); len(nodes) > 0 && !uuo.mutation.RolesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   user.RolesTable,
			Columns: user.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RolesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   user.RolesTable,
			Columns: user.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.OrganizationInfosCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.OrganizationInfosTable,
			Columns: []string{user.OrganizationInfosColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organizationuserinfo.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedOrganizationInfosIDs(); len(nodes) > 0 && !uuo.mutation.OrganizationInfosCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.OrganizationInfosTable,
			Columns: []string{user.OrganizationInfosColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organizationuserinfo.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.OrganizationInfosIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.OrganizationInfosTable,
			Columns: []string{user.OrganizationInfosColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organizationuserinfo.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.TenantInfosCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.TenantInfosTable,
			Columns: []string{user.TenantInfosColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenantuserinfo.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedTenantInfosIDs(); len(nodes) > 0 && !uuo.mutation.TenantInfosCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.TenantInfosTable,
			Columns: []string{user.TenantInfosColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenantuserinfo.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.TenantInfosIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.TenantInfosTable,
			Columns: []string{user.TenantInfosColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenantuserinfo.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.FilesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.FilesTable,
			Columns: []string{user.FilesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(file.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.RemovedFilesIDs(); len(nodes) > 0 && !uuo.mutation.FilesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.FilesTable,
			Columns: []string{user.FilesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(file.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.FilesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.FilesTable,
			Columns: []string{user.FilesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(file.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if uuo.mutation.AvatarCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   user.AvatarTable,
			Columns: []string{user.AvatarColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(file.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := uuo.mutation.AvatarIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   user.AvatarTable,
			Columns: []string{user.AvatarColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(file.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &User{config: uuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, uuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{user.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	uuo.mutation.done = true
	return _node, nil
}
