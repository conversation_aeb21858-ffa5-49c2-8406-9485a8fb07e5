// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/group"
	"phoenix/service/saas/model/ent/grouptype"
	"phoenix/service/saas/model/ent/role"
	"phoenix/service/saas/model/ent/tenant"
	"phoenix/service/saas/model/ent/user"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// GroupCreate is the builder for creating a Group entity.
type GroupCreate struct {
	config
	mutation *GroupMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (gc *GroupCreate) SetCreatedAt(t time.Time) *GroupCreate {
	gc.mutation.SetCreatedAt(t)
	return gc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (gc *GroupCreate) SetNillableCreatedAt(t *time.Time) *GroupCreate {
	if t != nil {
		gc.SetCreatedAt(*t)
	}
	return gc
}

// SetUpdatedAt sets the "updated_at" field.
func (gc *GroupCreate) SetUpdatedAt(t time.Time) *GroupCreate {
	gc.mutation.SetUpdatedAt(t)
	return gc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (gc *GroupCreate) SetNillableUpdatedAt(t *time.Time) *GroupCreate {
	if t != nil {
		gc.SetUpdatedAt(*t)
	}
	return gc
}

// SetStatus sets the "status" field.
func (gc *GroupCreate) SetStatus(b bool) *GroupCreate {
	gc.mutation.SetStatus(b)
	return gc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (gc *GroupCreate) SetNillableStatus(b *bool) *GroupCreate {
	if b != nil {
		gc.SetStatus(*b)
	}
	return gc
}

// SetSort sets the "sort" field.
func (gc *GroupCreate) SetSort(u uint32) *GroupCreate {
	gc.mutation.SetSort(u)
	return gc
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (gc *GroupCreate) SetNillableSort(u *uint32) *GroupCreate {
	if u != nil {
		gc.SetSort(*u)
	}
	return gc
}

// SetTenantID sets the "tenant_id" field.
func (gc *GroupCreate) SetTenantID(s string) *GroupCreate {
	gc.mutation.SetTenantID(s)
	return gc
}

// SetDeletedAt sets the "deleted_at" field.
func (gc *GroupCreate) SetDeletedAt(t time.Time) *GroupCreate {
	gc.mutation.SetDeletedAt(t)
	return gc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (gc *GroupCreate) SetNillableDeletedAt(t *time.Time) *GroupCreate {
	if t != nil {
		gc.SetDeletedAt(*t)
	}
	return gc
}

// SetGroupTypeID sets the "group_type_id" field.
func (gc *GroupCreate) SetGroupTypeID(s string) *GroupCreate {
	gc.mutation.SetGroupTypeID(s)
	return gc
}

// SetNillableGroupTypeID sets the "group_type_id" field if the given value is not nil.
func (gc *GroupCreate) SetNillableGroupTypeID(s *string) *GroupCreate {
	if s != nil {
		gc.SetGroupTypeID(*s)
	}
	return gc
}

// SetName sets the "name" field.
func (gc *GroupCreate) SetName(s string) *GroupCreate {
	gc.mutation.SetName(s)
	return gc
}

// SetCode sets the "code" field.
func (gc *GroupCreate) SetCode(s string) *GroupCreate {
	gc.mutation.SetCode(s)
	return gc
}

// SetRemark sets the "remark" field.
func (gc *GroupCreate) SetRemark(s string) *GroupCreate {
	gc.mutation.SetRemark(s)
	return gc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (gc *GroupCreate) SetNillableRemark(s *string) *GroupCreate {
	if s != nil {
		gc.SetRemark(*s)
	}
	return gc
}

// SetID sets the "id" field.
func (gc *GroupCreate) SetID(s string) *GroupCreate {
	gc.mutation.SetID(s)
	return gc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (gc *GroupCreate) SetNillableID(s *string) *GroupCreate {
	if s != nil {
		gc.SetID(*s)
	}
	return gc
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (gc *GroupCreate) SetTenant(t *Tenant) *GroupCreate {
	return gc.SetTenantID(t.ID)
}

// AddUserIDs adds the "users" edge to the User entity by IDs.
func (gc *GroupCreate) AddUserIDs(ids ...string) *GroupCreate {
	gc.mutation.AddUserIDs(ids...)
	return gc
}

// AddUsers adds the "users" edges to the User entity.
func (gc *GroupCreate) AddUsers(u ...*User) *GroupCreate {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return gc.AddUserIDs(ids...)
}

// AddRoleIDs adds the "roles" edge to the Role entity by IDs.
func (gc *GroupCreate) AddRoleIDs(ids ...string) *GroupCreate {
	gc.mutation.AddRoleIDs(ids...)
	return gc
}

// AddRoles adds the "roles" edges to the Role entity.
func (gc *GroupCreate) AddRoles(r ...*Role) *GroupCreate {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return gc.AddRoleIDs(ids...)
}

// SetGroupType sets the "group_type" edge to the GroupType entity.
func (gc *GroupCreate) SetGroupType(g *GroupType) *GroupCreate {
	return gc.SetGroupTypeID(g.ID)
}

// Mutation returns the GroupMutation object of the builder.
func (gc *GroupCreate) Mutation() *GroupMutation {
	return gc.mutation
}

// Save creates the Group in the database.
func (gc *GroupCreate) Save(ctx context.Context) (*Group, error) {
	if err := gc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, gc.sqlSave, gc.mutation, gc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (gc *GroupCreate) SaveX(ctx context.Context) *Group {
	v, err := gc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (gc *GroupCreate) Exec(ctx context.Context) error {
	_, err := gc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (gc *GroupCreate) ExecX(ctx context.Context) {
	if err := gc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (gc *GroupCreate) defaults() error {
	if _, ok := gc.mutation.CreatedAt(); !ok {
		if group.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized group.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := group.DefaultCreatedAt()
		gc.mutation.SetCreatedAt(v)
	}
	if _, ok := gc.mutation.UpdatedAt(); !ok {
		if group.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized group.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := group.DefaultUpdatedAt()
		gc.mutation.SetUpdatedAt(v)
	}
	if _, ok := gc.mutation.Status(); !ok {
		v := group.DefaultStatus
		gc.mutation.SetStatus(v)
	}
	if _, ok := gc.mutation.Sort(); !ok {
		v := group.DefaultSort
		gc.mutation.SetSort(v)
	}
	if _, ok := gc.mutation.Remark(); !ok {
		v := group.DefaultRemark
		gc.mutation.SetRemark(v)
	}
	if _, ok := gc.mutation.ID(); !ok {
		if group.DefaultID == nil {
			return fmt.Errorf("ent: uninitialized group.DefaultID (forgotten import ent/runtime?)")
		}
		v := group.DefaultID()
		gc.mutation.SetID(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (gc *GroupCreate) check() error {
	if _, ok := gc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Group.created_at"`)}
	}
	if _, ok := gc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Group.updated_at"`)}
	}
	if _, ok := gc.mutation.Sort(); !ok {
		return &ValidationError{Name: "sort", err: errors.New(`ent: missing required field "Group.sort"`)}
	}
	if _, ok := gc.mutation.TenantID(); !ok {
		return &ValidationError{Name: "tenant_id", err: errors.New(`ent: missing required field "Group.tenant_id"`)}
	}
	if _, ok := gc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Group.name"`)}
	}
	if _, ok := gc.mutation.Code(); !ok {
		return &ValidationError{Name: "code", err: errors.New(`ent: missing required field "Group.code"`)}
	}
	if _, ok := gc.mutation.TenantID(); !ok {
		return &ValidationError{Name: "tenant", err: errors.New(`ent: missing required edge "Group.tenant"`)}
	}
	return nil
}

func (gc *GroupCreate) sqlSave(ctx context.Context) (*Group, error) {
	if err := gc.check(); err != nil {
		return nil, err
	}
	_node, _spec := gc.createSpec()
	if err := sqlgraph.CreateNode(ctx, gc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected Group.ID type: %T", _spec.ID.Value)
		}
	}
	gc.mutation.id = &_node.ID
	gc.mutation.done = true
	return _node, nil
}

func (gc *GroupCreate) createSpec() (*Group, *sqlgraph.CreateSpec) {
	var (
		_node = &Group{config: gc.config}
		_spec = sqlgraph.NewCreateSpec(group.Table, sqlgraph.NewFieldSpec(group.FieldID, field.TypeString))
	)
	_spec.OnConflict = gc.conflict
	if id, ok := gc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := gc.mutation.CreatedAt(); ok {
		_spec.SetField(group.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := gc.mutation.UpdatedAt(); ok {
		_spec.SetField(group.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := gc.mutation.Status(); ok {
		_spec.SetField(group.FieldStatus, field.TypeBool, value)
		_node.Status = value
	}
	if value, ok := gc.mutation.Sort(); ok {
		_spec.SetField(group.FieldSort, field.TypeUint32, value)
		_node.Sort = value
	}
	if value, ok := gc.mutation.DeletedAt(); ok {
		_spec.SetField(group.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := gc.mutation.Name(); ok {
		_spec.SetField(group.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := gc.mutation.Code(); ok {
		_spec.SetField(group.FieldCode, field.TypeString, value)
		_node.Code = value
	}
	if value, ok := gc.mutation.Remark(); ok {
		_spec.SetField(group.FieldRemark, field.TypeString, value)
		_node.Remark = value
	}
	if nodes := gc.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   group.TenantTable,
			Columns: []string{group.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TenantID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := gc.mutation.UsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   group.UsersTable,
			Columns: group.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := gc.mutation.RolesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   group.RolesTable,
			Columns: group.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := gc.mutation.GroupTypeIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   group.GroupTypeTable,
			Columns: []string{group.GroupTypeColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(grouptype.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.GroupTypeID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Group.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.GroupUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (gc *GroupCreate) OnConflict(opts ...sql.ConflictOption) *GroupUpsertOne {
	gc.conflict = opts
	return &GroupUpsertOne{
		create: gc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Group.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (gc *GroupCreate) OnConflictColumns(columns ...string) *GroupUpsertOne {
	gc.conflict = append(gc.conflict, sql.ConflictColumns(columns...))
	return &GroupUpsertOne{
		create: gc,
	}
}

type (
	// GroupUpsertOne is the builder for "upsert"-ing
	//  one Group node.
	GroupUpsertOne struct {
		create *GroupCreate
	}

	// GroupUpsert is the "OnConflict" setter.
	GroupUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *GroupUpsert) SetUpdatedAt(v time.Time) *GroupUpsert {
	u.Set(group.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *GroupUpsert) UpdateUpdatedAt() *GroupUpsert {
	u.SetExcluded(group.FieldUpdatedAt)
	return u
}

// SetStatus sets the "status" field.
func (u *GroupUpsert) SetStatus(v bool) *GroupUpsert {
	u.Set(group.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *GroupUpsert) UpdateStatus() *GroupUpsert {
	u.SetExcluded(group.FieldStatus)
	return u
}

// ClearStatus clears the value of the "status" field.
func (u *GroupUpsert) ClearStatus() *GroupUpsert {
	u.SetNull(group.FieldStatus)
	return u
}

// SetSort sets the "sort" field.
func (u *GroupUpsert) SetSort(v uint32) *GroupUpsert {
	u.Set(group.FieldSort, v)
	return u
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *GroupUpsert) UpdateSort() *GroupUpsert {
	u.SetExcluded(group.FieldSort)
	return u
}

// AddSort adds v to the "sort" field.
func (u *GroupUpsert) AddSort(v uint32) *GroupUpsert {
	u.Add(group.FieldSort, v)
	return u
}

// SetTenantID sets the "tenant_id" field.
func (u *GroupUpsert) SetTenantID(v string) *GroupUpsert {
	u.Set(group.FieldTenantID, v)
	return u
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *GroupUpsert) UpdateTenantID() *GroupUpsert {
	u.SetExcluded(group.FieldTenantID)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *GroupUpsert) SetDeletedAt(v time.Time) *GroupUpsert {
	u.Set(group.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *GroupUpsert) UpdateDeletedAt() *GroupUpsert {
	u.SetExcluded(group.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *GroupUpsert) ClearDeletedAt() *GroupUpsert {
	u.SetNull(group.FieldDeletedAt)
	return u
}

// SetGroupTypeID sets the "group_type_id" field.
func (u *GroupUpsert) SetGroupTypeID(v string) *GroupUpsert {
	u.Set(group.FieldGroupTypeID, v)
	return u
}

// UpdateGroupTypeID sets the "group_type_id" field to the value that was provided on create.
func (u *GroupUpsert) UpdateGroupTypeID() *GroupUpsert {
	u.SetExcluded(group.FieldGroupTypeID)
	return u
}

// ClearGroupTypeID clears the value of the "group_type_id" field.
func (u *GroupUpsert) ClearGroupTypeID() *GroupUpsert {
	u.SetNull(group.FieldGroupTypeID)
	return u
}

// SetName sets the "name" field.
func (u *GroupUpsert) SetName(v string) *GroupUpsert {
	u.Set(group.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *GroupUpsert) UpdateName() *GroupUpsert {
	u.SetExcluded(group.FieldName)
	return u
}

// SetCode sets the "code" field.
func (u *GroupUpsert) SetCode(v string) *GroupUpsert {
	u.Set(group.FieldCode, v)
	return u
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *GroupUpsert) UpdateCode() *GroupUpsert {
	u.SetExcluded(group.FieldCode)
	return u
}

// SetRemark sets the "remark" field.
func (u *GroupUpsert) SetRemark(v string) *GroupUpsert {
	u.Set(group.FieldRemark, v)
	return u
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *GroupUpsert) UpdateRemark() *GroupUpsert {
	u.SetExcluded(group.FieldRemark)
	return u
}

// ClearRemark clears the value of the "remark" field.
func (u *GroupUpsert) ClearRemark() *GroupUpsert {
	u.SetNull(group.FieldRemark)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Group.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(group.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *GroupUpsertOne) UpdateNewValues() *GroupUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(group.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(group.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Group.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *GroupUpsertOne) Ignore() *GroupUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *GroupUpsertOne) DoNothing() *GroupUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the GroupCreate.OnConflict
// documentation for more info.
func (u *GroupUpsertOne) Update(set func(*GroupUpsert)) *GroupUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&GroupUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *GroupUpsertOne) SetUpdatedAt(v time.Time) *GroupUpsertOne {
	return u.Update(func(s *GroupUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *GroupUpsertOne) UpdateUpdatedAt() *GroupUpsertOne {
	return u.Update(func(s *GroupUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetStatus sets the "status" field.
func (u *GroupUpsertOne) SetStatus(v bool) *GroupUpsertOne {
	return u.Update(func(s *GroupUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *GroupUpsertOne) UpdateStatus() *GroupUpsertOne {
	return u.Update(func(s *GroupUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *GroupUpsertOne) ClearStatus() *GroupUpsertOne {
	return u.Update(func(s *GroupUpsert) {
		s.ClearStatus()
	})
}

// SetSort sets the "sort" field.
func (u *GroupUpsertOne) SetSort(v uint32) *GroupUpsertOne {
	return u.Update(func(s *GroupUpsert) {
		s.SetSort(v)
	})
}

// AddSort adds v to the "sort" field.
func (u *GroupUpsertOne) AddSort(v uint32) *GroupUpsertOne {
	return u.Update(func(s *GroupUpsert) {
		s.AddSort(v)
	})
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *GroupUpsertOne) UpdateSort() *GroupUpsertOne {
	return u.Update(func(s *GroupUpsert) {
		s.UpdateSort()
	})
}

// SetTenantID sets the "tenant_id" field.
func (u *GroupUpsertOne) SetTenantID(v string) *GroupUpsertOne {
	return u.Update(func(s *GroupUpsert) {
		s.SetTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *GroupUpsertOne) UpdateTenantID() *GroupUpsertOne {
	return u.Update(func(s *GroupUpsert) {
		s.UpdateTenantID()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *GroupUpsertOne) SetDeletedAt(v time.Time) *GroupUpsertOne {
	return u.Update(func(s *GroupUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *GroupUpsertOne) UpdateDeletedAt() *GroupUpsertOne {
	return u.Update(func(s *GroupUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *GroupUpsertOne) ClearDeletedAt() *GroupUpsertOne {
	return u.Update(func(s *GroupUpsert) {
		s.ClearDeletedAt()
	})
}

// SetGroupTypeID sets the "group_type_id" field.
func (u *GroupUpsertOne) SetGroupTypeID(v string) *GroupUpsertOne {
	return u.Update(func(s *GroupUpsert) {
		s.SetGroupTypeID(v)
	})
}

// UpdateGroupTypeID sets the "group_type_id" field to the value that was provided on create.
func (u *GroupUpsertOne) UpdateGroupTypeID() *GroupUpsertOne {
	return u.Update(func(s *GroupUpsert) {
		s.UpdateGroupTypeID()
	})
}

// ClearGroupTypeID clears the value of the "group_type_id" field.
func (u *GroupUpsertOne) ClearGroupTypeID() *GroupUpsertOne {
	return u.Update(func(s *GroupUpsert) {
		s.ClearGroupTypeID()
	})
}

// SetName sets the "name" field.
func (u *GroupUpsertOne) SetName(v string) *GroupUpsertOne {
	return u.Update(func(s *GroupUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *GroupUpsertOne) UpdateName() *GroupUpsertOne {
	return u.Update(func(s *GroupUpsert) {
		s.UpdateName()
	})
}

// SetCode sets the "code" field.
func (u *GroupUpsertOne) SetCode(v string) *GroupUpsertOne {
	return u.Update(func(s *GroupUpsert) {
		s.SetCode(v)
	})
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *GroupUpsertOne) UpdateCode() *GroupUpsertOne {
	return u.Update(func(s *GroupUpsert) {
		s.UpdateCode()
	})
}

// SetRemark sets the "remark" field.
func (u *GroupUpsertOne) SetRemark(v string) *GroupUpsertOne {
	return u.Update(func(s *GroupUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *GroupUpsertOne) UpdateRemark() *GroupUpsertOne {
	return u.Update(func(s *GroupUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *GroupUpsertOne) ClearRemark() *GroupUpsertOne {
	return u.Update(func(s *GroupUpsert) {
		s.ClearRemark()
	})
}

// Exec executes the query.
func (u *GroupUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for GroupCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *GroupUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *GroupUpsertOne) ID(ctx context.Context) (id string, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: GroupUpsertOne.ID is not supported by MySQL driver. Use GroupUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *GroupUpsertOne) IDX(ctx context.Context) string {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// GroupCreateBulk is the builder for creating many Group entities in bulk.
type GroupCreateBulk struct {
	config
	err      error
	builders []*GroupCreate
	conflict []sql.ConflictOption
}

// Save creates the Group entities in the database.
func (gcb *GroupCreateBulk) Save(ctx context.Context) ([]*Group, error) {
	if gcb.err != nil {
		return nil, gcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(gcb.builders))
	nodes := make([]*Group, len(gcb.builders))
	mutators := make([]Mutator, len(gcb.builders))
	for i := range gcb.builders {
		func(i int, root context.Context) {
			builder := gcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*GroupMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, gcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = gcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, gcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, gcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (gcb *GroupCreateBulk) SaveX(ctx context.Context) []*Group {
	v, err := gcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (gcb *GroupCreateBulk) Exec(ctx context.Context) error {
	_, err := gcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (gcb *GroupCreateBulk) ExecX(ctx context.Context) {
	if err := gcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Group.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.GroupUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (gcb *GroupCreateBulk) OnConflict(opts ...sql.ConflictOption) *GroupUpsertBulk {
	gcb.conflict = opts
	return &GroupUpsertBulk{
		create: gcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Group.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (gcb *GroupCreateBulk) OnConflictColumns(columns ...string) *GroupUpsertBulk {
	gcb.conflict = append(gcb.conflict, sql.ConflictColumns(columns...))
	return &GroupUpsertBulk{
		create: gcb,
	}
}

// GroupUpsertBulk is the builder for "upsert"-ing
// a bulk of Group nodes.
type GroupUpsertBulk struct {
	create *GroupCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Group.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(group.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *GroupUpsertBulk) UpdateNewValues() *GroupUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(group.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(group.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Group.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *GroupUpsertBulk) Ignore() *GroupUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *GroupUpsertBulk) DoNothing() *GroupUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the GroupCreateBulk.OnConflict
// documentation for more info.
func (u *GroupUpsertBulk) Update(set func(*GroupUpsert)) *GroupUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&GroupUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *GroupUpsertBulk) SetUpdatedAt(v time.Time) *GroupUpsertBulk {
	return u.Update(func(s *GroupUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *GroupUpsertBulk) UpdateUpdatedAt() *GroupUpsertBulk {
	return u.Update(func(s *GroupUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetStatus sets the "status" field.
func (u *GroupUpsertBulk) SetStatus(v bool) *GroupUpsertBulk {
	return u.Update(func(s *GroupUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *GroupUpsertBulk) UpdateStatus() *GroupUpsertBulk {
	return u.Update(func(s *GroupUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *GroupUpsertBulk) ClearStatus() *GroupUpsertBulk {
	return u.Update(func(s *GroupUpsert) {
		s.ClearStatus()
	})
}

// SetSort sets the "sort" field.
func (u *GroupUpsertBulk) SetSort(v uint32) *GroupUpsertBulk {
	return u.Update(func(s *GroupUpsert) {
		s.SetSort(v)
	})
}

// AddSort adds v to the "sort" field.
func (u *GroupUpsertBulk) AddSort(v uint32) *GroupUpsertBulk {
	return u.Update(func(s *GroupUpsert) {
		s.AddSort(v)
	})
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *GroupUpsertBulk) UpdateSort() *GroupUpsertBulk {
	return u.Update(func(s *GroupUpsert) {
		s.UpdateSort()
	})
}

// SetTenantID sets the "tenant_id" field.
func (u *GroupUpsertBulk) SetTenantID(v string) *GroupUpsertBulk {
	return u.Update(func(s *GroupUpsert) {
		s.SetTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *GroupUpsertBulk) UpdateTenantID() *GroupUpsertBulk {
	return u.Update(func(s *GroupUpsert) {
		s.UpdateTenantID()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *GroupUpsertBulk) SetDeletedAt(v time.Time) *GroupUpsertBulk {
	return u.Update(func(s *GroupUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *GroupUpsertBulk) UpdateDeletedAt() *GroupUpsertBulk {
	return u.Update(func(s *GroupUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *GroupUpsertBulk) ClearDeletedAt() *GroupUpsertBulk {
	return u.Update(func(s *GroupUpsert) {
		s.ClearDeletedAt()
	})
}

// SetGroupTypeID sets the "group_type_id" field.
func (u *GroupUpsertBulk) SetGroupTypeID(v string) *GroupUpsertBulk {
	return u.Update(func(s *GroupUpsert) {
		s.SetGroupTypeID(v)
	})
}

// UpdateGroupTypeID sets the "group_type_id" field to the value that was provided on create.
func (u *GroupUpsertBulk) UpdateGroupTypeID() *GroupUpsertBulk {
	return u.Update(func(s *GroupUpsert) {
		s.UpdateGroupTypeID()
	})
}

// ClearGroupTypeID clears the value of the "group_type_id" field.
func (u *GroupUpsertBulk) ClearGroupTypeID() *GroupUpsertBulk {
	return u.Update(func(s *GroupUpsert) {
		s.ClearGroupTypeID()
	})
}

// SetName sets the "name" field.
func (u *GroupUpsertBulk) SetName(v string) *GroupUpsertBulk {
	return u.Update(func(s *GroupUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *GroupUpsertBulk) UpdateName() *GroupUpsertBulk {
	return u.Update(func(s *GroupUpsert) {
		s.UpdateName()
	})
}

// SetCode sets the "code" field.
func (u *GroupUpsertBulk) SetCode(v string) *GroupUpsertBulk {
	return u.Update(func(s *GroupUpsert) {
		s.SetCode(v)
	})
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *GroupUpsertBulk) UpdateCode() *GroupUpsertBulk {
	return u.Update(func(s *GroupUpsert) {
		s.UpdateCode()
	})
}

// SetRemark sets the "remark" field.
func (u *GroupUpsertBulk) SetRemark(v string) *GroupUpsertBulk {
	return u.Update(func(s *GroupUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *GroupUpsertBulk) UpdateRemark() *GroupUpsertBulk {
	return u.Update(func(s *GroupUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *GroupUpsertBulk) ClearRemark() *GroupUpsertBulk {
	return u.Update(func(s *GroupUpsert) {
		s.ClearRemark()
	})
}

// Exec executes the query.
func (u *GroupUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the GroupCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for GroupCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *GroupUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
