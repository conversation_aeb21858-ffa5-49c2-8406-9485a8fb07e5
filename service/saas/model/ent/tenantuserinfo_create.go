// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/tenant"
	"phoenix/service/saas/model/ent/tenantuserinfo"
	"phoenix/service/saas/model/ent/user"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// TenantUserInfoCreate is the builder for creating a TenantUserInfo entity.
type TenantUserInfoCreate struct {
	config
	mutation *TenantUserInfoMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (tuic *TenantUserInfoCreate) SetCreatedAt(t time.Time) *TenantUserInfoCreate {
	tuic.mutation.SetCreatedAt(t)
	return tuic
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (tuic *TenantUserInfoCreate) SetNillableCreatedAt(t *time.Time) *TenantUserInfoCreate {
	if t != nil {
		tuic.SetCreatedAt(*t)
	}
	return tuic
}

// SetUpdatedAt sets the "updated_at" field.
func (tuic *TenantUserInfoCreate) SetUpdatedAt(t time.Time) *TenantUserInfoCreate {
	tuic.mutation.SetUpdatedAt(t)
	return tuic
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (tuic *TenantUserInfoCreate) SetNillableUpdatedAt(t *time.Time) *TenantUserInfoCreate {
	if t != nil {
		tuic.SetUpdatedAt(*t)
	}
	return tuic
}

// SetSort sets the "sort" field.
func (tuic *TenantUserInfoCreate) SetSort(u uint32) *TenantUserInfoCreate {
	tuic.mutation.SetSort(u)
	return tuic
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (tuic *TenantUserInfoCreate) SetNillableSort(u *uint32) *TenantUserInfoCreate {
	if u != nil {
		tuic.SetSort(*u)
	}
	return tuic
}

// SetDeletedAt sets the "deleted_at" field.
func (tuic *TenantUserInfoCreate) SetDeletedAt(t time.Time) *TenantUserInfoCreate {
	tuic.mutation.SetDeletedAt(t)
	return tuic
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (tuic *TenantUserInfoCreate) SetNillableDeletedAt(t *time.Time) *TenantUserInfoCreate {
	if t != nil {
		tuic.SetDeletedAt(*t)
	}
	return tuic
}

// SetTenantID sets the "tenant_id" field.
func (tuic *TenantUserInfoCreate) SetTenantID(s string) *TenantUserInfoCreate {
	tuic.mutation.SetTenantID(s)
	return tuic
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (tuic *TenantUserInfoCreate) SetNillableTenantID(s *string) *TenantUserInfoCreate {
	if s != nil {
		tuic.SetTenantID(*s)
	}
	return tuic
}

// SetUserID sets the "user_id" field.
func (tuic *TenantUserInfoCreate) SetUserID(s string) *TenantUserInfoCreate {
	tuic.mutation.SetUserID(s)
	return tuic
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (tuic *TenantUserInfoCreate) SetNillableUserID(s *string) *TenantUserInfoCreate {
	if s != nil {
		tuic.SetUserID(*s)
	}
	return tuic
}

// SetExtra sets the "extra" field.
func (tuic *TenantUserInfoCreate) SetExtra(s string) *TenantUserInfoCreate {
	tuic.mutation.SetExtra(s)
	return tuic
}

// SetNillableExtra sets the "extra" field if the given value is not nil.
func (tuic *TenantUserInfoCreate) SetNillableExtra(s *string) *TenantUserInfoCreate {
	if s != nil {
		tuic.SetExtra(*s)
	}
	return tuic
}

// SetID sets the "id" field.
func (tuic *TenantUserInfoCreate) SetID(s string) *TenantUserInfoCreate {
	tuic.mutation.SetID(s)
	return tuic
}

// SetNillableID sets the "id" field if the given value is not nil.
func (tuic *TenantUserInfoCreate) SetNillableID(s *string) *TenantUserInfoCreate {
	if s != nil {
		tuic.SetID(*s)
	}
	return tuic
}

// SetTenantUserInfoTenantID sets the "tenant_user_info_tenant" edge to the Tenant entity by ID.
func (tuic *TenantUserInfoCreate) SetTenantUserInfoTenantID(id string) *TenantUserInfoCreate {
	tuic.mutation.SetTenantUserInfoTenantID(id)
	return tuic
}

// SetNillableTenantUserInfoTenantID sets the "tenant_user_info_tenant" edge to the Tenant entity by ID if the given value is not nil.
func (tuic *TenantUserInfoCreate) SetNillableTenantUserInfoTenantID(id *string) *TenantUserInfoCreate {
	if id != nil {
		tuic = tuic.SetTenantUserInfoTenantID(*id)
	}
	return tuic
}

// SetTenantUserInfoTenant sets the "tenant_user_info_tenant" edge to the Tenant entity.
func (tuic *TenantUserInfoCreate) SetTenantUserInfoTenant(t *Tenant) *TenantUserInfoCreate {
	return tuic.SetTenantUserInfoTenantID(t.ID)
}

// SetTenantUserInfoUserID sets the "tenant_user_info_user" edge to the User entity by ID.
func (tuic *TenantUserInfoCreate) SetTenantUserInfoUserID(id string) *TenantUserInfoCreate {
	tuic.mutation.SetTenantUserInfoUserID(id)
	return tuic
}

// SetNillableTenantUserInfoUserID sets the "tenant_user_info_user" edge to the User entity by ID if the given value is not nil.
func (tuic *TenantUserInfoCreate) SetNillableTenantUserInfoUserID(id *string) *TenantUserInfoCreate {
	if id != nil {
		tuic = tuic.SetTenantUserInfoUserID(*id)
	}
	return tuic
}

// SetTenantUserInfoUser sets the "tenant_user_info_user" edge to the User entity.
func (tuic *TenantUserInfoCreate) SetTenantUserInfoUser(u *User) *TenantUserInfoCreate {
	return tuic.SetTenantUserInfoUserID(u.ID)
}

// Mutation returns the TenantUserInfoMutation object of the builder.
func (tuic *TenantUserInfoCreate) Mutation() *TenantUserInfoMutation {
	return tuic.mutation
}

// Save creates the TenantUserInfo in the database.
func (tuic *TenantUserInfoCreate) Save(ctx context.Context) (*TenantUserInfo, error) {
	if err := tuic.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, tuic.sqlSave, tuic.mutation, tuic.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (tuic *TenantUserInfoCreate) SaveX(ctx context.Context) *TenantUserInfo {
	v, err := tuic.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tuic *TenantUserInfoCreate) Exec(ctx context.Context) error {
	_, err := tuic.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tuic *TenantUserInfoCreate) ExecX(ctx context.Context) {
	if err := tuic.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (tuic *TenantUserInfoCreate) defaults() error {
	if _, ok := tuic.mutation.CreatedAt(); !ok {
		if tenantuserinfo.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized tenantuserinfo.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := tenantuserinfo.DefaultCreatedAt()
		tuic.mutation.SetCreatedAt(v)
	}
	if _, ok := tuic.mutation.UpdatedAt(); !ok {
		if tenantuserinfo.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized tenantuserinfo.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := tenantuserinfo.DefaultUpdatedAt()
		tuic.mutation.SetUpdatedAt(v)
	}
	if _, ok := tuic.mutation.Sort(); !ok {
		v := tenantuserinfo.DefaultSort
		tuic.mutation.SetSort(v)
	}
	if _, ok := tuic.mutation.Extra(); !ok {
		v := tenantuserinfo.DefaultExtra
		tuic.mutation.SetExtra(v)
	}
	if _, ok := tuic.mutation.ID(); !ok {
		if tenantuserinfo.DefaultID == nil {
			return fmt.Errorf("ent: uninitialized tenantuserinfo.DefaultID (forgotten import ent/runtime?)")
		}
		v := tenantuserinfo.DefaultID()
		tuic.mutation.SetID(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (tuic *TenantUserInfoCreate) check() error {
	if _, ok := tuic.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "TenantUserInfo.created_at"`)}
	}
	if _, ok := tuic.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "TenantUserInfo.updated_at"`)}
	}
	if _, ok := tuic.mutation.Sort(); !ok {
		return &ValidationError{Name: "sort", err: errors.New(`ent: missing required field "TenantUserInfo.sort"`)}
	}
	return nil
}

func (tuic *TenantUserInfoCreate) sqlSave(ctx context.Context) (*TenantUserInfo, error) {
	if err := tuic.check(); err != nil {
		return nil, err
	}
	_node, _spec := tuic.createSpec()
	if err := sqlgraph.CreateNode(ctx, tuic.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected TenantUserInfo.ID type: %T", _spec.ID.Value)
		}
	}
	tuic.mutation.id = &_node.ID
	tuic.mutation.done = true
	return _node, nil
}

func (tuic *TenantUserInfoCreate) createSpec() (*TenantUserInfo, *sqlgraph.CreateSpec) {
	var (
		_node = &TenantUserInfo{config: tuic.config}
		_spec = sqlgraph.NewCreateSpec(tenantuserinfo.Table, sqlgraph.NewFieldSpec(tenantuserinfo.FieldID, field.TypeString))
	)
	_spec.OnConflict = tuic.conflict
	if id, ok := tuic.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := tuic.mutation.CreatedAt(); ok {
		_spec.SetField(tenantuserinfo.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := tuic.mutation.UpdatedAt(); ok {
		_spec.SetField(tenantuserinfo.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := tuic.mutation.Sort(); ok {
		_spec.SetField(tenantuserinfo.FieldSort, field.TypeUint32, value)
		_node.Sort = value
	}
	if value, ok := tuic.mutation.DeletedAt(); ok {
		_spec.SetField(tenantuserinfo.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := tuic.mutation.Extra(); ok {
		_spec.SetField(tenantuserinfo.FieldExtra, field.TypeString, value)
		_node.Extra = value
	}
	if nodes := tuic.mutation.TenantUserInfoTenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   tenantuserinfo.TenantUserInfoTenantTable,
			Columns: []string{tenantuserinfo.TenantUserInfoTenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TenantID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := tuic.mutation.TenantUserInfoUserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   tenantuserinfo.TenantUserInfoUserTable,
			Columns: []string{tenantuserinfo.TenantUserInfoUserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.UserID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.TenantUserInfo.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.TenantUserInfoUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (tuic *TenantUserInfoCreate) OnConflict(opts ...sql.ConflictOption) *TenantUserInfoUpsertOne {
	tuic.conflict = opts
	return &TenantUserInfoUpsertOne{
		create: tuic,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.TenantUserInfo.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (tuic *TenantUserInfoCreate) OnConflictColumns(columns ...string) *TenantUserInfoUpsertOne {
	tuic.conflict = append(tuic.conflict, sql.ConflictColumns(columns...))
	return &TenantUserInfoUpsertOne{
		create: tuic,
	}
}

type (
	// TenantUserInfoUpsertOne is the builder for "upsert"-ing
	//  one TenantUserInfo node.
	TenantUserInfoUpsertOne struct {
		create *TenantUserInfoCreate
	}

	// TenantUserInfoUpsert is the "OnConflict" setter.
	TenantUserInfoUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *TenantUserInfoUpsert) SetUpdatedAt(v time.Time) *TenantUserInfoUpsert {
	u.Set(tenantuserinfo.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *TenantUserInfoUpsert) UpdateUpdatedAt() *TenantUserInfoUpsert {
	u.SetExcluded(tenantuserinfo.FieldUpdatedAt)
	return u
}

// SetSort sets the "sort" field.
func (u *TenantUserInfoUpsert) SetSort(v uint32) *TenantUserInfoUpsert {
	u.Set(tenantuserinfo.FieldSort, v)
	return u
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *TenantUserInfoUpsert) UpdateSort() *TenantUserInfoUpsert {
	u.SetExcluded(tenantuserinfo.FieldSort)
	return u
}

// AddSort adds v to the "sort" field.
func (u *TenantUserInfoUpsert) AddSort(v uint32) *TenantUserInfoUpsert {
	u.Add(tenantuserinfo.FieldSort, v)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *TenantUserInfoUpsert) SetDeletedAt(v time.Time) *TenantUserInfoUpsert {
	u.Set(tenantuserinfo.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *TenantUserInfoUpsert) UpdateDeletedAt() *TenantUserInfoUpsert {
	u.SetExcluded(tenantuserinfo.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *TenantUserInfoUpsert) ClearDeletedAt() *TenantUserInfoUpsert {
	u.SetNull(tenantuserinfo.FieldDeletedAt)
	return u
}

// SetTenantID sets the "tenant_id" field.
func (u *TenantUserInfoUpsert) SetTenantID(v string) *TenantUserInfoUpsert {
	u.Set(tenantuserinfo.FieldTenantID, v)
	return u
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *TenantUserInfoUpsert) UpdateTenantID() *TenantUserInfoUpsert {
	u.SetExcluded(tenantuserinfo.FieldTenantID)
	return u
}

// ClearTenantID clears the value of the "tenant_id" field.
func (u *TenantUserInfoUpsert) ClearTenantID() *TenantUserInfoUpsert {
	u.SetNull(tenantuserinfo.FieldTenantID)
	return u
}

// SetUserID sets the "user_id" field.
func (u *TenantUserInfoUpsert) SetUserID(v string) *TenantUserInfoUpsert {
	u.Set(tenantuserinfo.FieldUserID, v)
	return u
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *TenantUserInfoUpsert) UpdateUserID() *TenantUserInfoUpsert {
	u.SetExcluded(tenantuserinfo.FieldUserID)
	return u
}

// ClearUserID clears the value of the "user_id" field.
func (u *TenantUserInfoUpsert) ClearUserID() *TenantUserInfoUpsert {
	u.SetNull(tenantuserinfo.FieldUserID)
	return u
}

// SetExtra sets the "extra" field.
func (u *TenantUserInfoUpsert) SetExtra(v string) *TenantUserInfoUpsert {
	u.Set(tenantuserinfo.FieldExtra, v)
	return u
}

// UpdateExtra sets the "extra" field to the value that was provided on create.
func (u *TenantUserInfoUpsert) UpdateExtra() *TenantUserInfoUpsert {
	u.SetExcluded(tenantuserinfo.FieldExtra)
	return u
}

// ClearExtra clears the value of the "extra" field.
func (u *TenantUserInfoUpsert) ClearExtra() *TenantUserInfoUpsert {
	u.SetNull(tenantuserinfo.FieldExtra)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.TenantUserInfo.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(tenantuserinfo.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *TenantUserInfoUpsertOne) UpdateNewValues() *TenantUserInfoUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(tenantuserinfo.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(tenantuserinfo.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.TenantUserInfo.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *TenantUserInfoUpsertOne) Ignore() *TenantUserInfoUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *TenantUserInfoUpsertOne) DoNothing() *TenantUserInfoUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the TenantUserInfoCreate.OnConflict
// documentation for more info.
func (u *TenantUserInfoUpsertOne) Update(set func(*TenantUserInfoUpsert)) *TenantUserInfoUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&TenantUserInfoUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *TenantUserInfoUpsertOne) SetUpdatedAt(v time.Time) *TenantUserInfoUpsertOne {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *TenantUserInfoUpsertOne) UpdateUpdatedAt() *TenantUserInfoUpsertOne {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetSort sets the "sort" field.
func (u *TenantUserInfoUpsertOne) SetSort(v uint32) *TenantUserInfoUpsertOne {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.SetSort(v)
	})
}

// AddSort adds v to the "sort" field.
func (u *TenantUserInfoUpsertOne) AddSort(v uint32) *TenantUserInfoUpsertOne {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.AddSort(v)
	})
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *TenantUserInfoUpsertOne) UpdateSort() *TenantUserInfoUpsertOne {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.UpdateSort()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *TenantUserInfoUpsertOne) SetDeletedAt(v time.Time) *TenantUserInfoUpsertOne {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *TenantUserInfoUpsertOne) UpdateDeletedAt() *TenantUserInfoUpsertOne {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *TenantUserInfoUpsertOne) ClearDeletedAt() *TenantUserInfoUpsertOne {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.ClearDeletedAt()
	})
}

// SetTenantID sets the "tenant_id" field.
func (u *TenantUserInfoUpsertOne) SetTenantID(v string) *TenantUserInfoUpsertOne {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.SetTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *TenantUserInfoUpsertOne) UpdateTenantID() *TenantUserInfoUpsertOne {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.UpdateTenantID()
	})
}

// ClearTenantID clears the value of the "tenant_id" field.
func (u *TenantUserInfoUpsertOne) ClearTenantID() *TenantUserInfoUpsertOne {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.ClearTenantID()
	})
}

// SetUserID sets the "user_id" field.
func (u *TenantUserInfoUpsertOne) SetUserID(v string) *TenantUserInfoUpsertOne {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.SetUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *TenantUserInfoUpsertOne) UpdateUserID() *TenantUserInfoUpsertOne {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.UpdateUserID()
	})
}

// ClearUserID clears the value of the "user_id" field.
func (u *TenantUserInfoUpsertOne) ClearUserID() *TenantUserInfoUpsertOne {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.ClearUserID()
	})
}

// SetExtra sets the "extra" field.
func (u *TenantUserInfoUpsertOne) SetExtra(v string) *TenantUserInfoUpsertOne {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.SetExtra(v)
	})
}

// UpdateExtra sets the "extra" field to the value that was provided on create.
func (u *TenantUserInfoUpsertOne) UpdateExtra() *TenantUserInfoUpsertOne {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.UpdateExtra()
	})
}

// ClearExtra clears the value of the "extra" field.
func (u *TenantUserInfoUpsertOne) ClearExtra() *TenantUserInfoUpsertOne {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.ClearExtra()
	})
}

// Exec executes the query.
func (u *TenantUserInfoUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for TenantUserInfoCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *TenantUserInfoUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *TenantUserInfoUpsertOne) ID(ctx context.Context) (id string, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: TenantUserInfoUpsertOne.ID is not supported by MySQL driver. Use TenantUserInfoUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *TenantUserInfoUpsertOne) IDX(ctx context.Context) string {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// TenantUserInfoCreateBulk is the builder for creating many TenantUserInfo entities in bulk.
type TenantUserInfoCreateBulk struct {
	config
	err      error
	builders []*TenantUserInfoCreate
	conflict []sql.ConflictOption
}

// Save creates the TenantUserInfo entities in the database.
func (tuicb *TenantUserInfoCreateBulk) Save(ctx context.Context) ([]*TenantUserInfo, error) {
	if tuicb.err != nil {
		return nil, tuicb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(tuicb.builders))
	nodes := make([]*TenantUserInfo, len(tuicb.builders))
	mutators := make([]Mutator, len(tuicb.builders))
	for i := range tuicb.builders {
		func(i int, root context.Context) {
			builder := tuicb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*TenantUserInfoMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, tuicb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = tuicb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, tuicb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, tuicb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (tuicb *TenantUserInfoCreateBulk) SaveX(ctx context.Context) []*TenantUserInfo {
	v, err := tuicb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tuicb *TenantUserInfoCreateBulk) Exec(ctx context.Context) error {
	_, err := tuicb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tuicb *TenantUserInfoCreateBulk) ExecX(ctx context.Context) {
	if err := tuicb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.TenantUserInfo.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.TenantUserInfoUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (tuicb *TenantUserInfoCreateBulk) OnConflict(opts ...sql.ConflictOption) *TenantUserInfoUpsertBulk {
	tuicb.conflict = opts
	return &TenantUserInfoUpsertBulk{
		create: tuicb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.TenantUserInfo.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (tuicb *TenantUserInfoCreateBulk) OnConflictColumns(columns ...string) *TenantUserInfoUpsertBulk {
	tuicb.conflict = append(tuicb.conflict, sql.ConflictColumns(columns...))
	return &TenantUserInfoUpsertBulk{
		create: tuicb,
	}
}

// TenantUserInfoUpsertBulk is the builder for "upsert"-ing
// a bulk of TenantUserInfo nodes.
type TenantUserInfoUpsertBulk struct {
	create *TenantUserInfoCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.TenantUserInfo.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(tenantuserinfo.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *TenantUserInfoUpsertBulk) UpdateNewValues() *TenantUserInfoUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(tenantuserinfo.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(tenantuserinfo.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.TenantUserInfo.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *TenantUserInfoUpsertBulk) Ignore() *TenantUserInfoUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *TenantUserInfoUpsertBulk) DoNothing() *TenantUserInfoUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the TenantUserInfoCreateBulk.OnConflict
// documentation for more info.
func (u *TenantUserInfoUpsertBulk) Update(set func(*TenantUserInfoUpsert)) *TenantUserInfoUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&TenantUserInfoUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *TenantUserInfoUpsertBulk) SetUpdatedAt(v time.Time) *TenantUserInfoUpsertBulk {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *TenantUserInfoUpsertBulk) UpdateUpdatedAt() *TenantUserInfoUpsertBulk {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetSort sets the "sort" field.
func (u *TenantUserInfoUpsertBulk) SetSort(v uint32) *TenantUserInfoUpsertBulk {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.SetSort(v)
	})
}

// AddSort adds v to the "sort" field.
func (u *TenantUserInfoUpsertBulk) AddSort(v uint32) *TenantUserInfoUpsertBulk {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.AddSort(v)
	})
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *TenantUserInfoUpsertBulk) UpdateSort() *TenantUserInfoUpsertBulk {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.UpdateSort()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *TenantUserInfoUpsertBulk) SetDeletedAt(v time.Time) *TenantUserInfoUpsertBulk {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *TenantUserInfoUpsertBulk) UpdateDeletedAt() *TenantUserInfoUpsertBulk {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *TenantUserInfoUpsertBulk) ClearDeletedAt() *TenantUserInfoUpsertBulk {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.ClearDeletedAt()
	})
}

// SetTenantID sets the "tenant_id" field.
func (u *TenantUserInfoUpsertBulk) SetTenantID(v string) *TenantUserInfoUpsertBulk {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.SetTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *TenantUserInfoUpsertBulk) UpdateTenantID() *TenantUserInfoUpsertBulk {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.UpdateTenantID()
	})
}

// ClearTenantID clears the value of the "tenant_id" field.
func (u *TenantUserInfoUpsertBulk) ClearTenantID() *TenantUserInfoUpsertBulk {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.ClearTenantID()
	})
}

// SetUserID sets the "user_id" field.
func (u *TenantUserInfoUpsertBulk) SetUserID(v string) *TenantUserInfoUpsertBulk {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.SetUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *TenantUserInfoUpsertBulk) UpdateUserID() *TenantUserInfoUpsertBulk {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.UpdateUserID()
	})
}

// ClearUserID clears the value of the "user_id" field.
func (u *TenantUserInfoUpsertBulk) ClearUserID() *TenantUserInfoUpsertBulk {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.ClearUserID()
	})
}

// SetExtra sets the "extra" field.
func (u *TenantUserInfoUpsertBulk) SetExtra(v string) *TenantUserInfoUpsertBulk {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.SetExtra(v)
	})
}

// UpdateExtra sets the "extra" field to the value that was provided on create.
func (u *TenantUserInfoUpsertBulk) UpdateExtra() *TenantUserInfoUpsertBulk {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.UpdateExtra()
	})
}

// ClearExtra clears the value of the "extra" field.
func (u *TenantUserInfoUpsertBulk) ClearExtra() *TenantUserInfoUpsertBulk {
	return u.Update(func(s *TenantUserInfoUpsert) {
		s.ClearExtra()
	})
}

// Exec executes the query.
func (u *TenantUserInfoUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the TenantUserInfoCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for TenantUserInfoCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *TenantUserInfoUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
