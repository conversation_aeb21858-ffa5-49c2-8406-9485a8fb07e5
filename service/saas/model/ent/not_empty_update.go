// Code generated by ent, DO NOT EDIT.

package ent

// set field if value is not empty. e.g. string does not equal to ""
func (a *APIUpdate) SetNotEmptyPath(value string) *APIUpdate {
	if value != "" {
		return a.SetPath(value)
	}
	return a
}

// set field if value is not empty. e.g. string does not equal to ""
func (a *APIUpdateOne) SetNotEmptyPath(value string) *APIUpdateOne {
	if value != "" {
		return a.SetPath(value)
	}
	return a
}

// set field if value is not empty. e.g. string does not equal to ""
func (a *APIUpdate) SetNotEmptyDescription(value string) *APIUpdate {
	if value != "" {
		return a.SetDescription(value)
	}
	return a
}

// set field if value is not empty. e.g. string does not equal to ""
func (a *APIUpdateOne) SetNotEmptyDescription(value string) *APIUpdateOne {
	if value != "" {
		return a.SetDescription(value)
	}
	return a
}

// set field if value is not empty. e.g. string does not equal to ""
func (a *APIUpdate) SetNotEmptyAPIGroup(value string) *APIUpdate {
	if value != "" {
		return a.SetAPIGroup(value)
	}
	return a
}

// set field if value is not empty. e.g. string does not equal to ""
func (a *APIUpdateOne) SetNotEmptyAPIGroup(value string) *APIUpdateOne {
	if value != "" {
		return a.SetAPIGroup(value)
	}
	return a
}

// set field if value is not empty. e.g. string does not equal to ""
func (a *APIUpdate) SetNotEmptyMethod(value string) *APIUpdate {
	if value != "" {
		return a.SetMethod(value)
	}
	return a
}

// set field if value is not empty. e.g. string does not equal to ""
func (a *APIUpdateOne) SetNotEmptyMethod(value string) *APIUpdateOne {
	if value != "" {
		return a.SetMethod(value)
	}
	return a
}

// set field if value is not empty. e.g. string does not equal to ""
func (a *APIUpdate) SetNotEmptyKind(value string) *APIUpdate {
	if value != "" {
		return a.SetKind(value)
	}
	return a
}

// set field if value is not empty. e.g. string does not equal to ""
func (a *APIUpdateOne) SetNotEmptyKind(value string) *APIUpdateOne {
	if value != "" {
		return a.SetKind(value)
	}
	return a
}

// set field if value is not empty. e.g. string does not equal to ""
func (a *APIUpdate) SetNotEmptyModule(value string) *APIUpdate {
	if value != "" {
		return a.SetModule(value)
	}
	return a
}

// set field if value is not empty. e.g. string does not equal to ""
func (a *APIUpdateOne) SetNotEmptyModule(value string) *APIUpdateOne {
	if value != "" {
		return a.SetModule(value)
	}
	return a
}

// set field if value is not empty. e.g. string does not equal to ""
func (a *ApplicationUpdate) SetNotEmptySort(value uint32) *ApplicationUpdate {
	if value != 0 {
		return a.SetSort(value)
	}
	return a
}

// set field if value is not empty. e.g. string does not equal to ""
func (a *ApplicationUpdateOne) SetNotEmptySort(value uint32) *ApplicationUpdateOne {
	if value != 0 {
		return a.SetSort(value)
	}
	return a
}

// set field if value is not empty. e.g. string does not equal to ""
func (a *ApplicationUpdate) SetNotEmptyTenantID(value string) *ApplicationUpdate {
	if value != "" {
		return a.SetTenantID(value)
	}
	return a
}

// set field if value is not empty. e.g. string does not equal to ""
func (a *ApplicationUpdateOne) SetNotEmptyTenantID(value string) *ApplicationUpdateOne {
	if value != "" {
		return a.SetTenantID(value)
	}
	return a
}

// set field if value is not empty. e.g. string does not equal to ""
func (a *ApplicationUpdate) SetNotEmptyName(value string) *ApplicationUpdate {
	if value != "" {
		return a.SetName(value)
	}
	return a
}

// set field if value is not empty. e.g. string does not equal to ""
func (a *ApplicationUpdateOne) SetNotEmptyName(value string) *ApplicationUpdateOne {
	if value != "" {
		return a.SetName(value)
	}
	return a
}

// set field if value is not empty. e.g. string does not equal to ""
func (a *ApplicationUpdate) SetNotEmptySecret(value string) *ApplicationUpdate {
	if value != "" {
		return a.SetSecret(value)
	}
	return a
}

// set field if value is not empty. e.g. string does not equal to ""
func (a *ApplicationUpdateOne) SetNotEmptySecret(value string) *ApplicationUpdateOne {
	if value != "" {
		return a.SetSecret(value)
	}
	return a
}

// set field if value is not empty. e.g. string does not equal to ""
func (a *ApplicationUpdate) SetNotEmptyRemark(value string) *ApplicationUpdate {
	if value != "" {
		return a.SetRemark(value)
	}
	return a
}

// set field if value is not empty. e.g. string does not equal to ""
func (a *ApplicationUpdateOne) SetNotEmptyRemark(value string) *ApplicationUpdateOne {
	if value != "" {
		return a.SetRemark(value)
	}
	return a
}

// set field if value is not empty. e.g. string does not equal to ""
func (b *ButtonUpdate) SetNotEmptySort(value uint32) *ButtonUpdate {
	if value != 0 {
		return b.SetSort(value)
	}
	return b
}

// set field if value is not empty. e.g. string does not equal to ""
func (b *ButtonUpdateOne) SetNotEmptySort(value uint32) *ButtonUpdateOne {
	if value != 0 {
		return b.SetSort(value)
	}
	return b
}

// set field if value is not empty. e.g. string does not equal to ""
func (b *ButtonUpdate) SetNotEmptyName(value string) *ButtonUpdate {
	if value != "" {
		return b.SetName(value)
	}
	return b
}

// set field if value is not empty. e.g. string does not equal to ""
func (b *ButtonUpdateOne) SetNotEmptyName(value string) *ButtonUpdateOne {
	if value != "" {
		return b.SetName(value)
	}
	return b
}

// set field if value is not empty. e.g. string does not equal to ""
func (b *ButtonUpdate) SetNotEmptyCode(value string) *ButtonUpdate {
	if value != "" {
		return b.SetCode(value)
	}
	return b
}

// set field if value is not empty. e.g. string does not equal to ""
func (b *ButtonUpdateOne) SetNotEmptyCode(value string) *ButtonUpdateOne {
	if value != "" {
		return b.SetCode(value)
	}
	return b
}

// set field if value is not empty. e.g. string does not equal to ""
func (b *ButtonUpdate) SetNotEmptyMenuID(value string) *ButtonUpdate {
	if value != "" {
		return b.SetMenuID(value)
	}
	return b
}

// set field if value is not empty. e.g. string does not equal to ""
func (b *ButtonUpdateOne) SetNotEmptyMenuID(value string) *ButtonUpdateOne {
	if value != "" {
		return b.SetMenuID(value)
	}
	return b
}

// set field if value is not empty. e.g. string does not equal to ""
func (f *FileUpdate) SetNotEmptySort(value uint32) *FileUpdate {
	if value != 0 {
		return f.SetSort(value)
	}
	return f
}

// set field if value is not empty. e.g. string does not equal to ""
func (f *FileUpdateOne) SetNotEmptySort(value uint32) *FileUpdateOne {
	if value != 0 {
		return f.SetSort(value)
	}
	return f
}

// set field if value is not empty. e.g. string does not equal to ""
func (f *FileUpdate) SetNotEmptyTenantID(value string) *FileUpdate {
	if value != "" {
		return f.SetTenantID(value)
	}
	return f
}

// set field if value is not empty. e.g. string does not equal to ""
func (f *FileUpdateOne) SetNotEmptyTenantID(value string) *FileUpdateOne {
	if value != "" {
		return f.SetTenantID(value)
	}
	return f
}

// set field if value is not empty. e.g. string does not equal to ""
func (f *FileUpdate) SetNotEmptyUUID(value string) *FileUpdate {
	if value != "" {
		return f.SetUUID(value)
	}
	return f
}

// set field if value is not empty. e.g. string does not equal to ""
func (f *FileUpdateOne) SetNotEmptyUUID(value string) *FileUpdateOne {
	if value != "" {
		return f.SetUUID(value)
	}
	return f
}

// set field if value is not empty. e.g. string does not equal to ""
func (f *FileUpdate) SetNotEmptyName(value string) *FileUpdate {
	if value != "" {
		return f.SetName(value)
	}
	return f
}

// set field if value is not empty. e.g. string does not equal to ""
func (f *FileUpdateOne) SetNotEmptyName(value string) *FileUpdateOne {
	if value != "" {
		return f.SetName(value)
	}
	return f
}

// set field if value is not empty. e.g. string does not equal to ""
func (f *FileUpdate) SetNotEmptyOriginName(value string) *FileUpdate {
	if value != "" {
		return f.SetOriginName(value)
	}
	return f
}

// set field if value is not empty. e.g. string does not equal to ""
func (f *FileUpdateOne) SetNotEmptyOriginName(value string) *FileUpdateOne {
	if value != "" {
		return f.SetOriginName(value)
	}
	return f
}

// set field if value is not empty. e.g. string does not equal to ""
func (f *FileUpdate) SetNotEmptyFileType(value uint8) *FileUpdate {
	if value != 0 {
		return f.SetFileType(value)
	}
	return f
}

// set field if value is not empty. e.g. string does not equal to ""
func (f *FileUpdateOne) SetNotEmptyFileType(value uint8) *FileUpdateOne {
	if value != 0 {
		return f.SetFileType(value)
	}
	return f
}

// set field if value is not empty. e.g. string does not equal to ""
func (f *FileUpdate) SetNotEmptySize(value uint64) *FileUpdate {
	if value != 0 {
		return f.SetSize(value)
	}
	return f
}

// set field if value is not empty. e.g. string does not equal to ""
func (f *FileUpdateOne) SetNotEmptySize(value uint64) *FileUpdateOne {
	if value != 0 {
		return f.SetSize(value)
	}
	return f
}

// set field if value is not empty. e.g. string does not equal to ""
func (f *FileUpdate) SetNotEmptyPath(value string) *FileUpdate {
	if value != "" {
		return f.SetPath(value)
	}
	return f
}

// set field if value is not empty. e.g. string does not equal to ""
func (f *FileUpdateOne) SetNotEmptyPath(value string) *FileUpdateOne {
	if value != "" {
		return f.SetPath(value)
	}
	return f
}

// set field if value is not empty. e.g. string does not equal to ""
func (f *FileUpdate) SetNotEmptyUserID(value string) *FileUpdate {
	if value != "" {
		return f.SetUserID(value)
	}
	return f
}

// set field if value is not empty. e.g. string does not equal to ""
func (f *FileUpdateOne) SetNotEmptyUserID(value string) *FileUpdateOne {
	if value != "" {
		return f.SetUserID(value)
	}
	return f
}

// set field if value is not empty. e.g. string does not equal to ""
func (f *FileUpdate) SetNotEmptyHash(value string) *FileUpdate {
	if value != "" {
		return f.SetHash(value)
	}
	return f
}

// set field if value is not empty. e.g. string does not equal to ""
func (f *FileUpdateOne) SetNotEmptyHash(value string) *FileUpdateOne {
	if value != "" {
		return f.SetHash(value)
	}
	return f
}

// set field if value is not empty. e.g. string does not equal to ""
func (f *FileUpdate) SetNotEmptyOpenStatus(value uint8) *FileUpdate {
	if value != 0 {
		return f.SetOpenStatus(value)
	}
	return f
}

// set field if value is not empty. e.g. string does not equal to ""
func (f *FileUpdateOne) SetNotEmptyOpenStatus(value uint8) *FileUpdateOne {
	if value != 0 {
		return f.SetOpenStatus(value)
	}
	return f
}

// set field if value is not empty. e.g. string does not equal to ""
func (gr *GroupUpdate) SetNotEmptySort(value uint32) *GroupUpdate {
	if value != 0 {
		return gr.SetSort(value)
	}
	return gr
}

// set field if value is not empty. e.g. string does not equal to ""
func (gr *GroupUpdateOne) SetNotEmptySort(value uint32) *GroupUpdateOne {
	if value != 0 {
		return gr.SetSort(value)
	}
	return gr
}

// set field if value is not empty. e.g. string does not equal to ""
func (gr *GroupUpdate) SetNotEmptyTenantID(value string) *GroupUpdate {
	if value != "" {
		return gr.SetTenantID(value)
	}
	return gr
}

// set field if value is not empty. e.g. string does not equal to ""
func (gr *GroupUpdateOne) SetNotEmptyTenantID(value string) *GroupUpdateOne {
	if value != "" {
		return gr.SetTenantID(value)
	}
	return gr
}

// set field if value is not empty. e.g. string does not equal to ""
func (gr *GroupUpdate) SetNotEmptyGroupTypeID(value string) *GroupUpdate {
	if value != "" {
		return gr.SetGroupTypeID(value)
	}
	return gr
}

// set field if value is not empty. e.g. string does not equal to ""
func (gr *GroupUpdateOne) SetNotEmptyGroupTypeID(value string) *GroupUpdateOne {
	if value != "" {
		return gr.SetGroupTypeID(value)
	}
	return gr
}

// set field if value is not empty. e.g. string does not equal to ""
func (gr *GroupUpdate) SetNotEmptyName(value string) *GroupUpdate {
	if value != "" {
		return gr.SetName(value)
	}
	return gr
}

// set field if value is not empty. e.g. string does not equal to ""
func (gr *GroupUpdateOne) SetNotEmptyName(value string) *GroupUpdateOne {
	if value != "" {
		return gr.SetName(value)
	}
	return gr
}

// set field if value is not empty. e.g. string does not equal to ""
func (gr *GroupUpdate) SetNotEmptyCode(value string) *GroupUpdate {
	if value != "" {
		return gr.SetCode(value)
	}
	return gr
}

// set field if value is not empty. e.g. string does not equal to ""
func (gr *GroupUpdateOne) SetNotEmptyCode(value string) *GroupUpdateOne {
	if value != "" {
		return gr.SetCode(value)
	}
	return gr
}

// set field if value is not empty. e.g. string does not equal to ""
func (gr *GroupUpdate) SetNotEmptyRemark(value string) *GroupUpdate {
	if value != "" {
		return gr.SetRemark(value)
	}
	return gr
}

// set field if value is not empty. e.g. string does not equal to ""
func (gr *GroupUpdateOne) SetNotEmptyRemark(value string) *GroupUpdateOne {
	if value != "" {
		return gr.SetRemark(value)
	}
	return gr
}

// set field if value is not empty. e.g. string does not equal to ""
func (gt *GroupTypeUpdate) SetNotEmptySort(value uint32) *GroupTypeUpdate {
	if value != 0 {
		return gt.SetSort(value)
	}
	return gt
}

// set field if value is not empty. e.g. string does not equal to ""
func (gt *GroupTypeUpdateOne) SetNotEmptySort(value uint32) *GroupTypeUpdateOne {
	if value != 0 {
		return gt.SetSort(value)
	}
	return gt
}

// set field if value is not empty. e.g. string does not equal to ""
func (gt *GroupTypeUpdate) SetNotEmptyTenantID(value string) *GroupTypeUpdate {
	if value != "" {
		return gt.SetTenantID(value)
	}
	return gt
}

// set field if value is not empty. e.g. string does not equal to ""
func (gt *GroupTypeUpdateOne) SetNotEmptyTenantID(value string) *GroupTypeUpdateOne {
	if value != "" {
		return gt.SetTenantID(value)
	}
	return gt
}

// set field if value is not empty. e.g. string does not equal to ""
func (gt *GroupTypeUpdate) SetNotEmptyName(value string) *GroupTypeUpdate {
	if value != "" {
		return gt.SetName(value)
	}
	return gt
}

// set field if value is not empty. e.g. string does not equal to ""
func (gt *GroupTypeUpdateOne) SetNotEmptyName(value string) *GroupTypeUpdateOne {
	if value != "" {
		return gt.SetName(value)
	}
	return gt
}

// set field if value is not empty. e.g. string does not equal to ""
func (gt *GroupTypeUpdate) SetNotEmptyCode(value string) *GroupTypeUpdate {
	if value != "" {
		return gt.SetCode(value)
	}
	return gt
}

// set field if value is not empty. e.g. string does not equal to ""
func (gt *GroupTypeUpdateOne) SetNotEmptyCode(value string) *GroupTypeUpdateOne {
	if value != "" {
		return gt.SetCode(value)
	}
	return gt
}

// set field if value is not empty. e.g. string does not equal to ""
func (gt *GroupTypeUpdate) SetNotEmptyRemark(value string) *GroupTypeUpdate {
	if value != "" {
		return gt.SetRemark(value)
	}
	return gt
}

// set field if value is not empty. e.g. string does not equal to ""
func (gt *GroupTypeUpdateOne) SetNotEmptyRemark(value string) *GroupTypeUpdateOne {
	if value != "" {
		return gt.SetRemark(value)
	}
	return gt
}

// set field if value is not empty. e.g. string does not equal to ""
func (m *MenuUpdate) SetNotEmptySort(value uint32) *MenuUpdate {
	if value != 0 {
		return m.SetSort(value)
	}
	return m
}

// set field if value is not empty. e.g. string does not equal to ""
func (m *MenuUpdateOne) SetNotEmptySort(value uint32) *MenuUpdateOne {
	if value != 0 {
		return m.SetSort(value)
	}
	return m
}

// set field if value is not empty. e.g. string does not equal to ""
func (m *MenuUpdate) SetNotEmptyName(value string) *MenuUpdate {
	if value != "" {
		return m.SetName(value)
	}
	return m
}

// set field if value is not empty. e.g. string does not equal to ""
func (m *MenuUpdateOne) SetNotEmptyName(value string) *MenuUpdateOne {
	if value != "" {
		return m.SetName(value)
	}
	return m
}

// set field if value is not empty. e.g. string does not equal to ""
func (m *MenuUpdate) SetNotEmptyTitle(value string) *MenuUpdate {
	if value != "" {
		return m.SetTitle(value)
	}
	return m
}

// set field if value is not empty. e.g. string does not equal to ""
func (m *MenuUpdateOne) SetNotEmptyTitle(value string) *MenuUpdateOne {
	if value != "" {
		return m.SetTitle(value)
	}
	return m
}

// set field if value is not empty. e.g. string does not equal to ""
func (m *MenuUpdate) SetNotEmptyIcon(value string) *MenuUpdate {
	if value != "" {
		return m.SetIcon(value)
	}
	return m
}

// set field if value is not empty. e.g. string does not equal to ""
func (m *MenuUpdateOne) SetNotEmptyIcon(value string) *MenuUpdateOne {
	if value != "" {
		return m.SetIcon(value)
	}
	return m
}

// set field if value is not empty. e.g. string does not equal to ""
func (m *MenuUpdate) SetNotEmptyParentID(value string) *MenuUpdate {
	if value != "" {
		return m.SetParentID(value)
	}
	return m
}

// set field if value is not empty. e.g. string does not equal to ""
func (m *MenuUpdateOne) SetNotEmptyParentID(value string) *MenuUpdateOne {
	if value != "" {
		return m.SetParentID(value)
	}
	return m
}

// set field if value is not empty. e.g. string does not equal to ""
func (m *MenuUpdate) SetNotEmptyMenuType(value uint32) *MenuUpdate {
	if value != 0 {
		return m.SetMenuType(value)
	}
	return m
}

// set field if value is not empty. e.g. string does not equal to ""
func (m *MenuUpdateOne) SetNotEmptyMenuType(value uint32) *MenuUpdateOne {
	if value != 0 {
		return m.SetMenuType(value)
	}
	return m
}

// set field if value is not empty. e.g. string does not equal to ""
func (m *MenuUpdate) SetNotEmptyURL(value string) *MenuUpdate {
	if value != "" {
		return m.SetURL(value)
	}
	return m
}

// set field if value is not empty. e.g. string does not equal to ""
func (m *MenuUpdateOne) SetNotEmptyURL(value string) *MenuUpdateOne {
	if value != "" {
		return m.SetURL(value)
	}
	return m
}

// set field if value is not empty. e.g. string does not equal to ""
func (m *MenuUpdate) SetNotEmptyRedirect(value string) *MenuUpdate {
	if value != "" {
		return m.SetRedirect(value)
	}
	return m
}

// set field if value is not empty. e.g. string does not equal to ""
func (m *MenuUpdateOne) SetNotEmptyRedirect(value string) *MenuUpdateOne {
	if value != "" {
		return m.SetRedirect(value)
	}
	return m
}

// set field if value is not empty. e.g. string does not equal to ""
func (m *MenuUpdate) SetNotEmptyComponent(value string) *MenuUpdate {
	if value != "" {
		return m.SetComponent(value)
	}
	return m
}

// set field if value is not empty. e.g. string does not equal to ""
func (m *MenuUpdateOne) SetNotEmptyComponent(value string) *MenuUpdateOne {
	if value != "" {
		return m.SetComponent(value)
	}
	return m
}

// set field if value is not empty. e.g. string does not equal to ""
func (m *MenuUpdate) SetNotEmptyRemark(value string) *MenuUpdate {
	if value != "" {
		return m.SetRemark(value)
	}
	return m
}

// set field if value is not empty. e.g. string does not equal to ""
func (m *MenuUpdateOne) SetNotEmptyRemark(value string) *MenuUpdateOne {
	if value != "" {
		return m.SetRemark(value)
	}
	return m
}

// set field if value is not empty. e.g. string does not equal to ""
func (m *MenuUpdate) SetNotEmptyMeta(value string) *MenuUpdate {
	if value != "" {
		return m.SetMeta(value)
	}
	return m
}

// set field if value is not empty. e.g. string does not equal to ""
func (m *MenuUpdateOne) SetNotEmptyMeta(value string) *MenuUpdateOne {
	if value != "" {
		return m.SetMeta(value)
	}
	return m
}

// set field if value is not empty. e.g. string does not equal to ""
func (o *OrganizationUpdate) SetNotEmptySort(value uint32) *OrganizationUpdate {
	if value != 0 {
		return o.SetSort(value)
	}
	return o
}

// set field if value is not empty. e.g. string does not equal to ""
func (o *OrganizationUpdateOne) SetNotEmptySort(value uint32) *OrganizationUpdateOne {
	if value != 0 {
		return o.SetSort(value)
	}
	return o
}

// set field if value is not empty. e.g. string does not equal to ""
func (o *OrganizationUpdate) SetNotEmptyTenantID(value string) *OrganizationUpdate {
	if value != "" {
		return o.SetTenantID(value)
	}
	return o
}

// set field if value is not empty. e.g. string does not equal to ""
func (o *OrganizationUpdateOne) SetNotEmptyTenantID(value string) *OrganizationUpdateOne {
	if value != "" {
		return o.SetTenantID(value)
	}
	return o
}

// set field if value is not empty. e.g. string does not equal to ""
func (o *OrganizationUpdate) SetNotEmptyName(value string) *OrganizationUpdate {
	if value != "" {
		return o.SetName(value)
	}
	return o
}

// set field if value is not empty. e.g. string does not equal to ""
func (o *OrganizationUpdateOne) SetNotEmptyName(value string) *OrganizationUpdateOne {
	if value != "" {
		return o.SetName(value)
	}
	return o
}

// set field if value is not empty. e.g. string does not equal to ""
func (o *OrganizationUpdate) SetNotEmptyAncestors(value string) *OrganizationUpdate {
	if value != "" {
		return o.SetAncestors(value)
	}
	return o
}

// set field if value is not empty. e.g. string does not equal to ""
func (o *OrganizationUpdateOne) SetNotEmptyAncestors(value string) *OrganizationUpdateOne {
	if value != "" {
		return o.SetAncestors(value)
	}
	return o
}

// set field if value is not empty. e.g. string does not equal to ""
func (o *OrganizationUpdate) SetNotEmptyCode(value string) *OrganizationUpdate {
	if value != "" {
		return o.SetCode(value)
	}
	return o
}

// set field if value is not empty. e.g. string does not equal to ""
func (o *OrganizationUpdateOne) SetNotEmptyCode(value string) *OrganizationUpdateOne {
	if value != "" {
		return o.SetCode(value)
	}
	return o
}

// set field if value is not empty. e.g. string does not equal to ""
func (o *OrganizationUpdate) SetNotEmptyNodeType(value uint32) *OrganizationUpdate {
	if value != 0 {
		return o.SetNodeType(value)
	}
	return o
}

// set field if value is not empty. e.g. string does not equal to ""
func (o *OrganizationUpdateOne) SetNotEmptyNodeType(value uint32) *OrganizationUpdateOne {
	if value != 0 {
		return o.SetNodeType(value)
	}
	return o
}

// set field if value is not empty. e.g. string does not equal to ""
func (o *OrganizationUpdate) SetNotEmptyLeader(value string) *OrganizationUpdate {
	if value != "" {
		return o.SetLeader(value)
	}
	return o
}

// set field if value is not empty. e.g. string does not equal to ""
func (o *OrganizationUpdateOne) SetNotEmptyLeader(value string) *OrganizationUpdateOne {
	if value != "" {
		return o.SetLeader(value)
	}
	return o
}

// set field if value is not empty. e.g. string does not equal to ""
func (o *OrganizationUpdate) SetNotEmptyPhone(value string) *OrganizationUpdate {
	if value != "" {
		return o.SetPhone(value)
	}
	return o
}

// set field if value is not empty. e.g. string does not equal to ""
func (o *OrganizationUpdateOne) SetNotEmptyPhone(value string) *OrganizationUpdateOne {
	if value != "" {
		return o.SetPhone(value)
	}
	return o
}

// set field if value is not empty. e.g. string does not equal to ""
func (o *OrganizationUpdate) SetNotEmptyEmail(value string) *OrganizationUpdate {
	if value != "" {
		return o.SetEmail(value)
	}
	return o
}

// set field if value is not empty. e.g. string does not equal to ""
func (o *OrganizationUpdateOne) SetNotEmptyEmail(value string) *OrganizationUpdateOne {
	if value != "" {
		return o.SetEmail(value)
	}
	return o
}

// set field if value is not empty. e.g. string does not equal to ""
func (o *OrganizationUpdate) SetNotEmptyRemark(value string) *OrganizationUpdate {
	if value != "" {
		return o.SetRemark(value)
	}
	return o
}

// set field if value is not empty. e.g. string does not equal to ""
func (o *OrganizationUpdateOne) SetNotEmptyRemark(value string) *OrganizationUpdateOne {
	if value != "" {
		return o.SetRemark(value)
	}
	return o
}

// set field if value is not empty. e.g. string does not equal to ""
func (o *OrganizationUpdate) SetNotEmptyParentID(value string) *OrganizationUpdate {
	if value != "" {
		return o.SetParentID(value)
	}
	return o
}

// set field if value is not empty. e.g. string does not equal to ""
func (o *OrganizationUpdateOne) SetNotEmptyParentID(value string) *OrganizationUpdateOne {
	if value != "" {
		return o.SetParentID(value)
	}
	return o
}

// set field if value is not empty. e.g. string does not equal to ""
func (oui *OrganizationUserInfoUpdate) SetNotEmptySort(value uint32) *OrganizationUserInfoUpdate {
	if value != 0 {
		return oui.SetSort(value)
	}
	return oui
}

// set field if value is not empty. e.g. string does not equal to ""
func (oui *OrganizationUserInfoUpdateOne) SetNotEmptySort(value uint32) *OrganizationUserInfoUpdateOne {
	if value != 0 {
		return oui.SetSort(value)
	}
	return oui
}

// set field if value is not empty. e.g. string does not equal to ""
func (oui *OrganizationUserInfoUpdate) SetNotEmptyOrganizationID(value string) *OrganizationUserInfoUpdate {
	if value != "" {
		return oui.SetOrganizationID(value)
	}
	return oui
}

// set field if value is not empty. e.g. string does not equal to ""
func (oui *OrganizationUserInfoUpdateOne) SetNotEmptyOrganizationID(value string) *OrganizationUserInfoUpdateOne {
	if value != "" {
		return oui.SetOrganizationID(value)
	}
	return oui
}

// set field if value is not empty. e.g. string does not equal to ""
func (oui *OrganizationUserInfoUpdate) SetNotEmptyUserID(value string) *OrganizationUserInfoUpdate {
	if value != "" {
		return oui.SetUserID(value)
	}
	return oui
}

// set field if value is not empty. e.g. string does not equal to ""
func (oui *OrganizationUserInfoUpdateOne) SetNotEmptyUserID(value string) *OrganizationUserInfoUpdateOne {
	if value != "" {
		return oui.SetUserID(value)
	}
	return oui
}

// set field if value is not empty. e.g. string does not equal to ""
func (oui *OrganizationUserInfoUpdate) SetNotEmptyExtra(value string) *OrganizationUserInfoUpdate {
	if value != "" {
		return oui.SetExtra(value)
	}
	return oui
}

// set field if value is not empty. e.g. string does not equal to ""
func (oui *OrganizationUserInfoUpdateOne) SetNotEmptyExtra(value string) *OrganizationUserInfoUpdateOne {
	if value != "" {
		return oui.SetExtra(value)
	}
	return oui
}

// set field if value is not empty. e.g. string does not equal to ""
func (po *PositionUpdate) SetNotEmptySort(value uint32) *PositionUpdate {
	if value != 0 {
		return po.SetSort(value)
	}
	return po
}

// set field if value is not empty. e.g. string does not equal to ""
func (po *PositionUpdateOne) SetNotEmptySort(value uint32) *PositionUpdateOne {
	if value != 0 {
		return po.SetSort(value)
	}
	return po
}

// set field if value is not empty. e.g. string does not equal to ""
func (po *PositionUpdate) SetNotEmptyTenantID(value string) *PositionUpdate {
	if value != "" {
		return po.SetTenantID(value)
	}
	return po
}

// set field if value is not empty. e.g. string does not equal to ""
func (po *PositionUpdateOne) SetNotEmptyTenantID(value string) *PositionUpdateOne {
	if value != "" {
		return po.SetTenantID(value)
	}
	return po
}

// set field if value is not empty. e.g. string does not equal to ""
func (po *PositionUpdate) SetNotEmptyName(value string) *PositionUpdate {
	if value != "" {
		return po.SetName(value)
	}
	return po
}

// set field if value is not empty. e.g. string does not equal to ""
func (po *PositionUpdateOne) SetNotEmptyName(value string) *PositionUpdateOne {
	if value != "" {
		return po.SetName(value)
	}
	return po
}

// set field if value is not empty. e.g. string does not equal to ""
func (po *PositionUpdate) SetNotEmptyCode(value string) *PositionUpdate {
	if value != "" {
		return po.SetCode(value)
	}
	return po
}

// set field if value is not empty. e.g. string does not equal to ""
func (po *PositionUpdateOne) SetNotEmptyCode(value string) *PositionUpdateOne {
	if value != "" {
		return po.SetCode(value)
	}
	return po
}

// set field if value is not empty. e.g. string does not equal to ""
func (po *PositionUpdate) SetNotEmptyOrganizationID(value string) *PositionUpdate {
	if value != "" {
		return po.SetOrganizationID(value)
	}
	return po
}

// set field if value is not empty. e.g. string does not equal to ""
func (po *PositionUpdateOne) SetNotEmptyOrganizationID(value string) *PositionUpdateOne {
	if value != "" {
		return po.SetOrganizationID(value)
	}
	return po
}

// set field if value is not empty. e.g. string does not equal to ""
func (po *PositionUpdate) SetNotEmptyRemark(value string) *PositionUpdate {
	if value != "" {
		return po.SetRemark(value)
	}
	return po
}

// set field if value is not empty. e.g. string does not equal to ""
func (po *PositionUpdateOne) SetNotEmptyRemark(value string) *PositionUpdateOne {
	if value != "" {
		return po.SetRemark(value)
	}
	return po
}

// set field if value is not empty. e.g. string does not equal to ""
func (r *RoleUpdate) SetNotEmptySort(value uint32) *RoleUpdate {
	if value != 0 {
		return r.SetSort(value)
	}
	return r
}

// set field if value is not empty. e.g. string does not equal to ""
func (r *RoleUpdateOne) SetNotEmptySort(value uint32) *RoleUpdateOne {
	if value != 0 {
		return r.SetSort(value)
	}
	return r
}

// set field if value is not empty. e.g. string does not equal to ""
func (r *RoleUpdate) SetNotEmptyTenantID(value string) *RoleUpdate {
	if value != "" {
		return r.SetTenantID(value)
	}
	return r
}

// set field if value is not empty. e.g. string does not equal to ""
func (r *RoleUpdateOne) SetNotEmptyTenantID(value string) *RoleUpdateOne {
	if value != "" {
		return r.SetTenantID(value)
	}
	return r
}

// set field if value is not empty. e.g. string does not equal to ""
func (r *RoleUpdate) SetNotEmptyName(value string) *RoleUpdate {
	if value != "" {
		return r.SetName(value)
	}
	return r
}

// set field if value is not empty. e.g. string does not equal to ""
func (r *RoleUpdateOne) SetNotEmptyName(value string) *RoleUpdateOne {
	if value != "" {
		return r.SetName(value)
	}
	return r
}

// set field if value is not empty. e.g. string does not equal to ""
func (r *RoleUpdate) SetNotEmptyCode(value string) *RoleUpdate {
	if value != "" {
		return r.SetCode(value)
	}
	return r
}

// set field if value is not empty. e.g. string does not equal to ""
func (r *RoleUpdateOne) SetNotEmptyCode(value string) *RoleUpdateOne {
	if value != "" {
		return r.SetCode(value)
	}
	return r
}

// set field if value is not empty. e.g. string does not equal to ""
func (r *RoleUpdate) SetNotEmptyDefaultRouter(value string) *RoleUpdate {
	if value != "" {
		return r.SetDefaultRouter(value)
	}
	return r
}

// set field if value is not empty. e.g. string does not equal to ""
func (r *RoleUpdateOne) SetNotEmptyDefaultRouter(value string) *RoleUpdateOne {
	if value != "" {
		return r.SetDefaultRouter(value)
	}
	return r
}

// set field if value is not empty. e.g. string does not equal to ""
func (r *RoleUpdate) SetNotEmptyRemark(value string) *RoleUpdate {
	if value != "" {
		return r.SetRemark(value)
	}
	return r
}

// set field if value is not empty. e.g. string does not equal to ""
func (r *RoleUpdateOne) SetNotEmptyRemark(value string) *RoleUpdateOne {
	if value != "" {
		return r.SetRemark(value)
	}
	return r
}

// set field if value is not empty. e.g. string does not equal to ""
func (r *RoleUpdate) SetNotEmptyOrganizationID(value string) *RoleUpdate {
	if value != "" {
		return r.SetOrganizationID(value)
	}
	return r
}

// set field if value is not empty. e.g. string does not equal to ""
func (r *RoleUpdateOne) SetNotEmptyOrganizationID(value string) *RoleUpdateOne {
	if value != "" {
		return r.SetOrganizationID(value)
	}
	return r
}

// set field if value is not empty. e.g. string does not equal to ""
func (r *RoleUpdate) SetNotEmptyParentID(value string) *RoleUpdate {
	if value != "" {
		return r.SetParentID(value)
	}
	return r
}

// set field if value is not empty. e.g. string does not equal to ""
func (r *RoleUpdateOne) SetNotEmptyParentID(value string) *RoleUpdateOne {
	if value != "" {
		return r.SetParentID(value)
	}
	return r
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdate) SetNotEmptyKey(value string) *TenantUpdate {
	if value != "" {
		return t.SetKey(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdateOne) SetNotEmptyKey(value string) *TenantUpdateOne {
	if value != "" {
		return t.SetKey(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdate) SetNotEmptySecret(value string) *TenantUpdate {
	if value != "" {
		return t.SetSecret(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdateOne) SetNotEmptySecret(value string) *TenantUpdateOne {
	if value != "" {
		return t.SetSecret(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdate) SetNotEmptyName(value string) *TenantUpdate {
	if value != "" {
		return t.SetName(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdateOne) SetNotEmptyName(value string) *TenantUpdateOne {
	if value != "" {
		return t.SetName(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdate) SetNotEmptyAfterSalesContact(value string) *TenantUpdate {
	if value != "" {
		return t.SetAfterSalesContact(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdateOne) SetNotEmptyAfterSalesContact(value string) *TenantUpdateOne {
	if value != "" {
		return t.SetAfterSalesContact(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdate) SetNotEmptyLocationID(value string) *TenantUpdate {
	if value != "" {
		return t.SetLocationID(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdateOne) SetNotEmptyLocationID(value string) *TenantUpdateOne {
	if value != "" {
		return t.SetLocationID(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdate) SetNotEmptyLogSaveKeepDays(value int64) *TenantUpdate {
	if value != 0 {
		return t.SetLogSaveKeepDays(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdateOne) SetNotEmptyLogSaveKeepDays(value int64) *TenantUpdateOne {
	if value != 0 {
		return t.SetLogSaveKeepDays(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdate) SetNotEmptyMaxAttendanceUserCount(value int64) *TenantUpdate {
	if value != 0 {
		return t.SetMaxAttendanceUserCount(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdateOne) SetNotEmptyMaxAttendanceUserCount(value int64) *TenantUpdateOne {
	if value != 0 {
		return t.SetMaxAttendanceUserCount(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdate) SetNotEmptyMaxDeviceCount(value int64) *TenantUpdate {
	if value != 0 {
		return t.SetMaxDeviceCount(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdateOne) SetNotEmptyMaxDeviceCount(value int64) *TenantUpdateOne {
	if value != 0 {
		return t.SetMaxDeviceCount(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdate) SetNotEmptyMaxUploadFileSize(value int64) *TenantUpdate {
	if value != 0 {
		return t.SetMaxUploadFileSize(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdateOne) SetNotEmptyMaxUploadFileSize(value int64) *TenantUpdateOne {
	if value != 0 {
		return t.SetMaxUploadFileSize(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdate) SetNotEmptyMaxUserCount(value int64) *TenantUpdate {
	if value != 0 {
		return t.SetMaxUserCount(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdateOne) SetNotEmptyMaxUserCount(value int64) *TenantUpdateOne {
	if value != 0 {
		return t.SetMaxUserCount(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdate) SetNotEmptyPrincipal(value string) *TenantUpdate {
	if value != "" {
		return t.SetPrincipal(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdateOne) SetNotEmptyPrincipal(value string) *TenantUpdateOne {
	if value != "" {
		return t.SetPrincipal(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdate) SetNotEmptyPrincipalContactInformation(value string) *TenantUpdate {
	if value != "" {
		return t.SetPrincipalContactInformation(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdateOne) SetNotEmptyPrincipalContactInformation(value string) *TenantUpdateOne {
	if value != "" {
		return t.SetPrincipalContactInformation(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdate) SetNotEmptySaleContact(value string) *TenantUpdate {
	if value != "" {
		return t.SetSaleContact(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdateOne) SetNotEmptySaleContact(value string) *TenantUpdateOne {
	if value != "" {
		return t.SetSaleContact(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdate) SetNotEmptySecretKey(value string) *TenantUpdate {
	if value != "" {
		return t.SetSecretKey(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdateOne) SetNotEmptySecretKey(value string) *TenantUpdateOne {
	if value != "" {
		return t.SetSecretKey(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdate) SetNotEmptyMaxConferenceAgendaTitle(value int64) *TenantUpdate {
	if value != 0 {
		return t.SetMaxConferenceAgendaTitle(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TenantUpdateOne) SetNotEmptyMaxConferenceAgendaTitle(value int64) *TenantUpdateOne {
	if value != 0 {
		return t.SetMaxConferenceAgendaTitle(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (tui *TenantUserInfoUpdate) SetNotEmptySort(value uint32) *TenantUserInfoUpdate {
	if value != 0 {
		return tui.SetSort(value)
	}
	return tui
}

// set field if value is not empty. e.g. string does not equal to ""
func (tui *TenantUserInfoUpdateOne) SetNotEmptySort(value uint32) *TenantUserInfoUpdateOne {
	if value != 0 {
		return tui.SetSort(value)
	}
	return tui
}

// set field if value is not empty. e.g. string does not equal to ""
func (tui *TenantUserInfoUpdate) SetNotEmptyTenantID(value string) *TenantUserInfoUpdate {
	if value != "" {
		return tui.SetTenantID(value)
	}
	return tui
}

// set field if value is not empty. e.g. string does not equal to ""
func (tui *TenantUserInfoUpdateOne) SetNotEmptyTenantID(value string) *TenantUserInfoUpdateOne {
	if value != "" {
		return tui.SetTenantID(value)
	}
	return tui
}

// set field if value is not empty. e.g. string does not equal to ""
func (tui *TenantUserInfoUpdate) SetNotEmptyUserID(value string) *TenantUserInfoUpdate {
	if value != "" {
		return tui.SetUserID(value)
	}
	return tui
}

// set field if value is not empty. e.g. string does not equal to ""
func (tui *TenantUserInfoUpdateOne) SetNotEmptyUserID(value string) *TenantUserInfoUpdateOne {
	if value != "" {
		return tui.SetUserID(value)
	}
	return tui
}

// set field if value is not empty. e.g. string does not equal to ""
func (tui *TenantUserInfoUpdate) SetNotEmptyExtra(value string) *TenantUserInfoUpdate {
	if value != "" {
		return tui.SetExtra(value)
	}
	return tui
}

// set field if value is not empty. e.g. string does not equal to ""
func (tui *TenantUserInfoUpdateOne) SetNotEmptyExtra(value string) *TenantUserInfoUpdateOne {
	if value != "" {
		return tui.SetExtra(value)
	}
	return tui
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TokenUpdate) SetNotEmptyUID(value string) *TokenUpdate {
	if value != "" {
		return t.SetUID(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TokenUpdateOne) SetNotEmptyUID(value string) *TokenUpdateOne {
	if value != "" {
		return t.SetUID(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TokenUpdate) SetNotEmptyToken(value string) *TokenUpdate {
	if value != "" {
		return t.SetToken(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TokenUpdateOne) SetNotEmptyToken(value string) *TokenUpdateOne {
	if value != "" {
		return t.SetToken(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TokenUpdate) SetNotEmptySource(value string) *TokenUpdate {
	if value != "" {
		return t.SetSource(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TokenUpdateOne) SetNotEmptySource(value string) *TokenUpdateOne {
	if value != "" {
		return t.SetSource(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TokenUpdate) SetNotEmptyTenantID(value string) *TokenUpdate {
	if value != "" {
		return t.SetTenantID(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TokenUpdateOne) SetNotEmptyTenantID(value string) *TokenUpdateOne {
	if value != "" {
		return t.SetTenantID(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TokenUpdate) SetNotEmptyDeviceKind(value string) *TokenUpdate {
	if value != "" {
		return t.SetDeviceKind(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TokenUpdateOne) SetNotEmptyDeviceKind(value string) *TokenUpdateOne {
	if value != "" {
		return t.SetDeviceKind(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TokenUpdate) SetNotEmptyIP(value string) *TokenUpdate {
	if value != "" {
		return t.SetIP(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (t *TokenUpdateOne) SetNotEmptyIP(value string) *TokenUpdateOne {
	if value != "" {
		return t.SetIP(value)
	}
	return t
}

// set field if value is not empty. e.g. string does not equal to ""
func (u *UserUpdate) SetNotEmptyUsername(value string) *UserUpdate {
	if value != "" {
		return u.SetUsername(value)
	}
	return u
}

// set field if value is not empty. e.g. string does not equal to ""
func (u *UserUpdateOne) SetNotEmptyUsername(value string) *UserUpdateOne {
	if value != "" {
		return u.SetUsername(value)
	}
	return u
}

// set field if value is not empty. e.g. string does not equal to ""
func (u *UserUpdate) SetNotEmptyPassword(value string) *UserUpdate {
	if value != "" {
		return u.SetPassword(value)
	}
	return u
}

// set field if value is not empty. e.g. string does not equal to ""
func (u *UserUpdateOne) SetNotEmptyPassword(value string) *UserUpdateOne {
	if value != "" {
		return u.SetPassword(value)
	}
	return u
}

// set field if value is not empty. e.g. string does not equal to ""
func (u *UserUpdate) SetNotEmptyNickname(value string) *UserUpdate {
	if value != "" {
		return u.SetNickname(value)
	}
	return u
}

// set field if value is not empty. e.g. string does not equal to ""
func (u *UserUpdateOne) SetNotEmptyNickname(value string) *UserUpdateOne {
	if value != "" {
		return u.SetNickname(value)
	}
	return u
}

// set field if value is not empty. e.g. string does not equal to ""
func (u *UserUpdate) SetNotEmptyMobile(value string) *UserUpdate {
	if value != "" {
		return u.SetMobile(value)
	}
	return u
}

// set field if value is not empty. e.g. string does not equal to ""
func (u *UserUpdateOne) SetNotEmptyMobile(value string) *UserUpdateOne {
	if value != "" {
		return u.SetMobile(value)
	}
	return u
}

// set field if value is not empty. e.g. string does not equal to ""
func (u *UserUpdate) SetNotEmptyEmail(value string) *UserUpdate {
	if value != "" {
		return u.SetEmail(value)
	}
	return u
}

// set field if value is not empty. e.g. string does not equal to ""
func (u *UserUpdateOne) SetNotEmptyEmail(value string) *UserUpdateOne {
	if value != "" {
		return u.SetEmail(value)
	}
	return u
}

// set field if value is not empty. e.g. string does not equal to ""
func (u *UserUpdate) SetNotEmptyPost(value string) *UserUpdate {
	if value != "" {
		return u.SetPost(value)
	}
	return u
}

// set field if value is not empty. e.g. string does not equal to ""
func (u *UserUpdateOne) SetNotEmptyPost(value string) *UserUpdateOne {
	if value != "" {
		return u.SetPost(value)
	}
	return u
}

// set field if value is not empty. e.g. string does not equal to ""
func (u *UserUpdate) SetNotEmptyDefaultTenantID(value string) *UserUpdate {
	if value != "" {
		return u.SetDefaultTenantID(value)
	}
	return u
}

// set field if value is not empty. e.g. string does not equal to ""
func (u *UserUpdateOne) SetNotEmptyDefaultTenantID(value string) *UserUpdateOne {
	if value != "" {
		return u.SetDefaultTenantID(value)
	}
	return u
}

// set field if value is not empty. e.g. string does not equal to ""
func (u *UserUpdate) SetNotEmptyAvatarID(value string) *UserUpdate {
	if value != "" {
		return u.SetAvatarID(value)
	}
	return u
}

// set field if value is not empty. e.g. string does not equal to ""
func (u *UserUpdateOne) SetNotEmptyAvatarID(value string) *UserUpdateOne {
	if value != "" {
		return u.SetAvatarID(value)
	}
	return u
}

// set field if value is not empty. e.g. string does not equal to ""
func (u *UserUpdate) SetNotEmptyDeviceNo(value string) *UserUpdate {
	if value != "" {
		return u.SetDeviceNo(value)
	}
	return u
}

// set field if value is not empty. e.g. string does not equal to ""
func (u *UserUpdateOne) SetNotEmptyDeviceNo(value string) *UserUpdateOne {
	if value != "" {
		return u.SetDeviceNo(value)
	}
	return u
}

// set field if value is not empty. e.g. string does not equal to ""
func (u *UserUpdate) SetNotEmptyKind(value string) *UserUpdate {
	if value != "" {
		return u.SetKind(value)
	}
	return u
}

// set field if value is not empty. e.g. string does not equal to ""
func (u *UserUpdateOne) SetNotEmptyKind(value string) *UserUpdateOne {
	if value != "" {
		return u.SetKind(value)
	}
	return u
}

// set field if value is not empty. e.g. string does not equal to ""
func (u *UserUpdate) SetNotEmptyImei(value string) *UserUpdate {
	if value != "" {
		return u.SetImei(value)
	}
	return u
}

// set field if value is not empty. e.g. string does not equal to ""
func (u *UserUpdateOne) SetNotEmptyImei(value string) *UserUpdateOne {
	if value != "" {
		return u.SetImei(value)
	}
	return u
}
