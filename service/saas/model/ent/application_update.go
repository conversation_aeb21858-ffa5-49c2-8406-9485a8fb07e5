// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/application"
	"phoenix/service/saas/model/ent/predicate"
	"phoenix/service/saas/model/ent/tenant"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ApplicationUpdate is the builder for updating Application entities.
type ApplicationUpdate struct {
	config
	hooks    []Hook
	mutation *ApplicationMutation
}

// Where appends a list predicates to the ApplicationUpdate builder.
func (au *ApplicationUpdate) Where(ps ...predicate.Application) *ApplicationUpdate {
	au.mutation.Where(ps...)
	return au
}

// SetUpdatedAt sets the "updated_at" field.
func (au *ApplicationUpdate) SetUpdatedAt(t time.Time) *ApplicationUpdate {
	au.mutation.SetUpdatedAt(t)
	return au
}

// SetStatus sets the "status" field.
func (au *ApplicationUpdate) SetStatus(b bool) *ApplicationUpdate {
	au.mutation.SetStatus(b)
	return au
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (au *ApplicationUpdate) SetNillableStatus(b *bool) *ApplicationUpdate {
	if b != nil {
		au.SetStatus(*b)
	}
	return au
}

// ClearStatus clears the value of the "status" field.
func (au *ApplicationUpdate) ClearStatus() *ApplicationUpdate {
	au.mutation.ClearStatus()
	return au
}

// SetSort sets the "sort" field.
func (au *ApplicationUpdate) SetSort(u uint32) *ApplicationUpdate {
	au.mutation.ResetSort()
	au.mutation.SetSort(u)
	return au
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (au *ApplicationUpdate) SetNillableSort(u *uint32) *ApplicationUpdate {
	if u != nil {
		au.SetSort(*u)
	}
	return au
}

// AddSort adds u to the "sort" field.
func (au *ApplicationUpdate) AddSort(u int32) *ApplicationUpdate {
	au.mutation.AddSort(u)
	return au
}

// SetTenantID sets the "tenant_id" field.
func (au *ApplicationUpdate) SetTenantID(s string) *ApplicationUpdate {
	au.mutation.SetTenantID(s)
	return au
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (au *ApplicationUpdate) SetNillableTenantID(s *string) *ApplicationUpdate {
	if s != nil {
		au.SetTenantID(*s)
	}
	return au
}

// SetDeletedAt sets the "deleted_at" field.
func (au *ApplicationUpdate) SetDeletedAt(t time.Time) *ApplicationUpdate {
	au.mutation.SetDeletedAt(t)
	return au
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (au *ApplicationUpdate) SetNillableDeletedAt(t *time.Time) *ApplicationUpdate {
	if t != nil {
		au.SetDeletedAt(*t)
	}
	return au
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (au *ApplicationUpdate) ClearDeletedAt() *ApplicationUpdate {
	au.mutation.ClearDeletedAt()
	return au
}

// SetName sets the "name" field.
func (au *ApplicationUpdate) SetName(s string) *ApplicationUpdate {
	au.mutation.SetName(s)
	return au
}

// SetNillableName sets the "name" field if the given value is not nil.
func (au *ApplicationUpdate) SetNillableName(s *string) *ApplicationUpdate {
	if s != nil {
		au.SetName(*s)
	}
	return au
}

// SetSecret sets the "secret" field.
func (au *ApplicationUpdate) SetSecret(s string) *ApplicationUpdate {
	au.mutation.SetSecret(s)
	return au
}

// SetNillableSecret sets the "secret" field if the given value is not nil.
func (au *ApplicationUpdate) SetNillableSecret(s *string) *ApplicationUpdate {
	if s != nil {
		au.SetSecret(*s)
	}
	return au
}

// SetRemark sets the "remark" field.
func (au *ApplicationUpdate) SetRemark(s string) *ApplicationUpdate {
	au.mutation.SetRemark(s)
	return au
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (au *ApplicationUpdate) SetNillableRemark(s *string) *ApplicationUpdate {
	if s != nil {
		au.SetRemark(*s)
	}
	return au
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (au *ApplicationUpdate) SetTenant(t *Tenant) *ApplicationUpdate {
	return au.SetTenantID(t.ID)
}

// Mutation returns the ApplicationMutation object of the builder.
func (au *ApplicationUpdate) Mutation() *ApplicationMutation {
	return au.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (au *ApplicationUpdate) ClearTenant() *ApplicationUpdate {
	au.mutation.ClearTenant()
	return au
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (au *ApplicationUpdate) Save(ctx context.Context) (int, error) {
	if err := au.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, au.sqlSave, au.mutation, au.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (au *ApplicationUpdate) SaveX(ctx context.Context) int {
	affected, err := au.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (au *ApplicationUpdate) Exec(ctx context.Context) error {
	_, err := au.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (au *ApplicationUpdate) ExecX(ctx context.Context) {
	if err := au.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (au *ApplicationUpdate) defaults() error {
	if _, ok := au.mutation.UpdatedAt(); !ok {
		if application.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized application.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := application.UpdateDefaultUpdatedAt()
		au.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (au *ApplicationUpdate) check() error {
	if _, ok := au.mutation.TenantID(); au.mutation.TenantCleared() && !ok {
		return errors.New(`ent: clearing a required unique edge "Application.tenant"`)
	}
	return nil
}

func (au *ApplicationUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := au.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(application.Table, application.Columns, sqlgraph.NewFieldSpec(application.FieldID, field.TypeString))
	if ps := au.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := au.mutation.UpdatedAt(); ok {
		_spec.SetField(application.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := au.mutation.Status(); ok {
		_spec.SetField(application.FieldStatus, field.TypeBool, value)
	}
	if au.mutation.StatusCleared() {
		_spec.ClearField(application.FieldStatus, field.TypeBool)
	}
	if value, ok := au.mutation.Sort(); ok {
		_spec.SetField(application.FieldSort, field.TypeUint32, value)
	}
	if value, ok := au.mutation.AddedSort(); ok {
		_spec.AddField(application.FieldSort, field.TypeUint32, value)
	}
	if value, ok := au.mutation.DeletedAt(); ok {
		_spec.SetField(application.FieldDeletedAt, field.TypeTime, value)
	}
	if au.mutation.DeletedAtCleared() {
		_spec.ClearField(application.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := au.mutation.Name(); ok {
		_spec.SetField(application.FieldName, field.TypeString, value)
	}
	if value, ok := au.mutation.Secret(); ok {
		_spec.SetField(application.FieldSecret, field.TypeString, value)
	}
	if value, ok := au.mutation.Remark(); ok {
		_spec.SetField(application.FieldRemark, field.TypeString, value)
	}
	if au.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   application.TenantTable,
			Columns: []string{application.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := au.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   application.TenantTable,
			Columns: []string{application.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, au.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{application.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	au.mutation.done = true
	return n, nil
}

// ApplicationUpdateOne is the builder for updating a single Application entity.
type ApplicationUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *ApplicationMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (auo *ApplicationUpdateOne) SetUpdatedAt(t time.Time) *ApplicationUpdateOne {
	auo.mutation.SetUpdatedAt(t)
	return auo
}

// SetStatus sets the "status" field.
func (auo *ApplicationUpdateOne) SetStatus(b bool) *ApplicationUpdateOne {
	auo.mutation.SetStatus(b)
	return auo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (auo *ApplicationUpdateOne) SetNillableStatus(b *bool) *ApplicationUpdateOne {
	if b != nil {
		auo.SetStatus(*b)
	}
	return auo
}

// ClearStatus clears the value of the "status" field.
func (auo *ApplicationUpdateOne) ClearStatus() *ApplicationUpdateOne {
	auo.mutation.ClearStatus()
	return auo
}

// SetSort sets the "sort" field.
func (auo *ApplicationUpdateOne) SetSort(u uint32) *ApplicationUpdateOne {
	auo.mutation.ResetSort()
	auo.mutation.SetSort(u)
	return auo
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (auo *ApplicationUpdateOne) SetNillableSort(u *uint32) *ApplicationUpdateOne {
	if u != nil {
		auo.SetSort(*u)
	}
	return auo
}

// AddSort adds u to the "sort" field.
func (auo *ApplicationUpdateOne) AddSort(u int32) *ApplicationUpdateOne {
	auo.mutation.AddSort(u)
	return auo
}

// SetTenantID sets the "tenant_id" field.
func (auo *ApplicationUpdateOne) SetTenantID(s string) *ApplicationUpdateOne {
	auo.mutation.SetTenantID(s)
	return auo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (auo *ApplicationUpdateOne) SetNillableTenantID(s *string) *ApplicationUpdateOne {
	if s != nil {
		auo.SetTenantID(*s)
	}
	return auo
}

// SetDeletedAt sets the "deleted_at" field.
func (auo *ApplicationUpdateOne) SetDeletedAt(t time.Time) *ApplicationUpdateOne {
	auo.mutation.SetDeletedAt(t)
	return auo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (auo *ApplicationUpdateOne) SetNillableDeletedAt(t *time.Time) *ApplicationUpdateOne {
	if t != nil {
		auo.SetDeletedAt(*t)
	}
	return auo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (auo *ApplicationUpdateOne) ClearDeletedAt() *ApplicationUpdateOne {
	auo.mutation.ClearDeletedAt()
	return auo
}

// SetName sets the "name" field.
func (auo *ApplicationUpdateOne) SetName(s string) *ApplicationUpdateOne {
	auo.mutation.SetName(s)
	return auo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (auo *ApplicationUpdateOne) SetNillableName(s *string) *ApplicationUpdateOne {
	if s != nil {
		auo.SetName(*s)
	}
	return auo
}

// SetSecret sets the "secret" field.
func (auo *ApplicationUpdateOne) SetSecret(s string) *ApplicationUpdateOne {
	auo.mutation.SetSecret(s)
	return auo
}

// SetNillableSecret sets the "secret" field if the given value is not nil.
func (auo *ApplicationUpdateOne) SetNillableSecret(s *string) *ApplicationUpdateOne {
	if s != nil {
		auo.SetSecret(*s)
	}
	return auo
}

// SetRemark sets the "remark" field.
func (auo *ApplicationUpdateOne) SetRemark(s string) *ApplicationUpdateOne {
	auo.mutation.SetRemark(s)
	return auo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (auo *ApplicationUpdateOne) SetNillableRemark(s *string) *ApplicationUpdateOne {
	if s != nil {
		auo.SetRemark(*s)
	}
	return auo
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (auo *ApplicationUpdateOne) SetTenant(t *Tenant) *ApplicationUpdateOne {
	return auo.SetTenantID(t.ID)
}

// Mutation returns the ApplicationMutation object of the builder.
func (auo *ApplicationUpdateOne) Mutation() *ApplicationMutation {
	return auo.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (auo *ApplicationUpdateOne) ClearTenant() *ApplicationUpdateOne {
	auo.mutation.ClearTenant()
	return auo
}

// Where appends a list predicates to the ApplicationUpdate builder.
func (auo *ApplicationUpdateOne) Where(ps ...predicate.Application) *ApplicationUpdateOne {
	auo.mutation.Where(ps...)
	return auo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (auo *ApplicationUpdateOne) Select(field string, fields ...string) *ApplicationUpdateOne {
	auo.fields = append([]string{field}, fields...)
	return auo
}

// Save executes the query and returns the updated Application entity.
func (auo *ApplicationUpdateOne) Save(ctx context.Context) (*Application, error) {
	if err := auo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, auo.sqlSave, auo.mutation, auo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (auo *ApplicationUpdateOne) SaveX(ctx context.Context) *Application {
	node, err := auo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (auo *ApplicationUpdateOne) Exec(ctx context.Context) error {
	_, err := auo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (auo *ApplicationUpdateOne) ExecX(ctx context.Context) {
	if err := auo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (auo *ApplicationUpdateOne) defaults() error {
	if _, ok := auo.mutation.UpdatedAt(); !ok {
		if application.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized application.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := application.UpdateDefaultUpdatedAt()
		auo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (auo *ApplicationUpdateOne) check() error {
	if _, ok := auo.mutation.TenantID(); auo.mutation.TenantCleared() && !ok {
		return errors.New(`ent: clearing a required unique edge "Application.tenant"`)
	}
	return nil
}

func (auo *ApplicationUpdateOne) sqlSave(ctx context.Context) (_node *Application, err error) {
	if err := auo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(application.Table, application.Columns, sqlgraph.NewFieldSpec(application.FieldID, field.TypeString))
	id, ok := auo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Application.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := auo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, application.FieldID)
		for _, f := range fields {
			if !application.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != application.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := auo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := auo.mutation.UpdatedAt(); ok {
		_spec.SetField(application.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := auo.mutation.Status(); ok {
		_spec.SetField(application.FieldStatus, field.TypeBool, value)
	}
	if auo.mutation.StatusCleared() {
		_spec.ClearField(application.FieldStatus, field.TypeBool)
	}
	if value, ok := auo.mutation.Sort(); ok {
		_spec.SetField(application.FieldSort, field.TypeUint32, value)
	}
	if value, ok := auo.mutation.AddedSort(); ok {
		_spec.AddField(application.FieldSort, field.TypeUint32, value)
	}
	if value, ok := auo.mutation.DeletedAt(); ok {
		_spec.SetField(application.FieldDeletedAt, field.TypeTime, value)
	}
	if auo.mutation.DeletedAtCleared() {
		_spec.ClearField(application.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := auo.mutation.Name(); ok {
		_spec.SetField(application.FieldName, field.TypeString, value)
	}
	if value, ok := auo.mutation.Secret(); ok {
		_spec.SetField(application.FieldSecret, field.TypeString, value)
	}
	if value, ok := auo.mutation.Remark(); ok {
		_spec.SetField(application.FieldRemark, field.TypeString, value)
	}
	if auo.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   application.TenantTable,
			Columns: []string{application.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := auo.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   application.TenantTable,
			Columns: []string{application.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Application{config: auo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, auo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{application.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	auo.mutation.done = true
	return _node, nil
}
