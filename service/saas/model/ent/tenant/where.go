// Code generated by ent, DO NOT EDIT.

package tenant

import (
	"phoenix/service/saas/model/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	uuid "github.com/gofrs/uuid/v5"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContainsFold(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldUpdatedAt, v))
}

// UUID applies equality check predicate on the "uuid" field. It's identical to UUIDEQ.
func UUID(v uuid.UUID) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldUUID, v))
}

// Key applies equality check predicate on the "key" field. It's identical to KeyEQ.
func Key(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldKey, v))
}

// Secret applies equality check predicate on the "secret" field. It's identical to SecretEQ.
func Secret(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldSecret, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v bool) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldStatus, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldDeletedAt, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldName, v))
}

// IsSuper applies equality check predicate on the "is_super" field. It's identical to IsSuperEQ.
func IsSuper(v bool) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldIsSuper, v))
}

// ServiceStartAt applies equality check predicate on the "service_start_at" field. It's identical to ServiceStartAtEQ.
func ServiceStartAt(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldServiceStartAt, v))
}

// ServiceEndAt applies equality check predicate on the "service_end_at" field. It's identical to ServiceEndAtEQ.
func ServiceEndAt(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldServiceEndAt, v))
}

// AfterSalesContact applies equality check predicate on the "after_sales_contact" field. It's identical to AfterSalesContactEQ.
func AfterSalesContact(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldAfterSalesContact, v))
}

// LocationID applies equality check predicate on the "location_id" field. It's identical to LocationIDEQ.
func LocationID(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldLocationID, v))
}

// LogSaveKeepDays applies equality check predicate on the "log_save_keep_days" field. It's identical to LogSaveKeepDaysEQ.
func LogSaveKeepDays(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldLogSaveKeepDays, v))
}

// MaxAttendanceUserCount applies equality check predicate on the "max_attendance_user_count" field. It's identical to MaxAttendanceUserCountEQ.
func MaxAttendanceUserCount(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldMaxAttendanceUserCount, v))
}

// MaxDeviceCount applies equality check predicate on the "max_device_count" field. It's identical to MaxDeviceCountEQ.
func MaxDeviceCount(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldMaxDeviceCount, v))
}

// MaxUploadFileSize applies equality check predicate on the "max_upload_file_size" field. It's identical to MaxUploadFileSizeEQ.
func MaxUploadFileSize(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldMaxUploadFileSize, v))
}

// MaxUserCount applies equality check predicate on the "max_user_count" field. It's identical to MaxUserCountEQ.
func MaxUserCount(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldMaxUserCount, v))
}

// Principal applies equality check predicate on the "principal" field. It's identical to PrincipalEQ.
func Principal(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldPrincipal, v))
}

// PrincipalContactInformation applies equality check predicate on the "principal_contact_information" field. It's identical to PrincipalContactInformationEQ.
func PrincipalContactInformation(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldPrincipalContactInformation, v))
}

// SaleContact applies equality check predicate on the "sale_contact" field. It's identical to SaleContactEQ.
func SaleContact(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldSaleContact, v))
}

// SecretKey applies equality check predicate on the "secret_key" field. It's identical to SecretKeyEQ.
func SecretKey(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldSecretKey, v))
}

// AiStatus applies equality check predicate on the "ai_status" field. It's identical to AiStatusEQ.
func AiStatus(v bool) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldAiStatus, v))
}

// MaxConferenceAgendaTitle applies equality check predicate on the "max_conference_agenda_title" field. It's identical to MaxConferenceAgendaTitleEQ.
func MaxConferenceAgendaTitle(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldMaxConferenceAgendaTitle, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldUpdatedAt, v))
}

// UUIDEQ applies the EQ predicate on the "uuid" field.
func UUIDEQ(v uuid.UUID) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldUUID, v))
}

// UUIDNEQ applies the NEQ predicate on the "uuid" field.
func UUIDNEQ(v uuid.UUID) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldUUID, v))
}

// UUIDIn applies the In predicate on the "uuid" field.
func UUIDIn(vs ...uuid.UUID) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldUUID, vs...))
}

// UUIDNotIn applies the NotIn predicate on the "uuid" field.
func UUIDNotIn(vs ...uuid.UUID) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldUUID, vs...))
}

// UUIDGT applies the GT predicate on the "uuid" field.
func UUIDGT(v uuid.UUID) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldUUID, v))
}

// UUIDGTE applies the GTE predicate on the "uuid" field.
func UUIDGTE(v uuid.UUID) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldUUID, v))
}

// UUIDLT applies the LT predicate on the "uuid" field.
func UUIDLT(v uuid.UUID) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldUUID, v))
}

// UUIDLTE applies the LTE predicate on the "uuid" field.
func UUIDLTE(v uuid.UUID) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldUUID, v))
}

// KeyEQ applies the EQ predicate on the "key" field.
func KeyEQ(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldKey, v))
}

// KeyNEQ applies the NEQ predicate on the "key" field.
func KeyNEQ(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldKey, v))
}

// KeyIn applies the In predicate on the "key" field.
func KeyIn(vs ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldKey, vs...))
}

// KeyNotIn applies the NotIn predicate on the "key" field.
func KeyNotIn(vs ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldKey, vs...))
}

// KeyGT applies the GT predicate on the "key" field.
func KeyGT(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldKey, v))
}

// KeyGTE applies the GTE predicate on the "key" field.
func KeyGTE(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldKey, v))
}

// KeyLT applies the LT predicate on the "key" field.
func KeyLT(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldKey, v))
}

// KeyLTE applies the LTE predicate on the "key" field.
func KeyLTE(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldKey, v))
}

// KeyContains applies the Contains predicate on the "key" field.
func KeyContains(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContains(FieldKey, v))
}

// KeyHasPrefix applies the HasPrefix predicate on the "key" field.
func KeyHasPrefix(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldHasPrefix(FieldKey, v))
}

// KeyHasSuffix applies the HasSuffix predicate on the "key" field.
func KeyHasSuffix(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldHasSuffix(FieldKey, v))
}

// KeyEqualFold applies the EqualFold predicate on the "key" field.
func KeyEqualFold(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEqualFold(FieldKey, v))
}

// KeyContainsFold applies the ContainsFold predicate on the "key" field.
func KeyContainsFold(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContainsFold(FieldKey, v))
}

// SecretEQ applies the EQ predicate on the "secret" field.
func SecretEQ(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldSecret, v))
}

// SecretNEQ applies the NEQ predicate on the "secret" field.
func SecretNEQ(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldSecret, v))
}

// SecretIn applies the In predicate on the "secret" field.
func SecretIn(vs ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldSecret, vs...))
}

// SecretNotIn applies the NotIn predicate on the "secret" field.
func SecretNotIn(vs ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldSecret, vs...))
}

// SecretGT applies the GT predicate on the "secret" field.
func SecretGT(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldSecret, v))
}

// SecretGTE applies the GTE predicate on the "secret" field.
func SecretGTE(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldSecret, v))
}

// SecretLT applies the LT predicate on the "secret" field.
func SecretLT(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldSecret, v))
}

// SecretLTE applies the LTE predicate on the "secret" field.
func SecretLTE(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldSecret, v))
}

// SecretContains applies the Contains predicate on the "secret" field.
func SecretContains(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContains(FieldSecret, v))
}

// SecretHasPrefix applies the HasPrefix predicate on the "secret" field.
func SecretHasPrefix(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldHasPrefix(FieldSecret, v))
}

// SecretHasSuffix applies the HasSuffix predicate on the "secret" field.
func SecretHasSuffix(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldHasSuffix(FieldSecret, v))
}

// SecretIsNil applies the IsNil predicate on the "secret" field.
func SecretIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldSecret))
}

// SecretNotNil applies the NotNil predicate on the "secret" field.
func SecretNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldSecret))
}

// SecretEqualFold applies the EqualFold predicate on the "secret" field.
func SecretEqualFold(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEqualFold(FieldSecret, v))
}

// SecretContainsFold applies the ContainsFold predicate on the "secret" field.
func SecretContainsFold(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContainsFold(FieldSecret, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v bool) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v bool) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldStatus, v))
}

// StatusIsNil applies the IsNil predicate on the "status" field.
func StatusIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldStatus))
}

// StatusNotNil applies the NotNil predicate on the "status" field.
func StatusNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldStatus))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldDeletedAt))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldHasSuffix(FieldName, v))
}

// NameIsNil applies the IsNil predicate on the "name" field.
func NameIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldName))
}

// NameNotNil applies the NotNil predicate on the "name" field.
func NameNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldName))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContainsFold(FieldName, v))
}

// IsSuperEQ applies the EQ predicate on the "is_super" field.
func IsSuperEQ(v bool) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldIsSuper, v))
}

// IsSuperNEQ applies the NEQ predicate on the "is_super" field.
func IsSuperNEQ(v bool) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldIsSuper, v))
}

// IsSuperIsNil applies the IsNil predicate on the "is_super" field.
func IsSuperIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldIsSuper))
}

// IsSuperNotNil applies the NotNil predicate on the "is_super" field.
func IsSuperNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldIsSuper))
}

// ServiceStartAtEQ applies the EQ predicate on the "service_start_at" field.
func ServiceStartAtEQ(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldServiceStartAt, v))
}

// ServiceStartAtNEQ applies the NEQ predicate on the "service_start_at" field.
func ServiceStartAtNEQ(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldServiceStartAt, v))
}

// ServiceStartAtIn applies the In predicate on the "service_start_at" field.
func ServiceStartAtIn(vs ...time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldServiceStartAt, vs...))
}

// ServiceStartAtNotIn applies the NotIn predicate on the "service_start_at" field.
func ServiceStartAtNotIn(vs ...time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldServiceStartAt, vs...))
}

// ServiceStartAtGT applies the GT predicate on the "service_start_at" field.
func ServiceStartAtGT(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldServiceStartAt, v))
}

// ServiceStartAtGTE applies the GTE predicate on the "service_start_at" field.
func ServiceStartAtGTE(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldServiceStartAt, v))
}

// ServiceStartAtLT applies the LT predicate on the "service_start_at" field.
func ServiceStartAtLT(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldServiceStartAt, v))
}

// ServiceStartAtLTE applies the LTE predicate on the "service_start_at" field.
func ServiceStartAtLTE(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldServiceStartAt, v))
}

// ServiceStartAtIsNil applies the IsNil predicate on the "service_start_at" field.
func ServiceStartAtIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldServiceStartAt))
}

// ServiceStartAtNotNil applies the NotNil predicate on the "service_start_at" field.
func ServiceStartAtNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldServiceStartAt))
}

// ServiceEndAtEQ applies the EQ predicate on the "service_end_at" field.
func ServiceEndAtEQ(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldServiceEndAt, v))
}

// ServiceEndAtNEQ applies the NEQ predicate on the "service_end_at" field.
func ServiceEndAtNEQ(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldServiceEndAt, v))
}

// ServiceEndAtIn applies the In predicate on the "service_end_at" field.
func ServiceEndAtIn(vs ...time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldServiceEndAt, vs...))
}

// ServiceEndAtNotIn applies the NotIn predicate on the "service_end_at" field.
func ServiceEndAtNotIn(vs ...time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldServiceEndAt, vs...))
}

// ServiceEndAtGT applies the GT predicate on the "service_end_at" field.
func ServiceEndAtGT(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldServiceEndAt, v))
}

// ServiceEndAtGTE applies the GTE predicate on the "service_end_at" field.
func ServiceEndAtGTE(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldServiceEndAt, v))
}

// ServiceEndAtLT applies the LT predicate on the "service_end_at" field.
func ServiceEndAtLT(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldServiceEndAt, v))
}

// ServiceEndAtLTE applies the LTE predicate on the "service_end_at" field.
func ServiceEndAtLTE(v time.Time) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldServiceEndAt, v))
}

// ServiceEndAtIsNil applies the IsNil predicate on the "service_end_at" field.
func ServiceEndAtIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldServiceEndAt))
}

// ServiceEndAtNotNil applies the NotNil predicate on the "service_end_at" field.
func ServiceEndAtNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldServiceEndAt))
}

// AfterSalesContactEQ applies the EQ predicate on the "after_sales_contact" field.
func AfterSalesContactEQ(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldAfterSalesContact, v))
}

// AfterSalesContactNEQ applies the NEQ predicate on the "after_sales_contact" field.
func AfterSalesContactNEQ(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldAfterSalesContact, v))
}

// AfterSalesContactIn applies the In predicate on the "after_sales_contact" field.
func AfterSalesContactIn(vs ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldAfterSalesContact, vs...))
}

// AfterSalesContactNotIn applies the NotIn predicate on the "after_sales_contact" field.
func AfterSalesContactNotIn(vs ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldAfterSalesContact, vs...))
}

// AfterSalesContactGT applies the GT predicate on the "after_sales_contact" field.
func AfterSalesContactGT(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldAfterSalesContact, v))
}

// AfterSalesContactGTE applies the GTE predicate on the "after_sales_contact" field.
func AfterSalesContactGTE(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldAfterSalesContact, v))
}

// AfterSalesContactLT applies the LT predicate on the "after_sales_contact" field.
func AfterSalesContactLT(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldAfterSalesContact, v))
}

// AfterSalesContactLTE applies the LTE predicate on the "after_sales_contact" field.
func AfterSalesContactLTE(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldAfterSalesContact, v))
}

// AfterSalesContactContains applies the Contains predicate on the "after_sales_contact" field.
func AfterSalesContactContains(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContains(FieldAfterSalesContact, v))
}

// AfterSalesContactHasPrefix applies the HasPrefix predicate on the "after_sales_contact" field.
func AfterSalesContactHasPrefix(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldHasPrefix(FieldAfterSalesContact, v))
}

// AfterSalesContactHasSuffix applies the HasSuffix predicate on the "after_sales_contact" field.
func AfterSalesContactHasSuffix(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldHasSuffix(FieldAfterSalesContact, v))
}

// AfterSalesContactIsNil applies the IsNil predicate on the "after_sales_contact" field.
func AfterSalesContactIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldAfterSalesContact))
}

// AfterSalesContactNotNil applies the NotNil predicate on the "after_sales_contact" field.
func AfterSalesContactNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldAfterSalesContact))
}

// AfterSalesContactEqualFold applies the EqualFold predicate on the "after_sales_contact" field.
func AfterSalesContactEqualFold(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEqualFold(FieldAfterSalesContact, v))
}

// AfterSalesContactContainsFold applies the ContainsFold predicate on the "after_sales_contact" field.
func AfterSalesContactContainsFold(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContainsFold(FieldAfterSalesContact, v))
}

// LocationIDEQ applies the EQ predicate on the "location_id" field.
func LocationIDEQ(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldLocationID, v))
}

// LocationIDNEQ applies the NEQ predicate on the "location_id" field.
func LocationIDNEQ(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldLocationID, v))
}

// LocationIDIn applies the In predicate on the "location_id" field.
func LocationIDIn(vs ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldLocationID, vs...))
}

// LocationIDNotIn applies the NotIn predicate on the "location_id" field.
func LocationIDNotIn(vs ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldLocationID, vs...))
}

// LocationIDGT applies the GT predicate on the "location_id" field.
func LocationIDGT(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldLocationID, v))
}

// LocationIDGTE applies the GTE predicate on the "location_id" field.
func LocationIDGTE(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldLocationID, v))
}

// LocationIDLT applies the LT predicate on the "location_id" field.
func LocationIDLT(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldLocationID, v))
}

// LocationIDLTE applies the LTE predicate on the "location_id" field.
func LocationIDLTE(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldLocationID, v))
}

// LocationIDContains applies the Contains predicate on the "location_id" field.
func LocationIDContains(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContains(FieldLocationID, v))
}

// LocationIDHasPrefix applies the HasPrefix predicate on the "location_id" field.
func LocationIDHasPrefix(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldHasPrefix(FieldLocationID, v))
}

// LocationIDHasSuffix applies the HasSuffix predicate on the "location_id" field.
func LocationIDHasSuffix(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldHasSuffix(FieldLocationID, v))
}

// LocationIDIsNil applies the IsNil predicate on the "location_id" field.
func LocationIDIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldLocationID))
}

// LocationIDNotNil applies the NotNil predicate on the "location_id" field.
func LocationIDNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldLocationID))
}

// LocationIDEqualFold applies the EqualFold predicate on the "location_id" field.
func LocationIDEqualFold(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEqualFold(FieldLocationID, v))
}

// LocationIDContainsFold applies the ContainsFold predicate on the "location_id" field.
func LocationIDContainsFold(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContainsFold(FieldLocationID, v))
}

// LogSaveKeepDaysEQ applies the EQ predicate on the "log_save_keep_days" field.
func LogSaveKeepDaysEQ(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldLogSaveKeepDays, v))
}

// LogSaveKeepDaysNEQ applies the NEQ predicate on the "log_save_keep_days" field.
func LogSaveKeepDaysNEQ(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldLogSaveKeepDays, v))
}

// LogSaveKeepDaysIn applies the In predicate on the "log_save_keep_days" field.
func LogSaveKeepDaysIn(vs ...int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldLogSaveKeepDays, vs...))
}

// LogSaveKeepDaysNotIn applies the NotIn predicate on the "log_save_keep_days" field.
func LogSaveKeepDaysNotIn(vs ...int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldLogSaveKeepDays, vs...))
}

// LogSaveKeepDaysGT applies the GT predicate on the "log_save_keep_days" field.
func LogSaveKeepDaysGT(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldLogSaveKeepDays, v))
}

// LogSaveKeepDaysGTE applies the GTE predicate on the "log_save_keep_days" field.
func LogSaveKeepDaysGTE(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldLogSaveKeepDays, v))
}

// LogSaveKeepDaysLT applies the LT predicate on the "log_save_keep_days" field.
func LogSaveKeepDaysLT(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldLogSaveKeepDays, v))
}

// LogSaveKeepDaysLTE applies the LTE predicate on the "log_save_keep_days" field.
func LogSaveKeepDaysLTE(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldLogSaveKeepDays, v))
}

// LogSaveKeepDaysIsNil applies the IsNil predicate on the "log_save_keep_days" field.
func LogSaveKeepDaysIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldLogSaveKeepDays))
}

// LogSaveKeepDaysNotNil applies the NotNil predicate on the "log_save_keep_days" field.
func LogSaveKeepDaysNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldLogSaveKeepDays))
}

// MaxAttendanceUserCountEQ applies the EQ predicate on the "max_attendance_user_count" field.
func MaxAttendanceUserCountEQ(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldMaxAttendanceUserCount, v))
}

// MaxAttendanceUserCountNEQ applies the NEQ predicate on the "max_attendance_user_count" field.
func MaxAttendanceUserCountNEQ(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldMaxAttendanceUserCount, v))
}

// MaxAttendanceUserCountIn applies the In predicate on the "max_attendance_user_count" field.
func MaxAttendanceUserCountIn(vs ...int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldMaxAttendanceUserCount, vs...))
}

// MaxAttendanceUserCountNotIn applies the NotIn predicate on the "max_attendance_user_count" field.
func MaxAttendanceUserCountNotIn(vs ...int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldMaxAttendanceUserCount, vs...))
}

// MaxAttendanceUserCountGT applies the GT predicate on the "max_attendance_user_count" field.
func MaxAttendanceUserCountGT(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldMaxAttendanceUserCount, v))
}

// MaxAttendanceUserCountGTE applies the GTE predicate on the "max_attendance_user_count" field.
func MaxAttendanceUserCountGTE(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldMaxAttendanceUserCount, v))
}

// MaxAttendanceUserCountLT applies the LT predicate on the "max_attendance_user_count" field.
func MaxAttendanceUserCountLT(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldMaxAttendanceUserCount, v))
}

// MaxAttendanceUserCountLTE applies the LTE predicate on the "max_attendance_user_count" field.
func MaxAttendanceUserCountLTE(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldMaxAttendanceUserCount, v))
}

// MaxAttendanceUserCountIsNil applies the IsNil predicate on the "max_attendance_user_count" field.
func MaxAttendanceUserCountIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldMaxAttendanceUserCount))
}

// MaxAttendanceUserCountNotNil applies the NotNil predicate on the "max_attendance_user_count" field.
func MaxAttendanceUserCountNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldMaxAttendanceUserCount))
}

// MaxDeviceCountEQ applies the EQ predicate on the "max_device_count" field.
func MaxDeviceCountEQ(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldMaxDeviceCount, v))
}

// MaxDeviceCountNEQ applies the NEQ predicate on the "max_device_count" field.
func MaxDeviceCountNEQ(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldMaxDeviceCount, v))
}

// MaxDeviceCountIn applies the In predicate on the "max_device_count" field.
func MaxDeviceCountIn(vs ...int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldMaxDeviceCount, vs...))
}

// MaxDeviceCountNotIn applies the NotIn predicate on the "max_device_count" field.
func MaxDeviceCountNotIn(vs ...int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldMaxDeviceCount, vs...))
}

// MaxDeviceCountGT applies the GT predicate on the "max_device_count" field.
func MaxDeviceCountGT(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldMaxDeviceCount, v))
}

// MaxDeviceCountGTE applies the GTE predicate on the "max_device_count" field.
func MaxDeviceCountGTE(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldMaxDeviceCount, v))
}

// MaxDeviceCountLT applies the LT predicate on the "max_device_count" field.
func MaxDeviceCountLT(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldMaxDeviceCount, v))
}

// MaxDeviceCountLTE applies the LTE predicate on the "max_device_count" field.
func MaxDeviceCountLTE(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldMaxDeviceCount, v))
}

// MaxDeviceCountIsNil applies the IsNil predicate on the "max_device_count" field.
func MaxDeviceCountIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldMaxDeviceCount))
}

// MaxDeviceCountNotNil applies the NotNil predicate on the "max_device_count" field.
func MaxDeviceCountNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldMaxDeviceCount))
}

// MaxUploadFileSizeEQ applies the EQ predicate on the "max_upload_file_size" field.
func MaxUploadFileSizeEQ(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldMaxUploadFileSize, v))
}

// MaxUploadFileSizeNEQ applies the NEQ predicate on the "max_upload_file_size" field.
func MaxUploadFileSizeNEQ(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldMaxUploadFileSize, v))
}

// MaxUploadFileSizeIn applies the In predicate on the "max_upload_file_size" field.
func MaxUploadFileSizeIn(vs ...int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldMaxUploadFileSize, vs...))
}

// MaxUploadFileSizeNotIn applies the NotIn predicate on the "max_upload_file_size" field.
func MaxUploadFileSizeNotIn(vs ...int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldMaxUploadFileSize, vs...))
}

// MaxUploadFileSizeGT applies the GT predicate on the "max_upload_file_size" field.
func MaxUploadFileSizeGT(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldMaxUploadFileSize, v))
}

// MaxUploadFileSizeGTE applies the GTE predicate on the "max_upload_file_size" field.
func MaxUploadFileSizeGTE(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldMaxUploadFileSize, v))
}

// MaxUploadFileSizeLT applies the LT predicate on the "max_upload_file_size" field.
func MaxUploadFileSizeLT(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldMaxUploadFileSize, v))
}

// MaxUploadFileSizeLTE applies the LTE predicate on the "max_upload_file_size" field.
func MaxUploadFileSizeLTE(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldMaxUploadFileSize, v))
}

// MaxUploadFileSizeIsNil applies the IsNil predicate on the "max_upload_file_size" field.
func MaxUploadFileSizeIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldMaxUploadFileSize))
}

// MaxUploadFileSizeNotNil applies the NotNil predicate on the "max_upload_file_size" field.
func MaxUploadFileSizeNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldMaxUploadFileSize))
}

// MaxUserCountEQ applies the EQ predicate on the "max_user_count" field.
func MaxUserCountEQ(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldMaxUserCount, v))
}

// MaxUserCountNEQ applies the NEQ predicate on the "max_user_count" field.
func MaxUserCountNEQ(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldMaxUserCount, v))
}

// MaxUserCountIn applies the In predicate on the "max_user_count" field.
func MaxUserCountIn(vs ...int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldMaxUserCount, vs...))
}

// MaxUserCountNotIn applies the NotIn predicate on the "max_user_count" field.
func MaxUserCountNotIn(vs ...int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldMaxUserCount, vs...))
}

// MaxUserCountGT applies the GT predicate on the "max_user_count" field.
func MaxUserCountGT(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldMaxUserCount, v))
}

// MaxUserCountGTE applies the GTE predicate on the "max_user_count" field.
func MaxUserCountGTE(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldMaxUserCount, v))
}

// MaxUserCountLT applies the LT predicate on the "max_user_count" field.
func MaxUserCountLT(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldMaxUserCount, v))
}

// MaxUserCountLTE applies the LTE predicate on the "max_user_count" field.
func MaxUserCountLTE(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldMaxUserCount, v))
}

// MaxUserCountIsNil applies the IsNil predicate on the "max_user_count" field.
func MaxUserCountIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldMaxUserCount))
}

// MaxUserCountNotNil applies the NotNil predicate on the "max_user_count" field.
func MaxUserCountNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldMaxUserCount))
}

// PrincipalEQ applies the EQ predicate on the "principal" field.
func PrincipalEQ(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldPrincipal, v))
}

// PrincipalNEQ applies the NEQ predicate on the "principal" field.
func PrincipalNEQ(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldPrincipal, v))
}

// PrincipalIn applies the In predicate on the "principal" field.
func PrincipalIn(vs ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldPrincipal, vs...))
}

// PrincipalNotIn applies the NotIn predicate on the "principal" field.
func PrincipalNotIn(vs ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldPrincipal, vs...))
}

// PrincipalGT applies the GT predicate on the "principal" field.
func PrincipalGT(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldPrincipal, v))
}

// PrincipalGTE applies the GTE predicate on the "principal" field.
func PrincipalGTE(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldPrincipal, v))
}

// PrincipalLT applies the LT predicate on the "principal" field.
func PrincipalLT(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldPrincipal, v))
}

// PrincipalLTE applies the LTE predicate on the "principal" field.
func PrincipalLTE(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldPrincipal, v))
}

// PrincipalContains applies the Contains predicate on the "principal" field.
func PrincipalContains(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContains(FieldPrincipal, v))
}

// PrincipalHasPrefix applies the HasPrefix predicate on the "principal" field.
func PrincipalHasPrefix(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldHasPrefix(FieldPrincipal, v))
}

// PrincipalHasSuffix applies the HasSuffix predicate on the "principal" field.
func PrincipalHasSuffix(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldHasSuffix(FieldPrincipal, v))
}

// PrincipalIsNil applies the IsNil predicate on the "principal" field.
func PrincipalIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldPrincipal))
}

// PrincipalNotNil applies the NotNil predicate on the "principal" field.
func PrincipalNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldPrincipal))
}

// PrincipalEqualFold applies the EqualFold predicate on the "principal" field.
func PrincipalEqualFold(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEqualFold(FieldPrincipal, v))
}

// PrincipalContainsFold applies the ContainsFold predicate on the "principal" field.
func PrincipalContainsFold(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContainsFold(FieldPrincipal, v))
}

// PrincipalContactInformationEQ applies the EQ predicate on the "principal_contact_information" field.
func PrincipalContactInformationEQ(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldPrincipalContactInformation, v))
}

// PrincipalContactInformationNEQ applies the NEQ predicate on the "principal_contact_information" field.
func PrincipalContactInformationNEQ(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldPrincipalContactInformation, v))
}

// PrincipalContactInformationIn applies the In predicate on the "principal_contact_information" field.
func PrincipalContactInformationIn(vs ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldPrincipalContactInformation, vs...))
}

// PrincipalContactInformationNotIn applies the NotIn predicate on the "principal_contact_information" field.
func PrincipalContactInformationNotIn(vs ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldPrincipalContactInformation, vs...))
}

// PrincipalContactInformationGT applies the GT predicate on the "principal_contact_information" field.
func PrincipalContactInformationGT(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldPrincipalContactInformation, v))
}

// PrincipalContactInformationGTE applies the GTE predicate on the "principal_contact_information" field.
func PrincipalContactInformationGTE(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldPrincipalContactInformation, v))
}

// PrincipalContactInformationLT applies the LT predicate on the "principal_contact_information" field.
func PrincipalContactInformationLT(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldPrincipalContactInformation, v))
}

// PrincipalContactInformationLTE applies the LTE predicate on the "principal_contact_information" field.
func PrincipalContactInformationLTE(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldPrincipalContactInformation, v))
}

// PrincipalContactInformationContains applies the Contains predicate on the "principal_contact_information" field.
func PrincipalContactInformationContains(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContains(FieldPrincipalContactInformation, v))
}

// PrincipalContactInformationHasPrefix applies the HasPrefix predicate on the "principal_contact_information" field.
func PrincipalContactInformationHasPrefix(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldHasPrefix(FieldPrincipalContactInformation, v))
}

// PrincipalContactInformationHasSuffix applies the HasSuffix predicate on the "principal_contact_information" field.
func PrincipalContactInformationHasSuffix(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldHasSuffix(FieldPrincipalContactInformation, v))
}

// PrincipalContactInformationIsNil applies the IsNil predicate on the "principal_contact_information" field.
func PrincipalContactInformationIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldPrincipalContactInformation))
}

// PrincipalContactInformationNotNil applies the NotNil predicate on the "principal_contact_information" field.
func PrincipalContactInformationNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldPrincipalContactInformation))
}

// PrincipalContactInformationEqualFold applies the EqualFold predicate on the "principal_contact_information" field.
func PrincipalContactInformationEqualFold(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEqualFold(FieldPrincipalContactInformation, v))
}

// PrincipalContactInformationContainsFold applies the ContainsFold predicate on the "principal_contact_information" field.
func PrincipalContactInformationContainsFold(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContainsFold(FieldPrincipalContactInformation, v))
}

// SaleContactEQ applies the EQ predicate on the "sale_contact" field.
func SaleContactEQ(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldSaleContact, v))
}

// SaleContactNEQ applies the NEQ predicate on the "sale_contact" field.
func SaleContactNEQ(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldSaleContact, v))
}

// SaleContactIn applies the In predicate on the "sale_contact" field.
func SaleContactIn(vs ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldSaleContact, vs...))
}

// SaleContactNotIn applies the NotIn predicate on the "sale_contact" field.
func SaleContactNotIn(vs ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldSaleContact, vs...))
}

// SaleContactGT applies the GT predicate on the "sale_contact" field.
func SaleContactGT(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldSaleContact, v))
}

// SaleContactGTE applies the GTE predicate on the "sale_contact" field.
func SaleContactGTE(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldSaleContact, v))
}

// SaleContactLT applies the LT predicate on the "sale_contact" field.
func SaleContactLT(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldSaleContact, v))
}

// SaleContactLTE applies the LTE predicate on the "sale_contact" field.
func SaleContactLTE(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldSaleContact, v))
}

// SaleContactContains applies the Contains predicate on the "sale_contact" field.
func SaleContactContains(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContains(FieldSaleContact, v))
}

// SaleContactHasPrefix applies the HasPrefix predicate on the "sale_contact" field.
func SaleContactHasPrefix(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldHasPrefix(FieldSaleContact, v))
}

// SaleContactHasSuffix applies the HasSuffix predicate on the "sale_contact" field.
func SaleContactHasSuffix(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldHasSuffix(FieldSaleContact, v))
}

// SaleContactIsNil applies the IsNil predicate on the "sale_contact" field.
func SaleContactIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldSaleContact))
}

// SaleContactNotNil applies the NotNil predicate on the "sale_contact" field.
func SaleContactNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldSaleContact))
}

// SaleContactEqualFold applies the EqualFold predicate on the "sale_contact" field.
func SaleContactEqualFold(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEqualFold(FieldSaleContact, v))
}

// SaleContactContainsFold applies the ContainsFold predicate on the "sale_contact" field.
func SaleContactContainsFold(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContainsFold(FieldSaleContact, v))
}

// SecretKeyEQ applies the EQ predicate on the "secret_key" field.
func SecretKeyEQ(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldSecretKey, v))
}

// SecretKeyNEQ applies the NEQ predicate on the "secret_key" field.
func SecretKeyNEQ(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldSecretKey, v))
}

// SecretKeyIn applies the In predicate on the "secret_key" field.
func SecretKeyIn(vs ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldSecretKey, vs...))
}

// SecretKeyNotIn applies the NotIn predicate on the "secret_key" field.
func SecretKeyNotIn(vs ...string) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldSecretKey, vs...))
}

// SecretKeyGT applies the GT predicate on the "secret_key" field.
func SecretKeyGT(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldSecretKey, v))
}

// SecretKeyGTE applies the GTE predicate on the "secret_key" field.
func SecretKeyGTE(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldSecretKey, v))
}

// SecretKeyLT applies the LT predicate on the "secret_key" field.
func SecretKeyLT(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldSecretKey, v))
}

// SecretKeyLTE applies the LTE predicate on the "secret_key" field.
func SecretKeyLTE(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldSecretKey, v))
}

// SecretKeyContains applies the Contains predicate on the "secret_key" field.
func SecretKeyContains(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContains(FieldSecretKey, v))
}

// SecretKeyHasPrefix applies the HasPrefix predicate on the "secret_key" field.
func SecretKeyHasPrefix(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldHasPrefix(FieldSecretKey, v))
}

// SecretKeyHasSuffix applies the HasSuffix predicate on the "secret_key" field.
func SecretKeyHasSuffix(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldHasSuffix(FieldSecretKey, v))
}

// SecretKeyIsNil applies the IsNil predicate on the "secret_key" field.
func SecretKeyIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldSecretKey))
}

// SecretKeyNotNil applies the NotNil predicate on the "secret_key" field.
func SecretKeyNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldSecretKey))
}

// SecretKeyEqualFold applies the EqualFold predicate on the "secret_key" field.
func SecretKeyEqualFold(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldEqualFold(FieldSecretKey, v))
}

// SecretKeyContainsFold applies the ContainsFold predicate on the "secret_key" field.
func SecretKeyContainsFold(v string) predicate.Tenant {
	return predicate.Tenant(sql.FieldContainsFold(FieldSecretKey, v))
}

// AiStatusEQ applies the EQ predicate on the "ai_status" field.
func AiStatusEQ(v bool) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldAiStatus, v))
}

// AiStatusNEQ applies the NEQ predicate on the "ai_status" field.
func AiStatusNEQ(v bool) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldAiStatus, v))
}

// AiStatusIsNil applies the IsNil predicate on the "ai_status" field.
func AiStatusIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldAiStatus))
}

// AiStatusNotNil applies the NotNil predicate on the "ai_status" field.
func AiStatusNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldAiStatus))
}

// MaxConferenceAgendaTitleEQ applies the EQ predicate on the "max_conference_agenda_title" field.
func MaxConferenceAgendaTitleEQ(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldEQ(FieldMaxConferenceAgendaTitle, v))
}

// MaxConferenceAgendaTitleNEQ applies the NEQ predicate on the "max_conference_agenda_title" field.
func MaxConferenceAgendaTitleNEQ(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldNEQ(FieldMaxConferenceAgendaTitle, v))
}

// MaxConferenceAgendaTitleIn applies the In predicate on the "max_conference_agenda_title" field.
func MaxConferenceAgendaTitleIn(vs ...int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldIn(FieldMaxConferenceAgendaTitle, vs...))
}

// MaxConferenceAgendaTitleNotIn applies the NotIn predicate on the "max_conference_agenda_title" field.
func MaxConferenceAgendaTitleNotIn(vs ...int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldNotIn(FieldMaxConferenceAgendaTitle, vs...))
}

// MaxConferenceAgendaTitleGT applies the GT predicate on the "max_conference_agenda_title" field.
func MaxConferenceAgendaTitleGT(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldGT(FieldMaxConferenceAgendaTitle, v))
}

// MaxConferenceAgendaTitleGTE applies the GTE predicate on the "max_conference_agenda_title" field.
func MaxConferenceAgendaTitleGTE(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldGTE(FieldMaxConferenceAgendaTitle, v))
}

// MaxConferenceAgendaTitleLT applies the LT predicate on the "max_conference_agenda_title" field.
func MaxConferenceAgendaTitleLT(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldLT(FieldMaxConferenceAgendaTitle, v))
}

// MaxConferenceAgendaTitleLTE applies the LTE predicate on the "max_conference_agenda_title" field.
func MaxConferenceAgendaTitleLTE(v int64) predicate.Tenant {
	return predicate.Tenant(sql.FieldLTE(FieldMaxConferenceAgendaTitle, v))
}

// MaxConferenceAgendaTitleIsNil applies the IsNil predicate on the "max_conference_agenda_title" field.
func MaxConferenceAgendaTitleIsNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldIsNull(FieldMaxConferenceAgendaTitle))
}

// MaxConferenceAgendaTitleNotNil applies the NotNil predicate on the "max_conference_agenda_title" field.
func MaxConferenceAgendaTitleNotNil() predicate.Tenant {
	return predicate.Tenant(sql.FieldNotNull(FieldMaxConferenceAgendaTitle))
}

// HasUsers applies the HasEdge predicate on the "users" edge.
func HasUsers() predicate.Tenant {
	return predicate.Tenant(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, UsersTable, UsersPrimaryKey...),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasUsersWith applies the HasEdge predicate on the "users" edge with a given conditions (other predicates).
func HasUsersWith(preds ...predicate.User) predicate.Tenant {
	return predicate.Tenant(func(s *sql.Selector) {
		step := newUsersStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasMenus applies the HasEdge predicate on the "menus" edge.
func HasMenus() predicate.Tenant {
	return predicate.Tenant(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, MenusTable, MenusColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasMenusWith applies the HasEdge predicate on the "menus" edge with a given conditions (other predicates).
func HasMenusWith(preds ...predicate.Menu) predicate.Tenant {
	return predicate.Tenant(func(s *sql.Selector) {
		step := newMenusStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasApis applies the HasEdge predicate on the "apis" edge.
func HasApis() predicate.Tenant {
	return predicate.Tenant(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, ApisTable, ApisColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasApisWith applies the HasEdge predicate on the "apis" edge with a given conditions (other predicates).
func HasApisWith(preds ...predicate.API) predicate.Tenant {
	return predicate.Tenant(func(s *sql.Selector) {
		step := newApisStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasButtons applies the HasEdge predicate on the "buttons" edge.
func HasButtons() predicate.Tenant {
	return predicate.Tenant(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, ButtonsTable, ButtonsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasButtonsWith applies the HasEdge predicate on the "buttons" edge with a given conditions (other predicates).
func HasButtonsWith(preds ...predicate.Button) predicate.Tenant {
	return predicate.Tenant(func(s *sql.Selector) {
		step := newButtonsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Tenant) predicate.Tenant {
	return predicate.Tenant(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Tenant) predicate.Tenant {
	return predicate.Tenant(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Tenant) predicate.Tenant {
	return predicate.Tenant(sql.NotPredicates(p))
}
