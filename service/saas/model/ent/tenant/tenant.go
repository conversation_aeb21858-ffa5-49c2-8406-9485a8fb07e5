// Code generated by ent, DO NOT EDIT.

package tenant

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	uuid "github.com/gofrs/uuid/v5"
)

const (
	// Label holds the string label denoting the tenant type in the database.
	Label = "tenant"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldUUID holds the string denoting the uuid field in the database.
	FieldUUID = "uuid"
	// FieldKey holds the string denoting the key field in the database.
	FieldKey = "key"
	// FieldSecret holds the string denoting the secret field in the database.
	FieldSecret = "secret"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldIsSuper holds the string denoting the is_super field in the database.
	FieldIsSuper = "is_super"
	// FieldServiceStartAt holds the string denoting the service_start_at field in the database.
	FieldServiceStartAt = "service_start_at"
	// FieldServiceEndAt holds the string denoting the service_end_at field in the database.
	FieldServiceEndAt = "service_end_at"
	// FieldAfterSalesContact holds the string denoting the after_sales_contact field in the database.
	FieldAfterSalesContact = "after_sales_contact"
	// FieldLocationID holds the string denoting the location_id field in the database.
	FieldLocationID = "location_id"
	// FieldLogSaveKeepDays holds the string denoting the log_save_keep_days field in the database.
	FieldLogSaveKeepDays = "log_save_keep_days"
	// FieldMaxAttendanceUserCount holds the string denoting the max_attendance_user_count field in the database.
	FieldMaxAttendanceUserCount = "max_attendance_user_count"
	// FieldMaxDeviceCount holds the string denoting the max_device_count field in the database.
	FieldMaxDeviceCount = "max_device_count"
	// FieldMaxUploadFileSize holds the string denoting the max_upload_file_size field in the database.
	FieldMaxUploadFileSize = "max_upload_file_size"
	// FieldMaxUserCount holds the string denoting the max_user_count field in the database.
	FieldMaxUserCount = "max_user_count"
	// FieldPrincipal holds the string denoting the principal field in the database.
	FieldPrincipal = "principal"
	// FieldPrincipalContactInformation holds the string denoting the principal_contact_information field in the database.
	FieldPrincipalContactInformation = "principal_contact_information"
	// FieldSaleContact holds the string denoting the sale_contact field in the database.
	FieldSaleContact = "sale_contact"
	// FieldSecretKey holds the string denoting the secret_key field in the database.
	FieldSecretKey = "secret_key"
	// FieldAiStatus holds the string denoting the ai_status field in the database.
	FieldAiStatus = "ai_status"
	// FieldMaxConferenceAgendaTitle holds the string denoting the max_conference_agenda_title field in the database.
	FieldMaxConferenceAgendaTitle = "max_conference_agenda_title"
	// EdgeUsers holds the string denoting the users edge name in mutations.
	EdgeUsers = "users"
	// EdgeMenus holds the string denoting the menus edge name in mutations.
	EdgeMenus = "menus"
	// EdgeApis holds the string denoting the apis edge name in mutations.
	EdgeApis = "apis"
	// EdgeButtons holds the string denoting the buttons edge name in mutations.
	EdgeButtons = "buttons"
	// Table holds the table name of the tenant in the database.
	Table = "saas_tenant"
	// UsersTable is the table that holds the users relation/edge. The primary key declared below.
	UsersTable = "tenant_users"
	// UsersInverseTable is the table name for the User entity.
	// It exists in this package in order to avoid circular dependency with the "user" package.
	UsersInverseTable = "saas_user"
	// MenusTable is the table that holds the menus relation/edge.
	MenusTable = "saas_menu"
	// MenusInverseTable is the table name for the Menu entity.
	// It exists in this package in order to avoid circular dependency with the "menu" package.
	MenusInverseTable = "saas_menu"
	// MenusColumn is the table column denoting the menus relation/edge.
	MenusColumn = "tenant_menus"
	// ApisTable is the table that holds the apis relation/edge.
	ApisTable = "saas_api"
	// ApisInverseTable is the table name for the API entity.
	// It exists in this package in order to avoid circular dependency with the "api" package.
	ApisInverseTable = "saas_api"
	// ApisColumn is the table column denoting the apis relation/edge.
	ApisColumn = "tenant_apis"
	// ButtonsTable is the table that holds the buttons relation/edge.
	ButtonsTable = "saas_button"
	// ButtonsInverseTable is the table name for the Button entity.
	// It exists in this package in order to avoid circular dependency with the "button" package.
	ButtonsInverseTable = "saas_button"
	// ButtonsColumn is the table column denoting the buttons relation/edge.
	ButtonsColumn = "tenant_buttons"
)

// Columns holds all SQL columns for tenant fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldUUID,
	FieldKey,
	FieldSecret,
	FieldStatus,
	FieldDeletedAt,
	FieldName,
	FieldIsSuper,
	FieldServiceStartAt,
	FieldServiceEndAt,
	FieldAfterSalesContact,
	FieldLocationID,
	FieldLogSaveKeepDays,
	FieldMaxAttendanceUserCount,
	FieldMaxDeviceCount,
	FieldMaxUploadFileSize,
	FieldMaxUserCount,
	FieldPrincipal,
	FieldPrincipalContactInformation,
	FieldSaleContact,
	FieldSecretKey,
	FieldAiStatus,
	FieldMaxConferenceAgendaTitle,
}

var (
	// UsersPrimaryKey and UsersColumn2 are the table columns denoting the
	// primary key for the users relation (M2M).
	UsersPrimaryKey = []string{"tenant_id", "user_id"}
)

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "phoenix/service/saas/model/ent/runtime"
var (
	Hooks        [1]ent.Hook
	Interceptors [1]ent.Interceptor
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultUUID holds the default value on creation for the "uuid" field.
	DefaultUUID func() uuid.UUID
	// DefaultKey holds the default value on creation for the "key" field.
	DefaultKey func() string
	// DefaultSecret holds the default value on creation for the "secret" field.
	DefaultSecret func() string
	// DefaultStatus holds the default value on creation for the "status" field.
	DefaultStatus bool
	// DefaultName holds the default value on creation for the "name" field.
	DefaultName string
	// DefaultIsSuper holds the default value on creation for the "is_super" field.
	DefaultIsSuper bool
	// DefaultAiStatus holds the default value on creation for the "ai_status" field.
	DefaultAiStatus bool
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() string
)

// OrderOption defines the ordering options for the Tenant queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByUUID orders the results by the uuid field.
func ByUUID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUUID, opts...).ToFunc()
}

// ByKey orders the results by the key field.
func ByKey(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldKey, opts...).ToFunc()
}

// BySecret orders the results by the secret field.
func BySecret(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSecret, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByIsSuper orders the results by the is_super field.
func ByIsSuper(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsSuper, opts...).ToFunc()
}

// ByServiceStartAt orders the results by the service_start_at field.
func ByServiceStartAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldServiceStartAt, opts...).ToFunc()
}

// ByServiceEndAt orders the results by the service_end_at field.
func ByServiceEndAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldServiceEndAt, opts...).ToFunc()
}

// ByAfterSalesContact orders the results by the after_sales_contact field.
func ByAfterSalesContact(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAfterSalesContact, opts...).ToFunc()
}

// ByLocationID orders the results by the location_id field.
func ByLocationID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldLocationID, opts...).ToFunc()
}

// ByLogSaveKeepDays orders the results by the log_save_keep_days field.
func ByLogSaveKeepDays(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldLogSaveKeepDays, opts...).ToFunc()
}

// ByMaxAttendanceUserCount orders the results by the max_attendance_user_count field.
func ByMaxAttendanceUserCount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMaxAttendanceUserCount, opts...).ToFunc()
}

// ByMaxDeviceCount orders the results by the max_device_count field.
func ByMaxDeviceCount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMaxDeviceCount, opts...).ToFunc()
}

// ByMaxUploadFileSize orders the results by the max_upload_file_size field.
func ByMaxUploadFileSize(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMaxUploadFileSize, opts...).ToFunc()
}

// ByMaxUserCount orders the results by the max_user_count field.
func ByMaxUserCount(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMaxUserCount, opts...).ToFunc()
}

// ByPrincipal orders the results by the principal field.
func ByPrincipal(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPrincipal, opts...).ToFunc()
}

// ByPrincipalContactInformation orders the results by the principal_contact_information field.
func ByPrincipalContactInformation(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPrincipalContactInformation, opts...).ToFunc()
}

// BySaleContact orders the results by the sale_contact field.
func BySaleContact(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSaleContact, opts...).ToFunc()
}

// BySecretKey orders the results by the secret_key field.
func BySecretKey(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSecretKey, opts...).ToFunc()
}

// ByAiStatus orders the results by the ai_status field.
func ByAiStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAiStatus, opts...).ToFunc()
}

// ByMaxConferenceAgendaTitle orders the results by the max_conference_agenda_title field.
func ByMaxConferenceAgendaTitle(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMaxConferenceAgendaTitle, opts...).ToFunc()
}

// ByUsersCount orders the results by users count.
func ByUsersCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newUsersStep(), opts...)
	}
}

// ByUsers orders the results by users terms.
func ByUsers(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newUsersStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByMenusCount orders the results by menus count.
func ByMenusCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newMenusStep(), opts...)
	}
}

// ByMenus orders the results by menus terms.
func ByMenus(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newMenusStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByApisCount orders the results by apis count.
func ByApisCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newApisStep(), opts...)
	}
}

// ByApis orders the results by apis terms.
func ByApis(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newApisStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByButtonsCount orders the results by buttons count.
func ByButtonsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newButtonsStep(), opts...)
	}
}

// ByButtons orders the results by buttons terms.
func ByButtons(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newButtonsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newUsersStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(UsersInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2M, false, UsersTable, UsersPrimaryKey...),
	)
}
func newMenusStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(MenusInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, MenusTable, MenusColumn),
	)
}
func newApisStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(ApisInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, ApisTable, ApisColumn),
	)
}
func newButtonsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(ButtonsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, ButtonsTable, ButtonsColumn),
	)
}
