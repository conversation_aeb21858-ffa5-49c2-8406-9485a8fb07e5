// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/api"
	"phoenix/service/saas/model/ent/button"
	"phoenix/service/saas/model/ent/menu"
	"phoenix/service/saas/model/ent/predicate"
	"phoenix/service/saas/model/ent/tenant"
	"phoenix/service/saas/model/ent/user"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	uuid "github.com/gofrs/uuid/v5"
)

// TenantUpdate is the builder for updating Tenant entities.
type TenantUpdate struct {
	config
	hooks    []Hook
	mutation *TenantMutation
}

// Where appends a list predicates to the TenantUpdate builder.
func (tu *TenantUpdate) Where(ps ...predicate.Tenant) *TenantUpdate {
	tu.mutation.Where(ps...)
	return tu
}

// SetUpdatedAt sets the "updated_at" field.
func (tu *TenantUpdate) SetUpdatedAt(t time.Time) *TenantUpdate {
	tu.mutation.SetUpdatedAt(t)
	return tu
}

// SetUUID sets the "uuid" field.
func (tu *TenantUpdate) SetUUID(u uuid.UUID) *TenantUpdate {
	tu.mutation.SetUUID(u)
	return tu
}

// SetNillableUUID sets the "uuid" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableUUID(u *uuid.UUID) *TenantUpdate {
	if u != nil {
		tu.SetUUID(*u)
	}
	return tu
}

// SetKey sets the "key" field.
func (tu *TenantUpdate) SetKey(s string) *TenantUpdate {
	tu.mutation.SetKey(s)
	return tu
}

// SetNillableKey sets the "key" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableKey(s *string) *TenantUpdate {
	if s != nil {
		tu.SetKey(*s)
	}
	return tu
}

// SetSecret sets the "secret" field.
func (tu *TenantUpdate) SetSecret(s string) *TenantUpdate {
	tu.mutation.SetSecret(s)
	return tu
}

// SetNillableSecret sets the "secret" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableSecret(s *string) *TenantUpdate {
	if s != nil {
		tu.SetSecret(*s)
	}
	return tu
}

// ClearSecret clears the value of the "secret" field.
func (tu *TenantUpdate) ClearSecret() *TenantUpdate {
	tu.mutation.ClearSecret()
	return tu
}

// SetStatus sets the "status" field.
func (tu *TenantUpdate) SetStatus(b bool) *TenantUpdate {
	tu.mutation.SetStatus(b)
	return tu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableStatus(b *bool) *TenantUpdate {
	if b != nil {
		tu.SetStatus(*b)
	}
	return tu
}

// ClearStatus clears the value of the "status" field.
func (tu *TenantUpdate) ClearStatus() *TenantUpdate {
	tu.mutation.ClearStatus()
	return tu
}

// SetDeletedAt sets the "deleted_at" field.
func (tu *TenantUpdate) SetDeletedAt(t time.Time) *TenantUpdate {
	tu.mutation.SetDeletedAt(t)
	return tu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableDeletedAt(t *time.Time) *TenantUpdate {
	if t != nil {
		tu.SetDeletedAt(*t)
	}
	return tu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (tu *TenantUpdate) ClearDeletedAt() *TenantUpdate {
	tu.mutation.ClearDeletedAt()
	return tu
}

// SetName sets the "name" field.
func (tu *TenantUpdate) SetName(s string) *TenantUpdate {
	tu.mutation.SetName(s)
	return tu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableName(s *string) *TenantUpdate {
	if s != nil {
		tu.SetName(*s)
	}
	return tu
}

// ClearName clears the value of the "name" field.
func (tu *TenantUpdate) ClearName() *TenantUpdate {
	tu.mutation.ClearName()
	return tu
}

// SetIsSuper sets the "is_super" field.
func (tu *TenantUpdate) SetIsSuper(b bool) *TenantUpdate {
	tu.mutation.SetIsSuper(b)
	return tu
}

// SetNillableIsSuper sets the "is_super" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableIsSuper(b *bool) *TenantUpdate {
	if b != nil {
		tu.SetIsSuper(*b)
	}
	return tu
}

// ClearIsSuper clears the value of the "is_super" field.
func (tu *TenantUpdate) ClearIsSuper() *TenantUpdate {
	tu.mutation.ClearIsSuper()
	return tu
}

// SetServiceStartAt sets the "service_start_at" field.
func (tu *TenantUpdate) SetServiceStartAt(t time.Time) *TenantUpdate {
	tu.mutation.SetServiceStartAt(t)
	return tu
}

// SetNillableServiceStartAt sets the "service_start_at" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableServiceStartAt(t *time.Time) *TenantUpdate {
	if t != nil {
		tu.SetServiceStartAt(*t)
	}
	return tu
}

// ClearServiceStartAt clears the value of the "service_start_at" field.
func (tu *TenantUpdate) ClearServiceStartAt() *TenantUpdate {
	tu.mutation.ClearServiceStartAt()
	return tu
}

// SetServiceEndAt sets the "service_end_at" field.
func (tu *TenantUpdate) SetServiceEndAt(t time.Time) *TenantUpdate {
	tu.mutation.SetServiceEndAt(t)
	return tu
}

// SetNillableServiceEndAt sets the "service_end_at" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableServiceEndAt(t *time.Time) *TenantUpdate {
	if t != nil {
		tu.SetServiceEndAt(*t)
	}
	return tu
}

// ClearServiceEndAt clears the value of the "service_end_at" field.
func (tu *TenantUpdate) ClearServiceEndAt() *TenantUpdate {
	tu.mutation.ClearServiceEndAt()
	return tu
}

// SetAfterSalesContact sets the "after_sales_contact" field.
func (tu *TenantUpdate) SetAfterSalesContact(s string) *TenantUpdate {
	tu.mutation.SetAfterSalesContact(s)
	return tu
}

// SetNillableAfterSalesContact sets the "after_sales_contact" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableAfterSalesContact(s *string) *TenantUpdate {
	if s != nil {
		tu.SetAfterSalesContact(*s)
	}
	return tu
}

// ClearAfterSalesContact clears the value of the "after_sales_contact" field.
func (tu *TenantUpdate) ClearAfterSalesContact() *TenantUpdate {
	tu.mutation.ClearAfterSalesContact()
	return tu
}

// SetLocationID sets the "location_id" field.
func (tu *TenantUpdate) SetLocationID(s string) *TenantUpdate {
	tu.mutation.SetLocationID(s)
	return tu
}

// SetNillableLocationID sets the "location_id" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableLocationID(s *string) *TenantUpdate {
	if s != nil {
		tu.SetLocationID(*s)
	}
	return tu
}

// ClearLocationID clears the value of the "location_id" field.
func (tu *TenantUpdate) ClearLocationID() *TenantUpdate {
	tu.mutation.ClearLocationID()
	return tu
}

// SetLogSaveKeepDays sets the "log_save_keep_days" field.
func (tu *TenantUpdate) SetLogSaveKeepDays(i int64) *TenantUpdate {
	tu.mutation.ResetLogSaveKeepDays()
	tu.mutation.SetLogSaveKeepDays(i)
	return tu
}

// SetNillableLogSaveKeepDays sets the "log_save_keep_days" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableLogSaveKeepDays(i *int64) *TenantUpdate {
	if i != nil {
		tu.SetLogSaveKeepDays(*i)
	}
	return tu
}

// AddLogSaveKeepDays adds i to the "log_save_keep_days" field.
func (tu *TenantUpdate) AddLogSaveKeepDays(i int64) *TenantUpdate {
	tu.mutation.AddLogSaveKeepDays(i)
	return tu
}

// ClearLogSaveKeepDays clears the value of the "log_save_keep_days" field.
func (tu *TenantUpdate) ClearLogSaveKeepDays() *TenantUpdate {
	tu.mutation.ClearLogSaveKeepDays()
	return tu
}

// SetMaxAttendanceUserCount sets the "max_attendance_user_count" field.
func (tu *TenantUpdate) SetMaxAttendanceUserCount(i int64) *TenantUpdate {
	tu.mutation.ResetMaxAttendanceUserCount()
	tu.mutation.SetMaxAttendanceUserCount(i)
	return tu
}

// SetNillableMaxAttendanceUserCount sets the "max_attendance_user_count" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableMaxAttendanceUserCount(i *int64) *TenantUpdate {
	if i != nil {
		tu.SetMaxAttendanceUserCount(*i)
	}
	return tu
}

// AddMaxAttendanceUserCount adds i to the "max_attendance_user_count" field.
func (tu *TenantUpdate) AddMaxAttendanceUserCount(i int64) *TenantUpdate {
	tu.mutation.AddMaxAttendanceUserCount(i)
	return tu
}

// ClearMaxAttendanceUserCount clears the value of the "max_attendance_user_count" field.
func (tu *TenantUpdate) ClearMaxAttendanceUserCount() *TenantUpdate {
	tu.mutation.ClearMaxAttendanceUserCount()
	return tu
}

// SetMaxDeviceCount sets the "max_device_count" field.
func (tu *TenantUpdate) SetMaxDeviceCount(i int64) *TenantUpdate {
	tu.mutation.ResetMaxDeviceCount()
	tu.mutation.SetMaxDeviceCount(i)
	return tu
}

// SetNillableMaxDeviceCount sets the "max_device_count" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableMaxDeviceCount(i *int64) *TenantUpdate {
	if i != nil {
		tu.SetMaxDeviceCount(*i)
	}
	return tu
}

// AddMaxDeviceCount adds i to the "max_device_count" field.
func (tu *TenantUpdate) AddMaxDeviceCount(i int64) *TenantUpdate {
	tu.mutation.AddMaxDeviceCount(i)
	return tu
}

// ClearMaxDeviceCount clears the value of the "max_device_count" field.
func (tu *TenantUpdate) ClearMaxDeviceCount() *TenantUpdate {
	tu.mutation.ClearMaxDeviceCount()
	return tu
}

// SetMaxUploadFileSize sets the "max_upload_file_size" field.
func (tu *TenantUpdate) SetMaxUploadFileSize(i int64) *TenantUpdate {
	tu.mutation.ResetMaxUploadFileSize()
	tu.mutation.SetMaxUploadFileSize(i)
	return tu
}

// SetNillableMaxUploadFileSize sets the "max_upload_file_size" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableMaxUploadFileSize(i *int64) *TenantUpdate {
	if i != nil {
		tu.SetMaxUploadFileSize(*i)
	}
	return tu
}

// AddMaxUploadFileSize adds i to the "max_upload_file_size" field.
func (tu *TenantUpdate) AddMaxUploadFileSize(i int64) *TenantUpdate {
	tu.mutation.AddMaxUploadFileSize(i)
	return tu
}

// ClearMaxUploadFileSize clears the value of the "max_upload_file_size" field.
func (tu *TenantUpdate) ClearMaxUploadFileSize() *TenantUpdate {
	tu.mutation.ClearMaxUploadFileSize()
	return tu
}

// SetMaxUserCount sets the "max_user_count" field.
func (tu *TenantUpdate) SetMaxUserCount(i int64) *TenantUpdate {
	tu.mutation.ResetMaxUserCount()
	tu.mutation.SetMaxUserCount(i)
	return tu
}

// SetNillableMaxUserCount sets the "max_user_count" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableMaxUserCount(i *int64) *TenantUpdate {
	if i != nil {
		tu.SetMaxUserCount(*i)
	}
	return tu
}

// AddMaxUserCount adds i to the "max_user_count" field.
func (tu *TenantUpdate) AddMaxUserCount(i int64) *TenantUpdate {
	tu.mutation.AddMaxUserCount(i)
	return tu
}

// ClearMaxUserCount clears the value of the "max_user_count" field.
func (tu *TenantUpdate) ClearMaxUserCount() *TenantUpdate {
	tu.mutation.ClearMaxUserCount()
	return tu
}

// SetPrincipal sets the "principal" field.
func (tu *TenantUpdate) SetPrincipal(s string) *TenantUpdate {
	tu.mutation.SetPrincipal(s)
	return tu
}

// SetNillablePrincipal sets the "principal" field if the given value is not nil.
func (tu *TenantUpdate) SetNillablePrincipal(s *string) *TenantUpdate {
	if s != nil {
		tu.SetPrincipal(*s)
	}
	return tu
}

// ClearPrincipal clears the value of the "principal" field.
func (tu *TenantUpdate) ClearPrincipal() *TenantUpdate {
	tu.mutation.ClearPrincipal()
	return tu
}

// SetPrincipalContactInformation sets the "principal_contact_information" field.
func (tu *TenantUpdate) SetPrincipalContactInformation(s string) *TenantUpdate {
	tu.mutation.SetPrincipalContactInformation(s)
	return tu
}

// SetNillablePrincipalContactInformation sets the "principal_contact_information" field if the given value is not nil.
func (tu *TenantUpdate) SetNillablePrincipalContactInformation(s *string) *TenantUpdate {
	if s != nil {
		tu.SetPrincipalContactInformation(*s)
	}
	return tu
}

// ClearPrincipalContactInformation clears the value of the "principal_contact_information" field.
func (tu *TenantUpdate) ClearPrincipalContactInformation() *TenantUpdate {
	tu.mutation.ClearPrincipalContactInformation()
	return tu
}

// SetSaleContact sets the "sale_contact" field.
func (tu *TenantUpdate) SetSaleContact(s string) *TenantUpdate {
	tu.mutation.SetSaleContact(s)
	return tu
}

// SetNillableSaleContact sets the "sale_contact" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableSaleContact(s *string) *TenantUpdate {
	if s != nil {
		tu.SetSaleContact(*s)
	}
	return tu
}

// ClearSaleContact clears the value of the "sale_contact" field.
func (tu *TenantUpdate) ClearSaleContact() *TenantUpdate {
	tu.mutation.ClearSaleContact()
	return tu
}

// SetSecretKey sets the "secret_key" field.
func (tu *TenantUpdate) SetSecretKey(s string) *TenantUpdate {
	tu.mutation.SetSecretKey(s)
	return tu
}

// SetNillableSecretKey sets the "secret_key" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableSecretKey(s *string) *TenantUpdate {
	if s != nil {
		tu.SetSecretKey(*s)
	}
	return tu
}

// ClearSecretKey clears the value of the "secret_key" field.
func (tu *TenantUpdate) ClearSecretKey() *TenantUpdate {
	tu.mutation.ClearSecretKey()
	return tu
}

// SetAiStatus sets the "ai_status" field.
func (tu *TenantUpdate) SetAiStatus(b bool) *TenantUpdate {
	tu.mutation.SetAiStatus(b)
	return tu
}

// SetNillableAiStatus sets the "ai_status" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableAiStatus(b *bool) *TenantUpdate {
	if b != nil {
		tu.SetAiStatus(*b)
	}
	return tu
}

// ClearAiStatus clears the value of the "ai_status" field.
func (tu *TenantUpdate) ClearAiStatus() *TenantUpdate {
	tu.mutation.ClearAiStatus()
	return tu
}

// SetMaxConferenceAgendaTitle sets the "max_conference_agenda_title" field.
func (tu *TenantUpdate) SetMaxConferenceAgendaTitle(i int64) *TenantUpdate {
	tu.mutation.ResetMaxConferenceAgendaTitle()
	tu.mutation.SetMaxConferenceAgendaTitle(i)
	return tu
}

// SetNillableMaxConferenceAgendaTitle sets the "max_conference_agenda_title" field if the given value is not nil.
func (tu *TenantUpdate) SetNillableMaxConferenceAgendaTitle(i *int64) *TenantUpdate {
	if i != nil {
		tu.SetMaxConferenceAgendaTitle(*i)
	}
	return tu
}

// AddMaxConferenceAgendaTitle adds i to the "max_conference_agenda_title" field.
func (tu *TenantUpdate) AddMaxConferenceAgendaTitle(i int64) *TenantUpdate {
	tu.mutation.AddMaxConferenceAgendaTitle(i)
	return tu
}

// ClearMaxConferenceAgendaTitle clears the value of the "max_conference_agenda_title" field.
func (tu *TenantUpdate) ClearMaxConferenceAgendaTitle() *TenantUpdate {
	tu.mutation.ClearMaxConferenceAgendaTitle()
	return tu
}

// AddUserIDs adds the "users" edge to the User entity by IDs.
func (tu *TenantUpdate) AddUserIDs(ids ...string) *TenantUpdate {
	tu.mutation.AddUserIDs(ids...)
	return tu
}

// AddUsers adds the "users" edges to the User entity.
func (tu *TenantUpdate) AddUsers(u ...*User) *TenantUpdate {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return tu.AddUserIDs(ids...)
}

// AddMenuIDs adds the "menus" edge to the Menu entity by IDs.
func (tu *TenantUpdate) AddMenuIDs(ids ...string) *TenantUpdate {
	tu.mutation.AddMenuIDs(ids...)
	return tu
}

// AddMenus adds the "menus" edges to the Menu entity.
func (tu *TenantUpdate) AddMenus(m ...*Menu) *TenantUpdate {
	ids := make([]string, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return tu.AddMenuIDs(ids...)
}

// AddAPIIDs adds the "apis" edge to the API entity by IDs.
func (tu *TenantUpdate) AddAPIIDs(ids ...string) *TenantUpdate {
	tu.mutation.AddAPIIDs(ids...)
	return tu
}

// AddApis adds the "apis" edges to the API entity.
func (tu *TenantUpdate) AddApis(a ...*API) *TenantUpdate {
	ids := make([]string, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return tu.AddAPIIDs(ids...)
}

// AddButtonIDs adds the "buttons" edge to the Button entity by IDs.
func (tu *TenantUpdate) AddButtonIDs(ids ...string) *TenantUpdate {
	tu.mutation.AddButtonIDs(ids...)
	return tu
}

// AddButtons adds the "buttons" edges to the Button entity.
func (tu *TenantUpdate) AddButtons(b ...*Button) *TenantUpdate {
	ids := make([]string, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return tu.AddButtonIDs(ids...)
}

// Mutation returns the TenantMutation object of the builder.
func (tu *TenantUpdate) Mutation() *TenantMutation {
	return tu.mutation
}

// ClearUsers clears all "users" edges to the User entity.
func (tu *TenantUpdate) ClearUsers() *TenantUpdate {
	tu.mutation.ClearUsers()
	return tu
}

// RemoveUserIDs removes the "users" edge to User entities by IDs.
func (tu *TenantUpdate) RemoveUserIDs(ids ...string) *TenantUpdate {
	tu.mutation.RemoveUserIDs(ids...)
	return tu
}

// RemoveUsers removes "users" edges to User entities.
func (tu *TenantUpdate) RemoveUsers(u ...*User) *TenantUpdate {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return tu.RemoveUserIDs(ids...)
}

// ClearMenus clears all "menus" edges to the Menu entity.
func (tu *TenantUpdate) ClearMenus() *TenantUpdate {
	tu.mutation.ClearMenus()
	return tu
}

// RemoveMenuIDs removes the "menus" edge to Menu entities by IDs.
func (tu *TenantUpdate) RemoveMenuIDs(ids ...string) *TenantUpdate {
	tu.mutation.RemoveMenuIDs(ids...)
	return tu
}

// RemoveMenus removes "menus" edges to Menu entities.
func (tu *TenantUpdate) RemoveMenus(m ...*Menu) *TenantUpdate {
	ids := make([]string, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return tu.RemoveMenuIDs(ids...)
}

// ClearApis clears all "apis" edges to the API entity.
func (tu *TenantUpdate) ClearApis() *TenantUpdate {
	tu.mutation.ClearApis()
	return tu
}

// RemoveAPIIDs removes the "apis" edge to API entities by IDs.
func (tu *TenantUpdate) RemoveAPIIDs(ids ...string) *TenantUpdate {
	tu.mutation.RemoveAPIIDs(ids...)
	return tu
}

// RemoveApis removes "apis" edges to API entities.
func (tu *TenantUpdate) RemoveApis(a ...*API) *TenantUpdate {
	ids := make([]string, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return tu.RemoveAPIIDs(ids...)
}

// ClearButtons clears all "buttons" edges to the Button entity.
func (tu *TenantUpdate) ClearButtons() *TenantUpdate {
	tu.mutation.ClearButtons()
	return tu
}

// RemoveButtonIDs removes the "buttons" edge to Button entities by IDs.
func (tu *TenantUpdate) RemoveButtonIDs(ids ...string) *TenantUpdate {
	tu.mutation.RemoveButtonIDs(ids...)
	return tu
}

// RemoveButtons removes "buttons" edges to Button entities.
func (tu *TenantUpdate) RemoveButtons(b ...*Button) *TenantUpdate {
	ids := make([]string, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return tu.RemoveButtonIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (tu *TenantUpdate) Save(ctx context.Context) (int, error) {
	if err := tu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, tu.sqlSave, tu.mutation, tu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tu *TenantUpdate) SaveX(ctx context.Context) int {
	affected, err := tu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (tu *TenantUpdate) Exec(ctx context.Context) error {
	_, err := tu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tu *TenantUpdate) ExecX(ctx context.Context) {
	if err := tu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (tu *TenantUpdate) defaults() error {
	if _, ok := tu.mutation.UpdatedAt(); !ok {
		if tenant.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized tenant.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := tenant.UpdateDefaultUpdatedAt()
		tu.mutation.SetUpdatedAt(v)
	}
	return nil
}

func (tu *TenantUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(tenant.Table, tenant.Columns, sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString))
	if ps := tu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := tu.mutation.UpdatedAt(); ok {
		_spec.SetField(tenant.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := tu.mutation.UUID(); ok {
		_spec.SetField(tenant.FieldUUID, field.TypeUUID, value)
	}
	if value, ok := tu.mutation.Key(); ok {
		_spec.SetField(tenant.FieldKey, field.TypeString, value)
	}
	if value, ok := tu.mutation.Secret(); ok {
		_spec.SetField(tenant.FieldSecret, field.TypeString, value)
	}
	if tu.mutation.SecretCleared() {
		_spec.ClearField(tenant.FieldSecret, field.TypeString)
	}
	if value, ok := tu.mutation.Status(); ok {
		_spec.SetField(tenant.FieldStatus, field.TypeBool, value)
	}
	if tu.mutation.StatusCleared() {
		_spec.ClearField(tenant.FieldStatus, field.TypeBool)
	}
	if value, ok := tu.mutation.DeletedAt(); ok {
		_spec.SetField(tenant.FieldDeletedAt, field.TypeTime, value)
	}
	if tu.mutation.DeletedAtCleared() {
		_spec.ClearField(tenant.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := tu.mutation.Name(); ok {
		_spec.SetField(tenant.FieldName, field.TypeString, value)
	}
	if tu.mutation.NameCleared() {
		_spec.ClearField(tenant.FieldName, field.TypeString)
	}
	if value, ok := tu.mutation.IsSuper(); ok {
		_spec.SetField(tenant.FieldIsSuper, field.TypeBool, value)
	}
	if tu.mutation.IsSuperCleared() {
		_spec.ClearField(tenant.FieldIsSuper, field.TypeBool)
	}
	if value, ok := tu.mutation.ServiceStartAt(); ok {
		_spec.SetField(tenant.FieldServiceStartAt, field.TypeTime, value)
	}
	if tu.mutation.ServiceStartAtCleared() {
		_spec.ClearField(tenant.FieldServiceStartAt, field.TypeTime)
	}
	if value, ok := tu.mutation.ServiceEndAt(); ok {
		_spec.SetField(tenant.FieldServiceEndAt, field.TypeTime, value)
	}
	if tu.mutation.ServiceEndAtCleared() {
		_spec.ClearField(tenant.FieldServiceEndAt, field.TypeTime)
	}
	if value, ok := tu.mutation.AfterSalesContact(); ok {
		_spec.SetField(tenant.FieldAfterSalesContact, field.TypeString, value)
	}
	if tu.mutation.AfterSalesContactCleared() {
		_spec.ClearField(tenant.FieldAfterSalesContact, field.TypeString)
	}
	if value, ok := tu.mutation.LocationID(); ok {
		_spec.SetField(tenant.FieldLocationID, field.TypeString, value)
	}
	if tu.mutation.LocationIDCleared() {
		_spec.ClearField(tenant.FieldLocationID, field.TypeString)
	}
	if value, ok := tu.mutation.LogSaveKeepDays(); ok {
		_spec.SetField(tenant.FieldLogSaveKeepDays, field.TypeInt64, value)
	}
	if value, ok := tu.mutation.AddedLogSaveKeepDays(); ok {
		_spec.AddField(tenant.FieldLogSaveKeepDays, field.TypeInt64, value)
	}
	if tu.mutation.LogSaveKeepDaysCleared() {
		_spec.ClearField(tenant.FieldLogSaveKeepDays, field.TypeInt64)
	}
	if value, ok := tu.mutation.MaxAttendanceUserCount(); ok {
		_spec.SetField(tenant.FieldMaxAttendanceUserCount, field.TypeInt64, value)
	}
	if value, ok := tu.mutation.AddedMaxAttendanceUserCount(); ok {
		_spec.AddField(tenant.FieldMaxAttendanceUserCount, field.TypeInt64, value)
	}
	if tu.mutation.MaxAttendanceUserCountCleared() {
		_spec.ClearField(tenant.FieldMaxAttendanceUserCount, field.TypeInt64)
	}
	if value, ok := tu.mutation.MaxDeviceCount(); ok {
		_spec.SetField(tenant.FieldMaxDeviceCount, field.TypeInt64, value)
	}
	if value, ok := tu.mutation.AddedMaxDeviceCount(); ok {
		_spec.AddField(tenant.FieldMaxDeviceCount, field.TypeInt64, value)
	}
	if tu.mutation.MaxDeviceCountCleared() {
		_spec.ClearField(tenant.FieldMaxDeviceCount, field.TypeInt64)
	}
	if value, ok := tu.mutation.MaxUploadFileSize(); ok {
		_spec.SetField(tenant.FieldMaxUploadFileSize, field.TypeInt64, value)
	}
	if value, ok := tu.mutation.AddedMaxUploadFileSize(); ok {
		_spec.AddField(tenant.FieldMaxUploadFileSize, field.TypeInt64, value)
	}
	if tu.mutation.MaxUploadFileSizeCleared() {
		_spec.ClearField(tenant.FieldMaxUploadFileSize, field.TypeInt64)
	}
	if value, ok := tu.mutation.MaxUserCount(); ok {
		_spec.SetField(tenant.FieldMaxUserCount, field.TypeInt64, value)
	}
	if value, ok := tu.mutation.AddedMaxUserCount(); ok {
		_spec.AddField(tenant.FieldMaxUserCount, field.TypeInt64, value)
	}
	if tu.mutation.MaxUserCountCleared() {
		_spec.ClearField(tenant.FieldMaxUserCount, field.TypeInt64)
	}
	if value, ok := tu.mutation.Principal(); ok {
		_spec.SetField(tenant.FieldPrincipal, field.TypeString, value)
	}
	if tu.mutation.PrincipalCleared() {
		_spec.ClearField(tenant.FieldPrincipal, field.TypeString)
	}
	if value, ok := tu.mutation.PrincipalContactInformation(); ok {
		_spec.SetField(tenant.FieldPrincipalContactInformation, field.TypeString, value)
	}
	if tu.mutation.PrincipalContactInformationCleared() {
		_spec.ClearField(tenant.FieldPrincipalContactInformation, field.TypeString)
	}
	if value, ok := tu.mutation.SaleContact(); ok {
		_spec.SetField(tenant.FieldSaleContact, field.TypeString, value)
	}
	if tu.mutation.SaleContactCleared() {
		_spec.ClearField(tenant.FieldSaleContact, field.TypeString)
	}
	if value, ok := tu.mutation.SecretKey(); ok {
		_spec.SetField(tenant.FieldSecretKey, field.TypeString, value)
	}
	if tu.mutation.SecretKeyCleared() {
		_spec.ClearField(tenant.FieldSecretKey, field.TypeString)
	}
	if value, ok := tu.mutation.AiStatus(); ok {
		_spec.SetField(tenant.FieldAiStatus, field.TypeBool, value)
	}
	if tu.mutation.AiStatusCleared() {
		_spec.ClearField(tenant.FieldAiStatus, field.TypeBool)
	}
	if value, ok := tu.mutation.MaxConferenceAgendaTitle(); ok {
		_spec.SetField(tenant.FieldMaxConferenceAgendaTitle, field.TypeInt64, value)
	}
	if value, ok := tu.mutation.AddedMaxConferenceAgendaTitle(); ok {
		_spec.AddField(tenant.FieldMaxConferenceAgendaTitle, field.TypeInt64, value)
	}
	if tu.mutation.MaxConferenceAgendaTitleCleared() {
		_spec.ClearField(tenant.FieldMaxConferenceAgendaTitle, field.TypeInt64)
	}
	if tu.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   tenant.UsersTable,
			Columns: tenant.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tu.mutation.RemovedUsersIDs(); len(nodes) > 0 && !tu.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   tenant.UsersTable,
			Columns: tenant.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tu.mutation.UsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   tenant.UsersTable,
			Columns: tenant.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tu.mutation.MenusCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tenant.MenusTable,
			Columns: []string{tenant.MenusColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tu.mutation.RemovedMenusIDs(); len(nodes) > 0 && !tu.mutation.MenusCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tenant.MenusTable,
			Columns: []string{tenant.MenusColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tu.mutation.MenusIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tenant.MenusTable,
			Columns: []string{tenant.MenusColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tu.mutation.ApisCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tenant.ApisTable,
			Columns: []string{tenant.ApisColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(api.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tu.mutation.RemovedApisIDs(); len(nodes) > 0 && !tu.mutation.ApisCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tenant.ApisTable,
			Columns: []string{tenant.ApisColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(api.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tu.mutation.ApisIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tenant.ApisTable,
			Columns: []string{tenant.ApisColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(api.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tu.mutation.ButtonsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tenant.ButtonsTable,
			Columns: []string{tenant.ButtonsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(button.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tu.mutation.RemovedButtonsIDs(); len(nodes) > 0 && !tu.mutation.ButtonsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tenant.ButtonsTable,
			Columns: []string{tenant.ButtonsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(button.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tu.mutation.ButtonsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tenant.ButtonsTable,
			Columns: []string{tenant.ButtonsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(button.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, tu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{tenant.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	tu.mutation.done = true
	return n, nil
}

// TenantUpdateOne is the builder for updating a single Tenant entity.
type TenantUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *TenantMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (tuo *TenantUpdateOne) SetUpdatedAt(t time.Time) *TenantUpdateOne {
	tuo.mutation.SetUpdatedAt(t)
	return tuo
}

// SetUUID sets the "uuid" field.
func (tuo *TenantUpdateOne) SetUUID(u uuid.UUID) *TenantUpdateOne {
	tuo.mutation.SetUUID(u)
	return tuo
}

// SetNillableUUID sets the "uuid" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableUUID(u *uuid.UUID) *TenantUpdateOne {
	if u != nil {
		tuo.SetUUID(*u)
	}
	return tuo
}

// SetKey sets the "key" field.
func (tuo *TenantUpdateOne) SetKey(s string) *TenantUpdateOne {
	tuo.mutation.SetKey(s)
	return tuo
}

// SetNillableKey sets the "key" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableKey(s *string) *TenantUpdateOne {
	if s != nil {
		tuo.SetKey(*s)
	}
	return tuo
}

// SetSecret sets the "secret" field.
func (tuo *TenantUpdateOne) SetSecret(s string) *TenantUpdateOne {
	tuo.mutation.SetSecret(s)
	return tuo
}

// SetNillableSecret sets the "secret" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableSecret(s *string) *TenantUpdateOne {
	if s != nil {
		tuo.SetSecret(*s)
	}
	return tuo
}

// ClearSecret clears the value of the "secret" field.
func (tuo *TenantUpdateOne) ClearSecret() *TenantUpdateOne {
	tuo.mutation.ClearSecret()
	return tuo
}

// SetStatus sets the "status" field.
func (tuo *TenantUpdateOne) SetStatus(b bool) *TenantUpdateOne {
	tuo.mutation.SetStatus(b)
	return tuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableStatus(b *bool) *TenantUpdateOne {
	if b != nil {
		tuo.SetStatus(*b)
	}
	return tuo
}

// ClearStatus clears the value of the "status" field.
func (tuo *TenantUpdateOne) ClearStatus() *TenantUpdateOne {
	tuo.mutation.ClearStatus()
	return tuo
}

// SetDeletedAt sets the "deleted_at" field.
func (tuo *TenantUpdateOne) SetDeletedAt(t time.Time) *TenantUpdateOne {
	tuo.mutation.SetDeletedAt(t)
	return tuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableDeletedAt(t *time.Time) *TenantUpdateOne {
	if t != nil {
		tuo.SetDeletedAt(*t)
	}
	return tuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (tuo *TenantUpdateOne) ClearDeletedAt() *TenantUpdateOne {
	tuo.mutation.ClearDeletedAt()
	return tuo
}

// SetName sets the "name" field.
func (tuo *TenantUpdateOne) SetName(s string) *TenantUpdateOne {
	tuo.mutation.SetName(s)
	return tuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableName(s *string) *TenantUpdateOne {
	if s != nil {
		tuo.SetName(*s)
	}
	return tuo
}

// ClearName clears the value of the "name" field.
func (tuo *TenantUpdateOne) ClearName() *TenantUpdateOne {
	tuo.mutation.ClearName()
	return tuo
}

// SetIsSuper sets the "is_super" field.
func (tuo *TenantUpdateOne) SetIsSuper(b bool) *TenantUpdateOne {
	tuo.mutation.SetIsSuper(b)
	return tuo
}

// SetNillableIsSuper sets the "is_super" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableIsSuper(b *bool) *TenantUpdateOne {
	if b != nil {
		tuo.SetIsSuper(*b)
	}
	return tuo
}

// ClearIsSuper clears the value of the "is_super" field.
func (tuo *TenantUpdateOne) ClearIsSuper() *TenantUpdateOne {
	tuo.mutation.ClearIsSuper()
	return tuo
}

// SetServiceStartAt sets the "service_start_at" field.
func (tuo *TenantUpdateOne) SetServiceStartAt(t time.Time) *TenantUpdateOne {
	tuo.mutation.SetServiceStartAt(t)
	return tuo
}

// SetNillableServiceStartAt sets the "service_start_at" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableServiceStartAt(t *time.Time) *TenantUpdateOne {
	if t != nil {
		tuo.SetServiceStartAt(*t)
	}
	return tuo
}

// ClearServiceStartAt clears the value of the "service_start_at" field.
func (tuo *TenantUpdateOne) ClearServiceStartAt() *TenantUpdateOne {
	tuo.mutation.ClearServiceStartAt()
	return tuo
}

// SetServiceEndAt sets the "service_end_at" field.
func (tuo *TenantUpdateOne) SetServiceEndAt(t time.Time) *TenantUpdateOne {
	tuo.mutation.SetServiceEndAt(t)
	return tuo
}

// SetNillableServiceEndAt sets the "service_end_at" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableServiceEndAt(t *time.Time) *TenantUpdateOne {
	if t != nil {
		tuo.SetServiceEndAt(*t)
	}
	return tuo
}

// ClearServiceEndAt clears the value of the "service_end_at" field.
func (tuo *TenantUpdateOne) ClearServiceEndAt() *TenantUpdateOne {
	tuo.mutation.ClearServiceEndAt()
	return tuo
}

// SetAfterSalesContact sets the "after_sales_contact" field.
func (tuo *TenantUpdateOne) SetAfterSalesContact(s string) *TenantUpdateOne {
	tuo.mutation.SetAfterSalesContact(s)
	return tuo
}

// SetNillableAfterSalesContact sets the "after_sales_contact" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableAfterSalesContact(s *string) *TenantUpdateOne {
	if s != nil {
		tuo.SetAfterSalesContact(*s)
	}
	return tuo
}

// ClearAfterSalesContact clears the value of the "after_sales_contact" field.
func (tuo *TenantUpdateOne) ClearAfterSalesContact() *TenantUpdateOne {
	tuo.mutation.ClearAfterSalesContact()
	return tuo
}

// SetLocationID sets the "location_id" field.
func (tuo *TenantUpdateOne) SetLocationID(s string) *TenantUpdateOne {
	tuo.mutation.SetLocationID(s)
	return tuo
}

// SetNillableLocationID sets the "location_id" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableLocationID(s *string) *TenantUpdateOne {
	if s != nil {
		tuo.SetLocationID(*s)
	}
	return tuo
}

// ClearLocationID clears the value of the "location_id" field.
func (tuo *TenantUpdateOne) ClearLocationID() *TenantUpdateOne {
	tuo.mutation.ClearLocationID()
	return tuo
}

// SetLogSaveKeepDays sets the "log_save_keep_days" field.
func (tuo *TenantUpdateOne) SetLogSaveKeepDays(i int64) *TenantUpdateOne {
	tuo.mutation.ResetLogSaveKeepDays()
	tuo.mutation.SetLogSaveKeepDays(i)
	return tuo
}

// SetNillableLogSaveKeepDays sets the "log_save_keep_days" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableLogSaveKeepDays(i *int64) *TenantUpdateOne {
	if i != nil {
		tuo.SetLogSaveKeepDays(*i)
	}
	return tuo
}

// AddLogSaveKeepDays adds i to the "log_save_keep_days" field.
func (tuo *TenantUpdateOne) AddLogSaveKeepDays(i int64) *TenantUpdateOne {
	tuo.mutation.AddLogSaveKeepDays(i)
	return tuo
}

// ClearLogSaveKeepDays clears the value of the "log_save_keep_days" field.
func (tuo *TenantUpdateOne) ClearLogSaveKeepDays() *TenantUpdateOne {
	tuo.mutation.ClearLogSaveKeepDays()
	return tuo
}

// SetMaxAttendanceUserCount sets the "max_attendance_user_count" field.
func (tuo *TenantUpdateOne) SetMaxAttendanceUserCount(i int64) *TenantUpdateOne {
	tuo.mutation.ResetMaxAttendanceUserCount()
	tuo.mutation.SetMaxAttendanceUserCount(i)
	return tuo
}

// SetNillableMaxAttendanceUserCount sets the "max_attendance_user_count" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableMaxAttendanceUserCount(i *int64) *TenantUpdateOne {
	if i != nil {
		tuo.SetMaxAttendanceUserCount(*i)
	}
	return tuo
}

// AddMaxAttendanceUserCount adds i to the "max_attendance_user_count" field.
func (tuo *TenantUpdateOne) AddMaxAttendanceUserCount(i int64) *TenantUpdateOne {
	tuo.mutation.AddMaxAttendanceUserCount(i)
	return tuo
}

// ClearMaxAttendanceUserCount clears the value of the "max_attendance_user_count" field.
func (tuo *TenantUpdateOne) ClearMaxAttendanceUserCount() *TenantUpdateOne {
	tuo.mutation.ClearMaxAttendanceUserCount()
	return tuo
}

// SetMaxDeviceCount sets the "max_device_count" field.
func (tuo *TenantUpdateOne) SetMaxDeviceCount(i int64) *TenantUpdateOne {
	tuo.mutation.ResetMaxDeviceCount()
	tuo.mutation.SetMaxDeviceCount(i)
	return tuo
}

// SetNillableMaxDeviceCount sets the "max_device_count" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableMaxDeviceCount(i *int64) *TenantUpdateOne {
	if i != nil {
		tuo.SetMaxDeviceCount(*i)
	}
	return tuo
}

// AddMaxDeviceCount adds i to the "max_device_count" field.
func (tuo *TenantUpdateOne) AddMaxDeviceCount(i int64) *TenantUpdateOne {
	tuo.mutation.AddMaxDeviceCount(i)
	return tuo
}

// ClearMaxDeviceCount clears the value of the "max_device_count" field.
func (tuo *TenantUpdateOne) ClearMaxDeviceCount() *TenantUpdateOne {
	tuo.mutation.ClearMaxDeviceCount()
	return tuo
}

// SetMaxUploadFileSize sets the "max_upload_file_size" field.
func (tuo *TenantUpdateOne) SetMaxUploadFileSize(i int64) *TenantUpdateOne {
	tuo.mutation.ResetMaxUploadFileSize()
	tuo.mutation.SetMaxUploadFileSize(i)
	return tuo
}

// SetNillableMaxUploadFileSize sets the "max_upload_file_size" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableMaxUploadFileSize(i *int64) *TenantUpdateOne {
	if i != nil {
		tuo.SetMaxUploadFileSize(*i)
	}
	return tuo
}

// AddMaxUploadFileSize adds i to the "max_upload_file_size" field.
func (tuo *TenantUpdateOne) AddMaxUploadFileSize(i int64) *TenantUpdateOne {
	tuo.mutation.AddMaxUploadFileSize(i)
	return tuo
}

// ClearMaxUploadFileSize clears the value of the "max_upload_file_size" field.
func (tuo *TenantUpdateOne) ClearMaxUploadFileSize() *TenantUpdateOne {
	tuo.mutation.ClearMaxUploadFileSize()
	return tuo
}

// SetMaxUserCount sets the "max_user_count" field.
func (tuo *TenantUpdateOne) SetMaxUserCount(i int64) *TenantUpdateOne {
	tuo.mutation.ResetMaxUserCount()
	tuo.mutation.SetMaxUserCount(i)
	return tuo
}

// SetNillableMaxUserCount sets the "max_user_count" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableMaxUserCount(i *int64) *TenantUpdateOne {
	if i != nil {
		tuo.SetMaxUserCount(*i)
	}
	return tuo
}

// AddMaxUserCount adds i to the "max_user_count" field.
func (tuo *TenantUpdateOne) AddMaxUserCount(i int64) *TenantUpdateOne {
	tuo.mutation.AddMaxUserCount(i)
	return tuo
}

// ClearMaxUserCount clears the value of the "max_user_count" field.
func (tuo *TenantUpdateOne) ClearMaxUserCount() *TenantUpdateOne {
	tuo.mutation.ClearMaxUserCount()
	return tuo
}

// SetPrincipal sets the "principal" field.
func (tuo *TenantUpdateOne) SetPrincipal(s string) *TenantUpdateOne {
	tuo.mutation.SetPrincipal(s)
	return tuo
}

// SetNillablePrincipal sets the "principal" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillablePrincipal(s *string) *TenantUpdateOne {
	if s != nil {
		tuo.SetPrincipal(*s)
	}
	return tuo
}

// ClearPrincipal clears the value of the "principal" field.
func (tuo *TenantUpdateOne) ClearPrincipal() *TenantUpdateOne {
	tuo.mutation.ClearPrincipal()
	return tuo
}

// SetPrincipalContactInformation sets the "principal_contact_information" field.
func (tuo *TenantUpdateOne) SetPrincipalContactInformation(s string) *TenantUpdateOne {
	tuo.mutation.SetPrincipalContactInformation(s)
	return tuo
}

// SetNillablePrincipalContactInformation sets the "principal_contact_information" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillablePrincipalContactInformation(s *string) *TenantUpdateOne {
	if s != nil {
		tuo.SetPrincipalContactInformation(*s)
	}
	return tuo
}

// ClearPrincipalContactInformation clears the value of the "principal_contact_information" field.
func (tuo *TenantUpdateOne) ClearPrincipalContactInformation() *TenantUpdateOne {
	tuo.mutation.ClearPrincipalContactInformation()
	return tuo
}

// SetSaleContact sets the "sale_contact" field.
func (tuo *TenantUpdateOne) SetSaleContact(s string) *TenantUpdateOne {
	tuo.mutation.SetSaleContact(s)
	return tuo
}

// SetNillableSaleContact sets the "sale_contact" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableSaleContact(s *string) *TenantUpdateOne {
	if s != nil {
		tuo.SetSaleContact(*s)
	}
	return tuo
}

// ClearSaleContact clears the value of the "sale_contact" field.
func (tuo *TenantUpdateOne) ClearSaleContact() *TenantUpdateOne {
	tuo.mutation.ClearSaleContact()
	return tuo
}

// SetSecretKey sets the "secret_key" field.
func (tuo *TenantUpdateOne) SetSecretKey(s string) *TenantUpdateOne {
	tuo.mutation.SetSecretKey(s)
	return tuo
}

// SetNillableSecretKey sets the "secret_key" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableSecretKey(s *string) *TenantUpdateOne {
	if s != nil {
		tuo.SetSecretKey(*s)
	}
	return tuo
}

// ClearSecretKey clears the value of the "secret_key" field.
func (tuo *TenantUpdateOne) ClearSecretKey() *TenantUpdateOne {
	tuo.mutation.ClearSecretKey()
	return tuo
}

// SetAiStatus sets the "ai_status" field.
func (tuo *TenantUpdateOne) SetAiStatus(b bool) *TenantUpdateOne {
	tuo.mutation.SetAiStatus(b)
	return tuo
}

// SetNillableAiStatus sets the "ai_status" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableAiStatus(b *bool) *TenantUpdateOne {
	if b != nil {
		tuo.SetAiStatus(*b)
	}
	return tuo
}

// ClearAiStatus clears the value of the "ai_status" field.
func (tuo *TenantUpdateOne) ClearAiStatus() *TenantUpdateOne {
	tuo.mutation.ClearAiStatus()
	return tuo
}

// SetMaxConferenceAgendaTitle sets the "max_conference_agenda_title" field.
func (tuo *TenantUpdateOne) SetMaxConferenceAgendaTitle(i int64) *TenantUpdateOne {
	tuo.mutation.ResetMaxConferenceAgendaTitle()
	tuo.mutation.SetMaxConferenceAgendaTitle(i)
	return tuo
}

// SetNillableMaxConferenceAgendaTitle sets the "max_conference_agenda_title" field if the given value is not nil.
func (tuo *TenantUpdateOne) SetNillableMaxConferenceAgendaTitle(i *int64) *TenantUpdateOne {
	if i != nil {
		tuo.SetMaxConferenceAgendaTitle(*i)
	}
	return tuo
}

// AddMaxConferenceAgendaTitle adds i to the "max_conference_agenda_title" field.
func (tuo *TenantUpdateOne) AddMaxConferenceAgendaTitle(i int64) *TenantUpdateOne {
	tuo.mutation.AddMaxConferenceAgendaTitle(i)
	return tuo
}

// ClearMaxConferenceAgendaTitle clears the value of the "max_conference_agenda_title" field.
func (tuo *TenantUpdateOne) ClearMaxConferenceAgendaTitle() *TenantUpdateOne {
	tuo.mutation.ClearMaxConferenceAgendaTitle()
	return tuo
}

// AddUserIDs adds the "users" edge to the User entity by IDs.
func (tuo *TenantUpdateOne) AddUserIDs(ids ...string) *TenantUpdateOne {
	tuo.mutation.AddUserIDs(ids...)
	return tuo
}

// AddUsers adds the "users" edges to the User entity.
func (tuo *TenantUpdateOne) AddUsers(u ...*User) *TenantUpdateOne {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return tuo.AddUserIDs(ids...)
}

// AddMenuIDs adds the "menus" edge to the Menu entity by IDs.
func (tuo *TenantUpdateOne) AddMenuIDs(ids ...string) *TenantUpdateOne {
	tuo.mutation.AddMenuIDs(ids...)
	return tuo
}

// AddMenus adds the "menus" edges to the Menu entity.
func (tuo *TenantUpdateOne) AddMenus(m ...*Menu) *TenantUpdateOne {
	ids := make([]string, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return tuo.AddMenuIDs(ids...)
}

// AddAPIIDs adds the "apis" edge to the API entity by IDs.
func (tuo *TenantUpdateOne) AddAPIIDs(ids ...string) *TenantUpdateOne {
	tuo.mutation.AddAPIIDs(ids...)
	return tuo
}

// AddApis adds the "apis" edges to the API entity.
func (tuo *TenantUpdateOne) AddApis(a ...*API) *TenantUpdateOne {
	ids := make([]string, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return tuo.AddAPIIDs(ids...)
}

// AddButtonIDs adds the "buttons" edge to the Button entity by IDs.
func (tuo *TenantUpdateOne) AddButtonIDs(ids ...string) *TenantUpdateOne {
	tuo.mutation.AddButtonIDs(ids...)
	return tuo
}

// AddButtons adds the "buttons" edges to the Button entity.
func (tuo *TenantUpdateOne) AddButtons(b ...*Button) *TenantUpdateOne {
	ids := make([]string, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return tuo.AddButtonIDs(ids...)
}

// Mutation returns the TenantMutation object of the builder.
func (tuo *TenantUpdateOne) Mutation() *TenantMutation {
	return tuo.mutation
}

// ClearUsers clears all "users" edges to the User entity.
func (tuo *TenantUpdateOne) ClearUsers() *TenantUpdateOne {
	tuo.mutation.ClearUsers()
	return tuo
}

// RemoveUserIDs removes the "users" edge to User entities by IDs.
func (tuo *TenantUpdateOne) RemoveUserIDs(ids ...string) *TenantUpdateOne {
	tuo.mutation.RemoveUserIDs(ids...)
	return tuo
}

// RemoveUsers removes "users" edges to User entities.
func (tuo *TenantUpdateOne) RemoveUsers(u ...*User) *TenantUpdateOne {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return tuo.RemoveUserIDs(ids...)
}

// ClearMenus clears all "menus" edges to the Menu entity.
func (tuo *TenantUpdateOne) ClearMenus() *TenantUpdateOne {
	tuo.mutation.ClearMenus()
	return tuo
}

// RemoveMenuIDs removes the "menus" edge to Menu entities by IDs.
func (tuo *TenantUpdateOne) RemoveMenuIDs(ids ...string) *TenantUpdateOne {
	tuo.mutation.RemoveMenuIDs(ids...)
	return tuo
}

// RemoveMenus removes "menus" edges to Menu entities.
func (tuo *TenantUpdateOne) RemoveMenus(m ...*Menu) *TenantUpdateOne {
	ids := make([]string, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return tuo.RemoveMenuIDs(ids...)
}

// ClearApis clears all "apis" edges to the API entity.
func (tuo *TenantUpdateOne) ClearApis() *TenantUpdateOne {
	tuo.mutation.ClearApis()
	return tuo
}

// RemoveAPIIDs removes the "apis" edge to API entities by IDs.
func (tuo *TenantUpdateOne) RemoveAPIIDs(ids ...string) *TenantUpdateOne {
	tuo.mutation.RemoveAPIIDs(ids...)
	return tuo
}

// RemoveApis removes "apis" edges to API entities.
func (tuo *TenantUpdateOne) RemoveApis(a ...*API) *TenantUpdateOne {
	ids := make([]string, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return tuo.RemoveAPIIDs(ids...)
}

// ClearButtons clears all "buttons" edges to the Button entity.
func (tuo *TenantUpdateOne) ClearButtons() *TenantUpdateOne {
	tuo.mutation.ClearButtons()
	return tuo
}

// RemoveButtonIDs removes the "buttons" edge to Button entities by IDs.
func (tuo *TenantUpdateOne) RemoveButtonIDs(ids ...string) *TenantUpdateOne {
	tuo.mutation.RemoveButtonIDs(ids...)
	return tuo
}

// RemoveButtons removes "buttons" edges to Button entities.
func (tuo *TenantUpdateOne) RemoveButtons(b ...*Button) *TenantUpdateOne {
	ids := make([]string, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return tuo.RemoveButtonIDs(ids...)
}

// Where appends a list predicates to the TenantUpdate builder.
func (tuo *TenantUpdateOne) Where(ps ...predicate.Tenant) *TenantUpdateOne {
	tuo.mutation.Where(ps...)
	return tuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (tuo *TenantUpdateOne) Select(field string, fields ...string) *TenantUpdateOne {
	tuo.fields = append([]string{field}, fields...)
	return tuo
}

// Save executes the query and returns the updated Tenant entity.
func (tuo *TenantUpdateOne) Save(ctx context.Context) (*Tenant, error) {
	if err := tuo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, tuo.sqlSave, tuo.mutation, tuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tuo *TenantUpdateOne) SaveX(ctx context.Context) *Tenant {
	node, err := tuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (tuo *TenantUpdateOne) Exec(ctx context.Context) error {
	_, err := tuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tuo *TenantUpdateOne) ExecX(ctx context.Context) {
	if err := tuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (tuo *TenantUpdateOne) defaults() error {
	if _, ok := tuo.mutation.UpdatedAt(); !ok {
		if tenant.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized tenant.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := tenant.UpdateDefaultUpdatedAt()
		tuo.mutation.SetUpdatedAt(v)
	}
	return nil
}

func (tuo *TenantUpdateOne) sqlSave(ctx context.Context) (_node *Tenant, err error) {
	_spec := sqlgraph.NewUpdateSpec(tenant.Table, tenant.Columns, sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString))
	id, ok := tuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Tenant.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := tuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, tenant.FieldID)
		for _, f := range fields {
			if !tenant.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != tenant.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := tuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := tuo.mutation.UpdatedAt(); ok {
		_spec.SetField(tenant.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := tuo.mutation.UUID(); ok {
		_spec.SetField(tenant.FieldUUID, field.TypeUUID, value)
	}
	if value, ok := tuo.mutation.Key(); ok {
		_spec.SetField(tenant.FieldKey, field.TypeString, value)
	}
	if value, ok := tuo.mutation.Secret(); ok {
		_spec.SetField(tenant.FieldSecret, field.TypeString, value)
	}
	if tuo.mutation.SecretCleared() {
		_spec.ClearField(tenant.FieldSecret, field.TypeString)
	}
	if value, ok := tuo.mutation.Status(); ok {
		_spec.SetField(tenant.FieldStatus, field.TypeBool, value)
	}
	if tuo.mutation.StatusCleared() {
		_spec.ClearField(tenant.FieldStatus, field.TypeBool)
	}
	if value, ok := tuo.mutation.DeletedAt(); ok {
		_spec.SetField(tenant.FieldDeletedAt, field.TypeTime, value)
	}
	if tuo.mutation.DeletedAtCleared() {
		_spec.ClearField(tenant.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := tuo.mutation.Name(); ok {
		_spec.SetField(tenant.FieldName, field.TypeString, value)
	}
	if tuo.mutation.NameCleared() {
		_spec.ClearField(tenant.FieldName, field.TypeString)
	}
	if value, ok := tuo.mutation.IsSuper(); ok {
		_spec.SetField(tenant.FieldIsSuper, field.TypeBool, value)
	}
	if tuo.mutation.IsSuperCleared() {
		_spec.ClearField(tenant.FieldIsSuper, field.TypeBool)
	}
	if value, ok := tuo.mutation.ServiceStartAt(); ok {
		_spec.SetField(tenant.FieldServiceStartAt, field.TypeTime, value)
	}
	if tuo.mutation.ServiceStartAtCleared() {
		_spec.ClearField(tenant.FieldServiceStartAt, field.TypeTime)
	}
	if value, ok := tuo.mutation.ServiceEndAt(); ok {
		_spec.SetField(tenant.FieldServiceEndAt, field.TypeTime, value)
	}
	if tuo.mutation.ServiceEndAtCleared() {
		_spec.ClearField(tenant.FieldServiceEndAt, field.TypeTime)
	}
	if value, ok := tuo.mutation.AfterSalesContact(); ok {
		_spec.SetField(tenant.FieldAfterSalesContact, field.TypeString, value)
	}
	if tuo.mutation.AfterSalesContactCleared() {
		_spec.ClearField(tenant.FieldAfterSalesContact, field.TypeString)
	}
	if value, ok := tuo.mutation.LocationID(); ok {
		_spec.SetField(tenant.FieldLocationID, field.TypeString, value)
	}
	if tuo.mutation.LocationIDCleared() {
		_spec.ClearField(tenant.FieldLocationID, field.TypeString)
	}
	if value, ok := tuo.mutation.LogSaveKeepDays(); ok {
		_spec.SetField(tenant.FieldLogSaveKeepDays, field.TypeInt64, value)
	}
	if value, ok := tuo.mutation.AddedLogSaveKeepDays(); ok {
		_spec.AddField(tenant.FieldLogSaveKeepDays, field.TypeInt64, value)
	}
	if tuo.mutation.LogSaveKeepDaysCleared() {
		_spec.ClearField(tenant.FieldLogSaveKeepDays, field.TypeInt64)
	}
	if value, ok := tuo.mutation.MaxAttendanceUserCount(); ok {
		_spec.SetField(tenant.FieldMaxAttendanceUserCount, field.TypeInt64, value)
	}
	if value, ok := tuo.mutation.AddedMaxAttendanceUserCount(); ok {
		_spec.AddField(tenant.FieldMaxAttendanceUserCount, field.TypeInt64, value)
	}
	if tuo.mutation.MaxAttendanceUserCountCleared() {
		_spec.ClearField(tenant.FieldMaxAttendanceUserCount, field.TypeInt64)
	}
	if value, ok := tuo.mutation.MaxDeviceCount(); ok {
		_spec.SetField(tenant.FieldMaxDeviceCount, field.TypeInt64, value)
	}
	if value, ok := tuo.mutation.AddedMaxDeviceCount(); ok {
		_spec.AddField(tenant.FieldMaxDeviceCount, field.TypeInt64, value)
	}
	if tuo.mutation.MaxDeviceCountCleared() {
		_spec.ClearField(tenant.FieldMaxDeviceCount, field.TypeInt64)
	}
	if value, ok := tuo.mutation.MaxUploadFileSize(); ok {
		_spec.SetField(tenant.FieldMaxUploadFileSize, field.TypeInt64, value)
	}
	if value, ok := tuo.mutation.AddedMaxUploadFileSize(); ok {
		_spec.AddField(tenant.FieldMaxUploadFileSize, field.TypeInt64, value)
	}
	if tuo.mutation.MaxUploadFileSizeCleared() {
		_spec.ClearField(tenant.FieldMaxUploadFileSize, field.TypeInt64)
	}
	if value, ok := tuo.mutation.MaxUserCount(); ok {
		_spec.SetField(tenant.FieldMaxUserCount, field.TypeInt64, value)
	}
	if value, ok := tuo.mutation.AddedMaxUserCount(); ok {
		_spec.AddField(tenant.FieldMaxUserCount, field.TypeInt64, value)
	}
	if tuo.mutation.MaxUserCountCleared() {
		_spec.ClearField(tenant.FieldMaxUserCount, field.TypeInt64)
	}
	if value, ok := tuo.mutation.Principal(); ok {
		_spec.SetField(tenant.FieldPrincipal, field.TypeString, value)
	}
	if tuo.mutation.PrincipalCleared() {
		_spec.ClearField(tenant.FieldPrincipal, field.TypeString)
	}
	if value, ok := tuo.mutation.PrincipalContactInformation(); ok {
		_spec.SetField(tenant.FieldPrincipalContactInformation, field.TypeString, value)
	}
	if tuo.mutation.PrincipalContactInformationCleared() {
		_spec.ClearField(tenant.FieldPrincipalContactInformation, field.TypeString)
	}
	if value, ok := tuo.mutation.SaleContact(); ok {
		_spec.SetField(tenant.FieldSaleContact, field.TypeString, value)
	}
	if tuo.mutation.SaleContactCleared() {
		_spec.ClearField(tenant.FieldSaleContact, field.TypeString)
	}
	if value, ok := tuo.mutation.SecretKey(); ok {
		_spec.SetField(tenant.FieldSecretKey, field.TypeString, value)
	}
	if tuo.mutation.SecretKeyCleared() {
		_spec.ClearField(tenant.FieldSecretKey, field.TypeString)
	}
	if value, ok := tuo.mutation.AiStatus(); ok {
		_spec.SetField(tenant.FieldAiStatus, field.TypeBool, value)
	}
	if tuo.mutation.AiStatusCleared() {
		_spec.ClearField(tenant.FieldAiStatus, field.TypeBool)
	}
	if value, ok := tuo.mutation.MaxConferenceAgendaTitle(); ok {
		_spec.SetField(tenant.FieldMaxConferenceAgendaTitle, field.TypeInt64, value)
	}
	if value, ok := tuo.mutation.AddedMaxConferenceAgendaTitle(); ok {
		_spec.AddField(tenant.FieldMaxConferenceAgendaTitle, field.TypeInt64, value)
	}
	if tuo.mutation.MaxConferenceAgendaTitleCleared() {
		_spec.ClearField(tenant.FieldMaxConferenceAgendaTitle, field.TypeInt64)
	}
	if tuo.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   tenant.UsersTable,
			Columns: tenant.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuo.mutation.RemovedUsersIDs(); len(nodes) > 0 && !tuo.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   tenant.UsersTable,
			Columns: tenant.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuo.mutation.UsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   tenant.UsersTable,
			Columns: tenant.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tuo.mutation.MenusCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tenant.MenusTable,
			Columns: []string{tenant.MenusColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuo.mutation.RemovedMenusIDs(); len(nodes) > 0 && !tuo.mutation.MenusCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tenant.MenusTable,
			Columns: []string{tenant.MenusColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuo.mutation.MenusIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tenant.MenusTable,
			Columns: []string{tenant.MenusColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tuo.mutation.ApisCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tenant.ApisTable,
			Columns: []string{tenant.ApisColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(api.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuo.mutation.RemovedApisIDs(); len(nodes) > 0 && !tuo.mutation.ApisCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tenant.ApisTable,
			Columns: []string{tenant.ApisColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(api.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuo.mutation.ApisIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tenant.ApisTable,
			Columns: []string{tenant.ApisColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(api.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tuo.mutation.ButtonsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tenant.ButtonsTable,
			Columns: []string{tenant.ButtonsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(button.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuo.mutation.RemovedButtonsIDs(); len(nodes) > 0 && !tuo.mutation.ButtonsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tenant.ButtonsTable,
			Columns: []string{tenant.ButtonsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(button.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuo.mutation.ButtonsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tenant.ButtonsTable,
			Columns: []string{tenant.ButtonsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(button.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Tenant{config: tuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, tuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{tenant.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	tuo.mutation.done = true
	return _node, nil
}
