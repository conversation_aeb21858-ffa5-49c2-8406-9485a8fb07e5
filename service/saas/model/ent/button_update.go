// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/button"
	"phoenix/service/saas/model/ent/menu"
	"phoenix/service/saas/model/ent/predicate"
	"phoenix/service/saas/model/ent/role"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ButtonUpdate is the builder for updating Button entities.
type ButtonUpdate struct {
	config
	hooks    []Hook
	mutation *ButtonMutation
}

// Where appends a list predicates to the ButtonUpdate builder.
func (bu *ButtonUpdate) Where(ps ...predicate.Button) *ButtonUpdate {
	bu.mutation.Where(ps...)
	return bu
}

// SetUpdatedAt sets the "updated_at" field.
func (bu *ButtonUpdate) SetUpdatedAt(t time.Time) *ButtonUpdate {
	bu.mutation.SetUpdatedAt(t)
	return bu
}

// SetSort sets the "sort" field.
func (bu *ButtonUpdate) SetSort(u uint32) *ButtonUpdate {
	bu.mutation.ResetSort()
	bu.mutation.SetSort(u)
	return bu
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (bu *ButtonUpdate) SetNillableSort(u *uint32) *ButtonUpdate {
	if u != nil {
		bu.SetSort(*u)
	}
	return bu
}

// AddSort adds u to the "sort" field.
func (bu *ButtonUpdate) AddSort(u int32) *ButtonUpdate {
	bu.mutation.AddSort(u)
	return bu
}

// SetDeletedAt sets the "deleted_at" field.
func (bu *ButtonUpdate) SetDeletedAt(t time.Time) *ButtonUpdate {
	bu.mutation.SetDeletedAt(t)
	return bu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (bu *ButtonUpdate) SetNillableDeletedAt(t *time.Time) *ButtonUpdate {
	if t != nil {
		bu.SetDeletedAt(*t)
	}
	return bu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (bu *ButtonUpdate) ClearDeletedAt() *ButtonUpdate {
	bu.mutation.ClearDeletedAt()
	return bu
}

// SetName sets the "name" field.
func (bu *ButtonUpdate) SetName(s string) *ButtonUpdate {
	bu.mutation.SetName(s)
	return bu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (bu *ButtonUpdate) SetNillableName(s *string) *ButtonUpdate {
	if s != nil {
		bu.SetName(*s)
	}
	return bu
}

// ClearName clears the value of the "name" field.
func (bu *ButtonUpdate) ClearName() *ButtonUpdate {
	bu.mutation.ClearName()
	return bu
}

// SetCode sets the "code" field.
func (bu *ButtonUpdate) SetCode(s string) *ButtonUpdate {
	bu.mutation.SetCode(s)
	return bu
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (bu *ButtonUpdate) SetNillableCode(s *string) *ButtonUpdate {
	if s != nil {
		bu.SetCode(*s)
	}
	return bu
}

// SetMenuID sets the "menu_id" field.
func (bu *ButtonUpdate) SetMenuID(s string) *ButtonUpdate {
	bu.mutation.SetMenuID(s)
	return bu
}

// SetNillableMenuID sets the "menu_id" field if the given value is not nil.
func (bu *ButtonUpdate) SetNillableMenuID(s *string) *ButtonUpdate {
	if s != nil {
		bu.SetMenuID(*s)
	}
	return bu
}

// ClearMenuID clears the value of the "menu_id" field.
func (bu *ButtonUpdate) ClearMenuID() *ButtonUpdate {
	bu.mutation.ClearMenuID()
	return bu
}

// AddRoleIDs adds the "roles" edge to the Role entity by IDs.
func (bu *ButtonUpdate) AddRoleIDs(ids ...string) *ButtonUpdate {
	bu.mutation.AddRoleIDs(ids...)
	return bu
}

// AddRoles adds the "roles" edges to the Role entity.
func (bu *ButtonUpdate) AddRoles(r ...*Role) *ButtonUpdate {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return bu.AddRoleIDs(ids...)
}

// SetMenu sets the "menu" edge to the Menu entity.
func (bu *ButtonUpdate) SetMenu(m *Menu) *ButtonUpdate {
	return bu.SetMenuID(m.ID)
}

// Mutation returns the ButtonMutation object of the builder.
func (bu *ButtonUpdate) Mutation() *ButtonMutation {
	return bu.mutation
}

// ClearRoles clears all "roles" edges to the Role entity.
func (bu *ButtonUpdate) ClearRoles() *ButtonUpdate {
	bu.mutation.ClearRoles()
	return bu
}

// RemoveRoleIDs removes the "roles" edge to Role entities by IDs.
func (bu *ButtonUpdate) RemoveRoleIDs(ids ...string) *ButtonUpdate {
	bu.mutation.RemoveRoleIDs(ids...)
	return bu
}

// RemoveRoles removes "roles" edges to Role entities.
func (bu *ButtonUpdate) RemoveRoles(r ...*Role) *ButtonUpdate {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return bu.RemoveRoleIDs(ids...)
}

// ClearMenu clears the "menu" edge to the Menu entity.
func (bu *ButtonUpdate) ClearMenu() *ButtonUpdate {
	bu.mutation.ClearMenu()
	return bu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (bu *ButtonUpdate) Save(ctx context.Context) (int, error) {
	if err := bu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, bu.sqlSave, bu.mutation, bu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (bu *ButtonUpdate) SaveX(ctx context.Context) int {
	affected, err := bu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (bu *ButtonUpdate) Exec(ctx context.Context) error {
	_, err := bu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (bu *ButtonUpdate) ExecX(ctx context.Context) {
	if err := bu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (bu *ButtonUpdate) defaults() error {
	if _, ok := bu.mutation.UpdatedAt(); !ok {
		if button.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized button.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := button.UpdateDefaultUpdatedAt()
		bu.mutation.SetUpdatedAt(v)
	}
	return nil
}

func (bu *ButtonUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(button.Table, button.Columns, sqlgraph.NewFieldSpec(button.FieldID, field.TypeString))
	if ps := bu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := bu.mutation.UpdatedAt(); ok {
		_spec.SetField(button.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := bu.mutation.Sort(); ok {
		_spec.SetField(button.FieldSort, field.TypeUint32, value)
	}
	if value, ok := bu.mutation.AddedSort(); ok {
		_spec.AddField(button.FieldSort, field.TypeUint32, value)
	}
	if value, ok := bu.mutation.DeletedAt(); ok {
		_spec.SetField(button.FieldDeletedAt, field.TypeTime, value)
	}
	if bu.mutation.DeletedAtCleared() {
		_spec.ClearField(button.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := bu.mutation.Name(); ok {
		_spec.SetField(button.FieldName, field.TypeString, value)
	}
	if bu.mutation.NameCleared() {
		_spec.ClearField(button.FieldName, field.TypeString)
	}
	if value, ok := bu.mutation.Code(); ok {
		_spec.SetField(button.FieldCode, field.TypeString, value)
	}
	if bu.mutation.RolesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   button.RolesTable,
			Columns: button.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := bu.mutation.RemovedRolesIDs(); len(nodes) > 0 && !bu.mutation.RolesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   button.RolesTable,
			Columns: button.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := bu.mutation.RolesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   button.RolesTable,
			Columns: button.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if bu.mutation.MenuCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   button.MenuTable,
			Columns: []string{button.MenuColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := bu.mutation.MenuIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   button.MenuTable,
			Columns: []string{button.MenuColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, bu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{button.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	bu.mutation.done = true
	return n, nil
}

// ButtonUpdateOne is the builder for updating a single Button entity.
type ButtonUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *ButtonMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (buo *ButtonUpdateOne) SetUpdatedAt(t time.Time) *ButtonUpdateOne {
	buo.mutation.SetUpdatedAt(t)
	return buo
}

// SetSort sets the "sort" field.
func (buo *ButtonUpdateOne) SetSort(u uint32) *ButtonUpdateOne {
	buo.mutation.ResetSort()
	buo.mutation.SetSort(u)
	return buo
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (buo *ButtonUpdateOne) SetNillableSort(u *uint32) *ButtonUpdateOne {
	if u != nil {
		buo.SetSort(*u)
	}
	return buo
}

// AddSort adds u to the "sort" field.
func (buo *ButtonUpdateOne) AddSort(u int32) *ButtonUpdateOne {
	buo.mutation.AddSort(u)
	return buo
}

// SetDeletedAt sets the "deleted_at" field.
func (buo *ButtonUpdateOne) SetDeletedAt(t time.Time) *ButtonUpdateOne {
	buo.mutation.SetDeletedAt(t)
	return buo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (buo *ButtonUpdateOne) SetNillableDeletedAt(t *time.Time) *ButtonUpdateOne {
	if t != nil {
		buo.SetDeletedAt(*t)
	}
	return buo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (buo *ButtonUpdateOne) ClearDeletedAt() *ButtonUpdateOne {
	buo.mutation.ClearDeletedAt()
	return buo
}

// SetName sets the "name" field.
func (buo *ButtonUpdateOne) SetName(s string) *ButtonUpdateOne {
	buo.mutation.SetName(s)
	return buo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (buo *ButtonUpdateOne) SetNillableName(s *string) *ButtonUpdateOne {
	if s != nil {
		buo.SetName(*s)
	}
	return buo
}

// ClearName clears the value of the "name" field.
func (buo *ButtonUpdateOne) ClearName() *ButtonUpdateOne {
	buo.mutation.ClearName()
	return buo
}

// SetCode sets the "code" field.
func (buo *ButtonUpdateOne) SetCode(s string) *ButtonUpdateOne {
	buo.mutation.SetCode(s)
	return buo
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (buo *ButtonUpdateOne) SetNillableCode(s *string) *ButtonUpdateOne {
	if s != nil {
		buo.SetCode(*s)
	}
	return buo
}

// SetMenuID sets the "menu_id" field.
func (buo *ButtonUpdateOne) SetMenuID(s string) *ButtonUpdateOne {
	buo.mutation.SetMenuID(s)
	return buo
}

// SetNillableMenuID sets the "menu_id" field if the given value is not nil.
func (buo *ButtonUpdateOne) SetNillableMenuID(s *string) *ButtonUpdateOne {
	if s != nil {
		buo.SetMenuID(*s)
	}
	return buo
}

// ClearMenuID clears the value of the "menu_id" field.
func (buo *ButtonUpdateOne) ClearMenuID() *ButtonUpdateOne {
	buo.mutation.ClearMenuID()
	return buo
}

// AddRoleIDs adds the "roles" edge to the Role entity by IDs.
func (buo *ButtonUpdateOne) AddRoleIDs(ids ...string) *ButtonUpdateOne {
	buo.mutation.AddRoleIDs(ids...)
	return buo
}

// AddRoles adds the "roles" edges to the Role entity.
func (buo *ButtonUpdateOne) AddRoles(r ...*Role) *ButtonUpdateOne {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return buo.AddRoleIDs(ids...)
}

// SetMenu sets the "menu" edge to the Menu entity.
func (buo *ButtonUpdateOne) SetMenu(m *Menu) *ButtonUpdateOne {
	return buo.SetMenuID(m.ID)
}

// Mutation returns the ButtonMutation object of the builder.
func (buo *ButtonUpdateOne) Mutation() *ButtonMutation {
	return buo.mutation
}

// ClearRoles clears all "roles" edges to the Role entity.
func (buo *ButtonUpdateOne) ClearRoles() *ButtonUpdateOne {
	buo.mutation.ClearRoles()
	return buo
}

// RemoveRoleIDs removes the "roles" edge to Role entities by IDs.
func (buo *ButtonUpdateOne) RemoveRoleIDs(ids ...string) *ButtonUpdateOne {
	buo.mutation.RemoveRoleIDs(ids...)
	return buo
}

// RemoveRoles removes "roles" edges to Role entities.
func (buo *ButtonUpdateOne) RemoveRoles(r ...*Role) *ButtonUpdateOne {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return buo.RemoveRoleIDs(ids...)
}

// ClearMenu clears the "menu" edge to the Menu entity.
func (buo *ButtonUpdateOne) ClearMenu() *ButtonUpdateOne {
	buo.mutation.ClearMenu()
	return buo
}

// Where appends a list predicates to the ButtonUpdate builder.
func (buo *ButtonUpdateOne) Where(ps ...predicate.Button) *ButtonUpdateOne {
	buo.mutation.Where(ps...)
	return buo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (buo *ButtonUpdateOne) Select(field string, fields ...string) *ButtonUpdateOne {
	buo.fields = append([]string{field}, fields...)
	return buo
}

// Save executes the query and returns the updated Button entity.
func (buo *ButtonUpdateOne) Save(ctx context.Context) (*Button, error) {
	if err := buo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, buo.sqlSave, buo.mutation, buo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (buo *ButtonUpdateOne) SaveX(ctx context.Context) *Button {
	node, err := buo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (buo *ButtonUpdateOne) Exec(ctx context.Context) error {
	_, err := buo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (buo *ButtonUpdateOne) ExecX(ctx context.Context) {
	if err := buo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (buo *ButtonUpdateOne) defaults() error {
	if _, ok := buo.mutation.UpdatedAt(); !ok {
		if button.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized button.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := button.UpdateDefaultUpdatedAt()
		buo.mutation.SetUpdatedAt(v)
	}
	return nil
}

func (buo *ButtonUpdateOne) sqlSave(ctx context.Context) (_node *Button, err error) {
	_spec := sqlgraph.NewUpdateSpec(button.Table, button.Columns, sqlgraph.NewFieldSpec(button.FieldID, field.TypeString))
	id, ok := buo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Button.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := buo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, button.FieldID)
		for _, f := range fields {
			if !button.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != button.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := buo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := buo.mutation.UpdatedAt(); ok {
		_spec.SetField(button.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := buo.mutation.Sort(); ok {
		_spec.SetField(button.FieldSort, field.TypeUint32, value)
	}
	if value, ok := buo.mutation.AddedSort(); ok {
		_spec.AddField(button.FieldSort, field.TypeUint32, value)
	}
	if value, ok := buo.mutation.DeletedAt(); ok {
		_spec.SetField(button.FieldDeletedAt, field.TypeTime, value)
	}
	if buo.mutation.DeletedAtCleared() {
		_spec.ClearField(button.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := buo.mutation.Name(); ok {
		_spec.SetField(button.FieldName, field.TypeString, value)
	}
	if buo.mutation.NameCleared() {
		_spec.ClearField(button.FieldName, field.TypeString)
	}
	if value, ok := buo.mutation.Code(); ok {
		_spec.SetField(button.FieldCode, field.TypeString, value)
	}
	if buo.mutation.RolesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   button.RolesTable,
			Columns: button.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := buo.mutation.RemovedRolesIDs(); len(nodes) > 0 && !buo.mutation.RolesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   button.RolesTable,
			Columns: button.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := buo.mutation.RolesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   button.RolesTable,
			Columns: button.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if buo.mutation.MenuCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   button.MenuTable,
			Columns: []string{button.MenuColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := buo.mutation.MenuIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   button.MenuTable,
			Columns: []string{button.MenuColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Button{config: buo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, buo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{button.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	buo.mutation.done = true
	return _node, nil
}
