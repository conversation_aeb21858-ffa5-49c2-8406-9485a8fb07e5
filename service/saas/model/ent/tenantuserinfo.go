// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"phoenix/service/saas/model/ent/tenant"
	"phoenix/service/saas/model/ent/tenantuserinfo"
	"phoenix/service/saas/model/ent/user"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// TenantUserInfo is the model entity for the TenantUserInfo schema.
type TenantUserInfo struct {
	config `json:"-"`
	// ID of the ent.
	// Snowflake ID | 全局唯一ID
	ID string `json:"id,omitempty"`
	// Created Time | 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// Updated Time | 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// Sort number | 排序编号
	Sort uint32 `json:"sort,omitempty"`
	// Deleted Time | 删除时间（软删除标识）
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// Tenant ID | 租户 ID
	TenantID string `json:"tenant_id,omitempty"`
	// User ID | 用户 ID
	UserID string `json:"user_id,omitempty"`
	// Extra information | 额外信息(json对象格式存储，用于存储的额外展示信息)
	Extra string `json:"extra,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the TenantUserInfoQuery when eager-loading is set.
	Edges             TenantUserInfoEdges `json:"edges"`
	user_tenant_infos *string
	selectValues      sql.SelectValues
}

// TenantUserInfoEdges holds the relations/edges for other nodes in the graph.
type TenantUserInfoEdges struct {
	// TenantUserInfoTenant holds the value of the tenant_user_info_tenant edge.
	TenantUserInfoTenant *Tenant `json:"tenant_user_info_tenant,omitempty"`
	// TenantUserInfoUser holds the value of the tenant_user_info_user edge.
	TenantUserInfoUser *User `json:"tenant_user_info_user,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// TenantUserInfoTenantOrErr returns the TenantUserInfoTenant value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e TenantUserInfoEdges) TenantUserInfoTenantOrErr() (*Tenant, error) {
	if e.TenantUserInfoTenant != nil {
		return e.TenantUserInfoTenant, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: tenant.Label}
	}
	return nil, &NotLoadedError{edge: "tenant_user_info_tenant"}
}

// TenantUserInfoUserOrErr returns the TenantUserInfoUser value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e TenantUserInfoEdges) TenantUserInfoUserOrErr() (*User, error) {
	if e.TenantUserInfoUser != nil {
		return e.TenantUserInfoUser, nil
	} else if e.loadedTypes[1] {
		return nil, &NotFoundError{label: user.Label}
	}
	return nil, &NotLoadedError{edge: "tenant_user_info_user"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*TenantUserInfo) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case tenantuserinfo.FieldSort:
			values[i] = new(sql.NullInt64)
		case tenantuserinfo.FieldID, tenantuserinfo.FieldTenantID, tenantuserinfo.FieldUserID, tenantuserinfo.FieldExtra:
			values[i] = new(sql.NullString)
		case tenantuserinfo.FieldCreatedAt, tenantuserinfo.FieldUpdatedAt, tenantuserinfo.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		case tenantuserinfo.ForeignKeys[0]: // user_tenant_infos
			values[i] = new(sql.NullString)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the TenantUserInfo fields.
func (tui *TenantUserInfo) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case tenantuserinfo.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				tui.ID = value.String
			}
		case tenantuserinfo.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				tui.CreatedAt = value.Time
			}
		case tenantuserinfo.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				tui.UpdatedAt = value.Time
			}
		case tenantuserinfo.FieldSort:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field sort", values[i])
			} else if value.Valid {
				tui.Sort = uint32(value.Int64)
			}
		case tenantuserinfo.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				tui.DeletedAt = value.Time
			}
		case tenantuserinfo.FieldTenantID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				tui.TenantID = value.String
			}
		case tenantuserinfo.FieldUserID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value.Valid {
				tui.UserID = value.String
			}
		case tenantuserinfo.FieldExtra:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field extra", values[i])
			} else if value.Valid {
				tui.Extra = value.String
			}
		case tenantuserinfo.ForeignKeys[0]:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field user_tenant_infos", values[i])
			} else if value.Valid {
				tui.user_tenant_infos = new(string)
				*tui.user_tenant_infos = value.String
			}
		default:
			tui.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the TenantUserInfo.
// This includes values selected through modifiers, order, etc.
func (tui *TenantUserInfo) Value(name string) (ent.Value, error) {
	return tui.selectValues.Get(name)
}

// QueryTenantUserInfoTenant queries the "tenant_user_info_tenant" edge of the TenantUserInfo entity.
func (tui *TenantUserInfo) QueryTenantUserInfoTenant() *TenantQuery {
	return NewTenantUserInfoClient(tui.config).QueryTenantUserInfoTenant(tui)
}

// QueryTenantUserInfoUser queries the "tenant_user_info_user" edge of the TenantUserInfo entity.
func (tui *TenantUserInfo) QueryTenantUserInfoUser() *UserQuery {
	return NewTenantUserInfoClient(tui.config).QueryTenantUserInfoUser(tui)
}

// Update returns a builder for updating this TenantUserInfo.
// Note that you need to call TenantUserInfo.Unwrap() before calling this method if this TenantUserInfo
// was returned from a transaction, and the transaction was committed or rolled back.
func (tui *TenantUserInfo) Update() *TenantUserInfoUpdateOne {
	return NewTenantUserInfoClient(tui.config).UpdateOne(tui)
}

// Unwrap unwraps the TenantUserInfo entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (tui *TenantUserInfo) Unwrap() *TenantUserInfo {
	_tx, ok := tui.config.driver.(*txDriver)
	if !ok {
		panic("ent: TenantUserInfo is not a transactional entity")
	}
	tui.config.driver = _tx.drv
	return tui
}

// String implements the fmt.Stringer.
func (tui *TenantUserInfo) String() string {
	var builder strings.Builder
	builder.WriteString("TenantUserInfo(")
	builder.WriteString(fmt.Sprintf("id=%v, ", tui.ID))
	builder.WriteString("created_at=")
	builder.WriteString(tui.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(tui.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("sort=")
	builder.WriteString(fmt.Sprintf("%v", tui.Sort))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(tui.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("tenant_id=")
	builder.WriteString(tui.TenantID)
	builder.WriteString(", ")
	builder.WriteString("user_id=")
	builder.WriteString(tui.UserID)
	builder.WriteString(", ")
	builder.WriteString("extra=")
	builder.WriteString(tui.Extra)
	builder.WriteByte(')')
	return builder.String()
}

// TenantUserInfos is a parsable slice of TenantUserInfo.
type TenantUserInfos []*TenantUserInfo
