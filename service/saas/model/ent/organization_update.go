// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/organization"
	"phoenix/service/saas/model/ent/organizationuserinfo"
	"phoenix/service/saas/model/ent/predicate"
	"phoenix/service/saas/model/ent/tenant"
	"phoenix/service/saas/model/ent/user"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// OrganizationUpdate is the builder for updating Organization entities.
type OrganizationUpdate struct {
	config
	hooks    []Hook
	mutation *OrganizationMutation
}

// Where appends a list predicates to the OrganizationUpdate builder.
func (ou *OrganizationUpdate) Where(ps ...predicate.Organization) *OrganizationUpdate {
	ou.mutation.Where(ps...)
	return ou
}

// SetUpdatedAt sets the "updated_at" field.
func (ou *OrganizationUpdate) SetUpdatedAt(t time.Time) *OrganizationUpdate {
	ou.mutation.SetUpdatedAt(t)
	return ou
}

// SetStatus sets the "status" field.
func (ou *OrganizationUpdate) SetStatus(b bool) *OrganizationUpdate {
	ou.mutation.SetStatus(b)
	return ou
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ou *OrganizationUpdate) SetNillableStatus(b *bool) *OrganizationUpdate {
	if b != nil {
		ou.SetStatus(*b)
	}
	return ou
}

// ClearStatus clears the value of the "status" field.
func (ou *OrganizationUpdate) ClearStatus() *OrganizationUpdate {
	ou.mutation.ClearStatus()
	return ou
}

// SetSort sets the "sort" field.
func (ou *OrganizationUpdate) SetSort(u uint32) *OrganizationUpdate {
	ou.mutation.ResetSort()
	ou.mutation.SetSort(u)
	return ou
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (ou *OrganizationUpdate) SetNillableSort(u *uint32) *OrganizationUpdate {
	if u != nil {
		ou.SetSort(*u)
	}
	return ou
}

// AddSort adds u to the "sort" field.
func (ou *OrganizationUpdate) AddSort(u int32) *OrganizationUpdate {
	ou.mutation.AddSort(u)
	return ou
}

// SetTenantID sets the "tenant_id" field.
func (ou *OrganizationUpdate) SetTenantID(s string) *OrganizationUpdate {
	ou.mutation.SetTenantID(s)
	return ou
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (ou *OrganizationUpdate) SetNillableTenantID(s *string) *OrganizationUpdate {
	if s != nil {
		ou.SetTenantID(*s)
	}
	return ou
}

// SetDeletedAt sets the "deleted_at" field.
func (ou *OrganizationUpdate) SetDeletedAt(t time.Time) *OrganizationUpdate {
	ou.mutation.SetDeletedAt(t)
	return ou
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (ou *OrganizationUpdate) SetNillableDeletedAt(t *time.Time) *OrganizationUpdate {
	if t != nil {
		ou.SetDeletedAt(*t)
	}
	return ou
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (ou *OrganizationUpdate) ClearDeletedAt() *OrganizationUpdate {
	ou.mutation.ClearDeletedAt()
	return ou
}

// SetName sets the "name" field.
func (ou *OrganizationUpdate) SetName(s string) *OrganizationUpdate {
	ou.mutation.SetName(s)
	return ou
}

// SetNillableName sets the "name" field if the given value is not nil.
func (ou *OrganizationUpdate) SetNillableName(s *string) *OrganizationUpdate {
	if s != nil {
		ou.SetName(*s)
	}
	return ou
}

// SetAncestors sets the "ancestors" field.
func (ou *OrganizationUpdate) SetAncestors(s string) *OrganizationUpdate {
	ou.mutation.SetAncestors(s)
	return ou
}

// SetNillableAncestors sets the "ancestors" field if the given value is not nil.
func (ou *OrganizationUpdate) SetNillableAncestors(s *string) *OrganizationUpdate {
	if s != nil {
		ou.SetAncestors(*s)
	}
	return ou
}

// SetCode sets the "code" field.
func (ou *OrganizationUpdate) SetCode(s string) *OrganizationUpdate {
	ou.mutation.SetCode(s)
	return ou
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (ou *OrganizationUpdate) SetNillableCode(s *string) *OrganizationUpdate {
	if s != nil {
		ou.SetCode(*s)
	}
	return ou
}

// SetNodeType sets the "node_type" field.
func (ou *OrganizationUpdate) SetNodeType(u uint32) *OrganizationUpdate {
	ou.mutation.ResetNodeType()
	ou.mutation.SetNodeType(u)
	return ou
}

// SetNillableNodeType sets the "node_type" field if the given value is not nil.
func (ou *OrganizationUpdate) SetNillableNodeType(u *uint32) *OrganizationUpdate {
	if u != nil {
		ou.SetNodeType(*u)
	}
	return ou
}

// AddNodeType adds u to the "node_type" field.
func (ou *OrganizationUpdate) AddNodeType(u int32) *OrganizationUpdate {
	ou.mutation.AddNodeType(u)
	return ou
}

// SetLeader sets the "leader" field.
func (ou *OrganizationUpdate) SetLeader(s string) *OrganizationUpdate {
	ou.mutation.SetLeader(s)
	return ou
}

// SetNillableLeader sets the "leader" field if the given value is not nil.
func (ou *OrganizationUpdate) SetNillableLeader(s *string) *OrganizationUpdate {
	if s != nil {
		ou.SetLeader(*s)
	}
	return ou
}

// SetPhone sets the "phone" field.
func (ou *OrganizationUpdate) SetPhone(s string) *OrganizationUpdate {
	ou.mutation.SetPhone(s)
	return ou
}

// SetNillablePhone sets the "phone" field if the given value is not nil.
func (ou *OrganizationUpdate) SetNillablePhone(s *string) *OrganizationUpdate {
	if s != nil {
		ou.SetPhone(*s)
	}
	return ou
}

// SetEmail sets the "email" field.
func (ou *OrganizationUpdate) SetEmail(s string) *OrganizationUpdate {
	ou.mutation.SetEmail(s)
	return ou
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (ou *OrganizationUpdate) SetNillableEmail(s *string) *OrganizationUpdate {
	if s != nil {
		ou.SetEmail(*s)
	}
	return ou
}

// SetRemark sets the "remark" field.
func (ou *OrganizationUpdate) SetRemark(s string) *OrganizationUpdate {
	ou.mutation.SetRemark(s)
	return ou
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (ou *OrganizationUpdate) SetNillableRemark(s *string) *OrganizationUpdate {
	if s != nil {
		ou.SetRemark(*s)
	}
	return ou
}

// SetParentID sets the "parent_id" field.
func (ou *OrganizationUpdate) SetParentID(s string) *OrganizationUpdate {
	ou.mutation.SetParentID(s)
	return ou
}

// SetNillableParentID sets the "parent_id" field if the given value is not nil.
func (ou *OrganizationUpdate) SetNillableParentID(s *string) *OrganizationUpdate {
	if s != nil {
		ou.SetParentID(*s)
	}
	return ou
}

// ClearParentID clears the value of the "parent_id" field.
func (ou *OrganizationUpdate) ClearParentID() *OrganizationUpdate {
	ou.mutation.ClearParentID()
	return ou
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (ou *OrganizationUpdate) SetTenant(t *Tenant) *OrganizationUpdate {
	return ou.SetTenantID(t.ID)
}

// SetParent sets the "parent" edge to the Organization entity.
func (ou *OrganizationUpdate) SetParent(o *Organization) *OrganizationUpdate {
	return ou.SetParentID(o.ID)
}

// AddChildIDs adds the "children" edge to the Organization entity by IDs.
func (ou *OrganizationUpdate) AddChildIDs(ids ...string) *OrganizationUpdate {
	ou.mutation.AddChildIDs(ids...)
	return ou
}

// AddChildren adds the "children" edges to the Organization entity.
func (ou *OrganizationUpdate) AddChildren(o ...*Organization) *OrganizationUpdate {
	ids := make([]string, len(o))
	for i := range o {
		ids[i] = o[i].ID
	}
	return ou.AddChildIDs(ids...)
}

// AddUserIDs adds the "users" edge to the User entity by IDs.
func (ou *OrganizationUpdate) AddUserIDs(ids ...string) *OrganizationUpdate {
	ou.mutation.AddUserIDs(ids...)
	return ou
}

// AddUsers adds the "users" edges to the User entity.
func (ou *OrganizationUpdate) AddUsers(u ...*User) *OrganizationUpdate {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return ou.AddUserIDs(ids...)
}

// AddOrganizationInfoIDs adds the "organization_infos" edge to the OrganizationUserInfo entity by IDs.
func (ou *OrganizationUpdate) AddOrganizationInfoIDs(ids ...string) *OrganizationUpdate {
	ou.mutation.AddOrganizationInfoIDs(ids...)
	return ou
}

// AddOrganizationInfos adds the "organization_infos" edges to the OrganizationUserInfo entity.
func (ou *OrganizationUpdate) AddOrganizationInfos(o ...*OrganizationUserInfo) *OrganizationUpdate {
	ids := make([]string, len(o))
	for i := range o {
		ids[i] = o[i].ID
	}
	return ou.AddOrganizationInfoIDs(ids...)
}

// Mutation returns the OrganizationMutation object of the builder.
func (ou *OrganizationUpdate) Mutation() *OrganizationMutation {
	return ou.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (ou *OrganizationUpdate) ClearTenant() *OrganizationUpdate {
	ou.mutation.ClearTenant()
	return ou
}

// ClearParent clears the "parent" edge to the Organization entity.
func (ou *OrganizationUpdate) ClearParent() *OrganizationUpdate {
	ou.mutation.ClearParent()
	return ou
}

// ClearChildren clears all "children" edges to the Organization entity.
func (ou *OrganizationUpdate) ClearChildren() *OrganizationUpdate {
	ou.mutation.ClearChildren()
	return ou
}

// RemoveChildIDs removes the "children" edge to Organization entities by IDs.
func (ou *OrganizationUpdate) RemoveChildIDs(ids ...string) *OrganizationUpdate {
	ou.mutation.RemoveChildIDs(ids...)
	return ou
}

// RemoveChildren removes "children" edges to Organization entities.
func (ou *OrganizationUpdate) RemoveChildren(o ...*Organization) *OrganizationUpdate {
	ids := make([]string, len(o))
	for i := range o {
		ids[i] = o[i].ID
	}
	return ou.RemoveChildIDs(ids...)
}

// ClearUsers clears all "users" edges to the User entity.
func (ou *OrganizationUpdate) ClearUsers() *OrganizationUpdate {
	ou.mutation.ClearUsers()
	return ou
}

// RemoveUserIDs removes the "users" edge to User entities by IDs.
func (ou *OrganizationUpdate) RemoveUserIDs(ids ...string) *OrganizationUpdate {
	ou.mutation.RemoveUserIDs(ids...)
	return ou
}

// RemoveUsers removes "users" edges to User entities.
func (ou *OrganizationUpdate) RemoveUsers(u ...*User) *OrganizationUpdate {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return ou.RemoveUserIDs(ids...)
}

// ClearOrganizationInfos clears all "organization_infos" edges to the OrganizationUserInfo entity.
func (ou *OrganizationUpdate) ClearOrganizationInfos() *OrganizationUpdate {
	ou.mutation.ClearOrganizationInfos()
	return ou
}

// RemoveOrganizationInfoIDs removes the "organization_infos" edge to OrganizationUserInfo entities by IDs.
func (ou *OrganizationUpdate) RemoveOrganizationInfoIDs(ids ...string) *OrganizationUpdate {
	ou.mutation.RemoveOrganizationInfoIDs(ids...)
	return ou
}

// RemoveOrganizationInfos removes "organization_infos" edges to OrganizationUserInfo entities.
func (ou *OrganizationUpdate) RemoveOrganizationInfos(o ...*OrganizationUserInfo) *OrganizationUpdate {
	ids := make([]string, len(o))
	for i := range o {
		ids[i] = o[i].ID
	}
	return ou.RemoveOrganizationInfoIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (ou *OrganizationUpdate) Save(ctx context.Context) (int, error) {
	if err := ou.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, ou.sqlSave, ou.mutation, ou.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ou *OrganizationUpdate) SaveX(ctx context.Context) int {
	affected, err := ou.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (ou *OrganizationUpdate) Exec(ctx context.Context) error {
	_, err := ou.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ou *OrganizationUpdate) ExecX(ctx context.Context) {
	if err := ou.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ou *OrganizationUpdate) defaults() error {
	if _, ok := ou.mutation.UpdatedAt(); !ok {
		if organization.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized organization.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := organization.UpdateDefaultUpdatedAt()
		ou.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (ou *OrganizationUpdate) check() error {
	if _, ok := ou.mutation.TenantID(); ou.mutation.TenantCleared() && !ok {
		return errors.New(`ent: clearing a required unique edge "Organization.tenant"`)
	}
	return nil
}

func (ou *OrganizationUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := ou.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(organization.Table, organization.Columns, sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString))
	if ps := ou.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ou.mutation.UpdatedAt(); ok {
		_spec.SetField(organization.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := ou.mutation.Status(); ok {
		_spec.SetField(organization.FieldStatus, field.TypeBool, value)
	}
	if ou.mutation.StatusCleared() {
		_spec.ClearField(organization.FieldStatus, field.TypeBool)
	}
	if value, ok := ou.mutation.Sort(); ok {
		_spec.SetField(organization.FieldSort, field.TypeUint32, value)
	}
	if value, ok := ou.mutation.AddedSort(); ok {
		_spec.AddField(organization.FieldSort, field.TypeUint32, value)
	}
	if value, ok := ou.mutation.DeletedAt(); ok {
		_spec.SetField(organization.FieldDeletedAt, field.TypeTime, value)
	}
	if ou.mutation.DeletedAtCleared() {
		_spec.ClearField(organization.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := ou.mutation.Name(); ok {
		_spec.SetField(organization.FieldName, field.TypeString, value)
	}
	if value, ok := ou.mutation.Ancestors(); ok {
		_spec.SetField(organization.FieldAncestors, field.TypeString, value)
	}
	if value, ok := ou.mutation.Code(); ok {
		_spec.SetField(organization.FieldCode, field.TypeString, value)
	}
	if value, ok := ou.mutation.NodeType(); ok {
		_spec.SetField(organization.FieldNodeType, field.TypeUint32, value)
	}
	if value, ok := ou.mutation.AddedNodeType(); ok {
		_spec.AddField(organization.FieldNodeType, field.TypeUint32, value)
	}
	if value, ok := ou.mutation.Leader(); ok {
		_spec.SetField(organization.FieldLeader, field.TypeString, value)
	}
	if value, ok := ou.mutation.Phone(); ok {
		_spec.SetField(organization.FieldPhone, field.TypeString, value)
	}
	if value, ok := ou.mutation.Email(); ok {
		_spec.SetField(organization.FieldEmail, field.TypeString, value)
	}
	if value, ok := ou.mutation.Remark(); ok {
		_spec.SetField(organization.FieldRemark, field.TypeString, value)
	}
	if ou.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   organization.TenantTable,
			Columns: []string{organization.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ou.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   organization.TenantTable,
			Columns: []string{organization.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ou.mutation.ParentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   organization.ParentTable,
			Columns: []string{organization.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ou.mutation.ParentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   organization.ParentTable,
			Columns: []string{organization.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ou.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   organization.ChildrenTable,
			Columns: []string{organization.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ou.mutation.RemovedChildrenIDs(); len(nodes) > 0 && !ou.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   organization.ChildrenTable,
			Columns: []string{organization.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ou.mutation.ChildrenIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   organization.ChildrenTable,
			Columns: []string{organization.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ou.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   organization.UsersTable,
			Columns: organization.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ou.mutation.RemovedUsersIDs(); len(nodes) > 0 && !ou.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   organization.UsersTable,
			Columns: organization.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ou.mutation.UsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   organization.UsersTable,
			Columns: organization.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ou.mutation.OrganizationInfosCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   organization.OrganizationInfosTable,
			Columns: []string{organization.OrganizationInfosColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organizationuserinfo.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ou.mutation.RemovedOrganizationInfosIDs(); len(nodes) > 0 && !ou.mutation.OrganizationInfosCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   organization.OrganizationInfosTable,
			Columns: []string{organization.OrganizationInfosColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organizationuserinfo.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ou.mutation.OrganizationInfosIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   organization.OrganizationInfosTable,
			Columns: []string{organization.OrganizationInfosColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organizationuserinfo.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, ou.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{organization.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	ou.mutation.done = true
	return n, nil
}

// OrganizationUpdateOne is the builder for updating a single Organization entity.
type OrganizationUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *OrganizationMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (ouo *OrganizationUpdateOne) SetUpdatedAt(t time.Time) *OrganizationUpdateOne {
	ouo.mutation.SetUpdatedAt(t)
	return ouo
}

// SetStatus sets the "status" field.
func (ouo *OrganizationUpdateOne) SetStatus(b bool) *OrganizationUpdateOne {
	ouo.mutation.SetStatus(b)
	return ouo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ouo *OrganizationUpdateOne) SetNillableStatus(b *bool) *OrganizationUpdateOne {
	if b != nil {
		ouo.SetStatus(*b)
	}
	return ouo
}

// ClearStatus clears the value of the "status" field.
func (ouo *OrganizationUpdateOne) ClearStatus() *OrganizationUpdateOne {
	ouo.mutation.ClearStatus()
	return ouo
}

// SetSort sets the "sort" field.
func (ouo *OrganizationUpdateOne) SetSort(u uint32) *OrganizationUpdateOne {
	ouo.mutation.ResetSort()
	ouo.mutation.SetSort(u)
	return ouo
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (ouo *OrganizationUpdateOne) SetNillableSort(u *uint32) *OrganizationUpdateOne {
	if u != nil {
		ouo.SetSort(*u)
	}
	return ouo
}

// AddSort adds u to the "sort" field.
func (ouo *OrganizationUpdateOne) AddSort(u int32) *OrganizationUpdateOne {
	ouo.mutation.AddSort(u)
	return ouo
}

// SetTenantID sets the "tenant_id" field.
func (ouo *OrganizationUpdateOne) SetTenantID(s string) *OrganizationUpdateOne {
	ouo.mutation.SetTenantID(s)
	return ouo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (ouo *OrganizationUpdateOne) SetNillableTenantID(s *string) *OrganizationUpdateOne {
	if s != nil {
		ouo.SetTenantID(*s)
	}
	return ouo
}

// SetDeletedAt sets the "deleted_at" field.
func (ouo *OrganizationUpdateOne) SetDeletedAt(t time.Time) *OrganizationUpdateOne {
	ouo.mutation.SetDeletedAt(t)
	return ouo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (ouo *OrganizationUpdateOne) SetNillableDeletedAt(t *time.Time) *OrganizationUpdateOne {
	if t != nil {
		ouo.SetDeletedAt(*t)
	}
	return ouo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (ouo *OrganizationUpdateOne) ClearDeletedAt() *OrganizationUpdateOne {
	ouo.mutation.ClearDeletedAt()
	return ouo
}

// SetName sets the "name" field.
func (ouo *OrganizationUpdateOne) SetName(s string) *OrganizationUpdateOne {
	ouo.mutation.SetName(s)
	return ouo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (ouo *OrganizationUpdateOne) SetNillableName(s *string) *OrganizationUpdateOne {
	if s != nil {
		ouo.SetName(*s)
	}
	return ouo
}

// SetAncestors sets the "ancestors" field.
func (ouo *OrganizationUpdateOne) SetAncestors(s string) *OrganizationUpdateOne {
	ouo.mutation.SetAncestors(s)
	return ouo
}

// SetNillableAncestors sets the "ancestors" field if the given value is not nil.
func (ouo *OrganizationUpdateOne) SetNillableAncestors(s *string) *OrganizationUpdateOne {
	if s != nil {
		ouo.SetAncestors(*s)
	}
	return ouo
}

// SetCode sets the "code" field.
func (ouo *OrganizationUpdateOne) SetCode(s string) *OrganizationUpdateOne {
	ouo.mutation.SetCode(s)
	return ouo
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (ouo *OrganizationUpdateOne) SetNillableCode(s *string) *OrganizationUpdateOne {
	if s != nil {
		ouo.SetCode(*s)
	}
	return ouo
}

// SetNodeType sets the "node_type" field.
func (ouo *OrganizationUpdateOne) SetNodeType(u uint32) *OrganizationUpdateOne {
	ouo.mutation.ResetNodeType()
	ouo.mutation.SetNodeType(u)
	return ouo
}

// SetNillableNodeType sets the "node_type" field if the given value is not nil.
func (ouo *OrganizationUpdateOne) SetNillableNodeType(u *uint32) *OrganizationUpdateOne {
	if u != nil {
		ouo.SetNodeType(*u)
	}
	return ouo
}

// AddNodeType adds u to the "node_type" field.
func (ouo *OrganizationUpdateOne) AddNodeType(u int32) *OrganizationUpdateOne {
	ouo.mutation.AddNodeType(u)
	return ouo
}

// SetLeader sets the "leader" field.
func (ouo *OrganizationUpdateOne) SetLeader(s string) *OrganizationUpdateOne {
	ouo.mutation.SetLeader(s)
	return ouo
}

// SetNillableLeader sets the "leader" field if the given value is not nil.
func (ouo *OrganizationUpdateOne) SetNillableLeader(s *string) *OrganizationUpdateOne {
	if s != nil {
		ouo.SetLeader(*s)
	}
	return ouo
}

// SetPhone sets the "phone" field.
func (ouo *OrganizationUpdateOne) SetPhone(s string) *OrganizationUpdateOne {
	ouo.mutation.SetPhone(s)
	return ouo
}

// SetNillablePhone sets the "phone" field if the given value is not nil.
func (ouo *OrganizationUpdateOne) SetNillablePhone(s *string) *OrganizationUpdateOne {
	if s != nil {
		ouo.SetPhone(*s)
	}
	return ouo
}

// SetEmail sets the "email" field.
func (ouo *OrganizationUpdateOne) SetEmail(s string) *OrganizationUpdateOne {
	ouo.mutation.SetEmail(s)
	return ouo
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (ouo *OrganizationUpdateOne) SetNillableEmail(s *string) *OrganizationUpdateOne {
	if s != nil {
		ouo.SetEmail(*s)
	}
	return ouo
}

// SetRemark sets the "remark" field.
func (ouo *OrganizationUpdateOne) SetRemark(s string) *OrganizationUpdateOne {
	ouo.mutation.SetRemark(s)
	return ouo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (ouo *OrganizationUpdateOne) SetNillableRemark(s *string) *OrganizationUpdateOne {
	if s != nil {
		ouo.SetRemark(*s)
	}
	return ouo
}

// SetParentID sets the "parent_id" field.
func (ouo *OrganizationUpdateOne) SetParentID(s string) *OrganizationUpdateOne {
	ouo.mutation.SetParentID(s)
	return ouo
}

// SetNillableParentID sets the "parent_id" field if the given value is not nil.
func (ouo *OrganizationUpdateOne) SetNillableParentID(s *string) *OrganizationUpdateOne {
	if s != nil {
		ouo.SetParentID(*s)
	}
	return ouo
}

// ClearParentID clears the value of the "parent_id" field.
func (ouo *OrganizationUpdateOne) ClearParentID() *OrganizationUpdateOne {
	ouo.mutation.ClearParentID()
	return ouo
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (ouo *OrganizationUpdateOne) SetTenant(t *Tenant) *OrganizationUpdateOne {
	return ouo.SetTenantID(t.ID)
}

// SetParent sets the "parent" edge to the Organization entity.
func (ouo *OrganizationUpdateOne) SetParent(o *Organization) *OrganizationUpdateOne {
	return ouo.SetParentID(o.ID)
}

// AddChildIDs adds the "children" edge to the Organization entity by IDs.
func (ouo *OrganizationUpdateOne) AddChildIDs(ids ...string) *OrganizationUpdateOne {
	ouo.mutation.AddChildIDs(ids...)
	return ouo
}

// AddChildren adds the "children" edges to the Organization entity.
func (ouo *OrganizationUpdateOne) AddChildren(o ...*Organization) *OrganizationUpdateOne {
	ids := make([]string, len(o))
	for i := range o {
		ids[i] = o[i].ID
	}
	return ouo.AddChildIDs(ids...)
}

// AddUserIDs adds the "users" edge to the User entity by IDs.
func (ouo *OrganizationUpdateOne) AddUserIDs(ids ...string) *OrganizationUpdateOne {
	ouo.mutation.AddUserIDs(ids...)
	return ouo
}

// AddUsers adds the "users" edges to the User entity.
func (ouo *OrganizationUpdateOne) AddUsers(u ...*User) *OrganizationUpdateOne {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return ouo.AddUserIDs(ids...)
}

// AddOrganizationInfoIDs adds the "organization_infos" edge to the OrganizationUserInfo entity by IDs.
func (ouo *OrganizationUpdateOne) AddOrganizationInfoIDs(ids ...string) *OrganizationUpdateOne {
	ouo.mutation.AddOrganizationInfoIDs(ids...)
	return ouo
}

// AddOrganizationInfos adds the "organization_infos" edges to the OrganizationUserInfo entity.
func (ouo *OrganizationUpdateOne) AddOrganizationInfos(o ...*OrganizationUserInfo) *OrganizationUpdateOne {
	ids := make([]string, len(o))
	for i := range o {
		ids[i] = o[i].ID
	}
	return ouo.AddOrganizationInfoIDs(ids...)
}

// Mutation returns the OrganizationMutation object of the builder.
func (ouo *OrganizationUpdateOne) Mutation() *OrganizationMutation {
	return ouo.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (ouo *OrganizationUpdateOne) ClearTenant() *OrganizationUpdateOne {
	ouo.mutation.ClearTenant()
	return ouo
}

// ClearParent clears the "parent" edge to the Organization entity.
func (ouo *OrganizationUpdateOne) ClearParent() *OrganizationUpdateOne {
	ouo.mutation.ClearParent()
	return ouo
}

// ClearChildren clears all "children" edges to the Organization entity.
func (ouo *OrganizationUpdateOne) ClearChildren() *OrganizationUpdateOne {
	ouo.mutation.ClearChildren()
	return ouo
}

// RemoveChildIDs removes the "children" edge to Organization entities by IDs.
func (ouo *OrganizationUpdateOne) RemoveChildIDs(ids ...string) *OrganizationUpdateOne {
	ouo.mutation.RemoveChildIDs(ids...)
	return ouo
}

// RemoveChildren removes "children" edges to Organization entities.
func (ouo *OrganizationUpdateOne) RemoveChildren(o ...*Organization) *OrganizationUpdateOne {
	ids := make([]string, len(o))
	for i := range o {
		ids[i] = o[i].ID
	}
	return ouo.RemoveChildIDs(ids...)
}

// ClearUsers clears all "users" edges to the User entity.
func (ouo *OrganizationUpdateOne) ClearUsers() *OrganizationUpdateOne {
	ouo.mutation.ClearUsers()
	return ouo
}

// RemoveUserIDs removes the "users" edge to User entities by IDs.
func (ouo *OrganizationUpdateOne) RemoveUserIDs(ids ...string) *OrganizationUpdateOne {
	ouo.mutation.RemoveUserIDs(ids...)
	return ouo
}

// RemoveUsers removes "users" edges to User entities.
func (ouo *OrganizationUpdateOne) RemoveUsers(u ...*User) *OrganizationUpdateOne {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return ouo.RemoveUserIDs(ids...)
}

// ClearOrganizationInfos clears all "organization_infos" edges to the OrganizationUserInfo entity.
func (ouo *OrganizationUpdateOne) ClearOrganizationInfos() *OrganizationUpdateOne {
	ouo.mutation.ClearOrganizationInfos()
	return ouo
}

// RemoveOrganizationInfoIDs removes the "organization_infos" edge to OrganizationUserInfo entities by IDs.
func (ouo *OrganizationUpdateOne) RemoveOrganizationInfoIDs(ids ...string) *OrganizationUpdateOne {
	ouo.mutation.RemoveOrganizationInfoIDs(ids...)
	return ouo
}

// RemoveOrganizationInfos removes "organization_infos" edges to OrganizationUserInfo entities.
func (ouo *OrganizationUpdateOne) RemoveOrganizationInfos(o ...*OrganizationUserInfo) *OrganizationUpdateOne {
	ids := make([]string, len(o))
	for i := range o {
		ids[i] = o[i].ID
	}
	return ouo.RemoveOrganizationInfoIDs(ids...)
}

// Where appends a list predicates to the OrganizationUpdate builder.
func (ouo *OrganizationUpdateOne) Where(ps ...predicate.Organization) *OrganizationUpdateOne {
	ouo.mutation.Where(ps...)
	return ouo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (ouo *OrganizationUpdateOne) Select(field string, fields ...string) *OrganizationUpdateOne {
	ouo.fields = append([]string{field}, fields...)
	return ouo
}

// Save executes the query and returns the updated Organization entity.
func (ouo *OrganizationUpdateOne) Save(ctx context.Context) (*Organization, error) {
	if err := ouo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, ouo.sqlSave, ouo.mutation, ouo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ouo *OrganizationUpdateOne) SaveX(ctx context.Context) *Organization {
	node, err := ouo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (ouo *OrganizationUpdateOne) Exec(ctx context.Context) error {
	_, err := ouo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ouo *OrganizationUpdateOne) ExecX(ctx context.Context) {
	if err := ouo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ouo *OrganizationUpdateOne) defaults() error {
	if _, ok := ouo.mutation.UpdatedAt(); !ok {
		if organization.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized organization.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := organization.UpdateDefaultUpdatedAt()
		ouo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (ouo *OrganizationUpdateOne) check() error {
	if _, ok := ouo.mutation.TenantID(); ouo.mutation.TenantCleared() && !ok {
		return errors.New(`ent: clearing a required unique edge "Organization.tenant"`)
	}
	return nil
}

func (ouo *OrganizationUpdateOne) sqlSave(ctx context.Context) (_node *Organization, err error) {
	if err := ouo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(organization.Table, organization.Columns, sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString))
	id, ok := ouo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Organization.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := ouo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, organization.FieldID)
		for _, f := range fields {
			if !organization.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != organization.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := ouo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ouo.mutation.UpdatedAt(); ok {
		_spec.SetField(organization.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := ouo.mutation.Status(); ok {
		_spec.SetField(organization.FieldStatus, field.TypeBool, value)
	}
	if ouo.mutation.StatusCleared() {
		_spec.ClearField(organization.FieldStatus, field.TypeBool)
	}
	if value, ok := ouo.mutation.Sort(); ok {
		_spec.SetField(organization.FieldSort, field.TypeUint32, value)
	}
	if value, ok := ouo.mutation.AddedSort(); ok {
		_spec.AddField(organization.FieldSort, field.TypeUint32, value)
	}
	if value, ok := ouo.mutation.DeletedAt(); ok {
		_spec.SetField(organization.FieldDeletedAt, field.TypeTime, value)
	}
	if ouo.mutation.DeletedAtCleared() {
		_spec.ClearField(organization.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := ouo.mutation.Name(); ok {
		_spec.SetField(organization.FieldName, field.TypeString, value)
	}
	if value, ok := ouo.mutation.Ancestors(); ok {
		_spec.SetField(organization.FieldAncestors, field.TypeString, value)
	}
	if value, ok := ouo.mutation.Code(); ok {
		_spec.SetField(organization.FieldCode, field.TypeString, value)
	}
	if value, ok := ouo.mutation.NodeType(); ok {
		_spec.SetField(organization.FieldNodeType, field.TypeUint32, value)
	}
	if value, ok := ouo.mutation.AddedNodeType(); ok {
		_spec.AddField(organization.FieldNodeType, field.TypeUint32, value)
	}
	if value, ok := ouo.mutation.Leader(); ok {
		_spec.SetField(organization.FieldLeader, field.TypeString, value)
	}
	if value, ok := ouo.mutation.Phone(); ok {
		_spec.SetField(organization.FieldPhone, field.TypeString, value)
	}
	if value, ok := ouo.mutation.Email(); ok {
		_spec.SetField(organization.FieldEmail, field.TypeString, value)
	}
	if value, ok := ouo.mutation.Remark(); ok {
		_spec.SetField(organization.FieldRemark, field.TypeString, value)
	}
	if ouo.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   organization.TenantTable,
			Columns: []string{organization.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ouo.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   organization.TenantTable,
			Columns: []string{organization.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ouo.mutation.ParentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   organization.ParentTable,
			Columns: []string{organization.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ouo.mutation.ParentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   organization.ParentTable,
			Columns: []string{organization.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ouo.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   organization.ChildrenTable,
			Columns: []string{organization.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ouo.mutation.RemovedChildrenIDs(); len(nodes) > 0 && !ouo.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   organization.ChildrenTable,
			Columns: []string{organization.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ouo.mutation.ChildrenIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   organization.ChildrenTable,
			Columns: []string{organization.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ouo.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   organization.UsersTable,
			Columns: organization.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ouo.mutation.RemovedUsersIDs(); len(nodes) > 0 && !ouo.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   organization.UsersTable,
			Columns: organization.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ouo.mutation.UsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   organization.UsersTable,
			Columns: organization.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ouo.mutation.OrganizationInfosCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   organization.OrganizationInfosTable,
			Columns: []string{organization.OrganizationInfosColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organizationuserinfo.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ouo.mutation.RemovedOrganizationInfosIDs(); len(nodes) > 0 && !ouo.mutation.OrganizationInfosCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   organization.OrganizationInfosTable,
			Columns: []string{organization.OrganizationInfosColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organizationuserinfo.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ouo.mutation.OrganizationInfosIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   organization.OrganizationInfosTable,
			Columns: []string{organization.OrganizationInfosColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organizationuserinfo.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Organization{config: ouo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, ouo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{organization.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	ouo.mutation.done = true
	return _node, nil
}
