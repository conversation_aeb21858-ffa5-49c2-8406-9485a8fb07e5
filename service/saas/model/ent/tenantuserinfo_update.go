// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/predicate"
	"phoenix/service/saas/model/ent/tenant"
	"phoenix/service/saas/model/ent/tenantuserinfo"
	"phoenix/service/saas/model/ent/user"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// TenantUserInfoUpdate is the builder for updating TenantUserInfo entities.
type TenantUserInfoUpdate struct {
	config
	hooks    []Hook
	mutation *TenantUserInfoMutation
}

// Where appends a list predicates to the TenantUserInfoUpdate builder.
func (tuiu *TenantUserInfoUpdate) Where(ps ...predicate.TenantUserInfo) *TenantUserInfoUpdate {
	tuiu.mutation.Where(ps...)
	return tuiu
}

// SetUpdatedAt sets the "updated_at" field.
func (tuiu *TenantUserInfoUpdate) SetUpdatedAt(t time.Time) *TenantUserInfoUpdate {
	tuiu.mutation.SetUpdatedAt(t)
	return tuiu
}

// SetSort sets the "sort" field.
func (tuiu *TenantUserInfoUpdate) SetSort(u uint32) *TenantUserInfoUpdate {
	tuiu.mutation.ResetSort()
	tuiu.mutation.SetSort(u)
	return tuiu
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (tuiu *TenantUserInfoUpdate) SetNillableSort(u *uint32) *TenantUserInfoUpdate {
	if u != nil {
		tuiu.SetSort(*u)
	}
	return tuiu
}

// AddSort adds u to the "sort" field.
func (tuiu *TenantUserInfoUpdate) AddSort(u int32) *TenantUserInfoUpdate {
	tuiu.mutation.AddSort(u)
	return tuiu
}

// SetDeletedAt sets the "deleted_at" field.
func (tuiu *TenantUserInfoUpdate) SetDeletedAt(t time.Time) *TenantUserInfoUpdate {
	tuiu.mutation.SetDeletedAt(t)
	return tuiu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (tuiu *TenantUserInfoUpdate) SetNillableDeletedAt(t *time.Time) *TenantUserInfoUpdate {
	if t != nil {
		tuiu.SetDeletedAt(*t)
	}
	return tuiu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (tuiu *TenantUserInfoUpdate) ClearDeletedAt() *TenantUserInfoUpdate {
	tuiu.mutation.ClearDeletedAt()
	return tuiu
}

// SetTenantID sets the "tenant_id" field.
func (tuiu *TenantUserInfoUpdate) SetTenantID(s string) *TenantUserInfoUpdate {
	tuiu.mutation.SetTenantID(s)
	return tuiu
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (tuiu *TenantUserInfoUpdate) SetNillableTenantID(s *string) *TenantUserInfoUpdate {
	if s != nil {
		tuiu.SetTenantID(*s)
	}
	return tuiu
}

// ClearTenantID clears the value of the "tenant_id" field.
func (tuiu *TenantUserInfoUpdate) ClearTenantID() *TenantUserInfoUpdate {
	tuiu.mutation.ClearTenantID()
	return tuiu
}

// SetUserID sets the "user_id" field.
func (tuiu *TenantUserInfoUpdate) SetUserID(s string) *TenantUserInfoUpdate {
	tuiu.mutation.SetUserID(s)
	return tuiu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (tuiu *TenantUserInfoUpdate) SetNillableUserID(s *string) *TenantUserInfoUpdate {
	if s != nil {
		tuiu.SetUserID(*s)
	}
	return tuiu
}

// ClearUserID clears the value of the "user_id" field.
func (tuiu *TenantUserInfoUpdate) ClearUserID() *TenantUserInfoUpdate {
	tuiu.mutation.ClearUserID()
	return tuiu
}

// SetExtra sets the "extra" field.
func (tuiu *TenantUserInfoUpdate) SetExtra(s string) *TenantUserInfoUpdate {
	tuiu.mutation.SetExtra(s)
	return tuiu
}

// SetNillableExtra sets the "extra" field if the given value is not nil.
func (tuiu *TenantUserInfoUpdate) SetNillableExtra(s *string) *TenantUserInfoUpdate {
	if s != nil {
		tuiu.SetExtra(*s)
	}
	return tuiu
}

// ClearExtra clears the value of the "extra" field.
func (tuiu *TenantUserInfoUpdate) ClearExtra() *TenantUserInfoUpdate {
	tuiu.mutation.ClearExtra()
	return tuiu
}

// SetTenantUserInfoTenantID sets the "tenant_user_info_tenant" edge to the Tenant entity by ID.
func (tuiu *TenantUserInfoUpdate) SetTenantUserInfoTenantID(id string) *TenantUserInfoUpdate {
	tuiu.mutation.SetTenantUserInfoTenantID(id)
	return tuiu
}

// SetNillableTenantUserInfoTenantID sets the "tenant_user_info_tenant" edge to the Tenant entity by ID if the given value is not nil.
func (tuiu *TenantUserInfoUpdate) SetNillableTenantUserInfoTenantID(id *string) *TenantUserInfoUpdate {
	if id != nil {
		tuiu = tuiu.SetTenantUserInfoTenantID(*id)
	}
	return tuiu
}

// SetTenantUserInfoTenant sets the "tenant_user_info_tenant" edge to the Tenant entity.
func (tuiu *TenantUserInfoUpdate) SetTenantUserInfoTenant(t *Tenant) *TenantUserInfoUpdate {
	return tuiu.SetTenantUserInfoTenantID(t.ID)
}

// SetTenantUserInfoUserID sets the "tenant_user_info_user" edge to the User entity by ID.
func (tuiu *TenantUserInfoUpdate) SetTenantUserInfoUserID(id string) *TenantUserInfoUpdate {
	tuiu.mutation.SetTenantUserInfoUserID(id)
	return tuiu
}

// SetNillableTenantUserInfoUserID sets the "tenant_user_info_user" edge to the User entity by ID if the given value is not nil.
func (tuiu *TenantUserInfoUpdate) SetNillableTenantUserInfoUserID(id *string) *TenantUserInfoUpdate {
	if id != nil {
		tuiu = tuiu.SetTenantUserInfoUserID(*id)
	}
	return tuiu
}

// SetTenantUserInfoUser sets the "tenant_user_info_user" edge to the User entity.
func (tuiu *TenantUserInfoUpdate) SetTenantUserInfoUser(u *User) *TenantUserInfoUpdate {
	return tuiu.SetTenantUserInfoUserID(u.ID)
}

// Mutation returns the TenantUserInfoMutation object of the builder.
func (tuiu *TenantUserInfoUpdate) Mutation() *TenantUserInfoMutation {
	return tuiu.mutation
}

// ClearTenantUserInfoTenant clears the "tenant_user_info_tenant" edge to the Tenant entity.
func (tuiu *TenantUserInfoUpdate) ClearTenantUserInfoTenant() *TenantUserInfoUpdate {
	tuiu.mutation.ClearTenantUserInfoTenant()
	return tuiu
}

// ClearTenantUserInfoUser clears the "tenant_user_info_user" edge to the User entity.
func (tuiu *TenantUserInfoUpdate) ClearTenantUserInfoUser() *TenantUserInfoUpdate {
	tuiu.mutation.ClearTenantUserInfoUser()
	return tuiu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (tuiu *TenantUserInfoUpdate) Save(ctx context.Context) (int, error) {
	if err := tuiu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, tuiu.sqlSave, tuiu.mutation, tuiu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tuiu *TenantUserInfoUpdate) SaveX(ctx context.Context) int {
	affected, err := tuiu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (tuiu *TenantUserInfoUpdate) Exec(ctx context.Context) error {
	_, err := tuiu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tuiu *TenantUserInfoUpdate) ExecX(ctx context.Context) {
	if err := tuiu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (tuiu *TenantUserInfoUpdate) defaults() error {
	if _, ok := tuiu.mutation.UpdatedAt(); !ok {
		if tenantuserinfo.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized tenantuserinfo.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := tenantuserinfo.UpdateDefaultUpdatedAt()
		tuiu.mutation.SetUpdatedAt(v)
	}
	return nil
}

func (tuiu *TenantUserInfoUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(tenantuserinfo.Table, tenantuserinfo.Columns, sqlgraph.NewFieldSpec(tenantuserinfo.FieldID, field.TypeString))
	if ps := tuiu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := tuiu.mutation.UpdatedAt(); ok {
		_spec.SetField(tenantuserinfo.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := tuiu.mutation.Sort(); ok {
		_spec.SetField(tenantuserinfo.FieldSort, field.TypeUint32, value)
	}
	if value, ok := tuiu.mutation.AddedSort(); ok {
		_spec.AddField(tenantuserinfo.FieldSort, field.TypeUint32, value)
	}
	if value, ok := tuiu.mutation.DeletedAt(); ok {
		_spec.SetField(tenantuserinfo.FieldDeletedAt, field.TypeTime, value)
	}
	if tuiu.mutation.DeletedAtCleared() {
		_spec.ClearField(tenantuserinfo.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := tuiu.mutation.Extra(); ok {
		_spec.SetField(tenantuserinfo.FieldExtra, field.TypeString, value)
	}
	if tuiu.mutation.ExtraCleared() {
		_spec.ClearField(tenantuserinfo.FieldExtra, field.TypeString)
	}
	if tuiu.mutation.TenantUserInfoTenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   tenantuserinfo.TenantUserInfoTenantTable,
			Columns: []string{tenantuserinfo.TenantUserInfoTenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuiu.mutation.TenantUserInfoTenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   tenantuserinfo.TenantUserInfoTenantTable,
			Columns: []string{tenantuserinfo.TenantUserInfoTenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tuiu.mutation.TenantUserInfoUserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   tenantuserinfo.TenantUserInfoUserTable,
			Columns: []string{tenantuserinfo.TenantUserInfoUserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuiu.mutation.TenantUserInfoUserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   tenantuserinfo.TenantUserInfoUserTable,
			Columns: []string{tenantuserinfo.TenantUserInfoUserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, tuiu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{tenantuserinfo.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	tuiu.mutation.done = true
	return n, nil
}

// TenantUserInfoUpdateOne is the builder for updating a single TenantUserInfo entity.
type TenantUserInfoUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *TenantUserInfoMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (tuiuo *TenantUserInfoUpdateOne) SetUpdatedAt(t time.Time) *TenantUserInfoUpdateOne {
	tuiuo.mutation.SetUpdatedAt(t)
	return tuiuo
}

// SetSort sets the "sort" field.
func (tuiuo *TenantUserInfoUpdateOne) SetSort(u uint32) *TenantUserInfoUpdateOne {
	tuiuo.mutation.ResetSort()
	tuiuo.mutation.SetSort(u)
	return tuiuo
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (tuiuo *TenantUserInfoUpdateOne) SetNillableSort(u *uint32) *TenantUserInfoUpdateOne {
	if u != nil {
		tuiuo.SetSort(*u)
	}
	return tuiuo
}

// AddSort adds u to the "sort" field.
func (tuiuo *TenantUserInfoUpdateOne) AddSort(u int32) *TenantUserInfoUpdateOne {
	tuiuo.mutation.AddSort(u)
	return tuiuo
}

// SetDeletedAt sets the "deleted_at" field.
func (tuiuo *TenantUserInfoUpdateOne) SetDeletedAt(t time.Time) *TenantUserInfoUpdateOne {
	tuiuo.mutation.SetDeletedAt(t)
	return tuiuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (tuiuo *TenantUserInfoUpdateOne) SetNillableDeletedAt(t *time.Time) *TenantUserInfoUpdateOne {
	if t != nil {
		tuiuo.SetDeletedAt(*t)
	}
	return tuiuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (tuiuo *TenantUserInfoUpdateOne) ClearDeletedAt() *TenantUserInfoUpdateOne {
	tuiuo.mutation.ClearDeletedAt()
	return tuiuo
}

// SetTenantID sets the "tenant_id" field.
func (tuiuo *TenantUserInfoUpdateOne) SetTenantID(s string) *TenantUserInfoUpdateOne {
	tuiuo.mutation.SetTenantID(s)
	return tuiuo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (tuiuo *TenantUserInfoUpdateOne) SetNillableTenantID(s *string) *TenantUserInfoUpdateOne {
	if s != nil {
		tuiuo.SetTenantID(*s)
	}
	return tuiuo
}

// ClearTenantID clears the value of the "tenant_id" field.
func (tuiuo *TenantUserInfoUpdateOne) ClearTenantID() *TenantUserInfoUpdateOne {
	tuiuo.mutation.ClearTenantID()
	return tuiuo
}

// SetUserID sets the "user_id" field.
func (tuiuo *TenantUserInfoUpdateOne) SetUserID(s string) *TenantUserInfoUpdateOne {
	tuiuo.mutation.SetUserID(s)
	return tuiuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (tuiuo *TenantUserInfoUpdateOne) SetNillableUserID(s *string) *TenantUserInfoUpdateOne {
	if s != nil {
		tuiuo.SetUserID(*s)
	}
	return tuiuo
}

// ClearUserID clears the value of the "user_id" field.
func (tuiuo *TenantUserInfoUpdateOne) ClearUserID() *TenantUserInfoUpdateOne {
	tuiuo.mutation.ClearUserID()
	return tuiuo
}

// SetExtra sets the "extra" field.
func (tuiuo *TenantUserInfoUpdateOne) SetExtra(s string) *TenantUserInfoUpdateOne {
	tuiuo.mutation.SetExtra(s)
	return tuiuo
}

// SetNillableExtra sets the "extra" field if the given value is not nil.
func (tuiuo *TenantUserInfoUpdateOne) SetNillableExtra(s *string) *TenantUserInfoUpdateOne {
	if s != nil {
		tuiuo.SetExtra(*s)
	}
	return tuiuo
}

// ClearExtra clears the value of the "extra" field.
func (tuiuo *TenantUserInfoUpdateOne) ClearExtra() *TenantUserInfoUpdateOne {
	tuiuo.mutation.ClearExtra()
	return tuiuo
}

// SetTenantUserInfoTenantID sets the "tenant_user_info_tenant" edge to the Tenant entity by ID.
func (tuiuo *TenantUserInfoUpdateOne) SetTenantUserInfoTenantID(id string) *TenantUserInfoUpdateOne {
	tuiuo.mutation.SetTenantUserInfoTenantID(id)
	return tuiuo
}

// SetNillableTenantUserInfoTenantID sets the "tenant_user_info_tenant" edge to the Tenant entity by ID if the given value is not nil.
func (tuiuo *TenantUserInfoUpdateOne) SetNillableTenantUserInfoTenantID(id *string) *TenantUserInfoUpdateOne {
	if id != nil {
		tuiuo = tuiuo.SetTenantUserInfoTenantID(*id)
	}
	return tuiuo
}

// SetTenantUserInfoTenant sets the "tenant_user_info_tenant" edge to the Tenant entity.
func (tuiuo *TenantUserInfoUpdateOne) SetTenantUserInfoTenant(t *Tenant) *TenantUserInfoUpdateOne {
	return tuiuo.SetTenantUserInfoTenantID(t.ID)
}

// SetTenantUserInfoUserID sets the "tenant_user_info_user" edge to the User entity by ID.
func (tuiuo *TenantUserInfoUpdateOne) SetTenantUserInfoUserID(id string) *TenantUserInfoUpdateOne {
	tuiuo.mutation.SetTenantUserInfoUserID(id)
	return tuiuo
}

// SetNillableTenantUserInfoUserID sets the "tenant_user_info_user" edge to the User entity by ID if the given value is not nil.
func (tuiuo *TenantUserInfoUpdateOne) SetNillableTenantUserInfoUserID(id *string) *TenantUserInfoUpdateOne {
	if id != nil {
		tuiuo = tuiuo.SetTenantUserInfoUserID(*id)
	}
	return tuiuo
}

// SetTenantUserInfoUser sets the "tenant_user_info_user" edge to the User entity.
func (tuiuo *TenantUserInfoUpdateOne) SetTenantUserInfoUser(u *User) *TenantUserInfoUpdateOne {
	return tuiuo.SetTenantUserInfoUserID(u.ID)
}

// Mutation returns the TenantUserInfoMutation object of the builder.
func (tuiuo *TenantUserInfoUpdateOne) Mutation() *TenantUserInfoMutation {
	return tuiuo.mutation
}

// ClearTenantUserInfoTenant clears the "tenant_user_info_tenant" edge to the Tenant entity.
func (tuiuo *TenantUserInfoUpdateOne) ClearTenantUserInfoTenant() *TenantUserInfoUpdateOne {
	tuiuo.mutation.ClearTenantUserInfoTenant()
	return tuiuo
}

// ClearTenantUserInfoUser clears the "tenant_user_info_user" edge to the User entity.
func (tuiuo *TenantUserInfoUpdateOne) ClearTenantUserInfoUser() *TenantUserInfoUpdateOne {
	tuiuo.mutation.ClearTenantUserInfoUser()
	return tuiuo
}

// Where appends a list predicates to the TenantUserInfoUpdate builder.
func (tuiuo *TenantUserInfoUpdateOne) Where(ps ...predicate.TenantUserInfo) *TenantUserInfoUpdateOne {
	tuiuo.mutation.Where(ps...)
	return tuiuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (tuiuo *TenantUserInfoUpdateOne) Select(field string, fields ...string) *TenantUserInfoUpdateOne {
	tuiuo.fields = append([]string{field}, fields...)
	return tuiuo
}

// Save executes the query and returns the updated TenantUserInfo entity.
func (tuiuo *TenantUserInfoUpdateOne) Save(ctx context.Context) (*TenantUserInfo, error) {
	if err := tuiuo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, tuiuo.sqlSave, tuiuo.mutation, tuiuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (tuiuo *TenantUserInfoUpdateOne) SaveX(ctx context.Context) *TenantUserInfo {
	node, err := tuiuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (tuiuo *TenantUserInfoUpdateOne) Exec(ctx context.Context) error {
	_, err := tuiuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tuiuo *TenantUserInfoUpdateOne) ExecX(ctx context.Context) {
	if err := tuiuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (tuiuo *TenantUserInfoUpdateOne) defaults() error {
	if _, ok := tuiuo.mutation.UpdatedAt(); !ok {
		if tenantuserinfo.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized tenantuserinfo.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := tenantuserinfo.UpdateDefaultUpdatedAt()
		tuiuo.mutation.SetUpdatedAt(v)
	}
	return nil
}

func (tuiuo *TenantUserInfoUpdateOne) sqlSave(ctx context.Context) (_node *TenantUserInfo, err error) {
	_spec := sqlgraph.NewUpdateSpec(tenantuserinfo.Table, tenantuserinfo.Columns, sqlgraph.NewFieldSpec(tenantuserinfo.FieldID, field.TypeString))
	id, ok := tuiuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "TenantUserInfo.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := tuiuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, tenantuserinfo.FieldID)
		for _, f := range fields {
			if !tenantuserinfo.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != tenantuserinfo.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := tuiuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := tuiuo.mutation.UpdatedAt(); ok {
		_spec.SetField(tenantuserinfo.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := tuiuo.mutation.Sort(); ok {
		_spec.SetField(tenantuserinfo.FieldSort, field.TypeUint32, value)
	}
	if value, ok := tuiuo.mutation.AddedSort(); ok {
		_spec.AddField(tenantuserinfo.FieldSort, field.TypeUint32, value)
	}
	if value, ok := tuiuo.mutation.DeletedAt(); ok {
		_spec.SetField(tenantuserinfo.FieldDeletedAt, field.TypeTime, value)
	}
	if tuiuo.mutation.DeletedAtCleared() {
		_spec.ClearField(tenantuserinfo.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := tuiuo.mutation.Extra(); ok {
		_spec.SetField(tenantuserinfo.FieldExtra, field.TypeString, value)
	}
	if tuiuo.mutation.ExtraCleared() {
		_spec.ClearField(tenantuserinfo.FieldExtra, field.TypeString)
	}
	if tuiuo.mutation.TenantUserInfoTenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   tenantuserinfo.TenantUserInfoTenantTable,
			Columns: []string{tenantuserinfo.TenantUserInfoTenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuiuo.mutation.TenantUserInfoTenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   tenantuserinfo.TenantUserInfoTenantTable,
			Columns: []string{tenantuserinfo.TenantUserInfoTenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if tuiuo.mutation.TenantUserInfoUserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   tenantuserinfo.TenantUserInfoUserTable,
			Columns: []string{tenantuserinfo.TenantUserInfoUserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := tuiuo.mutation.TenantUserInfoUserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   tenantuserinfo.TenantUserInfoUserTable,
			Columns: []string{tenantuserinfo.TenantUserInfoUserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &TenantUserInfo{config: tuiuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, tuiuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{tenantuserinfo.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	tuiuo.mutation.done = true
	return _node, nil
}
