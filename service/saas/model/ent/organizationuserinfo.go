// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"phoenix/service/saas/model/ent/organization"
	"phoenix/service/saas/model/ent/organizationuserinfo"
	"phoenix/service/saas/model/ent/user"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// OrganizationUserInfo is the model entity for the OrganizationUserInfo schema.
type OrganizationUserInfo struct {
	config `json:"-"`
	// ID of the ent.
	// Snowflake ID | 全局唯一ID
	ID string `json:"id,omitempty"`
	// Created Time | 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// Updated Time | 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// Sort number | 排序编号
	Sort uint32 `json:"sort,omitempty"`
	// Deleted Time | 删除时间（软删除标识）
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// Organization ID | 组织架构 ID
	OrganizationID string `json:"organization_id,omitempty"`
	// User ID | 用户 ID
	UserID string `json:"user_id,omitempty"`
	// Extra information | 额外信息(json对象格式存储，用于存储的额外展示信息)
	Extra string `json:"extra,omitempty"`
	// Is Leader | 是否领导
	IsLeader bool `json:"is_leader,omitempty"`
	// Is Admin | 是否管理员
	IsAdmin bool `json:"is_admin,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the OrganizationUserInfoQuery when eager-loading is set.
	Edges        OrganizationUserInfoEdges `json:"edges"`
	selectValues sql.SelectValues
}

// OrganizationUserInfoEdges holds the relations/edges for other nodes in the graph.
type OrganizationUserInfoEdges struct {
	// Organization holds the value of the organization edge.
	Organization *Organization `json:"organization,omitempty"`
	// User holds the value of the user edge.
	User *User `json:"user,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// OrganizationOrErr returns the Organization value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e OrganizationUserInfoEdges) OrganizationOrErr() (*Organization, error) {
	if e.Organization != nil {
		return e.Organization, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: organization.Label}
	}
	return nil, &NotLoadedError{edge: "organization"}
}

// UserOrErr returns the User value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e OrganizationUserInfoEdges) UserOrErr() (*User, error) {
	if e.User != nil {
		return e.User, nil
	} else if e.loadedTypes[1] {
		return nil, &NotFoundError{label: user.Label}
	}
	return nil, &NotLoadedError{edge: "user"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*OrganizationUserInfo) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case organizationuserinfo.FieldIsLeader, organizationuserinfo.FieldIsAdmin:
			values[i] = new(sql.NullBool)
		case organizationuserinfo.FieldSort:
			values[i] = new(sql.NullInt64)
		case organizationuserinfo.FieldID, organizationuserinfo.FieldOrganizationID, organizationuserinfo.FieldUserID, organizationuserinfo.FieldExtra:
			values[i] = new(sql.NullString)
		case organizationuserinfo.FieldCreatedAt, organizationuserinfo.FieldUpdatedAt, organizationuserinfo.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the OrganizationUserInfo fields.
func (oui *OrganizationUserInfo) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case organizationuserinfo.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				oui.ID = value.String
			}
		case organizationuserinfo.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				oui.CreatedAt = value.Time
			}
		case organizationuserinfo.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				oui.UpdatedAt = value.Time
			}
		case organizationuserinfo.FieldSort:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field sort", values[i])
			} else if value.Valid {
				oui.Sort = uint32(value.Int64)
			}
		case organizationuserinfo.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				oui.DeletedAt = value.Time
			}
		case organizationuserinfo.FieldOrganizationID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field organization_id", values[i])
			} else if value.Valid {
				oui.OrganizationID = value.String
			}
		case organizationuserinfo.FieldUserID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value.Valid {
				oui.UserID = value.String
			}
		case organizationuserinfo.FieldExtra:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field extra", values[i])
			} else if value.Valid {
				oui.Extra = value.String
			}
		case organizationuserinfo.FieldIsLeader:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_leader", values[i])
			} else if value.Valid {
				oui.IsLeader = value.Bool
			}
		case organizationuserinfo.FieldIsAdmin:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_admin", values[i])
			} else if value.Valid {
				oui.IsAdmin = value.Bool
			}
		default:
			oui.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the OrganizationUserInfo.
// This includes values selected through modifiers, order, etc.
func (oui *OrganizationUserInfo) Value(name string) (ent.Value, error) {
	return oui.selectValues.Get(name)
}

// QueryOrganization queries the "organization" edge of the OrganizationUserInfo entity.
func (oui *OrganizationUserInfo) QueryOrganization() *OrganizationQuery {
	return NewOrganizationUserInfoClient(oui.config).QueryOrganization(oui)
}

// QueryUser queries the "user" edge of the OrganizationUserInfo entity.
func (oui *OrganizationUserInfo) QueryUser() *UserQuery {
	return NewOrganizationUserInfoClient(oui.config).QueryUser(oui)
}

// Update returns a builder for updating this OrganizationUserInfo.
// Note that you need to call OrganizationUserInfo.Unwrap() before calling this method if this OrganizationUserInfo
// was returned from a transaction, and the transaction was committed or rolled back.
func (oui *OrganizationUserInfo) Update() *OrganizationUserInfoUpdateOne {
	return NewOrganizationUserInfoClient(oui.config).UpdateOne(oui)
}

// Unwrap unwraps the OrganizationUserInfo entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (oui *OrganizationUserInfo) Unwrap() *OrganizationUserInfo {
	_tx, ok := oui.config.driver.(*txDriver)
	if !ok {
		panic("ent: OrganizationUserInfo is not a transactional entity")
	}
	oui.config.driver = _tx.drv
	return oui
}

// String implements the fmt.Stringer.
func (oui *OrganizationUserInfo) String() string {
	var builder strings.Builder
	builder.WriteString("OrganizationUserInfo(")
	builder.WriteString(fmt.Sprintf("id=%v, ", oui.ID))
	builder.WriteString("created_at=")
	builder.WriteString(oui.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(oui.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("sort=")
	builder.WriteString(fmt.Sprintf("%v", oui.Sort))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(oui.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("organization_id=")
	builder.WriteString(oui.OrganizationID)
	builder.WriteString(", ")
	builder.WriteString("user_id=")
	builder.WriteString(oui.UserID)
	builder.WriteString(", ")
	builder.WriteString("extra=")
	builder.WriteString(oui.Extra)
	builder.WriteString(", ")
	builder.WriteString("is_leader=")
	builder.WriteString(fmt.Sprintf("%v", oui.IsLeader))
	builder.WriteString(", ")
	builder.WriteString("is_admin=")
	builder.WriteString(fmt.Sprintf("%v", oui.IsAdmin))
	builder.WriteByte(')')
	return builder.String()
}

// OrganizationUserInfos is a parsable slice of OrganizationUserInfo.
type OrganizationUserInfos []*OrganizationUserInfo
