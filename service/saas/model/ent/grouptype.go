// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"phoenix/service/saas/model/ent/grouptype"
	"phoenix/service/saas/model/ent/tenant"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// GroupType is the model entity for the GroupType schema.
type GroupType struct {
	config `json:"-"`
	// ID of the ent.
	// Snowflake ID | 全局唯一ID
	ID string `json:"id,omitempty"`
	// Created Time | 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// Updated Time | 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// status true normal false ban | 状态  正常/禁用
	Status bool `json:"status,omitempty"`
	// Sort number | 排序编号
	Sort uint32 `json:"sort,omitempty"`
	// Tenant ID
	TenantID string `json:"tenant_id,omitempty"`
	// Deleted Time | 删除时间（软删除标识）
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// Position Name | 组分类名称
	Name string `json:"name,omitempty"`
	// The code of group type | 组分类编码
	Code string `json:"code,omitempty"`
	// Remark | 备注
	Remark string `json:"remark,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the GroupTypeQuery when eager-loading is set.
	Edges        GroupTypeEdges `json:"edges"`
	selectValues sql.SelectValues
}

// GroupTypeEdges holds the relations/edges for other nodes in the graph.
type GroupTypeEdges struct {
	// Tenant holds the value of the tenant edge.
	Tenant *Tenant `json:"tenant,omitempty"`
	// Groups holds the value of the groups edge.
	Groups []*Group `json:"groups,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [2]bool
}

// TenantOrErr returns the Tenant value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e GroupTypeEdges) TenantOrErr() (*Tenant, error) {
	if e.Tenant != nil {
		return e.Tenant, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: tenant.Label}
	}
	return nil, &NotLoadedError{edge: "tenant"}
}

// GroupsOrErr returns the Groups value or an error if the edge
// was not loaded in eager-loading.
func (e GroupTypeEdges) GroupsOrErr() ([]*Group, error) {
	if e.loadedTypes[1] {
		return e.Groups, nil
	}
	return nil, &NotLoadedError{edge: "groups"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*GroupType) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case grouptype.FieldStatus:
			values[i] = new(sql.NullBool)
		case grouptype.FieldSort:
			values[i] = new(sql.NullInt64)
		case grouptype.FieldID, grouptype.FieldTenantID, grouptype.FieldName, grouptype.FieldCode, grouptype.FieldRemark:
			values[i] = new(sql.NullString)
		case grouptype.FieldCreatedAt, grouptype.FieldUpdatedAt, grouptype.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the GroupType fields.
func (gt *GroupType) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case grouptype.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				gt.ID = value.String
			}
		case grouptype.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				gt.CreatedAt = value.Time
			}
		case grouptype.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				gt.UpdatedAt = value.Time
			}
		case grouptype.FieldStatus:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				gt.Status = value.Bool
			}
		case grouptype.FieldSort:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field sort", values[i])
			} else if value.Valid {
				gt.Sort = uint32(value.Int64)
			}
		case grouptype.FieldTenantID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				gt.TenantID = value.String
			}
		case grouptype.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				gt.DeletedAt = value.Time
			}
		case grouptype.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				gt.Name = value.String
			}
		case grouptype.FieldCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field code", values[i])
			} else if value.Valid {
				gt.Code = value.String
			}
		case grouptype.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				gt.Remark = value.String
			}
		default:
			gt.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the GroupType.
// This includes values selected through modifiers, order, etc.
func (gt *GroupType) Value(name string) (ent.Value, error) {
	return gt.selectValues.Get(name)
}

// QueryTenant queries the "tenant" edge of the GroupType entity.
func (gt *GroupType) QueryTenant() *TenantQuery {
	return NewGroupTypeClient(gt.config).QueryTenant(gt)
}

// QueryGroups queries the "groups" edge of the GroupType entity.
func (gt *GroupType) QueryGroups() *GroupQuery {
	return NewGroupTypeClient(gt.config).QueryGroups(gt)
}

// Update returns a builder for updating this GroupType.
// Note that you need to call GroupType.Unwrap() before calling this method if this GroupType
// was returned from a transaction, and the transaction was committed or rolled back.
func (gt *GroupType) Update() *GroupTypeUpdateOne {
	return NewGroupTypeClient(gt.config).UpdateOne(gt)
}

// Unwrap unwraps the GroupType entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (gt *GroupType) Unwrap() *GroupType {
	_tx, ok := gt.config.driver.(*txDriver)
	if !ok {
		panic("ent: GroupType is not a transactional entity")
	}
	gt.config.driver = _tx.drv
	return gt
}

// String implements the fmt.Stringer.
func (gt *GroupType) String() string {
	var builder strings.Builder
	builder.WriteString("GroupType(")
	builder.WriteString(fmt.Sprintf("id=%v, ", gt.ID))
	builder.WriteString("created_at=")
	builder.WriteString(gt.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(gt.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", gt.Status))
	builder.WriteString(", ")
	builder.WriteString("sort=")
	builder.WriteString(fmt.Sprintf("%v", gt.Sort))
	builder.WriteString(", ")
	builder.WriteString("tenant_id=")
	builder.WriteString(gt.TenantID)
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(gt.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(gt.Name)
	builder.WriteString(", ")
	builder.WriteString("code=")
	builder.WriteString(gt.Code)
	builder.WriteString(", ")
	builder.WriteString("remark=")
	builder.WriteString(gt.Remark)
	builder.WriteByte(')')
	return builder.String()
}

// GroupTypes is a parsable slice of GroupType.
type GroupTypes []*GroupType
