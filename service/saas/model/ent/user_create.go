// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/enums"
	"phoenix/service/saas/model/ent/file"
	"phoenix/service/saas/model/ent/group"
	"phoenix/service/saas/model/ent/organization"
	"phoenix/service/saas/model/ent/organizationuserinfo"
	"phoenix/service/saas/model/ent/position"
	"phoenix/service/saas/model/ent/role"
	"phoenix/service/saas/model/ent/tenant"
	"phoenix/service/saas/model/ent/tenantuserinfo"
	"phoenix/service/saas/model/ent/user"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// UserCreate is the builder for creating a User entity.
type UserCreate struct {
	config
	mutation *UserMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (uc *UserCreate) SetCreatedAt(t time.Time) *UserCreate {
	uc.mutation.SetCreatedAt(t)
	return uc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (uc *UserCreate) SetNillableCreatedAt(t *time.Time) *UserCreate {
	if t != nil {
		uc.SetCreatedAt(*t)
	}
	return uc
}

// SetUpdatedAt sets the "updated_at" field.
func (uc *UserCreate) SetUpdatedAt(t time.Time) *UserCreate {
	uc.mutation.SetUpdatedAt(t)
	return uc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (uc *UserCreate) SetNillableUpdatedAt(t *time.Time) *UserCreate {
	if t != nil {
		uc.SetUpdatedAt(*t)
	}
	return uc
}

// SetStatus sets the "status" field.
func (uc *UserCreate) SetStatus(b bool) *UserCreate {
	uc.mutation.SetStatus(b)
	return uc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (uc *UserCreate) SetNillableStatus(b *bool) *UserCreate {
	if b != nil {
		uc.SetStatus(*b)
	}
	return uc
}

// SetDeletedAt sets the "deleted_at" field.
func (uc *UserCreate) SetDeletedAt(t time.Time) *UserCreate {
	uc.mutation.SetDeletedAt(t)
	return uc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (uc *UserCreate) SetNillableDeletedAt(t *time.Time) *UserCreate {
	if t != nil {
		uc.SetDeletedAt(*t)
	}
	return uc
}

// SetUsername sets the "username" field.
func (uc *UserCreate) SetUsername(s string) *UserCreate {
	uc.mutation.SetUsername(s)
	return uc
}

// SetPassword sets the "password" field.
func (uc *UserCreate) SetPassword(s string) *UserCreate {
	uc.mutation.SetPassword(s)
	return uc
}

// SetNickname sets the "nickname" field.
func (uc *UserCreate) SetNickname(s string) *UserCreate {
	uc.mutation.SetNickname(s)
	return uc
}

// SetNillableNickname sets the "nickname" field if the given value is not nil.
func (uc *UserCreate) SetNillableNickname(s *string) *UserCreate {
	if s != nil {
		uc.SetNickname(*s)
	}
	return uc
}

// SetMobile sets the "mobile" field.
func (uc *UserCreate) SetMobile(s string) *UserCreate {
	uc.mutation.SetMobile(s)
	return uc
}

// SetNillableMobile sets the "mobile" field if the given value is not nil.
func (uc *UserCreate) SetNillableMobile(s *string) *UserCreate {
	if s != nil {
		uc.SetMobile(*s)
	}
	return uc
}

// SetEmail sets the "email" field.
func (uc *UserCreate) SetEmail(s string) *UserCreate {
	uc.mutation.SetEmail(s)
	return uc
}

// SetNillableEmail sets the "email" field if the given value is not nil.
func (uc *UserCreate) SetNillableEmail(s *string) *UserCreate {
	if s != nil {
		uc.SetEmail(*s)
	}
	return uc
}

// SetGender sets the "gender" field.
func (uc *UserCreate) SetGender(e enums.Gender) *UserCreate {
	uc.mutation.SetGender(e)
	return uc
}

// SetNillableGender sets the "gender" field if the given value is not nil.
func (uc *UserCreate) SetNillableGender(e *enums.Gender) *UserCreate {
	if e != nil {
		uc.SetGender(*e)
	}
	return uc
}

// SetPost sets the "post" field.
func (uc *UserCreate) SetPost(s string) *UserCreate {
	uc.mutation.SetPost(s)
	return uc
}

// SetNillablePost sets the "post" field if the given value is not nil.
func (uc *UserCreate) SetNillablePost(s *string) *UserCreate {
	if s != nil {
		uc.SetPost(*s)
	}
	return uc
}

// SetIsSuperuser sets the "is_superuser" field.
func (uc *UserCreate) SetIsSuperuser(b bool) *UserCreate {
	uc.mutation.SetIsSuperuser(b)
	return uc
}

// SetNillableIsSuperuser sets the "is_superuser" field if the given value is not nil.
func (uc *UserCreate) SetNillableIsSuperuser(b *bool) *UserCreate {
	if b != nil {
		uc.SetIsSuperuser(*b)
	}
	return uc
}

// SetDefaultTenantID sets the "default_tenant_id" field.
func (uc *UserCreate) SetDefaultTenantID(s string) *UserCreate {
	uc.mutation.SetDefaultTenantID(s)
	return uc
}

// SetNillableDefaultTenantID sets the "default_tenant_id" field if the given value is not nil.
func (uc *UserCreate) SetNillableDefaultTenantID(s *string) *UserCreate {
	if s != nil {
		uc.SetDefaultTenantID(*s)
	}
	return uc
}

// SetAvatarID sets the "avatar_id" field.
func (uc *UserCreate) SetAvatarID(s string) *UserCreate {
	uc.mutation.SetAvatarID(s)
	return uc
}

// SetNillableAvatarID sets the "avatar_id" field if the given value is not nil.
func (uc *UserCreate) SetNillableAvatarID(s *string) *UserCreate {
	if s != nil {
		uc.SetAvatarID(*s)
	}
	return uc
}

// SetDeviceNo sets the "device_no" field.
func (uc *UserCreate) SetDeviceNo(s string) *UserCreate {
	uc.mutation.SetDeviceNo(s)
	return uc
}

// SetNillableDeviceNo sets the "device_no" field if the given value is not nil.
func (uc *UserCreate) SetNillableDeviceNo(s *string) *UserCreate {
	if s != nil {
		uc.SetDeviceNo(*s)
	}
	return uc
}

// SetKind sets the "kind" field.
func (uc *UserCreate) SetKind(s string) *UserCreate {
	uc.mutation.SetKind(s)
	return uc
}

// SetNillableKind sets the "kind" field if the given value is not nil.
func (uc *UserCreate) SetNillableKind(s *string) *UserCreate {
	if s != nil {
		uc.SetKind(*s)
	}
	return uc
}

// SetImei sets the "imei" field.
func (uc *UserCreate) SetImei(s string) *UserCreate {
	uc.mutation.SetImei(s)
	return uc
}

// SetID sets the "id" field.
func (uc *UserCreate) SetID(s string) *UserCreate {
	uc.mutation.SetID(s)
	return uc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (uc *UserCreate) SetNillableID(s *string) *UserCreate {
	if s != nil {
		uc.SetID(*s)
	}
	return uc
}

// AddTenantIDs adds the "tenants" edge to the Tenant entity by IDs.
func (uc *UserCreate) AddTenantIDs(ids ...string) *UserCreate {
	uc.mutation.AddTenantIDs(ids...)
	return uc
}

// AddTenants adds the "tenants" edges to the Tenant entity.
func (uc *UserCreate) AddTenants(t ...*Tenant) *UserCreate {
	ids := make([]string, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return uc.AddTenantIDs(ids...)
}

// AddOrganizationIDs adds the "organizations" edge to the Organization entity by IDs.
func (uc *UserCreate) AddOrganizationIDs(ids ...string) *UserCreate {
	uc.mutation.AddOrganizationIDs(ids...)
	return uc
}

// AddOrganizations adds the "organizations" edges to the Organization entity.
func (uc *UserCreate) AddOrganizations(o ...*Organization) *UserCreate {
	ids := make([]string, len(o))
	for i := range o {
		ids[i] = o[i].ID
	}
	return uc.AddOrganizationIDs(ids...)
}

// AddPositionIDs adds the "positions" edge to the Position entity by IDs.
func (uc *UserCreate) AddPositionIDs(ids ...string) *UserCreate {
	uc.mutation.AddPositionIDs(ids...)
	return uc
}

// AddPositions adds the "positions" edges to the Position entity.
func (uc *UserCreate) AddPositions(p ...*Position) *UserCreate {
	ids := make([]string, len(p))
	for i := range p {
		ids[i] = p[i].ID
	}
	return uc.AddPositionIDs(ids...)
}

// AddGroupIDs adds the "groups" edge to the Group entity by IDs.
func (uc *UserCreate) AddGroupIDs(ids ...string) *UserCreate {
	uc.mutation.AddGroupIDs(ids...)
	return uc
}

// AddGroups adds the "groups" edges to the Group entity.
func (uc *UserCreate) AddGroups(g ...*Group) *UserCreate {
	ids := make([]string, len(g))
	for i := range g {
		ids[i] = g[i].ID
	}
	return uc.AddGroupIDs(ids...)
}

// AddRoleIDs adds the "roles" edge to the Role entity by IDs.
func (uc *UserCreate) AddRoleIDs(ids ...string) *UserCreate {
	uc.mutation.AddRoleIDs(ids...)
	return uc
}

// AddRoles adds the "roles" edges to the Role entity.
func (uc *UserCreate) AddRoles(r ...*Role) *UserCreate {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return uc.AddRoleIDs(ids...)
}

// AddOrganizationInfoIDs adds the "organization_infos" edge to the OrganizationUserInfo entity by IDs.
func (uc *UserCreate) AddOrganizationInfoIDs(ids ...string) *UserCreate {
	uc.mutation.AddOrganizationInfoIDs(ids...)
	return uc
}

// AddOrganizationInfos adds the "organization_infos" edges to the OrganizationUserInfo entity.
func (uc *UserCreate) AddOrganizationInfos(o ...*OrganizationUserInfo) *UserCreate {
	ids := make([]string, len(o))
	for i := range o {
		ids[i] = o[i].ID
	}
	return uc.AddOrganizationInfoIDs(ids...)
}

// AddTenantInfoIDs adds the "tenant_infos" edge to the TenantUserInfo entity by IDs.
func (uc *UserCreate) AddTenantInfoIDs(ids ...string) *UserCreate {
	uc.mutation.AddTenantInfoIDs(ids...)
	return uc
}

// AddTenantInfos adds the "tenant_infos" edges to the TenantUserInfo entity.
func (uc *UserCreate) AddTenantInfos(t ...*TenantUserInfo) *UserCreate {
	ids := make([]string, len(t))
	for i := range t {
		ids[i] = t[i].ID
	}
	return uc.AddTenantInfoIDs(ids...)
}

// AddFileIDs adds the "files" edge to the File entity by IDs.
func (uc *UserCreate) AddFileIDs(ids ...string) *UserCreate {
	uc.mutation.AddFileIDs(ids...)
	return uc
}

// AddFiles adds the "files" edges to the File entity.
func (uc *UserCreate) AddFiles(f ...*File) *UserCreate {
	ids := make([]string, len(f))
	for i := range f {
		ids[i] = f[i].ID
	}
	return uc.AddFileIDs(ids...)
}

// SetAvatar sets the "avatar" edge to the File entity.
func (uc *UserCreate) SetAvatar(f *File) *UserCreate {
	return uc.SetAvatarID(f.ID)
}

// Mutation returns the UserMutation object of the builder.
func (uc *UserCreate) Mutation() *UserMutation {
	return uc.mutation
}

// Save creates the User in the database.
func (uc *UserCreate) Save(ctx context.Context) (*User, error) {
	if err := uc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, uc.sqlSave, uc.mutation, uc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (uc *UserCreate) SaveX(ctx context.Context) *User {
	v, err := uc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (uc *UserCreate) Exec(ctx context.Context) error {
	_, err := uc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (uc *UserCreate) ExecX(ctx context.Context) {
	if err := uc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (uc *UserCreate) defaults() error {
	if _, ok := uc.mutation.CreatedAt(); !ok {
		if user.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized user.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := user.DefaultCreatedAt()
		uc.mutation.SetCreatedAt(v)
	}
	if _, ok := uc.mutation.UpdatedAt(); !ok {
		if user.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized user.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := user.DefaultUpdatedAt()
		uc.mutation.SetUpdatedAt(v)
	}
	if _, ok := uc.mutation.Status(); !ok {
		v := user.DefaultStatus
		uc.mutation.SetStatus(v)
	}
	if _, ok := uc.mutation.Nickname(); !ok {
		v := user.DefaultNickname
		uc.mutation.SetNickname(v)
	}
	if _, ok := uc.mutation.Gender(); !ok {
		v := user.DefaultGender
		uc.mutation.SetGender(v)
	}
	if _, ok := uc.mutation.Post(); !ok {
		v := user.DefaultPost
		uc.mutation.SetPost(v)
	}
	if _, ok := uc.mutation.IsSuperuser(); !ok {
		v := user.DefaultIsSuperuser
		uc.mutation.SetIsSuperuser(v)
	}
	if _, ok := uc.mutation.AvatarID(); !ok {
		v := user.DefaultAvatarID
		uc.mutation.SetAvatarID(v)
	}
	if _, ok := uc.mutation.DeviceNo(); !ok {
		v := user.DefaultDeviceNo
		uc.mutation.SetDeviceNo(v)
	}
	if _, ok := uc.mutation.Kind(); !ok {
		v := user.DefaultKind
		uc.mutation.SetKind(v)
	}
	if _, ok := uc.mutation.ID(); !ok {
		if user.DefaultID == nil {
			return fmt.Errorf("ent: uninitialized user.DefaultID (forgotten import ent/runtime?)")
		}
		v := user.DefaultID()
		uc.mutation.SetID(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (uc *UserCreate) check() error {
	if _, ok := uc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "User.created_at"`)}
	}
	if _, ok := uc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "User.updated_at"`)}
	}
	if _, ok := uc.mutation.Username(); !ok {
		return &ValidationError{Name: "username", err: errors.New(`ent: missing required field "User.username"`)}
	}
	if _, ok := uc.mutation.Password(); !ok {
		return &ValidationError{Name: "password", err: errors.New(`ent: missing required field "User.password"`)}
	}
	if _, ok := uc.mutation.Gender(); !ok {
		return &ValidationError{Name: "gender", err: errors.New(`ent: missing required field "User.gender"`)}
	}
	if v, ok := uc.mutation.Gender(); ok {
		if err := user.GenderValidator(v); err != nil {
			return &ValidationError{Name: "gender", err: fmt.Errorf(`ent: validator failed for field "User.gender": %w`, err)}
		}
	}
	if _, ok := uc.mutation.IsSuperuser(); !ok {
		return &ValidationError{Name: "is_superuser", err: errors.New(`ent: missing required field "User.is_superuser"`)}
	}
	if _, ok := uc.mutation.Imei(); !ok {
		return &ValidationError{Name: "imei", err: errors.New(`ent: missing required field "User.imei"`)}
	}
	return nil
}

func (uc *UserCreate) sqlSave(ctx context.Context) (*User, error) {
	if err := uc.check(); err != nil {
		return nil, err
	}
	_node, _spec := uc.createSpec()
	if err := sqlgraph.CreateNode(ctx, uc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected User.ID type: %T", _spec.ID.Value)
		}
	}
	uc.mutation.id = &_node.ID
	uc.mutation.done = true
	return _node, nil
}

func (uc *UserCreate) createSpec() (*User, *sqlgraph.CreateSpec) {
	var (
		_node = &User{config: uc.config}
		_spec = sqlgraph.NewCreateSpec(user.Table, sqlgraph.NewFieldSpec(user.FieldID, field.TypeString))
	)
	_spec.OnConflict = uc.conflict
	if id, ok := uc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := uc.mutation.CreatedAt(); ok {
		_spec.SetField(user.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := uc.mutation.UpdatedAt(); ok {
		_spec.SetField(user.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := uc.mutation.Status(); ok {
		_spec.SetField(user.FieldStatus, field.TypeBool, value)
		_node.Status = value
	}
	if value, ok := uc.mutation.DeletedAt(); ok {
		_spec.SetField(user.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := uc.mutation.Username(); ok {
		_spec.SetField(user.FieldUsername, field.TypeString, value)
		_node.Username = value
	}
	if value, ok := uc.mutation.Password(); ok {
		_spec.SetField(user.FieldPassword, field.TypeString, value)
		_node.Password = value
	}
	if value, ok := uc.mutation.Nickname(); ok {
		_spec.SetField(user.FieldNickname, field.TypeString, value)
		_node.Nickname = value
	}
	if value, ok := uc.mutation.Mobile(); ok {
		_spec.SetField(user.FieldMobile, field.TypeString, value)
		_node.Mobile = value
	}
	if value, ok := uc.mutation.Email(); ok {
		_spec.SetField(user.FieldEmail, field.TypeString, value)
		_node.Email = value
	}
	if value, ok := uc.mutation.Gender(); ok {
		_spec.SetField(user.FieldGender, field.TypeEnum, value)
		_node.Gender = value
	}
	if value, ok := uc.mutation.Post(); ok {
		_spec.SetField(user.FieldPost, field.TypeString, value)
		_node.Post = value
	}
	if value, ok := uc.mutation.IsSuperuser(); ok {
		_spec.SetField(user.FieldIsSuperuser, field.TypeBool, value)
		_node.IsSuperuser = value
	}
	if value, ok := uc.mutation.DefaultTenantID(); ok {
		_spec.SetField(user.FieldDefaultTenantID, field.TypeString, value)
		_node.DefaultTenantID = value
	}
	if value, ok := uc.mutation.DeviceNo(); ok {
		_spec.SetField(user.FieldDeviceNo, field.TypeString, value)
		_node.DeviceNo = value
	}
	if value, ok := uc.mutation.Kind(); ok {
		_spec.SetField(user.FieldKind, field.TypeString, value)
		_node.Kind = value
	}
	if value, ok := uc.mutation.Imei(); ok {
		_spec.SetField(user.FieldImei, field.TypeString, value)
		_node.Imei = value
	}
	if nodes := uc.mutation.TenantsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   user.TenantsTable,
			Columns: user.TenantsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := uc.mutation.OrganizationsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   user.OrganizationsTable,
			Columns: user.OrganizationsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := uc.mutation.PositionsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   user.PositionsTable,
			Columns: user.PositionsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(position.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := uc.mutation.GroupsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   user.GroupsTable,
			Columns: user.GroupsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(group.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := uc.mutation.RolesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   user.RolesTable,
			Columns: user.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := uc.mutation.OrganizationInfosIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.OrganizationInfosTable,
			Columns: []string{user.OrganizationInfosColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organizationuserinfo.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := uc.mutation.TenantInfosIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.TenantInfosTable,
			Columns: []string{user.TenantInfosColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenantuserinfo.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := uc.mutation.FilesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   user.FilesTable,
			Columns: []string{user.FilesColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(file.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := uc.mutation.AvatarIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   user.AvatarTable,
			Columns: []string{user.AvatarColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(file.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.AvatarID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.User.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.UserUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (uc *UserCreate) OnConflict(opts ...sql.ConflictOption) *UserUpsertOne {
	uc.conflict = opts
	return &UserUpsertOne{
		create: uc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.User.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (uc *UserCreate) OnConflictColumns(columns ...string) *UserUpsertOne {
	uc.conflict = append(uc.conflict, sql.ConflictColumns(columns...))
	return &UserUpsertOne{
		create: uc,
	}
}

type (
	// UserUpsertOne is the builder for "upsert"-ing
	//  one User node.
	UserUpsertOne struct {
		create *UserCreate
	}

	// UserUpsert is the "OnConflict" setter.
	UserUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *UserUpsert) SetUpdatedAt(v time.Time) *UserUpsert {
	u.Set(user.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *UserUpsert) UpdateUpdatedAt() *UserUpsert {
	u.SetExcluded(user.FieldUpdatedAt)
	return u
}

// SetStatus sets the "status" field.
func (u *UserUpsert) SetStatus(v bool) *UserUpsert {
	u.Set(user.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *UserUpsert) UpdateStatus() *UserUpsert {
	u.SetExcluded(user.FieldStatus)
	return u
}

// ClearStatus clears the value of the "status" field.
func (u *UserUpsert) ClearStatus() *UserUpsert {
	u.SetNull(user.FieldStatus)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *UserUpsert) SetDeletedAt(v time.Time) *UserUpsert {
	u.Set(user.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *UserUpsert) UpdateDeletedAt() *UserUpsert {
	u.SetExcluded(user.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *UserUpsert) ClearDeletedAt() *UserUpsert {
	u.SetNull(user.FieldDeletedAt)
	return u
}

// SetUsername sets the "username" field.
func (u *UserUpsert) SetUsername(v string) *UserUpsert {
	u.Set(user.FieldUsername, v)
	return u
}

// UpdateUsername sets the "username" field to the value that was provided on create.
func (u *UserUpsert) UpdateUsername() *UserUpsert {
	u.SetExcluded(user.FieldUsername)
	return u
}

// SetPassword sets the "password" field.
func (u *UserUpsert) SetPassword(v string) *UserUpsert {
	u.Set(user.FieldPassword, v)
	return u
}

// UpdatePassword sets the "password" field to the value that was provided on create.
func (u *UserUpsert) UpdatePassword() *UserUpsert {
	u.SetExcluded(user.FieldPassword)
	return u
}

// SetNickname sets the "nickname" field.
func (u *UserUpsert) SetNickname(v string) *UserUpsert {
	u.Set(user.FieldNickname, v)
	return u
}

// UpdateNickname sets the "nickname" field to the value that was provided on create.
func (u *UserUpsert) UpdateNickname() *UserUpsert {
	u.SetExcluded(user.FieldNickname)
	return u
}

// ClearNickname clears the value of the "nickname" field.
func (u *UserUpsert) ClearNickname() *UserUpsert {
	u.SetNull(user.FieldNickname)
	return u
}

// SetMobile sets the "mobile" field.
func (u *UserUpsert) SetMobile(v string) *UserUpsert {
	u.Set(user.FieldMobile, v)
	return u
}

// UpdateMobile sets the "mobile" field to the value that was provided on create.
func (u *UserUpsert) UpdateMobile() *UserUpsert {
	u.SetExcluded(user.FieldMobile)
	return u
}

// ClearMobile clears the value of the "mobile" field.
func (u *UserUpsert) ClearMobile() *UserUpsert {
	u.SetNull(user.FieldMobile)
	return u
}

// SetEmail sets the "email" field.
func (u *UserUpsert) SetEmail(v string) *UserUpsert {
	u.Set(user.FieldEmail, v)
	return u
}

// UpdateEmail sets the "email" field to the value that was provided on create.
func (u *UserUpsert) UpdateEmail() *UserUpsert {
	u.SetExcluded(user.FieldEmail)
	return u
}

// ClearEmail clears the value of the "email" field.
func (u *UserUpsert) ClearEmail() *UserUpsert {
	u.SetNull(user.FieldEmail)
	return u
}

// SetGender sets the "gender" field.
func (u *UserUpsert) SetGender(v enums.Gender) *UserUpsert {
	u.Set(user.FieldGender, v)
	return u
}

// UpdateGender sets the "gender" field to the value that was provided on create.
func (u *UserUpsert) UpdateGender() *UserUpsert {
	u.SetExcluded(user.FieldGender)
	return u
}

// SetPost sets the "post" field.
func (u *UserUpsert) SetPost(v string) *UserUpsert {
	u.Set(user.FieldPost, v)
	return u
}

// UpdatePost sets the "post" field to the value that was provided on create.
func (u *UserUpsert) UpdatePost() *UserUpsert {
	u.SetExcluded(user.FieldPost)
	return u
}

// ClearPost clears the value of the "post" field.
func (u *UserUpsert) ClearPost() *UserUpsert {
	u.SetNull(user.FieldPost)
	return u
}

// SetIsSuperuser sets the "is_superuser" field.
func (u *UserUpsert) SetIsSuperuser(v bool) *UserUpsert {
	u.Set(user.FieldIsSuperuser, v)
	return u
}

// UpdateIsSuperuser sets the "is_superuser" field to the value that was provided on create.
func (u *UserUpsert) UpdateIsSuperuser() *UserUpsert {
	u.SetExcluded(user.FieldIsSuperuser)
	return u
}

// SetDefaultTenantID sets the "default_tenant_id" field.
func (u *UserUpsert) SetDefaultTenantID(v string) *UserUpsert {
	u.Set(user.FieldDefaultTenantID, v)
	return u
}

// UpdateDefaultTenantID sets the "default_tenant_id" field to the value that was provided on create.
func (u *UserUpsert) UpdateDefaultTenantID() *UserUpsert {
	u.SetExcluded(user.FieldDefaultTenantID)
	return u
}

// ClearDefaultTenantID clears the value of the "default_tenant_id" field.
func (u *UserUpsert) ClearDefaultTenantID() *UserUpsert {
	u.SetNull(user.FieldDefaultTenantID)
	return u
}

// SetAvatarID sets the "avatar_id" field.
func (u *UserUpsert) SetAvatarID(v string) *UserUpsert {
	u.Set(user.FieldAvatarID, v)
	return u
}

// UpdateAvatarID sets the "avatar_id" field to the value that was provided on create.
func (u *UserUpsert) UpdateAvatarID() *UserUpsert {
	u.SetExcluded(user.FieldAvatarID)
	return u
}

// ClearAvatarID clears the value of the "avatar_id" field.
func (u *UserUpsert) ClearAvatarID() *UserUpsert {
	u.SetNull(user.FieldAvatarID)
	return u
}

// SetDeviceNo sets the "device_no" field.
func (u *UserUpsert) SetDeviceNo(v string) *UserUpsert {
	u.Set(user.FieldDeviceNo, v)
	return u
}

// UpdateDeviceNo sets the "device_no" field to the value that was provided on create.
func (u *UserUpsert) UpdateDeviceNo() *UserUpsert {
	u.SetExcluded(user.FieldDeviceNo)
	return u
}

// ClearDeviceNo clears the value of the "device_no" field.
func (u *UserUpsert) ClearDeviceNo() *UserUpsert {
	u.SetNull(user.FieldDeviceNo)
	return u
}

// SetKind sets the "kind" field.
func (u *UserUpsert) SetKind(v string) *UserUpsert {
	u.Set(user.FieldKind, v)
	return u
}

// UpdateKind sets the "kind" field to the value that was provided on create.
func (u *UserUpsert) UpdateKind() *UserUpsert {
	u.SetExcluded(user.FieldKind)
	return u
}

// ClearKind clears the value of the "kind" field.
func (u *UserUpsert) ClearKind() *UserUpsert {
	u.SetNull(user.FieldKind)
	return u
}

// SetImei sets the "imei" field.
func (u *UserUpsert) SetImei(v string) *UserUpsert {
	u.Set(user.FieldImei, v)
	return u
}

// UpdateImei sets the "imei" field to the value that was provided on create.
func (u *UserUpsert) UpdateImei() *UserUpsert {
	u.SetExcluded(user.FieldImei)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.User.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(user.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *UserUpsertOne) UpdateNewValues() *UserUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(user.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(user.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.User.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *UserUpsertOne) Ignore() *UserUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *UserUpsertOne) DoNothing() *UserUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the UserCreate.OnConflict
// documentation for more info.
func (u *UserUpsertOne) Update(set func(*UserUpsert)) *UserUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&UserUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *UserUpsertOne) SetUpdatedAt(v time.Time) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateUpdatedAt() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetStatus sets the "status" field.
func (u *UserUpsertOne) SetStatus(v bool) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateStatus() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *UserUpsertOne) ClearStatus() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearStatus()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *UserUpsertOne) SetDeletedAt(v time.Time) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateDeletedAt() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *UserUpsertOne) ClearDeletedAt() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearDeletedAt()
	})
}

// SetUsername sets the "username" field.
func (u *UserUpsertOne) SetUsername(v string) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetUsername(v)
	})
}

// UpdateUsername sets the "username" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateUsername() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateUsername()
	})
}

// SetPassword sets the "password" field.
func (u *UserUpsertOne) SetPassword(v string) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetPassword(v)
	})
}

// UpdatePassword sets the "password" field to the value that was provided on create.
func (u *UserUpsertOne) UpdatePassword() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdatePassword()
	})
}

// SetNickname sets the "nickname" field.
func (u *UserUpsertOne) SetNickname(v string) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetNickname(v)
	})
}

// UpdateNickname sets the "nickname" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateNickname() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateNickname()
	})
}

// ClearNickname clears the value of the "nickname" field.
func (u *UserUpsertOne) ClearNickname() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearNickname()
	})
}

// SetMobile sets the "mobile" field.
func (u *UserUpsertOne) SetMobile(v string) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetMobile(v)
	})
}

// UpdateMobile sets the "mobile" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateMobile() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateMobile()
	})
}

// ClearMobile clears the value of the "mobile" field.
func (u *UserUpsertOne) ClearMobile() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearMobile()
	})
}

// SetEmail sets the "email" field.
func (u *UserUpsertOne) SetEmail(v string) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetEmail(v)
	})
}

// UpdateEmail sets the "email" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateEmail() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateEmail()
	})
}

// ClearEmail clears the value of the "email" field.
func (u *UserUpsertOne) ClearEmail() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearEmail()
	})
}

// SetGender sets the "gender" field.
func (u *UserUpsertOne) SetGender(v enums.Gender) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetGender(v)
	})
}

// UpdateGender sets the "gender" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateGender() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateGender()
	})
}

// SetPost sets the "post" field.
func (u *UserUpsertOne) SetPost(v string) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetPost(v)
	})
}

// UpdatePost sets the "post" field to the value that was provided on create.
func (u *UserUpsertOne) UpdatePost() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdatePost()
	})
}

// ClearPost clears the value of the "post" field.
func (u *UserUpsertOne) ClearPost() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearPost()
	})
}

// SetIsSuperuser sets the "is_superuser" field.
func (u *UserUpsertOne) SetIsSuperuser(v bool) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetIsSuperuser(v)
	})
}

// UpdateIsSuperuser sets the "is_superuser" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateIsSuperuser() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateIsSuperuser()
	})
}

// SetDefaultTenantID sets the "default_tenant_id" field.
func (u *UserUpsertOne) SetDefaultTenantID(v string) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetDefaultTenantID(v)
	})
}

// UpdateDefaultTenantID sets the "default_tenant_id" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateDefaultTenantID() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateDefaultTenantID()
	})
}

// ClearDefaultTenantID clears the value of the "default_tenant_id" field.
func (u *UserUpsertOne) ClearDefaultTenantID() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearDefaultTenantID()
	})
}

// SetAvatarID sets the "avatar_id" field.
func (u *UserUpsertOne) SetAvatarID(v string) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetAvatarID(v)
	})
}

// UpdateAvatarID sets the "avatar_id" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateAvatarID() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateAvatarID()
	})
}

// ClearAvatarID clears the value of the "avatar_id" field.
func (u *UserUpsertOne) ClearAvatarID() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearAvatarID()
	})
}

// SetDeviceNo sets the "device_no" field.
func (u *UserUpsertOne) SetDeviceNo(v string) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetDeviceNo(v)
	})
}

// UpdateDeviceNo sets the "device_no" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateDeviceNo() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateDeviceNo()
	})
}

// ClearDeviceNo clears the value of the "device_no" field.
func (u *UserUpsertOne) ClearDeviceNo() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearDeviceNo()
	})
}

// SetKind sets the "kind" field.
func (u *UserUpsertOne) SetKind(v string) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetKind(v)
	})
}

// UpdateKind sets the "kind" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateKind() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateKind()
	})
}

// ClearKind clears the value of the "kind" field.
func (u *UserUpsertOne) ClearKind() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.ClearKind()
	})
}

// SetImei sets the "imei" field.
func (u *UserUpsertOne) SetImei(v string) *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.SetImei(v)
	})
}

// UpdateImei sets the "imei" field to the value that was provided on create.
func (u *UserUpsertOne) UpdateImei() *UserUpsertOne {
	return u.Update(func(s *UserUpsert) {
		s.UpdateImei()
	})
}

// Exec executes the query.
func (u *UserUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for UserCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *UserUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *UserUpsertOne) ID(ctx context.Context) (id string, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: UserUpsertOne.ID is not supported by MySQL driver. Use UserUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *UserUpsertOne) IDX(ctx context.Context) string {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// UserCreateBulk is the builder for creating many User entities in bulk.
type UserCreateBulk struct {
	config
	err      error
	builders []*UserCreate
	conflict []sql.ConflictOption
}

// Save creates the User entities in the database.
func (ucb *UserCreateBulk) Save(ctx context.Context) ([]*User, error) {
	if ucb.err != nil {
		return nil, ucb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(ucb.builders))
	nodes := make([]*User, len(ucb.builders))
	mutators := make([]Mutator, len(ucb.builders))
	for i := range ucb.builders {
		func(i int, root context.Context) {
			builder := ucb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*UserMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, ucb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = ucb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, ucb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, ucb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (ucb *UserCreateBulk) SaveX(ctx context.Context) []*User {
	v, err := ucb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ucb *UserCreateBulk) Exec(ctx context.Context) error {
	_, err := ucb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ucb *UserCreateBulk) ExecX(ctx context.Context) {
	if err := ucb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.User.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.UserUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (ucb *UserCreateBulk) OnConflict(opts ...sql.ConflictOption) *UserUpsertBulk {
	ucb.conflict = opts
	return &UserUpsertBulk{
		create: ucb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.User.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (ucb *UserCreateBulk) OnConflictColumns(columns ...string) *UserUpsertBulk {
	ucb.conflict = append(ucb.conflict, sql.ConflictColumns(columns...))
	return &UserUpsertBulk{
		create: ucb,
	}
}

// UserUpsertBulk is the builder for "upsert"-ing
// a bulk of User nodes.
type UserUpsertBulk struct {
	create *UserCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.User.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(user.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *UserUpsertBulk) UpdateNewValues() *UserUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(user.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(user.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.User.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *UserUpsertBulk) Ignore() *UserUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *UserUpsertBulk) DoNothing() *UserUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the UserCreateBulk.OnConflict
// documentation for more info.
func (u *UserUpsertBulk) Update(set func(*UserUpsert)) *UserUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&UserUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *UserUpsertBulk) SetUpdatedAt(v time.Time) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateUpdatedAt() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetStatus sets the "status" field.
func (u *UserUpsertBulk) SetStatus(v bool) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateStatus() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *UserUpsertBulk) ClearStatus() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearStatus()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *UserUpsertBulk) SetDeletedAt(v time.Time) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateDeletedAt() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *UserUpsertBulk) ClearDeletedAt() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearDeletedAt()
	})
}

// SetUsername sets the "username" field.
func (u *UserUpsertBulk) SetUsername(v string) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetUsername(v)
	})
}

// UpdateUsername sets the "username" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateUsername() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateUsername()
	})
}

// SetPassword sets the "password" field.
func (u *UserUpsertBulk) SetPassword(v string) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetPassword(v)
	})
}

// UpdatePassword sets the "password" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdatePassword() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdatePassword()
	})
}

// SetNickname sets the "nickname" field.
func (u *UserUpsertBulk) SetNickname(v string) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetNickname(v)
	})
}

// UpdateNickname sets the "nickname" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateNickname() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateNickname()
	})
}

// ClearNickname clears the value of the "nickname" field.
func (u *UserUpsertBulk) ClearNickname() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearNickname()
	})
}

// SetMobile sets the "mobile" field.
func (u *UserUpsertBulk) SetMobile(v string) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetMobile(v)
	})
}

// UpdateMobile sets the "mobile" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateMobile() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateMobile()
	})
}

// ClearMobile clears the value of the "mobile" field.
func (u *UserUpsertBulk) ClearMobile() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearMobile()
	})
}

// SetEmail sets the "email" field.
func (u *UserUpsertBulk) SetEmail(v string) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetEmail(v)
	})
}

// UpdateEmail sets the "email" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateEmail() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateEmail()
	})
}

// ClearEmail clears the value of the "email" field.
func (u *UserUpsertBulk) ClearEmail() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearEmail()
	})
}

// SetGender sets the "gender" field.
func (u *UserUpsertBulk) SetGender(v enums.Gender) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetGender(v)
	})
}

// UpdateGender sets the "gender" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateGender() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateGender()
	})
}

// SetPost sets the "post" field.
func (u *UserUpsertBulk) SetPost(v string) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetPost(v)
	})
}

// UpdatePost sets the "post" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdatePost() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdatePost()
	})
}

// ClearPost clears the value of the "post" field.
func (u *UserUpsertBulk) ClearPost() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearPost()
	})
}

// SetIsSuperuser sets the "is_superuser" field.
func (u *UserUpsertBulk) SetIsSuperuser(v bool) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetIsSuperuser(v)
	})
}

// UpdateIsSuperuser sets the "is_superuser" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateIsSuperuser() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateIsSuperuser()
	})
}

// SetDefaultTenantID sets the "default_tenant_id" field.
func (u *UserUpsertBulk) SetDefaultTenantID(v string) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetDefaultTenantID(v)
	})
}

// UpdateDefaultTenantID sets the "default_tenant_id" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateDefaultTenantID() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateDefaultTenantID()
	})
}

// ClearDefaultTenantID clears the value of the "default_tenant_id" field.
func (u *UserUpsertBulk) ClearDefaultTenantID() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearDefaultTenantID()
	})
}

// SetAvatarID sets the "avatar_id" field.
func (u *UserUpsertBulk) SetAvatarID(v string) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetAvatarID(v)
	})
}

// UpdateAvatarID sets the "avatar_id" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateAvatarID() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateAvatarID()
	})
}

// ClearAvatarID clears the value of the "avatar_id" field.
func (u *UserUpsertBulk) ClearAvatarID() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearAvatarID()
	})
}

// SetDeviceNo sets the "device_no" field.
func (u *UserUpsertBulk) SetDeviceNo(v string) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetDeviceNo(v)
	})
}

// UpdateDeviceNo sets the "device_no" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateDeviceNo() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateDeviceNo()
	})
}

// ClearDeviceNo clears the value of the "device_no" field.
func (u *UserUpsertBulk) ClearDeviceNo() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearDeviceNo()
	})
}

// SetKind sets the "kind" field.
func (u *UserUpsertBulk) SetKind(v string) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetKind(v)
	})
}

// UpdateKind sets the "kind" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateKind() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateKind()
	})
}

// ClearKind clears the value of the "kind" field.
func (u *UserUpsertBulk) ClearKind() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.ClearKind()
	})
}

// SetImei sets the "imei" field.
func (u *UserUpsertBulk) SetImei(v string) *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.SetImei(v)
	})
}

// UpdateImei sets the "imei" field to the value that was provided on create.
func (u *UserUpsertBulk) UpdateImei() *UserUpsertBulk {
	return u.Update(func(s *UserUpsert) {
		s.UpdateImei()
	})
}

// Exec executes the query.
func (u *UserUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the UserCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for UserCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *UserUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
