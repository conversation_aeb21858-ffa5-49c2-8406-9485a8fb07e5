// Code generated by ent, DO NOT EDIT.

package organizationuserinfo

import (
	"phoenix/service/saas/model/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldContainsFold(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldEQ(FieldUpdatedAt, v))
}

// Sort applies equality check predicate on the "sort" field. It's identical to SortEQ.
func Sort(v uint32) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldEQ(FieldSort, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldEQ(FieldDeletedAt, v))
}

// OrganizationID applies equality check predicate on the "organization_id" field. It's identical to OrganizationIDEQ.
func OrganizationID(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldEQ(FieldOrganizationID, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldEQ(FieldUserID, v))
}

// Extra applies equality check predicate on the "extra" field. It's identical to ExtraEQ.
func Extra(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldEQ(FieldExtra, v))
}

// IsLeader applies equality check predicate on the "is_leader" field. It's identical to IsLeaderEQ.
func IsLeader(v bool) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldEQ(FieldIsLeader, v))
}

// IsAdmin applies equality check predicate on the "is_admin" field. It's identical to IsAdminEQ.
func IsAdmin(v bool) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldEQ(FieldIsAdmin, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldLTE(FieldUpdatedAt, v))
}

// SortEQ applies the EQ predicate on the "sort" field.
func SortEQ(v uint32) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldEQ(FieldSort, v))
}

// SortNEQ applies the NEQ predicate on the "sort" field.
func SortNEQ(v uint32) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldNEQ(FieldSort, v))
}

// SortIn applies the In predicate on the "sort" field.
func SortIn(vs ...uint32) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldIn(FieldSort, vs...))
}

// SortNotIn applies the NotIn predicate on the "sort" field.
func SortNotIn(vs ...uint32) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldNotIn(FieldSort, vs...))
}

// SortGT applies the GT predicate on the "sort" field.
func SortGT(v uint32) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldGT(FieldSort, v))
}

// SortGTE applies the GTE predicate on the "sort" field.
func SortGTE(v uint32) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldGTE(FieldSort, v))
}

// SortLT applies the LT predicate on the "sort" field.
func SortLT(v uint32) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldLT(FieldSort, v))
}

// SortLTE applies the LTE predicate on the "sort" field.
func SortLTE(v uint32) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldLTE(FieldSort, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldNotNull(FieldDeletedAt))
}

// OrganizationIDEQ applies the EQ predicate on the "organization_id" field.
func OrganizationIDEQ(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldEQ(FieldOrganizationID, v))
}

// OrganizationIDNEQ applies the NEQ predicate on the "organization_id" field.
func OrganizationIDNEQ(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldNEQ(FieldOrganizationID, v))
}

// OrganizationIDIn applies the In predicate on the "organization_id" field.
func OrganizationIDIn(vs ...string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldIn(FieldOrganizationID, vs...))
}

// OrganizationIDNotIn applies the NotIn predicate on the "organization_id" field.
func OrganizationIDNotIn(vs ...string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldNotIn(FieldOrganizationID, vs...))
}

// OrganizationIDGT applies the GT predicate on the "organization_id" field.
func OrganizationIDGT(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldGT(FieldOrganizationID, v))
}

// OrganizationIDGTE applies the GTE predicate on the "organization_id" field.
func OrganizationIDGTE(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldGTE(FieldOrganizationID, v))
}

// OrganizationIDLT applies the LT predicate on the "organization_id" field.
func OrganizationIDLT(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldLT(FieldOrganizationID, v))
}

// OrganizationIDLTE applies the LTE predicate on the "organization_id" field.
func OrganizationIDLTE(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldLTE(FieldOrganizationID, v))
}

// OrganizationIDContains applies the Contains predicate on the "organization_id" field.
func OrganizationIDContains(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldContains(FieldOrganizationID, v))
}

// OrganizationIDHasPrefix applies the HasPrefix predicate on the "organization_id" field.
func OrganizationIDHasPrefix(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldHasPrefix(FieldOrganizationID, v))
}

// OrganizationIDHasSuffix applies the HasSuffix predicate on the "organization_id" field.
func OrganizationIDHasSuffix(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldHasSuffix(FieldOrganizationID, v))
}

// OrganizationIDEqualFold applies the EqualFold predicate on the "organization_id" field.
func OrganizationIDEqualFold(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldEqualFold(FieldOrganizationID, v))
}

// OrganizationIDContainsFold applies the ContainsFold predicate on the "organization_id" field.
func OrganizationIDContainsFold(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldContainsFold(FieldOrganizationID, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldLTE(FieldUserID, v))
}

// UserIDContains applies the Contains predicate on the "user_id" field.
func UserIDContains(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldContains(FieldUserID, v))
}

// UserIDHasPrefix applies the HasPrefix predicate on the "user_id" field.
func UserIDHasPrefix(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldHasPrefix(FieldUserID, v))
}

// UserIDHasSuffix applies the HasSuffix predicate on the "user_id" field.
func UserIDHasSuffix(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldHasSuffix(FieldUserID, v))
}

// UserIDEqualFold applies the EqualFold predicate on the "user_id" field.
func UserIDEqualFold(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldEqualFold(FieldUserID, v))
}

// UserIDContainsFold applies the ContainsFold predicate on the "user_id" field.
func UserIDContainsFold(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldContainsFold(FieldUserID, v))
}

// ExtraEQ applies the EQ predicate on the "extra" field.
func ExtraEQ(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldEQ(FieldExtra, v))
}

// ExtraNEQ applies the NEQ predicate on the "extra" field.
func ExtraNEQ(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldNEQ(FieldExtra, v))
}

// ExtraIn applies the In predicate on the "extra" field.
func ExtraIn(vs ...string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldIn(FieldExtra, vs...))
}

// ExtraNotIn applies the NotIn predicate on the "extra" field.
func ExtraNotIn(vs ...string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldNotIn(FieldExtra, vs...))
}

// ExtraGT applies the GT predicate on the "extra" field.
func ExtraGT(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldGT(FieldExtra, v))
}

// ExtraGTE applies the GTE predicate on the "extra" field.
func ExtraGTE(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldGTE(FieldExtra, v))
}

// ExtraLT applies the LT predicate on the "extra" field.
func ExtraLT(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldLT(FieldExtra, v))
}

// ExtraLTE applies the LTE predicate on the "extra" field.
func ExtraLTE(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldLTE(FieldExtra, v))
}

// ExtraContains applies the Contains predicate on the "extra" field.
func ExtraContains(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldContains(FieldExtra, v))
}

// ExtraHasPrefix applies the HasPrefix predicate on the "extra" field.
func ExtraHasPrefix(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldHasPrefix(FieldExtra, v))
}

// ExtraHasSuffix applies the HasSuffix predicate on the "extra" field.
func ExtraHasSuffix(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldHasSuffix(FieldExtra, v))
}

// ExtraIsNil applies the IsNil predicate on the "extra" field.
func ExtraIsNil() predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldIsNull(FieldExtra))
}

// ExtraNotNil applies the NotNil predicate on the "extra" field.
func ExtraNotNil() predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldNotNull(FieldExtra))
}

// ExtraEqualFold applies the EqualFold predicate on the "extra" field.
func ExtraEqualFold(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldEqualFold(FieldExtra, v))
}

// ExtraContainsFold applies the ContainsFold predicate on the "extra" field.
func ExtraContainsFold(v string) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldContainsFold(FieldExtra, v))
}

// IsLeaderEQ applies the EQ predicate on the "is_leader" field.
func IsLeaderEQ(v bool) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldEQ(FieldIsLeader, v))
}

// IsLeaderNEQ applies the NEQ predicate on the "is_leader" field.
func IsLeaderNEQ(v bool) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldNEQ(FieldIsLeader, v))
}

// IsAdminEQ applies the EQ predicate on the "is_admin" field.
func IsAdminEQ(v bool) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldEQ(FieldIsAdmin, v))
}

// IsAdminNEQ applies the NEQ predicate on the "is_admin" field.
func IsAdminNEQ(v bool) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.FieldNEQ(FieldIsAdmin, v))
}

// HasOrganization applies the HasEdge predicate on the "organization" edge.
func HasOrganization() predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, OrganizationTable, OrganizationColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasOrganizationWith applies the HasEdge predicate on the "organization" edge with a given conditions (other predicates).
func HasOrganizationWith(preds ...predicate.Organization) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(func(s *sql.Selector) {
		step := newOrganizationStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasUser applies the HasEdge predicate on the "user" edge.
func HasUser() predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, UserTable, UserColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasUserWith applies the HasEdge predicate on the "user" edge with a given conditions (other predicates).
func HasUserWith(preds ...predicate.User) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(func(s *sql.Selector) {
		step := newUserStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.OrganizationUserInfo) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.OrganizationUserInfo) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.OrganizationUserInfo) predicate.OrganizationUserInfo {
	return predicate.OrganizationUserInfo(sql.NotPredicates(p))
}
