// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"
	"phoenix/service/saas/model/ent/predicate"
	"phoenix/service/saas/model/ent/tenant"
	"phoenix/service/saas/model/ent/tenantuserinfo"
	"phoenix/service/saas/model/ent/user"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// TenantUserInfoQuery is the builder for querying TenantUserInfo entities.
type TenantUserInfoQuery struct {
	config
	ctx                      *QueryContext
	order                    []tenantuserinfo.OrderOption
	inters                   []Interceptor
	predicates               []predicate.TenantUserInfo
	withTenantUserInfoTenant *TenantQuery
	withTenantUserInfoUser   *UserQuery
	withFKs                  bool
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the TenantUserInfoQuery builder.
func (tuiq *TenantUserInfoQuery) Where(ps ...predicate.TenantUserInfo) *TenantUserInfoQuery {
	tuiq.predicates = append(tuiq.predicates, ps...)
	return tuiq
}

// Limit the number of records to be returned by this query.
func (tuiq *TenantUserInfoQuery) Limit(limit int) *TenantUserInfoQuery {
	tuiq.ctx.Limit = &limit
	return tuiq
}

// Offset to start from.
func (tuiq *TenantUserInfoQuery) Offset(offset int) *TenantUserInfoQuery {
	tuiq.ctx.Offset = &offset
	return tuiq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (tuiq *TenantUserInfoQuery) Unique(unique bool) *TenantUserInfoQuery {
	tuiq.ctx.Unique = &unique
	return tuiq
}

// Order specifies how the records should be ordered.
func (tuiq *TenantUserInfoQuery) Order(o ...tenantuserinfo.OrderOption) *TenantUserInfoQuery {
	tuiq.order = append(tuiq.order, o...)
	return tuiq
}

// QueryTenantUserInfoTenant chains the current query on the "tenant_user_info_tenant" edge.
func (tuiq *TenantUserInfoQuery) QueryTenantUserInfoTenant() *TenantQuery {
	query := (&TenantClient{config: tuiq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := tuiq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := tuiq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(tenantuserinfo.Table, tenantuserinfo.FieldID, selector),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, tenantuserinfo.TenantUserInfoTenantTable, tenantuserinfo.TenantUserInfoTenantColumn),
		)
		fromU = sqlgraph.SetNeighbors(tuiq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryTenantUserInfoUser chains the current query on the "tenant_user_info_user" edge.
func (tuiq *TenantUserInfoQuery) QueryTenantUserInfoUser() *UserQuery {
	query := (&UserClient{config: tuiq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := tuiq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := tuiq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(tenantuserinfo.Table, tenantuserinfo.FieldID, selector),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, tenantuserinfo.TenantUserInfoUserTable, tenantuserinfo.TenantUserInfoUserColumn),
		)
		fromU = sqlgraph.SetNeighbors(tuiq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first TenantUserInfo entity from the query.
// Returns a *NotFoundError when no TenantUserInfo was found.
func (tuiq *TenantUserInfoQuery) First(ctx context.Context) (*TenantUserInfo, error) {
	nodes, err := tuiq.Limit(1).All(setContextOp(ctx, tuiq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{tenantuserinfo.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (tuiq *TenantUserInfoQuery) FirstX(ctx context.Context) *TenantUserInfo {
	node, err := tuiq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first TenantUserInfo ID from the query.
// Returns a *NotFoundError when no TenantUserInfo ID was found.
func (tuiq *TenantUserInfoQuery) FirstID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = tuiq.Limit(1).IDs(setContextOp(ctx, tuiq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{tenantuserinfo.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (tuiq *TenantUserInfoQuery) FirstIDX(ctx context.Context) string {
	id, err := tuiq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single TenantUserInfo entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one TenantUserInfo entity is found.
// Returns a *NotFoundError when no TenantUserInfo entities are found.
func (tuiq *TenantUserInfoQuery) Only(ctx context.Context) (*TenantUserInfo, error) {
	nodes, err := tuiq.Limit(2).All(setContextOp(ctx, tuiq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{tenantuserinfo.Label}
	default:
		return nil, &NotSingularError{tenantuserinfo.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (tuiq *TenantUserInfoQuery) OnlyX(ctx context.Context) *TenantUserInfo {
	node, err := tuiq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only TenantUserInfo ID in the query.
// Returns a *NotSingularError when more than one TenantUserInfo ID is found.
// Returns a *NotFoundError when no entities are found.
func (tuiq *TenantUserInfoQuery) OnlyID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = tuiq.Limit(2).IDs(setContextOp(ctx, tuiq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{tenantuserinfo.Label}
	default:
		err = &NotSingularError{tenantuserinfo.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (tuiq *TenantUserInfoQuery) OnlyIDX(ctx context.Context) string {
	id, err := tuiq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of TenantUserInfos.
func (tuiq *TenantUserInfoQuery) All(ctx context.Context) ([]*TenantUserInfo, error) {
	ctx = setContextOp(ctx, tuiq.ctx, "All")
	if err := tuiq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*TenantUserInfo, *TenantUserInfoQuery]()
	return withInterceptors[[]*TenantUserInfo](ctx, tuiq, qr, tuiq.inters)
}

// AllX is like All, but panics if an error occurs.
func (tuiq *TenantUserInfoQuery) AllX(ctx context.Context) []*TenantUserInfo {
	nodes, err := tuiq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of TenantUserInfo IDs.
func (tuiq *TenantUserInfoQuery) IDs(ctx context.Context) (ids []string, err error) {
	if tuiq.ctx.Unique == nil && tuiq.path != nil {
		tuiq.Unique(true)
	}
	ctx = setContextOp(ctx, tuiq.ctx, "IDs")
	if err = tuiq.Select(tenantuserinfo.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (tuiq *TenantUserInfoQuery) IDsX(ctx context.Context) []string {
	ids, err := tuiq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (tuiq *TenantUserInfoQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, tuiq.ctx, "Count")
	if err := tuiq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, tuiq, querierCount[*TenantUserInfoQuery](), tuiq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (tuiq *TenantUserInfoQuery) CountX(ctx context.Context) int {
	count, err := tuiq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (tuiq *TenantUserInfoQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, tuiq.ctx, "Exist")
	switch _, err := tuiq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (tuiq *TenantUserInfoQuery) ExistX(ctx context.Context) bool {
	exist, err := tuiq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the TenantUserInfoQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (tuiq *TenantUserInfoQuery) Clone() *TenantUserInfoQuery {
	if tuiq == nil {
		return nil
	}
	return &TenantUserInfoQuery{
		config:                   tuiq.config,
		ctx:                      tuiq.ctx.Clone(),
		order:                    append([]tenantuserinfo.OrderOption{}, tuiq.order...),
		inters:                   append([]Interceptor{}, tuiq.inters...),
		predicates:               append([]predicate.TenantUserInfo{}, tuiq.predicates...),
		withTenantUserInfoTenant: tuiq.withTenantUserInfoTenant.Clone(),
		withTenantUserInfoUser:   tuiq.withTenantUserInfoUser.Clone(),
		// clone intermediate query.
		sql:  tuiq.sql.Clone(),
		path: tuiq.path,
	}
}

// WithTenantUserInfoTenant tells the query-builder to eager-load the nodes that are connected to
// the "tenant_user_info_tenant" edge. The optional arguments are used to configure the query builder of the edge.
func (tuiq *TenantUserInfoQuery) WithTenantUserInfoTenant(opts ...func(*TenantQuery)) *TenantUserInfoQuery {
	query := (&TenantClient{config: tuiq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	tuiq.withTenantUserInfoTenant = query
	return tuiq
}

// WithTenantUserInfoUser tells the query-builder to eager-load the nodes that are connected to
// the "tenant_user_info_user" edge. The optional arguments are used to configure the query builder of the edge.
func (tuiq *TenantUserInfoQuery) WithTenantUserInfoUser(opts ...func(*UserQuery)) *TenantUserInfoQuery {
	query := (&UserClient{config: tuiq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	tuiq.withTenantUserInfoUser = query
	return tuiq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.TenantUserInfo.Query().
//		GroupBy(tenantuserinfo.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (tuiq *TenantUserInfoQuery) GroupBy(field string, fields ...string) *TenantUserInfoGroupBy {
	tuiq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &TenantUserInfoGroupBy{build: tuiq}
	grbuild.flds = &tuiq.ctx.Fields
	grbuild.label = tenantuserinfo.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.TenantUserInfo.Query().
//		Select(tenantuserinfo.FieldCreatedAt).
//		Scan(ctx, &v)
func (tuiq *TenantUserInfoQuery) Select(fields ...string) *TenantUserInfoSelect {
	tuiq.ctx.Fields = append(tuiq.ctx.Fields, fields...)
	sbuild := &TenantUserInfoSelect{TenantUserInfoQuery: tuiq}
	sbuild.label = tenantuserinfo.Label
	sbuild.flds, sbuild.scan = &tuiq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a TenantUserInfoSelect configured with the given aggregations.
func (tuiq *TenantUserInfoQuery) Aggregate(fns ...AggregateFunc) *TenantUserInfoSelect {
	return tuiq.Select().Aggregate(fns...)
}

func (tuiq *TenantUserInfoQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range tuiq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, tuiq); err != nil {
				return err
			}
		}
	}
	for _, f := range tuiq.ctx.Fields {
		if !tenantuserinfo.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if tuiq.path != nil {
		prev, err := tuiq.path(ctx)
		if err != nil {
			return err
		}
		tuiq.sql = prev
	}
	return nil
}

func (tuiq *TenantUserInfoQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*TenantUserInfo, error) {
	var (
		nodes       = []*TenantUserInfo{}
		withFKs     = tuiq.withFKs
		_spec       = tuiq.querySpec()
		loadedTypes = [2]bool{
			tuiq.withTenantUserInfoTenant != nil,
			tuiq.withTenantUserInfoUser != nil,
		}
	)
	if withFKs {
		_spec.Node.Columns = append(_spec.Node.Columns, tenantuserinfo.ForeignKeys...)
	}
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*TenantUserInfo).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &TenantUserInfo{config: tuiq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, tuiq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := tuiq.withTenantUserInfoTenant; query != nil {
		if err := tuiq.loadTenantUserInfoTenant(ctx, query, nodes, nil,
			func(n *TenantUserInfo, e *Tenant) { n.Edges.TenantUserInfoTenant = e }); err != nil {
			return nil, err
		}
	}
	if query := tuiq.withTenantUserInfoUser; query != nil {
		if err := tuiq.loadTenantUserInfoUser(ctx, query, nodes, nil,
			func(n *TenantUserInfo, e *User) { n.Edges.TenantUserInfoUser = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (tuiq *TenantUserInfoQuery) loadTenantUserInfoTenant(ctx context.Context, query *TenantQuery, nodes []*TenantUserInfo, init func(*TenantUserInfo), assign func(*TenantUserInfo, *Tenant)) error {
	ids := make([]string, 0, len(nodes))
	nodeids := make(map[string][]*TenantUserInfo)
	for i := range nodes {
		fk := nodes[i].TenantID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(tenant.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "tenant_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (tuiq *TenantUserInfoQuery) loadTenantUserInfoUser(ctx context.Context, query *UserQuery, nodes []*TenantUserInfo, init func(*TenantUserInfo), assign func(*TenantUserInfo, *User)) error {
	ids := make([]string, 0, len(nodes))
	nodeids := make(map[string][]*TenantUserInfo)
	for i := range nodes {
		fk := nodes[i].UserID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(user.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "user_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (tuiq *TenantUserInfoQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := tuiq.querySpec()
	_spec.Node.Columns = tuiq.ctx.Fields
	if len(tuiq.ctx.Fields) > 0 {
		_spec.Unique = tuiq.ctx.Unique != nil && *tuiq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, tuiq.driver, _spec)
}

func (tuiq *TenantUserInfoQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(tenantuserinfo.Table, tenantuserinfo.Columns, sqlgraph.NewFieldSpec(tenantuserinfo.FieldID, field.TypeString))
	_spec.From = tuiq.sql
	if unique := tuiq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if tuiq.path != nil {
		_spec.Unique = true
	}
	if fields := tuiq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, tenantuserinfo.FieldID)
		for i := range fields {
			if fields[i] != tenantuserinfo.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if tuiq.withTenantUserInfoTenant != nil {
			_spec.Node.AddColumnOnce(tenantuserinfo.FieldTenantID)
		}
		if tuiq.withTenantUserInfoUser != nil {
			_spec.Node.AddColumnOnce(tenantuserinfo.FieldUserID)
		}
	}
	if ps := tuiq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := tuiq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := tuiq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := tuiq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (tuiq *TenantUserInfoQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(tuiq.driver.Dialect())
	t1 := builder.Table(tenantuserinfo.Table)
	columns := tuiq.ctx.Fields
	if len(columns) == 0 {
		columns = tenantuserinfo.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if tuiq.sql != nil {
		selector = tuiq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if tuiq.ctx.Unique != nil && *tuiq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range tuiq.predicates {
		p(selector)
	}
	for _, p := range tuiq.order {
		p(selector)
	}
	if offset := tuiq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := tuiq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// TenantUserInfoGroupBy is the group-by builder for TenantUserInfo entities.
type TenantUserInfoGroupBy struct {
	selector
	build *TenantUserInfoQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (tuigb *TenantUserInfoGroupBy) Aggregate(fns ...AggregateFunc) *TenantUserInfoGroupBy {
	tuigb.fns = append(tuigb.fns, fns...)
	return tuigb
}

// Scan applies the selector query and scans the result into the given value.
func (tuigb *TenantUserInfoGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, tuigb.build.ctx, "GroupBy")
	if err := tuigb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*TenantUserInfoQuery, *TenantUserInfoGroupBy](ctx, tuigb.build, tuigb, tuigb.build.inters, v)
}

func (tuigb *TenantUserInfoGroupBy) sqlScan(ctx context.Context, root *TenantUserInfoQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(tuigb.fns))
	for _, fn := range tuigb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*tuigb.flds)+len(tuigb.fns))
		for _, f := range *tuigb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*tuigb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := tuigb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// TenantUserInfoSelect is the builder for selecting fields of TenantUserInfo entities.
type TenantUserInfoSelect struct {
	*TenantUserInfoQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (tuis *TenantUserInfoSelect) Aggregate(fns ...AggregateFunc) *TenantUserInfoSelect {
	tuis.fns = append(tuis.fns, fns...)
	return tuis
}

// Scan applies the selector query and scans the result into the given value.
func (tuis *TenantUserInfoSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, tuis.ctx, "Select")
	if err := tuis.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*TenantUserInfoQuery, *TenantUserInfoSelect](ctx, tuis.TenantUserInfoQuery, tuis, tuis.inters, v)
}

func (tuis *TenantUserInfoSelect) sqlScan(ctx context.Context, root *TenantUserInfoQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(tuis.fns))
	for _, fn := range tuis.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*tuis.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := tuis.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
