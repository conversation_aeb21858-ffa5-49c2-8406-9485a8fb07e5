// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"math"
	"phoenix/service/saas/model/ent/organization"
	"phoenix/service/saas/model/ent/organizationuserinfo"
	"phoenix/service/saas/model/ent/predicate"
	"phoenix/service/saas/model/ent/user"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// OrganizationUserInfoQuery is the builder for querying OrganizationUserInfo entities.
type OrganizationUserInfoQuery struct {
	config
	ctx              *QueryContext
	order            []organizationuserinfo.OrderOption
	inters           []Interceptor
	predicates       []predicate.OrganizationUserInfo
	withOrganization *OrganizationQuery
	withUser         *UserQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the OrganizationUserInfoQuery builder.
func (ouiq *OrganizationUserInfoQuery) Where(ps ...predicate.OrganizationUserInfo) *OrganizationUserInfoQuery {
	ouiq.predicates = append(ouiq.predicates, ps...)
	return ouiq
}

// Limit the number of records to be returned by this query.
func (ouiq *OrganizationUserInfoQuery) Limit(limit int) *OrganizationUserInfoQuery {
	ouiq.ctx.Limit = &limit
	return ouiq
}

// Offset to start from.
func (ouiq *OrganizationUserInfoQuery) Offset(offset int) *OrganizationUserInfoQuery {
	ouiq.ctx.Offset = &offset
	return ouiq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (ouiq *OrganizationUserInfoQuery) Unique(unique bool) *OrganizationUserInfoQuery {
	ouiq.ctx.Unique = &unique
	return ouiq
}

// Order specifies how the records should be ordered.
func (ouiq *OrganizationUserInfoQuery) Order(o ...organizationuserinfo.OrderOption) *OrganizationUserInfoQuery {
	ouiq.order = append(ouiq.order, o...)
	return ouiq
}

// QueryOrganization chains the current query on the "organization" edge.
func (ouiq *OrganizationUserInfoQuery) QueryOrganization() *OrganizationQuery {
	query := (&OrganizationClient{config: ouiq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := ouiq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := ouiq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(organizationuserinfo.Table, organizationuserinfo.FieldID, selector),
			sqlgraph.To(organization.Table, organization.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, organizationuserinfo.OrganizationTable, organizationuserinfo.OrganizationColumn),
		)
		fromU = sqlgraph.SetNeighbors(ouiq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryUser chains the current query on the "user" edge.
func (ouiq *OrganizationUserInfoQuery) QueryUser() *UserQuery {
	query := (&UserClient{config: ouiq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := ouiq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := ouiq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(organizationuserinfo.Table, organizationuserinfo.FieldID, selector),
			sqlgraph.To(user.Table, user.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, organizationuserinfo.UserTable, organizationuserinfo.UserColumn),
		)
		fromU = sqlgraph.SetNeighbors(ouiq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first OrganizationUserInfo entity from the query.
// Returns a *NotFoundError when no OrganizationUserInfo was found.
func (ouiq *OrganizationUserInfoQuery) First(ctx context.Context) (*OrganizationUserInfo, error) {
	nodes, err := ouiq.Limit(1).All(setContextOp(ctx, ouiq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{organizationuserinfo.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (ouiq *OrganizationUserInfoQuery) FirstX(ctx context.Context) *OrganizationUserInfo {
	node, err := ouiq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first OrganizationUserInfo ID from the query.
// Returns a *NotFoundError when no OrganizationUserInfo ID was found.
func (ouiq *OrganizationUserInfoQuery) FirstID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = ouiq.Limit(1).IDs(setContextOp(ctx, ouiq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{organizationuserinfo.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (ouiq *OrganizationUserInfoQuery) FirstIDX(ctx context.Context) string {
	id, err := ouiq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single OrganizationUserInfo entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one OrganizationUserInfo entity is found.
// Returns a *NotFoundError when no OrganizationUserInfo entities are found.
func (ouiq *OrganizationUserInfoQuery) Only(ctx context.Context) (*OrganizationUserInfo, error) {
	nodes, err := ouiq.Limit(2).All(setContextOp(ctx, ouiq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{organizationuserinfo.Label}
	default:
		return nil, &NotSingularError{organizationuserinfo.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (ouiq *OrganizationUserInfoQuery) OnlyX(ctx context.Context) *OrganizationUserInfo {
	node, err := ouiq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only OrganizationUserInfo ID in the query.
// Returns a *NotSingularError when more than one OrganizationUserInfo ID is found.
// Returns a *NotFoundError when no entities are found.
func (ouiq *OrganizationUserInfoQuery) OnlyID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = ouiq.Limit(2).IDs(setContextOp(ctx, ouiq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{organizationuserinfo.Label}
	default:
		err = &NotSingularError{organizationuserinfo.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (ouiq *OrganizationUserInfoQuery) OnlyIDX(ctx context.Context) string {
	id, err := ouiq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of OrganizationUserInfos.
func (ouiq *OrganizationUserInfoQuery) All(ctx context.Context) ([]*OrganizationUserInfo, error) {
	ctx = setContextOp(ctx, ouiq.ctx, "All")
	if err := ouiq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*OrganizationUserInfo, *OrganizationUserInfoQuery]()
	return withInterceptors[[]*OrganizationUserInfo](ctx, ouiq, qr, ouiq.inters)
}

// AllX is like All, but panics if an error occurs.
func (ouiq *OrganizationUserInfoQuery) AllX(ctx context.Context) []*OrganizationUserInfo {
	nodes, err := ouiq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of OrganizationUserInfo IDs.
func (ouiq *OrganizationUserInfoQuery) IDs(ctx context.Context) (ids []string, err error) {
	if ouiq.ctx.Unique == nil && ouiq.path != nil {
		ouiq.Unique(true)
	}
	ctx = setContextOp(ctx, ouiq.ctx, "IDs")
	if err = ouiq.Select(organizationuserinfo.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (ouiq *OrganizationUserInfoQuery) IDsX(ctx context.Context) []string {
	ids, err := ouiq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (ouiq *OrganizationUserInfoQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, ouiq.ctx, "Count")
	if err := ouiq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, ouiq, querierCount[*OrganizationUserInfoQuery](), ouiq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (ouiq *OrganizationUserInfoQuery) CountX(ctx context.Context) int {
	count, err := ouiq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (ouiq *OrganizationUserInfoQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, ouiq.ctx, "Exist")
	switch _, err := ouiq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (ouiq *OrganizationUserInfoQuery) ExistX(ctx context.Context) bool {
	exist, err := ouiq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the OrganizationUserInfoQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (ouiq *OrganizationUserInfoQuery) Clone() *OrganizationUserInfoQuery {
	if ouiq == nil {
		return nil
	}
	return &OrganizationUserInfoQuery{
		config:           ouiq.config,
		ctx:              ouiq.ctx.Clone(),
		order:            append([]organizationuserinfo.OrderOption{}, ouiq.order...),
		inters:           append([]Interceptor{}, ouiq.inters...),
		predicates:       append([]predicate.OrganizationUserInfo{}, ouiq.predicates...),
		withOrganization: ouiq.withOrganization.Clone(),
		withUser:         ouiq.withUser.Clone(),
		// clone intermediate query.
		sql:  ouiq.sql.Clone(),
		path: ouiq.path,
	}
}

// WithOrganization tells the query-builder to eager-load the nodes that are connected to
// the "organization" edge. The optional arguments are used to configure the query builder of the edge.
func (ouiq *OrganizationUserInfoQuery) WithOrganization(opts ...func(*OrganizationQuery)) *OrganizationUserInfoQuery {
	query := (&OrganizationClient{config: ouiq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	ouiq.withOrganization = query
	return ouiq
}

// WithUser tells the query-builder to eager-load the nodes that are connected to
// the "user" edge. The optional arguments are used to configure the query builder of the edge.
func (ouiq *OrganizationUserInfoQuery) WithUser(opts ...func(*UserQuery)) *OrganizationUserInfoQuery {
	query := (&UserClient{config: ouiq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	ouiq.withUser = query
	return ouiq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.OrganizationUserInfo.Query().
//		GroupBy(organizationuserinfo.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (ouiq *OrganizationUserInfoQuery) GroupBy(field string, fields ...string) *OrganizationUserInfoGroupBy {
	ouiq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &OrganizationUserInfoGroupBy{build: ouiq}
	grbuild.flds = &ouiq.ctx.Fields
	grbuild.label = organizationuserinfo.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.OrganizationUserInfo.Query().
//		Select(organizationuserinfo.FieldCreatedAt).
//		Scan(ctx, &v)
func (ouiq *OrganizationUserInfoQuery) Select(fields ...string) *OrganizationUserInfoSelect {
	ouiq.ctx.Fields = append(ouiq.ctx.Fields, fields...)
	sbuild := &OrganizationUserInfoSelect{OrganizationUserInfoQuery: ouiq}
	sbuild.label = organizationuserinfo.Label
	sbuild.flds, sbuild.scan = &ouiq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a OrganizationUserInfoSelect configured with the given aggregations.
func (ouiq *OrganizationUserInfoQuery) Aggregate(fns ...AggregateFunc) *OrganizationUserInfoSelect {
	return ouiq.Select().Aggregate(fns...)
}

func (ouiq *OrganizationUserInfoQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range ouiq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, ouiq); err != nil {
				return err
			}
		}
	}
	for _, f := range ouiq.ctx.Fields {
		if !organizationuserinfo.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if ouiq.path != nil {
		prev, err := ouiq.path(ctx)
		if err != nil {
			return err
		}
		ouiq.sql = prev
	}
	return nil
}

func (ouiq *OrganizationUserInfoQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*OrganizationUserInfo, error) {
	var (
		nodes       = []*OrganizationUserInfo{}
		_spec       = ouiq.querySpec()
		loadedTypes = [2]bool{
			ouiq.withOrganization != nil,
			ouiq.withUser != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*OrganizationUserInfo).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &OrganizationUserInfo{config: ouiq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, ouiq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := ouiq.withOrganization; query != nil {
		if err := ouiq.loadOrganization(ctx, query, nodes, nil,
			func(n *OrganizationUserInfo, e *Organization) { n.Edges.Organization = e }); err != nil {
			return nil, err
		}
	}
	if query := ouiq.withUser; query != nil {
		if err := ouiq.loadUser(ctx, query, nodes, nil,
			func(n *OrganizationUserInfo, e *User) { n.Edges.User = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (ouiq *OrganizationUserInfoQuery) loadOrganization(ctx context.Context, query *OrganizationQuery, nodes []*OrganizationUserInfo, init func(*OrganizationUserInfo), assign func(*OrganizationUserInfo, *Organization)) error {
	ids := make([]string, 0, len(nodes))
	nodeids := make(map[string][]*OrganizationUserInfo)
	for i := range nodes {
		fk := nodes[i].OrganizationID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(organization.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "organization_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (ouiq *OrganizationUserInfoQuery) loadUser(ctx context.Context, query *UserQuery, nodes []*OrganizationUserInfo, init func(*OrganizationUserInfo), assign func(*OrganizationUserInfo, *User)) error {
	ids := make([]string, 0, len(nodes))
	nodeids := make(map[string][]*OrganizationUserInfo)
	for i := range nodes {
		fk := nodes[i].UserID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(user.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "user_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (ouiq *OrganizationUserInfoQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := ouiq.querySpec()
	_spec.Node.Columns = ouiq.ctx.Fields
	if len(ouiq.ctx.Fields) > 0 {
		_spec.Unique = ouiq.ctx.Unique != nil && *ouiq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, ouiq.driver, _spec)
}

func (ouiq *OrganizationUserInfoQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(organizationuserinfo.Table, organizationuserinfo.Columns, sqlgraph.NewFieldSpec(organizationuserinfo.FieldID, field.TypeString))
	_spec.From = ouiq.sql
	if unique := ouiq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if ouiq.path != nil {
		_spec.Unique = true
	}
	if fields := ouiq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, organizationuserinfo.FieldID)
		for i := range fields {
			if fields[i] != organizationuserinfo.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if ouiq.withOrganization != nil {
			_spec.Node.AddColumnOnce(organizationuserinfo.FieldOrganizationID)
		}
		if ouiq.withUser != nil {
			_spec.Node.AddColumnOnce(organizationuserinfo.FieldUserID)
		}
	}
	if ps := ouiq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := ouiq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := ouiq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := ouiq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (ouiq *OrganizationUserInfoQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(ouiq.driver.Dialect())
	t1 := builder.Table(organizationuserinfo.Table)
	columns := ouiq.ctx.Fields
	if len(columns) == 0 {
		columns = organizationuserinfo.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if ouiq.sql != nil {
		selector = ouiq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if ouiq.ctx.Unique != nil && *ouiq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range ouiq.predicates {
		p(selector)
	}
	for _, p := range ouiq.order {
		p(selector)
	}
	if offset := ouiq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := ouiq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// OrganizationUserInfoGroupBy is the group-by builder for OrganizationUserInfo entities.
type OrganizationUserInfoGroupBy struct {
	selector
	build *OrganizationUserInfoQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (ouigb *OrganizationUserInfoGroupBy) Aggregate(fns ...AggregateFunc) *OrganizationUserInfoGroupBy {
	ouigb.fns = append(ouigb.fns, fns...)
	return ouigb
}

// Scan applies the selector query and scans the result into the given value.
func (ouigb *OrganizationUserInfoGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ouigb.build.ctx, "GroupBy")
	if err := ouigb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*OrganizationUserInfoQuery, *OrganizationUserInfoGroupBy](ctx, ouigb.build, ouigb, ouigb.build.inters, v)
}

func (ouigb *OrganizationUserInfoGroupBy) sqlScan(ctx context.Context, root *OrganizationUserInfoQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(ouigb.fns))
	for _, fn := range ouigb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*ouigb.flds)+len(ouigb.fns))
		for _, f := range *ouigb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*ouigb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ouigb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// OrganizationUserInfoSelect is the builder for selecting fields of OrganizationUserInfo entities.
type OrganizationUserInfoSelect struct {
	*OrganizationUserInfoQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (ouis *OrganizationUserInfoSelect) Aggregate(fns ...AggregateFunc) *OrganizationUserInfoSelect {
	ouis.fns = append(ouis.fns, fns...)
	return ouis
}

// Scan applies the selector query and scans the result into the given value.
func (ouis *OrganizationUserInfoSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, ouis.ctx, "Select")
	if err := ouis.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*OrganizationUserInfoQuery, *OrganizationUserInfoSelect](ctx, ouis.OrganizationUserInfoQuery, ouis, ouis.inters, v)
}

func (ouis *OrganizationUserInfoSelect) sqlScan(ctx context.Context, root *OrganizationUserInfoQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(ouis.fns))
	for _, fn := range ouis.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*ouis.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := ouis.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
