// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"phoenix/service/saas/model/ent/token"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// Token is the model entity for the Token schema.
type Token struct {
	config `json:"-"`
	// ID of the ent.
	// Snowflake ID | 全局唯一ID
	ID string `json:"id,omitempty"`
	// Created Time | 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// Updated Time | 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// status true normal false ban | 状态  正常/禁用
	Status bool `json:"status,omitempty"`
	//  User's UID | 用户的UID
	UID string `json:"uid,omitempty"`
	// Token string | Token 字符串
	Token string `json:"token,omitempty"`
	// Log in source such as GitHub | Token 来源 （saas, 第三方如github等）
	Source string `json:"source,omitempty"`
	//  Expire time | 过期时间
	ExpiredAt time.Time `json:"expired_at,omitempty"`
	// Tenant ID | 租户ID
	TenantID string `json:"tenant_id,omitempty"`
	// Device kind | 设备类型
	DeviceKind string `json:"device_kind,omitempty"`
	// IP | IP
	IP           string `json:"ip,omitempty"`
	selectValues sql.SelectValues
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Token) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case token.FieldStatus:
			values[i] = new(sql.NullBool)
		case token.FieldID, token.FieldUID, token.FieldToken, token.FieldSource, token.FieldTenantID, token.FieldDeviceKind, token.FieldIP:
			values[i] = new(sql.NullString)
		case token.FieldCreatedAt, token.FieldUpdatedAt, token.FieldExpiredAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Token fields.
func (t *Token) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case token.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				t.ID = value.String
			}
		case token.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				t.CreatedAt = value.Time
			}
		case token.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				t.UpdatedAt = value.Time
			}
		case token.FieldStatus:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				t.Status = value.Bool
			}
		case token.FieldUID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field uid", values[i])
			} else if value.Valid {
				t.UID = value.String
			}
		case token.FieldToken:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field token", values[i])
			} else if value.Valid {
				t.Token = value.String
			}
		case token.FieldSource:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field source", values[i])
			} else if value.Valid {
				t.Source = value.String
			}
		case token.FieldExpiredAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field expired_at", values[i])
			} else if value.Valid {
				t.ExpiredAt = value.Time
			}
		case token.FieldTenantID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				t.TenantID = value.String
			}
		case token.FieldDeviceKind:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field device_kind", values[i])
			} else if value.Valid {
				t.DeviceKind = value.String
			}
		case token.FieldIP:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field ip", values[i])
			} else if value.Valid {
				t.IP = value.String
			}
		default:
			t.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Token.
// This includes values selected through modifiers, order, etc.
func (t *Token) Value(name string) (ent.Value, error) {
	return t.selectValues.Get(name)
}

// Update returns a builder for updating this Token.
// Note that you need to call Token.Unwrap() before calling this method if this Token
// was returned from a transaction, and the transaction was committed or rolled back.
func (t *Token) Update() *TokenUpdateOne {
	return NewTokenClient(t.config).UpdateOne(t)
}

// Unwrap unwraps the Token entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (t *Token) Unwrap() *Token {
	_tx, ok := t.config.driver.(*txDriver)
	if !ok {
		panic("ent: Token is not a transactional entity")
	}
	t.config.driver = _tx.drv
	return t
}

// String implements the fmt.Stringer.
func (t *Token) String() string {
	var builder strings.Builder
	builder.WriteString("Token(")
	builder.WriteString(fmt.Sprintf("id=%v, ", t.ID))
	builder.WriteString("created_at=")
	builder.WriteString(t.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(t.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", t.Status))
	builder.WriteString(", ")
	builder.WriteString("uid=")
	builder.WriteString(t.UID)
	builder.WriteString(", ")
	builder.WriteString("token=")
	builder.WriteString(t.Token)
	builder.WriteString(", ")
	builder.WriteString("source=")
	builder.WriteString(t.Source)
	builder.WriteString(", ")
	builder.WriteString("expired_at=")
	builder.WriteString(t.ExpiredAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("tenant_id=")
	builder.WriteString(t.TenantID)
	builder.WriteString(", ")
	builder.WriteString("device_kind=")
	builder.WriteString(t.DeviceKind)
	builder.WriteString(", ")
	builder.WriteString("ip=")
	builder.WriteString(t.IP)
	builder.WriteByte(')')
	return builder.String()
}

// Tokens is a parsable slice of Token.
type Tokens []*Token
