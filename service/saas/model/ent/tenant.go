// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"phoenix/service/saas/model/ent/tenant"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	uuid "github.com/gofrs/uuid/v5"
)

// Tenant is the model entity for the Tenant schema.
type Tenant struct {
	config `json:"-"`
	// ID of the ent.
	// Snowflake ID | 全局唯一ID
	ID string `json:"id,omitempty"`
	// Created Time | 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// Updated Time | 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// UUID
	UUID uuid.UUID `json:"uuid,omitempty"`
	// key
	Key string `json:"key,omitempty"`
	// secret | 授权secret
	Secret string `json:"secret,omitempty"`
	// status true normal false ban | 状态  正常/禁用
	Status bool `json:"status,omitempty"`
	// Deleted Time | 删除时间（软删除标识）
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// Tenant's name | 租户名称
	Name string `json:"name,omitempty"`
	// Is super tenant | 是否是超级租户
	IsSuper bool `json:"is_super,omitempty"`
	// Tenant's service start | 服务开始时间例如2023-01-01 00:00:00
	ServiceStartAt time.Time `json:"service_start_at,omitempty"`
	// Tenant's service end| 服务到期时间例如2023-10-10 00:00:00
	ServiceEndAt time.Time `json:"service_end_at,omitempty"`
	// 售后联系人
	AfterSalesContact string `json:"after_sales_contact,omitempty"`
	// 归属地ID
	LocationID string `json:"location_id,omitempty"`
	// 日志保留天数
	LogSaveKeepDays int64 `json:"log_save_keep_days,omitempty"`
	// 最大列席用户数
	MaxAttendanceUserCount int64 `json:"max_attendance_user_count,omitempty"`
	// 最大设备数
	MaxDeviceCount int64 `json:"max_device_count,omitempty"`
	// 最大上传文件大小
	MaxUploadFileSize int64 `json:"max_upload_file_size,omitempty"`
	// 最大用户数
	MaxUserCount int64 `json:"max_user_count,omitempty"`
	// 负责人
	Principal string `json:"principal,omitempty"`
	// 负责人联系方式
	PrincipalContactInformation string `json:"principal_contact_information,omitempty"`
	// 销售联系人
	SaleContact string `json:"sale_contact,omitempty"`
	// 密钥
	SecretKey string `json:"secret_key,omitempty"`
	// AI状态
	AiStatus bool `json:"ai_status,omitempty"`
	// 最大会议议程标题字数
	MaxConferenceAgendaTitle int64 `json:"max_conference_agenda_title,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the TenantQuery when eager-loading is set.
	Edges        TenantEdges `json:"edges"`
	selectValues sql.SelectValues
}

// TenantEdges holds the relations/edges for other nodes in the graph.
type TenantEdges struct {
	// Users holds the value of the users edge.
	Users []*User `json:"users,omitempty"`
	// Menus holds the value of the menus edge.
	Menus []*Menu `json:"menus,omitempty"`
	// Apis holds the value of the apis edge.
	Apis []*API `json:"apis,omitempty"`
	// Buttons holds the value of the buttons edge.
	Buttons []*Button `json:"buttons,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [4]bool
}

// UsersOrErr returns the Users value or an error if the edge
// was not loaded in eager-loading.
func (e TenantEdges) UsersOrErr() ([]*User, error) {
	if e.loadedTypes[0] {
		return e.Users, nil
	}
	return nil, &NotLoadedError{edge: "users"}
}

// MenusOrErr returns the Menus value or an error if the edge
// was not loaded in eager-loading.
func (e TenantEdges) MenusOrErr() ([]*Menu, error) {
	if e.loadedTypes[1] {
		return e.Menus, nil
	}
	return nil, &NotLoadedError{edge: "menus"}
}

// ApisOrErr returns the Apis value or an error if the edge
// was not loaded in eager-loading.
func (e TenantEdges) ApisOrErr() ([]*API, error) {
	if e.loadedTypes[2] {
		return e.Apis, nil
	}
	return nil, &NotLoadedError{edge: "apis"}
}

// ButtonsOrErr returns the Buttons value or an error if the edge
// was not loaded in eager-loading.
func (e TenantEdges) ButtonsOrErr() ([]*Button, error) {
	if e.loadedTypes[3] {
		return e.Buttons, nil
	}
	return nil, &NotLoadedError{edge: "buttons"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Tenant) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case tenant.FieldStatus, tenant.FieldIsSuper, tenant.FieldAiStatus:
			values[i] = new(sql.NullBool)
		case tenant.FieldLogSaveKeepDays, tenant.FieldMaxAttendanceUserCount, tenant.FieldMaxDeviceCount, tenant.FieldMaxUploadFileSize, tenant.FieldMaxUserCount, tenant.FieldMaxConferenceAgendaTitle:
			values[i] = new(sql.NullInt64)
		case tenant.FieldID, tenant.FieldKey, tenant.FieldSecret, tenant.FieldName, tenant.FieldAfterSalesContact, tenant.FieldLocationID, tenant.FieldPrincipal, tenant.FieldPrincipalContactInformation, tenant.FieldSaleContact, tenant.FieldSecretKey:
			values[i] = new(sql.NullString)
		case tenant.FieldCreatedAt, tenant.FieldUpdatedAt, tenant.FieldDeletedAt, tenant.FieldServiceStartAt, tenant.FieldServiceEndAt:
			values[i] = new(sql.NullTime)
		case tenant.FieldUUID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Tenant fields.
func (t *Tenant) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case tenant.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				t.ID = value.String
			}
		case tenant.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				t.CreatedAt = value.Time
			}
		case tenant.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				t.UpdatedAt = value.Time
			}
		case tenant.FieldUUID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field uuid", values[i])
			} else if value != nil {
				t.UUID = *value
			}
		case tenant.FieldKey:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field key", values[i])
			} else if value.Valid {
				t.Key = value.String
			}
		case tenant.FieldSecret:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field secret", values[i])
			} else if value.Valid {
				t.Secret = value.String
			}
		case tenant.FieldStatus:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				t.Status = value.Bool
			}
		case tenant.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				t.DeletedAt = value.Time
			}
		case tenant.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				t.Name = value.String
			}
		case tenant.FieldIsSuper:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_super", values[i])
			} else if value.Valid {
				t.IsSuper = value.Bool
			}
		case tenant.FieldServiceStartAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field service_start_at", values[i])
			} else if value.Valid {
				t.ServiceStartAt = value.Time
			}
		case tenant.FieldServiceEndAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field service_end_at", values[i])
			} else if value.Valid {
				t.ServiceEndAt = value.Time
			}
		case tenant.FieldAfterSalesContact:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field after_sales_contact", values[i])
			} else if value.Valid {
				t.AfterSalesContact = value.String
			}
		case tenant.FieldLocationID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field location_id", values[i])
			} else if value.Valid {
				t.LocationID = value.String
			}
		case tenant.FieldLogSaveKeepDays:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field log_save_keep_days", values[i])
			} else if value.Valid {
				t.LogSaveKeepDays = value.Int64
			}
		case tenant.FieldMaxAttendanceUserCount:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field max_attendance_user_count", values[i])
			} else if value.Valid {
				t.MaxAttendanceUserCount = value.Int64
			}
		case tenant.FieldMaxDeviceCount:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field max_device_count", values[i])
			} else if value.Valid {
				t.MaxDeviceCount = value.Int64
			}
		case tenant.FieldMaxUploadFileSize:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field max_upload_file_size", values[i])
			} else if value.Valid {
				t.MaxUploadFileSize = value.Int64
			}
		case tenant.FieldMaxUserCount:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field max_user_count", values[i])
			} else if value.Valid {
				t.MaxUserCount = value.Int64
			}
		case tenant.FieldPrincipal:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field principal", values[i])
			} else if value.Valid {
				t.Principal = value.String
			}
		case tenant.FieldPrincipalContactInformation:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field principal_contact_information", values[i])
			} else if value.Valid {
				t.PrincipalContactInformation = value.String
			}
		case tenant.FieldSaleContact:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field sale_contact", values[i])
			} else if value.Valid {
				t.SaleContact = value.String
			}
		case tenant.FieldSecretKey:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field secret_key", values[i])
			} else if value.Valid {
				t.SecretKey = value.String
			}
		case tenant.FieldAiStatus:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field ai_status", values[i])
			} else if value.Valid {
				t.AiStatus = value.Bool
			}
		case tenant.FieldMaxConferenceAgendaTitle:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field max_conference_agenda_title", values[i])
			} else if value.Valid {
				t.MaxConferenceAgendaTitle = value.Int64
			}
		default:
			t.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Tenant.
// This includes values selected through modifiers, order, etc.
func (t *Tenant) Value(name string) (ent.Value, error) {
	return t.selectValues.Get(name)
}

// QueryUsers queries the "users" edge of the Tenant entity.
func (t *Tenant) QueryUsers() *UserQuery {
	return NewTenantClient(t.config).QueryUsers(t)
}

// QueryMenus queries the "menus" edge of the Tenant entity.
func (t *Tenant) QueryMenus() *MenuQuery {
	return NewTenantClient(t.config).QueryMenus(t)
}

// QueryApis queries the "apis" edge of the Tenant entity.
func (t *Tenant) QueryApis() *APIQuery {
	return NewTenantClient(t.config).QueryApis(t)
}

// QueryButtons queries the "buttons" edge of the Tenant entity.
func (t *Tenant) QueryButtons() *ButtonQuery {
	return NewTenantClient(t.config).QueryButtons(t)
}

// Update returns a builder for updating this Tenant.
// Note that you need to call Tenant.Unwrap() before calling this method if this Tenant
// was returned from a transaction, and the transaction was committed or rolled back.
func (t *Tenant) Update() *TenantUpdateOne {
	return NewTenantClient(t.config).UpdateOne(t)
}

// Unwrap unwraps the Tenant entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (t *Tenant) Unwrap() *Tenant {
	_tx, ok := t.config.driver.(*txDriver)
	if !ok {
		panic("ent: Tenant is not a transactional entity")
	}
	t.config.driver = _tx.drv
	return t
}

// String implements the fmt.Stringer.
func (t *Tenant) String() string {
	var builder strings.Builder
	builder.WriteString("Tenant(")
	builder.WriteString(fmt.Sprintf("id=%v, ", t.ID))
	builder.WriteString("created_at=")
	builder.WriteString(t.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(t.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("uuid=")
	builder.WriteString(fmt.Sprintf("%v", t.UUID))
	builder.WriteString(", ")
	builder.WriteString("key=")
	builder.WriteString(t.Key)
	builder.WriteString(", ")
	builder.WriteString("secret=")
	builder.WriteString(t.Secret)
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", t.Status))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(t.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(t.Name)
	builder.WriteString(", ")
	builder.WriteString("is_super=")
	builder.WriteString(fmt.Sprintf("%v", t.IsSuper))
	builder.WriteString(", ")
	builder.WriteString("service_start_at=")
	builder.WriteString(t.ServiceStartAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("service_end_at=")
	builder.WriteString(t.ServiceEndAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("after_sales_contact=")
	builder.WriteString(t.AfterSalesContact)
	builder.WriteString(", ")
	builder.WriteString("location_id=")
	builder.WriteString(t.LocationID)
	builder.WriteString(", ")
	builder.WriteString("log_save_keep_days=")
	builder.WriteString(fmt.Sprintf("%v", t.LogSaveKeepDays))
	builder.WriteString(", ")
	builder.WriteString("max_attendance_user_count=")
	builder.WriteString(fmt.Sprintf("%v", t.MaxAttendanceUserCount))
	builder.WriteString(", ")
	builder.WriteString("max_device_count=")
	builder.WriteString(fmt.Sprintf("%v", t.MaxDeviceCount))
	builder.WriteString(", ")
	builder.WriteString("max_upload_file_size=")
	builder.WriteString(fmt.Sprintf("%v", t.MaxUploadFileSize))
	builder.WriteString(", ")
	builder.WriteString("max_user_count=")
	builder.WriteString(fmt.Sprintf("%v", t.MaxUserCount))
	builder.WriteString(", ")
	builder.WriteString("principal=")
	builder.WriteString(t.Principal)
	builder.WriteString(", ")
	builder.WriteString("principal_contact_information=")
	builder.WriteString(t.PrincipalContactInformation)
	builder.WriteString(", ")
	builder.WriteString("sale_contact=")
	builder.WriteString(t.SaleContact)
	builder.WriteString(", ")
	builder.WriteString("secret_key=")
	builder.WriteString(t.SecretKey)
	builder.WriteString(", ")
	builder.WriteString("ai_status=")
	builder.WriteString(fmt.Sprintf("%v", t.AiStatus))
	builder.WriteString(", ")
	builder.WriteString("max_conference_agenda_title=")
	builder.WriteString(fmt.Sprintf("%v", t.MaxConferenceAgendaTitle))
	builder.WriteByte(')')
	return builder.String()
}

// Tenants is a parsable slice of Tenant.
type Tenants []*Tenant
