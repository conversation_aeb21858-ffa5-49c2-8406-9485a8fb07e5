// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"database/sql/driver"
	"fmt"
	"math"
	"phoenix/service/saas/model/ent/group"
	"phoenix/service/saas/model/ent/grouptype"
	"phoenix/service/saas/model/ent/predicate"
	"phoenix/service/saas/model/ent/tenant"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// GroupTypeQuery is the builder for querying GroupType entities.
type GroupTypeQuery struct {
	config
	ctx        *QueryContext
	order      []grouptype.OrderOption
	inters     []Interceptor
	predicates []predicate.GroupType
	withTenant *TenantQuery
	withGroups *GroupQuery
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the GroupTypeQuery builder.
func (gtq *GroupTypeQuery) Where(ps ...predicate.GroupType) *GroupTypeQuery {
	gtq.predicates = append(gtq.predicates, ps...)
	return gtq
}

// Limit the number of records to be returned by this query.
func (gtq *GroupTypeQuery) Limit(limit int) *GroupTypeQuery {
	gtq.ctx.Limit = &limit
	return gtq
}

// Offset to start from.
func (gtq *GroupTypeQuery) Offset(offset int) *GroupTypeQuery {
	gtq.ctx.Offset = &offset
	return gtq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (gtq *GroupTypeQuery) Unique(unique bool) *GroupTypeQuery {
	gtq.ctx.Unique = &unique
	return gtq
}

// Order specifies how the records should be ordered.
func (gtq *GroupTypeQuery) Order(o ...grouptype.OrderOption) *GroupTypeQuery {
	gtq.order = append(gtq.order, o...)
	return gtq
}

// QueryTenant chains the current query on the "tenant" edge.
func (gtq *GroupTypeQuery) QueryTenant() *TenantQuery {
	query := (&TenantClient{config: gtq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := gtq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := gtq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(grouptype.Table, grouptype.FieldID, selector),
			sqlgraph.To(tenant.Table, tenant.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, grouptype.TenantTable, grouptype.TenantColumn),
		)
		fromU = sqlgraph.SetNeighbors(gtq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryGroups chains the current query on the "groups" edge.
func (gtq *GroupTypeQuery) QueryGroups() *GroupQuery {
	query := (&GroupClient{config: gtq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := gtq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := gtq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(grouptype.Table, grouptype.FieldID, selector),
			sqlgraph.To(group.Table, group.FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, grouptype.GroupsTable, grouptype.GroupsColumn),
		)
		fromU = sqlgraph.SetNeighbors(gtq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first GroupType entity from the query.
// Returns a *NotFoundError when no GroupType was found.
func (gtq *GroupTypeQuery) First(ctx context.Context) (*GroupType, error) {
	nodes, err := gtq.Limit(1).All(setContextOp(ctx, gtq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{grouptype.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (gtq *GroupTypeQuery) FirstX(ctx context.Context) *GroupType {
	node, err := gtq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first GroupType ID from the query.
// Returns a *NotFoundError when no GroupType ID was found.
func (gtq *GroupTypeQuery) FirstID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = gtq.Limit(1).IDs(setContextOp(ctx, gtq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{grouptype.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (gtq *GroupTypeQuery) FirstIDX(ctx context.Context) string {
	id, err := gtq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single GroupType entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one GroupType entity is found.
// Returns a *NotFoundError when no GroupType entities are found.
func (gtq *GroupTypeQuery) Only(ctx context.Context) (*GroupType, error) {
	nodes, err := gtq.Limit(2).All(setContextOp(ctx, gtq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{grouptype.Label}
	default:
		return nil, &NotSingularError{grouptype.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (gtq *GroupTypeQuery) OnlyX(ctx context.Context) *GroupType {
	node, err := gtq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only GroupType ID in the query.
// Returns a *NotSingularError when more than one GroupType ID is found.
// Returns a *NotFoundError when no entities are found.
func (gtq *GroupTypeQuery) OnlyID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = gtq.Limit(2).IDs(setContextOp(ctx, gtq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{grouptype.Label}
	default:
		err = &NotSingularError{grouptype.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (gtq *GroupTypeQuery) OnlyIDX(ctx context.Context) string {
	id, err := gtq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of GroupTypes.
func (gtq *GroupTypeQuery) All(ctx context.Context) ([]*GroupType, error) {
	ctx = setContextOp(ctx, gtq.ctx, "All")
	if err := gtq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*GroupType, *GroupTypeQuery]()
	return withInterceptors[[]*GroupType](ctx, gtq, qr, gtq.inters)
}

// AllX is like All, but panics if an error occurs.
func (gtq *GroupTypeQuery) AllX(ctx context.Context) []*GroupType {
	nodes, err := gtq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of GroupType IDs.
func (gtq *GroupTypeQuery) IDs(ctx context.Context) (ids []string, err error) {
	if gtq.ctx.Unique == nil && gtq.path != nil {
		gtq.Unique(true)
	}
	ctx = setContextOp(ctx, gtq.ctx, "IDs")
	if err = gtq.Select(grouptype.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (gtq *GroupTypeQuery) IDsX(ctx context.Context) []string {
	ids, err := gtq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (gtq *GroupTypeQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, gtq.ctx, "Count")
	if err := gtq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, gtq, querierCount[*GroupTypeQuery](), gtq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (gtq *GroupTypeQuery) CountX(ctx context.Context) int {
	count, err := gtq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (gtq *GroupTypeQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, gtq.ctx, "Exist")
	switch _, err := gtq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (gtq *GroupTypeQuery) ExistX(ctx context.Context) bool {
	exist, err := gtq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the GroupTypeQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (gtq *GroupTypeQuery) Clone() *GroupTypeQuery {
	if gtq == nil {
		return nil
	}
	return &GroupTypeQuery{
		config:     gtq.config,
		ctx:        gtq.ctx.Clone(),
		order:      append([]grouptype.OrderOption{}, gtq.order...),
		inters:     append([]Interceptor{}, gtq.inters...),
		predicates: append([]predicate.GroupType{}, gtq.predicates...),
		withTenant: gtq.withTenant.Clone(),
		withGroups: gtq.withGroups.Clone(),
		// clone intermediate query.
		sql:  gtq.sql.Clone(),
		path: gtq.path,
	}
}

// WithTenant tells the query-builder to eager-load the nodes that are connected to
// the "tenant" edge. The optional arguments are used to configure the query builder of the edge.
func (gtq *GroupTypeQuery) WithTenant(opts ...func(*TenantQuery)) *GroupTypeQuery {
	query := (&TenantClient{config: gtq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	gtq.withTenant = query
	return gtq
}

// WithGroups tells the query-builder to eager-load the nodes that are connected to
// the "groups" edge. The optional arguments are used to configure the query builder of the edge.
func (gtq *GroupTypeQuery) WithGroups(opts ...func(*GroupQuery)) *GroupTypeQuery {
	query := (&GroupClient{config: gtq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	gtq.withGroups = query
	return gtq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.GroupType.Query().
//		GroupBy(grouptype.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (gtq *GroupTypeQuery) GroupBy(field string, fields ...string) *GroupTypeGroupBy {
	gtq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &GroupTypeGroupBy{build: gtq}
	grbuild.flds = &gtq.ctx.Fields
	grbuild.label = grouptype.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.GroupType.Query().
//		Select(grouptype.FieldCreatedAt).
//		Scan(ctx, &v)
func (gtq *GroupTypeQuery) Select(fields ...string) *GroupTypeSelect {
	gtq.ctx.Fields = append(gtq.ctx.Fields, fields...)
	sbuild := &GroupTypeSelect{GroupTypeQuery: gtq}
	sbuild.label = grouptype.Label
	sbuild.flds, sbuild.scan = &gtq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a GroupTypeSelect configured with the given aggregations.
func (gtq *GroupTypeQuery) Aggregate(fns ...AggregateFunc) *GroupTypeSelect {
	return gtq.Select().Aggregate(fns...)
}

func (gtq *GroupTypeQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range gtq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, gtq); err != nil {
				return err
			}
		}
	}
	for _, f := range gtq.ctx.Fields {
		if !grouptype.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if gtq.path != nil {
		prev, err := gtq.path(ctx)
		if err != nil {
			return err
		}
		gtq.sql = prev
	}
	return nil
}

func (gtq *GroupTypeQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*GroupType, error) {
	var (
		nodes       = []*GroupType{}
		_spec       = gtq.querySpec()
		loadedTypes = [2]bool{
			gtq.withTenant != nil,
			gtq.withGroups != nil,
		}
	)
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*GroupType).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &GroupType{config: gtq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, gtq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := gtq.withTenant; query != nil {
		if err := gtq.loadTenant(ctx, query, nodes, nil,
			func(n *GroupType, e *Tenant) { n.Edges.Tenant = e }); err != nil {
			return nil, err
		}
	}
	if query := gtq.withGroups; query != nil {
		if err := gtq.loadGroups(ctx, query, nodes,
			func(n *GroupType) { n.Edges.Groups = []*Group{} },
			func(n *GroupType, e *Group) { n.Edges.Groups = append(n.Edges.Groups, e) }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (gtq *GroupTypeQuery) loadTenant(ctx context.Context, query *TenantQuery, nodes []*GroupType, init func(*GroupType), assign func(*GroupType, *Tenant)) error {
	ids := make([]string, 0, len(nodes))
	nodeids := make(map[string][]*GroupType)
	for i := range nodes {
		fk := nodes[i].TenantID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(tenant.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "tenant_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}
func (gtq *GroupTypeQuery) loadGroups(ctx context.Context, query *GroupQuery, nodes []*GroupType, init func(*GroupType), assign func(*GroupType, *Group)) error {
	fks := make([]driver.Value, 0, len(nodes))
	nodeids := make(map[string]*GroupType)
	for i := range nodes {
		fks = append(fks, nodes[i].ID)
		nodeids[nodes[i].ID] = nodes[i]
		if init != nil {
			init(nodes[i])
		}
	}
	if len(query.ctx.Fields) > 0 {
		query.ctx.AppendFieldOnce(group.FieldGroupTypeID)
	}
	query.Where(predicate.Group(func(s *sql.Selector) {
		s.Where(sql.InValues(s.C(grouptype.GroupsColumn), fks...))
	}))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		fk := n.GroupTypeID
		node, ok := nodeids[fk]
		if !ok {
			return fmt.Errorf(`unexpected referenced foreign-key "group_type_id" returned %v for node %v`, fk, n.ID)
		}
		assign(node, n)
	}
	return nil
}

func (gtq *GroupTypeQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := gtq.querySpec()
	_spec.Node.Columns = gtq.ctx.Fields
	if len(gtq.ctx.Fields) > 0 {
		_spec.Unique = gtq.ctx.Unique != nil && *gtq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, gtq.driver, _spec)
}

func (gtq *GroupTypeQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(grouptype.Table, grouptype.Columns, sqlgraph.NewFieldSpec(grouptype.FieldID, field.TypeString))
	_spec.From = gtq.sql
	if unique := gtq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if gtq.path != nil {
		_spec.Unique = true
	}
	if fields := gtq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, grouptype.FieldID)
		for i := range fields {
			if fields[i] != grouptype.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if gtq.withTenant != nil {
			_spec.Node.AddColumnOnce(grouptype.FieldTenantID)
		}
	}
	if ps := gtq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := gtq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := gtq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := gtq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (gtq *GroupTypeQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(gtq.driver.Dialect())
	t1 := builder.Table(grouptype.Table)
	columns := gtq.ctx.Fields
	if len(columns) == 0 {
		columns = grouptype.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if gtq.sql != nil {
		selector = gtq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if gtq.ctx.Unique != nil && *gtq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range gtq.predicates {
		p(selector)
	}
	for _, p := range gtq.order {
		p(selector)
	}
	if offset := gtq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := gtq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// GroupTypeGroupBy is the group-by builder for GroupType entities.
type GroupTypeGroupBy struct {
	selector
	build *GroupTypeQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (gtgb *GroupTypeGroupBy) Aggregate(fns ...AggregateFunc) *GroupTypeGroupBy {
	gtgb.fns = append(gtgb.fns, fns...)
	return gtgb
}

// Scan applies the selector query and scans the result into the given value.
func (gtgb *GroupTypeGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, gtgb.build.ctx, "GroupBy")
	if err := gtgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*GroupTypeQuery, *GroupTypeGroupBy](ctx, gtgb.build, gtgb, gtgb.build.inters, v)
}

func (gtgb *GroupTypeGroupBy) sqlScan(ctx context.Context, root *GroupTypeQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(gtgb.fns))
	for _, fn := range gtgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*gtgb.flds)+len(gtgb.fns))
		for _, f := range *gtgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*gtgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := gtgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// GroupTypeSelect is the builder for selecting fields of GroupType entities.
type GroupTypeSelect struct {
	*GroupTypeQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (gts *GroupTypeSelect) Aggregate(fns ...AggregateFunc) *GroupTypeSelect {
	gts.fns = append(gts.fns, fns...)
	return gts
}

// Scan applies the selector query and scans the result into the given value.
func (gts *GroupTypeSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, gts.ctx, "Select")
	if err := gts.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*GroupTypeQuery, *GroupTypeSelect](ctx, gts.GroupTypeQuery, gts, gts.inters, v)
}

func (gts *GroupTypeSelect) sqlScan(ctx context.Context, root *GroupTypeQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(gts.fns))
	for _, fn := range gts.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*gts.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := gts.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
