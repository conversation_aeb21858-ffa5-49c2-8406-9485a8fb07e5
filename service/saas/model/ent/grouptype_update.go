// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/group"
	"phoenix/service/saas/model/ent/grouptype"
	"phoenix/service/saas/model/ent/predicate"
	"phoenix/service/saas/model/ent/tenant"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// GroupTypeUpdate is the builder for updating GroupType entities.
type GroupTypeUpdate struct {
	config
	hooks    []Hook
	mutation *GroupTypeMutation
}

// Where appends a list predicates to the GroupTypeUpdate builder.
func (gtu *GroupTypeUpdate) Where(ps ...predicate.GroupType) *GroupTypeUpdate {
	gtu.mutation.Where(ps...)
	return gtu
}

// SetUpdatedAt sets the "updated_at" field.
func (gtu *GroupTypeUpdate) SetUpdatedAt(t time.Time) *GroupTypeUpdate {
	gtu.mutation.SetUpdatedAt(t)
	return gtu
}

// SetStatus sets the "status" field.
func (gtu *GroupTypeUpdate) SetStatus(b bool) *GroupTypeUpdate {
	gtu.mutation.SetStatus(b)
	return gtu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (gtu *GroupTypeUpdate) SetNillableStatus(b *bool) *GroupTypeUpdate {
	if b != nil {
		gtu.SetStatus(*b)
	}
	return gtu
}

// ClearStatus clears the value of the "status" field.
func (gtu *GroupTypeUpdate) ClearStatus() *GroupTypeUpdate {
	gtu.mutation.ClearStatus()
	return gtu
}

// SetSort sets the "sort" field.
func (gtu *GroupTypeUpdate) SetSort(u uint32) *GroupTypeUpdate {
	gtu.mutation.ResetSort()
	gtu.mutation.SetSort(u)
	return gtu
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (gtu *GroupTypeUpdate) SetNillableSort(u *uint32) *GroupTypeUpdate {
	if u != nil {
		gtu.SetSort(*u)
	}
	return gtu
}

// AddSort adds u to the "sort" field.
func (gtu *GroupTypeUpdate) AddSort(u int32) *GroupTypeUpdate {
	gtu.mutation.AddSort(u)
	return gtu
}

// SetTenantID sets the "tenant_id" field.
func (gtu *GroupTypeUpdate) SetTenantID(s string) *GroupTypeUpdate {
	gtu.mutation.SetTenantID(s)
	return gtu
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (gtu *GroupTypeUpdate) SetNillableTenantID(s *string) *GroupTypeUpdate {
	if s != nil {
		gtu.SetTenantID(*s)
	}
	return gtu
}

// SetDeletedAt sets the "deleted_at" field.
func (gtu *GroupTypeUpdate) SetDeletedAt(t time.Time) *GroupTypeUpdate {
	gtu.mutation.SetDeletedAt(t)
	return gtu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (gtu *GroupTypeUpdate) SetNillableDeletedAt(t *time.Time) *GroupTypeUpdate {
	if t != nil {
		gtu.SetDeletedAt(*t)
	}
	return gtu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (gtu *GroupTypeUpdate) ClearDeletedAt() *GroupTypeUpdate {
	gtu.mutation.ClearDeletedAt()
	return gtu
}

// SetName sets the "name" field.
func (gtu *GroupTypeUpdate) SetName(s string) *GroupTypeUpdate {
	gtu.mutation.SetName(s)
	return gtu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (gtu *GroupTypeUpdate) SetNillableName(s *string) *GroupTypeUpdate {
	if s != nil {
		gtu.SetName(*s)
	}
	return gtu
}

// SetCode sets the "code" field.
func (gtu *GroupTypeUpdate) SetCode(s string) *GroupTypeUpdate {
	gtu.mutation.SetCode(s)
	return gtu
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (gtu *GroupTypeUpdate) SetNillableCode(s *string) *GroupTypeUpdate {
	if s != nil {
		gtu.SetCode(*s)
	}
	return gtu
}

// SetRemark sets the "remark" field.
func (gtu *GroupTypeUpdate) SetRemark(s string) *GroupTypeUpdate {
	gtu.mutation.SetRemark(s)
	return gtu
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (gtu *GroupTypeUpdate) SetNillableRemark(s *string) *GroupTypeUpdate {
	if s != nil {
		gtu.SetRemark(*s)
	}
	return gtu
}

// ClearRemark clears the value of the "remark" field.
func (gtu *GroupTypeUpdate) ClearRemark() *GroupTypeUpdate {
	gtu.mutation.ClearRemark()
	return gtu
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (gtu *GroupTypeUpdate) SetTenant(t *Tenant) *GroupTypeUpdate {
	return gtu.SetTenantID(t.ID)
}

// AddGroupIDs adds the "groups" edge to the Group entity by IDs.
func (gtu *GroupTypeUpdate) AddGroupIDs(ids ...string) *GroupTypeUpdate {
	gtu.mutation.AddGroupIDs(ids...)
	return gtu
}

// AddGroups adds the "groups" edges to the Group entity.
func (gtu *GroupTypeUpdate) AddGroups(g ...*Group) *GroupTypeUpdate {
	ids := make([]string, len(g))
	for i := range g {
		ids[i] = g[i].ID
	}
	return gtu.AddGroupIDs(ids...)
}

// Mutation returns the GroupTypeMutation object of the builder.
func (gtu *GroupTypeUpdate) Mutation() *GroupTypeMutation {
	return gtu.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (gtu *GroupTypeUpdate) ClearTenant() *GroupTypeUpdate {
	gtu.mutation.ClearTenant()
	return gtu
}

// ClearGroups clears all "groups" edges to the Group entity.
func (gtu *GroupTypeUpdate) ClearGroups() *GroupTypeUpdate {
	gtu.mutation.ClearGroups()
	return gtu
}

// RemoveGroupIDs removes the "groups" edge to Group entities by IDs.
func (gtu *GroupTypeUpdate) RemoveGroupIDs(ids ...string) *GroupTypeUpdate {
	gtu.mutation.RemoveGroupIDs(ids...)
	return gtu
}

// RemoveGroups removes "groups" edges to Group entities.
func (gtu *GroupTypeUpdate) RemoveGroups(g ...*Group) *GroupTypeUpdate {
	ids := make([]string, len(g))
	for i := range g {
		ids[i] = g[i].ID
	}
	return gtu.RemoveGroupIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (gtu *GroupTypeUpdate) Save(ctx context.Context) (int, error) {
	if err := gtu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, gtu.sqlSave, gtu.mutation, gtu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (gtu *GroupTypeUpdate) SaveX(ctx context.Context) int {
	affected, err := gtu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (gtu *GroupTypeUpdate) Exec(ctx context.Context) error {
	_, err := gtu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (gtu *GroupTypeUpdate) ExecX(ctx context.Context) {
	if err := gtu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (gtu *GroupTypeUpdate) defaults() error {
	if _, ok := gtu.mutation.UpdatedAt(); !ok {
		if grouptype.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized grouptype.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := grouptype.UpdateDefaultUpdatedAt()
		gtu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (gtu *GroupTypeUpdate) check() error {
	if _, ok := gtu.mutation.TenantID(); gtu.mutation.TenantCleared() && !ok {
		return errors.New(`ent: clearing a required unique edge "GroupType.tenant"`)
	}
	return nil
}

func (gtu *GroupTypeUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := gtu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(grouptype.Table, grouptype.Columns, sqlgraph.NewFieldSpec(grouptype.FieldID, field.TypeString))
	if ps := gtu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := gtu.mutation.UpdatedAt(); ok {
		_spec.SetField(grouptype.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := gtu.mutation.Status(); ok {
		_spec.SetField(grouptype.FieldStatus, field.TypeBool, value)
	}
	if gtu.mutation.StatusCleared() {
		_spec.ClearField(grouptype.FieldStatus, field.TypeBool)
	}
	if value, ok := gtu.mutation.Sort(); ok {
		_spec.SetField(grouptype.FieldSort, field.TypeUint32, value)
	}
	if value, ok := gtu.mutation.AddedSort(); ok {
		_spec.AddField(grouptype.FieldSort, field.TypeUint32, value)
	}
	if value, ok := gtu.mutation.DeletedAt(); ok {
		_spec.SetField(grouptype.FieldDeletedAt, field.TypeTime, value)
	}
	if gtu.mutation.DeletedAtCleared() {
		_spec.ClearField(grouptype.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := gtu.mutation.Name(); ok {
		_spec.SetField(grouptype.FieldName, field.TypeString, value)
	}
	if value, ok := gtu.mutation.Code(); ok {
		_spec.SetField(grouptype.FieldCode, field.TypeString, value)
	}
	if value, ok := gtu.mutation.Remark(); ok {
		_spec.SetField(grouptype.FieldRemark, field.TypeString, value)
	}
	if gtu.mutation.RemarkCleared() {
		_spec.ClearField(grouptype.FieldRemark, field.TypeString)
	}
	if gtu.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   grouptype.TenantTable,
			Columns: []string{grouptype.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := gtu.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   grouptype.TenantTable,
			Columns: []string{grouptype.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if gtu.mutation.GroupsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   grouptype.GroupsTable,
			Columns: []string{grouptype.GroupsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(group.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := gtu.mutation.RemovedGroupsIDs(); len(nodes) > 0 && !gtu.mutation.GroupsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   grouptype.GroupsTable,
			Columns: []string{grouptype.GroupsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(group.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := gtu.mutation.GroupsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   grouptype.GroupsTable,
			Columns: []string{grouptype.GroupsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(group.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, gtu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{grouptype.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	gtu.mutation.done = true
	return n, nil
}

// GroupTypeUpdateOne is the builder for updating a single GroupType entity.
type GroupTypeUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *GroupTypeMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (gtuo *GroupTypeUpdateOne) SetUpdatedAt(t time.Time) *GroupTypeUpdateOne {
	gtuo.mutation.SetUpdatedAt(t)
	return gtuo
}

// SetStatus sets the "status" field.
func (gtuo *GroupTypeUpdateOne) SetStatus(b bool) *GroupTypeUpdateOne {
	gtuo.mutation.SetStatus(b)
	return gtuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (gtuo *GroupTypeUpdateOne) SetNillableStatus(b *bool) *GroupTypeUpdateOne {
	if b != nil {
		gtuo.SetStatus(*b)
	}
	return gtuo
}

// ClearStatus clears the value of the "status" field.
func (gtuo *GroupTypeUpdateOne) ClearStatus() *GroupTypeUpdateOne {
	gtuo.mutation.ClearStatus()
	return gtuo
}

// SetSort sets the "sort" field.
func (gtuo *GroupTypeUpdateOne) SetSort(u uint32) *GroupTypeUpdateOne {
	gtuo.mutation.ResetSort()
	gtuo.mutation.SetSort(u)
	return gtuo
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (gtuo *GroupTypeUpdateOne) SetNillableSort(u *uint32) *GroupTypeUpdateOne {
	if u != nil {
		gtuo.SetSort(*u)
	}
	return gtuo
}

// AddSort adds u to the "sort" field.
func (gtuo *GroupTypeUpdateOne) AddSort(u int32) *GroupTypeUpdateOne {
	gtuo.mutation.AddSort(u)
	return gtuo
}

// SetTenantID sets the "tenant_id" field.
func (gtuo *GroupTypeUpdateOne) SetTenantID(s string) *GroupTypeUpdateOne {
	gtuo.mutation.SetTenantID(s)
	return gtuo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (gtuo *GroupTypeUpdateOne) SetNillableTenantID(s *string) *GroupTypeUpdateOne {
	if s != nil {
		gtuo.SetTenantID(*s)
	}
	return gtuo
}

// SetDeletedAt sets the "deleted_at" field.
func (gtuo *GroupTypeUpdateOne) SetDeletedAt(t time.Time) *GroupTypeUpdateOne {
	gtuo.mutation.SetDeletedAt(t)
	return gtuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (gtuo *GroupTypeUpdateOne) SetNillableDeletedAt(t *time.Time) *GroupTypeUpdateOne {
	if t != nil {
		gtuo.SetDeletedAt(*t)
	}
	return gtuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (gtuo *GroupTypeUpdateOne) ClearDeletedAt() *GroupTypeUpdateOne {
	gtuo.mutation.ClearDeletedAt()
	return gtuo
}

// SetName sets the "name" field.
func (gtuo *GroupTypeUpdateOne) SetName(s string) *GroupTypeUpdateOne {
	gtuo.mutation.SetName(s)
	return gtuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (gtuo *GroupTypeUpdateOne) SetNillableName(s *string) *GroupTypeUpdateOne {
	if s != nil {
		gtuo.SetName(*s)
	}
	return gtuo
}

// SetCode sets the "code" field.
func (gtuo *GroupTypeUpdateOne) SetCode(s string) *GroupTypeUpdateOne {
	gtuo.mutation.SetCode(s)
	return gtuo
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (gtuo *GroupTypeUpdateOne) SetNillableCode(s *string) *GroupTypeUpdateOne {
	if s != nil {
		gtuo.SetCode(*s)
	}
	return gtuo
}

// SetRemark sets the "remark" field.
func (gtuo *GroupTypeUpdateOne) SetRemark(s string) *GroupTypeUpdateOne {
	gtuo.mutation.SetRemark(s)
	return gtuo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (gtuo *GroupTypeUpdateOne) SetNillableRemark(s *string) *GroupTypeUpdateOne {
	if s != nil {
		gtuo.SetRemark(*s)
	}
	return gtuo
}

// ClearRemark clears the value of the "remark" field.
func (gtuo *GroupTypeUpdateOne) ClearRemark() *GroupTypeUpdateOne {
	gtuo.mutation.ClearRemark()
	return gtuo
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (gtuo *GroupTypeUpdateOne) SetTenant(t *Tenant) *GroupTypeUpdateOne {
	return gtuo.SetTenantID(t.ID)
}

// AddGroupIDs adds the "groups" edge to the Group entity by IDs.
func (gtuo *GroupTypeUpdateOne) AddGroupIDs(ids ...string) *GroupTypeUpdateOne {
	gtuo.mutation.AddGroupIDs(ids...)
	return gtuo
}

// AddGroups adds the "groups" edges to the Group entity.
func (gtuo *GroupTypeUpdateOne) AddGroups(g ...*Group) *GroupTypeUpdateOne {
	ids := make([]string, len(g))
	for i := range g {
		ids[i] = g[i].ID
	}
	return gtuo.AddGroupIDs(ids...)
}

// Mutation returns the GroupTypeMutation object of the builder.
func (gtuo *GroupTypeUpdateOne) Mutation() *GroupTypeMutation {
	return gtuo.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (gtuo *GroupTypeUpdateOne) ClearTenant() *GroupTypeUpdateOne {
	gtuo.mutation.ClearTenant()
	return gtuo
}

// ClearGroups clears all "groups" edges to the Group entity.
func (gtuo *GroupTypeUpdateOne) ClearGroups() *GroupTypeUpdateOne {
	gtuo.mutation.ClearGroups()
	return gtuo
}

// RemoveGroupIDs removes the "groups" edge to Group entities by IDs.
func (gtuo *GroupTypeUpdateOne) RemoveGroupIDs(ids ...string) *GroupTypeUpdateOne {
	gtuo.mutation.RemoveGroupIDs(ids...)
	return gtuo
}

// RemoveGroups removes "groups" edges to Group entities.
func (gtuo *GroupTypeUpdateOne) RemoveGroups(g ...*Group) *GroupTypeUpdateOne {
	ids := make([]string, len(g))
	for i := range g {
		ids[i] = g[i].ID
	}
	return gtuo.RemoveGroupIDs(ids...)
}

// Where appends a list predicates to the GroupTypeUpdate builder.
func (gtuo *GroupTypeUpdateOne) Where(ps ...predicate.GroupType) *GroupTypeUpdateOne {
	gtuo.mutation.Where(ps...)
	return gtuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (gtuo *GroupTypeUpdateOne) Select(field string, fields ...string) *GroupTypeUpdateOne {
	gtuo.fields = append([]string{field}, fields...)
	return gtuo
}

// Save executes the query and returns the updated GroupType entity.
func (gtuo *GroupTypeUpdateOne) Save(ctx context.Context) (*GroupType, error) {
	if err := gtuo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, gtuo.sqlSave, gtuo.mutation, gtuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (gtuo *GroupTypeUpdateOne) SaveX(ctx context.Context) *GroupType {
	node, err := gtuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (gtuo *GroupTypeUpdateOne) Exec(ctx context.Context) error {
	_, err := gtuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (gtuo *GroupTypeUpdateOne) ExecX(ctx context.Context) {
	if err := gtuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (gtuo *GroupTypeUpdateOne) defaults() error {
	if _, ok := gtuo.mutation.UpdatedAt(); !ok {
		if grouptype.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized grouptype.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := grouptype.UpdateDefaultUpdatedAt()
		gtuo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (gtuo *GroupTypeUpdateOne) check() error {
	if _, ok := gtuo.mutation.TenantID(); gtuo.mutation.TenantCleared() && !ok {
		return errors.New(`ent: clearing a required unique edge "GroupType.tenant"`)
	}
	return nil
}

func (gtuo *GroupTypeUpdateOne) sqlSave(ctx context.Context) (_node *GroupType, err error) {
	if err := gtuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(grouptype.Table, grouptype.Columns, sqlgraph.NewFieldSpec(grouptype.FieldID, field.TypeString))
	id, ok := gtuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "GroupType.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := gtuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, grouptype.FieldID)
		for _, f := range fields {
			if !grouptype.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != grouptype.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := gtuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := gtuo.mutation.UpdatedAt(); ok {
		_spec.SetField(grouptype.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := gtuo.mutation.Status(); ok {
		_spec.SetField(grouptype.FieldStatus, field.TypeBool, value)
	}
	if gtuo.mutation.StatusCleared() {
		_spec.ClearField(grouptype.FieldStatus, field.TypeBool)
	}
	if value, ok := gtuo.mutation.Sort(); ok {
		_spec.SetField(grouptype.FieldSort, field.TypeUint32, value)
	}
	if value, ok := gtuo.mutation.AddedSort(); ok {
		_spec.AddField(grouptype.FieldSort, field.TypeUint32, value)
	}
	if value, ok := gtuo.mutation.DeletedAt(); ok {
		_spec.SetField(grouptype.FieldDeletedAt, field.TypeTime, value)
	}
	if gtuo.mutation.DeletedAtCleared() {
		_spec.ClearField(grouptype.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := gtuo.mutation.Name(); ok {
		_spec.SetField(grouptype.FieldName, field.TypeString, value)
	}
	if value, ok := gtuo.mutation.Code(); ok {
		_spec.SetField(grouptype.FieldCode, field.TypeString, value)
	}
	if value, ok := gtuo.mutation.Remark(); ok {
		_spec.SetField(grouptype.FieldRemark, field.TypeString, value)
	}
	if gtuo.mutation.RemarkCleared() {
		_spec.ClearField(grouptype.FieldRemark, field.TypeString)
	}
	if gtuo.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   grouptype.TenantTable,
			Columns: []string{grouptype.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := gtuo.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   grouptype.TenantTable,
			Columns: []string{grouptype.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if gtuo.mutation.GroupsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   grouptype.GroupsTable,
			Columns: []string{grouptype.GroupsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(group.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := gtuo.mutation.RemovedGroupsIDs(); len(nodes) > 0 && !gtuo.mutation.GroupsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   grouptype.GroupsTable,
			Columns: []string{grouptype.GroupsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(group.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := gtuo.mutation.GroupsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   grouptype.GroupsTable,
			Columns: []string{grouptype.GroupsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(group.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &GroupType{config: gtuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, gtuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{grouptype.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	gtuo.mutation.done = true
	return _node, nil
}
