// Code generated by ent, DO NOT EDIT.

package predicate

import (
	"entgo.io/ent/dialect/sql"
)

// API is the predicate function for api builders.
type API func(*sql.Selector)

// Application is the predicate function for application builders.
type Application func(*sql.Selector)

// Button is the predicate function for button builders.
type Button func(*sql.Selector)

// File is the predicate function for file builders.
type File func(*sql.Selector)

// Group is the predicate function for group builders.
type Group func(*sql.Selector)

// GroupType is the predicate function for grouptype builders.
type GroupType func(*sql.Selector)

// Menu is the predicate function for menu builders.
type Menu func(*sql.Selector)

// Organization is the predicate function for organization builders.
type Organization func(*sql.Selector)

// OrganizationUserInfo is the predicate function for organizationuserinfo builders.
type OrganizationUserInfo func(*sql.Selector)

// Position is the predicate function for position builders.
type Position func(*sql.Selector)

// Role is the predicate function for role builders.
type Role func(*sql.Selector)

// Tenant is the predicate function for tenant builders.
type Tenant func(*sql.Selector)

// TenantUserInfo is the predicate function for tenantuserinfo builders.
type TenantUserInfo func(*sql.Selector)

// Token is the predicate function for token builders.
type Token func(*sql.Selector)

// User is the predicate function for user builders.
type User func(*sql.Selector)
