// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"fmt"
	"phoenix/service/saas/model/ent/api"
	"phoenix/service/saas/model/ent/application"
	"phoenix/service/saas/model/ent/button"
	"phoenix/service/saas/model/ent/file"
	"phoenix/service/saas/model/ent/group"
	"phoenix/service/saas/model/ent/grouptype"
	"phoenix/service/saas/model/ent/menu"
	"phoenix/service/saas/model/ent/organization"
	"phoenix/service/saas/model/ent/organizationuserinfo"
	"phoenix/service/saas/model/ent/position"
	"phoenix/service/saas/model/ent/role"
	"phoenix/service/saas/model/ent/tenant"
	"phoenix/service/saas/model/ent/tenantuserinfo"
	"phoenix/service/saas/model/ent/token"
	"phoenix/service/saas/model/ent/user"

	"entgo.io/ent/dialect/sql"
)

const errInvalidPage = "INVALID_PAGE"

const (
	listField     = "list"
	pageNumField  = "pageNum"
	pageSizeField = "pageSize"
)

type PageDetails struct {
	Page  uint64 `json:"page"`
	Size  uint64 `json:"size"`
	Total uint64 `json:"total"`
}

// OrderDirection defines the directions in which to order a list of items.
type OrderDirection string

const (
	// OrderDirectionAsc specifies an ascending order.
	OrderDirectionAsc OrderDirection = "ASC"
	// OrderDirectionDesc specifies a descending order.
	OrderDirectionDesc OrderDirection = "DESC"
)

// Validate the order direction value.
func (o OrderDirection) Validate() error {
	if o != OrderDirectionAsc && o != OrderDirectionDesc {
		return fmt.Errorf("%s is not a valid OrderDirection", o)
	}
	return nil
}

// String implements fmt.Stringer interface.
func (o OrderDirection) String() string {
	return string(o)
}

func (o OrderDirection) reverse() OrderDirection {
	if o == OrderDirectionDesc {
		return OrderDirectionAsc
	}
	return OrderDirectionDesc
}

const errInvalidPagination = "INVALID_PAGINATION"

type APIPager struct {
	Order  func(*sql.Selector)
	Filter func(*APIQuery) (*APIQuery, error)
}

// APIPaginateOption enables pagination customization.
type APIPaginateOption func(*APIPager)

// DefaultAPIOrder is the default ordering of API.
var DefaultAPIOrder = Desc(api.FieldID)

func newAPIPager(opts []APIPaginateOption) (*APIPager, error) {
	pager := &APIPager{}
	for _, opt := range opts {
		opt(pager)
	}
	if pager.Order == nil {
		pager.Order = DefaultAPIOrder
	}
	return pager, nil
}

func (p *APIPager) ApplyFilter(query *APIQuery) (*APIQuery, error) {
	if p.Filter != nil {
		return p.Filter(query)
	}
	return query, nil
}

// APIPageList is API PageList result.
type APIPageList struct {
	List        []*API       `json:"list"`
	PageDetails *PageDetails `json:"pageDetails"`
}

func (a *APIQuery) Page(
	ctx context.Context, pageNum uint64, pageSize uint64, opts ...APIPaginateOption,
) (*APIPageList, error) {

	pager, err := newAPIPager(opts)
	if err != nil {
		return nil, err
	}

	if a, err = pager.ApplyFilter(a); err != nil {
		return nil, err
	}

	ret := &APIPageList{}

	ret.PageDetails = &PageDetails{
		Page: pageNum,
		Size: pageSize,
	}

	count, err := a.Clone().Count(ctx)

	if err != nil {
		return nil, err
	}

	ret.PageDetails.Total = uint64(count)

	if pager.Order != nil {
		a = a.Order(pager.Order)
	} else {
		a = a.Order(DefaultAPIOrder)
	}
	if pageSize == 0 {
		pageSize = 10
	}
	if pageNum == 0 {
		pageNum = 1
	}
	a = a.Offset(int((pageNum - 1) * pageSize)).Limit(int(pageSize))
	list, err := a.All(ctx)
	if err != nil {
		return nil, err
	}
	ret.List = list

	return ret, nil
}

type ApplicationPager struct {
	Order  func(*sql.Selector)
	Filter func(*ApplicationQuery) (*ApplicationQuery, error)
}

// ApplicationPaginateOption enables pagination customization.
type ApplicationPaginateOption func(*ApplicationPager)

// DefaultApplicationOrder is the default ordering of Application.
var DefaultApplicationOrder = Desc(application.FieldID)

func newApplicationPager(opts []ApplicationPaginateOption) (*ApplicationPager, error) {
	pager := &ApplicationPager{}
	for _, opt := range opts {
		opt(pager)
	}
	if pager.Order == nil {
		pager.Order = DefaultApplicationOrder
	}
	return pager, nil
}

func (p *ApplicationPager) ApplyFilter(query *ApplicationQuery) (*ApplicationQuery, error) {
	if p.Filter != nil {
		return p.Filter(query)
	}
	return query, nil
}

// ApplicationPageList is Application PageList result.
type ApplicationPageList struct {
	List        []*Application `json:"list"`
	PageDetails *PageDetails   `json:"pageDetails"`
}

func (a *ApplicationQuery) Page(
	ctx context.Context, pageNum uint64, pageSize uint64, opts ...ApplicationPaginateOption,
) (*ApplicationPageList, error) {

	pager, err := newApplicationPager(opts)
	if err != nil {
		return nil, err
	}

	if a, err = pager.ApplyFilter(a); err != nil {
		return nil, err
	}

	ret := &ApplicationPageList{}

	ret.PageDetails = &PageDetails{
		Page: pageNum,
		Size: pageSize,
	}

	count, err := a.Clone().Count(ctx)

	if err != nil {
		return nil, err
	}

	ret.PageDetails.Total = uint64(count)

	if pager.Order != nil {
		a = a.Order(pager.Order)
	} else {
		a = a.Order(DefaultApplicationOrder)
	}
	if pageSize == 0 {
		pageSize = 10
	}
	if pageNum == 0 {
		pageNum = 1
	}
	a = a.Offset(int((pageNum - 1) * pageSize)).Limit(int(pageSize))
	list, err := a.All(ctx)
	if err != nil {
		return nil, err
	}
	ret.List = list

	return ret, nil
}

type ButtonPager struct {
	Order  func(*sql.Selector)
	Filter func(*ButtonQuery) (*ButtonQuery, error)
}

// ButtonPaginateOption enables pagination customization.
type ButtonPaginateOption func(*ButtonPager)

// DefaultButtonOrder is the default ordering of Button.
var DefaultButtonOrder = Desc(button.FieldID)

func newButtonPager(opts []ButtonPaginateOption) (*ButtonPager, error) {
	pager := &ButtonPager{}
	for _, opt := range opts {
		opt(pager)
	}
	if pager.Order == nil {
		pager.Order = DefaultButtonOrder
	}
	return pager, nil
}

func (p *ButtonPager) ApplyFilter(query *ButtonQuery) (*ButtonQuery, error) {
	if p.Filter != nil {
		return p.Filter(query)
	}
	return query, nil
}

// ButtonPageList is Button PageList result.
type ButtonPageList struct {
	List        []*Button    `json:"list"`
	PageDetails *PageDetails `json:"pageDetails"`
}

func (b *ButtonQuery) Page(
	ctx context.Context, pageNum uint64, pageSize uint64, opts ...ButtonPaginateOption,
) (*ButtonPageList, error) {

	pager, err := newButtonPager(opts)
	if err != nil {
		return nil, err
	}

	if b, err = pager.ApplyFilter(b); err != nil {
		return nil, err
	}

	ret := &ButtonPageList{}

	ret.PageDetails = &PageDetails{
		Page: pageNum,
		Size: pageSize,
	}

	count, err := b.Clone().Count(ctx)

	if err != nil {
		return nil, err
	}

	ret.PageDetails.Total = uint64(count)

	if pager.Order != nil {
		b = b.Order(pager.Order)
	} else {
		b = b.Order(DefaultButtonOrder)
	}
	if pageSize == 0 {
		pageSize = 10
	}
	if pageNum == 0 {
		pageNum = 1
	}
	b = b.Offset(int((pageNum - 1) * pageSize)).Limit(int(pageSize))
	list, err := b.All(ctx)
	if err != nil {
		return nil, err
	}
	ret.List = list

	return ret, nil
}

type FilePager struct {
	Order  func(*sql.Selector)
	Filter func(*FileQuery) (*FileQuery, error)
}

// FilePaginateOption enables pagination customization.
type FilePaginateOption func(*FilePager)

// DefaultFileOrder is the default ordering of File.
var DefaultFileOrder = Desc(file.FieldID)

func newFilePager(opts []FilePaginateOption) (*FilePager, error) {
	pager := &FilePager{}
	for _, opt := range opts {
		opt(pager)
	}
	if pager.Order == nil {
		pager.Order = DefaultFileOrder
	}
	return pager, nil
}

func (p *FilePager) ApplyFilter(query *FileQuery) (*FileQuery, error) {
	if p.Filter != nil {
		return p.Filter(query)
	}
	return query, nil
}

// FilePageList is File PageList result.
type FilePageList struct {
	List        []*File      `json:"list"`
	PageDetails *PageDetails `json:"pageDetails"`
}

func (f *FileQuery) Page(
	ctx context.Context, pageNum uint64, pageSize uint64, opts ...FilePaginateOption,
) (*FilePageList, error) {

	pager, err := newFilePager(opts)
	if err != nil {
		return nil, err
	}

	if f, err = pager.ApplyFilter(f); err != nil {
		return nil, err
	}

	ret := &FilePageList{}

	ret.PageDetails = &PageDetails{
		Page: pageNum,
		Size: pageSize,
	}

	count, err := f.Clone().Count(ctx)

	if err != nil {
		return nil, err
	}

	ret.PageDetails.Total = uint64(count)

	if pager.Order != nil {
		f = f.Order(pager.Order)
	} else {
		f = f.Order(DefaultFileOrder)
	}
	if pageSize == 0 {
		pageSize = 10
	}
	if pageNum == 0 {
		pageNum = 1
	}
	f = f.Offset(int((pageNum - 1) * pageSize)).Limit(int(pageSize))
	list, err := f.All(ctx)
	if err != nil {
		return nil, err
	}
	ret.List = list

	return ret, nil
}

type GroupPager struct {
	Order  func(*sql.Selector)
	Filter func(*GroupQuery) (*GroupQuery, error)
}

// GroupPaginateOption enables pagination customization.
type GroupPaginateOption func(*GroupPager)

// DefaultGroupOrder is the default ordering of Group.
var DefaultGroupOrder = Desc(group.FieldID)

func newGroupPager(opts []GroupPaginateOption) (*GroupPager, error) {
	pager := &GroupPager{}
	for _, opt := range opts {
		opt(pager)
	}
	if pager.Order == nil {
		pager.Order = DefaultGroupOrder
	}
	return pager, nil
}

func (p *GroupPager) ApplyFilter(query *GroupQuery) (*GroupQuery, error) {
	if p.Filter != nil {
		return p.Filter(query)
	}
	return query, nil
}

// GroupPageList is Group PageList result.
type GroupPageList struct {
	List        []*Group     `json:"list"`
	PageDetails *PageDetails `json:"pageDetails"`
}

func (gr *GroupQuery) Page(
	ctx context.Context, pageNum uint64, pageSize uint64, opts ...GroupPaginateOption,
) (*GroupPageList, error) {

	pager, err := newGroupPager(opts)
	if err != nil {
		return nil, err
	}

	if gr, err = pager.ApplyFilter(gr); err != nil {
		return nil, err
	}

	ret := &GroupPageList{}

	ret.PageDetails = &PageDetails{
		Page: pageNum,
		Size: pageSize,
	}

	count, err := gr.Clone().Count(ctx)

	if err != nil {
		return nil, err
	}

	ret.PageDetails.Total = uint64(count)

	if pager.Order != nil {
		gr = gr.Order(pager.Order)
	} else {
		gr = gr.Order(DefaultGroupOrder)
	}
	if pageSize == 0 {
		pageSize = 10
	}
	if pageNum == 0 {
		pageNum = 1
	}
	gr = gr.Offset(int((pageNum - 1) * pageSize)).Limit(int(pageSize))
	list, err := gr.All(ctx)
	if err != nil {
		return nil, err
	}
	ret.List = list

	return ret, nil
}

type GroupTypePager struct {
	Order  func(*sql.Selector)
	Filter func(*GroupTypeQuery) (*GroupTypeQuery, error)
}

// GroupTypePaginateOption enables pagination customization.
type GroupTypePaginateOption func(*GroupTypePager)

// DefaultGroupTypeOrder is the default ordering of GroupType.
var DefaultGroupTypeOrder = Desc(grouptype.FieldID)

func newGroupTypePager(opts []GroupTypePaginateOption) (*GroupTypePager, error) {
	pager := &GroupTypePager{}
	for _, opt := range opts {
		opt(pager)
	}
	if pager.Order == nil {
		pager.Order = DefaultGroupTypeOrder
	}
	return pager, nil
}

func (p *GroupTypePager) ApplyFilter(query *GroupTypeQuery) (*GroupTypeQuery, error) {
	if p.Filter != nil {
		return p.Filter(query)
	}
	return query, nil
}

// GroupTypePageList is GroupType PageList result.
type GroupTypePageList struct {
	List        []*GroupType `json:"list"`
	PageDetails *PageDetails `json:"pageDetails"`
}

func (gt *GroupTypeQuery) Page(
	ctx context.Context, pageNum uint64, pageSize uint64, opts ...GroupTypePaginateOption,
) (*GroupTypePageList, error) {

	pager, err := newGroupTypePager(opts)
	if err != nil {
		return nil, err
	}

	if gt, err = pager.ApplyFilter(gt); err != nil {
		return nil, err
	}

	ret := &GroupTypePageList{}

	ret.PageDetails = &PageDetails{
		Page: pageNum,
		Size: pageSize,
	}

	count, err := gt.Clone().Count(ctx)

	if err != nil {
		return nil, err
	}

	ret.PageDetails.Total = uint64(count)

	if pager.Order != nil {
		gt = gt.Order(pager.Order)
	} else {
		gt = gt.Order(DefaultGroupTypeOrder)
	}
	if pageSize == 0 {
		pageSize = 10
	}
	if pageNum == 0 {
		pageNum = 1
	}
	gt = gt.Offset(int((pageNum - 1) * pageSize)).Limit(int(pageSize))
	list, err := gt.All(ctx)
	if err != nil {
		return nil, err
	}
	ret.List = list

	return ret, nil
}

type MenuPager struct {
	Order  func(*sql.Selector)
	Filter func(*MenuQuery) (*MenuQuery, error)
}

// MenuPaginateOption enables pagination customization.
type MenuPaginateOption func(*MenuPager)

// DefaultMenuOrder is the default ordering of Menu.
var DefaultMenuOrder = Desc(menu.FieldID)

func newMenuPager(opts []MenuPaginateOption) (*MenuPager, error) {
	pager := &MenuPager{}
	for _, opt := range opts {
		opt(pager)
	}
	if pager.Order == nil {
		pager.Order = DefaultMenuOrder
	}
	return pager, nil
}

func (p *MenuPager) ApplyFilter(query *MenuQuery) (*MenuQuery, error) {
	if p.Filter != nil {
		return p.Filter(query)
	}
	return query, nil
}

// MenuPageList is Menu PageList result.
type MenuPageList struct {
	List        []*Menu      `json:"list"`
	PageDetails *PageDetails `json:"pageDetails"`
}

func (m *MenuQuery) Page(
	ctx context.Context, pageNum uint64, pageSize uint64, opts ...MenuPaginateOption,
) (*MenuPageList, error) {

	pager, err := newMenuPager(opts)
	if err != nil {
		return nil, err
	}

	if m, err = pager.ApplyFilter(m); err != nil {
		return nil, err
	}

	ret := &MenuPageList{}

	ret.PageDetails = &PageDetails{
		Page: pageNum,
		Size: pageSize,
	}

	count, err := m.Clone().Count(ctx)

	if err != nil {
		return nil, err
	}

	ret.PageDetails.Total = uint64(count)

	if pager.Order != nil {
		m = m.Order(pager.Order)
	} else {
		m = m.Order(DefaultMenuOrder)
	}
	if pageSize == 0 {
		pageSize = 10
	}
	if pageNum == 0 {
		pageNum = 1
	}
	m = m.Offset(int((pageNum - 1) * pageSize)).Limit(int(pageSize))
	list, err := m.All(ctx)
	if err != nil {
		return nil, err
	}
	ret.List = list

	return ret, nil
}

type OrganizationPager struct {
	Order  func(*sql.Selector)
	Filter func(*OrganizationQuery) (*OrganizationQuery, error)
}

// OrganizationPaginateOption enables pagination customization.
type OrganizationPaginateOption func(*OrganizationPager)

// DefaultOrganizationOrder is the default ordering of Organization.
var DefaultOrganizationOrder = Desc(organization.FieldID)

func newOrganizationPager(opts []OrganizationPaginateOption) (*OrganizationPager, error) {
	pager := &OrganizationPager{}
	for _, opt := range opts {
		opt(pager)
	}
	if pager.Order == nil {
		pager.Order = DefaultOrganizationOrder
	}
	return pager, nil
}

func (p *OrganizationPager) ApplyFilter(query *OrganizationQuery) (*OrganizationQuery, error) {
	if p.Filter != nil {
		return p.Filter(query)
	}
	return query, nil
}

// OrganizationPageList is Organization PageList result.
type OrganizationPageList struct {
	List        []*Organization `json:"list"`
	PageDetails *PageDetails    `json:"pageDetails"`
}

func (o *OrganizationQuery) Page(
	ctx context.Context, pageNum uint64, pageSize uint64, opts ...OrganizationPaginateOption,
) (*OrganizationPageList, error) {

	pager, err := newOrganizationPager(opts)
	if err != nil {
		return nil, err
	}

	if o, err = pager.ApplyFilter(o); err != nil {
		return nil, err
	}

	ret := &OrganizationPageList{}

	ret.PageDetails = &PageDetails{
		Page: pageNum,
		Size: pageSize,
	}

	count, err := o.Clone().Count(ctx)

	if err != nil {
		return nil, err
	}

	ret.PageDetails.Total = uint64(count)

	if pager.Order != nil {
		o = o.Order(pager.Order)
	} else {
		o = o.Order(DefaultOrganizationOrder)
	}
	if pageSize == 0 {
		pageSize = 10
	}
	if pageNum == 0 {
		pageNum = 1
	}
	o = o.Offset(int((pageNum - 1) * pageSize)).Limit(int(pageSize))
	list, err := o.All(ctx)
	if err != nil {
		return nil, err
	}
	ret.List = list

	return ret, nil
}

type OrganizationUserInfoPager struct {
	Order  func(*sql.Selector)
	Filter func(*OrganizationUserInfoQuery) (*OrganizationUserInfoQuery, error)
}

// OrganizationUserInfoPaginateOption enables pagination customization.
type OrganizationUserInfoPaginateOption func(*OrganizationUserInfoPager)

// DefaultOrganizationUserInfoOrder is the default ordering of OrganizationUserInfo.
var DefaultOrganizationUserInfoOrder = Desc(organizationuserinfo.FieldID)

func newOrganizationUserInfoPager(opts []OrganizationUserInfoPaginateOption) (*OrganizationUserInfoPager, error) {
	pager := &OrganizationUserInfoPager{}
	for _, opt := range opts {
		opt(pager)
	}
	if pager.Order == nil {
		pager.Order = DefaultOrganizationUserInfoOrder
	}
	return pager, nil
}

func (p *OrganizationUserInfoPager) ApplyFilter(query *OrganizationUserInfoQuery) (*OrganizationUserInfoQuery, error) {
	if p.Filter != nil {
		return p.Filter(query)
	}
	return query, nil
}

// OrganizationUserInfoPageList is OrganizationUserInfo PageList result.
type OrganizationUserInfoPageList struct {
	List        []*OrganizationUserInfo `json:"list"`
	PageDetails *PageDetails            `json:"pageDetails"`
}

func (oui *OrganizationUserInfoQuery) Page(
	ctx context.Context, pageNum uint64, pageSize uint64, opts ...OrganizationUserInfoPaginateOption,
) (*OrganizationUserInfoPageList, error) {

	pager, err := newOrganizationUserInfoPager(opts)
	if err != nil {
		return nil, err
	}

	if oui, err = pager.ApplyFilter(oui); err != nil {
		return nil, err
	}

	ret := &OrganizationUserInfoPageList{}

	ret.PageDetails = &PageDetails{
		Page: pageNum,
		Size: pageSize,
	}

	count, err := oui.Clone().Count(ctx)

	if err != nil {
		return nil, err
	}

	ret.PageDetails.Total = uint64(count)

	if pager.Order != nil {
		oui = oui.Order(pager.Order)
	} else {
		oui = oui.Order(DefaultOrganizationUserInfoOrder)
	}
	if pageSize == 0 {
		pageSize = 10
	}
	if pageNum == 0 {
		pageNum = 1
	}
	oui = oui.Offset(int((pageNum - 1) * pageSize)).Limit(int(pageSize))
	list, err := oui.All(ctx)
	if err != nil {
		return nil, err
	}
	ret.List = list

	return ret, nil
}

type PositionPager struct {
	Order  func(*sql.Selector)
	Filter func(*PositionQuery) (*PositionQuery, error)
}

// PositionPaginateOption enables pagination customization.
type PositionPaginateOption func(*PositionPager)

// DefaultPositionOrder is the default ordering of Position.
var DefaultPositionOrder = Desc(position.FieldID)

func newPositionPager(opts []PositionPaginateOption) (*PositionPager, error) {
	pager := &PositionPager{}
	for _, opt := range opts {
		opt(pager)
	}
	if pager.Order == nil {
		pager.Order = DefaultPositionOrder
	}
	return pager, nil
}

func (p *PositionPager) ApplyFilter(query *PositionQuery) (*PositionQuery, error) {
	if p.Filter != nil {
		return p.Filter(query)
	}
	return query, nil
}

// PositionPageList is Position PageList result.
type PositionPageList struct {
	List        []*Position  `json:"list"`
	PageDetails *PageDetails `json:"pageDetails"`
}

func (po *PositionQuery) Page(
	ctx context.Context, pageNum uint64, pageSize uint64, opts ...PositionPaginateOption,
) (*PositionPageList, error) {

	pager, err := newPositionPager(opts)
	if err != nil {
		return nil, err
	}

	if po, err = pager.ApplyFilter(po); err != nil {
		return nil, err
	}

	ret := &PositionPageList{}

	ret.PageDetails = &PageDetails{
		Page: pageNum,
		Size: pageSize,
	}

	count, err := po.Clone().Count(ctx)

	if err != nil {
		return nil, err
	}

	ret.PageDetails.Total = uint64(count)

	if pager.Order != nil {
		po = po.Order(pager.Order)
	} else {
		po = po.Order(DefaultPositionOrder)
	}
	if pageSize == 0 {
		pageSize = 10
	}
	if pageNum == 0 {
		pageNum = 1
	}
	po = po.Offset(int((pageNum - 1) * pageSize)).Limit(int(pageSize))
	list, err := po.All(ctx)
	if err != nil {
		return nil, err
	}
	ret.List = list

	return ret, nil
}

type RolePager struct {
	Order  func(*sql.Selector)
	Filter func(*RoleQuery) (*RoleQuery, error)
}

// RolePaginateOption enables pagination customization.
type RolePaginateOption func(*RolePager)

// DefaultRoleOrder is the default ordering of Role.
var DefaultRoleOrder = Desc(role.FieldID)

func newRolePager(opts []RolePaginateOption) (*RolePager, error) {
	pager := &RolePager{}
	for _, opt := range opts {
		opt(pager)
	}
	if pager.Order == nil {
		pager.Order = DefaultRoleOrder
	}
	return pager, nil
}

func (p *RolePager) ApplyFilter(query *RoleQuery) (*RoleQuery, error) {
	if p.Filter != nil {
		return p.Filter(query)
	}
	return query, nil
}

// RolePageList is Role PageList result.
type RolePageList struct {
	List        []*Role      `json:"list"`
	PageDetails *PageDetails `json:"pageDetails"`
}

func (r *RoleQuery) Page(
	ctx context.Context, pageNum uint64, pageSize uint64, opts ...RolePaginateOption,
) (*RolePageList, error) {

	pager, err := newRolePager(opts)
	if err != nil {
		return nil, err
	}

	if r, err = pager.ApplyFilter(r); err != nil {
		return nil, err
	}

	ret := &RolePageList{}

	ret.PageDetails = &PageDetails{
		Page: pageNum,
		Size: pageSize,
	}

	count, err := r.Clone().Count(ctx)

	if err != nil {
		return nil, err
	}

	ret.PageDetails.Total = uint64(count)

	if pager.Order != nil {
		r = r.Order(pager.Order)
	} else {
		r = r.Order(DefaultRoleOrder)
	}
	if pageSize == 0 {
		pageSize = 10
	}
	if pageNum == 0 {
		pageNum = 1
	}
	r = r.Offset(int((pageNum - 1) * pageSize)).Limit(int(pageSize))
	list, err := r.All(ctx)
	if err != nil {
		return nil, err
	}
	ret.List = list

	return ret, nil
}

type TenantPager struct {
	Order  func(*sql.Selector)
	Filter func(*TenantQuery) (*TenantQuery, error)
}

// TenantPaginateOption enables pagination customization.
type TenantPaginateOption func(*TenantPager)

// DefaultTenantOrder is the default ordering of Tenant.
var DefaultTenantOrder = Desc(tenant.FieldID)

func newTenantPager(opts []TenantPaginateOption) (*TenantPager, error) {
	pager := &TenantPager{}
	for _, opt := range opts {
		opt(pager)
	}
	if pager.Order == nil {
		pager.Order = DefaultTenantOrder
	}
	return pager, nil
}

func (p *TenantPager) ApplyFilter(query *TenantQuery) (*TenantQuery, error) {
	if p.Filter != nil {
		return p.Filter(query)
	}
	return query, nil
}

// TenantPageList is Tenant PageList result.
type TenantPageList struct {
	List        []*Tenant    `json:"list"`
	PageDetails *PageDetails `json:"pageDetails"`
}

func (t *TenantQuery) Page(
	ctx context.Context, pageNum uint64, pageSize uint64, opts ...TenantPaginateOption,
) (*TenantPageList, error) {

	pager, err := newTenantPager(opts)
	if err != nil {
		return nil, err
	}

	if t, err = pager.ApplyFilter(t); err != nil {
		return nil, err
	}

	ret := &TenantPageList{}

	ret.PageDetails = &PageDetails{
		Page: pageNum,
		Size: pageSize,
	}

	count, err := t.Clone().Count(ctx)

	if err != nil {
		return nil, err
	}

	ret.PageDetails.Total = uint64(count)

	if pager.Order != nil {
		t = t.Order(pager.Order)
	} else {
		t = t.Order(DefaultTenantOrder)
	}
	if pageSize == 0 {
		pageSize = 10
	}
	if pageNum == 0 {
		pageNum = 1
	}
	t = t.Offset(int((pageNum - 1) * pageSize)).Limit(int(pageSize))
	list, err := t.All(ctx)
	if err != nil {
		return nil, err
	}
	ret.List = list

	return ret, nil
}

type TenantUserInfoPager struct {
	Order  func(*sql.Selector)
	Filter func(*TenantUserInfoQuery) (*TenantUserInfoQuery, error)
}

// TenantUserInfoPaginateOption enables pagination customization.
type TenantUserInfoPaginateOption func(*TenantUserInfoPager)

// DefaultTenantUserInfoOrder is the default ordering of TenantUserInfo.
var DefaultTenantUserInfoOrder = Desc(tenantuserinfo.FieldID)

func newTenantUserInfoPager(opts []TenantUserInfoPaginateOption) (*TenantUserInfoPager, error) {
	pager := &TenantUserInfoPager{}
	for _, opt := range opts {
		opt(pager)
	}
	if pager.Order == nil {
		pager.Order = DefaultTenantUserInfoOrder
	}
	return pager, nil
}

func (p *TenantUserInfoPager) ApplyFilter(query *TenantUserInfoQuery) (*TenantUserInfoQuery, error) {
	if p.Filter != nil {
		return p.Filter(query)
	}
	return query, nil
}

// TenantUserInfoPageList is TenantUserInfo PageList result.
type TenantUserInfoPageList struct {
	List        []*TenantUserInfo `json:"list"`
	PageDetails *PageDetails      `json:"pageDetails"`
}

func (tui *TenantUserInfoQuery) Page(
	ctx context.Context, pageNum uint64, pageSize uint64, opts ...TenantUserInfoPaginateOption,
) (*TenantUserInfoPageList, error) {

	pager, err := newTenantUserInfoPager(opts)
	if err != nil {
		return nil, err
	}

	if tui, err = pager.ApplyFilter(tui); err != nil {
		return nil, err
	}

	ret := &TenantUserInfoPageList{}

	ret.PageDetails = &PageDetails{
		Page: pageNum,
		Size: pageSize,
	}

	count, err := tui.Clone().Count(ctx)

	if err != nil {
		return nil, err
	}

	ret.PageDetails.Total = uint64(count)

	if pager.Order != nil {
		tui = tui.Order(pager.Order)
	} else {
		tui = tui.Order(DefaultTenantUserInfoOrder)
	}
	if pageSize == 0 {
		pageSize = 10
	}
	if pageNum == 0 {
		pageNum = 1
	}
	tui = tui.Offset(int((pageNum - 1) * pageSize)).Limit(int(pageSize))
	list, err := tui.All(ctx)
	if err != nil {
		return nil, err
	}
	ret.List = list

	return ret, nil
}

type TokenPager struct {
	Order  func(*sql.Selector)
	Filter func(*TokenQuery) (*TokenQuery, error)
}

// TokenPaginateOption enables pagination customization.
type TokenPaginateOption func(*TokenPager)

// DefaultTokenOrder is the default ordering of Token.
var DefaultTokenOrder = Desc(token.FieldID)

func newTokenPager(opts []TokenPaginateOption) (*TokenPager, error) {
	pager := &TokenPager{}
	for _, opt := range opts {
		opt(pager)
	}
	if pager.Order == nil {
		pager.Order = DefaultTokenOrder
	}
	return pager, nil
}

func (p *TokenPager) ApplyFilter(query *TokenQuery) (*TokenQuery, error) {
	if p.Filter != nil {
		return p.Filter(query)
	}
	return query, nil
}

// TokenPageList is Token PageList result.
type TokenPageList struct {
	List        []*Token     `json:"list"`
	PageDetails *PageDetails `json:"pageDetails"`
}

func (t *TokenQuery) Page(
	ctx context.Context, pageNum uint64, pageSize uint64, opts ...TokenPaginateOption,
) (*TokenPageList, error) {

	pager, err := newTokenPager(opts)
	if err != nil {
		return nil, err
	}

	if t, err = pager.ApplyFilter(t); err != nil {
		return nil, err
	}

	ret := &TokenPageList{}

	ret.PageDetails = &PageDetails{
		Page: pageNum,
		Size: pageSize,
	}

	count, err := t.Clone().Count(ctx)

	if err != nil {
		return nil, err
	}

	ret.PageDetails.Total = uint64(count)

	if pager.Order != nil {
		t = t.Order(pager.Order)
	} else {
		t = t.Order(DefaultTokenOrder)
	}
	if pageSize == 0 {
		pageSize = 10
	}
	if pageNum == 0 {
		pageNum = 1
	}
	t = t.Offset(int((pageNum - 1) * pageSize)).Limit(int(pageSize))
	list, err := t.All(ctx)
	if err != nil {
		return nil, err
	}
	ret.List = list

	return ret, nil
}

type UserPager struct {
	Order  func(*sql.Selector)
	Filter func(*UserQuery) (*UserQuery, error)
}

// UserPaginateOption enables pagination customization.
type UserPaginateOption func(*UserPager)

// DefaultUserOrder is the default ordering of User.
var DefaultUserOrder = Desc(user.FieldID)

func newUserPager(opts []UserPaginateOption) (*UserPager, error) {
	pager := &UserPager{}
	for _, opt := range opts {
		opt(pager)
	}
	if pager.Order == nil {
		pager.Order = DefaultUserOrder
	}
	return pager, nil
}

func (p *UserPager) ApplyFilter(query *UserQuery) (*UserQuery, error) {
	if p.Filter != nil {
		return p.Filter(query)
	}
	return query, nil
}

// UserPageList is User PageList result.
type UserPageList struct {
	List        []*User      `json:"list"`
	PageDetails *PageDetails `json:"pageDetails"`
}

func (u *UserQuery) Page(
	ctx context.Context, pageNum uint64, pageSize uint64, opts ...UserPaginateOption,
) (*UserPageList, error) {

	pager, err := newUserPager(opts)
	if err != nil {
		return nil, err
	}

	if u, err = pager.ApplyFilter(u); err != nil {
		return nil, err
	}

	ret := &UserPageList{}

	ret.PageDetails = &PageDetails{
		Page: pageNum,
		Size: pageSize,
	}

	count, err := u.Clone().Count(ctx)

	if err != nil {
		return nil, err
	}

	ret.PageDetails.Total = uint64(count)

	if pager.Order != nil {
		u = u.Order(pager.Order)
	} else {
		u = u.Order(DefaultUserOrder)
	}
	if pageSize == 0 {
		pageSize = 10
	}
	if pageNum == 0 {
		pageNum = 1
	}
	u = u.Offset(int((pageNum - 1) * pageSize)).Limit(int(pageSize))
	list, err := u.All(ctx)
	if err != nil {
		return nil, err
	}
	ret.List = list

	return ret, nil
}
