// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/group"
	"phoenix/service/saas/model/ent/grouptype"
	"phoenix/service/saas/model/ent/tenant"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// GroupTypeCreate is the builder for creating a GroupType entity.
type GroupTypeCreate struct {
	config
	mutation *GroupTypeMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (gtc *GroupTypeCreate) SetCreatedAt(t time.Time) *GroupTypeCreate {
	gtc.mutation.SetCreatedAt(t)
	return gtc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (gtc *GroupTypeCreate) SetNillableCreatedAt(t *time.Time) *GroupTypeCreate {
	if t != nil {
		gtc.SetCreatedAt(*t)
	}
	return gtc
}

// SetUpdatedAt sets the "updated_at" field.
func (gtc *GroupTypeCreate) SetUpdatedAt(t time.Time) *GroupTypeCreate {
	gtc.mutation.SetUpdatedAt(t)
	return gtc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (gtc *GroupTypeCreate) SetNillableUpdatedAt(t *time.Time) *GroupTypeCreate {
	if t != nil {
		gtc.SetUpdatedAt(*t)
	}
	return gtc
}

// SetStatus sets the "status" field.
func (gtc *GroupTypeCreate) SetStatus(b bool) *GroupTypeCreate {
	gtc.mutation.SetStatus(b)
	return gtc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (gtc *GroupTypeCreate) SetNillableStatus(b *bool) *GroupTypeCreate {
	if b != nil {
		gtc.SetStatus(*b)
	}
	return gtc
}

// SetSort sets the "sort" field.
func (gtc *GroupTypeCreate) SetSort(u uint32) *GroupTypeCreate {
	gtc.mutation.SetSort(u)
	return gtc
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (gtc *GroupTypeCreate) SetNillableSort(u *uint32) *GroupTypeCreate {
	if u != nil {
		gtc.SetSort(*u)
	}
	return gtc
}

// SetTenantID sets the "tenant_id" field.
func (gtc *GroupTypeCreate) SetTenantID(s string) *GroupTypeCreate {
	gtc.mutation.SetTenantID(s)
	return gtc
}

// SetDeletedAt sets the "deleted_at" field.
func (gtc *GroupTypeCreate) SetDeletedAt(t time.Time) *GroupTypeCreate {
	gtc.mutation.SetDeletedAt(t)
	return gtc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (gtc *GroupTypeCreate) SetNillableDeletedAt(t *time.Time) *GroupTypeCreate {
	if t != nil {
		gtc.SetDeletedAt(*t)
	}
	return gtc
}

// SetName sets the "name" field.
func (gtc *GroupTypeCreate) SetName(s string) *GroupTypeCreate {
	gtc.mutation.SetName(s)
	return gtc
}

// SetCode sets the "code" field.
func (gtc *GroupTypeCreate) SetCode(s string) *GroupTypeCreate {
	gtc.mutation.SetCode(s)
	return gtc
}

// SetRemark sets the "remark" field.
func (gtc *GroupTypeCreate) SetRemark(s string) *GroupTypeCreate {
	gtc.mutation.SetRemark(s)
	return gtc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (gtc *GroupTypeCreate) SetNillableRemark(s *string) *GroupTypeCreate {
	if s != nil {
		gtc.SetRemark(*s)
	}
	return gtc
}

// SetID sets the "id" field.
func (gtc *GroupTypeCreate) SetID(s string) *GroupTypeCreate {
	gtc.mutation.SetID(s)
	return gtc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (gtc *GroupTypeCreate) SetNillableID(s *string) *GroupTypeCreate {
	if s != nil {
		gtc.SetID(*s)
	}
	return gtc
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (gtc *GroupTypeCreate) SetTenant(t *Tenant) *GroupTypeCreate {
	return gtc.SetTenantID(t.ID)
}

// AddGroupIDs adds the "groups" edge to the Group entity by IDs.
func (gtc *GroupTypeCreate) AddGroupIDs(ids ...string) *GroupTypeCreate {
	gtc.mutation.AddGroupIDs(ids...)
	return gtc
}

// AddGroups adds the "groups" edges to the Group entity.
func (gtc *GroupTypeCreate) AddGroups(g ...*Group) *GroupTypeCreate {
	ids := make([]string, len(g))
	for i := range g {
		ids[i] = g[i].ID
	}
	return gtc.AddGroupIDs(ids...)
}

// Mutation returns the GroupTypeMutation object of the builder.
func (gtc *GroupTypeCreate) Mutation() *GroupTypeMutation {
	return gtc.mutation
}

// Save creates the GroupType in the database.
func (gtc *GroupTypeCreate) Save(ctx context.Context) (*GroupType, error) {
	if err := gtc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, gtc.sqlSave, gtc.mutation, gtc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (gtc *GroupTypeCreate) SaveX(ctx context.Context) *GroupType {
	v, err := gtc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (gtc *GroupTypeCreate) Exec(ctx context.Context) error {
	_, err := gtc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (gtc *GroupTypeCreate) ExecX(ctx context.Context) {
	if err := gtc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (gtc *GroupTypeCreate) defaults() error {
	if _, ok := gtc.mutation.CreatedAt(); !ok {
		if grouptype.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized grouptype.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := grouptype.DefaultCreatedAt()
		gtc.mutation.SetCreatedAt(v)
	}
	if _, ok := gtc.mutation.UpdatedAt(); !ok {
		if grouptype.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized grouptype.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := grouptype.DefaultUpdatedAt()
		gtc.mutation.SetUpdatedAt(v)
	}
	if _, ok := gtc.mutation.Status(); !ok {
		v := grouptype.DefaultStatus
		gtc.mutation.SetStatus(v)
	}
	if _, ok := gtc.mutation.Sort(); !ok {
		v := grouptype.DefaultSort
		gtc.mutation.SetSort(v)
	}
	if _, ok := gtc.mutation.Remark(); !ok {
		v := grouptype.DefaultRemark
		gtc.mutation.SetRemark(v)
	}
	if _, ok := gtc.mutation.ID(); !ok {
		if grouptype.DefaultID == nil {
			return fmt.Errorf("ent: uninitialized grouptype.DefaultID (forgotten import ent/runtime?)")
		}
		v := grouptype.DefaultID()
		gtc.mutation.SetID(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (gtc *GroupTypeCreate) check() error {
	if _, ok := gtc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "GroupType.created_at"`)}
	}
	if _, ok := gtc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "GroupType.updated_at"`)}
	}
	if _, ok := gtc.mutation.Sort(); !ok {
		return &ValidationError{Name: "sort", err: errors.New(`ent: missing required field "GroupType.sort"`)}
	}
	if _, ok := gtc.mutation.TenantID(); !ok {
		return &ValidationError{Name: "tenant_id", err: errors.New(`ent: missing required field "GroupType.tenant_id"`)}
	}
	if _, ok := gtc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "GroupType.name"`)}
	}
	if _, ok := gtc.mutation.Code(); !ok {
		return &ValidationError{Name: "code", err: errors.New(`ent: missing required field "GroupType.code"`)}
	}
	if _, ok := gtc.mutation.TenantID(); !ok {
		return &ValidationError{Name: "tenant", err: errors.New(`ent: missing required edge "GroupType.tenant"`)}
	}
	return nil
}

func (gtc *GroupTypeCreate) sqlSave(ctx context.Context) (*GroupType, error) {
	if err := gtc.check(); err != nil {
		return nil, err
	}
	_node, _spec := gtc.createSpec()
	if err := sqlgraph.CreateNode(ctx, gtc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected GroupType.ID type: %T", _spec.ID.Value)
		}
	}
	gtc.mutation.id = &_node.ID
	gtc.mutation.done = true
	return _node, nil
}

func (gtc *GroupTypeCreate) createSpec() (*GroupType, *sqlgraph.CreateSpec) {
	var (
		_node = &GroupType{config: gtc.config}
		_spec = sqlgraph.NewCreateSpec(grouptype.Table, sqlgraph.NewFieldSpec(grouptype.FieldID, field.TypeString))
	)
	_spec.OnConflict = gtc.conflict
	if id, ok := gtc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := gtc.mutation.CreatedAt(); ok {
		_spec.SetField(grouptype.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := gtc.mutation.UpdatedAt(); ok {
		_spec.SetField(grouptype.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := gtc.mutation.Status(); ok {
		_spec.SetField(grouptype.FieldStatus, field.TypeBool, value)
		_node.Status = value
	}
	if value, ok := gtc.mutation.Sort(); ok {
		_spec.SetField(grouptype.FieldSort, field.TypeUint32, value)
		_node.Sort = value
	}
	if value, ok := gtc.mutation.DeletedAt(); ok {
		_spec.SetField(grouptype.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := gtc.mutation.Name(); ok {
		_spec.SetField(grouptype.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := gtc.mutation.Code(); ok {
		_spec.SetField(grouptype.FieldCode, field.TypeString, value)
		_node.Code = value
	}
	if value, ok := gtc.mutation.Remark(); ok {
		_spec.SetField(grouptype.FieldRemark, field.TypeString, value)
		_node.Remark = value
	}
	if nodes := gtc.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   grouptype.TenantTable,
			Columns: []string{grouptype.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TenantID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := gtc.mutation.GroupsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   grouptype.GroupsTable,
			Columns: []string{grouptype.GroupsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(group.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.GroupType.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.GroupTypeUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (gtc *GroupTypeCreate) OnConflict(opts ...sql.ConflictOption) *GroupTypeUpsertOne {
	gtc.conflict = opts
	return &GroupTypeUpsertOne{
		create: gtc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.GroupType.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (gtc *GroupTypeCreate) OnConflictColumns(columns ...string) *GroupTypeUpsertOne {
	gtc.conflict = append(gtc.conflict, sql.ConflictColumns(columns...))
	return &GroupTypeUpsertOne{
		create: gtc,
	}
}

type (
	// GroupTypeUpsertOne is the builder for "upsert"-ing
	//  one GroupType node.
	GroupTypeUpsertOne struct {
		create *GroupTypeCreate
	}

	// GroupTypeUpsert is the "OnConflict" setter.
	GroupTypeUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *GroupTypeUpsert) SetUpdatedAt(v time.Time) *GroupTypeUpsert {
	u.Set(grouptype.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *GroupTypeUpsert) UpdateUpdatedAt() *GroupTypeUpsert {
	u.SetExcluded(grouptype.FieldUpdatedAt)
	return u
}

// SetStatus sets the "status" field.
func (u *GroupTypeUpsert) SetStatus(v bool) *GroupTypeUpsert {
	u.Set(grouptype.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *GroupTypeUpsert) UpdateStatus() *GroupTypeUpsert {
	u.SetExcluded(grouptype.FieldStatus)
	return u
}

// ClearStatus clears the value of the "status" field.
func (u *GroupTypeUpsert) ClearStatus() *GroupTypeUpsert {
	u.SetNull(grouptype.FieldStatus)
	return u
}

// SetSort sets the "sort" field.
func (u *GroupTypeUpsert) SetSort(v uint32) *GroupTypeUpsert {
	u.Set(grouptype.FieldSort, v)
	return u
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *GroupTypeUpsert) UpdateSort() *GroupTypeUpsert {
	u.SetExcluded(grouptype.FieldSort)
	return u
}

// AddSort adds v to the "sort" field.
func (u *GroupTypeUpsert) AddSort(v uint32) *GroupTypeUpsert {
	u.Add(grouptype.FieldSort, v)
	return u
}

// SetTenantID sets the "tenant_id" field.
func (u *GroupTypeUpsert) SetTenantID(v string) *GroupTypeUpsert {
	u.Set(grouptype.FieldTenantID, v)
	return u
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *GroupTypeUpsert) UpdateTenantID() *GroupTypeUpsert {
	u.SetExcluded(grouptype.FieldTenantID)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *GroupTypeUpsert) SetDeletedAt(v time.Time) *GroupTypeUpsert {
	u.Set(grouptype.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *GroupTypeUpsert) UpdateDeletedAt() *GroupTypeUpsert {
	u.SetExcluded(grouptype.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *GroupTypeUpsert) ClearDeletedAt() *GroupTypeUpsert {
	u.SetNull(grouptype.FieldDeletedAt)
	return u
}

// SetName sets the "name" field.
func (u *GroupTypeUpsert) SetName(v string) *GroupTypeUpsert {
	u.Set(grouptype.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *GroupTypeUpsert) UpdateName() *GroupTypeUpsert {
	u.SetExcluded(grouptype.FieldName)
	return u
}

// SetCode sets the "code" field.
func (u *GroupTypeUpsert) SetCode(v string) *GroupTypeUpsert {
	u.Set(grouptype.FieldCode, v)
	return u
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *GroupTypeUpsert) UpdateCode() *GroupTypeUpsert {
	u.SetExcluded(grouptype.FieldCode)
	return u
}

// SetRemark sets the "remark" field.
func (u *GroupTypeUpsert) SetRemark(v string) *GroupTypeUpsert {
	u.Set(grouptype.FieldRemark, v)
	return u
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *GroupTypeUpsert) UpdateRemark() *GroupTypeUpsert {
	u.SetExcluded(grouptype.FieldRemark)
	return u
}

// ClearRemark clears the value of the "remark" field.
func (u *GroupTypeUpsert) ClearRemark() *GroupTypeUpsert {
	u.SetNull(grouptype.FieldRemark)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.GroupType.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(grouptype.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *GroupTypeUpsertOne) UpdateNewValues() *GroupTypeUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(grouptype.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(grouptype.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.GroupType.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *GroupTypeUpsertOne) Ignore() *GroupTypeUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *GroupTypeUpsertOne) DoNothing() *GroupTypeUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the GroupTypeCreate.OnConflict
// documentation for more info.
func (u *GroupTypeUpsertOne) Update(set func(*GroupTypeUpsert)) *GroupTypeUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&GroupTypeUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *GroupTypeUpsertOne) SetUpdatedAt(v time.Time) *GroupTypeUpsertOne {
	return u.Update(func(s *GroupTypeUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *GroupTypeUpsertOne) UpdateUpdatedAt() *GroupTypeUpsertOne {
	return u.Update(func(s *GroupTypeUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetStatus sets the "status" field.
func (u *GroupTypeUpsertOne) SetStatus(v bool) *GroupTypeUpsertOne {
	return u.Update(func(s *GroupTypeUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *GroupTypeUpsertOne) UpdateStatus() *GroupTypeUpsertOne {
	return u.Update(func(s *GroupTypeUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *GroupTypeUpsertOne) ClearStatus() *GroupTypeUpsertOne {
	return u.Update(func(s *GroupTypeUpsert) {
		s.ClearStatus()
	})
}

// SetSort sets the "sort" field.
func (u *GroupTypeUpsertOne) SetSort(v uint32) *GroupTypeUpsertOne {
	return u.Update(func(s *GroupTypeUpsert) {
		s.SetSort(v)
	})
}

// AddSort adds v to the "sort" field.
func (u *GroupTypeUpsertOne) AddSort(v uint32) *GroupTypeUpsertOne {
	return u.Update(func(s *GroupTypeUpsert) {
		s.AddSort(v)
	})
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *GroupTypeUpsertOne) UpdateSort() *GroupTypeUpsertOne {
	return u.Update(func(s *GroupTypeUpsert) {
		s.UpdateSort()
	})
}

// SetTenantID sets the "tenant_id" field.
func (u *GroupTypeUpsertOne) SetTenantID(v string) *GroupTypeUpsertOne {
	return u.Update(func(s *GroupTypeUpsert) {
		s.SetTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *GroupTypeUpsertOne) UpdateTenantID() *GroupTypeUpsertOne {
	return u.Update(func(s *GroupTypeUpsert) {
		s.UpdateTenantID()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *GroupTypeUpsertOne) SetDeletedAt(v time.Time) *GroupTypeUpsertOne {
	return u.Update(func(s *GroupTypeUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *GroupTypeUpsertOne) UpdateDeletedAt() *GroupTypeUpsertOne {
	return u.Update(func(s *GroupTypeUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *GroupTypeUpsertOne) ClearDeletedAt() *GroupTypeUpsertOne {
	return u.Update(func(s *GroupTypeUpsert) {
		s.ClearDeletedAt()
	})
}

// SetName sets the "name" field.
func (u *GroupTypeUpsertOne) SetName(v string) *GroupTypeUpsertOne {
	return u.Update(func(s *GroupTypeUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *GroupTypeUpsertOne) UpdateName() *GroupTypeUpsertOne {
	return u.Update(func(s *GroupTypeUpsert) {
		s.UpdateName()
	})
}

// SetCode sets the "code" field.
func (u *GroupTypeUpsertOne) SetCode(v string) *GroupTypeUpsertOne {
	return u.Update(func(s *GroupTypeUpsert) {
		s.SetCode(v)
	})
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *GroupTypeUpsertOne) UpdateCode() *GroupTypeUpsertOne {
	return u.Update(func(s *GroupTypeUpsert) {
		s.UpdateCode()
	})
}

// SetRemark sets the "remark" field.
func (u *GroupTypeUpsertOne) SetRemark(v string) *GroupTypeUpsertOne {
	return u.Update(func(s *GroupTypeUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *GroupTypeUpsertOne) UpdateRemark() *GroupTypeUpsertOne {
	return u.Update(func(s *GroupTypeUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *GroupTypeUpsertOne) ClearRemark() *GroupTypeUpsertOne {
	return u.Update(func(s *GroupTypeUpsert) {
		s.ClearRemark()
	})
}

// Exec executes the query.
func (u *GroupTypeUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for GroupTypeCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *GroupTypeUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *GroupTypeUpsertOne) ID(ctx context.Context) (id string, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: GroupTypeUpsertOne.ID is not supported by MySQL driver. Use GroupTypeUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *GroupTypeUpsertOne) IDX(ctx context.Context) string {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// GroupTypeCreateBulk is the builder for creating many GroupType entities in bulk.
type GroupTypeCreateBulk struct {
	config
	err      error
	builders []*GroupTypeCreate
	conflict []sql.ConflictOption
}

// Save creates the GroupType entities in the database.
func (gtcb *GroupTypeCreateBulk) Save(ctx context.Context) ([]*GroupType, error) {
	if gtcb.err != nil {
		return nil, gtcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(gtcb.builders))
	nodes := make([]*GroupType, len(gtcb.builders))
	mutators := make([]Mutator, len(gtcb.builders))
	for i := range gtcb.builders {
		func(i int, root context.Context) {
			builder := gtcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*GroupTypeMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, gtcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = gtcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, gtcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, gtcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (gtcb *GroupTypeCreateBulk) SaveX(ctx context.Context) []*GroupType {
	v, err := gtcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (gtcb *GroupTypeCreateBulk) Exec(ctx context.Context) error {
	_, err := gtcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (gtcb *GroupTypeCreateBulk) ExecX(ctx context.Context) {
	if err := gtcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.GroupType.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.GroupTypeUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (gtcb *GroupTypeCreateBulk) OnConflict(opts ...sql.ConflictOption) *GroupTypeUpsertBulk {
	gtcb.conflict = opts
	return &GroupTypeUpsertBulk{
		create: gtcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.GroupType.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (gtcb *GroupTypeCreateBulk) OnConflictColumns(columns ...string) *GroupTypeUpsertBulk {
	gtcb.conflict = append(gtcb.conflict, sql.ConflictColumns(columns...))
	return &GroupTypeUpsertBulk{
		create: gtcb,
	}
}

// GroupTypeUpsertBulk is the builder for "upsert"-ing
// a bulk of GroupType nodes.
type GroupTypeUpsertBulk struct {
	create *GroupTypeCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.GroupType.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(grouptype.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *GroupTypeUpsertBulk) UpdateNewValues() *GroupTypeUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(grouptype.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(grouptype.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.GroupType.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *GroupTypeUpsertBulk) Ignore() *GroupTypeUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *GroupTypeUpsertBulk) DoNothing() *GroupTypeUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the GroupTypeCreateBulk.OnConflict
// documentation for more info.
func (u *GroupTypeUpsertBulk) Update(set func(*GroupTypeUpsert)) *GroupTypeUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&GroupTypeUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *GroupTypeUpsertBulk) SetUpdatedAt(v time.Time) *GroupTypeUpsertBulk {
	return u.Update(func(s *GroupTypeUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *GroupTypeUpsertBulk) UpdateUpdatedAt() *GroupTypeUpsertBulk {
	return u.Update(func(s *GroupTypeUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetStatus sets the "status" field.
func (u *GroupTypeUpsertBulk) SetStatus(v bool) *GroupTypeUpsertBulk {
	return u.Update(func(s *GroupTypeUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *GroupTypeUpsertBulk) UpdateStatus() *GroupTypeUpsertBulk {
	return u.Update(func(s *GroupTypeUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *GroupTypeUpsertBulk) ClearStatus() *GroupTypeUpsertBulk {
	return u.Update(func(s *GroupTypeUpsert) {
		s.ClearStatus()
	})
}

// SetSort sets the "sort" field.
func (u *GroupTypeUpsertBulk) SetSort(v uint32) *GroupTypeUpsertBulk {
	return u.Update(func(s *GroupTypeUpsert) {
		s.SetSort(v)
	})
}

// AddSort adds v to the "sort" field.
func (u *GroupTypeUpsertBulk) AddSort(v uint32) *GroupTypeUpsertBulk {
	return u.Update(func(s *GroupTypeUpsert) {
		s.AddSort(v)
	})
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *GroupTypeUpsertBulk) UpdateSort() *GroupTypeUpsertBulk {
	return u.Update(func(s *GroupTypeUpsert) {
		s.UpdateSort()
	})
}

// SetTenantID sets the "tenant_id" field.
func (u *GroupTypeUpsertBulk) SetTenantID(v string) *GroupTypeUpsertBulk {
	return u.Update(func(s *GroupTypeUpsert) {
		s.SetTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *GroupTypeUpsertBulk) UpdateTenantID() *GroupTypeUpsertBulk {
	return u.Update(func(s *GroupTypeUpsert) {
		s.UpdateTenantID()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *GroupTypeUpsertBulk) SetDeletedAt(v time.Time) *GroupTypeUpsertBulk {
	return u.Update(func(s *GroupTypeUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *GroupTypeUpsertBulk) UpdateDeletedAt() *GroupTypeUpsertBulk {
	return u.Update(func(s *GroupTypeUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *GroupTypeUpsertBulk) ClearDeletedAt() *GroupTypeUpsertBulk {
	return u.Update(func(s *GroupTypeUpsert) {
		s.ClearDeletedAt()
	})
}

// SetName sets the "name" field.
func (u *GroupTypeUpsertBulk) SetName(v string) *GroupTypeUpsertBulk {
	return u.Update(func(s *GroupTypeUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *GroupTypeUpsertBulk) UpdateName() *GroupTypeUpsertBulk {
	return u.Update(func(s *GroupTypeUpsert) {
		s.UpdateName()
	})
}

// SetCode sets the "code" field.
func (u *GroupTypeUpsertBulk) SetCode(v string) *GroupTypeUpsertBulk {
	return u.Update(func(s *GroupTypeUpsert) {
		s.SetCode(v)
	})
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *GroupTypeUpsertBulk) UpdateCode() *GroupTypeUpsertBulk {
	return u.Update(func(s *GroupTypeUpsert) {
		s.UpdateCode()
	})
}

// SetRemark sets the "remark" field.
func (u *GroupTypeUpsertBulk) SetRemark(v string) *GroupTypeUpsertBulk {
	return u.Update(func(s *GroupTypeUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *GroupTypeUpsertBulk) UpdateRemark() *GroupTypeUpsertBulk {
	return u.Update(func(s *GroupTypeUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *GroupTypeUpsertBulk) ClearRemark() *GroupTypeUpsertBulk {
	return u.Update(func(s *GroupTypeUpsert) {
		s.ClearRemark()
	})
}

// Exec executes the query.
func (u *GroupTypeUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the GroupTypeCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for GroupTypeCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *GroupTypeUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
