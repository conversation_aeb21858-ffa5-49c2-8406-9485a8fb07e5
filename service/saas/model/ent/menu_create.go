// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/button"
	"phoenix/service/saas/model/ent/menu"
	"phoenix/service/saas/model/ent/role"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// MenuCreate is the builder for creating a Menu entity.
type MenuCreate struct {
	config
	mutation *MenuMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (mc *MenuCreate) SetCreatedAt(t time.Time) *MenuCreate {
	mc.mutation.SetCreatedAt(t)
	return mc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (mc *MenuCreate) SetNillableCreatedAt(t *time.Time) *MenuCreate {
	if t != nil {
		mc.SetCreatedAt(*t)
	}
	return mc
}

// SetUpdatedAt sets the "updated_at" field.
func (mc *MenuCreate) SetUpdatedAt(t time.Time) *MenuCreate {
	mc.mutation.SetUpdatedAt(t)
	return mc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (mc *MenuCreate) SetNillableUpdatedAt(t *time.Time) *MenuCreate {
	if t != nil {
		mc.SetUpdatedAt(*t)
	}
	return mc
}

// SetSort sets the "sort" field.
func (mc *MenuCreate) SetSort(u uint32) *MenuCreate {
	mc.mutation.SetSort(u)
	return mc
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (mc *MenuCreate) SetNillableSort(u *uint32) *MenuCreate {
	if u != nil {
		mc.SetSort(*u)
	}
	return mc
}

// SetDeletedAt sets the "deleted_at" field.
func (mc *MenuCreate) SetDeletedAt(t time.Time) *MenuCreate {
	mc.mutation.SetDeletedAt(t)
	return mc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (mc *MenuCreate) SetNillableDeletedAt(t *time.Time) *MenuCreate {
	if t != nil {
		mc.SetDeletedAt(*t)
	}
	return mc
}

// SetName sets the "name" field.
func (mc *MenuCreate) SetName(s string) *MenuCreate {
	mc.mutation.SetName(s)
	return mc
}

// SetTitle sets the "title" field.
func (mc *MenuCreate) SetTitle(s string) *MenuCreate {
	mc.mutation.SetTitle(s)
	return mc
}

// SetIcon sets the "icon" field.
func (mc *MenuCreate) SetIcon(s string) *MenuCreate {
	mc.mutation.SetIcon(s)
	return mc
}

// SetParentID sets the "parent_id" field.
func (mc *MenuCreate) SetParentID(s string) *MenuCreate {
	mc.mutation.SetParentID(s)
	return mc
}

// SetNillableParentID sets the "parent_id" field if the given value is not nil.
func (mc *MenuCreate) SetNillableParentID(s *string) *MenuCreate {
	if s != nil {
		mc.SetParentID(*s)
	}
	return mc
}

// SetMenuType sets the "menu_type" field.
func (mc *MenuCreate) SetMenuType(u uint32) *MenuCreate {
	mc.mutation.SetMenuType(u)
	return mc
}

// SetURL sets the "url" field.
func (mc *MenuCreate) SetURL(s string) *MenuCreate {
	mc.mutation.SetURL(s)
	return mc
}

// SetNillableURL sets the "url" field if the given value is not nil.
func (mc *MenuCreate) SetNillableURL(s *string) *MenuCreate {
	if s != nil {
		mc.SetURL(*s)
	}
	return mc
}

// SetRedirect sets the "redirect" field.
func (mc *MenuCreate) SetRedirect(s string) *MenuCreate {
	mc.mutation.SetRedirect(s)
	return mc
}

// SetNillableRedirect sets the "redirect" field if the given value is not nil.
func (mc *MenuCreate) SetNillableRedirect(s *string) *MenuCreate {
	if s != nil {
		mc.SetRedirect(*s)
	}
	return mc
}

// SetComponent sets the "component" field.
func (mc *MenuCreate) SetComponent(s string) *MenuCreate {
	mc.mutation.SetComponent(s)
	return mc
}

// SetNillableComponent sets the "component" field if the given value is not nil.
func (mc *MenuCreate) SetNillableComponent(s *string) *MenuCreate {
	if s != nil {
		mc.SetComponent(*s)
	}
	return mc
}

// SetIsActive sets the "is_active" field.
func (mc *MenuCreate) SetIsActive(b bool) *MenuCreate {
	mc.mutation.SetIsActive(b)
	return mc
}

// SetNillableIsActive sets the "is_active" field if the given value is not nil.
func (mc *MenuCreate) SetNillableIsActive(b *bool) *MenuCreate {
	if b != nil {
		mc.SetIsActive(*b)
	}
	return mc
}

// SetHidden sets the "hidden" field.
func (mc *MenuCreate) SetHidden(b bool) *MenuCreate {
	mc.mutation.SetHidden(b)
	return mc
}

// SetNillableHidden sets the "hidden" field if the given value is not nil.
func (mc *MenuCreate) SetNillableHidden(b *bool) *MenuCreate {
	if b != nil {
		mc.SetHidden(*b)
	}
	return mc
}

// SetHiddenInTab sets the "hidden_in_tab" field.
func (mc *MenuCreate) SetHiddenInTab(b bool) *MenuCreate {
	mc.mutation.SetHiddenInTab(b)
	return mc
}

// SetNillableHiddenInTab sets the "hidden_in_tab" field if the given value is not nil.
func (mc *MenuCreate) SetNillableHiddenInTab(b *bool) *MenuCreate {
	if b != nil {
		mc.SetHiddenInTab(*b)
	}
	return mc
}

// SetFixed sets the "fixed" field.
func (mc *MenuCreate) SetFixed(b bool) *MenuCreate {
	mc.mutation.SetFixed(b)
	return mc
}

// SetNillableFixed sets the "fixed" field if the given value is not nil.
func (mc *MenuCreate) SetNillableFixed(b *bool) *MenuCreate {
	if b != nil {
		mc.SetFixed(*b)
	}
	return mc
}

// SetRemark sets the "remark" field.
func (mc *MenuCreate) SetRemark(s string) *MenuCreate {
	mc.mutation.SetRemark(s)
	return mc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (mc *MenuCreate) SetNillableRemark(s *string) *MenuCreate {
	if s != nil {
		mc.SetRemark(*s)
	}
	return mc
}

// SetMeta sets the "meta" field.
func (mc *MenuCreate) SetMeta(s string) *MenuCreate {
	mc.mutation.SetMeta(s)
	return mc
}

// SetNillableMeta sets the "meta" field if the given value is not nil.
func (mc *MenuCreate) SetNillableMeta(s *string) *MenuCreate {
	if s != nil {
		mc.SetMeta(*s)
	}
	return mc
}

// SetIsFullPage sets the "is_full_page" field.
func (mc *MenuCreate) SetIsFullPage(b bool) *MenuCreate {
	mc.mutation.SetIsFullPage(b)
	return mc
}

// SetNillableIsFullPage sets the "is_full_page" field if the given value is not nil.
func (mc *MenuCreate) SetNillableIsFullPage(b *bool) *MenuCreate {
	if b != nil {
		mc.SetIsFullPage(*b)
	}
	return mc
}

// SetID sets the "id" field.
func (mc *MenuCreate) SetID(s string) *MenuCreate {
	mc.mutation.SetID(s)
	return mc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (mc *MenuCreate) SetNillableID(s *string) *MenuCreate {
	if s != nil {
		mc.SetID(*s)
	}
	return mc
}

// AddRoleIDs adds the "roles" edge to the Role entity by IDs.
func (mc *MenuCreate) AddRoleIDs(ids ...string) *MenuCreate {
	mc.mutation.AddRoleIDs(ids...)
	return mc
}

// AddRoles adds the "roles" edges to the Role entity.
func (mc *MenuCreate) AddRoles(r ...*Role) *MenuCreate {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return mc.AddRoleIDs(ids...)
}

// AddButtonIDs adds the "buttons" edge to the Button entity by IDs.
func (mc *MenuCreate) AddButtonIDs(ids ...string) *MenuCreate {
	mc.mutation.AddButtonIDs(ids...)
	return mc
}

// AddButtons adds the "buttons" edges to the Button entity.
func (mc *MenuCreate) AddButtons(b ...*Button) *MenuCreate {
	ids := make([]string, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return mc.AddButtonIDs(ids...)
}

// SetParent sets the "parent" edge to the Menu entity.
func (mc *MenuCreate) SetParent(m *Menu) *MenuCreate {
	return mc.SetParentID(m.ID)
}

// AddChildIDs adds the "children" edge to the Menu entity by IDs.
func (mc *MenuCreate) AddChildIDs(ids ...string) *MenuCreate {
	mc.mutation.AddChildIDs(ids...)
	return mc
}

// AddChildren adds the "children" edges to the Menu entity.
func (mc *MenuCreate) AddChildren(m ...*Menu) *MenuCreate {
	ids := make([]string, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return mc.AddChildIDs(ids...)
}

// Mutation returns the MenuMutation object of the builder.
func (mc *MenuCreate) Mutation() *MenuMutation {
	return mc.mutation
}

// Save creates the Menu in the database.
func (mc *MenuCreate) Save(ctx context.Context) (*Menu, error) {
	if err := mc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, mc.sqlSave, mc.mutation, mc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (mc *MenuCreate) SaveX(ctx context.Context) *Menu {
	v, err := mc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (mc *MenuCreate) Exec(ctx context.Context) error {
	_, err := mc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (mc *MenuCreate) ExecX(ctx context.Context) {
	if err := mc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (mc *MenuCreate) defaults() error {
	if _, ok := mc.mutation.CreatedAt(); !ok {
		if menu.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized menu.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := menu.DefaultCreatedAt()
		mc.mutation.SetCreatedAt(v)
	}
	if _, ok := mc.mutation.UpdatedAt(); !ok {
		if menu.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized menu.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := menu.DefaultUpdatedAt()
		mc.mutation.SetUpdatedAt(v)
	}
	if _, ok := mc.mutation.Sort(); !ok {
		v := menu.DefaultSort
		mc.mutation.SetSort(v)
	}
	if _, ok := mc.mutation.URL(); !ok {
		v := menu.DefaultURL
		mc.mutation.SetURL(v)
	}
	if _, ok := mc.mutation.Redirect(); !ok {
		v := menu.DefaultRedirect
		mc.mutation.SetRedirect(v)
	}
	if _, ok := mc.mutation.Component(); !ok {
		v := menu.DefaultComponent
		mc.mutation.SetComponent(v)
	}
	if _, ok := mc.mutation.IsActive(); !ok {
		v := menu.DefaultIsActive
		mc.mutation.SetIsActive(v)
	}
	if _, ok := mc.mutation.Hidden(); !ok {
		v := menu.DefaultHidden
		mc.mutation.SetHidden(v)
	}
	if _, ok := mc.mutation.HiddenInTab(); !ok {
		v := menu.DefaultHiddenInTab
		mc.mutation.SetHiddenInTab(v)
	}
	if _, ok := mc.mutation.Fixed(); !ok {
		v := menu.DefaultFixed
		mc.mutation.SetFixed(v)
	}
	if _, ok := mc.mutation.Remark(); !ok {
		v := menu.DefaultRemark
		mc.mutation.SetRemark(v)
	}
	if _, ok := mc.mutation.Meta(); !ok {
		v := menu.DefaultMeta
		mc.mutation.SetMeta(v)
	}
	if _, ok := mc.mutation.IsFullPage(); !ok {
		v := menu.DefaultIsFullPage
		mc.mutation.SetIsFullPage(v)
	}
	if _, ok := mc.mutation.ID(); !ok {
		if menu.DefaultID == nil {
			return fmt.Errorf("ent: uninitialized menu.DefaultID (forgotten import ent/runtime?)")
		}
		v := menu.DefaultID()
		mc.mutation.SetID(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (mc *MenuCreate) check() error {
	if _, ok := mc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Menu.created_at"`)}
	}
	if _, ok := mc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Menu.updated_at"`)}
	}
	if _, ok := mc.mutation.Sort(); !ok {
		return &ValidationError{Name: "sort", err: errors.New(`ent: missing required field "Menu.sort"`)}
	}
	if _, ok := mc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Menu.name"`)}
	}
	if _, ok := mc.mutation.Title(); !ok {
		return &ValidationError{Name: "title", err: errors.New(`ent: missing required field "Menu.title"`)}
	}
	if _, ok := mc.mutation.Icon(); !ok {
		return &ValidationError{Name: "icon", err: errors.New(`ent: missing required field "Menu.icon"`)}
	}
	if _, ok := mc.mutation.MenuType(); !ok {
		return &ValidationError{Name: "menu_type", err: errors.New(`ent: missing required field "Menu.menu_type"`)}
	}
	return nil
}

func (mc *MenuCreate) sqlSave(ctx context.Context) (*Menu, error) {
	if err := mc.check(); err != nil {
		return nil, err
	}
	_node, _spec := mc.createSpec()
	if err := sqlgraph.CreateNode(ctx, mc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected Menu.ID type: %T", _spec.ID.Value)
		}
	}
	mc.mutation.id = &_node.ID
	mc.mutation.done = true
	return _node, nil
}

func (mc *MenuCreate) createSpec() (*Menu, *sqlgraph.CreateSpec) {
	var (
		_node = &Menu{config: mc.config}
		_spec = sqlgraph.NewCreateSpec(menu.Table, sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString))
	)
	_spec.OnConflict = mc.conflict
	if id, ok := mc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := mc.mutation.CreatedAt(); ok {
		_spec.SetField(menu.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := mc.mutation.UpdatedAt(); ok {
		_spec.SetField(menu.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := mc.mutation.Sort(); ok {
		_spec.SetField(menu.FieldSort, field.TypeUint32, value)
		_node.Sort = value
	}
	if value, ok := mc.mutation.DeletedAt(); ok {
		_spec.SetField(menu.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := mc.mutation.Name(); ok {
		_spec.SetField(menu.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := mc.mutation.Title(); ok {
		_spec.SetField(menu.FieldTitle, field.TypeString, value)
		_node.Title = value
	}
	if value, ok := mc.mutation.Icon(); ok {
		_spec.SetField(menu.FieldIcon, field.TypeString, value)
		_node.Icon = value
	}
	if value, ok := mc.mutation.MenuType(); ok {
		_spec.SetField(menu.FieldMenuType, field.TypeUint32, value)
		_node.MenuType = value
	}
	if value, ok := mc.mutation.URL(); ok {
		_spec.SetField(menu.FieldURL, field.TypeString, value)
		_node.URL = value
	}
	if value, ok := mc.mutation.Redirect(); ok {
		_spec.SetField(menu.FieldRedirect, field.TypeString, value)
		_node.Redirect = value
	}
	if value, ok := mc.mutation.Component(); ok {
		_spec.SetField(menu.FieldComponent, field.TypeString, value)
		_node.Component = value
	}
	if value, ok := mc.mutation.IsActive(); ok {
		_spec.SetField(menu.FieldIsActive, field.TypeBool, value)
		_node.IsActive = value
	}
	if value, ok := mc.mutation.Hidden(); ok {
		_spec.SetField(menu.FieldHidden, field.TypeBool, value)
		_node.Hidden = value
	}
	if value, ok := mc.mutation.HiddenInTab(); ok {
		_spec.SetField(menu.FieldHiddenInTab, field.TypeBool, value)
		_node.HiddenInTab = value
	}
	if value, ok := mc.mutation.Fixed(); ok {
		_spec.SetField(menu.FieldFixed, field.TypeBool, value)
		_node.Fixed = value
	}
	if value, ok := mc.mutation.Remark(); ok {
		_spec.SetField(menu.FieldRemark, field.TypeString, value)
		_node.Remark = value
	}
	if value, ok := mc.mutation.Meta(); ok {
		_spec.SetField(menu.FieldMeta, field.TypeString, value)
		_node.Meta = value
	}
	if value, ok := mc.mutation.IsFullPage(); ok {
		_spec.SetField(menu.FieldIsFullPage, field.TypeBool, value)
		_node.IsFullPage = value
	}
	if nodes := mc.mutation.RolesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   menu.RolesTable,
			Columns: menu.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := mc.mutation.ButtonsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   menu.ButtonsTable,
			Columns: []string{menu.ButtonsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(button.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := mc.mutation.ParentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   menu.ParentTable,
			Columns: []string{menu.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.ParentID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := mc.mutation.ChildrenIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   menu.ChildrenTable,
			Columns: []string{menu.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Menu.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.MenuUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (mc *MenuCreate) OnConflict(opts ...sql.ConflictOption) *MenuUpsertOne {
	mc.conflict = opts
	return &MenuUpsertOne{
		create: mc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Menu.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (mc *MenuCreate) OnConflictColumns(columns ...string) *MenuUpsertOne {
	mc.conflict = append(mc.conflict, sql.ConflictColumns(columns...))
	return &MenuUpsertOne{
		create: mc,
	}
}

type (
	// MenuUpsertOne is the builder for "upsert"-ing
	//  one Menu node.
	MenuUpsertOne struct {
		create *MenuCreate
	}

	// MenuUpsert is the "OnConflict" setter.
	MenuUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *MenuUpsert) SetUpdatedAt(v time.Time) *MenuUpsert {
	u.Set(menu.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *MenuUpsert) UpdateUpdatedAt() *MenuUpsert {
	u.SetExcluded(menu.FieldUpdatedAt)
	return u
}

// SetSort sets the "sort" field.
func (u *MenuUpsert) SetSort(v uint32) *MenuUpsert {
	u.Set(menu.FieldSort, v)
	return u
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *MenuUpsert) UpdateSort() *MenuUpsert {
	u.SetExcluded(menu.FieldSort)
	return u
}

// AddSort adds v to the "sort" field.
func (u *MenuUpsert) AddSort(v uint32) *MenuUpsert {
	u.Add(menu.FieldSort, v)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *MenuUpsert) SetDeletedAt(v time.Time) *MenuUpsert {
	u.Set(menu.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *MenuUpsert) UpdateDeletedAt() *MenuUpsert {
	u.SetExcluded(menu.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *MenuUpsert) ClearDeletedAt() *MenuUpsert {
	u.SetNull(menu.FieldDeletedAt)
	return u
}

// SetName sets the "name" field.
func (u *MenuUpsert) SetName(v string) *MenuUpsert {
	u.Set(menu.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *MenuUpsert) UpdateName() *MenuUpsert {
	u.SetExcluded(menu.FieldName)
	return u
}

// SetTitle sets the "title" field.
func (u *MenuUpsert) SetTitle(v string) *MenuUpsert {
	u.Set(menu.FieldTitle, v)
	return u
}

// UpdateTitle sets the "title" field to the value that was provided on create.
func (u *MenuUpsert) UpdateTitle() *MenuUpsert {
	u.SetExcluded(menu.FieldTitle)
	return u
}

// SetIcon sets the "icon" field.
func (u *MenuUpsert) SetIcon(v string) *MenuUpsert {
	u.Set(menu.FieldIcon, v)
	return u
}

// UpdateIcon sets the "icon" field to the value that was provided on create.
func (u *MenuUpsert) UpdateIcon() *MenuUpsert {
	u.SetExcluded(menu.FieldIcon)
	return u
}

// SetParentID sets the "parent_id" field.
func (u *MenuUpsert) SetParentID(v string) *MenuUpsert {
	u.Set(menu.FieldParentID, v)
	return u
}

// UpdateParentID sets the "parent_id" field to the value that was provided on create.
func (u *MenuUpsert) UpdateParentID() *MenuUpsert {
	u.SetExcluded(menu.FieldParentID)
	return u
}

// ClearParentID clears the value of the "parent_id" field.
func (u *MenuUpsert) ClearParentID() *MenuUpsert {
	u.SetNull(menu.FieldParentID)
	return u
}

// SetMenuType sets the "menu_type" field.
func (u *MenuUpsert) SetMenuType(v uint32) *MenuUpsert {
	u.Set(menu.FieldMenuType, v)
	return u
}

// UpdateMenuType sets the "menu_type" field to the value that was provided on create.
func (u *MenuUpsert) UpdateMenuType() *MenuUpsert {
	u.SetExcluded(menu.FieldMenuType)
	return u
}

// AddMenuType adds v to the "menu_type" field.
func (u *MenuUpsert) AddMenuType(v uint32) *MenuUpsert {
	u.Add(menu.FieldMenuType, v)
	return u
}

// SetURL sets the "url" field.
func (u *MenuUpsert) SetURL(v string) *MenuUpsert {
	u.Set(menu.FieldURL, v)
	return u
}

// UpdateURL sets the "url" field to the value that was provided on create.
func (u *MenuUpsert) UpdateURL() *MenuUpsert {
	u.SetExcluded(menu.FieldURL)
	return u
}

// ClearURL clears the value of the "url" field.
func (u *MenuUpsert) ClearURL() *MenuUpsert {
	u.SetNull(menu.FieldURL)
	return u
}

// SetRedirect sets the "redirect" field.
func (u *MenuUpsert) SetRedirect(v string) *MenuUpsert {
	u.Set(menu.FieldRedirect, v)
	return u
}

// UpdateRedirect sets the "redirect" field to the value that was provided on create.
func (u *MenuUpsert) UpdateRedirect() *MenuUpsert {
	u.SetExcluded(menu.FieldRedirect)
	return u
}

// ClearRedirect clears the value of the "redirect" field.
func (u *MenuUpsert) ClearRedirect() *MenuUpsert {
	u.SetNull(menu.FieldRedirect)
	return u
}

// SetComponent sets the "component" field.
func (u *MenuUpsert) SetComponent(v string) *MenuUpsert {
	u.Set(menu.FieldComponent, v)
	return u
}

// UpdateComponent sets the "component" field to the value that was provided on create.
func (u *MenuUpsert) UpdateComponent() *MenuUpsert {
	u.SetExcluded(menu.FieldComponent)
	return u
}

// ClearComponent clears the value of the "component" field.
func (u *MenuUpsert) ClearComponent() *MenuUpsert {
	u.SetNull(menu.FieldComponent)
	return u
}

// SetIsActive sets the "is_active" field.
func (u *MenuUpsert) SetIsActive(v bool) *MenuUpsert {
	u.Set(menu.FieldIsActive, v)
	return u
}

// UpdateIsActive sets the "is_active" field to the value that was provided on create.
func (u *MenuUpsert) UpdateIsActive() *MenuUpsert {
	u.SetExcluded(menu.FieldIsActive)
	return u
}

// ClearIsActive clears the value of the "is_active" field.
func (u *MenuUpsert) ClearIsActive() *MenuUpsert {
	u.SetNull(menu.FieldIsActive)
	return u
}

// SetHidden sets the "hidden" field.
func (u *MenuUpsert) SetHidden(v bool) *MenuUpsert {
	u.Set(menu.FieldHidden, v)
	return u
}

// UpdateHidden sets the "hidden" field to the value that was provided on create.
func (u *MenuUpsert) UpdateHidden() *MenuUpsert {
	u.SetExcluded(menu.FieldHidden)
	return u
}

// ClearHidden clears the value of the "hidden" field.
func (u *MenuUpsert) ClearHidden() *MenuUpsert {
	u.SetNull(menu.FieldHidden)
	return u
}

// SetHiddenInTab sets the "hidden_in_tab" field.
func (u *MenuUpsert) SetHiddenInTab(v bool) *MenuUpsert {
	u.Set(menu.FieldHiddenInTab, v)
	return u
}

// UpdateHiddenInTab sets the "hidden_in_tab" field to the value that was provided on create.
func (u *MenuUpsert) UpdateHiddenInTab() *MenuUpsert {
	u.SetExcluded(menu.FieldHiddenInTab)
	return u
}

// ClearHiddenInTab clears the value of the "hidden_in_tab" field.
func (u *MenuUpsert) ClearHiddenInTab() *MenuUpsert {
	u.SetNull(menu.FieldHiddenInTab)
	return u
}

// SetFixed sets the "fixed" field.
func (u *MenuUpsert) SetFixed(v bool) *MenuUpsert {
	u.Set(menu.FieldFixed, v)
	return u
}

// UpdateFixed sets the "fixed" field to the value that was provided on create.
func (u *MenuUpsert) UpdateFixed() *MenuUpsert {
	u.SetExcluded(menu.FieldFixed)
	return u
}

// ClearFixed clears the value of the "fixed" field.
func (u *MenuUpsert) ClearFixed() *MenuUpsert {
	u.SetNull(menu.FieldFixed)
	return u
}

// SetRemark sets the "remark" field.
func (u *MenuUpsert) SetRemark(v string) *MenuUpsert {
	u.Set(menu.FieldRemark, v)
	return u
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *MenuUpsert) UpdateRemark() *MenuUpsert {
	u.SetExcluded(menu.FieldRemark)
	return u
}

// ClearRemark clears the value of the "remark" field.
func (u *MenuUpsert) ClearRemark() *MenuUpsert {
	u.SetNull(menu.FieldRemark)
	return u
}

// SetMeta sets the "meta" field.
func (u *MenuUpsert) SetMeta(v string) *MenuUpsert {
	u.Set(menu.FieldMeta, v)
	return u
}

// UpdateMeta sets the "meta" field to the value that was provided on create.
func (u *MenuUpsert) UpdateMeta() *MenuUpsert {
	u.SetExcluded(menu.FieldMeta)
	return u
}

// ClearMeta clears the value of the "meta" field.
func (u *MenuUpsert) ClearMeta() *MenuUpsert {
	u.SetNull(menu.FieldMeta)
	return u
}

// SetIsFullPage sets the "is_full_page" field.
func (u *MenuUpsert) SetIsFullPage(v bool) *MenuUpsert {
	u.Set(menu.FieldIsFullPage, v)
	return u
}

// UpdateIsFullPage sets the "is_full_page" field to the value that was provided on create.
func (u *MenuUpsert) UpdateIsFullPage() *MenuUpsert {
	u.SetExcluded(menu.FieldIsFullPage)
	return u
}

// ClearIsFullPage clears the value of the "is_full_page" field.
func (u *MenuUpsert) ClearIsFullPage() *MenuUpsert {
	u.SetNull(menu.FieldIsFullPage)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Menu.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(menu.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *MenuUpsertOne) UpdateNewValues() *MenuUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(menu.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(menu.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Menu.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *MenuUpsertOne) Ignore() *MenuUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *MenuUpsertOne) DoNothing() *MenuUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the MenuCreate.OnConflict
// documentation for more info.
func (u *MenuUpsertOne) Update(set func(*MenuUpsert)) *MenuUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&MenuUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *MenuUpsertOne) SetUpdatedAt(v time.Time) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateUpdatedAt() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetSort sets the "sort" field.
func (u *MenuUpsertOne) SetSort(v uint32) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetSort(v)
	})
}

// AddSort adds v to the "sort" field.
func (u *MenuUpsertOne) AddSort(v uint32) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.AddSort(v)
	})
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateSort() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateSort()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *MenuUpsertOne) SetDeletedAt(v time.Time) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateDeletedAt() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *MenuUpsertOne) ClearDeletedAt() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearDeletedAt()
	})
}

// SetName sets the "name" field.
func (u *MenuUpsertOne) SetName(v string) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateName() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateName()
	})
}

// SetTitle sets the "title" field.
func (u *MenuUpsertOne) SetTitle(v string) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetTitle(v)
	})
}

// UpdateTitle sets the "title" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateTitle() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateTitle()
	})
}

// SetIcon sets the "icon" field.
func (u *MenuUpsertOne) SetIcon(v string) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetIcon(v)
	})
}

// UpdateIcon sets the "icon" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateIcon() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateIcon()
	})
}

// SetParentID sets the "parent_id" field.
func (u *MenuUpsertOne) SetParentID(v string) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetParentID(v)
	})
}

// UpdateParentID sets the "parent_id" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateParentID() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateParentID()
	})
}

// ClearParentID clears the value of the "parent_id" field.
func (u *MenuUpsertOne) ClearParentID() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearParentID()
	})
}

// SetMenuType sets the "menu_type" field.
func (u *MenuUpsertOne) SetMenuType(v uint32) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetMenuType(v)
	})
}

// AddMenuType adds v to the "menu_type" field.
func (u *MenuUpsertOne) AddMenuType(v uint32) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.AddMenuType(v)
	})
}

// UpdateMenuType sets the "menu_type" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateMenuType() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateMenuType()
	})
}

// SetURL sets the "url" field.
func (u *MenuUpsertOne) SetURL(v string) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetURL(v)
	})
}

// UpdateURL sets the "url" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateURL() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateURL()
	})
}

// ClearURL clears the value of the "url" field.
func (u *MenuUpsertOne) ClearURL() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearURL()
	})
}

// SetRedirect sets the "redirect" field.
func (u *MenuUpsertOne) SetRedirect(v string) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetRedirect(v)
	})
}

// UpdateRedirect sets the "redirect" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateRedirect() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateRedirect()
	})
}

// ClearRedirect clears the value of the "redirect" field.
func (u *MenuUpsertOne) ClearRedirect() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearRedirect()
	})
}

// SetComponent sets the "component" field.
func (u *MenuUpsertOne) SetComponent(v string) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetComponent(v)
	})
}

// UpdateComponent sets the "component" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateComponent() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateComponent()
	})
}

// ClearComponent clears the value of the "component" field.
func (u *MenuUpsertOne) ClearComponent() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearComponent()
	})
}

// SetIsActive sets the "is_active" field.
func (u *MenuUpsertOne) SetIsActive(v bool) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetIsActive(v)
	})
}

// UpdateIsActive sets the "is_active" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateIsActive() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateIsActive()
	})
}

// ClearIsActive clears the value of the "is_active" field.
func (u *MenuUpsertOne) ClearIsActive() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearIsActive()
	})
}

// SetHidden sets the "hidden" field.
func (u *MenuUpsertOne) SetHidden(v bool) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetHidden(v)
	})
}

// UpdateHidden sets the "hidden" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateHidden() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateHidden()
	})
}

// ClearHidden clears the value of the "hidden" field.
func (u *MenuUpsertOne) ClearHidden() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearHidden()
	})
}

// SetHiddenInTab sets the "hidden_in_tab" field.
func (u *MenuUpsertOne) SetHiddenInTab(v bool) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetHiddenInTab(v)
	})
}

// UpdateHiddenInTab sets the "hidden_in_tab" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateHiddenInTab() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateHiddenInTab()
	})
}

// ClearHiddenInTab clears the value of the "hidden_in_tab" field.
func (u *MenuUpsertOne) ClearHiddenInTab() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearHiddenInTab()
	})
}

// SetFixed sets the "fixed" field.
func (u *MenuUpsertOne) SetFixed(v bool) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetFixed(v)
	})
}

// UpdateFixed sets the "fixed" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateFixed() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateFixed()
	})
}

// ClearFixed clears the value of the "fixed" field.
func (u *MenuUpsertOne) ClearFixed() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearFixed()
	})
}

// SetRemark sets the "remark" field.
func (u *MenuUpsertOne) SetRemark(v string) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateRemark() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *MenuUpsertOne) ClearRemark() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearRemark()
	})
}

// SetMeta sets the "meta" field.
func (u *MenuUpsertOne) SetMeta(v string) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetMeta(v)
	})
}

// UpdateMeta sets the "meta" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateMeta() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateMeta()
	})
}

// ClearMeta clears the value of the "meta" field.
func (u *MenuUpsertOne) ClearMeta() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearMeta()
	})
}

// SetIsFullPage sets the "is_full_page" field.
func (u *MenuUpsertOne) SetIsFullPage(v bool) *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.SetIsFullPage(v)
	})
}

// UpdateIsFullPage sets the "is_full_page" field to the value that was provided on create.
func (u *MenuUpsertOne) UpdateIsFullPage() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateIsFullPage()
	})
}

// ClearIsFullPage clears the value of the "is_full_page" field.
func (u *MenuUpsertOne) ClearIsFullPage() *MenuUpsertOne {
	return u.Update(func(s *MenuUpsert) {
		s.ClearIsFullPage()
	})
}

// Exec executes the query.
func (u *MenuUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for MenuCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *MenuUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *MenuUpsertOne) ID(ctx context.Context) (id string, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: MenuUpsertOne.ID is not supported by MySQL driver. Use MenuUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *MenuUpsertOne) IDX(ctx context.Context) string {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// MenuCreateBulk is the builder for creating many Menu entities in bulk.
type MenuCreateBulk struct {
	config
	err      error
	builders []*MenuCreate
	conflict []sql.ConflictOption
}

// Save creates the Menu entities in the database.
func (mcb *MenuCreateBulk) Save(ctx context.Context) ([]*Menu, error) {
	if mcb.err != nil {
		return nil, mcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(mcb.builders))
	nodes := make([]*Menu, len(mcb.builders))
	mutators := make([]Mutator, len(mcb.builders))
	for i := range mcb.builders {
		func(i int, root context.Context) {
			builder := mcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*MenuMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, mcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = mcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, mcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, mcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (mcb *MenuCreateBulk) SaveX(ctx context.Context) []*Menu {
	v, err := mcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (mcb *MenuCreateBulk) Exec(ctx context.Context) error {
	_, err := mcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (mcb *MenuCreateBulk) ExecX(ctx context.Context) {
	if err := mcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Menu.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.MenuUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (mcb *MenuCreateBulk) OnConflict(opts ...sql.ConflictOption) *MenuUpsertBulk {
	mcb.conflict = opts
	return &MenuUpsertBulk{
		create: mcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Menu.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (mcb *MenuCreateBulk) OnConflictColumns(columns ...string) *MenuUpsertBulk {
	mcb.conflict = append(mcb.conflict, sql.ConflictColumns(columns...))
	return &MenuUpsertBulk{
		create: mcb,
	}
}

// MenuUpsertBulk is the builder for "upsert"-ing
// a bulk of Menu nodes.
type MenuUpsertBulk struct {
	create *MenuCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Menu.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(menu.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *MenuUpsertBulk) UpdateNewValues() *MenuUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(menu.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(menu.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Menu.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *MenuUpsertBulk) Ignore() *MenuUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *MenuUpsertBulk) DoNothing() *MenuUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the MenuCreateBulk.OnConflict
// documentation for more info.
func (u *MenuUpsertBulk) Update(set func(*MenuUpsert)) *MenuUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&MenuUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *MenuUpsertBulk) SetUpdatedAt(v time.Time) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateUpdatedAt() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetSort sets the "sort" field.
func (u *MenuUpsertBulk) SetSort(v uint32) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetSort(v)
	})
}

// AddSort adds v to the "sort" field.
func (u *MenuUpsertBulk) AddSort(v uint32) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.AddSort(v)
	})
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateSort() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateSort()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *MenuUpsertBulk) SetDeletedAt(v time.Time) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateDeletedAt() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *MenuUpsertBulk) ClearDeletedAt() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearDeletedAt()
	})
}

// SetName sets the "name" field.
func (u *MenuUpsertBulk) SetName(v string) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateName() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateName()
	})
}

// SetTitle sets the "title" field.
func (u *MenuUpsertBulk) SetTitle(v string) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetTitle(v)
	})
}

// UpdateTitle sets the "title" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateTitle() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateTitle()
	})
}

// SetIcon sets the "icon" field.
func (u *MenuUpsertBulk) SetIcon(v string) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetIcon(v)
	})
}

// UpdateIcon sets the "icon" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateIcon() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateIcon()
	})
}

// SetParentID sets the "parent_id" field.
func (u *MenuUpsertBulk) SetParentID(v string) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetParentID(v)
	})
}

// UpdateParentID sets the "parent_id" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateParentID() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateParentID()
	})
}

// ClearParentID clears the value of the "parent_id" field.
func (u *MenuUpsertBulk) ClearParentID() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearParentID()
	})
}

// SetMenuType sets the "menu_type" field.
func (u *MenuUpsertBulk) SetMenuType(v uint32) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetMenuType(v)
	})
}

// AddMenuType adds v to the "menu_type" field.
func (u *MenuUpsertBulk) AddMenuType(v uint32) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.AddMenuType(v)
	})
}

// UpdateMenuType sets the "menu_type" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateMenuType() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateMenuType()
	})
}

// SetURL sets the "url" field.
func (u *MenuUpsertBulk) SetURL(v string) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetURL(v)
	})
}

// UpdateURL sets the "url" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateURL() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateURL()
	})
}

// ClearURL clears the value of the "url" field.
func (u *MenuUpsertBulk) ClearURL() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearURL()
	})
}

// SetRedirect sets the "redirect" field.
func (u *MenuUpsertBulk) SetRedirect(v string) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetRedirect(v)
	})
}

// UpdateRedirect sets the "redirect" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateRedirect() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateRedirect()
	})
}

// ClearRedirect clears the value of the "redirect" field.
func (u *MenuUpsertBulk) ClearRedirect() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearRedirect()
	})
}

// SetComponent sets the "component" field.
func (u *MenuUpsertBulk) SetComponent(v string) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetComponent(v)
	})
}

// UpdateComponent sets the "component" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateComponent() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateComponent()
	})
}

// ClearComponent clears the value of the "component" field.
func (u *MenuUpsertBulk) ClearComponent() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearComponent()
	})
}

// SetIsActive sets the "is_active" field.
func (u *MenuUpsertBulk) SetIsActive(v bool) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetIsActive(v)
	})
}

// UpdateIsActive sets the "is_active" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateIsActive() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateIsActive()
	})
}

// ClearIsActive clears the value of the "is_active" field.
func (u *MenuUpsertBulk) ClearIsActive() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearIsActive()
	})
}

// SetHidden sets the "hidden" field.
func (u *MenuUpsertBulk) SetHidden(v bool) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetHidden(v)
	})
}

// UpdateHidden sets the "hidden" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateHidden() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateHidden()
	})
}

// ClearHidden clears the value of the "hidden" field.
func (u *MenuUpsertBulk) ClearHidden() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearHidden()
	})
}

// SetHiddenInTab sets the "hidden_in_tab" field.
func (u *MenuUpsertBulk) SetHiddenInTab(v bool) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetHiddenInTab(v)
	})
}

// UpdateHiddenInTab sets the "hidden_in_tab" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateHiddenInTab() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateHiddenInTab()
	})
}

// ClearHiddenInTab clears the value of the "hidden_in_tab" field.
func (u *MenuUpsertBulk) ClearHiddenInTab() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearHiddenInTab()
	})
}

// SetFixed sets the "fixed" field.
func (u *MenuUpsertBulk) SetFixed(v bool) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetFixed(v)
	})
}

// UpdateFixed sets the "fixed" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateFixed() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateFixed()
	})
}

// ClearFixed clears the value of the "fixed" field.
func (u *MenuUpsertBulk) ClearFixed() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearFixed()
	})
}

// SetRemark sets the "remark" field.
func (u *MenuUpsertBulk) SetRemark(v string) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateRemark() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateRemark()
	})
}

// ClearRemark clears the value of the "remark" field.
func (u *MenuUpsertBulk) ClearRemark() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearRemark()
	})
}

// SetMeta sets the "meta" field.
func (u *MenuUpsertBulk) SetMeta(v string) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetMeta(v)
	})
}

// UpdateMeta sets the "meta" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateMeta() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateMeta()
	})
}

// ClearMeta clears the value of the "meta" field.
func (u *MenuUpsertBulk) ClearMeta() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearMeta()
	})
}

// SetIsFullPage sets the "is_full_page" field.
func (u *MenuUpsertBulk) SetIsFullPage(v bool) *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.SetIsFullPage(v)
	})
}

// UpdateIsFullPage sets the "is_full_page" field to the value that was provided on create.
func (u *MenuUpsertBulk) UpdateIsFullPage() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.UpdateIsFullPage()
	})
}

// ClearIsFullPage clears the value of the "is_full_page" field.
func (u *MenuUpsertBulk) ClearIsFullPage() *MenuUpsertBulk {
	return u.Update(func(s *MenuUpsert) {
		s.ClearIsFullPage()
	})
}

// Exec executes the query.
func (u *MenuUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the MenuCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for MenuCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *MenuUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
