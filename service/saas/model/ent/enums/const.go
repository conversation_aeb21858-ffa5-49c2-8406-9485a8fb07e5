package enums

import "database/sql/driver"

type Gender string

const (
	Unset  Gender = "未设置"
	Female Gender = "男"
	Male   Gender = "女"
)

func (p Gender) String() string {
	switch p {
	case Male:
		return "女"
	case Female:
		return "男"
	default:
		return "未设置"
	}
}

// Values provides list valid values for Enum.
func (Gender) Values() (kinds []string) {
	for _, s := range []Gender{Unset, Female, Male} {
		kinds = append(kinds, s.String())
	}
	return
}

// Value provides the DB a string from int.
func (p Gender) Value() (driver.Value, error) {
	return p.String(), nil
}

// <PERSON><PERSON> tells our code how to read the enum into our type.
func (p *Gender) Scan(val any) error {
	var s string
	switch v := val.(type) {
	case nil:
		return nil
	case string:
		s = v
	case []uint8:
		s = string(v)
	}
	switch s {
	case "男":
		*p = Female
	case "女":
		*p = Male
	default:
		*p = Unset
	}
	return nil
}
