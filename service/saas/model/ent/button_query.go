// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"database/sql/driver"
	"fmt"
	"math"
	"phoenix/service/saas/model/ent/button"
	"phoenix/service/saas/model/ent/menu"
	"phoenix/service/saas/model/ent/predicate"
	"phoenix/service/saas/model/ent/role"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ButtonQuery is the builder for querying Button entities.
type ButtonQuery struct {
	config
	ctx        *QueryContext
	order      []button.OrderOption
	inters     []Interceptor
	predicates []predicate.Button
	withRoles  *RoleQuery
	withMenu   *MenuQuery
	withFKs    bool
	// intermediate query (i.e. traversal path).
	sql  *sql.Selector
	path func(context.Context) (*sql.Selector, error)
}

// Where adds a new predicate for the ButtonQuery builder.
func (bq *ButtonQuery) Where(ps ...predicate.Button) *ButtonQuery {
	bq.predicates = append(bq.predicates, ps...)
	return bq
}

// Limit the number of records to be returned by this query.
func (bq *ButtonQuery) Limit(limit int) *ButtonQuery {
	bq.ctx.Limit = &limit
	return bq
}

// Offset to start from.
func (bq *ButtonQuery) Offset(offset int) *ButtonQuery {
	bq.ctx.Offset = &offset
	return bq
}

// Unique configures the query builder to filter duplicate records on query.
// By default, unique is set to true, and can be disabled using this method.
func (bq *ButtonQuery) Unique(unique bool) *ButtonQuery {
	bq.ctx.Unique = &unique
	return bq
}

// Order specifies how the records should be ordered.
func (bq *ButtonQuery) Order(o ...button.OrderOption) *ButtonQuery {
	bq.order = append(bq.order, o...)
	return bq
}

// QueryRoles chains the current query on the "roles" edge.
func (bq *ButtonQuery) QueryRoles() *RoleQuery {
	query := (&RoleClient{config: bq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := bq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := bq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(button.Table, button.FieldID, selector),
			sqlgraph.To(role.Table, role.FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, button.RolesTable, button.RolesPrimaryKey...),
		)
		fromU = sqlgraph.SetNeighbors(bq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// QueryMenu chains the current query on the "menu" edge.
func (bq *ButtonQuery) QueryMenu() *MenuQuery {
	query := (&MenuClient{config: bq.config}).Query()
	query.path = func(ctx context.Context) (fromU *sql.Selector, err error) {
		if err := bq.prepareQuery(ctx); err != nil {
			return nil, err
		}
		selector := bq.sqlQuery(ctx)
		if err := selector.Err(); err != nil {
			return nil, err
		}
		step := sqlgraph.NewStep(
			sqlgraph.From(button.Table, button.FieldID, selector),
			sqlgraph.To(menu.Table, menu.FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, button.MenuTable, button.MenuColumn),
		)
		fromU = sqlgraph.SetNeighbors(bq.driver.Dialect(), step)
		return fromU, nil
	}
	return query
}

// First returns the first Button entity from the query.
// Returns a *NotFoundError when no Button was found.
func (bq *ButtonQuery) First(ctx context.Context) (*Button, error) {
	nodes, err := bq.Limit(1).All(setContextOp(ctx, bq.ctx, "First"))
	if err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nil, &NotFoundError{button.Label}
	}
	return nodes[0], nil
}

// FirstX is like First, but panics if an error occurs.
func (bq *ButtonQuery) FirstX(ctx context.Context) *Button {
	node, err := bq.First(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return node
}

// FirstID returns the first Button ID from the query.
// Returns a *NotFoundError when no Button ID was found.
func (bq *ButtonQuery) FirstID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = bq.Limit(1).IDs(setContextOp(ctx, bq.ctx, "FirstID")); err != nil {
		return
	}
	if len(ids) == 0 {
		err = &NotFoundError{button.Label}
		return
	}
	return ids[0], nil
}

// FirstIDX is like FirstID, but panics if an error occurs.
func (bq *ButtonQuery) FirstIDX(ctx context.Context) string {
	id, err := bq.FirstID(ctx)
	if err != nil && !IsNotFound(err) {
		panic(err)
	}
	return id
}

// Only returns a single Button entity found by the query, ensuring it only returns one.
// Returns a *NotSingularError when more than one Button entity is found.
// Returns a *NotFoundError when no Button entities are found.
func (bq *ButtonQuery) Only(ctx context.Context) (*Button, error) {
	nodes, err := bq.Limit(2).All(setContextOp(ctx, bq.ctx, "Only"))
	if err != nil {
		return nil, err
	}
	switch len(nodes) {
	case 1:
		return nodes[0], nil
	case 0:
		return nil, &NotFoundError{button.Label}
	default:
		return nil, &NotSingularError{button.Label}
	}
}

// OnlyX is like Only, but panics if an error occurs.
func (bq *ButtonQuery) OnlyX(ctx context.Context) *Button {
	node, err := bq.Only(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// OnlyID is like Only, but returns the only Button ID in the query.
// Returns a *NotSingularError when more than one Button ID is found.
// Returns a *NotFoundError when no entities are found.
func (bq *ButtonQuery) OnlyID(ctx context.Context) (id string, err error) {
	var ids []string
	if ids, err = bq.Limit(2).IDs(setContextOp(ctx, bq.ctx, "OnlyID")); err != nil {
		return
	}
	switch len(ids) {
	case 1:
		id = ids[0]
	case 0:
		err = &NotFoundError{button.Label}
	default:
		err = &NotSingularError{button.Label}
	}
	return
}

// OnlyIDX is like OnlyID, but panics if an error occurs.
func (bq *ButtonQuery) OnlyIDX(ctx context.Context) string {
	id, err := bq.OnlyID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// All executes the query and returns a list of Buttons.
func (bq *ButtonQuery) All(ctx context.Context) ([]*Button, error) {
	ctx = setContextOp(ctx, bq.ctx, "All")
	if err := bq.prepareQuery(ctx); err != nil {
		return nil, err
	}
	qr := querierAll[[]*Button, *ButtonQuery]()
	return withInterceptors[[]*Button](ctx, bq, qr, bq.inters)
}

// AllX is like All, but panics if an error occurs.
func (bq *ButtonQuery) AllX(ctx context.Context) []*Button {
	nodes, err := bq.All(ctx)
	if err != nil {
		panic(err)
	}
	return nodes
}

// IDs executes the query and returns a list of Button IDs.
func (bq *ButtonQuery) IDs(ctx context.Context) (ids []string, err error) {
	if bq.ctx.Unique == nil && bq.path != nil {
		bq.Unique(true)
	}
	ctx = setContextOp(ctx, bq.ctx, "IDs")
	if err = bq.Select(button.FieldID).Scan(ctx, &ids); err != nil {
		return nil, err
	}
	return ids, nil
}

// IDsX is like IDs, but panics if an error occurs.
func (bq *ButtonQuery) IDsX(ctx context.Context) []string {
	ids, err := bq.IDs(ctx)
	if err != nil {
		panic(err)
	}
	return ids
}

// Count returns the count of the given query.
func (bq *ButtonQuery) Count(ctx context.Context) (int, error) {
	ctx = setContextOp(ctx, bq.ctx, "Count")
	if err := bq.prepareQuery(ctx); err != nil {
		return 0, err
	}
	return withInterceptors[int](ctx, bq, querierCount[*ButtonQuery](), bq.inters)
}

// CountX is like Count, but panics if an error occurs.
func (bq *ButtonQuery) CountX(ctx context.Context) int {
	count, err := bq.Count(ctx)
	if err != nil {
		panic(err)
	}
	return count
}

// Exist returns true if the query has elements in the graph.
func (bq *ButtonQuery) Exist(ctx context.Context) (bool, error) {
	ctx = setContextOp(ctx, bq.ctx, "Exist")
	switch _, err := bq.FirstID(ctx); {
	case IsNotFound(err):
		return false, nil
	case err != nil:
		return false, fmt.Errorf("ent: check existence: %w", err)
	default:
		return true, nil
	}
}

// ExistX is like Exist, but panics if an error occurs.
func (bq *ButtonQuery) ExistX(ctx context.Context) bool {
	exist, err := bq.Exist(ctx)
	if err != nil {
		panic(err)
	}
	return exist
}

// Clone returns a duplicate of the ButtonQuery builder, including all associated steps. It can be
// used to prepare common query builders and use them differently after the clone is made.
func (bq *ButtonQuery) Clone() *ButtonQuery {
	if bq == nil {
		return nil
	}
	return &ButtonQuery{
		config:     bq.config,
		ctx:        bq.ctx.Clone(),
		order:      append([]button.OrderOption{}, bq.order...),
		inters:     append([]Interceptor{}, bq.inters...),
		predicates: append([]predicate.Button{}, bq.predicates...),
		withRoles:  bq.withRoles.Clone(),
		withMenu:   bq.withMenu.Clone(),
		// clone intermediate query.
		sql:  bq.sql.Clone(),
		path: bq.path,
	}
}

// WithRoles tells the query-builder to eager-load the nodes that are connected to
// the "roles" edge. The optional arguments are used to configure the query builder of the edge.
func (bq *ButtonQuery) WithRoles(opts ...func(*RoleQuery)) *ButtonQuery {
	query := (&RoleClient{config: bq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	bq.withRoles = query
	return bq
}

// WithMenu tells the query-builder to eager-load the nodes that are connected to
// the "menu" edge. The optional arguments are used to configure the query builder of the edge.
func (bq *ButtonQuery) WithMenu(opts ...func(*MenuQuery)) *ButtonQuery {
	query := (&MenuClient{config: bq.config}).Query()
	for _, opt := range opts {
		opt(query)
	}
	bq.withMenu = query
	return bq
}

// GroupBy is used to group vertices by one or more fields/columns.
// It is often used with aggregate functions, like: count, max, mean, min, sum.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//		Count int `json:"count,omitempty"`
//	}
//
//	client.Button.Query().
//		GroupBy(button.FieldCreatedAt).
//		Aggregate(ent.Count()).
//		Scan(ctx, &v)
func (bq *ButtonQuery) GroupBy(field string, fields ...string) *ButtonGroupBy {
	bq.ctx.Fields = append([]string{field}, fields...)
	grbuild := &ButtonGroupBy{build: bq}
	grbuild.flds = &bq.ctx.Fields
	grbuild.label = button.Label
	grbuild.scan = grbuild.Scan
	return grbuild
}

// Select allows the selection one or more fields/columns for the given query,
// instead of selecting all fields in the entity.
//
// Example:
//
//	var v []struct {
//		CreatedAt time.Time `json:"created_at,omitempty"`
//	}
//
//	client.Button.Query().
//		Select(button.FieldCreatedAt).
//		Scan(ctx, &v)
func (bq *ButtonQuery) Select(fields ...string) *ButtonSelect {
	bq.ctx.Fields = append(bq.ctx.Fields, fields...)
	sbuild := &ButtonSelect{ButtonQuery: bq}
	sbuild.label = button.Label
	sbuild.flds, sbuild.scan = &bq.ctx.Fields, sbuild.Scan
	return sbuild
}

// Aggregate returns a ButtonSelect configured with the given aggregations.
func (bq *ButtonQuery) Aggregate(fns ...AggregateFunc) *ButtonSelect {
	return bq.Select().Aggregate(fns...)
}

func (bq *ButtonQuery) prepareQuery(ctx context.Context) error {
	for _, inter := range bq.inters {
		if inter == nil {
			return fmt.Errorf("ent: uninitialized interceptor (forgotten import ent/runtime?)")
		}
		if trv, ok := inter.(Traverser); ok {
			if err := trv.Traverse(ctx, bq); err != nil {
				return err
			}
		}
	}
	for _, f := range bq.ctx.Fields {
		if !button.ValidColumn(f) {
			return &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
		}
	}
	if bq.path != nil {
		prev, err := bq.path(ctx)
		if err != nil {
			return err
		}
		bq.sql = prev
	}
	return nil
}

func (bq *ButtonQuery) sqlAll(ctx context.Context, hooks ...queryHook) ([]*Button, error) {
	var (
		nodes       = []*Button{}
		withFKs     = bq.withFKs
		_spec       = bq.querySpec()
		loadedTypes = [2]bool{
			bq.withRoles != nil,
			bq.withMenu != nil,
		}
	)
	if withFKs {
		_spec.Node.Columns = append(_spec.Node.Columns, button.ForeignKeys...)
	}
	_spec.ScanValues = func(columns []string) ([]any, error) {
		return (*Button).scanValues(nil, columns)
	}
	_spec.Assign = func(columns []string, values []any) error {
		node := &Button{config: bq.config}
		nodes = append(nodes, node)
		node.Edges.loadedTypes = loadedTypes
		return node.assignValues(columns, values)
	}
	for i := range hooks {
		hooks[i](ctx, _spec)
	}
	if err := sqlgraph.QueryNodes(ctx, bq.driver, _spec); err != nil {
		return nil, err
	}
	if len(nodes) == 0 {
		return nodes, nil
	}
	if query := bq.withRoles; query != nil {
		if err := bq.loadRoles(ctx, query, nodes,
			func(n *Button) { n.Edges.Roles = []*Role{} },
			func(n *Button, e *Role) { n.Edges.Roles = append(n.Edges.Roles, e) }); err != nil {
			return nil, err
		}
	}
	if query := bq.withMenu; query != nil {
		if err := bq.loadMenu(ctx, query, nodes, nil,
			func(n *Button, e *Menu) { n.Edges.Menu = e }); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

func (bq *ButtonQuery) loadRoles(ctx context.Context, query *RoleQuery, nodes []*Button, init func(*Button), assign func(*Button, *Role)) error {
	edgeIDs := make([]driver.Value, len(nodes))
	byID := make(map[string]*Button)
	nids := make(map[string]map[*Button]struct{})
	for i, node := range nodes {
		edgeIDs[i] = node.ID
		byID[node.ID] = node
		if init != nil {
			init(node)
		}
	}
	query.Where(func(s *sql.Selector) {
		joinT := sql.Table(button.RolesTable)
		s.Join(joinT).On(s.C(role.FieldID), joinT.C(button.RolesPrimaryKey[0]))
		s.Where(sql.InValues(joinT.C(button.RolesPrimaryKey[1]), edgeIDs...))
		columns := s.SelectedColumns()
		s.Select(joinT.C(button.RolesPrimaryKey[1]))
		s.AppendSelect(columns...)
		s.SetDistinct(false)
	})
	if err := query.prepareQuery(ctx); err != nil {
		return err
	}
	qr := QuerierFunc(func(ctx context.Context, q Query) (Value, error) {
		return query.sqlAll(ctx, func(_ context.Context, spec *sqlgraph.QuerySpec) {
			assign := spec.Assign
			values := spec.ScanValues
			spec.ScanValues = func(columns []string) ([]any, error) {
				values, err := values(columns[1:])
				if err != nil {
					return nil, err
				}
				return append([]any{new(sql.NullString)}, values...), nil
			}
			spec.Assign = func(columns []string, values []any) error {
				outValue := values[0].(*sql.NullString).String
				inValue := values[1].(*sql.NullString).String
				if nids[inValue] == nil {
					nids[inValue] = map[*Button]struct{}{byID[outValue]: {}}
					return assign(columns[1:], values[1:])
				}
				nids[inValue][byID[outValue]] = struct{}{}
				return nil
			}
		})
	})
	neighbors, err := withInterceptors[[]*Role](ctx, query, qr, query.inters)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected "roles" node returned %v`, n.ID)
		}
		for kn := range nodes {
			assign(kn, n)
		}
	}
	return nil
}
func (bq *ButtonQuery) loadMenu(ctx context.Context, query *MenuQuery, nodes []*Button, init func(*Button), assign func(*Button, *Menu)) error {
	ids := make([]string, 0, len(nodes))
	nodeids := make(map[string][]*Button)
	for i := range nodes {
		fk := nodes[i].MenuID
		if _, ok := nodeids[fk]; !ok {
			ids = append(ids, fk)
		}
		nodeids[fk] = append(nodeids[fk], nodes[i])
	}
	if len(ids) == 0 {
		return nil
	}
	query.Where(menu.IDIn(ids...))
	neighbors, err := query.All(ctx)
	if err != nil {
		return err
	}
	for _, n := range neighbors {
		nodes, ok := nodeids[n.ID]
		if !ok {
			return fmt.Errorf(`unexpected foreign-key "menu_id" returned %v`, n.ID)
		}
		for i := range nodes {
			assign(nodes[i], n)
		}
	}
	return nil
}

func (bq *ButtonQuery) sqlCount(ctx context.Context) (int, error) {
	_spec := bq.querySpec()
	_spec.Node.Columns = bq.ctx.Fields
	if len(bq.ctx.Fields) > 0 {
		_spec.Unique = bq.ctx.Unique != nil && *bq.ctx.Unique
	}
	return sqlgraph.CountNodes(ctx, bq.driver, _spec)
}

func (bq *ButtonQuery) querySpec() *sqlgraph.QuerySpec {
	_spec := sqlgraph.NewQuerySpec(button.Table, button.Columns, sqlgraph.NewFieldSpec(button.FieldID, field.TypeString))
	_spec.From = bq.sql
	if unique := bq.ctx.Unique; unique != nil {
		_spec.Unique = *unique
	} else if bq.path != nil {
		_spec.Unique = true
	}
	if fields := bq.ctx.Fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, button.FieldID)
		for i := range fields {
			if fields[i] != button.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, fields[i])
			}
		}
		if bq.withMenu != nil {
			_spec.Node.AddColumnOnce(button.FieldMenuID)
		}
	}
	if ps := bq.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if limit := bq.ctx.Limit; limit != nil {
		_spec.Limit = *limit
	}
	if offset := bq.ctx.Offset; offset != nil {
		_spec.Offset = *offset
	}
	if ps := bq.order; len(ps) > 0 {
		_spec.Order = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	return _spec
}

func (bq *ButtonQuery) sqlQuery(ctx context.Context) *sql.Selector {
	builder := sql.Dialect(bq.driver.Dialect())
	t1 := builder.Table(button.Table)
	columns := bq.ctx.Fields
	if len(columns) == 0 {
		columns = button.Columns
	}
	selector := builder.Select(t1.Columns(columns...)...).From(t1)
	if bq.sql != nil {
		selector = bq.sql
		selector.Select(selector.Columns(columns...)...)
	}
	if bq.ctx.Unique != nil && *bq.ctx.Unique {
		selector.Distinct()
	}
	for _, p := range bq.predicates {
		p(selector)
	}
	for _, p := range bq.order {
		p(selector)
	}
	if offset := bq.ctx.Offset; offset != nil {
		// limit is mandatory for offset clause. We start
		// with default value, and override it below if needed.
		selector.Offset(*offset).Limit(math.MaxInt32)
	}
	if limit := bq.ctx.Limit; limit != nil {
		selector.Limit(*limit)
	}
	return selector
}

// ButtonGroupBy is the group-by builder for Button entities.
type ButtonGroupBy struct {
	selector
	build *ButtonQuery
}

// Aggregate adds the given aggregation functions to the group-by query.
func (bgb *ButtonGroupBy) Aggregate(fns ...AggregateFunc) *ButtonGroupBy {
	bgb.fns = append(bgb.fns, fns...)
	return bgb
}

// Scan applies the selector query and scans the result into the given value.
func (bgb *ButtonGroupBy) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, bgb.build.ctx, "GroupBy")
	if err := bgb.build.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*ButtonQuery, *ButtonGroupBy](ctx, bgb.build, bgb, bgb.build.inters, v)
}

func (bgb *ButtonGroupBy) sqlScan(ctx context.Context, root *ButtonQuery, v any) error {
	selector := root.sqlQuery(ctx).Select()
	aggregation := make([]string, 0, len(bgb.fns))
	for _, fn := range bgb.fns {
		aggregation = append(aggregation, fn(selector))
	}
	if len(selector.SelectedColumns()) == 0 {
		columns := make([]string, 0, len(*bgb.flds)+len(bgb.fns))
		for _, f := range *bgb.flds {
			columns = append(columns, selector.C(f))
		}
		columns = append(columns, aggregation...)
		selector.Select(columns...)
	}
	selector.GroupBy(selector.Columns(*bgb.flds...)...)
	if err := selector.Err(); err != nil {
		return err
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := bgb.build.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}

// ButtonSelect is the builder for selecting fields of Button entities.
type ButtonSelect struct {
	*ButtonQuery
	selector
}

// Aggregate adds the given aggregation functions to the selector query.
func (bs *ButtonSelect) Aggregate(fns ...AggregateFunc) *ButtonSelect {
	bs.fns = append(bs.fns, fns...)
	return bs
}

// Scan applies the selector query and scans the result into the given value.
func (bs *ButtonSelect) Scan(ctx context.Context, v any) error {
	ctx = setContextOp(ctx, bs.ctx, "Select")
	if err := bs.prepareQuery(ctx); err != nil {
		return err
	}
	return scanWithInterceptors[*ButtonQuery, *ButtonSelect](ctx, bs.ButtonQuery, bs, bs.inters, v)
}

func (bs *ButtonSelect) sqlScan(ctx context.Context, root *ButtonQuery, v any) error {
	selector := root.sqlQuery(ctx)
	aggregation := make([]string, 0, len(bs.fns))
	for _, fn := range bs.fns {
		aggregation = append(aggregation, fn(selector))
	}
	switch n := len(*bs.selector.flds); {
	case n == 0 && len(aggregation) > 0:
		selector.Select(aggregation...)
	case n != 0 && len(aggregation) > 0:
		selector.AppendSelect(aggregation...)
	}
	rows := &sql.Rows{}
	query, args := selector.Query()
	if err := bs.driver.Query(ctx, query, args, rows); err != nil {
		return err
	}
	defer rows.Close()
	return sql.ScanSlice(rows, v)
}
