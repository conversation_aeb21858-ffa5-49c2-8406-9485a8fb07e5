// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"phoenix/service/saas/model/ent/enums"
	"phoenix/service/saas/model/ent/file"
	"phoenix/service/saas/model/ent/user"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// User is the model entity for the User schema.
type User struct {
	config `json:"-"`
	// ID of the ent.
	// Snowflake ID | 全局唯一ID
	ID string `json:"id,omitempty"`
	// Created Time | 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// Updated Time | 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// status true normal false ban | 状态  正常/禁用
	Status bool `json:"status,omitempty"`
	// Deleted Time | 删除时间（软删除标识）
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// User's login name | 登录名
	Username string `json:"username,omitempty"`
	// Password | 密码Hash
	Password string `json:"-"`
	// Nickname | 昵称
	Nickname string `json:"nickname,omitempty"`
	// Mobile number | 手机号
	Mobile string `json:"mobile,omitempty"`
	// Email | 邮箱号
	Email string `json:"email,omitempty"`
	// gender | 性别
	Gender enums.Gender `json:"gender,omitempty"`
	// post | 职务
	Post string `json:"post,omitempty"`
	// Is Superuser | 是否超级管理员
	IsSuperuser bool `json:"is_superuser,omitempty"`
	// Default tenant id | 默认租户ID，用于快速登录
	DefaultTenantID string `json:"default_tenant_id,omitempty"`
	// Avatar FIle ID | 头像文件ID
	AvatarID string `json:"avatar_id,omitempty"`
	// Device No | 设备号
	DeviceNo string `json:"device_no,omitempty"`
	// kind | 类型，attendance：列席用户
	Kind string `json:"kind,omitempty"`
	// imei | imei
	Imei string `json:"imei,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the UserQuery when eager-loading is set.
	Edges        UserEdges `json:"edges"`
	selectValues sql.SelectValues
}

// UserEdges holds the relations/edges for other nodes in the graph.
type UserEdges struct {
	// Tenants holds the value of the tenants edge.
	Tenants []*Tenant `json:"tenants,omitempty"`
	// Organizations holds the value of the organizations edge.
	Organizations []*Organization `json:"organizations,omitempty"`
	// Positions holds the value of the positions edge.
	Positions []*Position `json:"positions,omitempty"`
	// Groups holds the value of the groups edge.
	Groups []*Group `json:"groups,omitempty"`
	// Roles holds the value of the roles edge.
	Roles []*Role `json:"roles,omitempty"`
	// OrganizationInfos holds the value of the organization_infos edge.
	OrganizationInfos []*OrganizationUserInfo `json:"organization_infos,omitempty"`
	// TenantInfos holds the value of the tenant_infos edge.
	TenantInfos []*TenantUserInfo `json:"tenant_infos,omitempty"`
	// Files holds the value of the files edge.
	Files []*File `json:"files,omitempty"`
	// Avatar holds the value of the avatar edge.
	Avatar *File `json:"avatar,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [9]bool
}

// TenantsOrErr returns the Tenants value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) TenantsOrErr() ([]*Tenant, error) {
	if e.loadedTypes[0] {
		return e.Tenants, nil
	}
	return nil, &NotLoadedError{edge: "tenants"}
}

// OrganizationsOrErr returns the Organizations value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) OrganizationsOrErr() ([]*Organization, error) {
	if e.loadedTypes[1] {
		return e.Organizations, nil
	}
	return nil, &NotLoadedError{edge: "organizations"}
}

// PositionsOrErr returns the Positions value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) PositionsOrErr() ([]*Position, error) {
	if e.loadedTypes[2] {
		return e.Positions, nil
	}
	return nil, &NotLoadedError{edge: "positions"}
}

// GroupsOrErr returns the Groups value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) GroupsOrErr() ([]*Group, error) {
	if e.loadedTypes[3] {
		return e.Groups, nil
	}
	return nil, &NotLoadedError{edge: "groups"}
}

// RolesOrErr returns the Roles value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) RolesOrErr() ([]*Role, error) {
	if e.loadedTypes[4] {
		return e.Roles, nil
	}
	return nil, &NotLoadedError{edge: "roles"}
}

// OrganizationInfosOrErr returns the OrganizationInfos value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) OrganizationInfosOrErr() ([]*OrganizationUserInfo, error) {
	if e.loadedTypes[5] {
		return e.OrganizationInfos, nil
	}
	return nil, &NotLoadedError{edge: "organization_infos"}
}

// TenantInfosOrErr returns the TenantInfos value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) TenantInfosOrErr() ([]*TenantUserInfo, error) {
	if e.loadedTypes[6] {
		return e.TenantInfos, nil
	}
	return nil, &NotLoadedError{edge: "tenant_infos"}
}

// FilesOrErr returns the Files value or an error if the edge
// was not loaded in eager-loading.
func (e UserEdges) FilesOrErr() ([]*File, error) {
	if e.loadedTypes[7] {
		return e.Files, nil
	}
	return nil, &NotLoadedError{edge: "files"}
}

// AvatarOrErr returns the Avatar value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e UserEdges) AvatarOrErr() (*File, error) {
	if e.Avatar != nil {
		return e.Avatar, nil
	} else if e.loadedTypes[8] {
		return nil, &NotFoundError{label: file.Label}
	}
	return nil, &NotLoadedError{edge: "avatar"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*User) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case user.FieldGender:
			values[i] = new(enums.Gender)
		case user.FieldStatus, user.FieldIsSuperuser:
			values[i] = new(sql.NullBool)
		case user.FieldID, user.FieldUsername, user.FieldPassword, user.FieldNickname, user.FieldMobile, user.FieldEmail, user.FieldPost, user.FieldDefaultTenantID, user.FieldAvatarID, user.FieldDeviceNo, user.FieldKind, user.FieldImei:
			values[i] = new(sql.NullString)
		case user.FieldCreatedAt, user.FieldUpdatedAt, user.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the User fields.
func (u *User) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case user.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				u.ID = value.String
			}
		case user.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				u.CreatedAt = value.Time
			}
		case user.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				u.UpdatedAt = value.Time
			}
		case user.FieldStatus:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				u.Status = value.Bool
			}
		case user.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				u.DeletedAt = value.Time
			}
		case user.FieldUsername:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field username", values[i])
			} else if value.Valid {
				u.Username = value.String
			}
		case user.FieldPassword:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field password", values[i])
			} else if value.Valid {
				u.Password = value.String
			}
		case user.FieldNickname:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field nickname", values[i])
			} else if value.Valid {
				u.Nickname = value.String
			}
		case user.FieldMobile:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field mobile", values[i])
			} else if value.Valid {
				u.Mobile = value.String
			}
		case user.FieldEmail:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field email", values[i])
			} else if value.Valid {
				u.Email = value.String
			}
		case user.FieldGender:
			if value, ok := values[i].(*enums.Gender); !ok {
				return fmt.Errorf("unexpected type %T for field gender", values[i])
			} else if value != nil {
				u.Gender = *value
			}
		case user.FieldPost:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field post", values[i])
			} else if value.Valid {
				u.Post = value.String
			}
		case user.FieldIsSuperuser:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field is_superuser", values[i])
			} else if value.Valid {
				u.IsSuperuser = value.Bool
			}
		case user.FieldDefaultTenantID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field default_tenant_id", values[i])
			} else if value.Valid {
				u.DefaultTenantID = value.String
			}
		case user.FieldAvatarID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field avatar_id", values[i])
			} else if value.Valid {
				u.AvatarID = value.String
			}
		case user.FieldDeviceNo:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field device_no", values[i])
			} else if value.Valid {
				u.DeviceNo = value.String
			}
		case user.FieldKind:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field kind", values[i])
			} else if value.Valid {
				u.Kind = value.String
			}
		case user.FieldImei:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field imei", values[i])
			} else if value.Valid {
				u.Imei = value.String
			}
		default:
			u.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the User.
// This includes values selected through modifiers, order, etc.
func (u *User) Value(name string) (ent.Value, error) {
	return u.selectValues.Get(name)
}

// QueryTenants queries the "tenants" edge of the User entity.
func (u *User) QueryTenants() *TenantQuery {
	return NewUserClient(u.config).QueryTenants(u)
}

// QueryOrganizations queries the "organizations" edge of the User entity.
func (u *User) QueryOrganizations() *OrganizationQuery {
	return NewUserClient(u.config).QueryOrganizations(u)
}

// QueryPositions queries the "positions" edge of the User entity.
func (u *User) QueryPositions() *PositionQuery {
	return NewUserClient(u.config).QueryPositions(u)
}

// QueryGroups queries the "groups" edge of the User entity.
func (u *User) QueryGroups() *GroupQuery {
	return NewUserClient(u.config).QueryGroups(u)
}

// QueryRoles queries the "roles" edge of the User entity.
func (u *User) QueryRoles() *RoleQuery {
	return NewUserClient(u.config).QueryRoles(u)
}

// QueryOrganizationInfos queries the "organization_infos" edge of the User entity.
func (u *User) QueryOrganizationInfos() *OrganizationUserInfoQuery {
	return NewUserClient(u.config).QueryOrganizationInfos(u)
}

// QueryTenantInfos queries the "tenant_infos" edge of the User entity.
func (u *User) QueryTenantInfos() *TenantUserInfoQuery {
	return NewUserClient(u.config).QueryTenantInfos(u)
}

// QueryFiles queries the "files" edge of the User entity.
func (u *User) QueryFiles() *FileQuery {
	return NewUserClient(u.config).QueryFiles(u)
}

// QueryAvatar queries the "avatar" edge of the User entity.
func (u *User) QueryAvatar() *FileQuery {
	return NewUserClient(u.config).QueryAvatar(u)
}

// Update returns a builder for updating this User.
// Note that you need to call User.Unwrap() before calling this method if this User
// was returned from a transaction, and the transaction was committed or rolled back.
func (u *User) Update() *UserUpdateOne {
	return NewUserClient(u.config).UpdateOne(u)
}

// Unwrap unwraps the User entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (u *User) Unwrap() *User {
	_tx, ok := u.config.driver.(*txDriver)
	if !ok {
		panic("ent: User is not a transactional entity")
	}
	u.config.driver = _tx.drv
	return u
}

// String implements the fmt.Stringer.
func (u *User) String() string {
	var builder strings.Builder
	builder.WriteString("User(")
	builder.WriteString(fmt.Sprintf("id=%v, ", u.ID))
	builder.WriteString("created_at=")
	builder.WriteString(u.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(u.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", u.Status))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(u.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("username=")
	builder.WriteString(u.Username)
	builder.WriteString(", ")
	builder.WriteString("password=<sensitive>")
	builder.WriteString(", ")
	builder.WriteString("nickname=")
	builder.WriteString(u.Nickname)
	builder.WriteString(", ")
	builder.WriteString("mobile=")
	builder.WriteString(u.Mobile)
	builder.WriteString(", ")
	builder.WriteString("email=")
	builder.WriteString(u.Email)
	builder.WriteString(", ")
	builder.WriteString("gender=")
	builder.WriteString(fmt.Sprintf("%v", u.Gender))
	builder.WriteString(", ")
	builder.WriteString("post=")
	builder.WriteString(u.Post)
	builder.WriteString(", ")
	builder.WriteString("is_superuser=")
	builder.WriteString(fmt.Sprintf("%v", u.IsSuperuser))
	builder.WriteString(", ")
	builder.WriteString("default_tenant_id=")
	builder.WriteString(u.DefaultTenantID)
	builder.WriteString(", ")
	builder.WriteString("avatar_id=")
	builder.WriteString(u.AvatarID)
	builder.WriteString(", ")
	builder.WriteString("device_no=")
	builder.WriteString(u.DeviceNo)
	builder.WriteString(", ")
	builder.WriteString("kind=")
	builder.WriteString(u.Kind)
	builder.WriteString(", ")
	builder.WriteString("imei=")
	builder.WriteString(u.Imei)
	builder.WriteByte(')')
	return builder.String()
}

// Users is a parsable slice of User.
type Users []*User
