// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/api"
	"phoenix/service/saas/model/ent/predicate"
	"phoenix/service/saas/model/ent/role"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// APIUpdate is the builder for updating API entities.
type APIUpdate struct {
	config
	hooks    []Hook
	mutation *APIMutation
}

// Where appends a list predicates to the APIUpdate builder.
func (au *APIUpdate) Where(ps ...predicate.API) *APIUpdate {
	au.mutation.Where(ps...)
	return au
}

// SetUpdatedAt sets the "updated_at" field.
func (au *APIUpdate) SetUpdatedAt(t time.Time) *APIUpdate {
	au.mutation.SetUpdatedAt(t)
	return au
}

// SetStatus sets the "status" field.
func (au *APIUpdate) SetStatus(b bool) *APIUpdate {
	au.mutation.SetStatus(b)
	return au
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (au *APIUpdate) SetNillableStatus(b *bool) *APIUpdate {
	if b != nil {
		au.SetStatus(*b)
	}
	return au
}

// ClearStatus clears the value of the "status" field.
func (au *APIUpdate) ClearStatus() *APIUpdate {
	au.mutation.ClearStatus()
	return au
}

// SetDeletedAt sets the "deleted_at" field.
func (au *APIUpdate) SetDeletedAt(t time.Time) *APIUpdate {
	au.mutation.SetDeletedAt(t)
	return au
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (au *APIUpdate) SetNillableDeletedAt(t *time.Time) *APIUpdate {
	if t != nil {
		au.SetDeletedAt(*t)
	}
	return au
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (au *APIUpdate) ClearDeletedAt() *APIUpdate {
	au.mutation.ClearDeletedAt()
	return au
}

// SetPath sets the "path" field.
func (au *APIUpdate) SetPath(s string) *APIUpdate {
	au.mutation.SetPath(s)
	return au
}

// SetNillablePath sets the "path" field if the given value is not nil.
func (au *APIUpdate) SetNillablePath(s *string) *APIUpdate {
	if s != nil {
		au.SetPath(*s)
	}
	return au
}

// SetDescription sets the "description" field.
func (au *APIUpdate) SetDescription(s string) *APIUpdate {
	au.mutation.SetDescription(s)
	return au
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (au *APIUpdate) SetNillableDescription(s *string) *APIUpdate {
	if s != nil {
		au.SetDescription(*s)
	}
	return au
}

// SetAPIGroup sets the "api_group" field.
func (au *APIUpdate) SetAPIGroup(s string) *APIUpdate {
	au.mutation.SetAPIGroup(s)
	return au
}

// SetNillableAPIGroup sets the "api_group" field if the given value is not nil.
func (au *APIUpdate) SetNillableAPIGroup(s *string) *APIUpdate {
	if s != nil {
		au.SetAPIGroup(*s)
	}
	return au
}

// SetMethod sets the "method" field.
func (au *APIUpdate) SetMethod(s string) *APIUpdate {
	au.mutation.SetMethod(s)
	return au
}

// SetNillableMethod sets the "method" field if the given value is not nil.
func (au *APIUpdate) SetNillableMethod(s *string) *APIUpdate {
	if s != nil {
		au.SetMethod(*s)
	}
	return au
}

// SetKind sets the "kind" field.
func (au *APIUpdate) SetKind(s string) *APIUpdate {
	au.mutation.SetKind(s)
	return au
}

// SetNillableKind sets the "kind" field if the given value is not nil.
func (au *APIUpdate) SetNillableKind(s *string) *APIUpdate {
	if s != nil {
		au.SetKind(*s)
	}
	return au
}

// SetModule sets the "module" field.
func (au *APIUpdate) SetModule(s string) *APIUpdate {
	au.mutation.SetModule(s)
	return au
}

// SetNillableModule sets the "module" field if the given value is not nil.
func (au *APIUpdate) SetNillableModule(s *string) *APIUpdate {
	if s != nil {
		au.SetModule(*s)
	}
	return au
}

// AddRoleIDs adds the "roles" edge to the Role entity by IDs.
func (au *APIUpdate) AddRoleIDs(ids ...string) *APIUpdate {
	au.mutation.AddRoleIDs(ids...)
	return au
}

// AddRoles adds the "roles" edges to the Role entity.
func (au *APIUpdate) AddRoles(r ...*Role) *APIUpdate {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return au.AddRoleIDs(ids...)
}

// Mutation returns the APIMutation object of the builder.
func (au *APIUpdate) Mutation() *APIMutation {
	return au.mutation
}

// ClearRoles clears all "roles" edges to the Role entity.
func (au *APIUpdate) ClearRoles() *APIUpdate {
	au.mutation.ClearRoles()
	return au
}

// RemoveRoleIDs removes the "roles" edge to Role entities by IDs.
func (au *APIUpdate) RemoveRoleIDs(ids ...string) *APIUpdate {
	au.mutation.RemoveRoleIDs(ids...)
	return au
}

// RemoveRoles removes "roles" edges to Role entities.
func (au *APIUpdate) RemoveRoles(r ...*Role) *APIUpdate {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return au.RemoveRoleIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (au *APIUpdate) Save(ctx context.Context) (int, error) {
	if err := au.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, au.sqlSave, au.mutation, au.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (au *APIUpdate) SaveX(ctx context.Context) int {
	affected, err := au.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (au *APIUpdate) Exec(ctx context.Context) error {
	_, err := au.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (au *APIUpdate) ExecX(ctx context.Context) {
	if err := au.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (au *APIUpdate) defaults() error {
	if _, ok := au.mutation.UpdatedAt(); !ok {
		if api.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized api.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := api.UpdateDefaultUpdatedAt()
		au.mutation.SetUpdatedAt(v)
	}
	return nil
}

func (au *APIUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(api.Table, api.Columns, sqlgraph.NewFieldSpec(api.FieldID, field.TypeString))
	if ps := au.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := au.mutation.UpdatedAt(); ok {
		_spec.SetField(api.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := au.mutation.Status(); ok {
		_spec.SetField(api.FieldStatus, field.TypeBool, value)
	}
	if au.mutation.StatusCleared() {
		_spec.ClearField(api.FieldStatus, field.TypeBool)
	}
	if value, ok := au.mutation.DeletedAt(); ok {
		_spec.SetField(api.FieldDeletedAt, field.TypeTime, value)
	}
	if au.mutation.DeletedAtCleared() {
		_spec.ClearField(api.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := au.mutation.Path(); ok {
		_spec.SetField(api.FieldPath, field.TypeString, value)
	}
	if value, ok := au.mutation.Description(); ok {
		_spec.SetField(api.FieldDescription, field.TypeString, value)
	}
	if value, ok := au.mutation.APIGroup(); ok {
		_spec.SetField(api.FieldAPIGroup, field.TypeString, value)
	}
	if value, ok := au.mutation.Method(); ok {
		_spec.SetField(api.FieldMethod, field.TypeString, value)
	}
	if value, ok := au.mutation.Kind(); ok {
		_spec.SetField(api.FieldKind, field.TypeString, value)
	}
	if value, ok := au.mutation.Module(); ok {
		_spec.SetField(api.FieldModule, field.TypeString, value)
	}
	if au.mutation.RolesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   api.RolesTable,
			Columns: api.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := au.mutation.RemovedRolesIDs(); len(nodes) > 0 && !au.mutation.RolesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   api.RolesTable,
			Columns: api.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := au.mutation.RolesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   api.RolesTable,
			Columns: api.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, au.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{api.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	au.mutation.done = true
	return n, nil
}

// APIUpdateOne is the builder for updating a single API entity.
type APIUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *APIMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (auo *APIUpdateOne) SetUpdatedAt(t time.Time) *APIUpdateOne {
	auo.mutation.SetUpdatedAt(t)
	return auo
}

// SetStatus sets the "status" field.
func (auo *APIUpdateOne) SetStatus(b bool) *APIUpdateOne {
	auo.mutation.SetStatus(b)
	return auo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (auo *APIUpdateOne) SetNillableStatus(b *bool) *APIUpdateOne {
	if b != nil {
		auo.SetStatus(*b)
	}
	return auo
}

// ClearStatus clears the value of the "status" field.
func (auo *APIUpdateOne) ClearStatus() *APIUpdateOne {
	auo.mutation.ClearStatus()
	return auo
}

// SetDeletedAt sets the "deleted_at" field.
func (auo *APIUpdateOne) SetDeletedAt(t time.Time) *APIUpdateOne {
	auo.mutation.SetDeletedAt(t)
	return auo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (auo *APIUpdateOne) SetNillableDeletedAt(t *time.Time) *APIUpdateOne {
	if t != nil {
		auo.SetDeletedAt(*t)
	}
	return auo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (auo *APIUpdateOne) ClearDeletedAt() *APIUpdateOne {
	auo.mutation.ClearDeletedAt()
	return auo
}

// SetPath sets the "path" field.
func (auo *APIUpdateOne) SetPath(s string) *APIUpdateOne {
	auo.mutation.SetPath(s)
	return auo
}

// SetNillablePath sets the "path" field if the given value is not nil.
func (auo *APIUpdateOne) SetNillablePath(s *string) *APIUpdateOne {
	if s != nil {
		auo.SetPath(*s)
	}
	return auo
}

// SetDescription sets the "description" field.
func (auo *APIUpdateOne) SetDescription(s string) *APIUpdateOne {
	auo.mutation.SetDescription(s)
	return auo
}

// SetNillableDescription sets the "description" field if the given value is not nil.
func (auo *APIUpdateOne) SetNillableDescription(s *string) *APIUpdateOne {
	if s != nil {
		auo.SetDescription(*s)
	}
	return auo
}

// SetAPIGroup sets the "api_group" field.
func (auo *APIUpdateOne) SetAPIGroup(s string) *APIUpdateOne {
	auo.mutation.SetAPIGroup(s)
	return auo
}

// SetNillableAPIGroup sets the "api_group" field if the given value is not nil.
func (auo *APIUpdateOne) SetNillableAPIGroup(s *string) *APIUpdateOne {
	if s != nil {
		auo.SetAPIGroup(*s)
	}
	return auo
}

// SetMethod sets the "method" field.
func (auo *APIUpdateOne) SetMethod(s string) *APIUpdateOne {
	auo.mutation.SetMethod(s)
	return auo
}

// SetNillableMethod sets the "method" field if the given value is not nil.
func (auo *APIUpdateOne) SetNillableMethod(s *string) *APIUpdateOne {
	if s != nil {
		auo.SetMethod(*s)
	}
	return auo
}

// SetKind sets the "kind" field.
func (auo *APIUpdateOne) SetKind(s string) *APIUpdateOne {
	auo.mutation.SetKind(s)
	return auo
}

// SetNillableKind sets the "kind" field if the given value is not nil.
func (auo *APIUpdateOne) SetNillableKind(s *string) *APIUpdateOne {
	if s != nil {
		auo.SetKind(*s)
	}
	return auo
}

// SetModule sets the "module" field.
func (auo *APIUpdateOne) SetModule(s string) *APIUpdateOne {
	auo.mutation.SetModule(s)
	return auo
}

// SetNillableModule sets the "module" field if the given value is not nil.
func (auo *APIUpdateOne) SetNillableModule(s *string) *APIUpdateOne {
	if s != nil {
		auo.SetModule(*s)
	}
	return auo
}

// AddRoleIDs adds the "roles" edge to the Role entity by IDs.
func (auo *APIUpdateOne) AddRoleIDs(ids ...string) *APIUpdateOne {
	auo.mutation.AddRoleIDs(ids...)
	return auo
}

// AddRoles adds the "roles" edges to the Role entity.
func (auo *APIUpdateOne) AddRoles(r ...*Role) *APIUpdateOne {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return auo.AddRoleIDs(ids...)
}

// Mutation returns the APIMutation object of the builder.
func (auo *APIUpdateOne) Mutation() *APIMutation {
	return auo.mutation
}

// ClearRoles clears all "roles" edges to the Role entity.
func (auo *APIUpdateOne) ClearRoles() *APIUpdateOne {
	auo.mutation.ClearRoles()
	return auo
}

// RemoveRoleIDs removes the "roles" edge to Role entities by IDs.
func (auo *APIUpdateOne) RemoveRoleIDs(ids ...string) *APIUpdateOne {
	auo.mutation.RemoveRoleIDs(ids...)
	return auo
}

// RemoveRoles removes "roles" edges to Role entities.
func (auo *APIUpdateOne) RemoveRoles(r ...*Role) *APIUpdateOne {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return auo.RemoveRoleIDs(ids...)
}

// Where appends a list predicates to the APIUpdate builder.
func (auo *APIUpdateOne) Where(ps ...predicate.API) *APIUpdateOne {
	auo.mutation.Where(ps...)
	return auo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (auo *APIUpdateOne) Select(field string, fields ...string) *APIUpdateOne {
	auo.fields = append([]string{field}, fields...)
	return auo
}

// Save executes the query and returns the updated API entity.
func (auo *APIUpdateOne) Save(ctx context.Context) (*API, error) {
	if err := auo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, auo.sqlSave, auo.mutation, auo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (auo *APIUpdateOne) SaveX(ctx context.Context) *API {
	node, err := auo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (auo *APIUpdateOne) Exec(ctx context.Context) error {
	_, err := auo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (auo *APIUpdateOne) ExecX(ctx context.Context) {
	if err := auo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (auo *APIUpdateOne) defaults() error {
	if _, ok := auo.mutation.UpdatedAt(); !ok {
		if api.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized api.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := api.UpdateDefaultUpdatedAt()
		auo.mutation.SetUpdatedAt(v)
	}
	return nil
}

func (auo *APIUpdateOne) sqlSave(ctx context.Context) (_node *API, err error) {
	_spec := sqlgraph.NewUpdateSpec(api.Table, api.Columns, sqlgraph.NewFieldSpec(api.FieldID, field.TypeString))
	id, ok := auo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "API.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := auo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, api.FieldID)
		for _, f := range fields {
			if !api.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != api.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := auo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := auo.mutation.UpdatedAt(); ok {
		_spec.SetField(api.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := auo.mutation.Status(); ok {
		_spec.SetField(api.FieldStatus, field.TypeBool, value)
	}
	if auo.mutation.StatusCleared() {
		_spec.ClearField(api.FieldStatus, field.TypeBool)
	}
	if value, ok := auo.mutation.DeletedAt(); ok {
		_spec.SetField(api.FieldDeletedAt, field.TypeTime, value)
	}
	if auo.mutation.DeletedAtCleared() {
		_spec.ClearField(api.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := auo.mutation.Path(); ok {
		_spec.SetField(api.FieldPath, field.TypeString, value)
	}
	if value, ok := auo.mutation.Description(); ok {
		_spec.SetField(api.FieldDescription, field.TypeString, value)
	}
	if value, ok := auo.mutation.APIGroup(); ok {
		_spec.SetField(api.FieldAPIGroup, field.TypeString, value)
	}
	if value, ok := auo.mutation.Method(); ok {
		_spec.SetField(api.FieldMethod, field.TypeString, value)
	}
	if value, ok := auo.mutation.Kind(); ok {
		_spec.SetField(api.FieldKind, field.TypeString, value)
	}
	if value, ok := auo.mutation.Module(); ok {
		_spec.SetField(api.FieldModule, field.TypeString, value)
	}
	if auo.mutation.RolesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   api.RolesTable,
			Columns: api.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := auo.mutation.RemovedRolesIDs(); len(nodes) > 0 && !auo.mutation.RolesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   api.RolesTable,
			Columns: api.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := auo.mutation.RolesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   api.RolesTable,
			Columns: api.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &API{config: auo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, auo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{api.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	auo.mutation.done = true
	return _node, nil
}
