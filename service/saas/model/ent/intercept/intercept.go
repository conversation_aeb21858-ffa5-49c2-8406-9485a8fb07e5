// Code generated by ent, DO NOT EDIT.

package intercept

import (
	"context"
	"fmt"
	"phoenix/service/saas/model/ent"
	"phoenix/service/saas/model/ent/api"
	"phoenix/service/saas/model/ent/application"
	"phoenix/service/saas/model/ent/button"
	"phoenix/service/saas/model/ent/file"
	"phoenix/service/saas/model/ent/group"
	"phoenix/service/saas/model/ent/grouptype"
	"phoenix/service/saas/model/ent/menu"
	"phoenix/service/saas/model/ent/organization"
	"phoenix/service/saas/model/ent/organizationuserinfo"
	"phoenix/service/saas/model/ent/position"
	"phoenix/service/saas/model/ent/predicate"
	"phoenix/service/saas/model/ent/role"
	"phoenix/service/saas/model/ent/tenant"
	"phoenix/service/saas/model/ent/tenantuserinfo"
	"phoenix/service/saas/model/ent/token"
	"phoenix/service/saas/model/ent/user"

	"entgo.io/ent/dialect/sql"
)

// The Query interface represents an operation that queries a graph.
// By using this interface, users can write generic code that manipulates
// query builders of different types.
type Query interface {
	// Type returns the string representation of the query type.
	Type() string
	// Limit the number of records to be returned by this query.
	Limit(int)
	// Offset to start from.
	Offset(int)
	// Unique configures the query builder to filter duplicate records.
	Unique(bool)
	// Order specifies how the records should be ordered.
	Order(...func(*sql.Selector))
	// WhereP appends storage-level predicates to the query builder. Using this method, users
	// can use type-assertion to append predicates that do not depend on any generated package.
	WhereP(...func(*sql.Selector))
}

// The Func type is an adapter that allows ordinary functions to be used as interceptors.
// Unlike traversal functions, interceptors are skipped during graph traversals. Note that the
// implementation of Func is different from the one defined in entgo.io/ent.InterceptFunc.
type Func func(context.Context, Query) error

// Intercept calls f(ctx, q) and then applied the next Querier.
func (f Func) Intercept(next ent.Querier) ent.Querier {
	return ent.QuerierFunc(func(ctx context.Context, q ent.Query) (ent.Value, error) {
		query, err := NewQuery(q)
		if err != nil {
			return nil, err
		}
		if err := f(ctx, query); err != nil {
			return nil, err
		}
		return next.Query(ctx, q)
	})
}

// The TraverseFunc type is an adapter to allow the use of ordinary function as Traverser.
// If f is a function with the appropriate signature, TraverseFunc(f) is a Traverser that calls f.
type TraverseFunc func(context.Context, Query) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseFunc) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseFunc) Traverse(ctx context.Context, q ent.Query) error {
	query, err := NewQuery(q)
	if err != nil {
		return err
	}
	return f(ctx, query)
}

// The APIFunc type is an adapter to allow the use of ordinary function as a Querier.
type APIFunc func(context.Context, *ent.APIQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f APIFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.APIQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.APIQuery", q)
}

// The TraverseAPI type is an adapter to allow the use of ordinary function as Traverser.
type TraverseAPI func(context.Context, *ent.APIQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseAPI) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseAPI) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.APIQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.APIQuery", q)
}

// The ApplicationFunc type is an adapter to allow the use of ordinary function as a Querier.
type ApplicationFunc func(context.Context, *ent.ApplicationQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f ApplicationFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.ApplicationQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.ApplicationQuery", q)
}

// The TraverseApplication type is an adapter to allow the use of ordinary function as Traverser.
type TraverseApplication func(context.Context, *ent.ApplicationQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseApplication) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseApplication) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.ApplicationQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.ApplicationQuery", q)
}

// The ButtonFunc type is an adapter to allow the use of ordinary function as a Querier.
type ButtonFunc func(context.Context, *ent.ButtonQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f ButtonFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.ButtonQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.ButtonQuery", q)
}

// The TraverseButton type is an adapter to allow the use of ordinary function as Traverser.
type TraverseButton func(context.Context, *ent.ButtonQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseButton) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseButton) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.ButtonQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.ButtonQuery", q)
}

// The FileFunc type is an adapter to allow the use of ordinary function as a Querier.
type FileFunc func(context.Context, *ent.FileQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f FileFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.FileQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.FileQuery", q)
}

// The TraverseFile type is an adapter to allow the use of ordinary function as Traverser.
type TraverseFile func(context.Context, *ent.FileQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseFile) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseFile) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.FileQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.FileQuery", q)
}

// The GroupFunc type is an adapter to allow the use of ordinary function as a Querier.
type GroupFunc func(context.Context, *ent.GroupQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f GroupFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.GroupQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.GroupQuery", q)
}

// The TraverseGroup type is an adapter to allow the use of ordinary function as Traverser.
type TraverseGroup func(context.Context, *ent.GroupQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseGroup) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseGroup) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.GroupQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.GroupQuery", q)
}

// The GroupTypeFunc type is an adapter to allow the use of ordinary function as a Querier.
type GroupTypeFunc func(context.Context, *ent.GroupTypeQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f GroupTypeFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.GroupTypeQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.GroupTypeQuery", q)
}

// The TraverseGroupType type is an adapter to allow the use of ordinary function as Traverser.
type TraverseGroupType func(context.Context, *ent.GroupTypeQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseGroupType) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseGroupType) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.GroupTypeQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.GroupTypeQuery", q)
}

// The MenuFunc type is an adapter to allow the use of ordinary function as a Querier.
type MenuFunc func(context.Context, *ent.MenuQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f MenuFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.MenuQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.MenuQuery", q)
}

// The TraverseMenu type is an adapter to allow the use of ordinary function as Traverser.
type TraverseMenu func(context.Context, *ent.MenuQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseMenu) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseMenu) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.MenuQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.MenuQuery", q)
}

// The OrganizationFunc type is an adapter to allow the use of ordinary function as a Querier.
type OrganizationFunc func(context.Context, *ent.OrganizationQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f OrganizationFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.OrganizationQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.OrganizationQuery", q)
}

// The TraverseOrganization type is an adapter to allow the use of ordinary function as Traverser.
type TraverseOrganization func(context.Context, *ent.OrganizationQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseOrganization) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseOrganization) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.OrganizationQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.OrganizationQuery", q)
}

// The OrganizationUserInfoFunc type is an adapter to allow the use of ordinary function as a Querier.
type OrganizationUserInfoFunc func(context.Context, *ent.OrganizationUserInfoQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f OrganizationUserInfoFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.OrganizationUserInfoQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.OrganizationUserInfoQuery", q)
}

// The TraverseOrganizationUserInfo type is an adapter to allow the use of ordinary function as Traverser.
type TraverseOrganizationUserInfo func(context.Context, *ent.OrganizationUserInfoQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseOrganizationUserInfo) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseOrganizationUserInfo) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.OrganizationUserInfoQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.OrganizationUserInfoQuery", q)
}

// The PositionFunc type is an adapter to allow the use of ordinary function as a Querier.
type PositionFunc func(context.Context, *ent.PositionQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f PositionFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.PositionQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.PositionQuery", q)
}

// The TraversePosition type is an adapter to allow the use of ordinary function as Traverser.
type TraversePosition func(context.Context, *ent.PositionQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraversePosition) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraversePosition) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.PositionQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.PositionQuery", q)
}

// The RoleFunc type is an adapter to allow the use of ordinary function as a Querier.
type RoleFunc func(context.Context, *ent.RoleQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f RoleFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.RoleQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.RoleQuery", q)
}

// The TraverseRole type is an adapter to allow the use of ordinary function as Traverser.
type TraverseRole func(context.Context, *ent.RoleQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseRole) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseRole) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.RoleQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.RoleQuery", q)
}

// The TenantFunc type is an adapter to allow the use of ordinary function as a Querier.
type TenantFunc func(context.Context, *ent.TenantQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f TenantFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.TenantQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.TenantQuery", q)
}

// The TraverseTenant type is an adapter to allow the use of ordinary function as Traverser.
type TraverseTenant func(context.Context, *ent.TenantQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseTenant) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseTenant) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.TenantQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.TenantQuery", q)
}

// The TenantUserInfoFunc type is an adapter to allow the use of ordinary function as a Querier.
type TenantUserInfoFunc func(context.Context, *ent.TenantUserInfoQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f TenantUserInfoFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.TenantUserInfoQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.TenantUserInfoQuery", q)
}

// The TraverseTenantUserInfo type is an adapter to allow the use of ordinary function as Traverser.
type TraverseTenantUserInfo func(context.Context, *ent.TenantUserInfoQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseTenantUserInfo) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseTenantUserInfo) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.TenantUserInfoQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.TenantUserInfoQuery", q)
}

// The TokenFunc type is an adapter to allow the use of ordinary function as a Querier.
type TokenFunc func(context.Context, *ent.TokenQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f TokenFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.TokenQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.TokenQuery", q)
}

// The TraverseToken type is an adapter to allow the use of ordinary function as Traverser.
type TraverseToken func(context.Context, *ent.TokenQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseToken) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseToken) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.TokenQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.TokenQuery", q)
}

// The UserFunc type is an adapter to allow the use of ordinary function as a Querier.
type UserFunc func(context.Context, *ent.UserQuery) (ent.Value, error)

// Query calls f(ctx, q).
func (f UserFunc) Query(ctx context.Context, q ent.Query) (ent.Value, error) {
	if q, ok := q.(*ent.UserQuery); ok {
		return f(ctx, q)
	}
	return nil, fmt.Errorf("unexpected query type %T. expect *ent.UserQuery", q)
}

// The TraverseUser type is an adapter to allow the use of ordinary function as Traverser.
type TraverseUser func(context.Context, *ent.UserQuery) error

// Intercept is a dummy implementation of Intercept that returns the next Querier in the pipeline.
func (f TraverseUser) Intercept(next ent.Querier) ent.Querier {
	return next
}

// Traverse calls f(ctx, q).
func (f TraverseUser) Traverse(ctx context.Context, q ent.Query) error {
	if q, ok := q.(*ent.UserQuery); ok {
		return f(ctx, q)
	}
	return fmt.Errorf("unexpected query type %T. expect *ent.UserQuery", q)
}

// NewQuery returns the generic Query interface for the given typed query.
func NewQuery(q ent.Query) (Query, error) {
	switch q := q.(type) {
	case *ent.APIQuery:
		return &query[*ent.APIQuery, predicate.API, api.OrderOption]{typ: ent.TypeAPI, tq: q}, nil
	case *ent.ApplicationQuery:
		return &query[*ent.ApplicationQuery, predicate.Application, application.OrderOption]{typ: ent.TypeApplication, tq: q}, nil
	case *ent.ButtonQuery:
		return &query[*ent.ButtonQuery, predicate.Button, button.OrderOption]{typ: ent.TypeButton, tq: q}, nil
	case *ent.FileQuery:
		return &query[*ent.FileQuery, predicate.File, file.OrderOption]{typ: ent.TypeFile, tq: q}, nil
	case *ent.GroupQuery:
		return &query[*ent.GroupQuery, predicate.Group, group.OrderOption]{typ: ent.TypeGroup, tq: q}, nil
	case *ent.GroupTypeQuery:
		return &query[*ent.GroupTypeQuery, predicate.GroupType, grouptype.OrderOption]{typ: ent.TypeGroupType, tq: q}, nil
	case *ent.MenuQuery:
		return &query[*ent.MenuQuery, predicate.Menu, menu.OrderOption]{typ: ent.TypeMenu, tq: q}, nil
	case *ent.OrganizationQuery:
		return &query[*ent.OrganizationQuery, predicate.Organization, organization.OrderOption]{typ: ent.TypeOrganization, tq: q}, nil
	case *ent.OrganizationUserInfoQuery:
		return &query[*ent.OrganizationUserInfoQuery, predicate.OrganizationUserInfo, organizationuserinfo.OrderOption]{typ: ent.TypeOrganizationUserInfo, tq: q}, nil
	case *ent.PositionQuery:
		return &query[*ent.PositionQuery, predicate.Position, position.OrderOption]{typ: ent.TypePosition, tq: q}, nil
	case *ent.RoleQuery:
		return &query[*ent.RoleQuery, predicate.Role, role.OrderOption]{typ: ent.TypeRole, tq: q}, nil
	case *ent.TenantQuery:
		return &query[*ent.TenantQuery, predicate.Tenant, tenant.OrderOption]{typ: ent.TypeTenant, tq: q}, nil
	case *ent.TenantUserInfoQuery:
		return &query[*ent.TenantUserInfoQuery, predicate.TenantUserInfo, tenantuserinfo.OrderOption]{typ: ent.TypeTenantUserInfo, tq: q}, nil
	case *ent.TokenQuery:
		return &query[*ent.TokenQuery, predicate.Token, token.OrderOption]{typ: ent.TypeToken, tq: q}, nil
	case *ent.UserQuery:
		return &query[*ent.UserQuery, predicate.User, user.OrderOption]{typ: ent.TypeUser, tq: q}, nil
	default:
		return nil, fmt.Errorf("unknown query type %T", q)
	}
}

type query[T any, P ~func(*sql.Selector), R ~func(*sql.Selector)] struct {
	typ string
	tq  interface {
		Limit(int) T
		Offset(int) T
		Unique(bool) T
		Order(...R) T
		Where(...P) T
	}
}

func (q query[T, P, R]) Type() string {
	return q.typ
}

func (q query[T, P, R]) Limit(limit int) {
	q.tq.Limit(limit)
}

func (q query[T, P, R]) Offset(offset int) {
	q.tq.Offset(offset)
}

func (q query[T, P, R]) Unique(unique bool) {
	q.tq.Unique(unique)
}

func (q query[T, P, R]) Order(orders ...func(*sql.Selector)) {
	rs := make([]R, len(orders))
	for i := range orders {
		rs[i] = orders[i]
	}
	q.tq.Order(rs...)
}

func (q query[T, P, R]) WhereP(ps ...func(*sql.Selector)) {
	p := make([]P, len(ps))
	for i := range ps {
		p[i] = ps[i]
	}
	q.tq.Where(p...)
}
