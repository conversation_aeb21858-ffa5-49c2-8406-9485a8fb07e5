// Code generated by ent, DO NOT EDIT.

package migrate

import (
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/dialect/sql/schema"
	"entgo.io/ent/schema/field"
)

var (
	// SaasAPIColumns holds the columns for the "saas_api" table.
	SaasAPIColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Comment: "Snowflake ID | 全局唯一ID"},
		{Name: "created_at", Type: field.TypeTime, Comment: "Created Time | 创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "Updated Time | 更新时间"},
		{Name: "status", Type: field.TypeBool, Nullable: true, Comment: "status true normal false ban | 状态  正常/禁用", Default: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true, Comment: "Deleted Time | 删除时间（软删除标识）"},
		{Name: "path", Type: field.TypeString, Comment: "API path | API 路径"},
		{Name: "description", Type: field.TypeString, Comment: "API description | API 描述"},
		{Name: "api_group", Type: field.TypeString, Comment: "API group | API 分组"},
		{Name: "method", Type: field.TypeString, Comment: "HTTP method | HTTP 请求类型", Default: "POST"},
		{Name: "kind", Type: field.TypeString, Comment: "API kind | 操作类型 如（新增、修改、查询、删除）"},
		{Name: "module", Type: field.TypeString, Comment: "API module | 操作模块"},
		{Name: "tenant_apis", Type: field.TypeString, Nullable: true},
	}
	// SaasAPITable holds the schema information for the "saas_api" table.
	SaasAPITable = &schema.Table{
		Name:       "saas_api",
		Columns:    SaasAPIColumns,
		PrimaryKey: []*schema.Column{SaasAPIColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "saas_api_saas_tenant_apis",
				Columns:    []*schema.Column{SaasAPIColumns[11]},
				RefColumns: []*schema.Column{SaasTenantColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "api_path_method",
				Unique:  true,
				Columns: []*schema.Column{SaasAPIColumns[5], SaasAPIColumns[8]},
			},
		},
	}
	// SaasApplicationColumns holds the columns for the "saas_application" table.
	SaasApplicationColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Comment: "Snowflake ID | 全局唯一ID"},
		{Name: "created_at", Type: field.TypeTime, Comment: "Created Time | 创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "Updated Time | 更新时间"},
		{Name: "status", Type: field.TypeBool, Nullable: true, Comment: "status true normal false ban | 状态  正常/禁用", Default: true},
		{Name: "sort", Type: field.TypeUint32, Comment: "Sort number | 排序编号", Default: 1},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true, Comment: "Deleted Time | 删除时间（软删除标识）"},
		{Name: "name", Type: field.TypeString, Comment: "app name | 应用名", Default: ""},
		{Name: "app_id", Type: field.TypeString, Unique: true, Comment: "APP ID"},
		{Name: "secret", Type: field.TypeString, Comment: "secret | 应用密钥"},
		{Name: "remark", Type: field.TypeString, Comment: "remark | 备注", Default: ""},
		{Name: "tenant_id", Type: field.TypeString, Comment: "Tenant ID"},
	}
	// SaasApplicationTable holds the schema information for the "saas_application" table.
	SaasApplicationTable = &schema.Table{
		Name:       "saas_application",
		Columns:    SaasApplicationColumns,
		PrimaryKey: []*schema.Column{SaasApplicationColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "saas_application_saas_tenant_tenant",
				Columns:    []*schema.Column{SaasApplicationColumns[10]},
				RefColumns: []*schema.Column{SaasTenantColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "application_app_id",
				Unique:  true,
				Columns: []*schema.Column{SaasApplicationColumns[7]},
			},
		},
	}
	// SaasButtonColumns holds the columns for the "saas_button" table.
	SaasButtonColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Comment: "Snowflake ID | 全局唯一ID"},
		{Name: "created_at", Type: field.TypeTime, Comment: "Created Time | 创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "Updated Time | 更新时间"},
		{Name: "sort", Type: field.TypeUint32, Comment: "Sort number | 排序编号", Default: 1},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true, Comment: "Deleted Time | 删除时间（软删除标识）"},
		{Name: "name", Type: field.TypeString, Nullable: true, Comment: "name | 按钮名称", Default: ""},
		{Name: "code", Type: field.TypeString, Comment: "code | 按钮CODE"},
		{Name: "menu_id", Type: field.TypeString, Nullable: true, Comment: "Menu ID | 菜单ID"},
		{Name: "tenant_buttons", Type: field.TypeString, Nullable: true},
	}
	// SaasButtonTable holds the schema information for the "saas_button" table.
	SaasButtonTable = &schema.Table{
		Name:       "saas_button",
		Columns:    SaasButtonColumns,
		PrimaryKey: []*schema.Column{SaasButtonColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "saas_button_saas_menu_buttons",
				Columns:    []*schema.Column{SaasButtonColumns[7]},
				RefColumns: []*schema.Column{SaasMenuColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "saas_button_saas_tenant_buttons",
				Columns:    []*schema.Column{SaasButtonColumns[8]},
				RefColumns: []*schema.Column{SaasTenantColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// FmsFileColumns holds the columns for the "fms_file" table.
	FmsFileColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Comment: "Snowflake ID | 全局唯一ID"},
		{Name: "created_at", Type: field.TypeTime, Comment: "Created Time | 创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "Updated Time | 更新时间"},
		{Name: "status", Type: field.TypeBool, Nullable: true, Comment: "status true normal false ban | 状态  正常/禁用", Default: true},
		{Name: "sort", Type: field.TypeUint32, Comment: "Sort number | 排序编号", Default: 1},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true, Comment: "Deleted Time | 删除时间（软删除标识）"},
		{Name: "uuid", Type: field.TypeString, Comment: "File's UUID | 文件的UUID"},
		{Name: "name", Type: field.TypeString, Comment: "File's name | 文件展示名称"},
		{Name: "origin_name", Type: field.TypeString, Comment: "File's origin name | 文件原始名称"},
		{Name: "file_type", Type: field.TypeUint8, Comment: "File's type | 文件类型"},
		{Name: "size", Type: field.TypeUint64, Comment: "File's size |  文件大小"},
		{Name: "path", Type: field.TypeString, Comment: "File's path | 文件相对路径"},
		{Name: "hash", Type: field.TypeString, Comment: "The hash of the file | 文件的 hash"},
		{Name: "open_status", Type: field.TypeUint8, Nullable: true, Comment: "status 1 private 2 public | 状态 1 私有 2 公开", Default: 1},
		{Name: "tenant_id", Type: field.TypeString, Comment: "Tenant ID"},
		{Name: "user_id", Type: field.TypeString, Nullable: true, Comment: "User's ID | 用户ID"},
	}
	// FmsFileTable holds the schema information for the "fms_file" table.
	FmsFileTable = &schema.Table{
		Name:       "fms_file",
		Columns:    FmsFileColumns,
		PrimaryKey: []*schema.Column{FmsFileColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "fms_file_saas_tenant_tenant",
				Columns:    []*schema.Column{FmsFileColumns[14]},
				RefColumns: []*schema.Column{SaasTenantColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "fms_file_saas_user_files",
				Columns:    []*schema.Column{FmsFileColumns[15]},
				RefColumns: []*schema.Column{SaasUserColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// SaasGroupColumns holds the columns for the "saas_group" table.
	SaasGroupColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Comment: "Snowflake ID | 全局唯一ID"},
		{Name: "created_at", Type: field.TypeTime, Comment: "Created Time | 创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "Updated Time | 更新时间"},
		{Name: "status", Type: field.TypeBool, Nullable: true, Comment: "status true normal false ban | 状态  正常/禁用", Default: true},
		{Name: "sort", Type: field.TypeUint32, Comment: "Sort number | 排序编号", Default: 1},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true, Comment: "Deleted Time | 删除时间（软删除标识）"},
		{Name: "name", Type: field.TypeString, Comment: "Position Name | 组名称"},
		{Name: "code", Type: field.TypeString, Comment: "The code of position | 组编码"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "Remark | 备注", Default: ""},
		{Name: "tenant_id", Type: field.TypeString, Comment: "Tenant ID"},
		{Name: "group_type_id", Type: field.TypeString, Nullable: true, Comment: "GroupType ID | 组类型ID"},
	}
	// SaasGroupTable holds the schema information for the "saas_group" table.
	SaasGroupTable = &schema.Table{
		Name:       "saas_group",
		Columns:    SaasGroupColumns,
		PrimaryKey: []*schema.Column{SaasGroupColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "saas_group_saas_tenant_tenant",
				Columns:    []*schema.Column{SaasGroupColumns[9]},
				RefColumns: []*schema.Column{SaasTenantColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "saas_group_saas_group_type_groups",
				Columns:    []*schema.Column{SaasGroupColumns[10]},
				RefColumns: []*schema.Column{SaasGroupTypeColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "group_code_tenant_id",
				Unique:  true,
				Columns: []*schema.Column{SaasGroupColumns[7], SaasGroupColumns[9]},
			},
		},
	}
	// SaasGroupTypeColumns holds the columns for the "saas_group_type" table.
	SaasGroupTypeColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Comment: "Snowflake ID | 全局唯一ID"},
		{Name: "created_at", Type: field.TypeTime, Comment: "Created Time | 创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "Updated Time | 更新时间"},
		{Name: "status", Type: field.TypeBool, Nullable: true, Comment: "status true normal false ban | 状态  正常/禁用", Default: true},
		{Name: "sort", Type: field.TypeUint32, Comment: "Sort number | 排序编号", Default: 1},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true, Comment: "Deleted Time | 删除时间（软删除标识）"},
		{Name: "name", Type: field.TypeString, Comment: "Position Name | 组分类名称"},
		{Name: "code", Type: field.TypeString, Comment: "The code of group type | 组分类编码"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "Remark | 备注", Default: ""},
		{Name: "tenant_id", Type: field.TypeString, Comment: "Tenant ID"},
	}
	// SaasGroupTypeTable holds the schema information for the "saas_group_type" table.
	SaasGroupTypeTable = &schema.Table{
		Name:       "saas_group_type",
		Columns:    SaasGroupTypeColumns,
		PrimaryKey: []*schema.Column{SaasGroupTypeColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "saas_group_type_saas_tenant_tenant",
				Columns:    []*schema.Column{SaasGroupTypeColumns[9]},
				RefColumns: []*schema.Column{SaasTenantColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "grouptype_code",
				Unique:  true,
				Columns: []*schema.Column{SaasGroupTypeColumns[7]},
			},
		},
	}
	// SaasMenuColumns holds the columns for the "saas_menu" table.
	SaasMenuColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Comment: "Snowflake ID | 全局唯一ID"},
		{Name: "created_at", Type: field.TypeTime, Comment: "Created Time | 创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "Updated Time | 更新时间"},
		{Name: "sort", Type: field.TypeUint32, Comment: "Sort number | 排序编号", Default: 1},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true, Comment: "Deleted Time | 删除时间（软删除标识）"},
		{Name: "name", Type: field.TypeString, Comment: "menu name | 菜单名称"},
		{Name: "title", Type: field.TypeString, Comment: "menu title | 菜单标题"},
		{Name: "icon", Type: field.TypeString, Comment: "menu icon | 菜单图标"},
		{Name: "menu_type", Type: field.TypeUint32, Comment: "menu type | 菜单类型 （菜单或目录）0 目录 1 菜单"},
		{Name: "url", Type: field.TypeString, Nullable: true, Comment: "index path | 菜单路由路径", Default: ""},
		{Name: "redirect", Type: field.TypeString, Nullable: true, Comment: "redirect path | 跳转路径 （外链）", Default: ""},
		{Name: "component", Type: field.TypeString, Nullable: true, Comment: "the path of vue file | 组件路径", Default: ""},
		{Name: "is_active", Type: field.TypeBool, Nullable: true, Comment: "is_active | 是否激活", Default: true},
		{Name: "hidden", Type: field.TypeBool, Nullable: true, Comment: "hidden | 是否隐藏", Default: false},
		{Name: "hidden_in_tab", Type: field.TypeBool, Nullable: true, Comment: "hidden_in_tab | 是否隐藏标签", Default: false},
		{Name: "fixed", Type: field.TypeBool, Nullable: true, Comment: "fixed | Fixed", Default: false},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "remark | 备注", Default: ""},
		{Name: "meta", Type: field.TypeString, Nullable: true, Size: 2147483647, Comment: "meta data | 菜单Meta信息", Default: "{}"},
		{Name: "is_full_page", Type: field.TypeBool, Nullable: true, Comment: "is_full_page | 是否全屏", Default: false},
		{Name: "parent_id", Type: field.TypeString, Nullable: true, Comment: "parent menu ID | 父菜单ID"},
		{Name: "tenant_menus", Type: field.TypeString, Nullable: true},
	}
	// SaasMenuTable holds the schema information for the "saas_menu" table.
	SaasMenuTable = &schema.Table{
		Name:       "saas_menu",
		Columns:    SaasMenuColumns,
		PrimaryKey: []*schema.Column{SaasMenuColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "saas_menu_saas_menu_children",
				Columns:    []*schema.Column{SaasMenuColumns[19]},
				RefColumns: []*schema.Column{SaasMenuColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "saas_menu_saas_tenant_menus",
				Columns:    []*schema.Column{SaasMenuColumns[20]},
				RefColumns: []*schema.Column{SaasTenantColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// SaasOrganizationColumns holds the columns for the "saas_organization" table.
	SaasOrganizationColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Comment: "Snowflake ID | 全局唯一ID"},
		{Name: "created_at", Type: field.TypeTime, Comment: "Created Time | 创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "Updated Time | 更新时间"},
		{Name: "status", Type: field.TypeBool, Nullable: true, Comment: "status true normal false ban | 状态  正常/禁用", Default: true},
		{Name: "sort", Type: field.TypeUint32, Comment: "Sort number | 排序编号", Default: 1},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true, Comment: "Deleted Time | 删除时间（软删除标识）"},
		{Name: "name", Type: field.TypeString, Comment: "Organization name | 部门名称"},
		{Name: "ancestors", Type: field.TypeString, Comment: "Parents' IDs | 父级列表"},
		{Name: "code", Type: field.TypeString, Comment: "Code | 组织架构节点编码（可作为扩展其他用户体系，比如钉钉，企业微信等）"},
		{Name: "node_type", Type: field.TypeUint32, Comment: "Node type | 组织架构类型 （单位或部门）0 单位 1 部门"},
		{Name: "leader", Type: field.TypeString, Comment: "Organization leader | 部门负责人"},
		{Name: "phone", Type: field.TypeString, Comment: "Leader's phone number | 负责人电话"},
		{Name: "email", Type: field.TypeString, Comment: "Leader's email | 部门负责人电子邮箱"},
		{Name: "remark", Type: field.TypeString, Comment: "Remark | 备注"},
		{Name: "tenant_id", Type: field.TypeString, Comment: "Tenant ID"},
		{Name: "parent_id", Type: field.TypeString, Nullable: true, Comment: "Parent organization ID | 父级部门ID"},
	}
	// SaasOrganizationTable holds the schema information for the "saas_organization" table.
	SaasOrganizationTable = &schema.Table{
		Name:       "saas_organization",
		Columns:    SaasOrganizationColumns,
		PrimaryKey: []*schema.Column{SaasOrganizationColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "saas_organization_saas_tenant_tenant",
				Columns:    []*schema.Column{SaasOrganizationColumns[14]},
				RefColumns: []*schema.Column{SaasTenantColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "saas_organization_saas_organization_children",
				Columns:    []*schema.Column{SaasOrganizationColumns[15]},
				RefColumns: []*schema.Column{SaasOrganizationColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// SaasOrganizationUserInfoColumns holds the columns for the "saas_organization_user_info" table.
	SaasOrganizationUserInfoColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Comment: "Snowflake ID | 全局唯一ID"},
		{Name: "created_at", Type: field.TypeTime, Comment: "Created Time | 创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "Updated Time | 更新时间"},
		{Name: "sort", Type: field.TypeUint32, Comment: "Sort number | 排序编号", Default: 1},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true, Comment: "Deleted Time | 删除时间（软删除标识）"},
		{Name: "extra", Type: field.TypeString, Nullable: true, Size: 2147483647, Comment: "Extra information | 额外信息(json对象格式存储，用于存储的额外展示信息)", Default: "{}"},
		{Name: "is_leader", Type: field.TypeBool, Comment: "Is Leader | 是否领导", Default: false},
		{Name: "is_admin", Type: field.TypeBool, Comment: "Is Admin | 是否管理员", Default: false},
		{Name: "organization_id", Type: field.TypeString, Comment: "Organization ID | 组织架构 ID"},
		{Name: "user_id", Type: field.TypeString, Comment: "User ID | 用户 ID"},
	}
	// SaasOrganizationUserInfoTable holds the schema information for the "saas_organization_user_info" table.
	SaasOrganizationUserInfoTable = &schema.Table{
		Name:       "saas_organization_user_info",
		Columns:    SaasOrganizationUserInfoColumns,
		PrimaryKey: []*schema.Column{SaasOrganizationUserInfoColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "saas_organization_user_info_saas_organization_organization_infos",
				Columns:    []*schema.Column{SaasOrganizationUserInfoColumns[8]},
				RefColumns: []*schema.Column{SaasOrganizationColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "saas_organization_user_info_saas_user_organization_infos",
				Columns:    []*schema.Column{SaasOrganizationUserInfoColumns[9]},
				RefColumns: []*schema.Column{SaasUserColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
	}
	// SaasPositionColumns holds the columns for the "saas_position" table.
	SaasPositionColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Comment: "Snowflake ID | 全局唯一ID"},
		{Name: "created_at", Type: field.TypeTime, Comment: "Created Time | 创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "Updated Time | 更新时间"},
		{Name: "status", Type: field.TypeBool, Nullable: true, Comment: "status true normal false ban | 状态  正常/禁用", Default: true},
		{Name: "sort", Type: field.TypeUint32, Comment: "Sort number | 排序编号", Default: 1},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true, Comment: "Deleted Time | 删除时间（软删除标识）"},
		{Name: "name", Type: field.TypeString, Comment: "Position Name | 职位名称"},
		{Name: "code", Type: field.TypeString, Comment: "The code of position | 职位编码"},
		{Name: "organization_id", Type: field.TypeString, Comment: "The id of organization | 组织id"},
		{Name: "remark", Type: field.TypeString, Nullable: true, Comment: "Remark | 备注", Default: ""},
		{Name: "tenant_id", Type: field.TypeString, Comment: "Tenant ID"},
	}
	// SaasPositionTable holds the schema information for the "saas_position" table.
	SaasPositionTable = &schema.Table{
		Name:       "saas_position",
		Columns:    SaasPositionColumns,
		PrimaryKey: []*schema.Column{SaasPositionColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "saas_position_saas_tenant_tenant",
				Columns:    []*schema.Column{SaasPositionColumns[10]},
				RefColumns: []*schema.Column{SaasTenantColumns[0]},
				OnDelete:   schema.NoAction,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "position_code",
				Unique:  true,
				Columns: []*schema.Column{SaasPositionColumns[7]},
			},
		},
	}
	// SaasRoleColumns holds the columns for the "saas_role" table.
	SaasRoleColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Comment: "Snowflake ID | 全局唯一ID"},
		{Name: "created_at", Type: field.TypeTime, Comment: "Created Time | 创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "Updated Time | 更新时间"},
		{Name: "status", Type: field.TypeBool, Nullable: true, Comment: "status true normal false ban | 状态  正常/禁用", Default: true},
		{Name: "sort", Type: field.TypeUint32, Comment: "Sort number | 排序编号", Default: 1},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true, Comment: "Deleted Time | 删除时间（软删除标识）"},
		{Name: "name", Type: field.TypeString, Comment: "role name | 角色名", Default: ""},
		{Name: "code", Type: field.TypeString, Unique: true, Comment: "role code for permission control in front end | 角色码，用于前端权限控制"},
		{Name: "uid", Type: field.TypeUUID, Comment: "UUID"},
		{Name: "default_router", Type: field.TypeString, Comment: "default menu : dashboard | 默认登录页面", Default: "dashboard"},
		{Name: "remark", Type: field.TypeString, Comment: "remark | 备注", Default: ""},
		{Name: "organization_id", Type: field.TypeString, Comment: "组织id", Default: ""},
		{Name: "tenant_id", Type: field.TypeString, Comment: "Tenant ID"},
		{Name: "parent_id", Type: field.TypeString, Nullable: true, Comment: "Parent role ID | 父级角色ID"},
	}
	// SaasRoleTable holds the schema information for the "saas_role" table.
	SaasRoleTable = &schema.Table{
		Name:       "saas_role",
		Columns:    SaasRoleColumns,
		PrimaryKey: []*schema.Column{SaasRoleColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "saas_role_saas_tenant_tenant",
				Columns:    []*schema.Column{SaasRoleColumns[12]},
				RefColumns: []*schema.Column{SaasTenantColumns[0]},
				OnDelete:   schema.NoAction,
			},
			{
				Symbol:     "saas_role_saas_role_children",
				Columns:    []*schema.Column{SaasRoleColumns[13]},
				RefColumns: []*schema.Column{SaasRoleColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "role_code_tenant_id",
				Unique:  true,
				Columns: []*schema.Column{SaasRoleColumns[7], SaasRoleColumns[12]},
			},
		},
	}
	// SaasTenantColumns holds the columns for the "saas_tenant" table.
	SaasTenantColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Comment: "Snowflake ID | 全局唯一ID"},
		{Name: "created_at", Type: field.TypeTime, Comment: "Created Time | 创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "Updated Time | 更新时间"},
		{Name: "uuid", Type: field.TypeUUID, Comment: "UUID"},
		{Name: "key", Type: field.TypeString, Comment: "key", SchemaType: map[string]string{"mysql": "char(32)"}},
		{Name: "secret", Type: field.TypeString, Nullable: true, Comment: "secret | 授权secret", SchemaType: map[string]string{"mysql": "varchar(300)"}},
		{Name: "status", Type: field.TypeBool, Nullable: true, Comment: "status true normal false ban | 状态  正常/禁用", Default: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true, Comment: "Deleted Time | 删除时间（软删除标识）"},
		{Name: "name", Type: field.TypeString, Nullable: true, Comment: "Tenant's name | 租户名称", Default: "", SchemaType: map[string]string{"mysql": "varchar(500)"}},
		{Name: "is_super", Type: field.TypeBool, Nullable: true, Comment: "Is super tenant | 是否是超级租户", Default: false},
		{Name: "service_start_at", Type: field.TypeTime, Nullable: true, Comment: "Tenant's service start | 服务开始时间例如2023-01-01 00:00:00"},
		{Name: "service_end_at", Type: field.TypeTime, Nullable: true, Comment: "Tenant's service end| 服务到期时间例如2023-10-10 00:00:00"},
		{Name: "after_sales_contact", Type: field.TypeString, Nullable: true, Comment: "售后联系人"},
		{Name: "location_id", Type: field.TypeString, Nullable: true, Comment: "归属地ID"},
		{Name: "log_save_keep_days", Type: field.TypeInt64, Nullable: true, Comment: "日志保留天数"},
		{Name: "max_attendance_user_count", Type: field.TypeInt64, Nullable: true, Comment: "最大列席用户数"},
		{Name: "max_device_count", Type: field.TypeInt64, Nullable: true, Comment: "最大设备数"},
		{Name: "max_upload_file_size", Type: field.TypeInt64, Nullable: true, Comment: "最大上传文件大小"},
		{Name: "max_user_count", Type: field.TypeInt64, Nullable: true, Comment: "最大用户数"},
		{Name: "principal", Type: field.TypeString, Nullable: true, Comment: "负责人"},
		{Name: "principal_contact_information", Type: field.TypeString, Nullable: true, Comment: "负责人联系方式"},
		{Name: "sale_contact", Type: field.TypeString, Nullable: true, Comment: "销售联系人"},
		{Name: "secret_key", Type: field.TypeString, Nullable: true, Comment: "密钥"},
		{Name: "ai_status", Type: field.TypeBool, Nullable: true, Comment: "AI状态", Default: false},
		{Name: "max_conference_agenda_title", Type: field.TypeInt64, Nullable: true, Comment: "最大会议议程标题字数"},
	}
	// SaasTenantTable holds the schema information for the "saas_tenant" table.
	SaasTenantTable = &schema.Table{
		Name:       "saas_tenant",
		Columns:    SaasTenantColumns,
		PrimaryKey: []*schema.Column{SaasTenantColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "tenant_name_key",
				Unique:  true,
				Columns: []*schema.Column{SaasTenantColumns[8], SaasTenantColumns[4]},
			},
		},
	}
	// SaasTenantUserInfoColumns holds the columns for the "saas_tenant_user_info" table.
	SaasTenantUserInfoColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Comment: "Snowflake ID | 全局唯一ID"},
		{Name: "created_at", Type: field.TypeTime, Comment: "Created Time | 创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "Updated Time | 更新时间"},
		{Name: "sort", Type: field.TypeUint32, Comment: "Sort number | 排序编号", Default: 1},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true, Comment: "Deleted Time | 删除时间（软删除标识）"},
		{Name: "extra", Type: field.TypeString, Nullable: true, Size: 2147483647, Comment: "Extra information | 额外信息(json对象格式存储，用于存储的额外展示信息)", Default: "{}"},
		{Name: "tenant_id", Type: field.TypeString, Nullable: true, Comment: "Tenant ID | 租户 ID"},
		{Name: "user_id", Type: field.TypeString, Nullable: true, Comment: "User ID | 用户 ID"},
		{Name: "user_tenant_infos", Type: field.TypeString, Nullable: true},
	}
	// SaasTenantUserInfoTable holds the schema information for the "saas_tenant_user_info" table.
	SaasTenantUserInfoTable = &schema.Table{
		Name:       "saas_tenant_user_info",
		Columns:    SaasTenantUserInfoColumns,
		PrimaryKey: []*schema.Column{SaasTenantUserInfoColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "saas_tenant_user_info_saas_tenant_tenant_user_info_tenant",
				Columns:    []*schema.Column{SaasTenantUserInfoColumns[6]},
				RefColumns: []*schema.Column{SaasTenantColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "saas_tenant_user_info_saas_user_tenant_user_info_user",
				Columns:    []*schema.Column{SaasTenantUserInfoColumns[7]},
				RefColumns: []*schema.Column{SaasUserColumns[0]},
				OnDelete:   schema.SetNull,
			},
			{
				Symbol:     "saas_tenant_user_info_saas_user_tenant_infos",
				Columns:    []*schema.Column{SaasTenantUserInfoColumns[8]},
				RefColumns: []*schema.Column{SaasUserColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
	}
	// SaasTokenColumns holds the columns for the "saas_token" table.
	SaasTokenColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Comment: "Snowflake ID | 全局唯一ID"},
		{Name: "created_at", Type: field.TypeTime, Comment: "Created Time | 创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "Updated Time | 更新时间"},
		{Name: "status", Type: field.TypeBool, Nullable: true, Comment: "status true normal false ban | 状态  正常/禁用", Default: true},
		{Name: "uid", Type: field.TypeString, Comment: " User's UID | 用户的UID"},
		{Name: "token", Type: field.TypeString, Comment: "Token string | Token 字符串"},
		{Name: "source", Type: field.TypeString, Comment: "Log in source such as GitHub | Token 来源 （saas, 第三方如github等）"},
		{Name: "expired_at", Type: field.TypeTime, Comment: " Expire time | 过期时间"},
		{Name: "tenant_id", Type: field.TypeString, Nullable: true, Comment: "Tenant ID | 租户ID"},
		{Name: "device_kind", Type: field.TypeString, Nullable: true, Comment: "Device kind | 设备类型"},
		{Name: "ip", Type: field.TypeString, Nullable: true, Comment: "IP | IP"},
	}
	// SaasTokenTable holds the schema information for the "saas_token" table.
	SaasTokenTable = &schema.Table{
		Name:       "saas_token",
		Columns:    SaasTokenColumns,
		PrimaryKey: []*schema.Column{SaasTokenColumns[0]},
		Indexes: []*schema.Index{
			{
				Name:    "token_uid",
				Unique:  false,
				Columns: []*schema.Column{SaasTokenColumns[4]},
			},
			{
				Name:    "token_expired_at",
				Unique:  false,
				Columns: []*schema.Column{SaasTokenColumns[7]},
			},
		},
	}
	// SaasUserColumns holds the columns for the "saas_user" table.
	SaasUserColumns = []*schema.Column{
		{Name: "id", Type: field.TypeString, Comment: "Snowflake ID | 全局唯一ID"},
		{Name: "created_at", Type: field.TypeTime, Comment: "Created Time | 创建时间"},
		{Name: "updated_at", Type: field.TypeTime, Comment: "Updated Time | 更新时间"},
		{Name: "status", Type: field.TypeBool, Nullable: true, Comment: "status true normal false ban | 状态  正常/禁用", Default: true},
		{Name: "deleted_at", Type: field.TypeTime, Nullable: true, Comment: "Deleted Time | 删除时间（软删除标识）"},
		{Name: "username", Type: field.TypeString, Unique: true, Comment: "User's login name | 登录名"},
		{Name: "password", Type: field.TypeString, Comment: "Password | 密码Hash"},
		{Name: "nickname", Type: field.TypeString, Nullable: true, Comment: "Nickname | 昵称", Default: ""},
		{Name: "mobile", Type: field.TypeString, Nullable: true, Comment: "Mobile number | 手机号"},
		{Name: "email", Type: field.TypeString, Nullable: true, Comment: "Email | 邮箱号"},
		{Name: "gender", Type: field.TypeEnum, Comment: "gender | 性别", Enums: []string{"未设置", "男", "女"}, Default: "未设置"},
		{Name: "post", Type: field.TypeString, Nullable: true, Comment: "post | 职务", Default: ""},
		{Name: "is_superuser", Type: field.TypeBool, Comment: "Is Superuser | 是否超级管理员", Default: false},
		{Name: "default_tenant_id", Type: field.TypeString, Nullable: true, Comment: "Default tenant id | 默认租户ID，用于快速登录"},
		{Name: "device_no", Type: field.TypeString, Nullable: true, Comment: "Device No | 设备号", Default: ""},
		{Name: "kind", Type: field.TypeString, Nullable: true, Comment: "kind | 类型，attendance：列席用户", Default: "common"},
		{Name: "imei", Type: field.TypeString, Unique: true, Comment: "imei | imei"},
		{Name: "avatar_id", Type: field.TypeString, Nullable: true, Comment: "Avatar FIle ID | 头像文件ID", Default: ""},
	}
	// SaasUserTable holds the schema information for the "saas_user" table.
	SaasUserTable = &schema.Table{
		Name:       "saas_user",
		Columns:    SaasUserColumns,
		PrimaryKey: []*schema.Column{SaasUserColumns[0]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "saas_user_fms_file_avatar_users",
				Columns:    []*schema.Column{SaasUserColumns[17]},
				RefColumns: []*schema.Column{FmsFileColumns[0]},
				OnDelete:   schema.SetNull,
			},
		},
		Indexes: []*schema.Index{
			{
				Name:    "user_username_email",
				Unique:  true,
				Columns: []*schema.Column{SaasUserColumns[5], SaasUserColumns[9]},
			},
		},
	}
	// RoleButtonsColumns holds the columns for the "role_buttons" table.
	RoleButtonsColumns = []*schema.Column{
		{Name: "role_id", Type: field.TypeString},
		{Name: "button_id", Type: field.TypeString},
	}
	// RoleButtonsTable holds the schema information for the "role_buttons" table.
	RoleButtonsTable = &schema.Table{
		Name:       "role_buttons",
		Columns:    RoleButtonsColumns,
		PrimaryKey: []*schema.Column{RoleButtonsColumns[0], RoleButtonsColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "role_buttons_role_id",
				Columns:    []*schema.Column{RoleButtonsColumns[0]},
				RefColumns: []*schema.Column{SaasRoleColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "role_buttons_button_id",
				Columns:    []*schema.Column{RoleButtonsColumns[1]},
				RefColumns: []*schema.Column{SaasButtonColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// RoleMenusColumns holds the columns for the "role_menus" table.
	RoleMenusColumns = []*schema.Column{
		{Name: "role_id", Type: field.TypeString},
		{Name: "menu_id", Type: field.TypeString},
	}
	// RoleMenusTable holds the schema information for the "role_menus" table.
	RoleMenusTable = &schema.Table{
		Name:       "role_menus",
		Columns:    RoleMenusColumns,
		PrimaryKey: []*schema.Column{RoleMenusColumns[0], RoleMenusColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "role_menus_role_id",
				Columns:    []*schema.Column{RoleMenusColumns[0]},
				RefColumns: []*schema.Column{SaasRoleColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "role_menus_menu_id",
				Columns:    []*schema.Column{RoleMenusColumns[1]},
				RefColumns: []*schema.Column{SaasMenuColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// RoleApisColumns holds the columns for the "role_apis" table.
	RoleApisColumns = []*schema.Column{
		{Name: "role_id", Type: field.TypeString},
		{Name: "api_id", Type: field.TypeString},
	}
	// RoleApisTable holds the schema information for the "role_apis" table.
	RoleApisTable = &schema.Table{
		Name:       "role_apis",
		Columns:    RoleApisColumns,
		PrimaryKey: []*schema.Column{RoleApisColumns[0], RoleApisColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "role_apis_role_id",
				Columns:    []*schema.Column{RoleApisColumns[0]},
				RefColumns: []*schema.Column{SaasRoleColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "role_apis_api_id",
				Columns:    []*schema.Column{RoleApisColumns[1]},
				RefColumns: []*schema.Column{SaasAPIColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// RoleGroupsColumns holds the columns for the "role_groups" table.
	RoleGroupsColumns = []*schema.Column{
		{Name: "role_id", Type: field.TypeString},
		{Name: "group_id", Type: field.TypeString},
	}
	// RoleGroupsTable holds the schema information for the "role_groups" table.
	RoleGroupsTable = &schema.Table{
		Name:       "role_groups",
		Columns:    RoleGroupsColumns,
		PrimaryKey: []*schema.Column{RoleGroupsColumns[0], RoleGroupsColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "role_groups_role_id",
				Columns:    []*schema.Column{RoleGroupsColumns[0]},
				RefColumns: []*schema.Column{SaasRoleColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "role_groups_group_id",
				Columns:    []*schema.Column{RoleGroupsColumns[1]},
				RefColumns: []*schema.Column{SaasGroupColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// RoleUsersColumns holds the columns for the "role_users" table.
	RoleUsersColumns = []*schema.Column{
		{Name: "role_id", Type: field.TypeString},
		{Name: "user_id", Type: field.TypeString},
	}
	// RoleUsersTable holds the schema information for the "role_users" table.
	RoleUsersTable = &schema.Table{
		Name:       "role_users",
		Columns:    RoleUsersColumns,
		PrimaryKey: []*schema.Column{RoleUsersColumns[0], RoleUsersColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "role_users_role_id",
				Columns:    []*schema.Column{RoleUsersColumns[0]},
				RefColumns: []*schema.Column{SaasRoleColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "role_users_user_id",
				Columns:    []*schema.Column{RoleUsersColumns[1]},
				RefColumns: []*schema.Column{SaasUserColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// TenantUsersColumns holds the columns for the "tenant_users" table.
	TenantUsersColumns = []*schema.Column{
		{Name: "tenant_id", Type: field.TypeString},
		{Name: "user_id", Type: field.TypeString},
	}
	// TenantUsersTable holds the schema information for the "tenant_users" table.
	TenantUsersTable = &schema.Table{
		Name:       "tenant_users",
		Columns:    TenantUsersColumns,
		PrimaryKey: []*schema.Column{TenantUsersColumns[0], TenantUsersColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "tenant_users_tenant_id",
				Columns:    []*schema.Column{TenantUsersColumns[0]},
				RefColumns: []*schema.Column{SaasTenantColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "tenant_users_user_id",
				Columns:    []*schema.Column{TenantUsersColumns[1]},
				RefColumns: []*schema.Column{SaasUserColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// UserOrganizationsColumns holds the columns for the "user_organizations" table.
	UserOrganizationsColumns = []*schema.Column{
		{Name: "user_id", Type: field.TypeString},
		{Name: "organization_id", Type: field.TypeString},
	}
	// UserOrganizationsTable holds the schema information for the "user_organizations" table.
	UserOrganizationsTable = &schema.Table{
		Name:       "user_organizations",
		Columns:    UserOrganizationsColumns,
		PrimaryKey: []*schema.Column{UserOrganizationsColumns[0], UserOrganizationsColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "user_organizations_user_id",
				Columns:    []*schema.Column{UserOrganizationsColumns[0]},
				RefColumns: []*schema.Column{SaasUserColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "user_organizations_organization_id",
				Columns:    []*schema.Column{UserOrganizationsColumns[1]},
				RefColumns: []*schema.Column{SaasOrganizationColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// UserPositionsColumns holds the columns for the "user_positions" table.
	UserPositionsColumns = []*schema.Column{
		{Name: "user_id", Type: field.TypeString},
		{Name: "position_id", Type: field.TypeString},
	}
	// UserPositionsTable holds the schema information for the "user_positions" table.
	UserPositionsTable = &schema.Table{
		Name:       "user_positions",
		Columns:    UserPositionsColumns,
		PrimaryKey: []*schema.Column{UserPositionsColumns[0], UserPositionsColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "user_positions_user_id",
				Columns:    []*schema.Column{UserPositionsColumns[0]},
				RefColumns: []*schema.Column{SaasUserColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "user_positions_position_id",
				Columns:    []*schema.Column{UserPositionsColumns[1]},
				RefColumns: []*schema.Column{SaasPositionColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// UserGroupsColumns holds the columns for the "user_groups" table.
	UserGroupsColumns = []*schema.Column{
		{Name: "user_id", Type: field.TypeString},
		{Name: "group_id", Type: field.TypeString},
	}
	// UserGroupsTable holds the schema information for the "user_groups" table.
	UserGroupsTable = &schema.Table{
		Name:       "user_groups",
		Columns:    UserGroupsColumns,
		PrimaryKey: []*schema.Column{UserGroupsColumns[0], UserGroupsColumns[1]},
		ForeignKeys: []*schema.ForeignKey{
			{
				Symbol:     "user_groups_user_id",
				Columns:    []*schema.Column{UserGroupsColumns[0]},
				RefColumns: []*schema.Column{SaasUserColumns[0]},
				OnDelete:   schema.Cascade,
			},
			{
				Symbol:     "user_groups_group_id",
				Columns:    []*schema.Column{UserGroupsColumns[1]},
				RefColumns: []*schema.Column{SaasGroupColumns[0]},
				OnDelete:   schema.Cascade,
			},
		},
	}
	// Tables holds all the tables in the schema.
	Tables = []*schema.Table{
		SaasAPITable,
		SaasApplicationTable,
		SaasButtonTable,
		FmsFileTable,
		SaasGroupTable,
		SaasGroupTypeTable,
		SaasMenuTable,
		SaasOrganizationTable,
		SaasOrganizationUserInfoTable,
		SaasPositionTable,
		SaasRoleTable,
		SaasTenantTable,
		SaasTenantUserInfoTable,
		SaasTokenTable,
		SaasUserTable,
		RoleButtonsTable,
		RoleMenusTable,
		RoleApisTable,
		RoleGroupsTable,
		RoleUsersTable,
		TenantUsersTable,
		UserOrganizationsTable,
		UserPositionsTable,
		UserGroupsTable,
	}
)

func init() {
	SaasAPITable.ForeignKeys[0].RefTable = SaasTenantTable
	SaasAPITable.Annotation = &entsql.Annotation{
		Table: "saas_api",
	}
	SaasApplicationTable.ForeignKeys[0].RefTable = SaasTenantTable
	SaasApplicationTable.Annotation = &entsql.Annotation{
		Table: "saas_application",
	}
	SaasButtonTable.ForeignKeys[0].RefTable = SaasMenuTable
	SaasButtonTable.ForeignKeys[1].RefTable = SaasTenantTable
	SaasButtonTable.Annotation = &entsql.Annotation{
		Table: "saas_button",
	}
	FmsFileTable.ForeignKeys[0].RefTable = SaasTenantTable
	FmsFileTable.ForeignKeys[1].RefTable = SaasUserTable
	FmsFileTable.Annotation = &entsql.Annotation{
		Table: "fms_file",
	}
	SaasGroupTable.ForeignKeys[0].RefTable = SaasTenantTable
	SaasGroupTable.ForeignKeys[1].RefTable = SaasGroupTypeTable
	SaasGroupTable.Annotation = &entsql.Annotation{
		Table: "saas_group",
	}
	SaasGroupTypeTable.ForeignKeys[0].RefTable = SaasTenantTable
	SaasGroupTypeTable.Annotation = &entsql.Annotation{
		Table: "saas_group_type",
	}
	SaasMenuTable.ForeignKeys[0].RefTable = SaasMenuTable
	SaasMenuTable.ForeignKeys[1].RefTable = SaasTenantTable
	SaasMenuTable.Annotation = &entsql.Annotation{
		Table: "saas_menu",
	}
	SaasOrganizationTable.ForeignKeys[0].RefTable = SaasTenantTable
	SaasOrganizationTable.ForeignKeys[1].RefTable = SaasOrganizationTable
	SaasOrganizationTable.Annotation = &entsql.Annotation{
		Table: "saas_organization",
	}
	SaasOrganizationUserInfoTable.ForeignKeys[0].RefTable = SaasOrganizationTable
	SaasOrganizationUserInfoTable.ForeignKeys[1].RefTable = SaasUserTable
	SaasOrganizationUserInfoTable.Annotation = &entsql.Annotation{
		Table: "saas_organization_user_info",
	}
	SaasPositionTable.ForeignKeys[0].RefTable = SaasTenantTable
	SaasPositionTable.Annotation = &entsql.Annotation{
		Table: "saas_position",
	}
	SaasRoleTable.ForeignKeys[0].RefTable = SaasTenantTable
	SaasRoleTable.ForeignKeys[1].RefTable = SaasRoleTable
	SaasRoleTable.Annotation = &entsql.Annotation{
		Table: "saas_role",
	}
	SaasTenantTable.Annotation = &entsql.Annotation{
		Table: "saas_tenant",
	}
	SaasTenantUserInfoTable.ForeignKeys[0].RefTable = SaasTenantTable
	SaasTenantUserInfoTable.ForeignKeys[1].RefTable = SaasUserTable
	SaasTenantUserInfoTable.ForeignKeys[2].RefTable = SaasUserTable
	SaasTenantUserInfoTable.Annotation = &entsql.Annotation{
		Table: "saas_tenant_user_info",
	}
	SaasTokenTable.Annotation = &entsql.Annotation{
		Table: "saas_token",
	}
	SaasUserTable.ForeignKeys[0].RefTable = FmsFileTable
	SaasUserTable.Annotation = &entsql.Annotation{
		Table: "saas_user",
	}
	RoleButtonsTable.ForeignKeys[0].RefTable = SaasRoleTable
	RoleButtonsTable.ForeignKeys[1].RefTable = SaasButtonTable
	RoleMenusTable.ForeignKeys[0].RefTable = SaasRoleTable
	RoleMenusTable.ForeignKeys[1].RefTable = SaasMenuTable
	RoleApisTable.ForeignKeys[0].RefTable = SaasRoleTable
	RoleApisTable.ForeignKeys[1].RefTable = SaasAPITable
	RoleGroupsTable.ForeignKeys[0].RefTable = SaasRoleTable
	RoleGroupsTable.ForeignKeys[1].RefTable = SaasGroupTable
	RoleUsersTable.ForeignKeys[0].RefTable = SaasRoleTable
	RoleUsersTable.ForeignKeys[1].RefTable = SaasUserTable
	TenantUsersTable.ForeignKeys[0].RefTable = SaasTenantTable
	TenantUsersTable.ForeignKeys[1].RefTable = SaasUserTable
	UserOrganizationsTable.ForeignKeys[0].RefTable = SaasUserTable
	UserOrganizationsTable.ForeignKeys[1].RefTable = SaasOrganizationTable
	UserPositionsTable.ForeignKeys[0].RefTable = SaasUserTable
	UserPositionsTable.ForeignKeys[1].RefTable = SaasPositionTable
	UserGroupsTable.ForeignKeys[0].RefTable = SaasUserTable
	UserGroupsTable.ForeignKeys[1].RefTable = SaasGroupTable
}
