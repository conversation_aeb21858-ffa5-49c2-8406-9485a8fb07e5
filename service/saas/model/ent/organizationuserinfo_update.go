// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/organization"
	"phoenix/service/saas/model/ent/organizationuserinfo"
	"phoenix/service/saas/model/ent/predicate"
	"phoenix/service/saas/model/ent/user"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// OrganizationUserInfoUpdate is the builder for updating OrganizationUserInfo entities.
type OrganizationUserInfoUpdate struct {
	config
	hooks    []Hook
	mutation *OrganizationUserInfoMutation
}

// Where appends a list predicates to the OrganizationUserInfoUpdate builder.
func (ouiu *OrganizationUserInfoUpdate) Where(ps ...predicate.OrganizationUserInfo) *OrganizationUserInfoUpdate {
	ouiu.mutation.Where(ps...)
	return ouiu
}

// SetUpdatedAt sets the "updated_at" field.
func (ouiu *OrganizationUserInfoUpdate) SetUpdatedAt(t time.Time) *OrganizationUserInfoUpdate {
	ouiu.mutation.SetUpdatedAt(t)
	return ouiu
}

// SetSort sets the "sort" field.
func (ouiu *OrganizationUserInfoUpdate) SetSort(u uint32) *OrganizationUserInfoUpdate {
	ouiu.mutation.ResetSort()
	ouiu.mutation.SetSort(u)
	return ouiu
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (ouiu *OrganizationUserInfoUpdate) SetNillableSort(u *uint32) *OrganizationUserInfoUpdate {
	if u != nil {
		ouiu.SetSort(*u)
	}
	return ouiu
}

// AddSort adds u to the "sort" field.
func (ouiu *OrganizationUserInfoUpdate) AddSort(u int32) *OrganizationUserInfoUpdate {
	ouiu.mutation.AddSort(u)
	return ouiu
}

// SetDeletedAt sets the "deleted_at" field.
func (ouiu *OrganizationUserInfoUpdate) SetDeletedAt(t time.Time) *OrganizationUserInfoUpdate {
	ouiu.mutation.SetDeletedAt(t)
	return ouiu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (ouiu *OrganizationUserInfoUpdate) SetNillableDeletedAt(t *time.Time) *OrganizationUserInfoUpdate {
	if t != nil {
		ouiu.SetDeletedAt(*t)
	}
	return ouiu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (ouiu *OrganizationUserInfoUpdate) ClearDeletedAt() *OrganizationUserInfoUpdate {
	ouiu.mutation.ClearDeletedAt()
	return ouiu
}

// SetOrganizationID sets the "organization_id" field.
func (ouiu *OrganizationUserInfoUpdate) SetOrganizationID(s string) *OrganizationUserInfoUpdate {
	ouiu.mutation.SetOrganizationID(s)
	return ouiu
}

// SetNillableOrganizationID sets the "organization_id" field if the given value is not nil.
func (ouiu *OrganizationUserInfoUpdate) SetNillableOrganizationID(s *string) *OrganizationUserInfoUpdate {
	if s != nil {
		ouiu.SetOrganizationID(*s)
	}
	return ouiu
}

// SetUserID sets the "user_id" field.
func (ouiu *OrganizationUserInfoUpdate) SetUserID(s string) *OrganizationUserInfoUpdate {
	ouiu.mutation.SetUserID(s)
	return ouiu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (ouiu *OrganizationUserInfoUpdate) SetNillableUserID(s *string) *OrganizationUserInfoUpdate {
	if s != nil {
		ouiu.SetUserID(*s)
	}
	return ouiu
}

// SetExtra sets the "extra" field.
func (ouiu *OrganizationUserInfoUpdate) SetExtra(s string) *OrganizationUserInfoUpdate {
	ouiu.mutation.SetExtra(s)
	return ouiu
}

// SetNillableExtra sets the "extra" field if the given value is not nil.
func (ouiu *OrganizationUserInfoUpdate) SetNillableExtra(s *string) *OrganizationUserInfoUpdate {
	if s != nil {
		ouiu.SetExtra(*s)
	}
	return ouiu
}

// ClearExtra clears the value of the "extra" field.
func (ouiu *OrganizationUserInfoUpdate) ClearExtra() *OrganizationUserInfoUpdate {
	ouiu.mutation.ClearExtra()
	return ouiu
}

// SetIsLeader sets the "is_leader" field.
func (ouiu *OrganizationUserInfoUpdate) SetIsLeader(b bool) *OrganizationUserInfoUpdate {
	ouiu.mutation.SetIsLeader(b)
	return ouiu
}

// SetNillableIsLeader sets the "is_leader" field if the given value is not nil.
func (ouiu *OrganizationUserInfoUpdate) SetNillableIsLeader(b *bool) *OrganizationUserInfoUpdate {
	if b != nil {
		ouiu.SetIsLeader(*b)
	}
	return ouiu
}

// SetIsAdmin sets the "is_admin" field.
func (ouiu *OrganizationUserInfoUpdate) SetIsAdmin(b bool) *OrganizationUserInfoUpdate {
	ouiu.mutation.SetIsAdmin(b)
	return ouiu
}

// SetNillableIsAdmin sets the "is_admin" field if the given value is not nil.
func (ouiu *OrganizationUserInfoUpdate) SetNillableIsAdmin(b *bool) *OrganizationUserInfoUpdate {
	if b != nil {
		ouiu.SetIsAdmin(*b)
	}
	return ouiu
}

// SetOrganization sets the "organization" edge to the Organization entity.
func (ouiu *OrganizationUserInfoUpdate) SetOrganization(o *Organization) *OrganizationUserInfoUpdate {
	return ouiu.SetOrganizationID(o.ID)
}

// SetUser sets the "user" edge to the User entity.
func (ouiu *OrganizationUserInfoUpdate) SetUser(u *User) *OrganizationUserInfoUpdate {
	return ouiu.SetUserID(u.ID)
}

// Mutation returns the OrganizationUserInfoMutation object of the builder.
func (ouiu *OrganizationUserInfoUpdate) Mutation() *OrganizationUserInfoMutation {
	return ouiu.mutation
}

// ClearOrganization clears the "organization" edge to the Organization entity.
func (ouiu *OrganizationUserInfoUpdate) ClearOrganization() *OrganizationUserInfoUpdate {
	ouiu.mutation.ClearOrganization()
	return ouiu
}

// ClearUser clears the "user" edge to the User entity.
func (ouiu *OrganizationUserInfoUpdate) ClearUser() *OrganizationUserInfoUpdate {
	ouiu.mutation.ClearUser()
	return ouiu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (ouiu *OrganizationUserInfoUpdate) Save(ctx context.Context) (int, error) {
	if err := ouiu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, ouiu.sqlSave, ouiu.mutation, ouiu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ouiu *OrganizationUserInfoUpdate) SaveX(ctx context.Context) int {
	affected, err := ouiu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (ouiu *OrganizationUserInfoUpdate) Exec(ctx context.Context) error {
	_, err := ouiu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ouiu *OrganizationUserInfoUpdate) ExecX(ctx context.Context) {
	if err := ouiu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ouiu *OrganizationUserInfoUpdate) defaults() error {
	if _, ok := ouiu.mutation.UpdatedAt(); !ok {
		if organizationuserinfo.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized organizationuserinfo.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := organizationuserinfo.UpdateDefaultUpdatedAt()
		ouiu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (ouiu *OrganizationUserInfoUpdate) check() error {
	if _, ok := ouiu.mutation.OrganizationID(); ouiu.mutation.OrganizationCleared() && !ok {
		return errors.New(`ent: clearing a required unique edge "OrganizationUserInfo.organization"`)
	}
	if _, ok := ouiu.mutation.UserID(); ouiu.mutation.UserCleared() && !ok {
		return errors.New(`ent: clearing a required unique edge "OrganizationUserInfo.user"`)
	}
	return nil
}

func (ouiu *OrganizationUserInfoUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := ouiu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(organizationuserinfo.Table, organizationuserinfo.Columns, sqlgraph.NewFieldSpec(organizationuserinfo.FieldID, field.TypeString))
	if ps := ouiu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ouiu.mutation.UpdatedAt(); ok {
		_spec.SetField(organizationuserinfo.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := ouiu.mutation.Sort(); ok {
		_spec.SetField(organizationuserinfo.FieldSort, field.TypeUint32, value)
	}
	if value, ok := ouiu.mutation.AddedSort(); ok {
		_spec.AddField(organizationuserinfo.FieldSort, field.TypeUint32, value)
	}
	if value, ok := ouiu.mutation.DeletedAt(); ok {
		_spec.SetField(organizationuserinfo.FieldDeletedAt, field.TypeTime, value)
	}
	if ouiu.mutation.DeletedAtCleared() {
		_spec.ClearField(organizationuserinfo.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := ouiu.mutation.Extra(); ok {
		_spec.SetField(organizationuserinfo.FieldExtra, field.TypeString, value)
	}
	if ouiu.mutation.ExtraCleared() {
		_spec.ClearField(organizationuserinfo.FieldExtra, field.TypeString)
	}
	if value, ok := ouiu.mutation.IsLeader(); ok {
		_spec.SetField(organizationuserinfo.FieldIsLeader, field.TypeBool, value)
	}
	if value, ok := ouiu.mutation.IsAdmin(); ok {
		_spec.SetField(organizationuserinfo.FieldIsAdmin, field.TypeBool, value)
	}
	if ouiu.mutation.OrganizationCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   organizationuserinfo.OrganizationTable,
			Columns: []string{organizationuserinfo.OrganizationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ouiu.mutation.OrganizationIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   organizationuserinfo.OrganizationTable,
			Columns: []string{organizationuserinfo.OrganizationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ouiu.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   organizationuserinfo.UserTable,
			Columns: []string{organizationuserinfo.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ouiu.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   organizationuserinfo.UserTable,
			Columns: []string{organizationuserinfo.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, ouiu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{organizationuserinfo.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	ouiu.mutation.done = true
	return n, nil
}

// OrganizationUserInfoUpdateOne is the builder for updating a single OrganizationUserInfo entity.
type OrganizationUserInfoUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *OrganizationUserInfoMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (ouiuo *OrganizationUserInfoUpdateOne) SetUpdatedAt(t time.Time) *OrganizationUserInfoUpdateOne {
	ouiuo.mutation.SetUpdatedAt(t)
	return ouiuo
}

// SetSort sets the "sort" field.
func (ouiuo *OrganizationUserInfoUpdateOne) SetSort(u uint32) *OrganizationUserInfoUpdateOne {
	ouiuo.mutation.ResetSort()
	ouiuo.mutation.SetSort(u)
	return ouiuo
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (ouiuo *OrganizationUserInfoUpdateOne) SetNillableSort(u *uint32) *OrganizationUserInfoUpdateOne {
	if u != nil {
		ouiuo.SetSort(*u)
	}
	return ouiuo
}

// AddSort adds u to the "sort" field.
func (ouiuo *OrganizationUserInfoUpdateOne) AddSort(u int32) *OrganizationUserInfoUpdateOne {
	ouiuo.mutation.AddSort(u)
	return ouiuo
}

// SetDeletedAt sets the "deleted_at" field.
func (ouiuo *OrganizationUserInfoUpdateOne) SetDeletedAt(t time.Time) *OrganizationUserInfoUpdateOne {
	ouiuo.mutation.SetDeletedAt(t)
	return ouiuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (ouiuo *OrganizationUserInfoUpdateOne) SetNillableDeletedAt(t *time.Time) *OrganizationUserInfoUpdateOne {
	if t != nil {
		ouiuo.SetDeletedAt(*t)
	}
	return ouiuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (ouiuo *OrganizationUserInfoUpdateOne) ClearDeletedAt() *OrganizationUserInfoUpdateOne {
	ouiuo.mutation.ClearDeletedAt()
	return ouiuo
}

// SetOrganizationID sets the "organization_id" field.
func (ouiuo *OrganizationUserInfoUpdateOne) SetOrganizationID(s string) *OrganizationUserInfoUpdateOne {
	ouiuo.mutation.SetOrganizationID(s)
	return ouiuo
}

// SetNillableOrganizationID sets the "organization_id" field if the given value is not nil.
func (ouiuo *OrganizationUserInfoUpdateOne) SetNillableOrganizationID(s *string) *OrganizationUserInfoUpdateOne {
	if s != nil {
		ouiuo.SetOrganizationID(*s)
	}
	return ouiuo
}

// SetUserID sets the "user_id" field.
func (ouiuo *OrganizationUserInfoUpdateOne) SetUserID(s string) *OrganizationUserInfoUpdateOne {
	ouiuo.mutation.SetUserID(s)
	return ouiuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (ouiuo *OrganizationUserInfoUpdateOne) SetNillableUserID(s *string) *OrganizationUserInfoUpdateOne {
	if s != nil {
		ouiuo.SetUserID(*s)
	}
	return ouiuo
}

// SetExtra sets the "extra" field.
func (ouiuo *OrganizationUserInfoUpdateOne) SetExtra(s string) *OrganizationUserInfoUpdateOne {
	ouiuo.mutation.SetExtra(s)
	return ouiuo
}

// SetNillableExtra sets the "extra" field if the given value is not nil.
func (ouiuo *OrganizationUserInfoUpdateOne) SetNillableExtra(s *string) *OrganizationUserInfoUpdateOne {
	if s != nil {
		ouiuo.SetExtra(*s)
	}
	return ouiuo
}

// ClearExtra clears the value of the "extra" field.
func (ouiuo *OrganizationUserInfoUpdateOne) ClearExtra() *OrganizationUserInfoUpdateOne {
	ouiuo.mutation.ClearExtra()
	return ouiuo
}

// SetIsLeader sets the "is_leader" field.
func (ouiuo *OrganizationUserInfoUpdateOne) SetIsLeader(b bool) *OrganizationUserInfoUpdateOne {
	ouiuo.mutation.SetIsLeader(b)
	return ouiuo
}

// SetNillableIsLeader sets the "is_leader" field if the given value is not nil.
func (ouiuo *OrganizationUserInfoUpdateOne) SetNillableIsLeader(b *bool) *OrganizationUserInfoUpdateOne {
	if b != nil {
		ouiuo.SetIsLeader(*b)
	}
	return ouiuo
}

// SetIsAdmin sets the "is_admin" field.
func (ouiuo *OrganizationUserInfoUpdateOne) SetIsAdmin(b bool) *OrganizationUserInfoUpdateOne {
	ouiuo.mutation.SetIsAdmin(b)
	return ouiuo
}

// SetNillableIsAdmin sets the "is_admin" field if the given value is not nil.
func (ouiuo *OrganizationUserInfoUpdateOne) SetNillableIsAdmin(b *bool) *OrganizationUserInfoUpdateOne {
	if b != nil {
		ouiuo.SetIsAdmin(*b)
	}
	return ouiuo
}

// SetOrganization sets the "organization" edge to the Organization entity.
func (ouiuo *OrganizationUserInfoUpdateOne) SetOrganization(o *Organization) *OrganizationUserInfoUpdateOne {
	return ouiuo.SetOrganizationID(o.ID)
}

// SetUser sets the "user" edge to the User entity.
func (ouiuo *OrganizationUserInfoUpdateOne) SetUser(u *User) *OrganizationUserInfoUpdateOne {
	return ouiuo.SetUserID(u.ID)
}

// Mutation returns the OrganizationUserInfoMutation object of the builder.
func (ouiuo *OrganizationUserInfoUpdateOne) Mutation() *OrganizationUserInfoMutation {
	return ouiuo.mutation
}

// ClearOrganization clears the "organization" edge to the Organization entity.
func (ouiuo *OrganizationUserInfoUpdateOne) ClearOrganization() *OrganizationUserInfoUpdateOne {
	ouiuo.mutation.ClearOrganization()
	return ouiuo
}

// ClearUser clears the "user" edge to the User entity.
func (ouiuo *OrganizationUserInfoUpdateOne) ClearUser() *OrganizationUserInfoUpdateOne {
	ouiuo.mutation.ClearUser()
	return ouiuo
}

// Where appends a list predicates to the OrganizationUserInfoUpdate builder.
func (ouiuo *OrganizationUserInfoUpdateOne) Where(ps ...predicate.OrganizationUserInfo) *OrganizationUserInfoUpdateOne {
	ouiuo.mutation.Where(ps...)
	return ouiuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (ouiuo *OrganizationUserInfoUpdateOne) Select(field string, fields ...string) *OrganizationUserInfoUpdateOne {
	ouiuo.fields = append([]string{field}, fields...)
	return ouiuo
}

// Save executes the query and returns the updated OrganizationUserInfo entity.
func (ouiuo *OrganizationUserInfoUpdateOne) Save(ctx context.Context) (*OrganizationUserInfo, error) {
	if err := ouiuo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, ouiuo.sqlSave, ouiuo.mutation, ouiuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ouiuo *OrganizationUserInfoUpdateOne) SaveX(ctx context.Context) *OrganizationUserInfo {
	node, err := ouiuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (ouiuo *OrganizationUserInfoUpdateOne) Exec(ctx context.Context) error {
	_, err := ouiuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ouiuo *OrganizationUserInfoUpdateOne) ExecX(ctx context.Context) {
	if err := ouiuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ouiuo *OrganizationUserInfoUpdateOne) defaults() error {
	if _, ok := ouiuo.mutation.UpdatedAt(); !ok {
		if organizationuserinfo.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized organizationuserinfo.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := organizationuserinfo.UpdateDefaultUpdatedAt()
		ouiuo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (ouiuo *OrganizationUserInfoUpdateOne) check() error {
	if _, ok := ouiuo.mutation.OrganizationID(); ouiuo.mutation.OrganizationCleared() && !ok {
		return errors.New(`ent: clearing a required unique edge "OrganizationUserInfo.organization"`)
	}
	if _, ok := ouiuo.mutation.UserID(); ouiuo.mutation.UserCleared() && !ok {
		return errors.New(`ent: clearing a required unique edge "OrganizationUserInfo.user"`)
	}
	return nil
}

func (ouiuo *OrganizationUserInfoUpdateOne) sqlSave(ctx context.Context) (_node *OrganizationUserInfo, err error) {
	if err := ouiuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(organizationuserinfo.Table, organizationuserinfo.Columns, sqlgraph.NewFieldSpec(organizationuserinfo.FieldID, field.TypeString))
	id, ok := ouiuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "OrganizationUserInfo.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := ouiuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, organizationuserinfo.FieldID)
		for _, f := range fields {
			if !organizationuserinfo.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != organizationuserinfo.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := ouiuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ouiuo.mutation.UpdatedAt(); ok {
		_spec.SetField(organizationuserinfo.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := ouiuo.mutation.Sort(); ok {
		_spec.SetField(organizationuserinfo.FieldSort, field.TypeUint32, value)
	}
	if value, ok := ouiuo.mutation.AddedSort(); ok {
		_spec.AddField(organizationuserinfo.FieldSort, field.TypeUint32, value)
	}
	if value, ok := ouiuo.mutation.DeletedAt(); ok {
		_spec.SetField(organizationuserinfo.FieldDeletedAt, field.TypeTime, value)
	}
	if ouiuo.mutation.DeletedAtCleared() {
		_spec.ClearField(organizationuserinfo.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := ouiuo.mutation.Extra(); ok {
		_spec.SetField(organizationuserinfo.FieldExtra, field.TypeString, value)
	}
	if ouiuo.mutation.ExtraCleared() {
		_spec.ClearField(organizationuserinfo.FieldExtra, field.TypeString)
	}
	if value, ok := ouiuo.mutation.IsLeader(); ok {
		_spec.SetField(organizationuserinfo.FieldIsLeader, field.TypeBool, value)
	}
	if value, ok := ouiuo.mutation.IsAdmin(); ok {
		_spec.SetField(organizationuserinfo.FieldIsAdmin, field.TypeBool, value)
	}
	if ouiuo.mutation.OrganizationCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   organizationuserinfo.OrganizationTable,
			Columns: []string{organizationuserinfo.OrganizationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ouiuo.mutation.OrganizationIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   organizationuserinfo.OrganizationTable,
			Columns: []string{organizationuserinfo.OrganizationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ouiuo.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   organizationuserinfo.UserTable,
			Columns: []string{organizationuserinfo.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ouiuo.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   organizationuserinfo.UserTable,
			Columns: []string{organizationuserinfo.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &OrganizationUserInfo{config: ouiuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, ouiuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{organizationuserinfo.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	ouiuo.mutation.done = true
	return _node, nil
}
