// Code generated by ent, DO NOT EDIT.

package menu

import (
	"phoenix/service/saas/model/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.Menu {
	return predicate.Menu(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.Menu {
	return predicate.Menu(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.Menu {
	return predicate.Menu(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.Menu {
	return predicate.Menu(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.Menu {
	return predicate.Menu(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.Menu {
	return predicate.Menu(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.Menu {
	return predicate.Menu(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.Menu {
	return predicate.Menu(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.Menu {
	return predicate.Menu(sql.FieldContainsFold(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldUpdatedAt, v))
}

// Sort applies equality check predicate on the "sort" field. It's identical to SortEQ.
func Sort(v uint32) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldSort, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldDeletedAt, v))
}

// Name applies equality check predicate on the "name" field. It's identical to NameEQ.
func Name(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldName, v))
}

// Title applies equality check predicate on the "title" field. It's identical to TitleEQ.
func Title(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldTitle, v))
}

// Icon applies equality check predicate on the "icon" field. It's identical to IconEQ.
func Icon(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldIcon, v))
}

// ParentID applies equality check predicate on the "parent_id" field. It's identical to ParentIDEQ.
func ParentID(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldParentID, v))
}

// MenuType applies equality check predicate on the "menu_type" field. It's identical to MenuTypeEQ.
func MenuType(v uint32) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldMenuType, v))
}

// URL applies equality check predicate on the "url" field. It's identical to URLEQ.
func URL(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldURL, v))
}

// Redirect applies equality check predicate on the "redirect" field. It's identical to RedirectEQ.
func Redirect(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldRedirect, v))
}

// Component applies equality check predicate on the "component" field. It's identical to ComponentEQ.
func Component(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldComponent, v))
}

// IsActive applies equality check predicate on the "is_active" field. It's identical to IsActiveEQ.
func IsActive(v bool) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldIsActive, v))
}

// Hidden applies equality check predicate on the "hidden" field. It's identical to HiddenEQ.
func Hidden(v bool) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldHidden, v))
}

// HiddenInTab applies equality check predicate on the "hidden_in_tab" field. It's identical to HiddenInTabEQ.
func HiddenInTab(v bool) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldHiddenInTab, v))
}

// Fixed applies equality check predicate on the "fixed" field. It's identical to FixedEQ.
func Fixed(v bool) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldFixed, v))
}

// Remark applies equality check predicate on the "remark" field. It's identical to RemarkEQ.
func Remark(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldRemark, v))
}

// Meta applies equality check predicate on the "meta" field. It's identical to MetaEQ.
func Meta(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldMeta, v))
}

// IsFullPage applies equality check predicate on the "is_full_page" field. It's identical to IsFullPageEQ.
func IsFullPage(v bool) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldIsFullPage, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldLTE(FieldUpdatedAt, v))
}

// SortEQ applies the EQ predicate on the "sort" field.
func SortEQ(v uint32) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldSort, v))
}

// SortNEQ applies the NEQ predicate on the "sort" field.
func SortNEQ(v uint32) predicate.Menu {
	return predicate.Menu(sql.FieldNEQ(FieldSort, v))
}

// SortIn applies the In predicate on the "sort" field.
func SortIn(vs ...uint32) predicate.Menu {
	return predicate.Menu(sql.FieldIn(FieldSort, vs...))
}

// SortNotIn applies the NotIn predicate on the "sort" field.
func SortNotIn(vs ...uint32) predicate.Menu {
	return predicate.Menu(sql.FieldNotIn(FieldSort, vs...))
}

// SortGT applies the GT predicate on the "sort" field.
func SortGT(v uint32) predicate.Menu {
	return predicate.Menu(sql.FieldGT(FieldSort, v))
}

// SortGTE applies the GTE predicate on the "sort" field.
func SortGTE(v uint32) predicate.Menu {
	return predicate.Menu(sql.FieldGTE(FieldSort, v))
}

// SortLT applies the LT predicate on the "sort" field.
func SortLT(v uint32) predicate.Menu {
	return predicate.Menu(sql.FieldLT(FieldSort, v))
}

// SortLTE applies the LTE predicate on the "sort" field.
func SortLTE(v uint32) predicate.Menu {
	return predicate.Menu(sql.FieldLTE(FieldSort, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.Menu {
	return predicate.Menu(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.Menu {
	return predicate.Menu(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.Menu {
	return predicate.Menu(sql.FieldNotNull(FieldDeletedAt))
}

// NameEQ applies the EQ predicate on the "name" field.
func NameEQ(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldName, v))
}

// NameNEQ applies the NEQ predicate on the "name" field.
func NameNEQ(v string) predicate.Menu {
	return predicate.Menu(sql.FieldNEQ(FieldName, v))
}

// NameIn applies the In predicate on the "name" field.
func NameIn(vs ...string) predicate.Menu {
	return predicate.Menu(sql.FieldIn(FieldName, vs...))
}

// NameNotIn applies the NotIn predicate on the "name" field.
func NameNotIn(vs ...string) predicate.Menu {
	return predicate.Menu(sql.FieldNotIn(FieldName, vs...))
}

// NameGT applies the GT predicate on the "name" field.
func NameGT(v string) predicate.Menu {
	return predicate.Menu(sql.FieldGT(FieldName, v))
}

// NameGTE applies the GTE predicate on the "name" field.
func NameGTE(v string) predicate.Menu {
	return predicate.Menu(sql.FieldGTE(FieldName, v))
}

// NameLT applies the LT predicate on the "name" field.
func NameLT(v string) predicate.Menu {
	return predicate.Menu(sql.FieldLT(FieldName, v))
}

// NameLTE applies the LTE predicate on the "name" field.
func NameLTE(v string) predicate.Menu {
	return predicate.Menu(sql.FieldLTE(FieldName, v))
}

// NameContains applies the Contains predicate on the "name" field.
func NameContains(v string) predicate.Menu {
	return predicate.Menu(sql.FieldContains(FieldName, v))
}

// NameHasPrefix applies the HasPrefix predicate on the "name" field.
func NameHasPrefix(v string) predicate.Menu {
	return predicate.Menu(sql.FieldHasPrefix(FieldName, v))
}

// NameHasSuffix applies the HasSuffix predicate on the "name" field.
func NameHasSuffix(v string) predicate.Menu {
	return predicate.Menu(sql.FieldHasSuffix(FieldName, v))
}

// NameEqualFold applies the EqualFold predicate on the "name" field.
func NameEqualFold(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEqualFold(FieldName, v))
}

// NameContainsFold applies the ContainsFold predicate on the "name" field.
func NameContainsFold(v string) predicate.Menu {
	return predicate.Menu(sql.FieldContainsFold(FieldName, v))
}

// TitleEQ applies the EQ predicate on the "title" field.
func TitleEQ(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldTitle, v))
}

// TitleNEQ applies the NEQ predicate on the "title" field.
func TitleNEQ(v string) predicate.Menu {
	return predicate.Menu(sql.FieldNEQ(FieldTitle, v))
}

// TitleIn applies the In predicate on the "title" field.
func TitleIn(vs ...string) predicate.Menu {
	return predicate.Menu(sql.FieldIn(FieldTitle, vs...))
}

// TitleNotIn applies the NotIn predicate on the "title" field.
func TitleNotIn(vs ...string) predicate.Menu {
	return predicate.Menu(sql.FieldNotIn(FieldTitle, vs...))
}

// TitleGT applies the GT predicate on the "title" field.
func TitleGT(v string) predicate.Menu {
	return predicate.Menu(sql.FieldGT(FieldTitle, v))
}

// TitleGTE applies the GTE predicate on the "title" field.
func TitleGTE(v string) predicate.Menu {
	return predicate.Menu(sql.FieldGTE(FieldTitle, v))
}

// TitleLT applies the LT predicate on the "title" field.
func TitleLT(v string) predicate.Menu {
	return predicate.Menu(sql.FieldLT(FieldTitle, v))
}

// TitleLTE applies the LTE predicate on the "title" field.
func TitleLTE(v string) predicate.Menu {
	return predicate.Menu(sql.FieldLTE(FieldTitle, v))
}

// TitleContains applies the Contains predicate on the "title" field.
func TitleContains(v string) predicate.Menu {
	return predicate.Menu(sql.FieldContains(FieldTitle, v))
}

// TitleHasPrefix applies the HasPrefix predicate on the "title" field.
func TitleHasPrefix(v string) predicate.Menu {
	return predicate.Menu(sql.FieldHasPrefix(FieldTitle, v))
}

// TitleHasSuffix applies the HasSuffix predicate on the "title" field.
func TitleHasSuffix(v string) predicate.Menu {
	return predicate.Menu(sql.FieldHasSuffix(FieldTitle, v))
}

// TitleEqualFold applies the EqualFold predicate on the "title" field.
func TitleEqualFold(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEqualFold(FieldTitle, v))
}

// TitleContainsFold applies the ContainsFold predicate on the "title" field.
func TitleContainsFold(v string) predicate.Menu {
	return predicate.Menu(sql.FieldContainsFold(FieldTitle, v))
}

// IconEQ applies the EQ predicate on the "icon" field.
func IconEQ(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldIcon, v))
}

// IconNEQ applies the NEQ predicate on the "icon" field.
func IconNEQ(v string) predicate.Menu {
	return predicate.Menu(sql.FieldNEQ(FieldIcon, v))
}

// IconIn applies the In predicate on the "icon" field.
func IconIn(vs ...string) predicate.Menu {
	return predicate.Menu(sql.FieldIn(FieldIcon, vs...))
}

// IconNotIn applies the NotIn predicate on the "icon" field.
func IconNotIn(vs ...string) predicate.Menu {
	return predicate.Menu(sql.FieldNotIn(FieldIcon, vs...))
}

// IconGT applies the GT predicate on the "icon" field.
func IconGT(v string) predicate.Menu {
	return predicate.Menu(sql.FieldGT(FieldIcon, v))
}

// IconGTE applies the GTE predicate on the "icon" field.
func IconGTE(v string) predicate.Menu {
	return predicate.Menu(sql.FieldGTE(FieldIcon, v))
}

// IconLT applies the LT predicate on the "icon" field.
func IconLT(v string) predicate.Menu {
	return predicate.Menu(sql.FieldLT(FieldIcon, v))
}

// IconLTE applies the LTE predicate on the "icon" field.
func IconLTE(v string) predicate.Menu {
	return predicate.Menu(sql.FieldLTE(FieldIcon, v))
}

// IconContains applies the Contains predicate on the "icon" field.
func IconContains(v string) predicate.Menu {
	return predicate.Menu(sql.FieldContains(FieldIcon, v))
}

// IconHasPrefix applies the HasPrefix predicate on the "icon" field.
func IconHasPrefix(v string) predicate.Menu {
	return predicate.Menu(sql.FieldHasPrefix(FieldIcon, v))
}

// IconHasSuffix applies the HasSuffix predicate on the "icon" field.
func IconHasSuffix(v string) predicate.Menu {
	return predicate.Menu(sql.FieldHasSuffix(FieldIcon, v))
}

// IconEqualFold applies the EqualFold predicate on the "icon" field.
func IconEqualFold(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEqualFold(FieldIcon, v))
}

// IconContainsFold applies the ContainsFold predicate on the "icon" field.
func IconContainsFold(v string) predicate.Menu {
	return predicate.Menu(sql.FieldContainsFold(FieldIcon, v))
}

// ParentIDEQ applies the EQ predicate on the "parent_id" field.
func ParentIDEQ(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldParentID, v))
}

// ParentIDNEQ applies the NEQ predicate on the "parent_id" field.
func ParentIDNEQ(v string) predicate.Menu {
	return predicate.Menu(sql.FieldNEQ(FieldParentID, v))
}

// ParentIDIn applies the In predicate on the "parent_id" field.
func ParentIDIn(vs ...string) predicate.Menu {
	return predicate.Menu(sql.FieldIn(FieldParentID, vs...))
}

// ParentIDNotIn applies the NotIn predicate on the "parent_id" field.
func ParentIDNotIn(vs ...string) predicate.Menu {
	return predicate.Menu(sql.FieldNotIn(FieldParentID, vs...))
}

// ParentIDGT applies the GT predicate on the "parent_id" field.
func ParentIDGT(v string) predicate.Menu {
	return predicate.Menu(sql.FieldGT(FieldParentID, v))
}

// ParentIDGTE applies the GTE predicate on the "parent_id" field.
func ParentIDGTE(v string) predicate.Menu {
	return predicate.Menu(sql.FieldGTE(FieldParentID, v))
}

// ParentIDLT applies the LT predicate on the "parent_id" field.
func ParentIDLT(v string) predicate.Menu {
	return predicate.Menu(sql.FieldLT(FieldParentID, v))
}

// ParentIDLTE applies the LTE predicate on the "parent_id" field.
func ParentIDLTE(v string) predicate.Menu {
	return predicate.Menu(sql.FieldLTE(FieldParentID, v))
}

// ParentIDContains applies the Contains predicate on the "parent_id" field.
func ParentIDContains(v string) predicate.Menu {
	return predicate.Menu(sql.FieldContains(FieldParentID, v))
}

// ParentIDHasPrefix applies the HasPrefix predicate on the "parent_id" field.
func ParentIDHasPrefix(v string) predicate.Menu {
	return predicate.Menu(sql.FieldHasPrefix(FieldParentID, v))
}

// ParentIDHasSuffix applies the HasSuffix predicate on the "parent_id" field.
func ParentIDHasSuffix(v string) predicate.Menu {
	return predicate.Menu(sql.FieldHasSuffix(FieldParentID, v))
}

// ParentIDIsNil applies the IsNil predicate on the "parent_id" field.
func ParentIDIsNil() predicate.Menu {
	return predicate.Menu(sql.FieldIsNull(FieldParentID))
}

// ParentIDNotNil applies the NotNil predicate on the "parent_id" field.
func ParentIDNotNil() predicate.Menu {
	return predicate.Menu(sql.FieldNotNull(FieldParentID))
}

// ParentIDEqualFold applies the EqualFold predicate on the "parent_id" field.
func ParentIDEqualFold(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEqualFold(FieldParentID, v))
}

// ParentIDContainsFold applies the ContainsFold predicate on the "parent_id" field.
func ParentIDContainsFold(v string) predicate.Menu {
	return predicate.Menu(sql.FieldContainsFold(FieldParentID, v))
}

// MenuTypeEQ applies the EQ predicate on the "menu_type" field.
func MenuTypeEQ(v uint32) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldMenuType, v))
}

// MenuTypeNEQ applies the NEQ predicate on the "menu_type" field.
func MenuTypeNEQ(v uint32) predicate.Menu {
	return predicate.Menu(sql.FieldNEQ(FieldMenuType, v))
}

// MenuTypeIn applies the In predicate on the "menu_type" field.
func MenuTypeIn(vs ...uint32) predicate.Menu {
	return predicate.Menu(sql.FieldIn(FieldMenuType, vs...))
}

// MenuTypeNotIn applies the NotIn predicate on the "menu_type" field.
func MenuTypeNotIn(vs ...uint32) predicate.Menu {
	return predicate.Menu(sql.FieldNotIn(FieldMenuType, vs...))
}

// MenuTypeGT applies the GT predicate on the "menu_type" field.
func MenuTypeGT(v uint32) predicate.Menu {
	return predicate.Menu(sql.FieldGT(FieldMenuType, v))
}

// MenuTypeGTE applies the GTE predicate on the "menu_type" field.
func MenuTypeGTE(v uint32) predicate.Menu {
	return predicate.Menu(sql.FieldGTE(FieldMenuType, v))
}

// MenuTypeLT applies the LT predicate on the "menu_type" field.
func MenuTypeLT(v uint32) predicate.Menu {
	return predicate.Menu(sql.FieldLT(FieldMenuType, v))
}

// MenuTypeLTE applies the LTE predicate on the "menu_type" field.
func MenuTypeLTE(v uint32) predicate.Menu {
	return predicate.Menu(sql.FieldLTE(FieldMenuType, v))
}

// URLEQ applies the EQ predicate on the "url" field.
func URLEQ(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldURL, v))
}

// URLNEQ applies the NEQ predicate on the "url" field.
func URLNEQ(v string) predicate.Menu {
	return predicate.Menu(sql.FieldNEQ(FieldURL, v))
}

// URLIn applies the In predicate on the "url" field.
func URLIn(vs ...string) predicate.Menu {
	return predicate.Menu(sql.FieldIn(FieldURL, vs...))
}

// URLNotIn applies the NotIn predicate on the "url" field.
func URLNotIn(vs ...string) predicate.Menu {
	return predicate.Menu(sql.FieldNotIn(FieldURL, vs...))
}

// URLGT applies the GT predicate on the "url" field.
func URLGT(v string) predicate.Menu {
	return predicate.Menu(sql.FieldGT(FieldURL, v))
}

// URLGTE applies the GTE predicate on the "url" field.
func URLGTE(v string) predicate.Menu {
	return predicate.Menu(sql.FieldGTE(FieldURL, v))
}

// URLLT applies the LT predicate on the "url" field.
func URLLT(v string) predicate.Menu {
	return predicate.Menu(sql.FieldLT(FieldURL, v))
}

// URLLTE applies the LTE predicate on the "url" field.
func URLLTE(v string) predicate.Menu {
	return predicate.Menu(sql.FieldLTE(FieldURL, v))
}

// URLContains applies the Contains predicate on the "url" field.
func URLContains(v string) predicate.Menu {
	return predicate.Menu(sql.FieldContains(FieldURL, v))
}

// URLHasPrefix applies the HasPrefix predicate on the "url" field.
func URLHasPrefix(v string) predicate.Menu {
	return predicate.Menu(sql.FieldHasPrefix(FieldURL, v))
}

// URLHasSuffix applies the HasSuffix predicate on the "url" field.
func URLHasSuffix(v string) predicate.Menu {
	return predicate.Menu(sql.FieldHasSuffix(FieldURL, v))
}

// URLIsNil applies the IsNil predicate on the "url" field.
func URLIsNil() predicate.Menu {
	return predicate.Menu(sql.FieldIsNull(FieldURL))
}

// URLNotNil applies the NotNil predicate on the "url" field.
func URLNotNil() predicate.Menu {
	return predicate.Menu(sql.FieldNotNull(FieldURL))
}

// URLEqualFold applies the EqualFold predicate on the "url" field.
func URLEqualFold(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEqualFold(FieldURL, v))
}

// URLContainsFold applies the ContainsFold predicate on the "url" field.
func URLContainsFold(v string) predicate.Menu {
	return predicate.Menu(sql.FieldContainsFold(FieldURL, v))
}

// RedirectEQ applies the EQ predicate on the "redirect" field.
func RedirectEQ(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldRedirect, v))
}

// RedirectNEQ applies the NEQ predicate on the "redirect" field.
func RedirectNEQ(v string) predicate.Menu {
	return predicate.Menu(sql.FieldNEQ(FieldRedirect, v))
}

// RedirectIn applies the In predicate on the "redirect" field.
func RedirectIn(vs ...string) predicate.Menu {
	return predicate.Menu(sql.FieldIn(FieldRedirect, vs...))
}

// RedirectNotIn applies the NotIn predicate on the "redirect" field.
func RedirectNotIn(vs ...string) predicate.Menu {
	return predicate.Menu(sql.FieldNotIn(FieldRedirect, vs...))
}

// RedirectGT applies the GT predicate on the "redirect" field.
func RedirectGT(v string) predicate.Menu {
	return predicate.Menu(sql.FieldGT(FieldRedirect, v))
}

// RedirectGTE applies the GTE predicate on the "redirect" field.
func RedirectGTE(v string) predicate.Menu {
	return predicate.Menu(sql.FieldGTE(FieldRedirect, v))
}

// RedirectLT applies the LT predicate on the "redirect" field.
func RedirectLT(v string) predicate.Menu {
	return predicate.Menu(sql.FieldLT(FieldRedirect, v))
}

// RedirectLTE applies the LTE predicate on the "redirect" field.
func RedirectLTE(v string) predicate.Menu {
	return predicate.Menu(sql.FieldLTE(FieldRedirect, v))
}

// RedirectContains applies the Contains predicate on the "redirect" field.
func RedirectContains(v string) predicate.Menu {
	return predicate.Menu(sql.FieldContains(FieldRedirect, v))
}

// RedirectHasPrefix applies the HasPrefix predicate on the "redirect" field.
func RedirectHasPrefix(v string) predicate.Menu {
	return predicate.Menu(sql.FieldHasPrefix(FieldRedirect, v))
}

// RedirectHasSuffix applies the HasSuffix predicate on the "redirect" field.
func RedirectHasSuffix(v string) predicate.Menu {
	return predicate.Menu(sql.FieldHasSuffix(FieldRedirect, v))
}

// RedirectIsNil applies the IsNil predicate on the "redirect" field.
func RedirectIsNil() predicate.Menu {
	return predicate.Menu(sql.FieldIsNull(FieldRedirect))
}

// RedirectNotNil applies the NotNil predicate on the "redirect" field.
func RedirectNotNil() predicate.Menu {
	return predicate.Menu(sql.FieldNotNull(FieldRedirect))
}

// RedirectEqualFold applies the EqualFold predicate on the "redirect" field.
func RedirectEqualFold(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEqualFold(FieldRedirect, v))
}

// RedirectContainsFold applies the ContainsFold predicate on the "redirect" field.
func RedirectContainsFold(v string) predicate.Menu {
	return predicate.Menu(sql.FieldContainsFold(FieldRedirect, v))
}

// ComponentEQ applies the EQ predicate on the "component" field.
func ComponentEQ(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldComponent, v))
}

// ComponentNEQ applies the NEQ predicate on the "component" field.
func ComponentNEQ(v string) predicate.Menu {
	return predicate.Menu(sql.FieldNEQ(FieldComponent, v))
}

// ComponentIn applies the In predicate on the "component" field.
func ComponentIn(vs ...string) predicate.Menu {
	return predicate.Menu(sql.FieldIn(FieldComponent, vs...))
}

// ComponentNotIn applies the NotIn predicate on the "component" field.
func ComponentNotIn(vs ...string) predicate.Menu {
	return predicate.Menu(sql.FieldNotIn(FieldComponent, vs...))
}

// ComponentGT applies the GT predicate on the "component" field.
func ComponentGT(v string) predicate.Menu {
	return predicate.Menu(sql.FieldGT(FieldComponent, v))
}

// ComponentGTE applies the GTE predicate on the "component" field.
func ComponentGTE(v string) predicate.Menu {
	return predicate.Menu(sql.FieldGTE(FieldComponent, v))
}

// ComponentLT applies the LT predicate on the "component" field.
func ComponentLT(v string) predicate.Menu {
	return predicate.Menu(sql.FieldLT(FieldComponent, v))
}

// ComponentLTE applies the LTE predicate on the "component" field.
func ComponentLTE(v string) predicate.Menu {
	return predicate.Menu(sql.FieldLTE(FieldComponent, v))
}

// ComponentContains applies the Contains predicate on the "component" field.
func ComponentContains(v string) predicate.Menu {
	return predicate.Menu(sql.FieldContains(FieldComponent, v))
}

// ComponentHasPrefix applies the HasPrefix predicate on the "component" field.
func ComponentHasPrefix(v string) predicate.Menu {
	return predicate.Menu(sql.FieldHasPrefix(FieldComponent, v))
}

// ComponentHasSuffix applies the HasSuffix predicate on the "component" field.
func ComponentHasSuffix(v string) predicate.Menu {
	return predicate.Menu(sql.FieldHasSuffix(FieldComponent, v))
}

// ComponentIsNil applies the IsNil predicate on the "component" field.
func ComponentIsNil() predicate.Menu {
	return predicate.Menu(sql.FieldIsNull(FieldComponent))
}

// ComponentNotNil applies the NotNil predicate on the "component" field.
func ComponentNotNil() predicate.Menu {
	return predicate.Menu(sql.FieldNotNull(FieldComponent))
}

// ComponentEqualFold applies the EqualFold predicate on the "component" field.
func ComponentEqualFold(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEqualFold(FieldComponent, v))
}

// ComponentContainsFold applies the ContainsFold predicate on the "component" field.
func ComponentContainsFold(v string) predicate.Menu {
	return predicate.Menu(sql.FieldContainsFold(FieldComponent, v))
}

// IsActiveEQ applies the EQ predicate on the "is_active" field.
func IsActiveEQ(v bool) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldIsActive, v))
}

// IsActiveNEQ applies the NEQ predicate on the "is_active" field.
func IsActiveNEQ(v bool) predicate.Menu {
	return predicate.Menu(sql.FieldNEQ(FieldIsActive, v))
}

// IsActiveIsNil applies the IsNil predicate on the "is_active" field.
func IsActiveIsNil() predicate.Menu {
	return predicate.Menu(sql.FieldIsNull(FieldIsActive))
}

// IsActiveNotNil applies the NotNil predicate on the "is_active" field.
func IsActiveNotNil() predicate.Menu {
	return predicate.Menu(sql.FieldNotNull(FieldIsActive))
}

// HiddenEQ applies the EQ predicate on the "hidden" field.
func HiddenEQ(v bool) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldHidden, v))
}

// HiddenNEQ applies the NEQ predicate on the "hidden" field.
func HiddenNEQ(v bool) predicate.Menu {
	return predicate.Menu(sql.FieldNEQ(FieldHidden, v))
}

// HiddenIsNil applies the IsNil predicate on the "hidden" field.
func HiddenIsNil() predicate.Menu {
	return predicate.Menu(sql.FieldIsNull(FieldHidden))
}

// HiddenNotNil applies the NotNil predicate on the "hidden" field.
func HiddenNotNil() predicate.Menu {
	return predicate.Menu(sql.FieldNotNull(FieldHidden))
}

// HiddenInTabEQ applies the EQ predicate on the "hidden_in_tab" field.
func HiddenInTabEQ(v bool) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldHiddenInTab, v))
}

// HiddenInTabNEQ applies the NEQ predicate on the "hidden_in_tab" field.
func HiddenInTabNEQ(v bool) predicate.Menu {
	return predicate.Menu(sql.FieldNEQ(FieldHiddenInTab, v))
}

// HiddenInTabIsNil applies the IsNil predicate on the "hidden_in_tab" field.
func HiddenInTabIsNil() predicate.Menu {
	return predicate.Menu(sql.FieldIsNull(FieldHiddenInTab))
}

// HiddenInTabNotNil applies the NotNil predicate on the "hidden_in_tab" field.
func HiddenInTabNotNil() predicate.Menu {
	return predicate.Menu(sql.FieldNotNull(FieldHiddenInTab))
}

// FixedEQ applies the EQ predicate on the "fixed" field.
func FixedEQ(v bool) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldFixed, v))
}

// FixedNEQ applies the NEQ predicate on the "fixed" field.
func FixedNEQ(v bool) predicate.Menu {
	return predicate.Menu(sql.FieldNEQ(FieldFixed, v))
}

// FixedIsNil applies the IsNil predicate on the "fixed" field.
func FixedIsNil() predicate.Menu {
	return predicate.Menu(sql.FieldIsNull(FieldFixed))
}

// FixedNotNil applies the NotNil predicate on the "fixed" field.
func FixedNotNil() predicate.Menu {
	return predicate.Menu(sql.FieldNotNull(FieldFixed))
}

// RemarkEQ applies the EQ predicate on the "remark" field.
func RemarkEQ(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldRemark, v))
}

// RemarkNEQ applies the NEQ predicate on the "remark" field.
func RemarkNEQ(v string) predicate.Menu {
	return predicate.Menu(sql.FieldNEQ(FieldRemark, v))
}

// RemarkIn applies the In predicate on the "remark" field.
func RemarkIn(vs ...string) predicate.Menu {
	return predicate.Menu(sql.FieldIn(FieldRemark, vs...))
}

// RemarkNotIn applies the NotIn predicate on the "remark" field.
func RemarkNotIn(vs ...string) predicate.Menu {
	return predicate.Menu(sql.FieldNotIn(FieldRemark, vs...))
}

// RemarkGT applies the GT predicate on the "remark" field.
func RemarkGT(v string) predicate.Menu {
	return predicate.Menu(sql.FieldGT(FieldRemark, v))
}

// RemarkGTE applies the GTE predicate on the "remark" field.
func RemarkGTE(v string) predicate.Menu {
	return predicate.Menu(sql.FieldGTE(FieldRemark, v))
}

// RemarkLT applies the LT predicate on the "remark" field.
func RemarkLT(v string) predicate.Menu {
	return predicate.Menu(sql.FieldLT(FieldRemark, v))
}

// RemarkLTE applies the LTE predicate on the "remark" field.
func RemarkLTE(v string) predicate.Menu {
	return predicate.Menu(sql.FieldLTE(FieldRemark, v))
}

// RemarkContains applies the Contains predicate on the "remark" field.
func RemarkContains(v string) predicate.Menu {
	return predicate.Menu(sql.FieldContains(FieldRemark, v))
}

// RemarkHasPrefix applies the HasPrefix predicate on the "remark" field.
func RemarkHasPrefix(v string) predicate.Menu {
	return predicate.Menu(sql.FieldHasPrefix(FieldRemark, v))
}

// RemarkHasSuffix applies the HasSuffix predicate on the "remark" field.
func RemarkHasSuffix(v string) predicate.Menu {
	return predicate.Menu(sql.FieldHasSuffix(FieldRemark, v))
}

// RemarkIsNil applies the IsNil predicate on the "remark" field.
func RemarkIsNil() predicate.Menu {
	return predicate.Menu(sql.FieldIsNull(FieldRemark))
}

// RemarkNotNil applies the NotNil predicate on the "remark" field.
func RemarkNotNil() predicate.Menu {
	return predicate.Menu(sql.FieldNotNull(FieldRemark))
}

// RemarkEqualFold applies the EqualFold predicate on the "remark" field.
func RemarkEqualFold(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEqualFold(FieldRemark, v))
}

// RemarkContainsFold applies the ContainsFold predicate on the "remark" field.
func RemarkContainsFold(v string) predicate.Menu {
	return predicate.Menu(sql.FieldContainsFold(FieldRemark, v))
}

// MetaEQ applies the EQ predicate on the "meta" field.
func MetaEQ(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldMeta, v))
}

// MetaNEQ applies the NEQ predicate on the "meta" field.
func MetaNEQ(v string) predicate.Menu {
	return predicate.Menu(sql.FieldNEQ(FieldMeta, v))
}

// MetaIn applies the In predicate on the "meta" field.
func MetaIn(vs ...string) predicate.Menu {
	return predicate.Menu(sql.FieldIn(FieldMeta, vs...))
}

// MetaNotIn applies the NotIn predicate on the "meta" field.
func MetaNotIn(vs ...string) predicate.Menu {
	return predicate.Menu(sql.FieldNotIn(FieldMeta, vs...))
}

// MetaGT applies the GT predicate on the "meta" field.
func MetaGT(v string) predicate.Menu {
	return predicate.Menu(sql.FieldGT(FieldMeta, v))
}

// MetaGTE applies the GTE predicate on the "meta" field.
func MetaGTE(v string) predicate.Menu {
	return predicate.Menu(sql.FieldGTE(FieldMeta, v))
}

// MetaLT applies the LT predicate on the "meta" field.
func MetaLT(v string) predicate.Menu {
	return predicate.Menu(sql.FieldLT(FieldMeta, v))
}

// MetaLTE applies the LTE predicate on the "meta" field.
func MetaLTE(v string) predicate.Menu {
	return predicate.Menu(sql.FieldLTE(FieldMeta, v))
}

// MetaContains applies the Contains predicate on the "meta" field.
func MetaContains(v string) predicate.Menu {
	return predicate.Menu(sql.FieldContains(FieldMeta, v))
}

// MetaHasPrefix applies the HasPrefix predicate on the "meta" field.
func MetaHasPrefix(v string) predicate.Menu {
	return predicate.Menu(sql.FieldHasPrefix(FieldMeta, v))
}

// MetaHasSuffix applies the HasSuffix predicate on the "meta" field.
func MetaHasSuffix(v string) predicate.Menu {
	return predicate.Menu(sql.FieldHasSuffix(FieldMeta, v))
}

// MetaIsNil applies the IsNil predicate on the "meta" field.
func MetaIsNil() predicate.Menu {
	return predicate.Menu(sql.FieldIsNull(FieldMeta))
}

// MetaNotNil applies the NotNil predicate on the "meta" field.
func MetaNotNil() predicate.Menu {
	return predicate.Menu(sql.FieldNotNull(FieldMeta))
}

// MetaEqualFold applies the EqualFold predicate on the "meta" field.
func MetaEqualFold(v string) predicate.Menu {
	return predicate.Menu(sql.FieldEqualFold(FieldMeta, v))
}

// MetaContainsFold applies the ContainsFold predicate on the "meta" field.
func MetaContainsFold(v string) predicate.Menu {
	return predicate.Menu(sql.FieldContainsFold(FieldMeta, v))
}

// IsFullPageEQ applies the EQ predicate on the "is_full_page" field.
func IsFullPageEQ(v bool) predicate.Menu {
	return predicate.Menu(sql.FieldEQ(FieldIsFullPage, v))
}

// IsFullPageNEQ applies the NEQ predicate on the "is_full_page" field.
func IsFullPageNEQ(v bool) predicate.Menu {
	return predicate.Menu(sql.FieldNEQ(FieldIsFullPage, v))
}

// IsFullPageIsNil applies the IsNil predicate on the "is_full_page" field.
func IsFullPageIsNil() predicate.Menu {
	return predicate.Menu(sql.FieldIsNull(FieldIsFullPage))
}

// IsFullPageNotNil applies the NotNil predicate on the "is_full_page" field.
func IsFullPageNotNil() predicate.Menu {
	return predicate.Menu(sql.FieldNotNull(FieldIsFullPage))
}

// HasRoles applies the HasEdge predicate on the "roles" edge.
func HasRoles() predicate.Menu {
	return predicate.Menu(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, RolesTable, RolesPrimaryKey...),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasRolesWith applies the HasEdge predicate on the "roles" edge with a given conditions (other predicates).
func HasRolesWith(preds ...predicate.Role) predicate.Menu {
	return predicate.Menu(func(s *sql.Selector) {
		step := newRolesStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasButtons applies the HasEdge predicate on the "buttons" edge.
func HasButtons() predicate.Menu {
	return predicate.Menu(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, ButtonsTable, ButtonsColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasButtonsWith applies the HasEdge predicate on the "buttons" edge with a given conditions (other predicates).
func HasButtonsWith(preds ...predicate.Button) predicate.Menu {
	return predicate.Menu(func(s *sql.Selector) {
		step := newButtonsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasParent applies the HasEdge predicate on the "parent" edge.
func HasParent() predicate.Menu {
	return predicate.Menu(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, ParentTable, ParentColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasParentWith applies the HasEdge predicate on the "parent" edge with a given conditions (other predicates).
func HasParentWith(preds ...predicate.Menu) predicate.Menu {
	return predicate.Menu(func(s *sql.Selector) {
		step := newParentStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasChildren applies the HasEdge predicate on the "children" edge.
func HasChildren() predicate.Menu {
	return predicate.Menu(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, ChildrenTable, ChildrenColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasChildrenWith applies the HasEdge predicate on the "children" edge with a given conditions (other predicates).
func HasChildrenWith(preds ...predicate.Menu) predicate.Menu {
	return predicate.Menu(func(s *sql.Selector) {
		step := newChildrenStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.Menu) predicate.Menu {
	return predicate.Menu(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.Menu) predicate.Menu {
	return predicate.Menu(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.Menu) predicate.Menu {
	return predicate.Menu(sql.NotPredicates(p))
}
