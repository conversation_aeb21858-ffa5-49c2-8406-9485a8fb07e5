// Code generated by ent, DO NOT EDIT.

package menu

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the menu type in the database.
	Label = "menu"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldSort holds the string denoting the sort field in the database.
	FieldSort = "sort"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// FieldName holds the string denoting the name field in the database.
	FieldName = "name"
	// FieldTitle holds the string denoting the title field in the database.
	FieldTitle = "title"
	// FieldIcon holds the string denoting the icon field in the database.
	FieldIcon = "icon"
	// FieldParentID holds the string denoting the parent_id field in the database.
	FieldParentID = "parent_id"
	// FieldMenuType holds the string denoting the menu_type field in the database.
	FieldMenuType = "menu_type"
	// FieldURL holds the string denoting the url field in the database.
	FieldURL = "url"
	// FieldRedirect holds the string denoting the redirect field in the database.
	FieldRedirect = "redirect"
	// FieldComponent holds the string denoting the component field in the database.
	FieldComponent = "component"
	// FieldIsActive holds the string denoting the is_active field in the database.
	FieldIsActive = "is_active"
	// FieldHidden holds the string denoting the hidden field in the database.
	FieldHidden = "hidden"
	// FieldHiddenInTab holds the string denoting the hidden_in_tab field in the database.
	FieldHiddenInTab = "hidden_in_tab"
	// FieldFixed holds the string denoting the fixed field in the database.
	FieldFixed = "fixed"
	// FieldRemark holds the string denoting the remark field in the database.
	FieldRemark = "remark"
	// FieldMeta holds the string denoting the meta field in the database.
	FieldMeta = "meta"
	// FieldIsFullPage holds the string denoting the is_full_page field in the database.
	FieldIsFullPage = "is_full_page"
	// EdgeRoles holds the string denoting the roles edge name in mutations.
	EdgeRoles = "roles"
	// EdgeButtons holds the string denoting the buttons edge name in mutations.
	EdgeButtons = "buttons"
	// EdgeParent holds the string denoting the parent edge name in mutations.
	EdgeParent = "parent"
	// EdgeChildren holds the string denoting the children edge name in mutations.
	EdgeChildren = "children"
	// Table holds the table name of the menu in the database.
	Table = "saas_menu"
	// RolesTable is the table that holds the roles relation/edge. The primary key declared below.
	RolesTable = "role_menus"
	// RolesInverseTable is the table name for the Role entity.
	// It exists in this package in order to avoid circular dependency with the "role" package.
	RolesInverseTable = "saas_role"
	// ButtonsTable is the table that holds the buttons relation/edge.
	ButtonsTable = "saas_button"
	// ButtonsInverseTable is the table name for the Button entity.
	// It exists in this package in order to avoid circular dependency with the "button" package.
	ButtonsInverseTable = "saas_button"
	// ButtonsColumn is the table column denoting the buttons relation/edge.
	ButtonsColumn = "menu_id"
	// ParentTable is the table that holds the parent relation/edge.
	ParentTable = "saas_menu"
	// ParentColumn is the table column denoting the parent relation/edge.
	ParentColumn = "parent_id"
	// ChildrenTable is the table that holds the children relation/edge.
	ChildrenTable = "saas_menu"
	// ChildrenColumn is the table column denoting the children relation/edge.
	ChildrenColumn = "parent_id"
)

// Columns holds all SQL columns for menu fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldSort,
	FieldDeletedAt,
	FieldName,
	FieldTitle,
	FieldIcon,
	FieldParentID,
	FieldMenuType,
	FieldURL,
	FieldRedirect,
	FieldComponent,
	FieldIsActive,
	FieldHidden,
	FieldHiddenInTab,
	FieldFixed,
	FieldRemark,
	FieldMeta,
	FieldIsFullPage,
}

// ForeignKeys holds the SQL foreign-keys that are owned by the "saas_menu"
// table and are not defined as standalone fields in the schema.
var ForeignKeys = []string{
	"tenant_menus",
}

var (
	// RolesPrimaryKey and RolesColumn2 are the table columns denoting the
	// primary key for the roles relation (M2M).
	RolesPrimaryKey = []string{"role_id", "menu_id"}
)

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	for i := range ForeignKeys {
		if column == ForeignKeys[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "phoenix/service/saas/model/ent/runtime"
var (
	Hooks        [1]ent.Hook
	Interceptors [1]ent.Interceptor
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultSort holds the default value on creation for the "sort" field.
	DefaultSort uint32
	// DefaultURL holds the default value on creation for the "url" field.
	DefaultURL string
	// DefaultRedirect holds the default value on creation for the "redirect" field.
	DefaultRedirect string
	// DefaultComponent holds the default value on creation for the "component" field.
	DefaultComponent string
	// DefaultIsActive holds the default value on creation for the "is_active" field.
	DefaultIsActive bool
	// DefaultHidden holds the default value on creation for the "hidden" field.
	DefaultHidden bool
	// DefaultHiddenInTab holds the default value on creation for the "hidden_in_tab" field.
	DefaultHiddenInTab bool
	// DefaultFixed holds the default value on creation for the "fixed" field.
	DefaultFixed bool
	// DefaultRemark holds the default value on creation for the "remark" field.
	DefaultRemark string
	// DefaultMeta holds the default value on creation for the "meta" field.
	DefaultMeta string
	// DefaultIsFullPage holds the default value on creation for the "is_full_page" field.
	DefaultIsFullPage bool
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() string
)

// OrderOption defines the ordering options for the Menu queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// BySort orders the results by the sort field.
func BySort(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSort, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// ByName orders the results by the name field.
func ByName(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldName, opts...).ToFunc()
}

// ByTitle orders the results by the title field.
func ByTitle(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTitle, opts...).ToFunc()
}

// ByIcon orders the results by the icon field.
func ByIcon(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIcon, opts...).ToFunc()
}

// ByParentID orders the results by the parent_id field.
func ByParentID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldParentID, opts...).ToFunc()
}

// ByMenuType orders the results by the menu_type field.
func ByMenuType(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMenuType, opts...).ToFunc()
}

// ByURL orders the results by the url field.
func ByURL(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldURL, opts...).ToFunc()
}

// ByRedirect orders the results by the redirect field.
func ByRedirect(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRedirect, opts...).ToFunc()
}

// ByComponent orders the results by the component field.
func ByComponent(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldComponent, opts...).ToFunc()
}

// ByIsActive orders the results by the is_active field.
func ByIsActive(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsActive, opts...).ToFunc()
}

// ByHidden orders the results by the hidden field.
func ByHidden(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldHidden, opts...).ToFunc()
}

// ByHiddenInTab orders the results by the hidden_in_tab field.
func ByHiddenInTab(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldHiddenInTab, opts...).ToFunc()
}

// ByFixed orders the results by the fixed field.
func ByFixed(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldFixed, opts...).ToFunc()
}

// ByRemark orders the results by the remark field.
func ByRemark(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldRemark, opts...).ToFunc()
}

// ByMeta orders the results by the meta field.
func ByMeta(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMeta, opts...).ToFunc()
}

// ByIsFullPage orders the results by the is_full_page field.
func ByIsFullPage(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsFullPage, opts...).ToFunc()
}

// ByRolesCount orders the results by roles count.
func ByRolesCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newRolesStep(), opts...)
	}
}

// ByRoles orders the results by roles terms.
func ByRoles(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newRolesStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByButtonsCount orders the results by buttons count.
func ByButtonsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newButtonsStep(), opts...)
	}
}

// ByButtons orders the results by buttons terms.
func ByButtons(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newButtonsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByParentField orders the results by parent field.
func ByParentField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newParentStep(), sql.OrderByField(field, opts...))
	}
}

// ByChildrenCount orders the results by children count.
func ByChildrenCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newChildrenStep(), opts...)
	}
}

// ByChildren orders the results by children terms.
func ByChildren(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newChildrenStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}
func newRolesStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(RolesInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2M, true, RolesTable, RolesPrimaryKey...),
	)
}
func newButtonsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(ButtonsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, ButtonsTable, ButtonsColumn),
	)
}
func newParentStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(Table, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, ParentTable, ParentColumn),
	)
}
func newChildrenStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(Table, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, ChildrenTable, ChildrenColumn),
	)
}
