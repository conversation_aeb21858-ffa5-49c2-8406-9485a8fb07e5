// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/organization"
	"phoenix/service/saas/model/ent/organizationuserinfo"
	"phoenix/service/saas/model/ent/user"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// OrganizationUserInfoCreate is the builder for creating a OrganizationUserInfo entity.
type OrganizationUserInfoCreate struct {
	config
	mutation *OrganizationUserInfoMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (ouic *OrganizationUserInfoCreate) SetCreatedAt(t time.Time) *OrganizationUserInfoCreate {
	ouic.mutation.SetCreatedAt(t)
	return ouic
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (ouic *OrganizationUserInfoCreate) SetNillableCreatedAt(t *time.Time) *OrganizationUserInfoCreate {
	if t != nil {
		ouic.SetCreatedAt(*t)
	}
	return ouic
}

// SetUpdatedAt sets the "updated_at" field.
func (ouic *OrganizationUserInfoCreate) SetUpdatedAt(t time.Time) *OrganizationUserInfoCreate {
	ouic.mutation.SetUpdatedAt(t)
	return ouic
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (ouic *OrganizationUserInfoCreate) SetNillableUpdatedAt(t *time.Time) *OrganizationUserInfoCreate {
	if t != nil {
		ouic.SetUpdatedAt(*t)
	}
	return ouic
}

// SetSort sets the "sort" field.
func (ouic *OrganizationUserInfoCreate) SetSort(u uint32) *OrganizationUserInfoCreate {
	ouic.mutation.SetSort(u)
	return ouic
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (ouic *OrganizationUserInfoCreate) SetNillableSort(u *uint32) *OrganizationUserInfoCreate {
	if u != nil {
		ouic.SetSort(*u)
	}
	return ouic
}

// SetDeletedAt sets the "deleted_at" field.
func (ouic *OrganizationUserInfoCreate) SetDeletedAt(t time.Time) *OrganizationUserInfoCreate {
	ouic.mutation.SetDeletedAt(t)
	return ouic
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (ouic *OrganizationUserInfoCreate) SetNillableDeletedAt(t *time.Time) *OrganizationUserInfoCreate {
	if t != nil {
		ouic.SetDeletedAt(*t)
	}
	return ouic
}

// SetOrganizationID sets the "organization_id" field.
func (ouic *OrganizationUserInfoCreate) SetOrganizationID(s string) *OrganizationUserInfoCreate {
	ouic.mutation.SetOrganizationID(s)
	return ouic
}

// SetUserID sets the "user_id" field.
func (ouic *OrganizationUserInfoCreate) SetUserID(s string) *OrganizationUserInfoCreate {
	ouic.mutation.SetUserID(s)
	return ouic
}

// SetExtra sets the "extra" field.
func (ouic *OrganizationUserInfoCreate) SetExtra(s string) *OrganizationUserInfoCreate {
	ouic.mutation.SetExtra(s)
	return ouic
}

// SetNillableExtra sets the "extra" field if the given value is not nil.
func (ouic *OrganizationUserInfoCreate) SetNillableExtra(s *string) *OrganizationUserInfoCreate {
	if s != nil {
		ouic.SetExtra(*s)
	}
	return ouic
}

// SetIsLeader sets the "is_leader" field.
func (ouic *OrganizationUserInfoCreate) SetIsLeader(b bool) *OrganizationUserInfoCreate {
	ouic.mutation.SetIsLeader(b)
	return ouic
}

// SetNillableIsLeader sets the "is_leader" field if the given value is not nil.
func (ouic *OrganizationUserInfoCreate) SetNillableIsLeader(b *bool) *OrganizationUserInfoCreate {
	if b != nil {
		ouic.SetIsLeader(*b)
	}
	return ouic
}

// SetIsAdmin sets the "is_admin" field.
func (ouic *OrganizationUserInfoCreate) SetIsAdmin(b bool) *OrganizationUserInfoCreate {
	ouic.mutation.SetIsAdmin(b)
	return ouic
}

// SetNillableIsAdmin sets the "is_admin" field if the given value is not nil.
func (ouic *OrganizationUserInfoCreate) SetNillableIsAdmin(b *bool) *OrganizationUserInfoCreate {
	if b != nil {
		ouic.SetIsAdmin(*b)
	}
	return ouic
}

// SetID sets the "id" field.
func (ouic *OrganizationUserInfoCreate) SetID(s string) *OrganizationUserInfoCreate {
	ouic.mutation.SetID(s)
	return ouic
}

// SetNillableID sets the "id" field if the given value is not nil.
func (ouic *OrganizationUserInfoCreate) SetNillableID(s *string) *OrganizationUserInfoCreate {
	if s != nil {
		ouic.SetID(*s)
	}
	return ouic
}

// SetOrganization sets the "organization" edge to the Organization entity.
func (ouic *OrganizationUserInfoCreate) SetOrganization(o *Organization) *OrganizationUserInfoCreate {
	return ouic.SetOrganizationID(o.ID)
}

// SetUser sets the "user" edge to the User entity.
func (ouic *OrganizationUserInfoCreate) SetUser(u *User) *OrganizationUserInfoCreate {
	return ouic.SetUserID(u.ID)
}

// Mutation returns the OrganizationUserInfoMutation object of the builder.
func (ouic *OrganizationUserInfoCreate) Mutation() *OrganizationUserInfoMutation {
	return ouic.mutation
}

// Save creates the OrganizationUserInfo in the database.
func (ouic *OrganizationUserInfoCreate) Save(ctx context.Context) (*OrganizationUserInfo, error) {
	if err := ouic.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, ouic.sqlSave, ouic.mutation, ouic.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (ouic *OrganizationUserInfoCreate) SaveX(ctx context.Context) *OrganizationUserInfo {
	v, err := ouic.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ouic *OrganizationUserInfoCreate) Exec(ctx context.Context) error {
	_, err := ouic.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ouic *OrganizationUserInfoCreate) ExecX(ctx context.Context) {
	if err := ouic.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ouic *OrganizationUserInfoCreate) defaults() error {
	if _, ok := ouic.mutation.CreatedAt(); !ok {
		if organizationuserinfo.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized organizationuserinfo.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := organizationuserinfo.DefaultCreatedAt()
		ouic.mutation.SetCreatedAt(v)
	}
	if _, ok := ouic.mutation.UpdatedAt(); !ok {
		if organizationuserinfo.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized organizationuserinfo.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := organizationuserinfo.DefaultUpdatedAt()
		ouic.mutation.SetUpdatedAt(v)
	}
	if _, ok := ouic.mutation.Sort(); !ok {
		v := organizationuserinfo.DefaultSort
		ouic.mutation.SetSort(v)
	}
	if _, ok := ouic.mutation.Extra(); !ok {
		v := organizationuserinfo.DefaultExtra
		ouic.mutation.SetExtra(v)
	}
	if _, ok := ouic.mutation.IsLeader(); !ok {
		v := organizationuserinfo.DefaultIsLeader
		ouic.mutation.SetIsLeader(v)
	}
	if _, ok := ouic.mutation.IsAdmin(); !ok {
		v := organizationuserinfo.DefaultIsAdmin
		ouic.mutation.SetIsAdmin(v)
	}
	if _, ok := ouic.mutation.ID(); !ok {
		if organizationuserinfo.DefaultID == nil {
			return fmt.Errorf("ent: uninitialized organizationuserinfo.DefaultID (forgotten import ent/runtime?)")
		}
		v := organizationuserinfo.DefaultID()
		ouic.mutation.SetID(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (ouic *OrganizationUserInfoCreate) check() error {
	if _, ok := ouic.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "OrganizationUserInfo.created_at"`)}
	}
	if _, ok := ouic.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "OrganizationUserInfo.updated_at"`)}
	}
	if _, ok := ouic.mutation.Sort(); !ok {
		return &ValidationError{Name: "sort", err: errors.New(`ent: missing required field "OrganizationUserInfo.sort"`)}
	}
	if _, ok := ouic.mutation.OrganizationID(); !ok {
		return &ValidationError{Name: "organization_id", err: errors.New(`ent: missing required field "OrganizationUserInfo.organization_id"`)}
	}
	if _, ok := ouic.mutation.UserID(); !ok {
		return &ValidationError{Name: "user_id", err: errors.New(`ent: missing required field "OrganizationUserInfo.user_id"`)}
	}
	if _, ok := ouic.mutation.IsLeader(); !ok {
		return &ValidationError{Name: "is_leader", err: errors.New(`ent: missing required field "OrganizationUserInfo.is_leader"`)}
	}
	if _, ok := ouic.mutation.IsAdmin(); !ok {
		return &ValidationError{Name: "is_admin", err: errors.New(`ent: missing required field "OrganizationUserInfo.is_admin"`)}
	}
	if _, ok := ouic.mutation.OrganizationID(); !ok {
		return &ValidationError{Name: "organization", err: errors.New(`ent: missing required edge "OrganizationUserInfo.organization"`)}
	}
	if _, ok := ouic.mutation.UserID(); !ok {
		return &ValidationError{Name: "user", err: errors.New(`ent: missing required edge "OrganizationUserInfo.user"`)}
	}
	return nil
}

func (ouic *OrganizationUserInfoCreate) sqlSave(ctx context.Context) (*OrganizationUserInfo, error) {
	if err := ouic.check(); err != nil {
		return nil, err
	}
	_node, _spec := ouic.createSpec()
	if err := sqlgraph.CreateNode(ctx, ouic.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected OrganizationUserInfo.ID type: %T", _spec.ID.Value)
		}
	}
	ouic.mutation.id = &_node.ID
	ouic.mutation.done = true
	return _node, nil
}

func (ouic *OrganizationUserInfoCreate) createSpec() (*OrganizationUserInfo, *sqlgraph.CreateSpec) {
	var (
		_node = &OrganizationUserInfo{config: ouic.config}
		_spec = sqlgraph.NewCreateSpec(organizationuserinfo.Table, sqlgraph.NewFieldSpec(organizationuserinfo.FieldID, field.TypeString))
	)
	_spec.OnConflict = ouic.conflict
	if id, ok := ouic.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := ouic.mutation.CreatedAt(); ok {
		_spec.SetField(organizationuserinfo.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := ouic.mutation.UpdatedAt(); ok {
		_spec.SetField(organizationuserinfo.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := ouic.mutation.Sort(); ok {
		_spec.SetField(organizationuserinfo.FieldSort, field.TypeUint32, value)
		_node.Sort = value
	}
	if value, ok := ouic.mutation.DeletedAt(); ok {
		_spec.SetField(organizationuserinfo.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := ouic.mutation.Extra(); ok {
		_spec.SetField(organizationuserinfo.FieldExtra, field.TypeString, value)
		_node.Extra = value
	}
	if value, ok := ouic.mutation.IsLeader(); ok {
		_spec.SetField(organizationuserinfo.FieldIsLeader, field.TypeBool, value)
		_node.IsLeader = value
	}
	if value, ok := ouic.mutation.IsAdmin(); ok {
		_spec.SetField(organizationuserinfo.FieldIsAdmin, field.TypeBool, value)
		_node.IsAdmin = value
	}
	if nodes := ouic.mutation.OrganizationIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   organizationuserinfo.OrganizationTable,
			Columns: []string{organizationuserinfo.OrganizationColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(organization.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.OrganizationID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := ouic.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   organizationuserinfo.UserTable,
			Columns: []string{organizationuserinfo.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.UserID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.OrganizationUserInfo.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.OrganizationUserInfoUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (ouic *OrganizationUserInfoCreate) OnConflict(opts ...sql.ConflictOption) *OrganizationUserInfoUpsertOne {
	ouic.conflict = opts
	return &OrganizationUserInfoUpsertOne{
		create: ouic,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.OrganizationUserInfo.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (ouic *OrganizationUserInfoCreate) OnConflictColumns(columns ...string) *OrganizationUserInfoUpsertOne {
	ouic.conflict = append(ouic.conflict, sql.ConflictColumns(columns...))
	return &OrganizationUserInfoUpsertOne{
		create: ouic,
	}
}

type (
	// OrganizationUserInfoUpsertOne is the builder for "upsert"-ing
	//  one OrganizationUserInfo node.
	OrganizationUserInfoUpsertOne struct {
		create *OrganizationUserInfoCreate
	}

	// OrganizationUserInfoUpsert is the "OnConflict" setter.
	OrganizationUserInfoUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *OrganizationUserInfoUpsert) SetUpdatedAt(v time.Time) *OrganizationUserInfoUpsert {
	u.Set(organizationuserinfo.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *OrganizationUserInfoUpsert) UpdateUpdatedAt() *OrganizationUserInfoUpsert {
	u.SetExcluded(organizationuserinfo.FieldUpdatedAt)
	return u
}

// SetSort sets the "sort" field.
func (u *OrganizationUserInfoUpsert) SetSort(v uint32) *OrganizationUserInfoUpsert {
	u.Set(organizationuserinfo.FieldSort, v)
	return u
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *OrganizationUserInfoUpsert) UpdateSort() *OrganizationUserInfoUpsert {
	u.SetExcluded(organizationuserinfo.FieldSort)
	return u
}

// AddSort adds v to the "sort" field.
func (u *OrganizationUserInfoUpsert) AddSort(v uint32) *OrganizationUserInfoUpsert {
	u.Add(organizationuserinfo.FieldSort, v)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *OrganizationUserInfoUpsert) SetDeletedAt(v time.Time) *OrganizationUserInfoUpsert {
	u.Set(organizationuserinfo.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *OrganizationUserInfoUpsert) UpdateDeletedAt() *OrganizationUserInfoUpsert {
	u.SetExcluded(organizationuserinfo.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *OrganizationUserInfoUpsert) ClearDeletedAt() *OrganizationUserInfoUpsert {
	u.SetNull(organizationuserinfo.FieldDeletedAt)
	return u
}

// SetOrganizationID sets the "organization_id" field.
func (u *OrganizationUserInfoUpsert) SetOrganizationID(v string) *OrganizationUserInfoUpsert {
	u.Set(organizationuserinfo.FieldOrganizationID, v)
	return u
}

// UpdateOrganizationID sets the "organization_id" field to the value that was provided on create.
func (u *OrganizationUserInfoUpsert) UpdateOrganizationID() *OrganizationUserInfoUpsert {
	u.SetExcluded(organizationuserinfo.FieldOrganizationID)
	return u
}

// SetUserID sets the "user_id" field.
func (u *OrganizationUserInfoUpsert) SetUserID(v string) *OrganizationUserInfoUpsert {
	u.Set(organizationuserinfo.FieldUserID, v)
	return u
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *OrganizationUserInfoUpsert) UpdateUserID() *OrganizationUserInfoUpsert {
	u.SetExcluded(organizationuserinfo.FieldUserID)
	return u
}

// SetExtra sets the "extra" field.
func (u *OrganizationUserInfoUpsert) SetExtra(v string) *OrganizationUserInfoUpsert {
	u.Set(organizationuserinfo.FieldExtra, v)
	return u
}

// UpdateExtra sets the "extra" field to the value that was provided on create.
func (u *OrganizationUserInfoUpsert) UpdateExtra() *OrganizationUserInfoUpsert {
	u.SetExcluded(organizationuserinfo.FieldExtra)
	return u
}

// ClearExtra clears the value of the "extra" field.
func (u *OrganizationUserInfoUpsert) ClearExtra() *OrganizationUserInfoUpsert {
	u.SetNull(organizationuserinfo.FieldExtra)
	return u
}

// SetIsLeader sets the "is_leader" field.
func (u *OrganizationUserInfoUpsert) SetIsLeader(v bool) *OrganizationUserInfoUpsert {
	u.Set(organizationuserinfo.FieldIsLeader, v)
	return u
}

// UpdateIsLeader sets the "is_leader" field to the value that was provided on create.
func (u *OrganizationUserInfoUpsert) UpdateIsLeader() *OrganizationUserInfoUpsert {
	u.SetExcluded(organizationuserinfo.FieldIsLeader)
	return u
}

// SetIsAdmin sets the "is_admin" field.
func (u *OrganizationUserInfoUpsert) SetIsAdmin(v bool) *OrganizationUserInfoUpsert {
	u.Set(organizationuserinfo.FieldIsAdmin, v)
	return u
}

// UpdateIsAdmin sets the "is_admin" field to the value that was provided on create.
func (u *OrganizationUserInfoUpsert) UpdateIsAdmin() *OrganizationUserInfoUpsert {
	u.SetExcluded(organizationuserinfo.FieldIsAdmin)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.OrganizationUserInfo.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(organizationuserinfo.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *OrganizationUserInfoUpsertOne) UpdateNewValues() *OrganizationUserInfoUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(organizationuserinfo.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(organizationuserinfo.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.OrganizationUserInfo.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *OrganizationUserInfoUpsertOne) Ignore() *OrganizationUserInfoUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *OrganizationUserInfoUpsertOne) DoNothing() *OrganizationUserInfoUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the OrganizationUserInfoCreate.OnConflict
// documentation for more info.
func (u *OrganizationUserInfoUpsertOne) Update(set func(*OrganizationUserInfoUpsert)) *OrganizationUserInfoUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&OrganizationUserInfoUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *OrganizationUserInfoUpsertOne) SetUpdatedAt(v time.Time) *OrganizationUserInfoUpsertOne {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *OrganizationUserInfoUpsertOne) UpdateUpdatedAt() *OrganizationUserInfoUpsertOne {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetSort sets the "sort" field.
func (u *OrganizationUserInfoUpsertOne) SetSort(v uint32) *OrganizationUserInfoUpsertOne {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.SetSort(v)
	})
}

// AddSort adds v to the "sort" field.
func (u *OrganizationUserInfoUpsertOne) AddSort(v uint32) *OrganizationUserInfoUpsertOne {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.AddSort(v)
	})
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *OrganizationUserInfoUpsertOne) UpdateSort() *OrganizationUserInfoUpsertOne {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.UpdateSort()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *OrganizationUserInfoUpsertOne) SetDeletedAt(v time.Time) *OrganizationUserInfoUpsertOne {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *OrganizationUserInfoUpsertOne) UpdateDeletedAt() *OrganizationUserInfoUpsertOne {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *OrganizationUserInfoUpsertOne) ClearDeletedAt() *OrganizationUserInfoUpsertOne {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.ClearDeletedAt()
	})
}

// SetOrganizationID sets the "organization_id" field.
func (u *OrganizationUserInfoUpsertOne) SetOrganizationID(v string) *OrganizationUserInfoUpsertOne {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.SetOrganizationID(v)
	})
}

// UpdateOrganizationID sets the "organization_id" field to the value that was provided on create.
func (u *OrganizationUserInfoUpsertOne) UpdateOrganizationID() *OrganizationUserInfoUpsertOne {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.UpdateOrganizationID()
	})
}

// SetUserID sets the "user_id" field.
func (u *OrganizationUserInfoUpsertOne) SetUserID(v string) *OrganizationUserInfoUpsertOne {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.SetUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *OrganizationUserInfoUpsertOne) UpdateUserID() *OrganizationUserInfoUpsertOne {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.UpdateUserID()
	})
}

// SetExtra sets the "extra" field.
func (u *OrganizationUserInfoUpsertOne) SetExtra(v string) *OrganizationUserInfoUpsertOne {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.SetExtra(v)
	})
}

// UpdateExtra sets the "extra" field to the value that was provided on create.
func (u *OrganizationUserInfoUpsertOne) UpdateExtra() *OrganizationUserInfoUpsertOne {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.UpdateExtra()
	})
}

// ClearExtra clears the value of the "extra" field.
func (u *OrganizationUserInfoUpsertOne) ClearExtra() *OrganizationUserInfoUpsertOne {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.ClearExtra()
	})
}

// SetIsLeader sets the "is_leader" field.
func (u *OrganizationUserInfoUpsertOne) SetIsLeader(v bool) *OrganizationUserInfoUpsertOne {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.SetIsLeader(v)
	})
}

// UpdateIsLeader sets the "is_leader" field to the value that was provided on create.
func (u *OrganizationUserInfoUpsertOne) UpdateIsLeader() *OrganizationUserInfoUpsertOne {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.UpdateIsLeader()
	})
}

// SetIsAdmin sets the "is_admin" field.
func (u *OrganizationUserInfoUpsertOne) SetIsAdmin(v bool) *OrganizationUserInfoUpsertOne {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.SetIsAdmin(v)
	})
}

// UpdateIsAdmin sets the "is_admin" field to the value that was provided on create.
func (u *OrganizationUserInfoUpsertOne) UpdateIsAdmin() *OrganizationUserInfoUpsertOne {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.UpdateIsAdmin()
	})
}

// Exec executes the query.
func (u *OrganizationUserInfoUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for OrganizationUserInfoCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *OrganizationUserInfoUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *OrganizationUserInfoUpsertOne) ID(ctx context.Context) (id string, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: OrganizationUserInfoUpsertOne.ID is not supported by MySQL driver. Use OrganizationUserInfoUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *OrganizationUserInfoUpsertOne) IDX(ctx context.Context) string {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// OrganizationUserInfoCreateBulk is the builder for creating many OrganizationUserInfo entities in bulk.
type OrganizationUserInfoCreateBulk struct {
	config
	err      error
	builders []*OrganizationUserInfoCreate
	conflict []sql.ConflictOption
}

// Save creates the OrganizationUserInfo entities in the database.
func (ouicb *OrganizationUserInfoCreateBulk) Save(ctx context.Context) ([]*OrganizationUserInfo, error) {
	if ouicb.err != nil {
		return nil, ouicb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(ouicb.builders))
	nodes := make([]*OrganizationUserInfo, len(ouicb.builders))
	mutators := make([]Mutator, len(ouicb.builders))
	for i := range ouicb.builders {
		func(i int, root context.Context) {
			builder := ouicb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*OrganizationUserInfoMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, ouicb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = ouicb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, ouicb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, ouicb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (ouicb *OrganizationUserInfoCreateBulk) SaveX(ctx context.Context) []*OrganizationUserInfo {
	v, err := ouicb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (ouicb *OrganizationUserInfoCreateBulk) Exec(ctx context.Context) error {
	_, err := ouicb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ouicb *OrganizationUserInfoCreateBulk) ExecX(ctx context.Context) {
	if err := ouicb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.OrganizationUserInfo.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.OrganizationUserInfoUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (ouicb *OrganizationUserInfoCreateBulk) OnConflict(opts ...sql.ConflictOption) *OrganizationUserInfoUpsertBulk {
	ouicb.conflict = opts
	return &OrganizationUserInfoUpsertBulk{
		create: ouicb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.OrganizationUserInfo.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (ouicb *OrganizationUserInfoCreateBulk) OnConflictColumns(columns ...string) *OrganizationUserInfoUpsertBulk {
	ouicb.conflict = append(ouicb.conflict, sql.ConflictColumns(columns...))
	return &OrganizationUserInfoUpsertBulk{
		create: ouicb,
	}
}

// OrganizationUserInfoUpsertBulk is the builder for "upsert"-ing
// a bulk of OrganizationUserInfo nodes.
type OrganizationUserInfoUpsertBulk struct {
	create *OrganizationUserInfoCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.OrganizationUserInfo.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(organizationuserinfo.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *OrganizationUserInfoUpsertBulk) UpdateNewValues() *OrganizationUserInfoUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(organizationuserinfo.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(organizationuserinfo.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.OrganizationUserInfo.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *OrganizationUserInfoUpsertBulk) Ignore() *OrganizationUserInfoUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *OrganizationUserInfoUpsertBulk) DoNothing() *OrganizationUserInfoUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the OrganizationUserInfoCreateBulk.OnConflict
// documentation for more info.
func (u *OrganizationUserInfoUpsertBulk) Update(set func(*OrganizationUserInfoUpsert)) *OrganizationUserInfoUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&OrganizationUserInfoUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *OrganizationUserInfoUpsertBulk) SetUpdatedAt(v time.Time) *OrganizationUserInfoUpsertBulk {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *OrganizationUserInfoUpsertBulk) UpdateUpdatedAt() *OrganizationUserInfoUpsertBulk {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetSort sets the "sort" field.
func (u *OrganizationUserInfoUpsertBulk) SetSort(v uint32) *OrganizationUserInfoUpsertBulk {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.SetSort(v)
	})
}

// AddSort adds v to the "sort" field.
func (u *OrganizationUserInfoUpsertBulk) AddSort(v uint32) *OrganizationUserInfoUpsertBulk {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.AddSort(v)
	})
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *OrganizationUserInfoUpsertBulk) UpdateSort() *OrganizationUserInfoUpsertBulk {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.UpdateSort()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *OrganizationUserInfoUpsertBulk) SetDeletedAt(v time.Time) *OrganizationUserInfoUpsertBulk {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *OrganizationUserInfoUpsertBulk) UpdateDeletedAt() *OrganizationUserInfoUpsertBulk {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *OrganizationUserInfoUpsertBulk) ClearDeletedAt() *OrganizationUserInfoUpsertBulk {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.ClearDeletedAt()
	})
}

// SetOrganizationID sets the "organization_id" field.
func (u *OrganizationUserInfoUpsertBulk) SetOrganizationID(v string) *OrganizationUserInfoUpsertBulk {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.SetOrganizationID(v)
	})
}

// UpdateOrganizationID sets the "organization_id" field to the value that was provided on create.
func (u *OrganizationUserInfoUpsertBulk) UpdateOrganizationID() *OrganizationUserInfoUpsertBulk {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.UpdateOrganizationID()
	})
}

// SetUserID sets the "user_id" field.
func (u *OrganizationUserInfoUpsertBulk) SetUserID(v string) *OrganizationUserInfoUpsertBulk {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.SetUserID(v)
	})
}

// UpdateUserID sets the "user_id" field to the value that was provided on create.
func (u *OrganizationUserInfoUpsertBulk) UpdateUserID() *OrganizationUserInfoUpsertBulk {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.UpdateUserID()
	})
}

// SetExtra sets the "extra" field.
func (u *OrganizationUserInfoUpsertBulk) SetExtra(v string) *OrganizationUserInfoUpsertBulk {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.SetExtra(v)
	})
}

// UpdateExtra sets the "extra" field to the value that was provided on create.
func (u *OrganizationUserInfoUpsertBulk) UpdateExtra() *OrganizationUserInfoUpsertBulk {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.UpdateExtra()
	})
}

// ClearExtra clears the value of the "extra" field.
func (u *OrganizationUserInfoUpsertBulk) ClearExtra() *OrganizationUserInfoUpsertBulk {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.ClearExtra()
	})
}

// SetIsLeader sets the "is_leader" field.
func (u *OrganizationUserInfoUpsertBulk) SetIsLeader(v bool) *OrganizationUserInfoUpsertBulk {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.SetIsLeader(v)
	})
}

// UpdateIsLeader sets the "is_leader" field to the value that was provided on create.
func (u *OrganizationUserInfoUpsertBulk) UpdateIsLeader() *OrganizationUserInfoUpsertBulk {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.UpdateIsLeader()
	})
}

// SetIsAdmin sets the "is_admin" field.
func (u *OrganizationUserInfoUpsertBulk) SetIsAdmin(v bool) *OrganizationUserInfoUpsertBulk {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.SetIsAdmin(v)
	})
}

// UpdateIsAdmin sets the "is_admin" field to the value that was provided on create.
func (u *OrganizationUserInfoUpsertBulk) UpdateIsAdmin() *OrganizationUserInfoUpsertBulk {
	return u.Update(func(s *OrganizationUserInfoUpsert) {
		s.UpdateIsAdmin()
	})
}

// Exec executes the query.
func (u *OrganizationUserInfoUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the OrganizationUserInfoCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for OrganizationUserInfoCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *OrganizationUserInfoUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
