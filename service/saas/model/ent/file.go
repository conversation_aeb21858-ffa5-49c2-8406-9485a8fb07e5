// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"phoenix/service/saas/model/ent/file"
	"phoenix/service/saas/model/ent/tenant"
	"phoenix/service/saas/model/ent/user"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// File is the model entity for the File schema.
type File struct {
	config `json:"-"`
	// ID of the ent.
	// Snowflake ID | 全局唯一ID
	ID string `json:"id,omitempty"`
	// Created Time | 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// Updated Time | 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// status true normal false ban | 状态  正常/禁用
	Status bool `json:"status,omitempty"`
	// Sort number | 排序编号
	Sort uint32 `json:"sort,omitempty"`
	// Tenant ID
	TenantID string `json:"tenant_id,omitempty"`
	// Deleted Time | 删除时间（软删除标识）
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// File's UUID | 文件的UUID
	UUID string `json:"uuid,omitempty"`
	// File's name | 文件展示名称
	Name string `json:"name,omitempty"`
	// File's origin name | 文件原始名称
	OriginName string `json:"origin_name,omitempty"`
	// File's type | 文件类型
	FileType uint8 `json:"file_type,omitempty"`
	// File's size |  文件大小
	Size uint64 `json:"size,omitempty"`
	// File's path | 文件相对路径
	Path string `json:"path,omitempty"`
	// User's ID | 用户ID
	UserID string `json:"user_id,omitempty"`
	// The hash of the file | 文件的 hash
	Hash string `json:"hash,omitempty"`
	// status 1 private 2 public | 状态 1 私有 2 公开
	OpenStatus uint8 `json:"open_status,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the FileQuery when eager-loading is set.
	Edges        FileEdges `json:"edges"`
	selectValues sql.SelectValues
}

// FileEdges holds the relations/edges for other nodes in the graph.
type FileEdges struct {
	// Tenant holds the value of the tenant edge.
	Tenant *Tenant `json:"tenant,omitempty"`
	// User holds the value of the user edge.
	User *User `json:"user,omitempty"`
	// AvatarUsers holds the value of the avatar_users edge.
	AvatarUsers []*User `json:"avatar_users,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [3]bool
}

// TenantOrErr returns the Tenant value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e FileEdges) TenantOrErr() (*Tenant, error) {
	if e.Tenant != nil {
		return e.Tenant, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: tenant.Label}
	}
	return nil, &NotLoadedError{edge: "tenant"}
}

// UserOrErr returns the User value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e FileEdges) UserOrErr() (*User, error) {
	if e.User != nil {
		return e.User, nil
	} else if e.loadedTypes[1] {
		return nil, &NotFoundError{label: user.Label}
	}
	return nil, &NotLoadedError{edge: "user"}
}

// AvatarUsersOrErr returns the AvatarUsers value or an error if the edge
// was not loaded in eager-loading.
func (e FileEdges) AvatarUsersOrErr() ([]*User, error) {
	if e.loadedTypes[2] {
		return e.AvatarUsers, nil
	}
	return nil, &NotLoadedError{edge: "avatar_users"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*File) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case file.FieldStatus:
			values[i] = new(sql.NullBool)
		case file.FieldSort, file.FieldFileType, file.FieldSize, file.FieldOpenStatus:
			values[i] = new(sql.NullInt64)
		case file.FieldID, file.FieldTenantID, file.FieldUUID, file.FieldName, file.FieldOriginName, file.FieldPath, file.FieldUserID, file.FieldHash:
			values[i] = new(sql.NullString)
		case file.FieldCreatedAt, file.FieldUpdatedAt, file.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the File fields.
func (f *File) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case file.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				f.ID = value.String
			}
		case file.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				f.CreatedAt = value.Time
			}
		case file.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				f.UpdatedAt = value.Time
			}
		case file.FieldStatus:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				f.Status = value.Bool
			}
		case file.FieldSort:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field sort", values[i])
			} else if value.Valid {
				f.Sort = uint32(value.Int64)
			}
		case file.FieldTenantID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				f.TenantID = value.String
			}
		case file.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				f.DeletedAt = value.Time
			}
		case file.FieldUUID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field uuid", values[i])
			} else if value.Valid {
				f.UUID = value.String
			}
		case file.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				f.Name = value.String
			}
		case file.FieldOriginName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field origin_name", values[i])
			} else if value.Valid {
				f.OriginName = value.String
			}
		case file.FieldFileType:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field file_type", values[i])
			} else if value.Valid {
				f.FileType = uint8(value.Int64)
			}
		case file.FieldSize:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field size", values[i])
			} else if value.Valid {
				f.Size = uint64(value.Int64)
			}
		case file.FieldPath:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field path", values[i])
			} else if value.Valid {
				f.Path = value.String
			}
		case file.FieldUserID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field user_id", values[i])
			} else if value.Valid {
				f.UserID = value.String
			}
		case file.FieldHash:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field hash", values[i])
			} else if value.Valid {
				f.Hash = value.String
			}
		case file.FieldOpenStatus:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field open_status", values[i])
			} else if value.Valid {
				f.OpenStatus = uint8(value.Int64)
			}
		default:
			f.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the File.
// This includes values selected through modifiers, order, etc.
func (f *File) Value(name string) (ent.Value, error) {
	return f.selectValues.Get(name)
}

// QueryTenant queries the "tenant" edge of the File entity.
func (f *File) QueryTenant() *TenantQuery {
	return NewFileClient(f.config).QueryTenant(f)
}

// QueryUser queries the "user" edge of the File entity.
func (f *File) QueryUser() *UserQuery {
	return NewFileClient(f.config).QueryUser(f)
}

// QueryAvatarUsers queries the "avatar_users" edge of the File entity.
func (f *File) QueryAvatarUsers() *UserQuery {
	return NewFileClient(f.config).QueryAvatarUsers(f)
}

// Update returns a builder for updating this File.
// Note that you need to call File.Unwrap() before calling this method if this File
// was returned from a transaction, and the transaction was committed or rolled back.
func (f *File) Update() *FileUpdateOne {
	return NewFileClient(f.config).UpdateOne(f)
}

// Unwrap unwraps the File entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (f *File) Unwrap() *File {
	_tx, ok := f.config.driver.(*txDriver)
	if !ok {
		panic("ent: File is not a transactional entity")
	}
	f.config.driver = _tx.drv
	return f
}

// String implements the fmt.Stringer.
func (f *File) String() string {
	var builder strings.Builder
	builder.WriteString("File(")
	builder.WriteString(fmt.Sprintf("id=%v, ", f.ID))
	builder.WriteString("created_at=")
	builder.WriteString(f.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(f.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", f.Status))
	builder.WriteString(", ")
	builder.WriteString("sort=")
	builder.WriteString(fmt.Sprintf("%v", f.Sort))
	builder.WriteString(", ")
	builder.WriteString("tenant_id=")
	builder.WriteString(f.TenantID)
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(f.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("uuid=")
	builder.WriteString(f.UUID)
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(f.Name)
	builder.WriteString(", ")
	builder.WriteString("origin_name=")
	builder.WriteString(f.OriginName)
	builder.WriteString(", ")
	builder.WriteString("file_type=")
	builder.WriteString(fmt.Sprintf("%v", f.FileType))
	builder.WriteString(", ")
	builder.WriteString("size=")
	builder.WriteString(fmt.Sprintf("%v", f.Size))
	builder.WriteString(", ")
	builder.WriteString("path=")
	builder.WriteString(f.Path)
	builder.WriteString(", ")
	builder.WriteString("user_id=")
	builder.WriteString(f.UserID)
	builder.WriteString(", ")
	builder.WriteString("hash=")
	builder.WriteString(f.Hash)
	builder.WriteString(", ")
	builder.WriteString("open_status=")
	builder.WriteString(fmt.Sprintf("%v", f.OpenStatus))
	builder.WriteByte(')')
	return builder.String()
}

// Files is a parsable slice of File.
type Files []*File
