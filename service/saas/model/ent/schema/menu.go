package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	commonMixin "gitlab.zhijiasoft.com/paperless-group/saas-common/orm/ent/mixins"
)

type Menu struct {
	ent.Schema
}

func (Menu) Fields() []ent.Field {
	return []ent.Field{
		field.String("name").Comment("menu name | 菜单名称"),
		field.String("title").Comment("menu title | 菜单标题"),
		field.String("icon").Comment("menu icon | 菜单图标"),
		field.String("parent_id").Optional().Comment("parent menu ID | 父菜单ID"),
		field.Uint32("menu_type").Comment("menu type | 菜单类型 （菜单或目录）0 目录 1 菜单"),
		field.String("url").Optional().Default("").Comment("index path | 菜单路由路径"),
		field.String("redirect").Optional().Default("").Comment("redirect path | 跳转路径 （外链）"),
		field.String("component").Optional().Default("").Comment("the path of vue file | 组件路径"),
		field.Bool("is_active").Optional().Default(true).Comment("is_active | 是否激活"),
		field.Bool("hidden").Optional().Default(false).Comment("hidden | 是否隐藏"),
		field.Bool("hidden_in_tab").Optional().Default(false).Comment("hidden_in_tab | 是否隐藏标签"),
		field.Bool("fixed").Optional().Default(false).Comment("fixed | Fixed"),
		field.String("remark").Optional().Default("").Comment("remark | 备注"),
		// meta
		field.Text("meta").Optional().Default("{}").Comment("meta data | 菜单Meta信息"),
		field.Bool("is_full_page").Optional().Default(false).Comment("is_full_page | 是否全屏"),
	}
}

func (Menu) Mixin() []ent.Mixin {
	return []ent.Mixin{
		commonMixin.SnowflakeMixin{}, commonMixin.TimeMixin{},
		commonMixin.SortMixin{},
		SoftDeleteMixin{},
	}
}

func (Menu) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("roles", Role.Type).Ref("menus"),
		edge.To("buttons", Button.Type),
		edge.To("children", Menu.Type).From("parent").Unique().Field("parent_id"),
	}
}

func (Menu) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "saas_menu"},
	}
}
