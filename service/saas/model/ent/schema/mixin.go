package schema

import (
	"context"
	"fmt"
	ent2 "phoenix/service/saas/model/ent"
	"phoenix/service/saas/model/ent/hook"
	"phoenix/service/saas/model/ent/intercept"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/mixin"
)

// SoftDeleteMixin implements the soft delete pattern for schemas.
type SoftDeleteMixin struct {
	mixin.Schema
}

// Fields of the SoftDeleteMixin.
func (SoftDeleteMixin) Fields() []ent.Field {
	return []ent.Field{
		field.Time("deleted_at").
			Optional().Comment("Deleted Time | 删除时间（软删除标识）"),
	}
}

type softDeleteKey struct{}

// SkipSoftDelete returns a new context that skips the soft-delete interceptor/mutators.
func SkipSoftDelete(parent context.Context) context.Context {
	return context.WithValue(parent, softDeleteKey{}, true)
}

// P adds a storage-level predicate to the queries and mutations.
func (d SoftDeleteMixin) P(w interface{ WhereP(...func(*sql.Selector)) }) {
	w.WhereP(
		sql.FieldIsNull(d.Fields()[0].Descriptor().Name),
	)
}

// Interceptors of the SoftDeleteMixin.
func (d SoftDeleteMixin) Interceptors() []ent.Interceptor {
	return []ent.Interceptor{
		intercept.TraverseFunc(func(
			ctx context.Context,
			q intercept.Query,
		) error {
			// Skip soft-delete, means include soft-deleted entities.
			if skip, _ := ctx.Value(softDeleteKey{}).(bool); skip {
				return nil
			}

			d.P(q)
			return nil
		}),
	}
}

// Hooks of the SoftDeleteMixin.
func (d SoftDeleteMixin) Hooks() []ent.Hook {
	return []ent.Hook{
		hook.On(
			func(next ent.Mutator) ent.Mutator {
				return ent.MutateFunc(func(ctx context.Context, m ent2.Mutation) (ent.Value, error) {
					// Skip soft-delete, means delete the entity permanently.
					if skip, _ := ctx.Value(softDeleteKey{}).(bool); skip {
						return next.Mutate(ctx, m)
					}
					mx, ok := m.(interface {
						SetOp(ent.Op)
						Client() *ent2.Client
						SetDeletedAt(time.Time)
						WhereP(...func(*sql.Selector))
					})
					if !ok {
						return nil, fmt.Errorf("unexpected mutation type %T", m)
					}
					d.P(mx)
					mx.SetOp(ent.OpUpdate)
					mx.SetDeletedAt(time.Now())
					return mx.Client().Mutate(ctx, m)
				})
			},
			ent.OpDeleteOne|ent.OpDelete,
		),
	}
}

// TenantMixin implements the tenant pattern for schemas.
type TenantMixin struct {
	mixin.Schema
}

func (TenantMixin) Fields() []ent.Field {
	return []ent.Field{
		field.String("tenant_id").Comment("Tenant ID"),
	}
}

// Edges for all schemas that embed TenantMixin.
func (TenantMixin) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("tenant", Tenant.Type).
			Field("tenant_id").
			Unique().
			Required().Annotations(),
	}
}

type tenantSkipKey struct{}

// SkipTenantInject returns a new context that skips the soft-delete interceptor/mutators.
func SkipTenantInject(parent context.Context) context.Context {
	return context.WithValue(parent, tenantSkipKey{}, true)
}

// P adds a storage-level predicate to the queries and mutations.
func (t TenantMixin) P(tenantId string, w interface{ WhereP(...func(*sql.Selector)) }) {
	if tenantId == "" {
		w.WhereP(sql.FieldIsNull("id"))
	} else {
		w.WhereP(
			sql.FieldEQ(t.Fields()[0].Descriptor().Name, tenantId),
		)
	}
}

// Interceptors of the TenantMixin.
func (t TenantMixin) Interceptors() []ent.Interceptor {
	return []ent.Interceptor{
		intercept.TraverseFunc(func(
			ctx context.Context,
			q intercept.Query,
		) error {
			// Skip soft-delete, means include soft-deleted entities.
			if skip, _ := ctx.Value(tenantSkipKey{}).(bool); skip {
				return nil
			}

			// Pre mutation action.
			// Get tenantId from context.
			tenantId, err := GetTenantIDFromContext(ctx)
			if err != nil {
				return err
			}

			t.P(tenantId, q)
			return nil
		}),
	}
}

func (t TenantMixin) Hooks() []ent.Hook {
	return []ent.Hook{
		hook.On(func(next ent.Mutator) ent.Mutator {
			return ent.MutateFunc(func(ctx context.Context, m ent.Mutation) (ent.Value, error) {

				if skip, _ := ctx.Value(tenantSkipKey{}).(bool); skip {
					return next.Mutate(ctx, m)
				}
				// Pre mutation action.
				// Get tenantId from context.
				tenantId, err := GetTenantIDFromContext(ctx)
				if err != nil {
					return nil, err
				}

				// 创建时，如果没有传入tenantId，则不允许创建
				if tenantId == "" {
					return nil, fmt.Errorf("请先加入至少一个组织或团队")
				}

				if s, ok := m.(interface{ SetTenantID(string) }); ok {

					s.SetTenantID(tenantId)
				}

				v, err := next.Mutate(ctx, m)
				// Post mutation action.
				fmt.Println("new value:", v)
				return v, err
			})
		}, ent.OpCreate),
	}
}

func GetTenantIDFromContext(ctx context.Context) (string, error) {
	id, ok := ctx.Value("TenantId").(string)
	if !ok || id == "" {
		return "", nil
	}
	return id, nil
}
