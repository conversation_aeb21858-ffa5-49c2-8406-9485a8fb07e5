package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/suyuan32/simple-admin-common/orm/ent/mixins"
	commonMixin "gitlab.zhijiasoft.com/paperless-group/saas-common/orm/ent/mixins"
)

type GroupType struct {
	ent.Schema
}

func (GroupType) Fields() []ent.Field {
	return []ent.Field{
		field.String("name").Comment("Position Name | 组分类名称"),
		field.String("code").Comment("The code of group type | 组分类编码"),
		field.String("remark").Optional().Default("").Comment("Remark | 备注"),
	}
}

func (GroupType) Mixin() []ent.Mixin {
	return []ent.Mixin{
		commonMixin.SnowflakeMixin{}, commonMixin.TimeMixin{},
		commonMixin.StatusMixin{},
		mixins.SortMixin{},
		TenantMixin{},
		SoftDeleteMixin{},
	}
}

func (GroupType) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("groups", Group.Type),
	}
}

func (GroupType) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("code").Unique(),
	}
}

func (GroupType) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "saas_group_type"},
	}
}
