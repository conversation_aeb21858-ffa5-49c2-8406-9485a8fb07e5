package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	commonMixin "gitlab.zhijiasoft.com/paperless-group/saas-common/orm/ent/mixins"
	//_ "saas-core/rpc/ent/runtime"
)

type Tenant struct {
	ent.Schema
}

func (Tenant) Fields() []ent.Field {
	return []ent.Field{
		field.String("name").SchemaType(map[string]string{
			dialect.MySQL: "varchar(500)",
		}).Optional().Default("").Comment("Tenant's name | 租户名称"),
		field.Bool("is_super").Optional().Default(false).Comment("Is super tenant | 是否是超级租户"),
		field.Time("service_start_at").Optional().Comment("Tenant's service start | 服务开始时间例如2023-01-01 00:00:00"),
		field.Time("service_end_at").Optional().Comment("Tenant's service end| 服务到期时间例如2023-10-10 00:00:00"),
		field.String("after_sales_contact").Optional().Comment("售后联系人"),
		field.String("location_id").Optional().Comment("归属地ID"),
		field.Int64("log_save_keep_days").Optional().Comment("日志保留天数"),
		field.Int64("max_attendance_user_count").Optional().Comment("最大列席用户数"),
		field.Int64("max_device_count").Optional().Comment("最大设备数"),
		field.Int64("max_upload_file_size").Optional().Comment("最大上传文件大小"),
		field.Int64("max_user_count").Optional().Comment("最大用户数"),
		field.String("principal").Optional().Comment("负责人"),
		field.String("principal_contact_information").Optional().Comment("负责人联系方式"),
		field.String("sale_contact").Optional().Comment("销售联系人"),
		field.String("secret_key").Optional().Comment("密钥"),
		field.Bool("ai_status").Optional().Default(false).Comment("AI状态"),
		field.Int64("max_conference_agenda_title").Optional().Comment("最大会议议程标题字数"),
	}
}

func (Tenant) Mixin() []ent.Mixin {
	return []ent.Mixin{
		commonMixin.SnowflakeMixin{}, commonMixin.TimeMixin{},
		commonMixin.SingleUUIDMixin{},
		commonMixin.KeyMixin{},
		commonMixin.StatusMixin{},
		SoftDeleteMixin{},
	}
}

func (Tenant) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("users", User.Type),
		edge.To("menus", Menu.Type),
		edge.To("apis", API.Type),
		edge.To("buttons", Button.Type),
	}
}

func (Tenant) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("name", "key").Unique(),
	}
}

func (Tenant) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "saas_tenant"},
	}
}
