package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"github.com/suyuan32/simple-admin-common/orm/ent/mixins"
	commonMixin "gitlab.zhijiasoft.com/paperless-group/saas-common/orm/ent/mixins"
)

type OrganizationUserInfo struct {
	ent.Schema
}

func (OrganizationUserInfo) Fields() []ent.Field {
	return []ent.Field{
		field.String("organization_id").Comment("Organization ID | 组织架构 ID"),
		field.String("user_id").Comment("User ID | 用户 ID"),
		field.Text("extra").Optional().Default("{}").Comment("Extra information | 额外信息(json对象格式存储，用于存储的额外展示信息)"),
		field.Bool("is_leader").Default(false).Comment("Is Leader | 是否领导"),
		field.Bool("is_admin").Default(false).Comment("Is Admin | 是否管理员"),
	}
}

func (OrganizationUserInfo) Mixin() []ent.Mixin {
	return []ent.Mixin{
		commonMixin.SnowflakeMixin{},
		commonMixin.TimeMixin{},
		mixins.SortMixin{},
		SoftDeleteMixin{},
	}
}

func (OrganizationUserInfo) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("organization", Organization.Type).Field("organization_id").Unique().Required().Ref("organization_infos"),
		edge.From("user", User.Type).Field("user_id").Unique().Required().Ref("organization_infos"),
	}
}

func (OrganizationUserInfo) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "saas_organization_user_info"},
	}
}
