package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/gofrs/uuid/v5"
	"github.com/suyuan32/simple-admin-common/orm/ent/mixins"
	commonMixin "gitlab.zhijiasoft.com/paperless-group/saas-common/orm/ent/mixins"
	uuid2 "gitlab.zhijiasoft.com/paperless-group/saas-common/utils/uuidx"
)

type Role struct {
	ent.Schema
}

func (Role) Fields() []ent.Field {
	return []ent.Field{
		field.String("name").Default("").Comment("role name | 角色名"),
		field.String("code").Unique().Comment("role code for permission control in front end | 角色码，用于前端权限控制"),
		field.UUID("uid", uuid.UUID{}).Default(uuid2.NewUUID).Immutable().Comment("UUID"),
		field.String("default_router").Default("dashboard").Comment("default menu : dashboard | 默认登录页面"),
		field.String("remark").Default("").Comment("remark | 备注"),
		field.String("organization_id").Default("").Comment("组织id"),
		field.String("parent_id").Optional().Comment("Parent role ID | 父级角色ID"),
	}
}

func (Role) Mixin() []ent.Mixin {
	return []ent.Mixin{
		commonMixin.SnowflakeMixin{}, commonMixin.TimeMixin{},
		commonMixin.StatusMixin{},
		mixins.SortMixin{},
		TenantMixin{},
		SoftDeleteMixin{},
	}
}

func (Role) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("buttons", Button.Type),
		edge.To("menus", Menu.Type),
		edge.To("apis", API.Type),
		edge.To("groups", Group.Type),
		edge.To("users", User.Type),
		edge.To("children", Role.Type).From("parent").Unique().Field("parent_id"),
	}
}

func (Role) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("code", "tenant_id").Unique(),
	}
}

func (Role) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "saas_role"},
	}
}
