package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"github.com/suyuan32/simple-admin-common/orm/ent/mixins"
	commonMixin "gitlab.zhijiasoft.com/paperless-group/saas-common/orm/ent/mixins"
)

// File holds the schema definition for the File entity.
type File struct {
	ent.Schema
}

// Fields of the File.
func (File) Fields() []ent.Field {
	return []ent.Field{
		field.String("uuid").Comment("File's UUID | 文件的UUID"),
		field.String("name").Comment("File's name | 文件展示名称"),
		field.String("origin_name").Comment("File's origin name | 文件原始名称"),
		field.Uint8("file_type").Comment("File's type | 文件类型"),
		field.Uint64("size").Comment("File's size |  文件大小"),
		field.String("path").Comment("File's path | 文件相对路径"),
		field.String("user_id").Optional().Comment("User's ID | 用户ID"),
		field.String("hash").Comment("The hash of the file | 文件的 hash"),
		field.Uint8("open_status").
			Default(1).
			Optional().
			Comment("status 1 private 2 public | 状态 1 私有 2 公开"),
	}
}

func (File) Mixin() []ent.Mixin {
	return []ent.Mixin{
		commonMixin.SnowflakeMixin{},
		commonMixin.TimeMixin{},
		commonMixin.StatusMixin{},
		mixins.SortMixin{},
		TenantMixin{},
		SoftDeleteMixin{},
	}
}

// Edges of the File.
func (File) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("user", User.Type).Field("user_id").Ref("files").Unique(),
		edge.To("avatar_users", User.Type),
	}
}

func (File) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "fms_file"}, // fms means file management service
	}
}
