package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"github.com/suyuan32/simple-admin-common/orm/ent/mixins"
	commonMixin "gitlab.zhijiasoft.com/paperless-group/saas-common/orm/ent/mixins"
)

type Organization struct {
	ent.Schema
}

func (Organization) Fields() []ent.Field {
	return []ent.Field{
		field.String("name").Comment("Organization name | 部门名称"),
		field.String("ancestors").Comment("Parents' IDs | 父级列表"),
		field.String("code").Comment("Code | 组织架构节点编码（可作为扩展其他用户体系，比如钉钉，企业微信等）"),
		field.Uint32("node_type").Comment("Node type | 组织架构类型 （单位或部门）0 单位 1 部门"),
		field.String("leader").Comment("Organization leader | 部门负责人"),
		field.String("phone").Comment("Leader's phone number | 负责人电话"),
		field.String("email").Comment("Leader's email | 部门负责人电子邮箱"),
		field.String("remark").Comment("Remark | 备注"),
		field.String("parent_id").Optional().Comment("Parent organization ID | 父级部门ID"),
	}
}

func (Organization) Mixin() []ent.Mixin {
	return []ent.Mixin{
		commonMixin.SnowflakeMixin{}, commonMixin.TimeMixin{},
		commonMixin.StatusMixin{},
		mixins.SortMixin{},
		TenantMixin{},
		SoftDeleteMixin{},
	}
}

func (Organization) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("children", Organization.Type).From("parent").Unique().Field("parent_id"),
		edge.From("users", User.Type).Ref("organizations"),
		edge.To("organization_infos", OrganizationUserInfo.Type),
	}
}

func (Organization) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "saas_organization"},
	}
}
