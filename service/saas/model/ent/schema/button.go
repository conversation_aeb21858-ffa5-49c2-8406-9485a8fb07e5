package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"github.com/suyuan32/simple-admin-common/orm/ent/mixins"
	commonMixin "gitlab.zhijiasoft.com/paperless-group/saas-common/orm/ent/mixins"
)

type Button struct {
	ent.Schema
}

func (Button) Fields() []ent.Field {
	return []ent.Field{
		field.String("name").Optional().Default("").Comment("name | 按钮名称"),
		field.String("code").Comment("code | 按钮CODE"),
		field.String("menu_id").Optional().Comment("Menu ID | 菜单ID"),
	}
}

func (Button) Mixin() []ent.Mixin {
	return []ent.Mixin{
		commonMixin.SnowflakeMixin{}, commonMixin.TimeMixin{},
		mixins.SortMixin{},
		SoftDeleteMixin{},
	}
}

func (Button) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("roles", Role.Type).Ref("buttons"),
		edge.From("menu", Menu.Type).Field("menu_id").Ref("buttons").Unique(),
	}
}

func (Button) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "saas_button"},
	}
}
