package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"github.com/suyuan32/simple-admin-common/orm/ent/mixins"
	commonMixin "gitlab.zhijiasoft.com/paperless-group/saas-common/orm/ent/mixins"
)

type TenantUserInfo struct {
	ent.Schema
}

func (TenantUserInfo) Fields() []ent.Field {
	return []ent.Field{
		field.String("tenant_id").Optional().Comment("Tenant ID | 租户 ID"),
		field.String("user_id").Optional().Comment("User ID | 用户 ID"),
		field.Text("extra").Optional().Default("{}").Comment("Extra information | 额外信息(json对象格式存储，用于存储的额外展示信息)"),
	}
}

func (TenantUserInfo) Mixin() []ent.Mixin {
	return []ent.Mixin{
		commonMixin.SnowflakeMixin{}, commonMixin.TimeMixin{},
		mixins.SortMixin{},
		SoftDeleteMixin{},
	}
}

func (TenantUserInfo) Edges() []ent.Edge {
	return []ent.Edge{
		edge.To("tenant_user_info_tenant", Tenant.Type).Field("tenant_id").Unique(),
		edge.To("tenant_user_info_user", User.Type).Field("user_id").Unique(),
	}
}

func (TenantUserInfo) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "saas_tenant_user_info"},
	}
}
