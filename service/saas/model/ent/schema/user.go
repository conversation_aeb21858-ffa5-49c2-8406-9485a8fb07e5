package schema

import (
	"context"
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/edge"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	commonMixin "gitlab.zhijiasoft.com/paperless-group/saas-common/orm/ent/mixins"
	localEnt "phoenix/service/saas/model/ent"
	"phoenix/service/saas/model/ent/enums"
	"phoenix/service/saas/model/ent/hook"
)

type User struct {
	ent.Schema
}

func (User) Fields() []ent.Field {
	return []ent.Field{
		field.String("username").Unique().Comment("User's login name | 登录名"),
		field.String("password").Comment("Password | 密码Hash").Sensitive(),
		field.String("nickname").Optional().Default("").Comment("Nickname | 昵称"),
		field.String("mobile").Optional().Comment("Mobile number | 手机号"),
		field.String("email").Optional().Comment("Email | 邮箱号"),
		field.Enum("gender").GoType(enums.Gender("")).Default(enums.Unset.String()).Comment("gender | 性别"),
		field.String("post").Optional().Default("").Comment("post | 职务"),
		field.Bool("is_superuser").Default(false).Comment("Is Superuser | 是否超级管理员"),
		field.String("default_tenant_id").Optional().Comment("Default tenant id | 默认租户ID，用于快速登录"),
		field.String("avatar_id").
			Optional().
			Default("").
			Comment("Avatar FIle ID | 头像文件ID"),
		field.String("device_no").Optional().Default("").Comment("Device No | 设备号"),
		field.String("kind").Optional().Default("common").Comment("kind | 类型，attendance：列席用户"),
		field.String("imei").Unique().Comment("imei | imei"),
	}
}

func (User) Mixin() []ent.Mixin {
	return []ent.Mixin{
		commonMixin.SnowflakeMixin{},
		commonMixin.TimeMixin{},
		commonMixin.StatusMixin{},
		//TenantMixin{},
		SoftDeleteMixin{},
	}
}

func (User) Edges() []ent.Edge {
	return []ent.Edge{
		edge.From("tenants", Tenant.Type).Ref("users"),
		edge.To("organizations", Organization.Type),
		edge.To("positions", Position.Type),
		edge.To("groups", Group.Type),
		edge.From("roles", Role.Type).Ref("users"),
		edge.To("organization_infos", OrganizationUserInfo.Type),
		edge.To("tenant_infos", TenantUserInfo.Type),
		edge.To("files", File.Type),
		edge.From("avatar", File.Type).Field("avatar_id").Unique().Ref("avatar_users"),
	}
}

func (User) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("username", "email").
			Unique(),
	}
}

func (User) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "saas_user"},
	}
}

// Hooks of the User.
func (User) Hooks() []ent.Hook {
	return []ent.Hook{
		// 执行用户删除操作之后将用户的唯一值信息进行混淆处理，避免二次添加时出现冲突 hook.
		hook.On(
			func(next ent.Mutator) ent.Mutator {
				return hook.UserFunc(func(ctx context.Context, m *localEnt.UserMutation) (ent.Value, error) {

					//timeStr := time.Now().Local().Format("20060102150405")
					//
					//if v, ok := m.Username(); ok {
					//	m.SetUsername(fmt.Sprintf("%s-history-%s", v, timeStr))
					//}
					//if v, ok := m.Mobile(); ok {
					//	m.SetMobile(fmt.Sprintf("%s-history-%s", v, timeStr))
					//}
					//if v, ok := m.Email(); ok {
					//	m.SetEmail(fmt.Sprintf("%s-history-%s", v, timeStr))
					//}

					return next.Mutate(ctx, m)
				})
			},
			// Limit the hook only for these operations.
			ent.OpDelete|ent.OpDeleteOne,
		),
	}
}
