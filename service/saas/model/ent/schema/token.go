package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	commonMixin "gitlab.zhijiasoft.com/paperless-group/saas-common/orm/ent/mixins"
)

type Token struct {
	ent.Schema
}

func (Token) Fields() []ent.Field {
	return []ent.Field{
		field.String("uid").Comment(" User's UID | 用户的UID"),
		field.String("token").Comment("Token string | Token 字符串"),
		field.String("source").Comment("Log in source such as GitHub | Token 来源 （saas, 第三方如github等）"),
		field.Time("expired_at").Comment(" Expire time | 过期时间"),
		field.String("tenant_id").Optional().Comment("Tenant ID | 租户ID"),
		field.String("device_kind").Optional().Comment("Device kind | 设备类型"),
		field.String("ip").Optional().Comment("IP | IP"),
	}
}

func (Token) Mixin() []ent.Mixin {
	return []ent.Mixin{
		commonMixin.SnowflakeMixin{}, commonMixin.TimeMixin{},
		commonMixin.StatusMixin{},
		//TenantMixin{},
	}
}

func (Token) Edges() []ent.Edge {
	return []ent.Edge{}
}

func (Token) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("uid"),
		index.Fields("expired_at"),
	}
}

func (Token) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "saas_token"},
	}
}
