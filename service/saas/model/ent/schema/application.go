package schema

import (
	"entgo.io/ent"
	"entgo.io/ent/dialect/entsql"
	"entgo.io/ent/schema"
	"entgo.io/ent/schema/field"
	"entgo.io/ent/schema/index"
	"github.com/suyuan32/simple-admin-common/orm/ent/mixins"
	commonMixin "gitlab.zhijiasoft.com/paperless-group/saas-common/orm/ent/mixins"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/utils/key_secret"
)

type Application struct {
	ent.Schema
}

func (Application) Fields() []ent.Field {
	return []ent.Field{
		field.String("name").Default("").Comment("app name | 应用名"),
		field.String("app_id").DefaultFunc(key_secret.GenKey).Unique().Immutable().Comment("APP ID"),
		field.String("secret").DefaultFunc(key_secret.GenSecret).Comment("secret | 应用密钥"),
		field.String("remark").Default("").Comment("remark | 备注"),
	}
}

func (Application) Mixin() []ent.Mixin {
	return []ent.Mixin{
		commonMixin.SnowflakeMixin{},
		commonMixin.TimeMixin{},
		commonMixin.StatusMixin{},
		mixins.SortMixin{},
		TenantMixin{},
		SoftDeleteMixin{},
	}
}

func (Application) Edges() []ent.Edge {
	return []ent.Edge{}
}

func (Application) Indexes() []ent.Index {
	return []ent.Index{
		index.Fields("app_id").Unique(),
	}
}

func (Application) Annotations() []schema.Annotation {
	return []schema.Annotation{
		entsql.Annotation{Table: "saas_application"},
	}
}
