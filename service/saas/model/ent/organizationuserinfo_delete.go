// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"phoenix/service/saas/model/ent/organizationuserinfo"
	"phoenix/service/saas/model/ent/predicate"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// OrganizationUserInfoDelete is the builder for deleting a OrganizationUserInfo entity.
type OrganizationUserInfoDelete struct {
	config
	hooks    []Hook
	mutation *OrganizationUserInfoMutation
}

// Where appends a list predicates to the OrganizationUserInfoDelete builder.
func (ouid *OrganizationUserInfoDelete) Where(ps ...predicate.OrganizationUserInfo) *OrganizationUserInfoDelete {
	ouid.mutation.Where(ps...)
	return ouid
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (ouid *OrganizationUserInfoDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, ouid.sqlExec, ouid.mutation, ouid.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (ouid *OrganizationUserInfoDelete) ExecX(ctx context.Context) int {
	n, err := ouid.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (ouid *OrganizationUserInfoDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(organizationuserinfo.Table, sqlgraph.NewFieldSpec(organizationuserinfo.FieldID, field.TypeString))
	if ps := ouid.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, ouid.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	ouid.mutation.done = true
	return affected, err
}

// OrganizationUserInfoDeleteOne is the builder for deleting a single OrganizationUserInfo entity.
type OrganizationUserInfoDeleteOne struct {
	ouid *OrganizationUserInfoDelete
}

// Where appends a list predicates to the OrganizationUserInfoDelete builder.
func (ouido *OrganizationUserInfoDeleteOne) Where(ps ...predicate.OrganizationUserInfo) *OrganizationUserInfoDeleteOne {
	ouido.ouid.mutation.Where(ps...)
	return ouido
}

// Exec executes the deletion query.
func (ouido *OrganizationUserInfoDeleteOne) Exec(ctx context.Context) error {
	n, err := ouido.ouid.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{organizationuserinfo.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (ouido *OrganizationUserInfoDeleteOne) ExecX(ctx context.Context) {
	if err := ouido.Exec(ctx); err != nil {
		panic(err)
	}
}
