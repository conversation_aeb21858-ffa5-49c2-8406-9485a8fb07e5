// Code generated by ent, DO NOT EDIT.

package user

import (
	"fmt"
	"phoenix/service/saas/model/ent/enums"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the user type in the database.
	Label = "user"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldStatus holds the string denoting the status field in the database.
	FieldStatus = "status"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// FieldUsername holds the string denoting the username field in the database.
	FieldUsername = "username"
	// FieldPassword holds the string denoting the password field in the database.
	FieldPassword = "password"
	// FieldNickname holds the string denoting the nickname field in the database.
	FieldNickname = "nickname"
	// FieldMobile holds the string denoting the mobile field in the database.
	FieldMobile = "mobile"
	// FieldEmail holds the string denoting the email field in the database.
	FieldEmail = "email"
	// FieldGender holds the string denoting the gender field in the database.
	FieldGender = "gender"
	// FieldPost holds the string denoting the post field in the database.
	FieldPost = "post"
	// FieldIsSuperuser holds the string denoting the is_superuser field in the database.
	FieldIsSuperuser = "is_superuser"
	// FieldDefaultTenantID holds the string denoting the default_tenant_id field in the database.
	FieldDefaultTenantID = "default_tenant_id"
	// FieldAvatarID holds the string denoting the avatar_id field in the database.
	FieldAvatarID = "avatar_id"
	// FieldDeviceNo holds the string denoting the device_no field in the database.
	FieldDeviceNo = "device_no"
	// FieldKind holds the string denoting the kind field in the database.
	FieldKind = "kind"
	// FieldImei holds the string denoting the imei field in the database.
	FieldImei = "imei"
	// EdgeTenants holds the string denoting the tenants edge name in mutations.
	EdgeTenants = "tenants"
	// EdgeOrganizations holds the string denoting the organizations edge name in mutations.
	EdgeOrganizations = "organizations"
	// EdgePositions holds the string denoting the positions edge name in mutations.
	EdgePositions = "positions"
	// EdgeGroups holds the string denoting the groups edge name in mutations.
	EdgeGroups = "groups"
	// EdgeRoles holds the string denoting the roles edge name in mutations.
	EdgeRoles = "roles"
	// EdgeOrganizationInfos holds the string denoting the organization_infos edge name in mutations.
	EdgeOrganizationInfos = "organization_infos"
	// EdgeTenantInfos holds the string denoting the tenant_infos edge name in mutations.
	EdgeTenantInfos = "tenant_infos"
	// EdgeFiles holds the string denoting the files edge name in mutations.
	EdgeFiles = "files"
	// EdgeAvatar holds the string denoting the avatar edge name in mutations.
	EdgeAvatar = "avatar"
	// Table holds the table name of the user in the database.
	Table = "saas_user"
	// TenantsTable is the table that holds the tenants relation/edge. The primary key declared below.
	TenantsTable = "tenant_users"
	// TenantsInverseTable is the table name for the Tenant entity.
	// It exists in this package in order to avoid circular dependency with the "tenant" package.
	TenantsInverseTable = "saas_tenant"
	// OrganizationsTable is the table that holds the organizations relation/edge. The primary key declared below.
	OrganizationsTable = "user_organizations"
	// OrganizationsInverseTable is the table name for the Organization entity.
	// It exists in this package in order to avoid circular dependency with the "organization" package.
	OrganizationsInverseTable = "saas_organization"
	// PositionsTable is the table that holds the positions relation/edge. The primary key declared below.
	PositionsTable = "user_positions"
	// PositionsInverseTable is the table name for the Position entity.
	// It exists in this package in order to avoid circular dependency with the "position" package.
	PositionsInverseTable = "saas_position"
	// GroupsTable is the table that holds the groups relation/edge. The primary key declared below.
	GroupsTable = "user_groups"
	// GroupsInverseTable is the table name for the Group entity.
	// It exists in this package in order to avoid circular dependency with the "group" package.
	GroupsInverseTable = "saas_group"
	// RolesTable is the table that holds the roles relation/edge. The primary key declared below.
	RolesTable = "role_users"
	// RolesInverseTable is the table name for the Role entity.
	// It exists in this package in order to avoid circular dependency with the "role" package.
	RolesInverseTable = "saas_role"
	// OrganizationInfosTable is the table that holds the organization_infos relation/edge.
	OrganizationInfosTable = "saas_organization_user_info"
	// OrganizationInfosInverseTable is the table name for the OrganizationUserInfo entity.
	// It exists in this package in order to avoid circular dependency with the "organizationuserinfo" package.
	OrganizationInfosInverseTable = "saas_organization_user_info"
	// OrganizationInfosColumn is the table column denoting the organization_infos relation/edge.
	OrganizationInfosColumn = "user_id"
	// TenantInfosTable is the table that holds the tenant_infos relation/edge.
	TenantInfosTable = "saas_tenant_user_info"
	// TenantInfosInverseTable is the table name for the TenantUserInfo entity.
	// It exists in this package in order to avoid circular dependency with the "tenantuserinfo" package.
	TenantInfosInverseTable = "saas_tenant_user_info"
	// TenantInfosColumn is the table column denoting the tenant_infos relation/edge.
	TenantInfosColumn = "user_tenant_infos"
	// FilesTable is the table that holds the files relation/edge.
	FilesTable = "fms_file"
	// FilesInverseTable is the table name for the File entity.
	// It exists in this package in order to avoid circular dependency with the "file" package.
	FilesInverseTable = "fms_file"
	// FilesColumn is the table column denoting the files relation/edge.
	FilesColumn = "user_id"
	// AvatarTable is the table that holds the avatar relation/edge.
	AvatarTable = "saas_user"
	// AvatarInverseTable is the table name for the File entity.
	// It exists in this package in order to avoid circular dependency with the "file" package.
	AvatarInverseTable = "fms_file"
	// AvatarColumn is the table column denoting the avatar relation/edge.
	AvatarColumn = "avatar_id"
)

// Columns holds all SQL columns for user fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldStatus,
	FieldDeletedAt,
	FieldUsername,
	FieldPassword,
	FieldNickname,
	FieldMobile,
	FieldEmail,
	FieldGender,
	FieldPost,
	FieldIsSuperuser,
	FieldDefaultTenantID,
	FieldAvatarID,
	FieldDeviceNo,
	FieldKind,
	FieldImei,
}

var (
	// TenantsPrimaryKey and TenantsColumn2 are the table columns denoting the
	// primary key for the tenants relation (M2M).
	TenantsPrimaryKey = []string{"tenant_id", "user_id"}
	// OrganizationsPrimaryKey and OrganizationsColumn2 are the table columns denoting the
	// primary key for the organizations relation (M2M).
	OrganizationsPrimaryKey = []string{"user_id", "organization_id"}
	// PositionsPrimaryKey and PositionsColumn2 are the table columns denoting the
	// primary key for the positions relation (M2M).
	PositionsPrimaryKey = []string{"user_id", "position_id"}
	// GroupsPrimaryKey and GroupsColumn2 are the table columns denoting the
	// primary key for the groups relation (M2M).
	GroupsPrimaryKey = []string{"user_id", "group_id"}
	// RolesPrimaryKey and RolesColumn2 are the table columns denoting the
	// primary key for the roles relation (M2M).
	RolesPrimaryKey = []string{"role_id", "user_id"}
)

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "phoenix/service/saas/model/ent/runtime"
var (
	Hooks        [2]ent.Hook
	Interceptors [1]ent.Interceptor
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultStatus holds the default value on creation for the "status" field.
	DefaultStatus bool
	// DefaultNickname holds the default value on creation for the "nickname" field.
	DefaultNickname string
	// DefaultPost holds the default value on creation for the "post" field.
	DefaultPost string
	// DefaultIsSuperuser holds the default value on creation for the "is_superuser" field.
	DefaultIsSuperuser bool
	// DefaultAvatarID holds the default value on creation for the "avatar_id" field.
	DefaultAvatarID string
	// DefaultDeviceNo holds the default value on creation for the "device_no" field.
	DefaultDeviceNo string
	// DefaultKind holds the default value on creation for the "kind" field.
	DefaultKind string
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() string
)

const DefaultGender enums.Gender = "未设置"

// GenderValidator is a validator for the "gender" field enum values. It is called by the builders before save.
func GenderValidator(ge enums.Gender) error {
	switch ge.String() {
	case "未设置", "男", "女":
		return nil
	default:
		return fmt.Errorf("user: invalid enum value for gender field: %q", ge)
	}
}

// OrderOption defines the ordering options for the User queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// ByStatus orders the results by the status field.
func ByStatus(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldStatus, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// ByUsername orders the results by the username field.
func ByUsername(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUsername, opts...).ToFunc()
}

// ByPassword orders the results by the password field.
func ByPassword(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPassword, opts...).ToFunc()
}

// ByNickname orders the results by the nickname field.
func ByNickname(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldNickname, opts...).ToFunc()
}

// ByMobile orders the results by the mobile field.
func ByMobile(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldMobile, opts...).ToFunc()
}

// ByEmail orders the results by the email field.
func ByEmail(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldEmail, opts...).ToFunc()
}

// ByGender orders the results by the gender field.
func ByGender(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldGender, opts...).ToFunc()
}

// ByPost orders the results by the post field.
func ByPost(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldPost, opts...).ToFunc()
}

// ByIsSuperuser orders the results by the is_superuser field.
func ByIsSuperuser(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldIsSuperuser, opts...).ToFunc()
}

// ByDefaultTenantID orders the results by the default_tenant_id field.
func ByDefaultTenantID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDefaultTenantID, opts...).ToFunc()
}

// ByAvatarID orders the results by the avatar_id field.
func ByAvatarID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldAvatarID, opts...).ToFunc()
}

// ByDeviceNo orders the results by the device_no field.
func ByDeviceNo(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeviceNo, opts...).ToFunc()
}

// ByKind orders the results by the kind field.
func ByKind(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldKind, opts...).ToFunc()
}

// ByImei orders the results by the imei field.
func ByImei(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldImei, opts...).ToFunc()
}

// ByTenantsCount orders the results by tenants count.
func ByTenantsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newTenantsStep(), opts...)
	}
}

// ByTenants orders the results by tenants terms.
func ByTenants(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTenantsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByOrganizationsCount orders the results by organizations count.
func ByOrganizationsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newOrganizationsStep(), opts...)
	}
}

// ByOrganizations orders the results by organizations terms.
func ByOrganizations(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newOrganizationsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByPositionsCount orders the results by positions count.
func ByPositionsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newPositionsStep(), opts...)
	}
}

// ByPositions orders the results by positions terms.
func ByPositions(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newPositionsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByGroupsCount orders the results by groups count.
func ByGroupsCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newGroupsStep(), opts...)
	}
}

// ByGroups orders the results by groups terms.
func ByGroups(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newGroupsStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByRolesCount orders the results by roles count.
func ByRolesCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newRolesStep(), opts...)
	}
}

// ByRoles orders the results by roles terms.
func ByRoles(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newRolesStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByOrganizationInfosCount orders the results by organization_infos count.
func ByOrganizationInfosCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newOrganizationInfosStep(), opts...)
	}
}

// ByOrganizationInfos orders the results by organization_infos terms.
func ByOrganizationInfos(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newOrganizationInfosStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByTenantInfosCount orders the results by tenant_infos count.
func ByTenantInfosCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newTenantInfosStep(), opts...)
	}
}

// ByTenantInfos orders the results by tenant_infos terms.
func ByTenantInfos(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTenantInfosStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByFilesCount orders the results by files count.
func ByFilesCount(opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborsCount(s, newFilesStep(), opts...)
	}
}

// ByFiles orders the results by files terms.
func ByFiles(term sql.OrderTerm, terms ...sql.OrderTerm) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newFilesStep(), append([]sql.OrderTerm{term}, terms...)...)
	}
}

// ByAvatarField orders the results by avatar field.
func ByAvatarField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newAvatarStep(), sql.OrderByField(field, opts...))
	}
}
func newTenantsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TenantsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2M, true, TenantsTable, TenantsPrimaryKey...),
	)
}
func newOrganizationsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(OrganizationsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2M, false, OrganizationsTable, OrganizationsPrimaryKey...),
	)
}
func newPositionsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(PositionsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2M, false, PositionsTable, PositionsPrimaryKey...),
	)
}
func newGroupsStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(GroupsInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2M, false, GroupsTable, GroupsPrimaryKey...),
	)
}
func newRolesStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(RolesInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2M, true, RolesTable, RolesPrimaryKey...),
	)
}
func newOrganizationInfosStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(OrganizationInfosInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, OrganizationInfosTable, OrganizationInfosColumn),
	)
}
func newTenantInfosStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TenantInfosInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, TenantInfosTable, TenantInfosColumn),
	)
}
func newFilesStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(FilesInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.O2M, false, FilesTable, FilesColumn),
	)
}
func newAvatarStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(AvatarInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, true, AvatarTable, AvatarColumn),
	)
}
