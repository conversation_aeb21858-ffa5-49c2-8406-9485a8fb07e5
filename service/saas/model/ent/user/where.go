// Code generated by ent, DO NOT EDIT.

package user

import (
	"phoenix/service/saas/model/ent/enums"
	"phoenix/service/saas/model/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.User {
	return predicate.User(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.User {
	return predicate.User(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldUpdatedAt, v))
}

// Status applies equality check predicate on the "status" field. It's identical to StatusEQ.
func Status(v bool) predicate.User {
	return predicate.User(sql.FieldEQ(FieldStatus, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldDeletedAt, v))
}

// Username applies equality check predicate on the "username" field. It's identical to UsernameEQ.
func Username(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldUsername, v))
}

// Password applies equality check predicate on the "password" field. It's identical to PasswordEQ.
func Password(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldPassword, v))
}

// Nickname applies equality check predicate on the "nickname" field. It's identical to NicknameEQ.
func Nickname(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldNickname, v))
}

// Mobile applies equality check predicate on the "mobile" field. It's identical to MobileEQ.
func Mobile(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldMobile, v))
}

// Email applies equality check predicate on the "email" field. It's identical to EmailEQ.
func Email(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldEmail, v))
}

// Post applies equality check predicate on the "post" field. It's identical to PostEQ.
func Post(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldPost, v))
}

// IsSuperuser applies equality check predicate on the "is_superuser" field. It's identical to IsSuperuserEQ.
func IsSuperuser(v bool) predicate.User {
	return predicate.User(sql.FieldEQ(FieldIsSuperuser, v))
}

// DefaultTenantID applies equality check predicate on the "default_tenant_id" field. It's identical to DefaultTenantIDEQ.
func DefaultTenantID(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldDefaultTenantID, v))
}

// AvatarID applies equality check predicate on the "avatar_id" field. It's identical to AvatarIDEQ.
func AvatarID(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldAvatarID, v))
}

// DeviceNo applies equality check predicate on the "device_no" field. It's identical to DeviceNoEQ.
func DeviceNo(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldDeviceNo, v))
}

// Kind applies equality check predicate on the "kind" field. It's identical to KindEQ.
func Kind(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldKind, v))
}

// Imei applies equality check predicate on the "imei" field. It's identical to ImeiEQ.
func Imei(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldImei, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.User {
	return predicate.User(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.User {
	return predicate.User(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.User {
	return predicate.User(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.User {
	return predicate.User(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldLTE(FieldUpdatedAt, v))
}

// StatusEQ applies the EQ predicate on the "status" field.
func StatusEQ(v bool) predicate.User {
	return predicate.User(sql.FieldEQ(FieldStatus, v))
}

// StatusNEQ applies the NEQ predicate on the "status" field.
func StatusNEQ(v bool) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldStatus, v))
}

// StatusIsNil applies the IsNil predicate on the "status" field.
func StatusIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldStatus))
}

// StatusNotNil applies the NotNil predicate on the "status" field.
func StatusNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldStatus))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.User {
	return predicate.User(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.User {
	return predicate.User(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.User {
	return predicate.User(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldDeletedAt))
}

// UsernameEQ applies the EQ predicate on the "username" field.
func UsernameEQ(v string) predicate.User {
	b, _ := NewSm4Tool().Encrypt([]byte(v))
	return predicate.User(sql.FieldEQ(FieldUsername, string(b)))
}

// UsernameNEQ applies the NEQ predicate on the "username" field.
func UsernameNEQ(v string) predicate.User {
	b, _ := NewSm4Tool().Encrypt([]byte(v))
	return predicate.User(sql.FieldNEQ(FieldUsername, string(b)))
}

// UsernameIn applies the In predicate on the "username" field.
func UsernameIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldUsername, vs...))
}

// UsernameNotIn applies the NotIn predicate on the "username" field.
func UsernameNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldUsername, vs...))
}

// UsernameGT applies the GT predicate on the "username" field.
func UsernameGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldUsername, v))
}

// UsernameGTE applies the GTE predicate on the "username" field.
func UsernameGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldUsername, v))
}

// UsernameLT applies the LT predicate on the "username" field.
func UsernameLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldUsername, v))
}

// UsernameLTE applies the LTE predicate on the "username" field.
func UsernameLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldUsername, v))
}

// UsernameContains applies the Contains predicate on the "username" field.
func UsernameContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldUsername, v))
}

// UsernameHasPrefix applies the HasPrefix predicate on the "username" field.
func UsernameHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldUsername, v))
}

// UsernameHasSuffix applies the HasSuffix predicate on the "username" field.
func UsernameHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldUsername, v))
}

// UsernameEqualFold applies the EqualFold predicate on the "username" field.
func UsernameEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldUsername, v))
}

// UsernameContainsFold applies the ContainsFold predicate on the "username" field.
func UsernameContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldUsername, v))
}

// PasswordEQ applies the EQ predicate on the "password" field.
func PasswordEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldPassword, v))
}

// PasswordNEQ applies the NEQ predicate on the "password" field.
func PasswordNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldPassword, v))
}

// PasswordIn applies the In predicate on the "password" field.
func PasswordIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldPassword, vs...))
}

// PasswordNotIn applies the NotIn predicate on the "password" field.
func PasswordNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldPassword, vs...))
}

// PasswordGT applies the GT predicate on the "password" field.
func PasswordGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldPassword, v))
}

// PasswordGTE applies the GTE predicate on the "password" field.
func PasswordGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldPassword, v))
}

// PasswordLT applies the LT predicate on the "password" field.
func PasswordLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldPassword, v))
}

// PasswordLTE applies the LTE predicate on the "password" field.
func PasswordLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldPassword, v))
}

// PasswordContains applies the Contains predicate on the "password" field.
func PasswordContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldPassword, v))
}

// PasswordHasPrefix applies the HasPrefix predicate on the "password" field.
func PasswordHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldPassword, v))
}

// PasswordHasSuffix applies the HasSuffix predicate on the "password" field.
func PasswordHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldPassword, v))
}

// PasswordEqualFold applies the EqualFold predicate on the "password" field.
func PasswordEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldPassword, v))
}

// PasswordContainsFold applies the ContainsFold predicate on the "password" field.
func PasswordContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldPassword, v))
}

// NicknameEQ applies the EQ predicate on the "nickname" field.
func NicknameEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldNickname, v))
}

// NicknameNEQ applies the NEQ predicate on the "nickname" field.
func NicknameNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldNickname, v))
}

// NicknameIn applies the In predicate on the "nickname" field.
func NicknameIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldNickname, vs...))
}

// NicknameNotIn applies the NotIn predicate on the "nickname" field.
func NicknameNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldNickname, vs...))
}

// NicknameGT applies the GT predicate on the "nickname" field.
func NicknameGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldNickname, v))
}

// NicknameGTE applies the GTE predicate on the "nickname" field.
func NicknameGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldNickname, v))
}

// NicknameLT applies the LT predicate on the "nickname" field.
func NicknameLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldNickname, v))
}

// NicknameLTE applies the LTE predicate on the "nickname" field.
func NicknameLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldNickname, v))
}

// NicknameContains applies the Contains predicate on the "nickname" field.
func NicknameContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldNickname, v))
}

// NicknameHasPrefix applies the HasPrefix predicate on the "nickname" field.
func NicknameHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldNickname, v))
}

// NicknameHasSuffix applies the HasSuffix predicate on the "nickname" field.
func NicknameHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldNickname, v))
}

// NicknameIsNil applies the IsNil predicate on the "nickname" field.
func NicknameIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldNickname))
}

// NicknameNotNil applies the NotNil predicate on the "nickname" field.
func NicknameNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldNickname))
}

// NicknameEqualFold applies the EqualFold predicate on the "nickname" field.
func NicknameEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldNickname, v))
}

// NicknameContainsFold applies the ContainsFold predicate on the "nickname" field.
func NicknameContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldNickname, v))
}

// MobileEQ applies the EQ predicate on the "mobile" field.
func MobileEQ(v string) predicate.User {
	b, _ := NewSm4Tool().Encrypt([]byte(v))
	return predicate.User(sql.FieldEQ(FieldMobile, string(b)))
}

// MobileNEQ applies the NEQ predicate on the "mobile" field.
func MobileNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldMobile, v))
}

// MobileIn applies the In predicate on the "mobile" field.
func MobileIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldMobile, vs...))
}

// MobileNotIn applies the NotIn predicate on the "mobile" field.
func MobileNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldMobile, vs...))
}

// MobileGT applies the GT predicate on the "mobile" field.
func MobileGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldMobile, v))
}

// MobileGTE applies the GTE predicate on the "mobile" field.
func MobileGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldMobile, v))
}

// MobileLT applies the LT predicate on the "mobile" field.
func MobileLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldMobile, v))
}

// MobileLTE applies the LTE predicate on the "mobile" field.
func MobileLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldMobile, v))
}

// MobileContains applies the Contains predicate on the "mobile" field.
func MobileContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldMobile, v))
}

// MobileHasPrefix applies the HasPrefix predicate on the "mobile" field.
func MobileHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldMobile, v))
}

// MobileHasSuffix applies the HasSuffix predicate on the "mobile" field.
func MobileHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldMobile, v))
}

// MobileIsNil applies the IsNil predicate on the "mobile" field.
func MobileIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldMobile))
}

// MobileNotNil applies the NotNil predicate on the "mobile" field.
func MobileNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldMobile))
}

// MobileEqualFold applies the EqualFold predicate on the "mobile" field.
func MobileEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldMobile, v))
}

// MobileContainsFold applies the ContainsFold predicate on the "mobile" field.
func MobileContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldMobile, v))
}

// EmailEQ applies the EQ predicate on the "email" field.
func EmailEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldEmail, v))
}

// EmailNEQ applies the NEQ predicate on the "email" field.
func EmailNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldEmail, v))
}

// EmailIn applies the In predicate on the "email" field.
func EmailIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldEmail, vs...))
}

// EmailNotIn applies the NotIn predicate on the "email" field.
func EmailNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldEmail, vs...))
}

// EmailGT applies the GT predicate on the "email" field.
func EmailGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldEmail, v))
}

// EmailGTE applies the GTE predicate on the "email" field.
func EmailGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldEmail, v))
}

// EmailLT applies the LT predicate on the "email" field.
func EmailLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldEmail, v))
}

// EmailLTE applies the LTE predicate on the "email" field.
func EmailLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldEmail, v))
}

// EmailContains applies the Contains predicate on the "email" field.
func EmailContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldEmail, v))
}

// EmailHasPrefix applies the HasPrefix predicate on the "email" field.
func EmailHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldEmail, v))
}

// EmailHasSuffix applies the HasSuffix predicate on the "email" field.
func EmailHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldEmail, v))
}

// EmailIsNil applies the IsNil predicate on the "email" field.
func EmailIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldEmail))
}

// EmailNotNil applies the NotNil predicate on the "email" field.
func EmailNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldEmail))
}

// EmailEqualFold applies the EqualFold predicate on the "email" field.
func EmailEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldEmail, v))
}

// EmailContainsFold applies the ContainsFold predicate on the "email" field.
func EmailContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldEmail, v))
}

// GenderEQ applies the EQ predicate on the "gender" field.
func GenderEQ(v enums.Gender) predicate.User {
	return predicate.User(sql.FieldEQ(FieldGender, v))
}

// GenderNEQ applies the NEQ predicate on the "gender" field.
func GenderNEQ(v enums.Gender) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldGender, v))
}

// GenderIn applies the In predicate on the "gender" field.
func GenderIn(vs ...enums.Gender) predicate.User {
	return predicate.User(sql.FieldIn(FieldGender, vs...))
}

// GenderNotIn applies the NotIn predicate on the "gender" field.
func GenderNotIn(vs ...enums.Gender) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldGender, vs...))
}

// PostEQ applies the EQ predicate on the "post" field.
func PostEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldPost, v))
}

// PostNEQ applies the NEQ predicate on the "post" field.
func PostNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldPost, v))
}

// PostIn applies the In predicate on the "post" field.
func PostIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldPost, vs...))
}

// PostNotIn applies the NotIn predicate on the "post" field.
func PostNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldPost, vs...))
}

// PostGT applies the GT predicate on the "post" field.
func PostGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldPost, v))
}

// PostGTE applies the GTE predicate on the "post" field.
func PostGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldPost, v))
}

// PostLT applies the LT predicate on the "post" field.
func PostLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldPost, v))
}

// PostLTE applies the LTE predicate on the "post" field.
func PostLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldPost, v))
}

// PostContains applies the Contains predicate on the "post" field.
func PostContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldPost, v))
}

// PostHasPrefix applies the HasPrefix predicate on the "post" field.
func PostHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldPost, v))
}

// PostHasSuffix applies the HasSuffix predicate on the "post" field.
func PostHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldPost, v))
}

// PostIsNil applies the IsNil predicate on the "post" field.
func PostIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldPost))
}

// PostNotNil applies the NotNil predicate on the "post" field.
func PostNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldPost))
}

// PostEqualFold applies the EqualFold predicate on the "post" field.
func PostEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldPost, v))
}

// PostContainsFold applies the ContainsFold predicate on the "post" field.
func PostContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldPost, v))
}

// IsSuperuserEQ applies the EQ predicate on the "is_superuser" field.
func IsSuperuserEQ(v bool) predicate.User {
	return predicate.User(sql.FieldEQ(FieldIsSuperuser, v))
}

// IsSuperuserNEQ applies the NEQ predicate on the "is_superuser" field.
func IsSuperuserNEQ(v bool) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldIsSuperuser, v))
}

// DefaultTenantIDEQ applies the EQ predicate on the "default_tenant_id" field.
func DefaultTenantIDEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldDefaultTenantID, v))
}

// DefaultTenantIDNEQ applies the NEQ predicate on the "default_tenant_id" field.
func DefaultTenantIDNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldDefaultTenantID, v))
}

// DefaultTenantIDIn applies the In predicate on the "default_tenant_id" field.
func DefaultTenantIDIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldDefaultTenantID, vs...))
}

// DefaultTenantIDNotIn applies the NotIn predicate on the "default_tenant_id" field.
func DefaultTenantIDNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldDefaultTenantID, vs...))
}

// DefaultTenantIDGT applies the GT predicate on the "default_tenant_id" field.
func DefaultTenantIDGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldDefaultTenantID, v))
}

// DefaultTenantIDGTE applies the GTE predicate on the "default_tenant_id" field.
func DefaultTenantIDGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldDefaultTenantID, v))
}

// DefaultTenantIDLT applies the LT predicate on the "default_tenant_id" field.
func DefaultTenantIDLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldDefaultTenantID, v))
}

// DefaultTenantIDLTE applies the LTE predicate on the "default_tenant_id" field.
func DefaultTenantIDLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldDefaultTenantID, v))
}

// DefaultTenantIDContains applies the Contains predicate on the "default_tenant_id" field.
func DefaultTenantIDContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldDefaultTenantID, v))
}

// DefaultTenantIDHasPrefix applies the HasPrefix predicate on the "default_tenant_id" field.
func DefaultTenantIDHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldDefaultTenantID, v))
}

// DefaultTenantIDHasSuffix applies the HasSuffix predicate on the "default_tenant_id" field.
func DefaultTenantIDHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldDefaultTenantID, v))
}

// DefaultTenantIDIsNil applies the IsNil predicate on the "default_tenant_id" field.
func DefaultTenantIDIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldDefaultTenantID))
}

// DefaultTenantIDNotNil applies the NotNil predicate on the "default_tenant_id" field.
func DefaultTenantIDNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldDefaultTenantID))
}

// DefaultTenantIDEqualFold applies the EqualFold predicate on the "default_tenant_id" field.
func DefaultTenantIDEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldDefaultTenantID, v))
}

// DefaultTenantIDContainsFold applies the ContainsFold predicate on the "default_tenant_id" field.
func DefaultTenantIDContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldDefaultTenantID, v))
}

// AvatarIDEQ applies the EQ predicate on the "avatar_id" field.
func AvatarIDEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldAvatarID, v))
}

// AvatarIDNEQ applies the NEQ predicate on the "avatar_id" field.
func AvatarIDNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldAvatarID, v))
}

// AvatarIDIn applies the In predicate on the "avatar_id" field.
func AvatarIDIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldAvatarID, vs...))
}

// AvatarIDNotIn applies the NotIn predicate on the "avatar_id" field.
func AvatarIDNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldAvatarID, vs...))
}

// AvatarIDGT applies the GT predicate on the "avatar_id" field.
func AvatarIDGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldAvatarID, v))
}

// AvatarIDGTE applies the GTE predicate on the "avatar_id" field.
func AvatarIDGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldAvatarID, v))
}

// AvatarIDLT applies the LT predicate on the "avatar_id" field.
func AvatarIDLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldAvatarID, v))
}

// AvatarIDLTE applies the LTE predicate on the "avatar_id" field.
func AvatarIDLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldAvatarID, v))
}

// AvatarIDContains applies the Contains predicate on the "avatar_id" field.
func AvatarIDContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldAvatarID, v))
}

// AvatarIDHasPrefix applies the HasPrefix predicate on the "avatar_id" field.
func AvatarIDHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldAvatarID, v))
}

// AvatarIDHasSuffix applies the HasSuffix predicate on the "avatar_id" field.
func AvatarIDHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldAvatarID, v))
}

// AvatarIDIsNil applies the IsNil predicate on the "avatar_id" field.
func AvatarIDIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldAvatarID))
}

// AvatarIDNotNil applies the NotNil predicate on the "avatar_id" field.
func AvatarIDNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldAvatarID))
}

// AvatarIDEqualFold applies the EqualFold predicate on the "avatar_id" field.
func AvatarIDEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldAvatarID, v))
}

// AvatarIDContainsFold applies the ContainsFold predicate on the "avatar_id" field.
func AvatarIDContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldAvatarID, v))
}

// DeviceNoEQ applies the EQ predicate on the "device_no" field.
func DeviceNoEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldDeviceNo, v))
}

// DeviceNoNEQ applies the NEQ predicate on the "device_no" field.
func DeviceNoNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldDeviceNo, v))
}

// DeviceNoIn applies the In predicate on the "device_no" field.
func DeviceNoIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldDeviceNo, vs...))
}

// DeviceNoNotIn applies the NotIn predicate on the "device_no" field.
func DeviceNoNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldDeviceNo, vs...))
}

// DeviceNoGT applies the GT predicate on the "device_no" field.
func DeviceNoGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldDeviceNo, v))
}

// DeviceNoGTE applies the GTE predicate on the "device_no" field.
func DeviceNoGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldDeviceNo, v))
}

// DeviceNoLT applies the LT predicate on the "device_no" field.
func DeviceNoLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldDeviceNo, v))
}

// DeviceNoLTE applies the LTE predicate on the "device_no" field.
func DeviceNoLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldDeviceNo, v))
}

// DeviceNoContains applies the Contains predicate on the "device_no" field.
func DeviceNoContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldDeviceNo, v))
}

// DeviceNoHasPrefix applies the HasPrefix predicate on the "device_no" field.
func DeviceNoHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldDeviceNo, v))
}

// DeviceNoHasSuffix applies the HasSuffix predicate on the "device_no" field.
func DeviceNoHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldDeviceNo, v))
}

// DeviceNoIsNil applies the IsNil predicate on the "device_no" field.
func DeviceNoIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldDeviceNo))
}

// DeviceNoNotNil applies the NotNil predicate on the "device_no" field.
func DeviceNoNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldDeviceNo))
}

// DeviceNoEqualFold applies the EqualFold predicate on the "device_no" field.
func DeviceNoEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldDeviceNo, v))
}

// DeviceNoContainsFold applies the ContainsFold predicate on the "device_no" field.
func DeviceNoContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldDeviceNo, v))
}

// KindEQ applies the EQ predicate on the "kind" field.
func KindEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldKind, v))
}

// KindNEQ applies the NEQ predicate on the "kind" field.
func KindNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldKind, v))
}

// KindIn applies the In predicate on the "kind" field.
func KindIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldKind, vs...))
}

// KindNotIn applies the NotIn predicate on the "kind" field.
func KindNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldKind, vs...))
}

// KindGT applies the GT predicate on the "kind" field.
func KindGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldKind, v))
}

// KindGTE applies the GTE predicate on the "kind" field.
func KindGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldKind, v))
}

// KindLT applies the LT predicate on the "kind" field.
func KindLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldKind, v))
}

// KindLTE applies the LTE predicate on the "kind" field.
func KindLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldKind, v))
}

// KindContains applies the Contains predicate on the "kind" field.
func KindContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldKind, v))
}

// KindHasPrefix applies the HasPrefix predicate on the "kind" field.
func KindHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldKind, v))
}

// KindHasSuffix applies the HasSuffix predicate on the "kind" field.
func KindHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldKind, v))
}

// KindIsNil applies the IsNil predicate on the "kind" field.
func KindIsNil() predicate.User {
	return predicate.User(sql.FieldIsNull(FieldKind))
}

// KindNotNil applies the NotNil predicate on the "kind" field.
func KindNotNil() predicate.User {
	return predicate.User(sql.FieldNotNull(FieldKind))
}

// KindEqualFold applies the EqualFold predicate on the "kind" field.
func KindEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldKind, v))
}

// KindContainsFold applies the ContainsFold predicate on the "kind" field.
func KindContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldKind, v))
}

// ImeiEQ applies the EQ predicate on the "imei" field.
func ImeiEQ(v string) predicate.User {
	return predicate.User(sql.FieldEQ(FieldImei, v))
}

// ImeiNEQ applies the NEQ predicate on the "imei" field.
func ImeiNEQ(v string) predicate.User {
	return predicate.User(sql.FieldNEQ(FieldImei, v))
}

// ImeiIn applies the In predicate on the "imei" field.
func ImeiIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldIn(FieldImei, vs...))
}

// ImeiNotIn applies the NotIn predicate on the "imei" field.
func ImeiNotIn(vs ...string) predicate.User {
	return predicate.User(sql.FieldNotIn(FieldImei, vs...))
}

// ImeiGT applies the GT predicate on the "imei" field.
func ImeiGT(v string) predicate.User {
	return predicate.User(sql.FieldGT(FieldImei, v))
}

// ImeiGTE applies the GTE predicate on the "imei" field.
func ImeiGTE(v string) predicate.User {
	return predicate.User(sql.FieldGTE(FieldImei, v))
}

// ImeiLT applies the LT predicate on the "imei" field.
func ImeiLT(v string) predicate.User {
	return predicate.User(sql.FieldLT(FieldImei, v))
}

// ImeiLTE applies the LTE predicate on the "imei" field.
func ImeiLTE(v string) predicate.User {
	return predicate.User(sql.FieldLTE(FieldImei, v))
}

// ImeiContains applies the Contains predicate on the "imei" field.
func ImeiContains(v string) predicate.User {
	return predicate.User(sql.FieldContains(FieldImei, v))
}

// ImeiHasPrefix applies the HasPrefix predicate on the "imei" field.
func ImeiHasPrefix(v string) predicate.User {
	return predicate.User(sql.FieldHasPrefix(FieldImei, v))
}

// ImeiHasSuffix applies the HasSuffix predicate on the "imei" field.
func ImeiHasSuffix(v string) predicate.User {
	return predicate.User(sql.FieldHasSuffix(FieldImei, v))
}

// ImeiEqualFold applies the EqualFold predicate on the "imei" field.
func ImeiEqualFold(v string) predicate.User {
	return predicate.User(sql.FieldEqualFold(FieldImei, v))
}

// ImeiContainsFold applies the ContainsFold predicate on the "imei" field.
func ImeiContainsFold(v string) predicate.User {
	return predicate.User(sql.FieldContainsFold(FieldImei, v))
}

// HasTenants applies the HasEdge predicate on the "tenants" edge.
func HasTenants() predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, TenantsTable, TenantsPrimaryKey...),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTenantsWith applies the HasEdge predicate on the "tenants" edge with a given conditions (other predicates).
func HasTenantsWith(preds ...predicate.Tenant) predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := newTenantsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasOrganizations applies the HasEdge predicate on the "organizations" edge.
func HasOrganizations() predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, OrganizationsTable, OrganizationsPrimaryKey...),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasOrganizationsWith applies the HasEdge predicate on the "organizations" edge with a given conditions (other predicates).
func HasOrganizationsWith(preds ...predicate.Organization) predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := newOrganizationsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasPositions applies the HasEdge predicate on the "positions" edge.
func HasPositions() predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, PositionsTable, PositionsPrimaryKey...),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasPositionsWith applies the HasEdge predicate on the "positions" edge with a given conditions (other predicates).
func HasPositionsWith(preds ...predicate.Position) predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := newPositionsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasGroups applies the HasEdge predicate on the "groups" edge.
func HasGroups() predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2M, false, GroupsTable, GroupsPrimaryKey...),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasGroupsWith applies the HasEdge predicate on the "groups" edge with a given conditions (other predicates).
func HasGroupsWith(preds ...predicate.Group) predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := newGroupsStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasRoles applies the HasEdge predicate on the "roles" edge.
func HasRoles() predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2M, true, RolesTable, RolesPrimaryKey...),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasRolesWith applies the HasEdge predicate on the "roles" edge with a given conditions (other predicates).
func HasRolesWith(preds ...predicate.Role) predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := newRolesStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasOrganizationInfos applies the HasEdge predicate on the "organization_infos" edge.
func HasOrganizationInfos() predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, OrganizationInfosTable, OrganizationInfosColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasOrganizationInfosWith applies the HasEdge predicate on the "organization_infos" edge with a given conditions (other predicates).
func HasOrganizationInfosWith(preds ...predicate.OrganizationUserInfo) predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := newOrganizationInfosStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasTenantInfos applies the HasEdge predicate on the "tenant_infos" edge.
func HasTenantInfos() predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, TenantInfosTable, TenantInfosColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTenantInfosWith applies the HasEdge predicate on the "tenant_infos" edge with a given conditions (other predicates).
func HasTenantInfosWith(preds ...predicate.TenantUserInfo) predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := newTenantInfosStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasFiles applies the HasEdge predicate on the "files" edge.
func HasFiles() predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.O2M, false, FilesTable, FilesColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasFilesWith applies the HasEdge predicate on the "files" edge with a given conditions (other predicates).
func HasFilesWith(preds ...predicate.File) predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := newFilesStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasAvatar applies the HasEdge predicate on the "avatar" edge.
func HasAvatar() predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, true, AvatarTable, AvatarColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasAvatarWith applies the HasEdge predicate on the "avatar" edge with a given conditions (other predicates).
func HasAvatarWith(preds ...predicate.File) predicate.User {
	return predicate.User(func(s *sql.Selector) {
		step := newAvatarStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.User) predicate.User {
	return predicate.User(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.User) predicate.User {
	return predicate.User(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.User) predicate.User {
	return predicate.User(sql.NotPredicates(p))
}
