// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/button"
	"phoenix/service/saas/model/ent/menu"
	"phoenix/service/saas/model/ent/role"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// ButtonCreate is the builder for creating a Button entity.
type ButtonCreate struct {
	config
	mutation *ButtonMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (bc *ButtonCreate) SetCreatedAt(t time.Time) *ButtonCreate {
	bc.mutation.SetCreatedAt(t)
	return bc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (bc *ButtonCreate) SetNillableCreatedAt(t *time.Time) *ButtonCreate {
	if t != nil {
		bc.SetCreatedAt(*t)
	}
	return bc
}

// SetUpdatedAt sets the "updated_at" field.
func (bc *ButtonCreate) SetUpdatedAt(t time.Time) *ButtonCreate {
	bc.mutation.SetUpdatedAt(t)
	return bc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (bc *ButtonCreate) SetNillableUpdatedAt(t *time.Time) *ButtonCreate {
	if t != nil {
		bc.SetUpdatedAt(*t)
	}
	return bc
}

// SetSort sets the "sort" field.
func (bc *ButtonCreate) SetSort(u uint32) *ButtonCreate {
	bc.mutation.SetSort(u)
	return bc
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (bc *ButtonCreate) SetNillableSort(u *uint32) *ButtonCreate {
	if u != nil {
		bc.SetSort(*u)
	}
	return bc
}

// SetDeletedAt sets the "deleted_at" field.
func (bc *ButtonCreate) SetDeletedAt(t time.Time) *ButtonCreate {
	bc.mutation.SetDeletedAt(t)
	return bc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (bc *ButtonCreate) SetNillableDeletedAt(t *time.Time) *ButtonCreate {
	if t != nil {
		bc.SetDeletedAt(*t)
	}
	return bc
}

// SetName sets the "name" field.
func (bc *ButtonCreate) SetName(s string) *ButtonCreate {
	bc.mutation.SetName(s)
	return bc
}

// SetNillableName sets the "name" field if the given value is not nil.
func (bc *ButtonCreate) SetNillableName(s *string) *ButtonCreate {
	if s != nil {
		bc.SetName(*s)
	}
	return bc
}

// SetCode sets the "code" field.
func (bc *ButtonCreate) SetCode(s string) *ButtonCreate {
	bc.mutation.SetCode(s)
	return bc
}

// SetMenuID sets the "menu_id" field.
func (bc *ButtonCreate) SetMenuID(s string) *ButtonCreate {
	bc.mutation.SetMenuID(s)
	return bc
}

// SetNillableMenuID sets the "menu_id" field if the given value is not nil.
func (bc *ButtonCreate) SetNillableMenuID(s *string) *ButtonCreate {
	if s != nil {
		bc.SetMenuID(*s)
	}
	return bc
}

// SetID sets the "id" field.
func (bc *ButtonCreate) SetID(s string) *ButtonCreate {
	bc.mutation.SetID(s)
	return bc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (bc *ButtonCreate) SetNillableID(s *string) *ButtonCreate {
	if s != nil {
		bc.SetID(*s)
	}
	return bc
}

// AddRoleIDs adds the "roles" edge to the Role entity by IDs.
func (bc *ButtonCreate) AddRoleIDs(ids ...string) *ButtonCreate {
	bc.mutation.AddRoleIDs(ids...)
	return bc
}

// AddRoles adds the "roles" edges to the Role entity.
func (bc *ButtonCreate) AddRoles(r ...*Role) *ButtonCreate {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return bc.AddRoleIDs(ids...)
}

// SetMenu sets the "menu" edge to the Menu entity.
func (bc *ButtonCreate) SetMenu(m *Menu) *ButtonCreate {
	return bc.SetMenuID(m.ID)
}

// Mutation returns the ButtonMutation object of the builder.
func (bc *ButtonCreate) Mutation() *ButtonMutation {
	return bc.mutation
}

// Save creates the Button in the database.
func (bc *ButtonCreate) Save(ctx context.Context) (*Button, error) {
	if err := bc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, bc.sqlSave, bc.mutation, bc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (bc *ButtonCreate) SaveX(ctx context.Context) *Button {
	v, err := bc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (bc *ButtonCreate) Exec(ctx context.Context) error {
	_, err := bc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (bc *ButtonCreate) ExecX(ctx context.Context) {
	if err := bc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (bc *ButtonCreate) defaults() error {
	if _, ok := bc.mutation.CreatedAt(); !ok {
		if button.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized button.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := button.DefaultCreatedAt()
		bc.mutation.SetCreatedAt(v)
	}
	if _, ok := bc.mutation.UpdatedAt(); !ok {
		if button.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized button.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := button.DefaultUpdatedAt()
		bc.mutation.SetUpdatedAt(v)
	}
	if _, ok := bc.mutation.Sort(); !ok {
		v := button.DefaultSort
		bc.mutation.SetSort(v)
	}
	if _, ok := bc.mutation.Name(); !ok {
		v := button.DefaultName
		bc.mutation.SetName(v)
	}
	if _, ok := bc.mutation.ID(); !ok {
		if button.DefaultID == nil {
			return fmt.Errorf("ent: uninitialized button.DefaultID (forgotten import ent/runtime?)")
		}
		v := button.DefaultID()
		bc.mutation.SetID(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (bc *ButtonCreate) check() error {
	if _, ok := bc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Button.created_at"`)}
	}
	if _, ok := bc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Button.updated_at"`)}
	}
	if _, ok := bc.mutation.Sort(); !ok {
		return &ValidationError{Name: "sort", err: errors.New(`ent: missing required field "Button.sort"`)}
	}
	if _, ok := bc.mutation.Code(); !ok {
		return &ValidationError{Name: "code", err: errors.New(`ent: missing required field "Button.code"`)}
	}
	return nil
}

func (bc *ButtonCreate) sqlSave(ctx context.Context) (*Button, error) {
	if err := bc.check(); err != nil {
		return nil, err
	}
	_node, _spec := bc.createSpec()
	if err := sqlgraph.CreateNode(ctx, bc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected Button.ID type: %T", _spec.ID.Value)
		}
	}
	bc.mutation.id = &_node.ID
	bc.mutation.done = true
	return _node, nil
}

func (bc *ButtonCreate) createSpec() (*Button, *sqlgraph.CreateSpec) {
	var (
		_node = &Button{config: bc.config}
		_spec = sqlgraph.NewCreateSpec(button.Table, sqlgraph.NewFieldSpec(button.FieldID, field.TypeString))
	)
	_spec.OnConflict = bc.conflict
	if id, ok := bc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := bc.mutation.CreatedAt(); ok {
		_spec.SetField(button.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := bc.mutation.UpdatedAt(); ok {
		_spec.SetField(button.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := bc.mutation.Sort(); ok {
		_spec.SetField(button.FieldSort, field.TypeUint32, value)
		_node.Sort = value
	}
	if value, ok := bc.mutation.DeletedAt(); ok {
		_spec.SetField(button.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := bc.mutation.Name(); ok {
		_spec.SetField(button.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := bc.mutation.Code(); ok {
		_spec.SetField(button.FieldCode, field.TypeString, value)
		_node.Code = value
	}
	if nodes := bc.mutation.RolesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   button.RolesTable,
			Columns: button.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := bc.mutation.MenuIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   button.MenuTable,
			Columns: []string{button.MenuColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.MenuID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Button.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.ButtonUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (bc *ButtonCreate) OnConflict(opts ...sql.ConflictOption) *ButtonUpsertOne {
	bc.conflict = opts
	return &ButtonUpsertOne{
		create: bc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Button.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (bc *ButtonCreate) OnConflictColumns(columns ...string) *ButtonUpsertOne {
	bc.conflict = append(bc.conflict, sql.ConflictColumns(columns...))
	return &ButtonUpsertOne{
		create: bc,
	}
}

type (
	// ButtonUpsertOne is the builder for "upsert"-ing
	//  one Button node.
	ButtonUpsertOne struct {
		create *ButtonCreate
	}

	// ButtonUpsert is the "OnConflict" setter.
	ButtonUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *ButtonUpsert) SetUpdatedAt(v time.Time) *ButtonUpsert {
	u.Set(button.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *ButtonUpsert) UpdateUpdatedAt() *ButtonUpsert {
	u.SetExcluded(button.FieldUpdatedAt)
	return u
}

// SetSort sets the "sort" field.
func (u *ButtonUpsert) SetSort(v uint32) *ButtonUpsert {
	u.Set(button.FieldSort, v)
	return u
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *ButtonUpsert) UpdateSort() *ButtonUpsert {
	u.SetExcluded(button.FieldSort)
	return u
}

// AddSort adds v to the "sort" field.
func (u *ButtonUpsert) AddSort(v uint32) *ButtonUpsert {
	u.Add(button.FieldSort, v)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *ButtonUpsert) SetDeletedAt(v time.Time) *ButtonUpsert {
	u.Set(button.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *ButtonUpsert) UpdateDeletedAt() *ButtonUpsert {
	u.SetExcluded(button.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *ButtonUpsert) ClearDeletedAt() *ButtonUpsert {
	u.SetNull(button.FieldDeletedAt)
	return u
}

// SetName sets the "name" field.
func (u *ButtonUpsert) SetName(v string) *ButtonUpsert {
	u.Set(button.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *ButtonUpsert) UpdateName() *ButtonUpsert {
	u.SetExcluded(button.FieldName)
	return u
}

// ClearName clears the value of the "name" field.
func (u *ButtonUpsert) ClearName() *ButtonUpsert {
	u.SetNull(button.FieldName)
	return u
}

// SetCode sets the "code" field.
func (u *ButtonUpsert) SetCode(v string) *ButtonUpsert {
	u.Set(button.FieldCode, v)
	return u
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *ButtonUpsert) UpdateCode() *ButtonUpsert {
	u.SetExcluded(button.FieldCode)
	return u
}

// SetMenuID sets the "menu_id" field.
func (u *ButtonUpsert) SetMenuID(v string) *ButtonUpsert {
	u.Set(button.FieldMenuID, v)
	return u
}

// UpdateMenuID sets the "menu_id" field to the value that was provided on create.
func (u *ButtonUpsert) UpdateMenuID() *ButtonUpsert {
	u.SetExcluded(button.FieldMenuID)
	return u
}

// ClearMenuID clears the value of the "menu_id" field.
func (u *ButtonUpsert) ClearMenuID() *ButtonUpsert {
	u.SetNull(button.FieldMenuID)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Button.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(button.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *ButtonUpsertOne) UpdateNewValues() *ButtonUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(button.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(button.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Button.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *ButtonUpsertOne) Ignore() *ButtonUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *ButtonUpsertOne) DoNothing() *ButtonUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the ButtonCreate.OnConflict
// documentation for more info.
func (u *ButtonUpsertOne) Update(set func(*ButtonUpsert)) *ButtonUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&ButtonUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *ButtonUpsertOne) SetUpdatedAt(v time.Time) *ButtonUpsertOne {
	return u.Update(func(s *ButtonUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *ButtonUpsertOne) UpdateUpdatedAt() *ButtonUpsertOne {
	return u.Update(func(s *ButtonUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetSort sets the "sort" field.
func (u *ButtonUpsertOne) SetSort(v uint32) *ButtonUpsertOne {
	return u.Update(func(s *ButtonUpsert) {
		s.SetSort(v)
	})
}

// AddSort adds v to the "sort" field.
func (u *ButtonUpsertOne) AddSort(v uint32) *ButtonUpsertOne {
	return u.Update(func(s *ButtonUpsert) {
		s.AddSort(v)
	})
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *ButtonUpsertOne) UpdateSort() *ButtonUpsertOne {
	return u.Update(func(s *ButtonUpsert) {
		s.UpdateSort()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *ButtonUpsertOne) SetDeletedAt(v time.Time) *ButtonUpsertOne {
	return u.Update(func(s *ButtonUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *ButtonUpsertOne) UpdateDeletedAt() *ButtonUpsertOne {
	return u.Update(func(s *ButtonUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *ButtonUpsertOne) ClearDeletedAt() *ButtonUpsertOne {
	return u.Update(func(s *ButtonUpsert) {
		s.ClearDeletedAt()
	})
}

// SetName sets the "name" field.
func (u *ButtonUpsertOne) SetName(v string) *ButtonUpsertOne {
	return u.Update(func(s *ButtonUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *ButtonUpsertOne) UpdateName() *ButtonUpsertOne {
	return u.Update(func(s *ButtonUpsert) {
		s.UpdateName()
	})
}

// ClearName clears the value of the "name" field.
func (u *ButtonUpsertOne) ClearName() *ButtonUpsertOne {
	return u.Update(func(s *ButtonUpsert) {
		s.ClearName()
	})
}

// SetCode sets the "code" field.
func (u *ButtonUpsertOne) SetCode(v string) *ButtonUpsertOne {
	return u.Update(func(s *ButtonUpsert) {
		s.SetCode(v)
	})
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *ButtonUpsertOne) UpdateCode() *ButtonUpsertOne {
	return u.Update(func(s *ButtonUpsert) {
		s.UpdateCode()
	})
}

// SetMenuID sets the "menu_id" field.
func (u *ButtonUpsertOne) SetMenuID(v string) *ButtonUpsertOne {
	return u.Update(func(s *ButtonUpsert) {
		s.SetMenuID(v)
	})
}

// UpdateMenuID sets the "menu_id" field to the value that was provided on create.
func (u *ButtonUpsertOne) UpdateMenuID() *ButtonUpsertOne {
	return u.Update(func(s *ButtonUpsert) {
		s.UpdateMenuID()
	})
}

// ClearMenuID clears the value of the "menu_id" field.
func (u *ButtonUpsertOne) ClearMenuID() *ButtonUpsertOne {
	return u.Update(func(s *ButtonUpsert) {
		s.ClearMenuID()
	})
}

// Exec executes the query.
func (u *ButtonUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for ButtonCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *ButtonUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *ButtonUpsertOne) ID(ctx context.Context) (id string, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: ButtonUpsertOne.ID is not supported by MySQL driver. Use ButtonUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *ButtonUpsertOne) IDX(ctx context.Context) string {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// ButtonCreateBulk is the builder for creating many Button entities in bulk.
type ButtonCreateBulk struct {
	config
	err      error
	builders []*ButtonCreate
	conflict []sql.ConflictOption
}

// Save creates the Button entities in the database.
func (bcb *ButtonCreateBulk) Save(ctx context.Context) ([]*Button, error) {
	if bcb.err != nil {
		return nil, bcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(bcb.builders))
	nodes := make([]*Button, len(bcb.builders))
	mutators := make([]Mutator, len(bcb.builders))
	for i := range bcb.builders {
		func(i int, root context.Context) {
			builder := bcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*ButtonMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, bcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = bcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, bcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, bcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (bcb *ButtonCreateBulk) SaveX(ctx context.Context) []*Button {
	v, err := bcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (bcb *ButtonCreateBulk) Exec(ctx context.Context) error {
	_, err := bcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (bcb *ButtonCreateBulk) ExecX(ctx context.Context) {
	if err := bcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Button.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.ButtonUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (bcb *ButtonCreateBulk) OnConflict(opts ...sql.ConflictOption) *ButtonUpsertBulk {
	bcb.conflict = opts
	return &ButtonUpsertBulk{
		create: bcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Button.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (bcb *ButtonCreateBulk) OnConflictColumns(columns ...string) *ButtonUpsertBulk {
	bcb.conflict = append(bcb.conflict, sql.ConflictColumns(columns...))
	return &ButtonUpsertBulk{
		create: bcb,
	}
}

// ButtonUpsertBulk is the builder for "upsert"-ing
// a bulk of Button nodes.
type ButtonUpsertBulk struct {
	create *ButtonCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Button.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(button.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *ButtonUpsertBulk) UpdateNewValues() *ButtonUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(button.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(button.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Button.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *ButtonUpsertBulk) Ignore() *ButtonUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *ButtonUpsertBulk) DoNothing() *ButtonUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the ButtonCreateBulk.OnConflict
// documentation for more info.
func (u *ButtonUpsertBulk) Update(set func(*ButtonUpsert)) *ButtonUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&ButtonUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *ButtonUpsertBulk) SetUpdatedAt(v time.Time) *ButtonUpsertBulk {
	return u.Update(func(s *ButtonUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *ButtonUpsertBulk) UpdateUpdatedAt() *ButtonUpsertBulk {
	return u.Update(func(s *ButtonUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetSort sets the "sort" field.
func (u *ButtonUpsertBulk) SetSort(v uint32) *ButtonUpsertBulk {
	return u.Update(func(s *ButtonUpsert) {
		s.SetSort(v)
	})
}

// AddSort adds v to the "sort" field.
func (u *ButtonUpsertBulk) AddSort(v uint32) *ButtonUpsertBulk {
	return u.Update(func(s *ButtonUpsert) {
		s.AddSort(v)
	})
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *ButtonUpsertBulk) UpdateSort() *ButtonUpsertBulk {
	return u.Update(func(s *ButtonUpsert) {
		s.UpdateSort()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *ButtonUpsertBulk) SetDeletedAt(v time.Time) *ButtonUpsertBulk {
	return u.Update(func(s *ButtonUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *ButtonUpsertBulk) UpdateDeletedAt() *ButtonUpsertBulk {
	return u.Update(func(s *ButtonUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *ButtonUpsertBulk) ClearDeletedAt() *ButtonUpsertBulk {
	return u.Update(func(s *ButtonUpsert) {
		s.ClearDeletedAt()
	})
}

// SetName sets the "name" field.
func (u *ButtonUpsertBulk) SetName(v string) *ButtonUpsertBulk {
	return u.Update(func(s *ButtonUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *ButtonUpsertBulk) UpdateName() *ButtonUpsertBulk {
	return u.Update(func(s *ButtonUpsert) {
		s.UpdateName()
	})
}

// ClearName clears the value of the "name" field.
func (u *ButtonUpsertBulk) ClearName() *ButtonUpsertBulk {
	return u.Update(func(s *ButtonUpsert) {
		s.ClearName()
	})
}

// SetCode sets the "code" field.
func (u *ButtonUpsertBulk) SetCode(v string) *ButtonUpsertBulk {
	return u.Update(func(s *ButtonUpsert) {
		s.SetCode(v)
	})
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *ButtonUpsertBulk) UpdateCode() *ButtonUpsertBulk {
	return u.Update(func(s *ButtonUpsert) {
		s.UpdateCode()
	})
}

// SetMenuID sets the "menu_id" field.
func (u *ButtonUpsertBulk) SetMenuID(v string) *ButtonUpsertBulk {
	return u.Update(func(s *ButtonUpsert) {
		s.SetMenuID(v)
	})
}

// UpdateMenuID sets the "menu_id" field to the value that was provided on create.
func (u *ButtonUpsertBulk) UpdateMenuID() *ButtonUpsertBulk {
	return u.Update(func(s *ButtonUpsert) {
		s.UpdateMenuID()
	})
}

// ClearMenuID clears the value of the "menu_id" field.
func (u *ButtonUpsertBulk) ClearMenuID() *ButtonUpsertBulk {
	return u.Update(func(s *ButtonUpsert) {
		s.ClearMenuID()
	})
}

// Exec executes the query.
func (u *ButtonUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the ButtonCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for ButtonCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *ButtonUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
