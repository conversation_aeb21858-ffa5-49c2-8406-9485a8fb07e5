// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/group"
	"phoenix/service/saas/model/ent/grouptype"
	"phoenix/service/saas/model/ent/predicate"
	"phoenix/service/saas/model/ent/role"
	"phoenix/service/saas/model/ent/tenant"
	"phoenix/service/saas/model/ent/user"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// GroupUpdate is the builder for updating Group entities.
type GroupUpdate struct {
	config
	hooks    []Hook
	mutation *GroupMutation
}

// Where appends a list predicates to the GroupUpdate builder.
func (gu *GroupUpdate) Where(ps ...predicate.Group) *GroupUpdate {
	gu.mutation.Where(ps...)
	return gu
}

// SetUpdatedAt sets the "updated_at" field.
func (gu *GroupUpdate) SetUpdatedAt(t time.Time) *GroupUpdate {
	gu.mutation.SetUpdatedAt(t)
	return gu
}

// SetStatus sets the "status" field.
func (gu *GroupUpdate) SetStatus(b bool) *GroupUpdate {
	gu.mutation.SetStatus(b)
	return gu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (gu *GroupUpdate) SetNillableStatus(b *bool) *GroupUpdate {
	if b != nil {
		gu.SetStatus(*b)
	}
	return gu
}

// ClearStatus clears the value of the "status" field.
func (gu *GroupUpdate) ClearStatus() *GroupUpdate {
	gu.mutation.ClearStatus()
	return gu
}

// SetSort sets the "sort" field.
func (gu *GroupUpdate) SetSort(u uint32) *GroupUpdate {
	gu.mutation.ResetSort()
	gu.mutation.SetSort(u)
	return gu
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (gu *GroupUpdate) SetNillableSort(u *uint32) *GroupUpdate {
	if u != nil {
		gu.SetSort(*u)
	}
	return gu
}

// AddSort adds u to the "sort" field.
func (gu *GroupUpdate) AddSort(u int32) *GroupUpdate {
	gu.mutation.AddSort(u)
	return gu
}

// SetTenantID sets the "tenant_id" field.
func (gu *GroupUpdate) SetTenantID(s string) *GroupUpdate {
	gu.mutation.SetTenantID(s)
	return gu
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (gu *GroupUpdate) SetNillableTenantID(s *string) *GroupUpdate {
	if s != nil {
		gu.SetTenantID(*s)
	}
	return gu
}

// SetDeletedAt sets the "deleted_at" field.
func (gu *GroupUpdate) SetDeletedAt(t time.Time) *GroupUpdate {
	gu.mutation.SetDeletedAt(t)
	return gu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (gu *GroupUpdate) SetNillableDeletedAt(t *time.Time) *GroupUpdate {
	if t != nil {
		gu.SetDeletedAt(*t)
	}
	return gu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (gu *GroupUpdate) ClearDeletedAt() *GroupUpdate {
	gu.mutation.ClearDeletedAt()
	return gu
}

// SetGroupTypeID sets the "group_type_id" field.
func (gu *GroupUpdate) SetGroupTypeID(s string) *GroupUpdate {
	gu.mutation.SetGroupTypeID(s)
	return gu
}

// SetNillableGroupTypeID sets the "group_type_id" field if the given value is not nil.
func (gu *GroupUpdate) SetNillableGroupTypeID(s *string) *GroupUpdate {
	if s != nil {
		gu.SetGroupTypeID(*s)
	}
	return gu
}

// ClearGroupTypeID clears the value of the "group_type_id" field.
func (gu *GroupUpdate) ClearGroupTypeID() *GroupUpdate {
	gu.mutation.ClearGroupTypeID()
	return gu
}

// SetName sets the "name" field.
func (gu *GroupUpdate) SetName(s string) *GroupUpdate {
	gu.mutation.SetName(s)
	return gu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (gu *GroupUpdate) SetNillableName(s *string) *GroupUpdate {
	if s != nil {
		gu.SetName(*s)
	}
	return gu
}

// SetCode sets the "code" field.
func (gu *GroupUpdate) SetCode(s string) *GroupUpdate {
	gu.mutation.SetCode(s)
	return gu
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (gu *GroupUpdate) SetNillableCode(s *string) *GroupUpdate {
	if s != nil {
		gu.SetCode(*s)
	}
	return gu
}

// SetRemark sets the "remark" field.
func (gu *GroupUpdate) SetRemark(s string) *GroupUpdate {
	gu.mutation.SetRemark(s)
	return gu
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (gu *GroupUpdate) SetNillableRemark(s *string) *GroupUpdate {
	if s != nil {
		gu.SetRemark(*s)
	}
	return gu
}

// ClearRemark clears the value of the "remark" field.
func (gu *GroupUpdate) ClearRemark() *GroupUpdate {
	gu.mutation.ClearRemark()
	return gu
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (gu *GroupUpdate) SetTenant(t *Tenant) *GroupUpdate {
	return gu.SetTenantID(t.ID)
}

// AddUserIDs adds the "users" edge to the User entity by IDs.
func (gu *GroupUpdate) AddUserIDs(ids ...string) *GroupUpdate {
	gu.mutation.AddUserIDs(ids...)
	return gu
}

// AddUsers adds the "users" edges to the User entity.
func (gu *GroupUpdate) AddUsers(u ...*User) *GroupUpdate {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return gu.AddUserIDs(ids...)
}

// AddRoleIDs adds the "roles" edge to the Role entity by IDs.
func (gu *GroupUpdate) AddRoleIDs(ids ...string) *GroupUpdate {
	gu.mutation.AddRoleIDs(ids...)
	return gu
}

// AddRoles adds the "roles" edges to the Role entity.
func (gu *GroupUpdate) AddRoles(r ...*Role) *GroupUpdate {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return gu.AddRoleIDs(ids...)
}

// SetGroupType sets the "group_type" edge to the GroupType entity.
func (gu *GroupUpdate) SetGroupType(g *GroupType) *GroupUpdate {
	return gu.SetGroupTypeID(g.ID)
}

// Mutation returns the GroupMutation object of the builder.
func (gu *GroupUpdate) Mutation() *GroupMutation {
	return gu.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (gu *GroupUpdate) ClearTenant() *GroupUpdate {
	gu.mutation.ClearTenant()
	return gu
}

// ClearUsers clears all "users" edges to the User entity.
func (gu *GroupUpdate) ClearUsers() *GroupUpdate {
	gu.mutation.ClearUsers()
	return gu
}

// RemoveUserIDs removes the "users" edge to User entities by IDs.
func (gu *GroupUpdate) RemoveUserIDs(ids ...string) *GroupUpdate {
	gu.mutation.RemoveUserIDs(ids...)
	return gu
}

// RemoveUsers removes "users" edges to User entities.
func (gu *GroupUpdate) RemoveUsers(u ...*User) *GroupUpdate {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return gu.RemoveUserIDs(ids...)
}

// ClearRoles clears all "roles" edges to the Role entity.
func (gu *GroupUpdate) ClearRoles() *GroupUpdate {
	gu.mutation.ClearRoles()
	return gu
}

// RemoveRoleIDs removes the "roles" edge to Role entities by IDs.
func (gu *GroupUpdate) RemoveRoleIDs(ids ...string) *GroupUpdate {
	gu.mutation.RemoveRoleIDs(ids...)
	return gu
}

// RemoveRoles removes "roles" edges to Role entities.
func (gu *GroupUpdate) RemoveRoles(r ...*Role) *GroupUpdate {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return gu.RemoveRoleIDs(ids...)
}

// ClearGroupType clears the "group_type" edge to the GroupType entity.
func (gu *GroupUpdate) ClearGroupType() *GroupUpdate {
	gu.mutation.ClearGroupType()
	return gu
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (gu *GroupUpdate) Save(ctx context.Context) (int, error) {
	if err := gu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, gu.sqlSave, gu.mutation, gu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (gu *GroupUpdate) SaveX(ctx context.Context) int {
	affected, err := gu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (gu *GroupUpdate) Exec(ctx context.Context) error {
	_, err := gu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (gu *GroupUpdate) ExecX(ctx context.Context) {
	if err := gu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (gu *GroupUpdate) defaults() error {
	if _, ok := gu.mutation.UpdatedAt(); !ok {
		if group.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized group.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := group.UpdateDefaultUpdatedAt()
		gu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (gu *GroupUpdate) check() error {
	if _, ok := gu.mutation.TenantID(); gu.mutation.TenantCleared() && !ok {
		return errors.New(`ent: clearing a required unique edge "Group.tenant"`)
	}
	return nil
}

func (gu *GroupUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := gu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(group.Table, group.Columns, sqlgraph.NewFieldSpec(group.FieldID, field.TypeString))
	if ps := gu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := gu.mutation.UpdatedAt(); ok {
		_spec.SetField(group.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := gu.mutation.Status(); ok {
		_spec.SetField(group.FieldStatus, field.TypeBool, value)
	}
	if gu.mutation.StatusCleared() {
		_spec.ClearField(group.FieldStatus, field.TypeBool)
	}
	if value, ok := gu.mutation.Sort(); ok {
		_spec.SetField(group.FieldSort, field.TypeUint32, value)
	}
	if value, ok := gu.mutation.AddedSort(); ok {
		_spec.AddField(group.FieldSort, field.TypeUint32, value)
	}
	if value, ok := gu.mutation.DeletedAt(); ok {
		_spec.SetField(group.FieldDeletedAt, field.TypeTime, value)
	}
	if gu.mutation.DeletedAtCleared() {
		_spec.ClearField(group.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := gu.mutation.Name(); ok {
		_spec.SetField(group.FieldName, field.TypeString, value)
	}
	if value, ok := gu.mutation.Code(); ok {
		_spec.SetField(group.FieldCode, field.TypeString, value)
	}
	if value, ok := gu.mutation.Remark(); ok {
		_spec.SetField(group.FieldRemark, field.TypeString, value)
	}
	if gu.mutation.RemarkCleared() {
		_spec.ClearField(group.FieldRemark, field.TypeString)
	}
	if gu.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   group.TenantTable,
			Columns: []string{group.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := gu.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   group.TenantTable,
			Columns: []string{group.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if gu.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   group.UsersTable,
			Columns: group.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := gu.mutation.RemovedUsersIDs(); len(nodes) > 0 && !gu.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   group.UsersTable,
			Columns: group.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := gu.mutation.UsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   group.UsersTable,
			Columns: group.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if gu.mutation.RolesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   group.RolesTable,
			Columns: group.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := gu.mutation.RemovedRolesIDs(); len(nodes) > 0 && !gu.mutation.RolesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   group.RolesTable,
			Columns: group.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := gu.mutation.RolesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   group.RolesTable,
			Columns: group.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if gu.mutation.GroupTypeCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   group.GroupTypeTable,
			Columns: []string{group.GroupTypeColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(grouptype.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := gu.mutation.GroupTypeIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   group.GroupTypeTable,
			Columns: []string{group.GroupTypeColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(grouptype.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, gu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{group.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	gu.mutation.done = true
	return n, nil
}

// GroupUpdateOne is the builder for updating a single Group entity.
type GroupUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *GroupMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (guo *GroupUpdateOne) SetUpdatedAt(t time.Time) *GroupUpdateOne {
	guo.mutation.SetUpdatedAt(t)
	return guo
}

// SetStatus sets the "status" field.
func (guo *GroupUpdateOne) SetStatus(b bool) *GroupUpdateOne {
	guo.mutation.SetStatus(b)
	return guo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (guo *GroupUpdateOne) SetNillableStatus(b *bool) *GroupUpdateOne {
	if b != nil {
		guo.SetStatus(*b)
	}
	return guo
}

// ClearStatus clears the value of the "status" field.
func (guo *GroupUpdateOne) ClearStatus() *GroupUpdateOne {
	guo.mutation.ClearStatus()
	return guo
}

// SetSort sets the "sort" field.
func (guo *GroupUpdateOne) SetSort(u uint32) *GroupUpdateOne {
	guo.mutation.ResetSort()
	guo.mutation.SetSort(u)
	return guo
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (guo *GroupUpdateOne) SetNillableSort(u *uint32) *GroupUpdateOne {
	if u != nil {
		guo.SetSort(*u)
	}
	return guo
}

// AddSort adds u to the "sort" field.
func (guo *GroupUpdateOne) AddSort(u int32) *GroupUpdateOne {
	guo.mutation.AddSort(u)
	return guo
}

// SetTenantID sets the "tenant_id" field.
func (guo *GroupUpdateOne) SetTenantID(s string) *GroupUpdateOne {
	guo.mutation.SetTenantID(s)
	return guo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (guo *GroupUpdateOne) SetNillableTenantID(s *string) *GroupUpdateOne {
	if s != nil {
		guo.SetTenantID(*s)
	}
	return guo
}

// SetDeletedAt sets the "deleted_at" field.
func (guo *GroupUpdateOne) SetDeletedAt(t time.Time) *GroupUpdateOne {
	guo.mutation.SetDeletedAt(t)
	return guo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (guo *GroupUpdateOne) SetNillableDeletedAt(t *time.Time) *GroupUpdateOne {
	if t != nil {
		guo.SetDeletedAt(*t)
	}
	return guo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (guo *GroupUpdateOne) ClearDeletedAt() *GroupUpdateOne {
	guo.mutation.ClearDeletedAt()
	return guo
}

// SetGroupTypeID sets the "group_type_id" field.
func (guo *GroupUpdateOne) SetGroupTypeID(s string) *GroupUpdateOne {
	guo.mutation.SetGroupTypeID(s)
	return guo
}

// SetNillableGroupTypeID sets the "group_type_id" field if the given value is not nil.
func (guo *GroupUpdateOne) SetNillableGroupTypeID(s *string) *GroupUpdateOne {
	if s != nil {
		guo.SetGroupTypeID(*s)
	}
	return guo
}

// ClearGroupTypeID clears the value of the "group_type_id" field.
func (guo *GroupUpdateOne) ClearGroupTypeID() *GroupUpdateOne {
	guo.mutation.ClearGroupTypeID()
	return guo
}

// SetName sets the "name" field.
func (guo *GroupUpdateOne) SetName(s string) *GroupUpdateOne {
	guo.mutation.SetName(s)
	return guo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (guo *GroupUpdateOne) SetNillableName(s *string) *GroupUpdateOne {
	if s != nil {
		guo.SetName(*s)
	}
	return guo
}

// SetCode sets the "code" field.
func (guo *GroupUpdateOne) SetCode(s string) *GroupUpdateOne {
	guo.mutation.SetCode(s)
	return guo
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (guo *GroupUpdateOne) SetNillableCode(s *string) *GroupUpdateOne {
	if s != nil {
		guo.SetCode(*s)
	}
	return guo
}

// SetRemark sets the "remark" field.
func (guo *GroupUpdateOne) SetRemark(s string) *GroupUpdateOne {
	guo.mutation.SetRemark(s)
	return guo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (guo *GroupUpdateOne) SetNillableRemark(s *string) *GroupUpdateOne {
	if s != nil {
		guo.SetRemark(*s)
	}
	return guo
}

// ClearRemark clears the value of the "remark" field.
func (guo *GroupUpdateOne) ClearRemark() *GroupUpdateOne {
	guo.mutation.ClearRemark()
	return guo
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (guo *GroupUpdateOne) SetTenant(t *Tenant) *GroupUpdateOne {
	return guo.SetTenantID(t.ID)
}

// AddUserIDs adds the "users" edge to the User entity by IDs.
func (guo *GroupUpdateOne) AddUserIDs(ids ...string) *GroupUpdateOne {
	guo.mutation.AddUserIDs(ids...)
	return guo
}

// AddUsers adds the "users" edges to the User entity.
func (guo *GroupUpdateOne) AddUsers(u ...*User) *GroupUpdateOne {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return guo.AddUserIDs(ids...)
}

// AddRoleIDs adds the "roles" edge to the Role entity by IDs.
func (guo *GroupUpdateOne) AddRoleIDs(ids ...string) *GroupUpdateOne {
	guo.mutation.AddRoleIDs(ids...)
	return guo
}

// AddRoles adds the "roles" edges to the Role entity.
func (guo *GroupUpdateOne) AddRoles(r ...*Role) *GroupUpdateOne {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return guo.AddRoleIDs(ids...)
}

// SetGroupType sets the "group_type" edge to the GroupType entity.
func (guo *GroupUpdateOne) SetGroupType(g *GroupType) *GroupUpdateOne {
	return guo.SetGroupTypeID(g.ID)
}

// Mutation returns the GroupMutation object of the builder.
func (guo *GroupUpdateOne) Mutation() *GroupMutation {
	return guo.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (guo *GroupUpdateOne) ClearTenant() *GroupUpdateOne {
	guo.mutation.ClearTenant()
	return guo
}

// ClearUsers clears all "users" edges to the User entity.
func (guo *GroupUpdateOne) ClearUsers() *GroupUpdateOne {
	guo.mutation.ClearUsers()
	return guo
}

// RemoveUserIDs removes the "users" edge to User entities by IDs.
func (guo *GroupUpdateOne) RemoveUserIDs(ids ...string) *GroupUpdateOne {
	guo.mutation.RemoveUserIDs(ids...)
	return guo
}

// RemoveUsers removes "users" edges to User entities.
func (guo *GroupUpdateOne) RemoveUsers(u ...*User) *GroupUpdateOne {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return guo.RemoveUserIDs(ids...)
}

// ClearRoles clears all "roles" edges to the Role entity.
func (guo *GroupUpdateOne) ClearRoles() *GroupUpdateOne {
	guo.mutation.ClearRoles()
	return guo
}

// RemoveRoleIDs removes the "roles" edge to Role entities by IDs.
func (guo *GroupUpdateOne) RemoveRoleIDs(ids ...string) *GroupUpdateOne {
	guo.mutation.RemoveRoleIDs(ids...)
	return guo
}

// RemoveRoles removes "roles" edges to Role entities.
func (guo *GroupUpdateOne) RemoveRoles(r ...*Role) *GroupUpdateOne {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return guo.RemoveRoleIDs(ids...)
}

// ClearGroupType clears the "group_type" edge to the GroupType entity.
func (guo *GroupUpdateOne) ClearGroupType() *GroupUpdateOne {
	guo.mutation.ClearGroupType()
	return guo
}

// Where appends a list predicates to the GroupUpdate builder.
func (guo *GroupUpdateOne) Where(ps ...predicate.Group) *GroupUpdateOne {
	guo.mutation.Where(ps...)
	return guo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (guo *GroupUpdateOne) Select(field string, fields ...string) *GroupUpdateOne {
	guo.fields = append([]string{field}, fields...)
	return guo
}

// Save executes the query and returns the updated Group entity.
func (guo *GroupUpdateOne) Save(ctx context.Context) (*Group, error) {
	if err := guo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, guo.sqlSave, guo.mutation, guo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (guo *GroupUpdateOne) SaveX(ctx context.Context) *Group {
	node, err := guo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (guo *GroupUpdateOne) Exec(ctx context.Context) error {
	_, err := guo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (guo *GroupUpdateOne) ExecX(ctx context.Context) {
	if err := guo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (guo *GroupUpdateOne) defaults() error {
	if _, ok := guo.mutation.UpdatedAt(); !ok {
		if group.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized group.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := group.UpdateDefaultUpdatedAt()
		guo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (guo *GroupUpdateOne) check() error {
	if _, ok := guo.mutation.TenantID(); guo.mutation.TenantCleared() && !ok {
		return errors.New(`ent: clearing a required unique edge "Group.tenant"`)
	}
	return nil
}

func (guo *GroupUpdateOne) sqlSave(ctx context.Context) (_node *Group, err error) {
	if err := guo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(group.Table, group.Columns, sqlgraph.NewFieldSpec(group.FieldID, field.TypeString))
	id, ok := guo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Group.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := guo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, group.FieldID)
		for _, f := range fields {
			if !group.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != group.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := guo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := guo.mutation.UpdatedAt(); ok {
		_spec.SetField(group.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := guo.mutation.Status(); ok {
		_spec.SetField(group.FieldStatus, field.TypeBool, value)
	}
	if guo.mutation.StatusCleared() {
		_spec.ClearField(group.FieldStatus, field.TypeBool)
	}
	if value, ok := guo.mutation.Sort(); ok {
		_spec.SetField(group.FieldSort, field.TypeUint32, value)
	}
	if value, ok := guo.mutation.AddedSort(); ok {
		_spec.AddField(group.FieldSort, field.TypeUint32, value)
	}
	if value, ok := guo.mutation.DeletedAt(); ok {
		_spec.SetField(group.FieldDeletedAt, field.TypeTime, value)
	}
	if guo.mutation.DeletedAtCleared() {
		_spec.ClearField(group.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := guo.mutation.Name(); ok {
		_spec.SetField(group.FieldName, field.TypeString, value)
	}
	if value, ok := guo.mutation.Code(); ok {
		_spec.SetField(group.FieldCode, field.TypeString, value)
	}
	if value, ok := guo.mutation.Remark(); ok {
		_spec.SetField(group.FieldRemark, field.TypeString, value)
	}
	if guo.mutation.RemarkCleared() {
		_spec.ClearField(group.FieldRemark, field.TypeString)
	}
	if guo.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   group.TenantTable,
			Columns: []string{group.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := guo.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   group.TenantTable,
			Columns: []string{group.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if guo.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   group.UsersTable,
			Columns: group.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := guo.mutation.RemovedUsersIDs(); len(nodes) > 0 && !guo.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   group.UsersTable,
			Columns: group.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := guo.mutation.UsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   group.UsersTable,
			Columns: group.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if guo.mutation.RolesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   group.RolesTable,
			Columns: group.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := guo.mutation.RemovedRolesIDs(); len(nodes) > 0 && !guo.mutation.RolesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   group.RolesTable,
			Columns: group.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := guo.mutation.RolesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   group.RolesTable,
			Columns: group.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if guo.mutation.GroupTypeCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   group.GroupTypeTable,
			Columns: []string{group.GroupTypeColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(grouptype.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := guo.mutation.GroupTypeIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   group.GroupTypeTable,
			Columns: []string{group.GroupTypeColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(grouptype.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Group{config: guo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, guo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{group.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	guo.mutation.done = true
	return _node, nil
}
