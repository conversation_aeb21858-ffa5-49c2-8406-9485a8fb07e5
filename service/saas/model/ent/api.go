// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"phoenix/service/saas/model/ent/api"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
)

// API is the model entity for the API schema.
type API struct {
	config `json:"-"`
	// ID of the ent.
	// Snowflake ID | 全局唯一ID
	ID string `json:"id,omitempty"`
	// Created Time | 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// Updated Time | 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// status true normal false ban | 状态  正常/禁用
	Status bool `json:"status,omitempty"`
	// Deleted Time | 删除时间（软删除标识）
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// API path | API 路径
	Path string `json:"path,omitempty"`
	// API description | API 描述
	Description string `json:"description,omitempty"`
	// API group | API 分组
	APIGroup string `json:"api_group,omitempty"`
	// HTTP method | HTTP 请求类型
	Method string `json:"method,omitempty"`
	// API kind | 操作类型 如（新增、修改、查询、删除）
	Kind string `json:"kind,omitempty"`
	// API module | 操作模块
	Module string `json:"module,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the APIQuery when eager-loading is set.
	Edges        APIEdges `json:"edges"`
	tenant_apis  *string
	selectValues sql.SelectValues
}

// APIEdges holds the relations/edges for other nodes in the graph.
type APIEdges struct {
	// Roles holds the value of the roles edge.
	Roles []*Role `json:"roles,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [1]bool
}

// RolesOrErr returns the Roles value or an error if the edge
// was not loaded in eager-loading.
func (e APIEdges) RolesOrErr() ([]*Role, error) {
	if e.loadedTypes[0] {
		return e.Roles, nil
	}
	return nil, &NotLoadedError{edge: "roles"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*API) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case api.FieldStatus:
			values[i] = new(sql.NullBool)
		case api.FieldID, api.FieldPath, api.FieldDescription, api.FieldAPIGroup, api.FieldMethod, api.FieldKind, api.FieldModule:
			values[i] = new(sql.NullString)
		case api.FieldCreatedAt, api.FieldUpdatedAt, api.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		case api.ForeignKeys[0]: // tenant_apis
			values[i] = new(sql.NullString)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the API fields.
func (a *API) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case api.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				a.ID = value.String
			}
		case api.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				a.CreatedAt = value.Time
			}
		case api.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				a.UpdatedAt = value.Time
			}
		case api.FieldStatus:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				a.Status = value.Bool
			}
		case api.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				a.DeletedAt = value.Time
			}
		case api.FieldPath:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field path", values[i])
			} else if value.Valid {
				a.Path = value.String
			}
		case api.FieldDescription:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field description", values[i])
			} else if value.Valid {
				a.Description = value.String
			}
		case api.FieldAPIGroup:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field api_group", values[i])
			} else if value.Valid {
				a.APIGroup = value.String
			}
		case api.FieldMethod:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field method", values[i])
			} else if value.Valid {
				a.Method = value.String
			}
		case api.FieldKind:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field kind", values[i])
			} else if value.Valid {
				a.Kind = value.String
			}
		case api.FieldModule:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field module", values[i])
			} else if value.Valid {
				a.Module = value.String
			}
		case api.ForeignKeys[0]:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_apis", values[i])
			} else if value.Valid {
				a.tenant_apis = new(string)
				*a.tenant_apis = value.String
			}
		default:
			a.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the API.
// This includes values selected through modifiers, order, etc.
func (a *API) Value(name string) (ent.Value, error) {
	return a.selectValues.Get(name)
}

// QueryRoles queries the "roles" edge of the API entity.
func (a *API) QueryRoles() *RoleQuery {
	return NewAPIClient(a.config).QueryRoles(a)
}

// Update returns a builder for updating this API.
// Note that you need to call API.Unwrap() before calling this method if this API
// was returned from a transaction, and the transaction was committed or rolled back.
func (a *API) Update() *APIUpdateOne {
	return NewAPIClient(a.config).UpdateOne(a)
}

// Unwrap unwraps the API entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (a *API) Unwrap() *API {
	_tx, ok := a.config.driver.(*txDriver)
	if !ok {
		panic("ent: API is not a transactional entity")
	}
	a.config.driver = _tx.drv
	return a
}

// String implements the fmt.Stringer.
func (a *API) String() string {
	var builder strings.Builder
	builder.WriteString("API(")
	builder.WriteString(fmt.Sprintf("id=%v, ", a.ID))
	builder.WriteString("created_at=")
	builder.WriteString(a.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(a.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", a.Status))
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(a.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("path=")
	builder.WriteString(a.Path)
	builder.WriteString(", ")
	builder.WriteString("description=")
	builder.WriteString(a.Description)
	builder.WriteString(", ")
	builder.WriteString("api_group=")
	builder.WriteString(a.APIGroup)
	builder.WriteString(", ")
	builder.WriteString("method=")
	builder.WriteString(a.Method)
	builder.WriteString(", ")
	builder.WriteString("kind=")
	builder.WriteString(a.Kind)
	builder.WriteString(", ")
	builder.WriteString("module=")
	builder.WriteString(a.Module)
	builder.WriteByte(')')
	return builder.String()
}

// APIs is a parsable slice of API.
type APIs []*API
