// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/button"
	"phoenix/service/saas/model/ent/menu"
	"phoenix/service/saas/model/ent/predicate"
	"phoenix/service/saas/model/ent/role"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// MenuUpdate is the builder for updating Menu entities.
type MenuUpdate struct {
	config
	hooks    []Hook
	mutation *MenuMutation
}

// Where appends a list predicates to the MenuUpdate builder.
func (mu *MenuUpdate) Where(ps ...predicate.Menu) *MenuUpdate {
	mu.mutation.Where(ps...)
	return mu
}

// SetUpdatedAt sets the "updated_at" field.
func (mu *MenuUpdate) SetUpdatedAt(t time.Time) *MenuUpdate {
	mu.mutation.SetUpdatedAt(t)
	return mu
}

// SetSort sets the "sort" field.
func (mu *MenuUpdate) SetSort(u uint32) *MenuUpdate {
	mu.mutation.ResetSort()
	mu.mutation.SetSort(u)
	return mu
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (mu *MenuUpdate) SetNillableSort(u *uint32) *MenuUpdate {
	if u != nil {
		mu.SetSort(*u)
	}
	return mu
}

// AddSort adds u to the "sort" field.
func (mu *MenuUpdate) AddSort(u int32) *MenuUpdate {
	mu.mutation.AddSort(u)
	return mu
}

// SetDeletedAt sets the "deleted_at" field.
func (mu *MenuUpdate) SetDeletedAt(t time.Time) *MenuUpdate {
	mu.mutation.SetDeletedAt(t)
	return mu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (mu *MenuUpdate) SetNillableDeletedAt(t *time.Time) *MenuUpdate {
	if t != nil {
		mu.SetDeletedAt(*t)
	}
	return mu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (mu *MenuUpdate) ClearDeletedAt() *MenuUpdate {
	mu.mutation.ClearDeletedAt()
	return mu
}

// SetName sets the "name" field.
func (mu *MenuUpdate) SetName(s string) *MenuUpdate {
	mu.mutation.SetName(s)
	return mu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (mu *MenuUpdate) SetNillableName(s *string) *MenuUpdate {
	if s != nil {
		mu.SetName(*s)
	}
	return mu
}

// SetTitle sets the "title" field.
func (mu *MenuUpdate) SetTitle(s string) *MenuUpdate {
	mu.mutation.SetTitle(s)
	return mu
}

// SetNillableTitle sets the "title" field if the given value is not nil.
func (mu *MenuUpdate) SetNillableTitle(s *string) *MenuUpdate {
	if s != nil {
		mu.SetTitle(*s)
	}
	return mu
}

// SetIcon sets the "icon" field.
func (mu *MenuUpdate) SetIcon(s string) *MenuUpdate {
	mu.mutation.SetIcon(s)
	return mu
}

// SetNillableIcon sets the "icon" field if the given value is not nil.
func (mu *MenuUpdate) SetNillableIcon(s *string) *MenuUpdate {
	if s != nil {
		mu.SetIcon(*s)
	}
	return mu
}

// SetParentID sets the "parent_id" field.
func (mu *MenuUpdate) SetParentID(s string) *MenuUpdate {
	mu.mutation.SetParentID(s)
	return mu
}

// SetNillableParentID sets the "parent_id" field if the given value is not nil.
func (mu *MenuUpdate) SetNillableParentID(s *string) *MenuUpdate {
	if s != nil {
		mu.SetParentID(*s)
	}
	return mu
}

// ClearParentID clears the value of the "parent_id" field.
func (mu *MenuUpdate) ClearParentID() *MenuUpdate {
	mu.mutation.ClearParentID()
	return mu
}

// SetMenuType sets the "menu_type" field.
func (mu *MenuUpdate) SetMenuType(u uint32) *MenuUpdate {
	mu.mutation.ResetMenuType()
	mu.mutation.SetMenuType(u)
	return mu
}

// SetNillableMenuType sets the "menu_type" field if the given value is not nil.
func (mu *MenuUpdate) SetNillableMenuType(u *uint32) *MenuUpdate {
	if u != nil {
		mu.SetMenuType(*u)
	}
	return mu
}

// AddMenuType adds u to the "menu_type" field.
func (mu *MenuUpdate) AddMenuType(u int32) *MenuUpdate {
	mu.mutation.AddMenuType(u)
	return mu
}

// SetURL sets the "url" field.
func (mu *MenuUpdate) SetURL(s string) *MenuUpdate {
	mu.mutation.SetURL(s)
	return mu
}

// SetNillableURL sets the "url" field if the given value is not nil.
func (mu *MenuUpdate) SetNillableURL(s *string) *MenuUpdate {
	if s != nil {
		mu.SetURL(*s)
	}
	return mu
}

// ClearURL clears the value of the "url" field.
func (mu *MenuUpdate) ClearURL() *MenuUpdate {
	mu.mutation.ClearURL()
	return mu
}

// SetRedirect sets the "redirect" field.
func (mu *MenuUpdate) SetRedirect(s string) *MenuUpdate {
	mu.mutation.SetRedirect(s)
	return mu
}

// SetNillableRedirect sets the "redirect" field if the given value is not nil.
func (mu *MenuUpdate) SetNillableRedirect(s *string) *MenuUpdate {
	if s != nil {
		mu.SetRedirect(*s)
	}
	return mu
}

// ClearRedirect clears the value of the "redirect" field.
func (mu *MenuUpdate) ClearRedirect() *MenuUpdate {
	mu.mutation.ClearRedirect()
	return mu
}

// SetComponent sets the "component" field.
func (mu *MenuUpdate) SetComponent(s string) *MenuUpdate {
	mu.mutation.SetComponent(s)
	return mu
}

// SetNillableComponent sets the "component" field if the given value is not nil.
func (mu *MenuUpdate) SetNillableComponent(s *string) *MenuUpdate {
	if s != nil {
		mu.SetComponent(*s)
	}
	return mu
}

// ClearComponent clears the value of the "component" field.
func (mu *MenuUpdate) ClearComponent() *MenuUpdate {
	mu.mutation.ClearComponent()
	return mu
}

// SetIsActive sets the "is_active" field.
func (mu *MenuUpdate) SetIsActive(b bool) *MenuUpdate {
	mu.mutation.SetIsActive(b)
	return mu
}

// SetNillableIsActive sets the "is_active" field if the given value is not nil.
func (mu *MenuUpdate) SetNillableIsActive(b *bool) *MenuUpdate {
	if b != nil {
		mu.SetIsActive(*b)
	}
	return mu
}

// ClearIsActive clears the value of the "is_active" field.
func (mu *MenuUpdate) ClearIsActive() *MenuUpdate {
	mu.mutation.ClearIsActive()
	return mu
}

// SetHidden sets the "hidden" field.
func (mu *MenuUpdate) SetHidden(b bool) *MenuUpdate {
	mu.mutation.SetHidden(b)
	return mu
}

// SetNillableHidden sets the "hidden" field if the given value is not nil.
func (mu *MenuUpdate) SetNillableHidden(b *bool) *MenuUpdate {
	if b != nil {
		mu.SetHidden(*b)
	}
	return mu
}

// ClearHidden clears the value of the "hidden" field.
func (mu *MenuUpdate) ClearHidden() *MenuUpdate {
	mu.mutation.ClearHidden()
	return mu
}

// SetHiddenInTab sets the "hidden_in_tab" field.
func (mu *MenuUpdate) SetHiddenInTab(b bool) *MenuUpdate {
	mu.mutation.SetHiddenInTab(b)
	return mu
}

// SetNillableHiddenInTab sets the "hidden_in_tab" field if the given value is not nil.
func (mu *MenuUpdate) SetNillableHiddenInTab(b *bool) *MenuUpdate {
	if b != nil {
		mu.SetHiddenInTab(*b)
	}
	return mu
}

// ClearHiddenInTab clears the value of the "hidden_in_tab" field.
func (mu *MenuUpdate) ClearHiddenInTab() *MenuUpdate {
	mu.mutation.ClearHiddenInTab()
	return mu
}

// SetFixed sets the "fixed" field.
func (mu *MenuUpdate) SetFixed(b bool) *MenuUpdate {
	mu.mutation.SetFixed(b)
	return mu
}

// SetNillableFixed sets the "fixed" field if the given value is not nil.
func (mu *MenuUpdate) SetNillableFixed(b *bool) *MenuUpdate {
	if b != nil {
		mu.SetFixed(*b)
	}
	return mu
}

// ClearFixed clears the value of the "fixed" field.
func (mu *MenuUpdate) ClearFixed() *MenuUpdate {
	mu.mutation.ClearFixed()
	return mu
}

// SetRemark sets the "remark" field.
func (mu *MenuUpdate) SetRemark(s string) *MenuUpdate {
	mu.mutation.SetRemark(s)
	return mu
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (mu *MenuUpdate) SetNillableRemark(s *string) *MenuUpdate {
	if s != nil {
		mu.SetRemark(*s)
	}
	return mu
}

// ClearRemark clears the value of the "remark" field.
func (mu *MenuUpdate) ClearRemark() *MenuUpdate {
	mu.mutation.ClearRemark()
	return mu
}

// SetMeta sets the "meta" field.
func (mu *MenuUpdate) SetMeta(s string) *MenuUpdate {
	mu.mutation.SetMeta(s)
	return mu
}

// SetNillableMeta sets the "meta" field if the given value is not nil.
func (mu *MenuUpdate) SetNillableMeta(s *string) *MenuUpdate {
	if s != nil {
		mu.SetMeta(*s)
	}
	return mu
}

// ClearMeta clears the value of the "meta" field.
func (mu *MenuUpdate) ClearMeta() *MenuUpdate {
	mu.mutation.ClearMeta()
	return mu
}

// SetIsFullPage sets the "is_full_page" field.
func (mu *MenuUpdate) SetIsFullPage(b bool) *MenuUpdate {
	mu.mutation.SetIsFullPage(b)
	return mu
}

// SetNillableIsFullPage sets the "is_full_page" field if the given value is not nil.
func (mu *MenuUpdate) SetNillableIsFullPage(b *bool) *MenuUpdate {
	if b != nil {
		mu.SetIsFullPage(*b)
	}
	return mu
}

// ClearIsFullPage clears the value of the "is_full_page" field.
func (mu *MenuUpdate) ClearIsFullPage() *MenuUpdate {
	mu.mutation.ClearIsFullPage()
	return mu
}

// AddRoleIDs adds the "roles" edge to the Role entity by IDs.
func (mu *MenuUpdate) AddRoleIDs(ids ...string) *MenuUpdate {
	mu.mutation.AddRoleIDs(ids...)
	return mu
}

// AddRoles adds the "roles" edges to the Role entity.
func (mu *MenuUpdate) AddRoles(r ...*Role) *MenuUpdate {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return mu.AddRoleIDs(ids...)
}

// AddButtonIDs adds the "buttons" edge to the Button entity by IDs.
func (mu *MenuUpdate) AddButtonIDs(ids ...string) *MenuUpdate {
	mu.mutation.AddButtonIDs(ids...)
	return mu
}

// AddButtons adds the "buttons" edges to the Button entity.
func (mu *MenuUpdate) AddButtons(b ...*Button) *MenuUpdate {
	ids := make([]string, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return mu.AddButtonIDs(ids...)
}

// SetParent sets the "parent" edge to the Menu entity.
func (mu *MenuUpdate) SetParent(m *Menu) *MenuUpdate {
	return mu.SetParentID(m.ID)
}

// AddChildIDs adds the "children" edge to the Menu entity by IDs.
func (mu *MenuUpdate) AddChildIDs(ids ...string) *MenuUpdate {
	mu.mutation.AddChildIDs(ids...)
	return mu
}

// AddChildren adds the "children" edges to the Menu entity.
func (mu *MenuUpdate) AddChildren(m ...*Menu) *MenuUpdate {
	ids := make([]string, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return mu.AddChildIDs(ids...)
}

// Mutation returns the MenuMutation object of the builder.
func (mu *MenuUpdate) Mutation() *MenuMutation {
	return mu.mutation
}

// ClearRoles clears all "roles" edges to the Role entity.
func (mu *MenuUpdate) ClearRoles() *MenuUpdate {
	mu.mutation.ClearRoles()
	return mu
}

// RemoveRoleIDs removes the "roles" edge to Role entities by IDs.
func (mu *MenuUpdate) RemoveRoleIDs(ids ...string) *MenuUpdate {
	mu.mutation.RemoveRoleIDs(ids...)
	return mu
}

// RemoveRoles removes "roles" edges to Role entities.
func (mu *MenuUpdate) RemoveRoles(r ...*Role) *MenuUpdate {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return mu.RemoveRoleIDs(ids...)
}

// ClearButtons clears all "buttons" edges to the Button entity.
func (mu *MenuUpdate) ClearButtons() *MenuUpdate {
	mu.mutation.ClearButtons()
	return mu
}

// RemoveButtonIDs removes the "buttons" edge to Button entities by IDs.
func (mu *MenuUpdate) RemoveButtonIDs(ids ...string) *MenuUpdate {
	mu.mutation.RemoveButtonIDs(ids...)
	return mu
}

// RemoveButtons removes "buttons" edges to Button entities.
func (mu *MenuUpdate) RemoveButtons(b ...*Button) *MenuUpdate {
	ids := make([]string, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return mu.RemoveButtonIDs(ids...)
}

// ClearParent clears the "parent" edge to the Menu entity.
func (mu *MenuUpdate) ClearParent() *MenuUpdate {
	mu.mutation.ClearParent()
	return mu
}

// ClearChildren clears all "children" edges to the Menu entity.
func (mu *MenuUpdate) ClearChildren() *MenuUpdate {
	mu.mutation.ClearChildren()
	return mu
}

// RemoveChildIDs removes the "children" edge to Menu entities by IDs.
func (mu *MenuUpdate) RemoveChildIDs(ids ...string) *MenuUpdate {
	mu.mutation.RemoveChildIDs(ids...)
	return mu
}

// RemoveChildren removes "children" edges to Menu entities.
func (mu *MenuUpdate) RemoveChildren(m ...*Menu) *MenuUpdate {
	ids := make([]string, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return mu.RemoveChildIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (mu *MenuUpdate) Save(ctx context.Context) (int, error) {
	if err := mu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, mu.sqlSave, mu.mutation, mu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (mu *MenuUpdate) SaveX(ctx context.Context) int {
	affected, err := mu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (mu *MenuUpdate) Exec(ctx context.Context) error {
	_, err := mu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (mu *MenuUpdate) ExecX(ctx context.Context) {
	if err := mu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (mu *MenuUpdate) defaults() error {
	if _, ok := mu.mutation.UpdatedAt(); !ok {
		if menu.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized menu.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := menu.UpdateDefaultUpdatedAt()
		mu.mutation.SetUpdatedAt(v)
	}
	return nil
}

func (mu *MenuUpdate) sqlSave(ctx context.Context) (n int, err error) {
	_spec := sqlgraph.NewUpdateSpec(menu.Table, menu.Columns, sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString))
	if ps := mu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := mu.mutation.UpdatedAt(); ok {
		_spec.SetField(menu.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := mu.mutation.Sort(); ok {
		_spec.SetField(menu.FieldSort, field.TypeUint32, value)
	}
	if value, ok := mu.mutation.AddedSort(); ok {
		_spec.AddField(menu.FieldSort, field.TypeUint32, value)
	}
	if value, ok := mu.mutation.DeletedAt(); ok {
		_spec.SetField(menu.FieldDeletedAt, field.TypeTime, value)
	}
	if mu.mutation.DeletedAtCleared() {
		_spec.ClearField(menu.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := mu.mutation.Name(); ok {
		_spec.SetField(menu.FieldName, field.TypeString, value)
	}
	if value, ok := mu.mutation.Title(); ok {
		_spec.SetField(menu.FieldTitle, field.TypeString, value)
	}
	if value, ok := mu.mutation.Icon(); ok {
		_spec.SetField(menu.FieldIcon, field.TypeString, value)
	}
	if value, ok := mu.mutation.MenuType(); ok {
		_spec.SetField(menu.FieldMenuType, field.TypeUint32, value)
	}
	if value, ok := mu.mutation.AddedMenuType(); ok {
		_spec.AddField(menu.FieldMenuType, field.TypeUint32, value)
	}
	if value, ok := mu.mutation.URL(); ok {
		_spec.SetField(menu.FieldURL, field.TypeString, value)
	}
	if mu.mutation.URLCleared() {
		_spec.ClearField(menu.FieldURL, field.TypeString)
	}
	if value, ok := mu.mutation.Redirect(); ok {
		_spec.SetField(menu.FieldRedirect, field.TypeString, value)
	}
	if mu.mutation.RedirectCleared() {
		_spec.ClearField(menu.FieldRedirect, field.TypeString)
	}
	if value, ok := mu.mutation.Component(); ok {
		_spec.SetField(menu.FieldComponent, field.TypeString, value)
	}
	if mu.mutation.ComponentCleared() {
		_spec.ClearField(menu.FieldComponent, field.TypeString)
	}
	if value, ok := mu.mutation.IsActive(); ok {
		_spec.SetField(menu.FieldIsActive, field.TypeBool, value)
	}
	if mu.mutation.IsActiveCleared() {
		_spec.ClearField(menu.FieldIsActive, field.TypeBool)
	}
	if value, ok := mu.mutation.Hidden(); ok {
		_spec.SetField(menu.FieldHidden, field.TypeBool, value)
	}
	if mu.mutation.HiddenCleared() {
		_spec.ClearField(menu.FieldHidden, field.TypeBool)
	}
	if value, ok := mu.mutation.HiddenInTab(); ok {
		_spec.SetField(menu.FieldHiddenInTab, field.TypeBool, value)
	}
	if mu.mutation.HiddenInTabCleared() {
		_spec.ClearField(menu.FieldHiddenInTab, field.TypeBool)
	}
	if value, ok := mu.mutation.Fixed(); ok {
		_spec.SetField(menu.FieldFixed, field.TypeBool, value)
	}
	if mu.mutation.FixedCleared() {
		_spec.ClearField(menu.FieldFixed, field.TypeBool)
	}
	if value, ok := mu.mutation.Remark(); ok {
		_spec.SetField(menu.FieldRemark, field.TypeString, value)
	}
	if mu.mutation.RemarkCleared() {
		_spec.ClearField(menu.FieldRemark, field.TypeString)
	}
	if value, ok := mu.mutation.Meta(); ok {
		_spec.SetField(menu.FieldMeta, field.TypeString, value)
	}
	if mu.mutation.MetaCleared() {
		_spec.ClearField(menu.FieldMeta, field.TypeString)
	}
	if value, ok := mu.mutation.IsFullPage(); ok {
		_spec.SetField(menu.FieldIsFullPage, field.TypeBool, value)
	}
	if mu.mutation.IsFullPageCleared() {
		_spec.ClearField(menu.FieldIsFullPage, field.TypeBool)
	}
	if mu.mutation.RolesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   menu.RolesTable,
			Columns: menu.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mu.mutation.RemovedRolesIDs(); len(nodes) > 0 && !mu.mutation.RolesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   menu.RolesTable,
			Columns: menu.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mu.mutation.RolesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   menu.RolesTable,
			Columns: menu.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if mu.mutation.ButtonsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   menu.ButtonsTable,
			Columns: []string{menu.ButtonsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(button.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mu.mutation.RemovedButtonsIDs(); len(nodes) > 0 && !mu.mutation.ButtonsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   menu.ButtonsTable,
			Columns: []string{menu.ButtonsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(button.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mu.mutation.ButtonsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   menu.ButtonsTable,
			Columns: []string{menu.ButtonsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(button.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if mu.mutation.ParentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   menu.ParentTable,
			Columns: []string{menu.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mu.mutation.ParentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   menu.ParentTable,
			Columns: []string{menu.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if mu.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   menu.ChildrenTable,
			Columns: []string{menu.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mu.mutation.RemovedChildrenIDs(); len(nodes) > 0 && !mu.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   menu.ChildrenTable,
			Columns: []string{menu.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := mu.mutation.ChildrenIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   menu.ChildrenTable,
			Columns: []string{menu.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, mu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{menu.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	mu.mutation.done = true
	return n, nil
}

// MenuUpdateOne is the builder for updating a single Menu entity.
type MenuUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *MenuMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (muo *MenuUpdateOne) SetUpdatedAt(t time.Time) *MenuUpdateOne {
	muo.mutation.SetUpdatedAt(t)
	return muo
}

// SetSort sets the "sort" field.
func (muo *MenuUpdateOne) SetSort(u uint32) *MenuUpdateOne {
	muo.mutation.ResetSort()
	muo.mutation.SetSort(u)
	return muo
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (muo *MenuUpdateOne) SetNillableSort(u *uint32) *MenuUpdateOne {
	if u != nil {
		muo.SetSort(*u)
	}
	return muo
}

// AddSort adds u to the "sort" field.
func (muo *MenuUpdateOne) AddSort(u int32) *MenuUpdateOne {
	muo.mutation.AddSort(u)
	return muo
}

// SetDeletedAt sets the "deleted_at" field.
func (muo *MenuUpdateOne) SetDeletedAt(t time.Time) *MenuUpdateOne {
	muo.mutation.SetDeletedAt(t)
	return muo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (muo *MenuUpdateOne) SetNillableDeletedAt(t *time.Time) *MenuUpdateOne {
	if t != nil {
		muo.SetDeletedAt(*t)
	}
	return muo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (muo *MenuUpdateOne) ClearDeletedAt() *MenuUpdateOne {
	muo.mutation.ClearDeletedAt()
	return muo
}

// SetName sets the "name" field.
func (muo *MenuUpdateOne) SetName(s string) *MenuUpdateOne {
	muo.mutation.SetName(s)
	return muo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (muo *MenuUpdateOne) SetNillableName(s *string) *MenuUpdateOne {
	if s != nil {
		muo.SetName(*s)
	}
	return muo
}

// SetTitle sets the "title" field.
func (muo *MenuUpdateOne) SetTitle(s string) *MenuUpdateOne {
	muo.mutation.SetTitle(s)
	return muo
}

// SetNillableTitle sets the "title" field if the given value is not nil.
func (muo *MenuUpdateOne) SetNillableTitle(s *string) *MenuUpdateOne {
	if s != nil {
		muo.SetTitle(*s)
	}
	return muo
}

// SetIcon sets the "icon" field.
func (muo *MenuUpdateOne) SetIcon(s string) *MenuUpdateOne {
	muo.mutation.SetIcon(s)
	return muo
}

// SetNillableIcon sets the "icon" field if the given value is not nil.
func (muo *MenuUpdateOne) SetNillableIcon(s *string) *MenuUpdateOne {
	if s != nil {
		muo.SetIcon(*s)
	}
	return muo
}

// SetParentID sets the "parent_id" field.
func (muo *MenuUpdateOne) SetParentID(s string) *MenuUpdateOne {
	muo.mutation.SetParentID(s)
	return muo
}

// SetNillableParentID sets the "parent_id" field if the given value is not nil.
func (muo *MenuUpdateOne) SetNillableParentID(s *string) *MenuUpdateOne {
	if s != nil {
		muo.SetParentID(*s)
	}
	return muo
}

// ClearParentID clears the value of the "parent_id" field.
func (muo *MenuUpdateOne) ClearParentID() *MenuUpdateOne {
	muo.mutation.ClearParentID()
	return muo
}

// SetMenuType sets the "menu_type" field.
func (muo *MenuUpdateOne) SetMenuType(u uint32) *MenuUpdateOne {
	muo.mutation.ResetMenuType()
	muo.mutation.SetMenuType(u)
	return muo
}

// SetNillableMenuType sets the "menu_type" field if the given value is not nil.
func (muo *MenuUpdateOne) SetNillableMenuType(u *uint32) *MenuUpdateOne {
	if u != nil {
		muo.SetMenuType(*u)
	}
	return muo
}

// AddMenuType adds u to the "menu_type" field.
func (muo *MenuUpdateOne) AddMenuType(u int32) *MenuUpdateOne {
	muo.mutation.AddMenuType(u)
	return muo
}

// SetURL sets the "url" field.
func (muo *MenuUpdateOne) SetURL(s string) *MenuUpdateOne {
	muo.mutation.SetURL(s)
	return muo
}

// SetNillableURL sets the "url" field if the given value is not nil.
func (muo *MenuUpdateOne) SetNillableURL(s *string) *MenuUpdateOne {
	if s != nil {
		muo.SetURL(*s)
	}
	return muo
}

// ClearURL clears the value of the "url" field.
func (muo *MenuUpdateOne) ClearURL() *MenuUpdateOne {
	muo.mutation.ClearURL()
	return muo
}

// SetRedirect sets the "redirect" field.
func (muo *MenuUpdateOne) SetRedirect(s string) *MenuUpdateOne {
	muo.mutation.SetRedirect(s)
	return muo
}

// SetNillableRedirect sets the "redirect" field if the given value is not nil.
func (muo *MenuUpdateOne) SetNillableRedirect(s *string) *MenuUpdateOne {
	if s != nil {
		muo.SetRedirect(*s)
	}
	return muo
}

// ClearRedirect clears the value of the "redirect" field.
func (muo *MenuUpdateOne) ClearRedirect() *MenuUpdateOne {
	muo.mutation.ClearRedirect()
	return muo
}

// SetComponent sets the "component" field.
func (muo *MenuUpdateOne) SetComponent(s string) *MenuUpdateOne {
	muo.mutation.SetComponent(s)
	return muo
}

// SetNillableComponent sets the "component" field if the given value is not nil.
func (muo *MenuUpdateOne) SetNillableComponent(s *string) *MenuUpdateOne {
	if s != nil {
		muo.SetComponent(*s)
	}
	return muo
}

// ClearComponent clears the value of the "component" field.
func (muo *MenuUpdateOne) ClearComponent() *MenuUpdateOne {
	muo.mutation.ClearComponent()
	return muo
}

// SetIsActive sets the "is_active" field.
func (muo *MenuUpdateOne) SetIsActive(b bool) *MenuUpdateOne {
	muo.mutation.SetIsActive(b)
	return muo
}

// SetNillableIsActive sets the "is_active" field if the given value is not nil.
func (muo *MenuUpdateOne) SetNillableIsActive(b *bool) *MenuUpdateOne {
	if b != nil {
		muo.SetIsActive(*b)
	}
	return muo
}

// ClearIsActive clears the value of the "is_active" field.
func (muo *MenuUpdateOne) ClearIsActive() *MenuUpdateOne {
	muo.mutation.ClearIsActive()
	return muo
}

// SetHidden sets the "hidden" field.
func (muo *MenuUpdateOne) SetHidden(b bool) *MenuUpdateOne {
	muo.mutation.SetHidden(b)
	return muo
}

// SetNillableHidden sets the "hidden" field if the given value is not nil.
func (muo *MenuUpdateOne) SetNillableHidden(b *bool) *MenuUpdateOne {
	if b != nil {
		muo.SetHidden(*b)
	}
	return muo
}

// ClearHidden clears the value of the "hidden" field.
func (muo *MenuUpdateOne) ClearHidden() *MenuUpdateOne {
	muo.mutation.ClearHidden()
	return muo
}

// SetHiddenInTab sets the "hidden_in_tab" field.
func (muo *MenuUpdateOne) SetHiddenInTab(b bool) *MenuUpdateOne {
	muo.mutation.SetHiddenInTab(b)
	return muo
}

// SetNillableHiddenInTab sets the "hidden_in_tab" field if the given value is not nil.
func (muo *MenuUpdateOne) SetNillableHiddenInTab(b *bool) *MenuUpdateOne {
	if b != nil {
		muo.SetHiddenInTab(*b)
	}
	return muo
}

// ClearHiddenInTab clears the value of the "hidden_in_tab" field.
func (muo *MenuUpdateOne) ClearHiddenInTab() *MenuUpdateOne {
	muo.mutation.ClearHiddenInTab()
	return muo
}

// SetFixed sets the "fixed" field.
func (muo *MenuUpdateOne) SetFixed(b bool) *MenuUpdateOne {
	muo.mutation.SetFixed(b)
	return muo
}

// SetNillableFixed sets the "fixed" field if the given value is not nil.
func (muo *MenuUpdateOne) SetNillableFixed(b *bool) *MenuUpdateOne {
	if b != nil {
		muo.SetFixed(*b)
	}
	return muo
}

// ClearFixed clears the value of the "fixed" field.
func (muo *MenuUpdateOne) ClearFixed() *MenuUpdateOne {
	muo.mutation.ClearFixed()
	return muo
}

// SetRemark sets the "remark" field.
func (muo *MenuUpdateOne) SetRemark(s string) *MenuUpdateOne {
	muo.mutation.SetRemark(s)
	return muo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (muo *MenuUpdateOne) SetNillableRemark(s *string) *MenuUpdateOne {
	if s != nil {
		muo.SetRemark(*s)
	}
	return muo
}

// ClearRemark clears the value of the "remark" field.
func (muo *MenuUpdateOne) ClearRemark() *MenuUpdateOne {
	muo.mutation.ClearRemark()
	return muo
}

// SetMeta sets the "meta" field.
func (muo *MenuUpdateOne) SetMeta(s string) *MenuUpdateOne {
	muo.mutation.SetMeta(s)
	return muo
}

// SetNillableMeta sets the "meta" field if the given value is not nil.
func (muo *MenuUpdateOne) SetNillableMeta(s *string) *MenuUpdateOne {
	if s != nil {
		muo.SetMeta(*s)
	}
	return muo
}

// ClearMeta clears the value of the "meta" field.
func (muo *MenuUpdateOne) ClearMeta() *MenuUpdateOne {
	muo.mutation.ClearMeta()
	return muo
}

// SetIsFullPage sets the "is_full_page" field.
func (muo *MenuUpdateOne) SetIsFullPage(b bool) *MenuUpdateOne {
	muo.mutation.SetIsFullPage(b)
	return muo
}

// SetNillableIsFullPage sets the "is_full_page" field if the given value is not nil.
func (muo *MenuUpdateOne) SetNillableIsFullPage(b *bool) *MenuUpdateOne {
	if b != nil {
		muo.SetIsFullPage(*b)
	}
	return muo
}

// ClearIsFullPage clears the value of the "is_full_page" field.
func (muo *MenuUpdateOne) ClearIsFullPage() *MenuUpdateOne {
	muo.mutation.ClearIsFullPage()
	return muo
}

// AddRoleIDs adds the "roles" edge to the Role entity by IDs.
func (muo *MenuUpdateOne) AddRoleIDs(ids ...string) *MenuUpdateOne {
	muo.mutation.AddRoleIDs(ids...)
	return muo
}

// AddRoles adds the "roles" edges to the Role entity.
func (muo *MenuUpdateOne) AddRoles(r ...*Role) *MenuUpdateOne {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return muo.AddRoleIDs(ids...)
}

// AddButtonIDs adds the "buttons" edge to the Button entity by IDs.
func (muo *MenuUpdateOne) AddButtonIDs(ids ...string) *MenuUpdateOne {
	muo.mutation.AddButtonIDs(ids...)
	return muo
}

// AddButtons adds the "buttons" edges to the Button entity.
func (muo *MenuUpdateOne) AddButtons(b ...*Button) *MenuUpdateOne {
	ids := make([]string, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return muo.AddButtonIDs(ids...)
}

// SetParent sets the "parent" edge to the Menu entity.
func (muo *MenuUpdateOne) SetParent(m *Menu) *MenuUpdateOne {
	return muo.SetParentID(m.ID)
}

// AddChildIDs adds the "children" edge to the Menu entity by IDs.
func (muo *MenuUpdateOne) AddChildIDs(ids ...string) *MenuUpdateOne {
	muo.mutation.AddChildIDs(ids...)
	return muo
}

// AddChildren adds the "children" edges to the Menu entity.
func (muo *MenuUpdateOne) AddChildren(m ...*Menu) *MenuUpdateOne {
	ids := make([]string, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return muo.AddChildIDs(ids...)
}

// Mutation returns the MenuMutation object of the builder.
func (muo *MenuUpdateOne) Mutation() *MenuMutation {
	return muo.mutation
}

// ClearRoles clears all "roles" edges to the Role entity.
func (muo *MenuUpdateOne) ClearRoles() *MenuUpdateOne {
	muo.mutation.ClearRoles()
	return muo
}

// RemoveRoleIDs removes the "roles" edge to Role entities by IDs.
func (muo *MenuUpdateOne) RemoveRoleIDs(ids ...string) *MenuUpdateOne {
	muo.mutation.RemoveRoleIDs(ids...)
	return muo
}

// RemoveRoles removes "roles" edges to Role entities.
func (muo *MenuUpdateOne) RemoveRoles(r ...*Role) *MenuUpdateOne {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return muo.RemoveRoleIDs(ids...)
}

// ClearButtons clears all "buttons" edges to the Button entity.
func (muo *MenuUpdateOne) ClearButtons() *MenuUpdateOne {
	muo.mutation.ClearButtons()
	return muo
}

// RemoveButtonIDs removes the "buttons" edge to Button entities by IDs.
func (muo *MenuUpdateOne) RemoveButtonIDs(ids ...string) *MenuUpdateOne {
	muo.mutation.RemoveButtonIDs(ids...)
	return muo
}

// RemoveButtons removes "buttons" edges to Button entities.
func (muo *MenuUpdateOne) RemoveButtons(b ...*Button) *MenuUpdateOne {
	ids := make([]string, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return muo.RemoveButtonIDs(ids...)
}

// ClearParent clears the "parent" edge to the Menu entity.
func (muo *MenuUpdateOne) ClearParent() *MenuUpdateOne {
	muo.mutation.ClearParent()
	return muo
}

// ClearChildren clears all "children" edges to the Menu entity.
func (muo *MenuUpdateOne) ClearChildren() *MenuUpdateOne {
	muo.mutation.ClearChildren()
	return muo
}

// RemoveChildIDs removes the "children" edge to Menu entities by IDs.
func (muo *MenuUpdateOne) RemoveChildIDs(ids ...string) *MenuUpdateOne {
	muo.mutation.RemoveChildIDs(ids...)
	return muo
}

// RemoveChildren removes "children" edges to Menu entities.
func (muo *MenuUpdateOne) RemoveChildren(m ...*Menu) *MenuUpdateOne {
	ids := make([]string, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return muo.RemoveChildIDs(ids...)
}

// Where appends a list predicates to the MenuUpdate builder.
func (muo *MenuUpdateOne) Where(ps ...predicate.Menu) *MenuUpdateOne {
	muo.mutation.Where(ps...)
	return muo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (muo *MenuUpdateOne) Select(field string, fields ...string) *MenuUpdateOne {
	muo.fields = append([]string{field}, fields...)
	return muo
}

// Save executes the query and returns the updated Menu entity.
func (muo *MenuUpdateOne) Save(ctx context.Context) (*Menu, error) {
	if err := muo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, muo.sqlSave, muo.mutation, muo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (muo *MenuUpdateOne) SaveX(ctx context.Context) *Menu {
	node, err := muo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (muo *MenuUpdateOne) Exec(ctx context.Context) error {
	_, err := muo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (muo *MenuUpdateOne) ExecX(ctx context.Context) {
	if err := muo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (muo *MenuUpdateOne) defaults() error {
	if _, ok := muo.mutation.UpdatedAt(); !ok {
		if menu.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized menu.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := menu.UpdateDefaultUpdatedAt()
		muo.mutation.SetUpdatedAt(v)
	}
	return nil
}

func (muo *MenuUpdateOne) sqlSave(ctx context.Context) (_node *Menu, err error) {
	_spec := sqlgraph.NewUpdateSpec(menu.Table, menu.Columns, sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString))
	id, ok := muo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Menu.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := muo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, menu.FieldID)
		for _, f := range fields {
			if !menu.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != menu.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := muo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := muo.mutation.UpdatedAt(); ok {
		_spec.SetField(menu.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := muo.mutation.Sort(); ok {
		_spec.SetField(menu.FieldSort, field.TypeUint32, value)
	}
	if value, ok := muo.mutation.AddedSort(); ok {
		_spec.AddField(menu.FieldSort, field.TypeUint32, value)
	}
	if value, ok := muo.mutation.DeletedAt(); ok {
		_spec.SetField(menu.FieldDeletedAt, field.TypeTime, value)
	}
	if muo.mutation.DeletedAtCleared() {
		_spec.ClearField(menu.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := muo.mutation.Name(); ok {
		_spec.SetField(menu.FieldName, field.TypeString, value)
	}
	if value, ok := muo.mutation.Title(); ok {
		_spec.SetField(menu.FieldTitle, field.TypeString, value)
	}
	if value, ok := muo.mutation.Icon(); ok {
		_spec.SetField(menu.FieldIcon, field.TypeString, value)
	}
	if value, ok := muo.mutation.MenuType(); ok {
		_spec.SetField(menu.FieldMenuType, field.TypeUint32, value)
	}
	if value, ok := muo.mutation.AddedMenuType(); ok {
		_spec.AddField(menu.FieldMenuType, field.TypeUint32, value)
	}
	if value, ok := muo.mutation.URL(); ok {
		_spec.SetField(menu.FieldURL, field.TypeString, value)
	}
	if muo.mutation.URLCleared() {
		_spec.ClearField(menu.FieldURL, field.TypeString)
	}
	if value, ok := muo.mutation.Redirect(); ok {
		_spec.SetField(menu.FieldRedirect, field.TypeString, value)
	}
	if muo.mutation.RedirectCleared() {
		_spec.ClearField(menu.FieldRedirect, field.TypeString)
	}
	if value, ok := muo.mutation.Component(); ok {
		_spec.SetField(menu.FieldComponent, field.TypeString, value)
	}
	if muo.mutation.ComponentCleared() {
		_spec.ClearField(menu.FieldComponent, field.TypeString)
	}
	if value, ok := muo.mutation.IsActive(); ok {
		_spec.SetField(menu.FieldIsActive, field.TypeBool, value)
	}
	if muo.mutation.IsActiveCleared() {
		_spec.ClearField(menu.FieldIsActive, field.TypeBool)
	}
	if value, ok := muo.mutation.Hidden(); ok {
		_spec.SetField(menu.FieldHidden, field.TypeBool, value)
	}
	if muo.mutation.HiddenCleared() {
		_spec.ClearField(menu.FieldHidden, field.TypeBool)
	}
	if value, ok := muo.mutation.HiddenInTab(); ok {
		_spec.SetField(menu.FieldHiddenInTab, field.TypeBool, value)
	}
	if muo.mutation.HiddenInTabCleared() {
		_spec.ClearField(menu.FieldHiddenInTab, field.TypeBool)
	}
	if value, ok := muo.mutation.Fixed(); ok {
		_spec.SetField(menu.FieldFixed, field.TypeBool, value)
	}
	if muo.mutation.FixedCleared() {
		_spec.ClearField(menu.FieldFixed, field.TypeBool)
	}
	if value, ok := muo.mutation.Remark(); ok {
		_spec.SetField(menu.FieldRemark, field.TypeString, value)
	}
	if muo.mutation.RemarkCleared() {
		_spec.ClearField(menu.FieldRemark, field.TypeString)
	}
	if value, ok := muo.mutation.Meta(); ok {
		_spec.SetField(menu.FieldMeta, field.TypeString, value)
	}
	if muo.mutation.MetaCleared() {
		_spec.ClearField(menu.FieldMeta, field.TypeString)
	}
	if value, ok := muo.mutation.IsFullPage(); ok {
		_spec.SetField(menu.FieldIsFullPage, field.TypeBool, value)
	}
	if muo.mutation.IsFullPageCleared() {
		_spec.ClearField(menu.FieldIsFullPage, field.TypeBool)
	}
	if muo.mutation.RolesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   menu.RolesTable,
			Columns: menu.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := muo.mutation.RemovedRolesIDs(); len(nodes) > 0 && !muo.mutation.RolesCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   menu.RolesTable,
			Columns: menu.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := muo.mutation.RolesIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: true,
			Table:   menu.RolesTable,
			Columns: menu.RolesPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if muo.mutation.ButtonsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   menu.ButtonsTable,
			Columns: []string{menu.ButtonsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(button.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := muo.mutation.RemovedButtonsIDs(); len(nodes) > 0 && !muo.mutation.ButtonsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   menu.ButtonsTable,
			Columns: []string{menu.ButtonsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(button.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := muo.mutation.ButtonsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   menu.ButtonsTable,
			Columns: []string{menu.ButtonsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(button.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if muo.mutation.ParentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   menu.ParentTable,
			Columns: []string{menu.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := muo.mutation.ParentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   menu.ParentTable,
			Columns: []string{menu.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if muo.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   menu.ChildrenTable,
			Columns: []string{menu.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := muo.mutation.RemovedChildrenIDs(); len(nodes) > 0 && !muo.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   menu.ChildrenTable,
			Columns: []string{menu.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := muo.mutation.ChildrenIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   menu.ChildrenTable,
			Columns: []string{menu.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Menu{config: muo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, muo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{menu.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	muo.mutation.done = true
	return _node, nil
}
