// Code generated by ent, DO NOT EDIT.

package ent

import (
	"fmt"
	"phoenix/service/saas/model/ent/role"
	"phoenix/service/saas/model/ent/tenant"
	"strings"
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	uuid "github.com/gofrs/uuid/v5"
)

// Role is the model entity for the Role schema.
type Role struct {
	config `json:"-"`
	// ID of the ent.
	// Snowflake ID | 全局唯一ID
	ID string `json:"id,omitempty"`
	// Created Time | 创建时间
	CreatedAt time.Time `json:"created_at,omitempty"`
	// Updated Time | 更新时间
	UpdatedAt time.Time `json:"updated_at,omitempty"`
	// status true normal false ban | 状态  正常/禁用
	Status bool `json:"status,omitempty"`
	// Sort number | 排序编号
	Sort uint32 `json:"sort,omitempty"`
	// Tenant ID
	TenantID string `json:"tenant_id,omitempty"`
	// Deleted Time | 删除时间（软删除标识）
	DeletedAt time.Time `json:"deleted_at,omitempty"`
	// role name | 角色名
	Name string `json:"name,omitempty"`
	// role code for permission control in front end | 角色码，用于前端权限控制
	Code string `json:"code,omitempty"`
	// UUID
	UID uuid.UUID `json:"uid,omitempty"`
	// default menu : dashboard | 默认登录页面
	DefaultRouter string `json:"default_router,omitempty"`
	// remark | 备注
	Remark string `json:"remark,omitempty"`
	// 组织id
	OrganizationID string `json:"organization_id,omitempty"`
	// Parent role ID | 父级角色ID
	ParentID string `json:"parent_id,omitempty"`
	// Edges holds the relations/edges for other nodes in the graph.
	// The values are being populated by the RoleQuery when eager-loading is set.
	Edges        RoleEdges `json:"edges"`
	selectValues sql.SelectValues
}

// RoleEdges holds the relations/edges for other nodes in the graph.
type RoleEdges struct {
	// Tenant holds the value of the tenant edge.
	Tenant *Tenant `json:"tenant,omitempty"`
	// Buttons holds the value of the buttons edge.
	Buttons []*Button `json:"buttons,omitempty"`
	// Menus holds the value of the menus edge.
	Menus []*Menu `json:"menus,omitempty"`
	// Apis holds the value of the apis edge.
	Apis []*API `json:"apis,omitempty"`
	// Groups holds the value of the groups edge.
	Groups []*Group `json:"groups,omitempty"`
	// Users holds the value of the users edge.
	Users []*User `json:"users,omitempty"`
	// Parent holds the value of the parent edge.
	Parent *Role `json:"parent,omitempty"`
	// Children holds the value of the children edge.
	Children []*Role `json:"children,omitempty"`
	// loadedTypes holds the information for reporting if a
	// type was loaded (or requested) in eager-loading or not.
	loadedTypes [8]bool
}

// TenantOrErr returns the Tenant value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e RoleEdges) TenantOrErr() (*Tenant, error) {
	if e.Tenant != nil {
		return e.Tenant, nil
	} else if e.loadedTypes[0] {
		return nil, &NotFoundError{label: tenant.Label}
	}
	return nil, &NotLoadedError{edge: "tenant"}
}

// ButtonsOrErr returns the Buttons value or an error if the edge
// was not loaded in eager-loading.
func (e RoleEdges) ButtonsOrErr() ([]*Button, error) {
	if e.loadedTypes[1] {
		return e.Buttons, nil
	}
	return nil, &NotLoadedError{edge: "buttons"}
}

// MenusOrErr returns the Menus value or an error if the edge
// was not loaded in eager-loading.
func (e RoleEdges) MenusOrErr() ([]*Menu, error) {
	if e.loadedTypes[2] {
		return e.Menus, nil
	}
	return nil, &NotLoadedError{edge: "menus"}
}

// ApisOrErr returns the Apis value or an error if the edge
// was not loaded in eager-loading.
func (e RoleEdges) ApisOrErr() ([]*API, error) {
	if e.loadedTypes[3] {
		return e.Apis, nil
	}
	return nil, &NotLoadedError{edge: "apis"}
}

// GroupsOrErr returns the Groups value or an error if the edge
// was not loaded in eager-loading.
func (e RoleEdges) GroupsOrErr() ([]*Group, error) {
	if e.loadedTypes[4] {
		return e.Groups, nil
	}
	return nil, &NotLoadedError{edge: "groups"}
}

// UsersOrErr returns the Users value or an error if the edge
// was not loaded in eager-loading.
func (e RoleEdges) UsersOrErr() ([]*User, error) {
	if e.loadedTypes[5] {
		return e.Users, nil
	}
	return nil, &NotLoadedError{edge: "users"}
}

// ParentOrErr returns the Parent value or an error if the edge
// was not loaded in eager-loading, or loaded but was not found.
func (e RoleEdges) ParentOrErr() (*Role, error) {
	if e.Parent != nil {
		return e.Parent, nil
	} else if e.loadedTypes[6] {
		return nil, &NotFoundError{label: role.Label}
	}
	return nil, &NotLoadedError{edge: "parent"}
}

// ChildrenOrErr returns the Children value or an error if the edge
// was not loaded in eager-loading.
func (e RoleEdges) ChildrenOrErr() ([]*Role, error) {
	if e.loadedTypes[7] {
		return e.Children, nil
	}
	return nil, &NotLoadedError{edge: "children"}
}

// scanValues returns the types for scanning values from sql.Rows.
func (*Role) scanValues(columns []string) ([]any, error) {
	values := make([]any, len(columns))
	for i := range columns {
		switch columns[i] {
		case role.FieldStatus:
			values[i] = new(sql.NullBool)
		case role.FieldSort:
			values[i] = new(sql.NullInt64)
		case role.FieldID, role.FieldTenantID, role.FieldName, role.FieldCode, role.FieldDefaultRouter, role.FieldRemark, role.FieldOrganizationID, role.FieldParentID:
			values[i] = new(sql.NullString)
		case role.FieldCreatedAt, role.FieldUpdatedAt, role.FieldDeletedAt:
			values[i] = new(sql.NullTime)
		case role.FieldUID:
			values[i] = new(uuid.UUID)
		default:
			values[i] = new(sql.UnknownType)
		}
	}
	return values, nil
}

// assignValues assigns the values that were returned from sql.Rows (after scanning)
// to the Role fields.
func (r *Role) assignValues(columns []string, values []any) error {
	if m, n := len(values), len(columns); m < n {
		return fmt.Errorf("mismatch number of scan values: %d != %d", m, n)
	}
	for i := range columns {
		switch columns[i] {
		case role.FieldID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field id", values[i])
			} else if value.Valid {
				r.ID = value.String
			}
		case role.FieldCreatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field created_at", values[i])
			} else if value.Valid {
				r.CreatedAt = value.Time
			}
		case role.FieldUpdatedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field updated_at", values[i])
			} else if value.Valid {
				r.UpdatedAt = value.Time
			}
		case role.FieldStatus:
			if value, ok := values[i].(*sql.NullBool); !ok {
				return fmt.Errorf("unexpected type %T for field status", values[i])
			} else if value.Valid {
				r.Status = value.Bool
			}
		case role.FieldSort:
			if value, ok := values[i].(*sql.NullInt64); !ok {
				return fmt.Errorf("unexpected type %T for field sort", values[i])
			} else if value.Valid {
				r.Sort = uint32(value.Int64)
			}
		case role.FieldTenantID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field tenant_id", values[i])
			} else if value.Valid {
				r.TenantID = value.String
			}
		case role.FieldDeletedAt:
			if value, ok := values[i].(*sql.NullTime); !ok {
				return fmt.Errorf("unexpected type %T for field deleted_at", values[i])
			} else if value.Valid {
				r.DeletedAt = value.Time
			}
		case role.FieldName:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field name", values[i])
			} else if value.Valid {
				r.Name = value.String
			}
		case role.FieldCode:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field code", values[i])
			} else if value.Valid {
				r.Code = value.String
			}
		case role.FieldUID:
			if value, ok := values[i].(*uuid.UUID); !ok {
				return fmt.Errorf("unexpected type %T for field uid", values[i])
			} else if value != nil {
				r.UID = *value
			}
		case role.FieldDefaultRouter:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field default_router", values[i])
			} else if value.Valid {
				r.DefaultRouter = value.String
			}
		case role.FieldRemark:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field remark", values[i])
			} else if value.Valid {
				r.Remark = value.String
			}
		case role.FieldOrganizationID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field organization_id", values[i])
			} else if value.Valid {
				r.OrganizationID = value.String
			}
		case role.FieldParentID:
			if value, ok := values[i].(*sql.NullString); !ok {
				return fmt.Errorf("unexpected type %T for field parent_id", values[i])
			} else if value.Valid {
				r.ParentID = value.String
			}
		default:
			r.selectValues.Set(columns[i], values[i])
		}
	}
	return nil
}

// Value returns the ent.Value that was dynamically selected and assigned to the Role.
// This includes values selected through modifiers, order, etc.
func (r *Role) Value(name string) (ent.Value, error) {
	return r.selectValues.Get(name)
}

// QueryTenant queries the "tenant" edge of the Role entity.
func (r *Role) QueryTenant() *TenantQuery {
	return NewRoleClient(r.config).QueryTenant(r)
}

// QueryButtons queries the "buttons" edge of the Role entity.
func (r *Role) QueryButtons() *ButtonQuery {
	return NewRoleClient(r.config).QueryButtons(r)
}

// QueryMenus queries the "menus" edge of the Role entity.
func (r *Role) QueryMenus() *MenuQuery {
	return NewRoleClient(r.config).QueryMenus(r)
}

// QueryApis queries the "apis" edge of the Role entity.
func (r *Role) QueryApis() *APIQuery {
	return NewRoleClient(r.config).QueryApis(r)
}

// QueryGroups queries the "groups" edge of the Role entity.
func (r *Role) QueryGroups() *GroupQuery {
	return NewRoleClient(r.config).QueryGroups(r)
}

// QueryUsers queries the "users" edge of the Role entity.
func (r *Role) QueryUsers() *UserQuery {
	return NewRoleClient(r.config).QueryUsers(r)
}

// QueryParent queries the "parent" edge of the Role entity.
func (r *Role) QueryParent() *RoleQuery {
	return NewRoleClient(r.config).QueryParent(r)
}

// QueryChildren queries the "children" edge of the Role entity.
func (r *Role) QueryChildren() *RoleQuery {
	return NewRoleClient(r.config).QueryChildren(r)
}

// Update returns a builder for updating this Role.
// Note that you need to call Role.Unwrap() before calling this method if this Role
// was returned from a transaction, and the transaction was committed or rolled back.
func (r *Role) Update() *RoleUpdateOne {
	return NewRoleClient(r.config).UpdateOne(r)
}

// Unwrap unwraps the Role entity that was returned from a transaction after it was closed,
// so that all future queries will be executed through the driver which created the transaction.
func (r *Role) Unwrap() *Role {
	_tx, ok := r.config.driver.(*txDriver)
	if !ok {
		panic("ent: Role is not a transactional entity")
	}
	r.config.driver = _tx.drv
	return r
}

// String implements the fmt.Stringer.
func (r *Role) String() string {
	var builder strings.Builder
	builder.WriteString("Role(")
	builder.WriteString(fmt.Sprintf("id=%v, ", r.ID))
	builder.WriteString("created_at=")
	builder.WriteString(r.CreatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("updated_at=")
	builder.WriteString(r.UpdatedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("status=")
	builder.WriteString(fmt.Sprintf("%v", r.Status))
	builder.WriteString(", ")
	builder.WriteString("sort=")
	builder.WriteString(fmt.Sprintf("%v", r.Sort))
	builder.WriteString(", ")
	builder.WriteString("tenant_id=")
	builder.WriteString(r.TenantID)
	builder.WriteString(", ")
	builder.WriteString("deleted_at=")
	builder.WriteString(r.DeletedAt.Format(time.ANSIC))
	builder.WriteString(", ")
	builder.WriteString("name=")
	builder.WriteString(r.Name)
	builder.WriteString(", ")
	builder.WriteString("code=")
	builder.WriteString(r.Code)
	builder.WriteString(", ")
	builder.WriteString("uid=")
	builder.WriteString(fmt.Sprintf("%v", r.UID))
	builder.WriteString(", ")
	builder.WriteString("default_router=")
	builder.WriteString(r.DefaultRouter)
	builder.WriteString(", ")
	builder.WriteString("remark=")
	builder.WriteString(r.Remark)
	builder.WriteString(", ")
	builder.WriteString("organization_id=")
	builder.WriteString(r.OrganizationID)
	builder.WriteString(", ")
	builder.WriteString("parent_id=")
	builder.WriteString(r.ParentID)
	builder.WriteByte(')')
	return builder.String()
}

// Roles is a parsable slice of Role.
type Roles []*Role
