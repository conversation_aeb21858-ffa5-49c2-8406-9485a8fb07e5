// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/api"
	"phoenix/service/saas/model/ent/button"
	"phoenix/service/saas/model/ent/menu"
	"phoenix/service/saas/model/ent/tenant"
	"phoenix/service/saas/model/ent/user"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	uuid "github.com/gofrs/uuid/v5"
)

// TenantCreate is the builder for creating a Tenant entity.
type TenantCreate struct {
	config
	mutation *TenantMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (tc *TenantCreate) SetCreatedAt(t time.Time) *TenantCreate {
	tc.mutation.SetCreatedAt(t)
	return tc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (tc *TenantCreate) SetNillableCreatedAt(t *time.Time) *TenantCreate {
	if t != nil {
		tc.SetCreatedAt(*t)
	}
	return tc
}

// SetUpdatedAt sets the "updated_at" field.
func (tc *TenantCreate) SetUpdatedAt(t time.Time) *TenantCreate {
	tc.mutation.SetUpdatedAt(t)
	return tc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (tc *TenantCreate) SetNillableUpdatedAt(t *time.Time) *TenantCreate {
	if t != nil {
		tc.SetUpdatedAt(*t)
	}
	return tc
}

// SetUUID sets the "uuid" field.
func (tc *TenantCreate) SetUUID(u uuid.UUID) *TenantCreate {
	tc.mutation.SetUUID(u)
	return tc
}

// SetNillableUUID sets the "uuid" field if the given value is not nil.
func (tc *TenantCreate) SetNillableUUID(u *uuid.UUID) *TenantCreate {
	if u != nil {
		tc.SetUUID(*u)
	}
	return tc
}

// SetKey sets the "key" field.
func (tc *TenantCreate) SetKey(s string) *TenantCreate {
	tc.mutation.SetKey(s)
	return tc
}

// SetNillableKey sets the "key" field if the given value is not nil.
func (tc *TenantCreate) SetNillableKey(s *string) *TenantCreate {
	if s != nil {
		tc.SetKey(*s)
	}
	return tc
}

// SetSecret sets the "secret" field.
func (tc *TenantCreate) SetSecret(s string) *TenantCreate {
	tc.mutation.SetSecret(s)
	return tc
}

// SetNillableSecret sets the "secret" field if the given value is not nil.
func (tc *TenantCreate) SetNillableSecret(s *string) *TenantCreate {
	if s != nil {
		tc.SetSecret(*s)
	}
	return tc
}

// SetStatus sets the "status" field.
func (tc *TenantCreate) SetStatus(b bool) *TenantCreate {
	tc.mutation.SetStatus(b)
	return tc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (tc *TenantCreate) SetNillableStatus(b *bool) *TenantCreate {
	if b != nil {
		tc.SetStatus(*b)
	}
	return tc
}

// SetDeletedAt sets the "deleted_at" field.
func (tc *TenantCreate) SetDeletedAt(t time.Time) *TenantCreate {
	tc.mutation.SetDeletedAt(t)
	return tc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (tc *TenantCreate) SetNillableDeletedAt(t *time.Time) *TenantCreate {
	if t != nil {
		tc.SetDeletedAt(*t)
	}
	return tc
}

// SetName sets the "name" field.
func (tc *TenantCreate) SetName(s string) *TenantCreate {
	tc.mutation.SetName(s)
	return tc
}

// SetNillableName sets the "name" field if the given value is not nil.
func (tc *TenantCreate) SetNillableName(s *string) *TenantCreate {
	if s != nil {
		tc.SetName(*s)
	}
	return tc
}

// SetIsSuper sets the "is_super" field.
func (tc *TenantCreate) SetIsSuper(b bool) *TenantCreate {
	tc.mutation.SetIsSuper(b)
	return tc
}

// SetNillableIsSuper sets the "is_super" field if the given value is not nil.
func (tc *TenantCreate) SetNillableIsSuper(b *bool) *TenantCreate {
	if b != nil {
		tc.SetIsSuper(*b)
	}
	return tc
}

// SetServiceStartAt sets the "service_start_at" field.
func (tc *TenantCreate) SetServiceStartAt(t time.Time) *TenantCreate {
	tc.mutation.SetServiceStartAt(t)
	return tc
}

// SetNillableServiceStartAt sets the "service_start_at" field if the given value is not nil.
func (tc *TenantCreate) SetNillableServiceStartAt(t *time.Time) *TenantCreate {
	if t != nil {
		tc.SetServiceStartAt(*t)
	}
	return tc
}

// SetServiceEndAt sets the "service_end_at" field.
func (tc *TenantCreate) SetServiceEndAt(t time.Time) *TenantCreate {
	tc.mutation.SetServiceEndAt(t)
	return tc
}

// SetNillableServiceEndAt sets the "service_end_at" field if the given value is not nil.
func (tc *TenantCreate) SetNillableServiceEndAt(t *time.Time) *TenantCreate {
	if t != nil {
		tc.SetServiceEndAt(*t)
	}
	return tc
}

// SetAfterSalesContact sets the "after_sales_contact" field.
func (tc *TenantCreate) SetAfterSalesContact(s string) *TenantCreate {
	tc.mutation.SetAfterSalesContact(s)
	return tc
}

// SetNillableAfterSalesContact sets the "after_sales_contact" field if the given value is not nil.
func (tc *TenantCreate) SetNillableAfterSalesContact(s *string) *TenantCreate {
	if s != nil {
		tc.SetAfterSalesContact(*s)
	}
	return tc
}

// SetLocationID sets the "location_id" field.
func (tc *TenantCreate) SetLocationID(s string) *TenantCreate {
	tc.mutation.SetLocationID(s)
	return tc
}

// SetNillableLocationID sets the "location_id" field if the given value is not nil.
func (tc *TenantCreate) SetNillableLocationID(s *string) *TenantCreate {
	if s != nil {
		tc.SetLocationID(*s)
	}
	return tc
}

// SetLogSaveKeepDays sets the "log_save_keep_days" field.
func (tc *TenantCreate) SetLogSaveKeepDays(i int64) *TenantCreate {
	tc.mutation.SetLogSaveKeepDays(i)
	return tc
}

// SetNillableLogSaveKeepDays sets the "log_save_keep_days" field if the given value is not nil.
func (tc *TenantCreate) SetNillableLogSaveKeepDays(i *int64) *TenantCreate {
	if i != nil {
		tc.SetLogSaveKeepDays(*i)
	}
	return tc
}

// SetMaxAttendanceUserCount sets the "max_attendance_user_count" field.
func (tc *TenantCreate) SetMaxAttendanceUserCount(i int64) *TenantCreate {
	tc.mutation.SetMaxAttendanceUserCount(i)
	return tc
}

// SetNillableMaxAttendanceUserCount sets the "max_attendance_user_count" field if the given value is not nil.
func (tc *TenantCreate) SetNillableMaxAttendanceUserCount(i *int64) *TenantCreate {
	if i != nil {
		tc.SetMaxAttendanceUserCount(*i)
	}
	return tc
}

// SetMaxDeviceCount sets the "max_device_count" field.
func (tc *TenantCreate) SetMaxDeviceCount(i int64) *TenantCreate {
	tc.mutation.SetMaxDeviceCount(i)
	return tc
}

// SetNillableMaxDeviceCount sets the "max_device_count" field if the given value is not nil.
func (tc *TenantCreate) SetNillableMaxDeviceCount(i *int64) *TenantCreate {
	if i != nil {
		tc.SetMaxDeviceCount(*i)
	}
	return tc
}

// SetMaxUploadFileSize sets the "max_upload_file_size" field.
func (tc *TenantCreate) SetMaxUploadFileSize(i int64) *TenantCreate {
	tc.mutation.SetMaxUploadFileSize(i)
	return tc
}

// SetNillableMaxUploadFileSize sets the "max_upload_file_size" field if the given value is not nil.
func (tc *TenantCreate) SetNillableMaxUploadFileSize(i *int64) *TenantCreate {
	if i != nil {
		tc.SetMaxUploadFileSize(*i)
	}
	return tc
}

// SetMaxUserCount sets the "max_user_count" field.
func (tc *TenantCreate) SetMaxUserCount(i int64) *TenantCreate {
	tc.mutation.SetMaxUserCount(i)
	return tc
}

// SetNillableMaxUserCount sets the "max_user_count" field if the given value is not nil.
func (tc *TenantCreate) SetNillableMaxUserCount(i *int64) *TenantCreate {
	if i != nil {
		tc.SetMaxUserCount(*i)
	}
	return tc
}

// SetPrincipal sets the "principal" field.
func (tc *TenantCreate) SetPrincipal(s string) *TenantCreate {
	tc.mutation.SetPrincipal(s)
	return tc
}

// SetNillablePrincipal sets the "principal" field if the given value is not nil.
func (tc *TenantCreate) SetNillablePrincipal(s *string) *TenantCreate {
	if s != nil {
		tc.SetPrincipal(*s)
	}
	return tc
}

// SetPrincipalContactInformation sets the "principal_contact_information" field.
func (tc *TenantCreate) SetPrincipalContactInformation(s string) *TenantCreate {
	tc.mutation.SetPrincipalContactInformation(s)
	return tc
}

// SetNillablePrincipalContactInformation sets the "principal_contact_information" field if the given value is not nil.
func (tc *TenantCreate) SetNillablePrincipalContactInformation(s *string) *TenantCreate {
	if s != nil {
		tc.SetPrincipalContactInformation(*s)
	}
	return tc
}

// SetSaleContact sets the "sale_contact" field.
func (tc *TenantCreate) SetSaleContact(s string) *TenantCreate {
	tc.mutation.SetSaleContact(s)
	return tc
}

// SetNillableSaleContact sets the "sale_contact" field if the given value is not nil.
func (tc *TenantCreate) SetNillableSaleContact(s *string) *TenantCreate {
	if s != nil {
		tc.SetSaleContact(*s)
	}
	return tc
}

// SetSecretKey sets the "secret_key" field.
func (tc *TenantCreate) SetSecretKey(s string) *TenantCreate {
	tc.mutation.SetSecretKey(s)
	return tc
}

// SetNillableSecretKey sets the "secret_key" field if the given value is not nil.
func (tc *TenantCreate) SetNillableSecretKey(s *string) *TenantCreate {
	if s != nil {
		tc.SetSecretKey(*s)
	}
	return tc
}

// SetAiStatus sets the "ai_status" field.
func (tc *TenantCreate) SetAiStatus(b bool) *TenantCreate {
	tc.mutation.SetAiStatus(b)
	return tc
}

// SetNillableAiStatus sets the "ai_status" field if the given value is not nil.
func (tc *TenantCreate) SetNillableAiStatus(b *bool) *TenantCreate {
	if b != nil {
		tc.SetAiStatus(*b)
	}
	return tc
}

// SetMaxConferenceAgendaTitle sets the "max_conference_agenda_title" field.
func (tc *TenantCreate) SetMaxConferenceAgendaTitle(i int64) *TenantCreate {
	tc.mutation.SetMaxConferenceAgendaTitle(i)
	return tc
}

// SetNillableMaxConferenceAgendaTitle sets the "max_conference_agenda_title" field if the given value is not nil.
func (tc *TenantCreate) SetNillableMaxConferenceAgendaTitle(i *int64) *TenantCreate {
	if i != nil {
		tc.SetMaxConferenceAgendaTitle(*i)
	}
	return tc
}

// SetID sets the "id" field.
func (tc *TenantCreate) SetID(s string) *TenantCreate {
	tc.mutation.SetID(s)
	return tc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (tc *TenantCreate) SetNillableID(s *string) *TenantCreate {
	if s != nil {
		tc.SetID(*s)
	}
	return tc
}

// AddUserIDs adds the "users" edge to the User entity by IDs.
func (tc *TenantCreate) AddUserIDs(ids ...string) *TenantCreate {
	tc.mutation.AddUserIDs(ids...)
	return tc
}

// AddUsers adds the "users" edges to the User entity.
func (tc *TenantCreate) AddUsers(u ...*User) *TenantCreate {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return tc.AddUserIDs(ids...)
}

// AddMenuIDs adds the "menus" edge to the Menu entity by IDs.
func (tc *TenantCreate) AddMenuIDs(ids ...string) *TenantCreate {
	tc.mutation.AddMenuIDs(ids...)
	return tc
}

// AddMenus adds the "menus" edges to the Menu entity.
func (tc *TenantCreate) AddMenus(m ...*Menu) *TenantCreate {
	ids := make([]string, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return tc.AddMenuIDs(ids...)
}

// AddAPIIDs adds the "apis" edge to the API entity by IDs.
func (tc *TenantCreate) AddAPIIDs(ids ...string) *TenantCreate {
	tc.mutation.AddAPIIDs(ids...)
	return tc
}

// AddApis adds the "apis" edges to the API entity.
func (tc *TenantCreate) AddApis(a ...*API) *TenantCreate {
	ids := make([]string, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return tc.AddAPIIDs(ids...)
}

// AddButtonIDs adds the "buttons" edge to the Button entity by IDs.
func (tc *TenantCreate) AddButtonIDs(ids ...string) *TenantCreate {
	tc.mutation.AddButtonIDs(ids...)
	return tc
}

// AddButtons adds the "buttons" edges to the Button entity.
func (tc *TenantCreate) AddButtons(b ...*Button) *TenantCreate {
	ids := make([]string, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return tc.AddButtonIDs(ids...)
}

// Mutation returns the TenantMutation object of the builder.
func (tc *TenantCreate) Mutation() *TenantMutation {
	return tc.mutation
}

// Save creates the Tenant in the database.
func (tc *TenantCreate) Save(ctx context.Context) (*Tenant, error) {
	if err := tc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, tc.sqlSave, tc.mutation, tc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (tc *TenantCreate) SaveX(ctx context.Context) *Tenant {
	v, err := tc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tc *TenantCreate) Exec(ctx context.Context) error {
	_, err := tc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tc *TenantCreate) ExecX(ctx context.Context) {
	if err := tc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (tc *TenantCreate) defaults() error {
	if _, ok := tc.mutation.CreatedAt(); !ok {
		if tenant.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized tenant.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := tenant.DefaultCreatedAt()
		tc.mutation.SetCreatedAt(v)
	}
	if _, ok := tc.mutation.UpdatedAt(); !ok {
		if tenant.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized tenant.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := tenant.DefaultUpdatedAt()
		tc.mutation.SetUpdatedAt(v)
	}
	if _, ok := tc.mutation.UUID(); !ok {
		if tenant.DefaultUUID == nil {
			return fmt.Errorf("ent: uninitialized tenant.DefaultUUID (forgotten import ent/runtime?)")
		}
		v := tenant.DefaultUUID()
		tc.mutation.SetUUID(v)
	}
	if _, ok := tc.mutation.Key(); !ok {
		if tenant.DefaultKey == nil {
			return fmt.Errorf("ent: uninitialized tenant.DefaultKey (forgotten import ent/runtime?)")
		}
		v := tenant.DefaultKey()
		tc.mutation.SetKey(v)
	}
	if _, ok := tc.mutation.Secret(); !ok {
		if tenant.DefaultSecret == nil {
			return fmt.Errorf("ent: uninitialized tenant.DefaultSecret (forgotten import ent/runtime?)")
		}
		v := tenant.DefaultSecret()
		tc.mutation.SetSecret(v)
	}
	if _, ok := tc.mutation.Status(); !ok {
		v := tenant.DefaultStatus
		tc.mutation.SetStatus(v)
	}
	if _, ok := tc.mutation.Name(); !ok {
		v := tenant.DefaultName
		tc.mutation.SetName(v)
	}
	if _, ok := tc.mutation.IsSuper(); !ok {
		v := tenant.DefaultIsSuper
		tc.mutation.SetIsSuper(v)
	}
	if _, ok := tc.mutation.AiStatus(); !ok {
		v := tenant.DefaultAiStatus
		tc.mutation.SetAiStatus(v)
	}
	if _, ok := tc.mutation.ID(); !ok {
		if tenant.DefaultID == nil {
			return fmt.Errorf("ent: uninitialized tenant.DefaultID (forgotten import ent/runtime?)")
		}
		v := tenant.DefaultID()
		tc.mutation.SetID(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (tc *TenantCreate) check() error {
	if _, ok := tc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Tenant.created_at"`)}
	}
	if _, ok := tc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Tenant.updated_at"`)}
	}
	if _, ok := tc.mutation.UUID(); !ok {
		return &ValidationError{Name: "uuid", err: errors.New(`ent: missing required field "Tenant.uuid"`)}
	}
	if _, ok := tc.mutation.Key(); !ok {
		return &ValidationError{Name: "key", err: errors.New(`ent: missing required field "Tenant.key"`)}
	}
	return nil
}

func (tc *TenantCreate) sqlSave(ctx context.Context) (*Tenant, error) {
	if err := tc.check(); err != nil {
		return nil, err
	}
	_node, _spec := tc.createSpec()
	if err := sqlgraph.CreateNode(ctx, tc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected Tenant.ID type: %T", _spec.ID.Value)
		}
	}
	tc.mutation.id = &_node.ID
	tc.mutation.done = true
	return _node, nil
}

func (tc *TenantCreate) createSpec() (*Tenant, *sqlgraph.CreateSpec) {
	var (
		_node = &Tenant{config: tc.config}
		_spec = sqlgraph.NewCreateSpec(tenant.Table, sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString))
	)
	_spec.OnConflict = tc.conflict
	if id, ok := tc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := tc.mutation.CreatedAt(); ok {
		_spec.SetField(tenant.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := tc.mutation.UpdatedAt(); ok {
		_spec.SetField(tenant.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := tc.mutation.UUID(); ok {
		_spec.SetField(tenant.FieldUUID, field.TypeUUID, value)
		_node.UUID = value
	}
	if value, ok := tc.mutation.Key(); ok {
		_spec.SetField(tenant.FieldKey, field.TypeString, value)
		_node.Key = value
	}
	if value, ok := tc.mutation.Secret(); ok {
		_spec.SetField(tenant.FieldSecret, field.TypeString, value)
		_node.Secret = value
	}
	if value, ok := tc.mutation.Status(); ok {
		_spec.SetField(tenant.FieldStatus, field.TypeBool, value)
		_node.Status = value
	}
	if value, ok := tc.mutation.DeletedAt(); ok {
		_spec.SetField(tenant.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := tc.mutation.Name(); ok {
		_spec.SetField(tenant.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := tc.mutation.IsSuper(); ok {
		_spec.SetField(tenant.FieldIsSuper, field.TypeBool, value)
		_node.IsSuper = value
	}
	if value, ok := tc.mutation.ServiceStartAt(); ok {
		_spec.SetField(tenant.FieldServiceStartAt, field.TypeTime, value)
		_node.ServiceStartAt = value
	}
	if value, ok := tc.mutation.ServiceEndAt(); ok {
		_spec.SetField(tenant.FieldServiceEndAt, field.TypeTime, value)
		_node.ServiceEndAt = value
	}
	if value, ok := tc.mutation.AfterSalesContact(); ok {
		_spec.SetField(tenant.FieldAfterSalesContact, field.TypeString, value)
		_node.AfterSalesContact = value
	}
	if value, ok := tc.mutation.LocationID(); ok {
		_spec.SetField(tenant.FieldLocationID, field.TypeString, value)
		_node.LocationID = value
	}
	if value, ok := tc.mutation.LogSaveKeepDays(); ok {
		_spec.SetField(tenant.FieldLogSaveKeepDays, field.TypeInt64, value)
		_node.LogSaveKeepDays = value
	}
	if value, ok := tc.mutation.MaxAttendanceUserCount(); ok {
		_spec.SetField(tenant.FieldMaxAttendanceUserCount, field.TypeInt64, value)
		_node.MaxAttendanceUserCount = value
	}
	if value, ok := tc.mutation.MaxDeviceCount(); ok {
		_spec.SetField(tenant.FieldMaxDeviceCount, field.TypeInt64, value)
		_node.MaxDeviceCount = value
	}
	if value, ok := tc.mutation.MaxUploadFileSize(); ok {
		_spec.SetField(tenant.FieldMaxUploadFileSize, field.TypeInt64, value)
		_node.MaxUploadFileSize = value
	}
	if value, ok := tc.mutation.MaxUserCount(); ok {
		_spec.SetField(tenant.FieldMaxUserCount, field.TypeInt64, value)
		_node.MaxUserCount = value
	}
	if value, ok := tc.mutation.Principal(); ok {
		_spec.SetField(tenant.FieldPrincipal, field.TypeString, value)
		_node.Principal = value
	}
	if value, ok := tc.mutation.PrincipalContactInformation(); ok {
		_spec.SetField(tenant.FieldPrincipalContactInformation, field.TypeString, value)
		_node.PrincipalContactInformation = value
	}
	if value, ok := tc.mutation.SaleContact(); ok {
		_spec.SetField(tenant.FieldSaleContact, field.TypeString, value)
		_node.SaleContact = value
	}
	if value, ok := tc.mutation.SecretKey(); ok {
		_spec.SetField(tenant.FieldSecretKey, field.TypeString, value)
		_node.SecretKey = value
	}
	if value, ok := tc.mutation.AiStatus(); ok {
		_spec.SetField(tenant.FieldAiStatus, field.TypeBool, value)
		_node.AiStatus = value
	}
	if value, ok := tc.mutation.MaxConferenceAgendaTitle(); ok {
		_spec.SetField(tenant.FieldMaxConferenceAgendaTitle, field.TypeInt64, value)
		_node.MaxConferenceAgendaTitle = value
	}
	if nodes := tc.mutation.UsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   tenant.UsersTable,
			Columns: tenant.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := tc.mutation.MenusIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tenant.MenusTable,
			Columns: []string{tenant.MenusColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := tc.mutation.ApisIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tenant.ApisTable,
			Columns: []string{tenant.ApisColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(api.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := tc.mutation.ButtonsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   tenant.ButtonsTable,
			Columns: []string{tenant.ButtonsColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(button.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Tenant.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.TenantUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (tc *TenantCreate) OnConflict(opts ...sql.ConflictOption) *TenantUpsertOne {
	tc.conflict = opts
	return &TenantUpsertOne{
		create: tc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Tenant.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (tc *TenantCreate) OnConflictColumns(columns ...string) *TenantUpsertOne {
	tc.conflict = append(tc.conflict, sql.ConflictColumns(columns...))
	return &TenantUpsertOne{
		create: tc,
	}
}

type (
	// TenantUpsertOne is the builder for "upsert"-ing
	//  one Tenant node.
	TenantUpsertOne struct {
		create *TenantCreate
	}

	// TenantUpsert is the "OnConflict" setter.
	TenantUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *TenantUpsert) SetUpdatedAt(v time.Time) *TenantUpsert {
	u.Set(tenant.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *TenantUpsert) UpdateUpdatedAt() *TenantUpsert {
	u.SetExcluded(tenant.FieldUpdatedAt)
	return u
}

// SetUUID sets the "uuid" field.
func (u *TenantUpsert) SetUUID(v uuid.UUID) *TenantUpsert {
	u.Set(tenant.FieldUUID, v)
	return u
}

// UpdateUUID sets the "uuid" field to the value that was provided on create.
func (u *TenantUpsert) UpdateUUID() *TenantUpsert {
	u.SetExcluded(tenant.FieldUUID)
	return u
}

// SetKey sets the "key" field.
func (u *TenantUpsert) SetKey(v string) *TenantUpsert {
	u.Set(tenant.FieldKey, v)
	return u
}

// UpdateKey sets the "key" field to the value that was provided on create.
func (u *TenantUpsert) UpdateKey() *TenantUpsert {
	u.SetExcluded(tenant.FieldKey)
	return u
}

// SetSecret sets the "secret" field.
func (u *TenantUpsert) SetSecret(v string) *TenantUpsert {
	u.Set(tenant.FieldSecret, v)
	return u
}

// UpdateSecret sets the "secret" field to the value that was provided on create.
func (u *TenantUpsert) UpdateSecret() *TenantUpsert {
	u.SetExcluded(tenant.FieldSecret)
	return u
}

// ClearSecret clears the value of the "secret" field.
func (u *TenantUpsert) ClearSecret() *TenantUpsert {
	u.SetNull(tenant.FieldSecret)
	return u
}

// SetStatus sets the "status" field.
func (u *TenantUpsert) SetStatus(v bool) *TenantUpsert {
	u.Set(tenant.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *TenantUpsert) UpdateStatus() *TenantUpsert {
	u.SetExcluded(tenant.FieldStatus)
	return u
}

// ClearStatus clears the value of the "status" field.
func (u *TenantUpsert) ClearStatus() *TenantUpsert {
	u.SetNull(tenant.FieldStatus)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *TenantUpsert) SetDeletedAt(v time.Time) *TenantUpsert {
	u.Set(tenant.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *TenantUpsert) UpdateDeletedAt() *TenantUpsert {
	u.SetExcluded(tenant.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *TenantUpsert) ClearDeletedAt() *TenantUpsert {
	u.SetNull(tenant.FieldDeletedAt)
	return u
}

// SetName sets the "name" field.
func (u *TenantUpsert) SetName(v string) *TenantUpsert {
	u.Set(tenant.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *TenantUpsert) UpdateName() *TenantUpsert {
	u.SetExcluded(tenant.FieldName)
	return u
}

// ClearName clears the value of the "name" field.
func (u *TenantUpsert) ClearName() *TenantUpsert {
	u.SetNull(tenant.FieldName)
	return u
}

// SetIsSuper sets the "is_super" field.
func (u *TenantUpsert) SetIsSuper(v bool) *TenantUpsert {
	u.Set(tenant.FieldIsSuper, v)
	return u
}

// UpdateIsSuper sets the "is_super" field to the value that was provided on create.
func (u *TenantUpsert) UpdateIsSuper() *TenantUpsert {
	u.SetExcluded(tenant.FieldIsSuper)
	return u
}

// ClearIsSuper clears the value of the "is_super" field.
func (u *TenantUpsert) ClearIsSuper() *TenantUpsert {
	u.SetNull(tenant.FieldIsSuper)
	return u
}

// SetServiceStartAt sets the "service_start_at" field.
func (u *TenantUpsert) SetServiceStartAt(v time.Time) *TenantUpsert {
	u.Set(tenant.FieldServiceStartAt, v)
	return u
}

// UpdateServiceStartAt sets the "service_start_at" field to the value that was provided on create.
func (u *TenantUpsert) UpdateServiceStartAt() *TenantUpsert {
	u.SetExcluded(tenant.FieldServiceStartAt)
	return u
}

// ClearServiceStartAt clears the value of the "service_start_at" field.
func (u *TenantUpsert) ClearServiceStartAt() *TenantUpsert {
	u.SetNull(tenant.FieldServiceStartAt)
	return u
}

// SetServiceEndAt sets the "service_end_at" field.
func (u *TenantUpsert) SetServiceEndAt(v time.Time) *TenantUpsert {
	u.Set(tenant.FieldServiceEndAt, v)
	return u
}

// UpdateServiceEndAt sets the "service_end_at" field to the value that was provided on create.
func (u *TenantUpsert) UpdateServiceEndAt() *TenantUpsert {
	u.SetExcluded(tenant.FieldServiceEndAt)
	return u
}

// ClearServiceEndAt clears the value of the "service_end_at" field.
func (u *TenantUpsert) ClearServiceEndAt() *TenantUpsert {
	u.SetNull(tenant.FieldServiceEndAt)
	return u
}

// SetAfterSalesContact sets the "after_sales_contact" field.
func (u *TenantUpsert) SetAfterSalesContact(v string) *TenantUpsert {
	u.Set(tenant.FieldAfterSalesContact, v)
	return u
}

// UpdateAfterSalesContact sets the "after_sales_contact" field to the value that was provided on create.
func (u *TenantUpsert) UpdateAfterSalesContact() *TenantUpsert {
	u.SetExcluded(tenant.FieldAfterSalesContact)
	return u
}

// ClearAfterSalesContact clears the value of the "after_sales_contact" field.
func (u *TenantUpsert) ClearAfterSalesContact() *TenantUpsert {
	u.SetNull(tenant.FieldAfterSalesContact)
	return u
}

// SetLocationID sets the "location_id" field.
func (u *TenantUpsert) SetLocationID(v string) *TenantUpsert {
	u.Set(tenant.FieldLocationID, v)
	return u
}

// UpdateLocationID sets the "location_id" field to the value that was provided on create.
func (u *TenantUpsert) UpdateLocationID() *TenantUpsert {
	u.SetExcluded(tenant.FieldLocationID)
	return u
}

// ClearLocationID clears the value of the "location_id" field.
func (u *TenantUpsert) ClearLocationID() *TenantUpsert {
	u.SetNull(tenant.FieldLocationID)
	return u
}

// SetLogSaveKeepDays sets the "log_save_keep_days" field.
func (u *TenantUpsert) SetLogSaveKeepDays(v int64) *TenantUpsert {
	u.Set(tenant.FieldLogSaveKeepDays, v)
	return u
}

// UpdateLogSaveKeepDays sets the "log_save_keep_days" field to the value that was provided on create.
func (u *TenantUpsert) UpdateLogSaveKeepDays() *TenantUpsert {
	u.SetExcluded(tenant.FieldLogSaveKeepDays)
	return u
}

// AddLogSaveKeepDays adds v to the "log_save_keep_days" field.
func (u *TenantUpsert) AddLogSaveKeepDays(v int64) *TenantUpsert {
	u.Add(tenant.FieldLogSaveKeepDays, v)
	return u
}

// ClearLogSaveKeepDays clears the value of the "log_save_keep_days" field.
func (u *TenantUpsert) ClearLogSaveKeepDays() *TenantUpsert {
	u.SetNull(tenant.FieldLogSaveKeepDays)
	return u
}

// SetMaxAttendanceUserCount sets the "max_attendance_user_count" field.
func (u *TenantUpsert) SetMaxAttendanceUserCount(v int64) *TenantUpsert {
	u.Set(tenant.FieldMaxAttendanceUserCount, v)
	return u
}

// UpdateMaxAttendanceUserCount sets the "max_attendance_user_count" field to the value that was provided on create.
func (u *TenantUpsert) UpdateMaxAttendanceUserCount() *TenantUpsert {
	u.SetExcluded(tenant.FieldMaxAttendanceUserCount)
	return u
}

// AddMaxAttendanceUserCount adds v to the "max_attendance_user_count" field.
func (u *TenantUpsert) AddMaxAttendanceUserCount(v int64) *TenantUpsert {
	u.Add(tenant.FieldMaxAttendanceUserCount, v)
	return u
}

// ClearMaxAttendanceUserCount clears the value of the "max_attendance_user_count" field.
func (u *TenantUpsert) ClearMaxAttendanceUserCount() *TenantUpsert {
	u.SetNull(tenant.FieldMaxAttendanceUserCount)
	return u
}

// SetMaxDeviceCount sets the "max_device_count" field.
func (u *TenantUpsert) SetMaxDeviceCount(v int64) *TenantUpsert {
	u.Set(tenant.FieldMaxDeviceCount, v)
	return u
}

// UpdateMaxDeviceCount sets the "max_device_count" field to the value that was provided on create.
func (u *TenantUpsert) UpdateMaxDeviceCount() *TenantUpsert {
	u.SetExcluded(tenant.FieldMaxDeviceCount)
	return u
}

// AddMaxDeviceCount adds v to the "max_device_count" field.
func (u *TenantUpsert) AddMaxDeviceCount(v int64) *TenantUpsert {
	u.Add(tenant.FieldMaxDeviceCount, v)
	return u
}

// ClearMaxDeviceCount clears the value of the "max_device_count" field.
func (u *TenantUpsert) ClearMaxDeviceCount() *TenantUpsert {
	u.SetNull(tenant.FieldMaxDeviceCount)
	return u
}

// SetMaxUploadFileSize sets the "max_upload_file_size" field.
func (u *TenantUpsert) SetMaxUploadFileSize(v int64) *TenantUpsert {
	u.Set(tenant.FieldMaxUploadFileSize, v)
	return u
}

// UpdateMaxUploadFileSize sets the "max_upload_file_size" field to the value that was provided on create.
func (u *TenantUpsert) UpdateMaxUploadFileSize() *TenantUpsert {
	u.SetExcluded(tenant.FieldMaxUploadFileSize)
	return u
}

// AddMaxUploadFileSize adds v to the "max_upload_file_size" field.
func (u *TenantUpsert) AddMaxUploadFileSize(v int64) *TenantUpsert {
	u.Add(tenant.FieldMaxUploadFileSize, v)
	return u
}

// ClearMaxUploadFileSize clears the value of the "max_upload_file_size" field.
func (u *TenantUpsert) ClearMaxUploadFileSize() *TenantUpsert {
	u.SetNull(tenant.FieldMaxUploadFileSize)
	return u
}

// SetMaxUserCount sets the "max_user_count" field.
func (u *TenantUpsert) SetMaxUserCount(v int64) *TenantUpsert {
	u.Set(tenant.FieldMaxUserCount, v)
	return u
}

// UpdateMaxUserCount sets the "max_user_count" field to the value that was provided on create.
func (u *TenantUpsert) UpdateMaxUserCount() *TenantUpsert {
	u.SetExcluded(tenant.FieldMaxUserCount)
	return u
}

// AddMaxUserCount adds v to the "max_user_count" field.
func (u *TenantUpsert) AddMaxUserCount(v int64) *TenantUpsert {
	u.Add(tenant.FieldMaxUserCount, v)
	return u
}

// ClearMaxUserCount clears the value of the "max_user_count" field.
func (u *TenantUpsert) ClearMaxUserCount() *TenantUpsert {
	u.SetNull(tenant.FieldMaxUserCount)
	return u
}

// SetPrincipal sets the "principal" field.
func (u *TenantUpsert) SetPrincipal(v string) *TenantUpsert {
	u.Set(tenant.FieldPrincipal, v)
	return u
}

// UpdatePrincipal sets the "principal" field to the value that was provided on create.
func (u *TenantUpsert) UpdatePrincipal() *TenantUpsert {
	u.SetExcluded(tenant.FieldPrincipal)
	return u
}

// ClearPrincipal clears the value of the "principal" field.
func (u *TenantUpsert) ClearPrincipal() *TenantUpsert {
	u.SetNull(tenant.FieldPrincipal)
	return u
}

// SetPrincipalContactInformation sets the "principal_contact_information" field.
func (u *TenantUpsert) SetPrincipalContactInformation(v string) *TenantUpsert {
	u.Set(tenant.FieldPrincipalContactInformation, v)
	return u
}

// UpdatePrincipalContactInformation sets the "principal_contact_information" field to the value that was provided on create.
func (u *TenantUpsert) UpdatePrincipalContactInformation() *TenantUpsert {
	u.SetExcluded(tenant.FieldPrincipalContactInformation)
	return u
}

// ClearPrincipalContactInformation clears the value of the "principal_contact_information" field.
func (u *TenantUpsert) ClearPrincipalContactInformation() *TenantUpsert {
	u.SetNull(tenant.FieldPrincipalContactInformation)
	return u
}

// SetSaleContact sets the "sale_contact" field.
func (u *TenantUpsert) SetSaleContact(v string) *TenantUpsert {
	u.Set(tenant.FieldSaleContact, v)
	return u
}

// UpdateSaleContact sets the "sale_contact" field to the value that was provided on create.
func (u *TenantUpsert) UpdateSaleContact() *TenantUpsert {
	u.SetExcluded(tenant.FieldSaleContact)
	return u
}

// ClearSaleContact clears the value of the "sale_contact" field.
func (u *TenantUpsert) ClearSaleContact() *TenantUpsert {
	u.SetNull(tenant.FieldSaleContact)
	return u
}

// SetSecretKey sets the "secret_key" field.
func (u *TenantUpsert) SetSecretKey(v string) *TenantUpsert {
	u.Set(tenant.FieldSecretKey, v)
	return u
}

// UpdateSecretKey sets the "secret_key" field to the value that was provided on create.
func (u *TenantUpsert) UpdateSecretKey() *TenantUpsert {
	u.SetExcluded(tenant.FieldSecretKey)
	return u
}

// ClearSecretKey clears the value of the "secret_key" field.
func (u *TenantUpsert) ClearSecretKey() *TenantUpsert {
	u.SetNull(tenant.FieldSecretKey)
	return u
}

// SetAiStatus sets the "ai_status" field.
func (u *TenantUpsert) SetAiStatus(v bool) *TenantUpsert {
	u.Set(tenant.FieldAiStatus, v)
	return u
}

// UpdateAiStatus sets the "ai_status" field to the value that was provided on create.
func (u *TenantUpsert) UpdateAiStatus() *TenantUpsert {
	u.SetExcluded(tenant.FieldAiStatus)
	return u
}

// ClearAiStatus clears the value of the "ai_status" field.
func (u *TenantUpsert) ClearAiStatus() *TenantUpsert {
	u.SetNull(tenant.FieldAiStatus)
	return u
}

// SetMaxConferenceAgendaTitle sets the "max_conference_agenda_title" field.
func (u *TenantUpsert) SetMaxConferenceAgendaTitle(v int64) *TenantUpsert {
	u.Set(tenant.FieldMaxConferenceAgendaTitle, v)
	return u
}

// UpdateMaxConferenceAgendaTitle sets the "max_conference_agenda_title" field to the value that was provided on create.
func (u *TenantUpsert) UpdateMaxConferenceAgendaTitle() *TenantUpsert {
	u.SetExcluded(tenant.FieldMaxConferenceAgendaTitle)
	return u
}

// AddMaxConferenceAgendaTitle adds v to the "max_conference_agenda_title" field.
func (u *TenantUpsert) AddMaxConferenceAgendaTitle(v int64) *TenantUpsert {
	u.Add(tenant.FieldMaxConferenceAgendaTitle, v)
	return u
}

// ClearMaxConferenceAgendaTitle clears the value of the "max_conference_agenda_title" field.
func (u *TenantUpsert) ClearMaxConferenceAgendaTitle() *TenantUpsert {
	u.SetNull(tenant.FieldMaxConferenceAgendaTitle)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Tenant.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(tenant.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *TenantUpsertOne) UpdateNewValues() *TenantUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(tenant.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(tenant.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Tenant.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *TenantUpsertOne) Ignore() *TenantUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *TenantUpsertOne) DoNothing() *TenantUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the TenantCreate.OnConflict
// documentation for more info.
func (u *TenantUpsertOne) Update(set func(*TenantUpsert)) *TenantUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&TenantUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *TenantUpsertOne) SetUpdatedAt(v time.Time) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateUpdatedAt() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetUUID sets the "uuid" field.
func (u *TenantUpsertOne) SetUUID(v uuid.UUID) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetUUID(v)
	})
}

// UpdateUUID sets the "uuid" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateUUID() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateUUID()
	})
}

// SetKey sets the "key" field.
func (u *TenantUpsertOne) SetKey(v string) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetKey(v)
	})
}

// UpdateKey sets the "key" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateKey() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateKey()
	})
}

// SetSecret sets the "secret" field.
func (u *TenantUpsertOne) SetSecret(v string) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetSecret(v)
	})
}

// UpdateSecret sets the "secret" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateSecret() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateSecret()
	})
}

// ClearSecret clears the value of the "secret" field.
func (u *TenantUpsertOne) ClearSecret() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearSecret()
	})
}

// SetStatus sets the "status" field.
func (u *TenantUpsertOne) SetStatus(v bool) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateStatus() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *TenantUpsertOne) ClearStatus() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearStatus()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *TenantUpsertOne) SetDeletedAt(v time.Time) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateDeletedAt() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *TenantUpsertOne) ClearDeletedAt() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearDeletedAt()
	})
}

// SetName sets the "name" field.
func (u *TenantUpsertOne) SetName(v string) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateName() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateName()
	})
}

// ClearName clears the value of the "name" field.
func (u *TenantUpsertOne) ClearName() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearName()
	})
}

// SetIsSuper sets the "is_super" field.
func (u *TenantUpsertOne) SetIsSuper(v bool) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetIsSuper(v)
	})
}

// UpdateIsSuper sets the "is_super" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateIsSuper() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateIsSuper()
	})
}

// ClearIsSuper clears the value of the "is_super" field.
func (u *TenantUpsertOne) ClearIsSuper() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearIsSuper()
	})
}

// SetServiceStartAt sets the "service_start_at" field.
func (u *TenantUpsertOne) SetServiceStartAt(v time.Time) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetServiceStartAt(v)
	})
}

// UpdateServiceStartAt sets the "service_start_at" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateServiceStartAt() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateServiceStartAt()
	})
}

// ClearServiceStartAt clears the value of the "service_start_at" field.
func (u *TenantUpsertOne) ClearServiceStartAt() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearServiceStartAt()
	})
}

// SetServiceEndAt sets the "service_end_at" field.
func (u *TenantUpsertOne) SetServiceEndAt(v time.Time) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetServiceEndAt(v)
	})
}

// UpdateServiceEndAt sets the "service_end_at" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateServiceEndAt() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateServiceEndAt()
	})
}

// ClearServiceEndAt clears the value of the "service_end_at" field.
func (u *TenantUpsertOne) ClearServiceEndAt() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearServiceEndAt()
	})
}

// SetAfterSalesContact sets the "after_sales_contact" field.
func (u *TenantUpsertOne) SetAfterSalesContact(v string) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetAfterSalesContact(v)
	})
}

// UpdateAfterSalesContact sets the "after_sales_contact" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateAfterSalesContact() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateAfterSalesContact()
	})
}

// ClearAfterSalesContact clears the value of the "after_sales_contact" field.
func (u *TenantUpsertOne) ClearAfterSalesContact() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearAfterSalesContact()
	})
}

// SetLocationID sets the "location_id" field.
func (u *TenantUpsertOne) SetLocationID(v string) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetLocationID(v)
	})
}

// UpdateLocationID sets the "location_id" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateLocationID() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateLocationID()
	})
}

// ClearLocationID clears the value of the "location_id" field.
func (u *TenantUpsertOne) ClearLocationID() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearLocationID()
	})
}

// SetLogSaveKeepDays sets the "log_save_keep_days" field.
func (u *TenantUpsertOne) SetLogSaveKeepDays(v int64) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetLogSaveKeepDays(v)
	})
}

// AddLogSaveKeepDays adds v to the "log_save_keep_days" field.
func (u *TenantUpsertOne) AddLogSaveKeepDays(v int64) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.AddLogSaveKeepDays(v)
	})
}

// UpdateLogSaveKeepDays sets the "log_save_keep_days" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateLogSaveKeepDays() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateLogSaveKeepDays()
	})
}

// ClearLogSaveKeepDays clears the value of the "log_save_keep_days" field.
func (u *TenantUpsertOne) ClearLogSaveKeepDays() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearLogSaveKeepDays()
	})
}

// SetMaxAttendanceUserCount sets the "max_attendance_user_count" field.
func (u *TenantUpsertOne) SetMaxAttendanceUserCount(v int64) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetMaxAttendanceUserCount(v)
	})
}

// AddMaxAttendanceUserCount adds v to the "max_attendance_user_count" field.
func (u *TenantUpsertOne) AddMaxAttendanceUserCount(v int64) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.AddMaxAttendanceUserCount(v)
	})
}

// UpdateMaxAttendanceUserCount sets the "max_attendance_user_count" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateMaxAttendanceUserCount() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateMaxAttendanceUserCount()
	})
}

// ClearMaxAttendanceUserCount clears the value of the "max_attendance_user_count" field.
func (u *TenantUpsertOne) ClearMaxAttendanceUserCount() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearMaxAttendanceUserCount()
	})
}

// SetMaxDeviceCount sets the "max_device_count" field.
func (u *TenantUpsertOne) SetMaxDeviceCount(v int64) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetMaxDeviceCount(v)
	})
}

// AddMaxDeviceCount adds v to the "max_device_count" field.
func (u *TenantUpsertOne) AddMaxDeviceCount(v int64) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.AddMaxDeviceCount(v)
	})
}

// UpdateMaxDeviceCount sets the "max_device_count" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateMaxDeviceCount() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateMaxDeviceCount()
	})
}

// ClearMaxDeviceCount clears the value of the "max_device_count" field.
func (u *TenantUpsertOne) ClearMaxDeviceCount() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearMaxDeviceCount()
	})
}

// SetMaxUploadFileSize sets the "max_upload_file_size" field.
func (u *TenantUpsertOne) SetMaxUploadFileSize(v int64) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetMaxUploadFileSize(v)
	})
}

// AddMaxUploadFileSize adds v to the "max_upload_file_size" field.
func (u *TenantUpsertOne) AddMaxUploadFileSize(v int64) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.AddMaxUploadFileSize(v)
	})
}

// UpdateMaxUploadFileSize sets the "max_upload_file_size" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateMaxUploadFileSize() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateMaxUploadFileSize()
	})
}

// ClearMaxUploadFileSize clears the value of the "max_upload_file_size" field.
func (u *TenantUpsertOne) ClearMaxUploadFileSize() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearMaxUploadFileSize()
	})
}

// SetMaxUserCount sets the "max_user_count" field.
func (u *TenantUpsertOne) SetMaxUserCount(v int64) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetMaxUserCount(v)
	})
}

// AddMaxUserCount adds v to the "max_user_count" field.
func (u *TenantUpsertOne) AddMaxUserCount(v int64) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.AddMaxUserCount(v)
	})
}

// UpdateMaxUserCount sets the "max_user_count" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateMaxUserCount() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateMaxUserCount()
	})
}

// ClearMaxUserCount clears the value of the "max_user_count" field.
func (u *TenantUpsertOne) ClearMaxUserCount() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearMaxUserCount()
	})
}

// SetPrincipal sets the "principal" field.
func (u *TenantUpsertOne) SetPrincipal(v string) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetPrincipal(v)
	})
}

// UpdatePrincipal sets the "principal" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdatePrincipal() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdatePrincipal()
	})
}

// ClearPrincipal clears the value of the "principal" field.
func (u *TenantUpsertOne) ClearPrincipal() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearPrincipal()
	})
}

// SetPrincipalContactInformation sets the "principal_contact_information" field.
func (u *TenantUpsertOne) SetPrincipalContactInformation(v string) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetPrincipalContactInformation(v)
	})
}

// UpdatePrincipalContactInformation sets the "principal_contact_information" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdatePrincipalContactInformation() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdatePrincipalContactInformation()
	})
}

// ClearPrincipalContactInformation clears the value of the "principal_contact_information" field.
func (u *TenantUpsertOne) ClearPrincipalContactInformation() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearPrincipalContactInformation()
	})
}

// SetSaleContact sets the "sale_contact" field.
func (u *TenantUpsertOne) SetSaleContact(v string) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetSaleContact(v)
	})
}

// UpdateSaleContact sets the "sale_contact" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateSaleContact() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateSaleContact()
	})
}

// ClearSaleContact clears the value of the "sale_contact" field.
func (u *TenantUpsertOne) ClearSaleContact() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearSaleContact()
	})
}

// SetSecretKey sets the "secret_key" field.
func (u *TenantUpsertOne) SetSecretKey(v string) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetSecretKey(v)
	})
}

// UpdateSecretKey sets the "secret_key" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateSecretKey() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateSecretKey()
	})
}

// ClearSecretKey clears the value of the "secret_key" field.
func (u *TenantUpsertOne) ClearSecretKey() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearSecretKey()
	})
}

// SetAiStatus sets the "ai_status" field.
func (u *TenantUpsertOne) SetAiStatus(v bool) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetAiStatus(v)
	})
}

// UpdateAiStatus sets the "ai_status" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateAiStatus() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateAiStatus()
	})
}

// ClearAiStatus clears the value of the "ai_status" field.
func (u *TenantUpsertOne) ClearAiStatus() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearAiStatus()
	})
}

// SetMaxConferenceAgendaTitle sets the "max_conference_agenda_title" field.
func (u *TenantUpsertOne) SetMaxConferenceAgendaTitle(v int64) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.SetMaxConferenceAgendaTitle(v)
	})
}

// AddMaxConferenceAgendaTitle adds v to the "max_conference_agenda_title" field.
func (u *TenantUpsertOne) AddMaxConferenceAgendaTitle(v int64) *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.AddMaxConferenceAgendaTitle(v)
	})
}

// UpdateMaxConferenceAgendaTitle sets the "max_conference_agenda_title" field to the value that was provided on create.
func (u *TenantUpsertOne) UpdateMaxConferenceAgendaTitle() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateMaxConferenceAgendaTitle()
	})
}

// ClearMaxConferenceAgendaTitle clears the value of the "max_conference_agenda_title" field.
func (u *TenantUpsertOne) ClearMaxConferenceAgendaTitle() *TenantUpsertOne {
	return u.Update(func(s *TenantUpsert) {
		s.ClearMaxConferenceAgendaTitle()
	})
}

// Exec executes the query.
func (u *TenantUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for TenantCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *TenantUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *TenantUpsertOne) ID(ctx context.Context) (id string, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: TenantUpsertOne.ID is not supported by MySQL driver. Use TenantUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *TenantUpsertOne) IDX(ctx context.Context) string {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// TenantCreateBulk is the builder for creating many Tenant entities in bulk.
type TenantCreateBulk struct {
	config
	err      error
	builders []*TenantCreate
	conflict []sql.ConflictOption
}

// Save creates the Tenant entities in the database.
func (tcb *TenantCreateBulk) Save(ctx context.Context) ([]*Tenant, error) {
	if tcb.err != nil {
		return nil, tcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(tcb.builders))
	nodes := make([]*Tenant, len(tcb.builders))
	mutators := make([]Mutator, len(tcb.builders))
	for i := range tcb.builders {
		func(i int, root context.Context) {
			builder := tcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*TenantMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, tcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = tcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, tcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, tcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (tcb *TenantCreateBulk) SaveX(ctx context.Context) []*Tenant {
	v, err := tcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tcb *TenantCreateBulk) Exec(ctx context.Context) error {
	_, err := tcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tcb *TenantCreateBulk) ExecX(ctx context.Context) {
	if err := tcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Tenant.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.TenantUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (tcb *TenantCreateBulk) OnConflict(opts ...sql.ConflictOption) *TenantUpsertBulk {
	tcb.conflict = opts
	return &TenantUpsertBulk{
		create: tcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Tenant.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (tcb *TenantCreateBulk) OnConflictColumns(columns ...string) *TenantUpsertBulk {
	tcb.conflict = append(tcb.conflict, sql.ConflictColumns(columns...))
	return &TenantUpsertBulk{
		create: tcb,
	}
}

// TenantUpsertBulk is the builder for "upsert"-ing
// a bulk of Tenant nodes.
type TenantUpsertBulk struct {
	create *TenantCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Tenant.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(tenant.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *TenantUpsertBulk) UpdateNewValues() *TenantUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(tenant.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(tenant.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Tenant.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *TenantUpsertBulk) Ignore() *TenantUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *TenantUpsertBulk) DoNothing() *TenantUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the TenantCreateBulk.OnConflict
// documentation for more info.
func (u *TenantUpsertBulk) Update(set func(*TenantUpsert)) *TenantUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&TenantUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *TenantUpsertBulk) SetUpdatedAt(v time.Time) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateUpdatedAt() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetUUID sets the "uuid" field.
func (u *TenantUpsertBulk) SetUUID(v uuid.UUID) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetUUID(v)
	})
}

// UpdateUUID sets the "uuid" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateUUID() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateUUID()
	})
}

// SetKey sets the "key" field.
func (u *TenantUpsertBulk) SetKey(v string) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetKey(v)
	})
}

// UpdateKey sets the "key" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateKey() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateKey()
	})
}

// SetSecret sets the "secret" field.
func (u *TenantUpsertBulk) SetSecret(v string) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetSecret(v)
	})
}

// UpdateSecret sets the "secret" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateSecret() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateSecret()
	})
}

// ClearSecret clears the value of the "secret" field.
func (u *TenantUpsertBulk) ClearSecret() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearSecret()
	})
}

// SetStatus sets the "status" field.
func (u *TenantUpsertBulk) SetStatus(v bool) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateStatus() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *TenantUpsertBulk) ClearStatus() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearStatus()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *TenantUpsertBulk) SetDeletedAt(v time.Time) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateDeletedAt() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *TenantUpsertBulk) ClearDeletedAt() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearDeletedAt()
	})
}

// SetName sets the "name" field.
func (u *TenantUpsertBulk) SetName(v string) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateName() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateName()
	})
}

// ClearName clears the value of the "name" field.
func (u *TenantUpsertBulk) ClearName() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearName()
	})
}

// SetIsSuper sets the "is_super" field.
func (u *TenantUpsertBulk) SetIsSuper(v bool) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetIsSuper(v)
	})
}

// UpdateIsSuper sets the "is_super" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateIsSuper() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateIsSuper()
	})
}

// ClearIsSuper clears the value of the "is_super" field.
func (u *TenantUpsertBulk) ClearIsSuper() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearIsSuper()
	})
}

// SetServiceStartAt sets the "service_start_at" field.
func (u *TenantUpsertBulk) SetServiceStartAt(v time.Time) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetServiceStartAt(v)
	})
}

// UpdateServiceStartAt sets the "service_start_at" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateServiceStartAt() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateServiceStartAt()
	})
}

// ClearServiceStartAt clears the value of the "service_start_at" field.
func (u *TenantUpsertBulk) ClearServiceStartAt() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearServiceStartAt()
	})
}

// SetServiceEndAt sets the "service_end_at" field.
func (u *TenantUpsertBulk) SetServiceEndAt(v time.Time) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetServiceEndAt(v)
	})
}

// UpdateServiceEndAt sets the "service_end_at" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateServiceEndAt() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateServiceEndAt()
	})
}

// ClearServiceEndAt clears the value of the "service_end_at" field.
func (u *TenantUpsertBulk) ClearServiceEndAt() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearServiceEndAt()
	})
}

// SetAfterSalesContact sets the "after_sales_contact" field.
func (u *TenantUpsertBulk) SetAfterSalesContact(v string) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetAfterSalesContact(v)
	})
}

// UpdateAfterSalesContact sets the "after_sales_contact" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateAfterSalesContact() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateAfterSalesContact()
	})
}

// ClearAfterSalesContact clears the value of the "after_sales_contact" field.
func (u *TenantUpsertBulk) ClearAfterSalesContact() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearAfterSalesContact()
	})
}

// SetLocationID sets the "location_id" field.
func (u *TenantUpsertBulk) SetLocationID(v string) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetLocationID(v)
	})
}

// UpdateLocationID sets the "location_id" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateLocationID() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateLocationID()
	})
}

// ClearLocationID clears the value of the "location_id" field.
func (u *TenantUpsertBulk) ClearLocationID() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearLocationID()
	})
}

// SetLogSaveKeepDays sets the "log_save_keep_days" field.
func (u *TenantUpsertBulk) SetLogSaveKeepDays(v int64) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetLogSaveKeepDays(v)
	})
}

// AddLogSaveKeepDays adds v to the "log_save_keep_days" field.
func (u *TenantUpsertBulk) AddLogSaveKeepDays(v int64) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.AddLogSaveKeepDays(v)
	})
}

// UpdateLogSaveKeepDays sets the "log_save_keep_days" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateLogSaveKeepDays() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateLogSaveKeepDays()
	})
}

// ClearLogSaveKeepDays clears the value of the "log_save_keep_days" field.
func (u *TenantUpsertBulk) ClearLogSaveKeepDays() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearLogSaveKeepDays()
	})
}

// SetMaxAttendanceUserCount sets the "max_attendance_user_count" field.
func (u *TenantUpsertBulk) SetMaxAttendanceUserCount(v int64) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetMaxAttendanceUserCount(v)
	})
}

// AddMaxAttendanceUserCount adds v to the "max_attendance_user_count" field.
func (u *TenantUpsertBulk) AddMaxAttendanceUserCount(v int64) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.AddMaxAttendanceUserCount(v)
	})
}

// UpdateMaxAttendanceUserCount sets the "max_attendance_user_count" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateMaxAttendanceUserCount() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateMaxAttendanceUserCount()
	})
}

// ClearMaxAttendanceUserCount clears the value of the "max_attendance_user_count" field.
func (u *TenantUpsertBulk) ClearMaxAttendanceUserCount() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearMaxAttendanceUserCount()
	})
}

// SetMaxDeviceCount sets the "max_device_count" field.
func (u *TenantUpsertBulk) SetMaxDeviceCount(v int64) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetMaxDeviceCount(v)
	})
}

// AddMaxDeviceCount adds v to the "max_device_count" field.
func (u *TenantUpsertBulk) AddMaxDeviceCount(v int64) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.AddMaxDeviceCount(v)
	})
}

// UpdateMaxDeviceCount sets the "max_device_count" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateMaxDeviceCount() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateMaxDeviceCount()
	})
}

// ClearMaxDeviceCount clears the value of the "max_device_count" field.
func (u *TenantUpsertBulk) ClearMaxDeviceCount() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearMaxDeviceCount()
	})
}

// SetMaxUploadFileSize sets the "max_upload_file_size" field.
func (u *TenantUpsertBulk) SetMaxUploadFileSize(v int64) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetMaxUploadFileSize(v)
	})
}

// AddMaxUploadFileSize adds v to the "max_upload_file_size" field.
func (u *TenantUpsertBulk) AddMaxUploadFileSize(v int64) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.AddMaxUploadFileSize(v)
	})
}

// UpdateMaxUploadFileSize sets the "max_upload_file_size" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateMaxUploadFileSize() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateMaxUploadFileSize()
	})
}

// ClearMaxUploadFileSize clears the value of the "max_upload_file_size" field.
func (u *TenantUpsertBulk) ClearMaxUploadFileSize() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearMaxUploadFileSize()
	})
}

// SetMaxUserCount sets the "max_user_count" field.
func (u *TenantUpsertBulk) SetMaxUserCount(v int64) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetMaxUserCount(v)
	})
}

// AddMaxUserCount adds v to the "max_user_count" field.
func (u *TenantUpsertBulk) AddMaxUserCount(v int64) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.AddMaxUserCount(v)
	})
}

// UpdateMaxUserCount sets the "max_user_count" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateMaxUserCount() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateMaxUserCount()
	})
}

// ClearMaxUserCount clears the value of the "max_user_count" field.
func (u *TenantUpsertBulk) ClearMaxUserCount() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearMaxUserCount()
	})
}

// SetPrincipal sets the "principal" field.
func (u *TenantUpsertBulk) SetPrincipal(v string) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetPrincipal(v)
	})
}

// UpdatePrincipal sets the "principal" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdatePrincipal() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdatePrincipal()
	})
}

// ClearPrincipal clears the value of the "principal" field.
func (u *TenantUpsertBulk) ClearPrincipal() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearPrincipal()
	})
}

// SetPrincipalContactInformation sets the "principal_contact_information" field.
func (u *TenantUpsertBulk) SetPrincipalContactInformation(v string) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetPrincipalContactInformation(v)
	})
}

// UpdatePrincipalContactInformation sets the "principal_contact_information" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdatePrincipalContactInformation() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdatePrincipalContactInformation()
	})
}

// ClearPrincipalContactInformation clears the value of the "principal_contact_information" field.
func (u *TenantUpsertBulk) ClearPrincipalContactInformation() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearPrincipalContactInformation()
	})
}

// SetSaleContact sets the "sale_contact" field.
func (u *TenantUpsertBulk) SetSaleContact(v string) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetSaleContact(v)
	})
}

// UpdateSaleContact sets the "sale_contact" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateSaleContact() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateSaleContact()
	})
}

// ClearSaleContact clears the value of the "sale_contact" field.
func (u *TenantUpsertBulk) ClearSaleContact() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearSaleContact()
	})
}

// SetSecretKey sets the "secret_key" field.
func (u *TenantUpsertBulk) SetSecretKey(v string) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetSecretKey(v)
	})
}

// UpdateSecretKey sets the "secret_key" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateSecretKey() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateSecretKey()
	})
}

// ClearSecretKey clears the value of the "secret_key" field.
func (u *TenantUpsertBulk) ClearSecretKey() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearSecretKey()
	})
}

// SetAiStatus sets the "ai_status" field.
func (u *TenantUpsertBulk) SetAiStatus(v bool) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetAiStatus(v)
	})
}

// UpdateAiStatus sets the "ai_status" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateAiStatus() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateAiStatus()
	})
}

// ClearAiStatus clears the value of the "ai_status" field.
func (u *TenantUpsertBulk) ClearAiStatus() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearAiStatus()
	})
}

// SetMaxConferenceAgendaTitle sets the "max_conference_agenda_title" field.
func (u *TenantUpsertBulk) SetMaxConferenceAgendaTitle(v int64) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.SetMaxConferenceAgendaTitle(v)
	})
}

// AddMaxConferenceAgendaTitle adds v to the "max_conference_agenda_title" field.
func (u *TenantUpsertBulk) AddMaxConferenceAgendaTitle(v int64) *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.AddMaxConferenceAgendaTitle(v)
	})
}

// UpdateMaxConferenceAgendaTitle sets the "max_conference_agenda_title" field to the value that was provided on create.
func (u *TenantUpsertBulk) UpdateMaxConferenceAgendaTitle() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.UpdateMaxConferenceAgendaTitle()
	})
}

// ClearMaxConferenceAgendaTitle clears the value of the "max_conference_agenda_title" field.
func (u *TenantUpsertBulk) ClearMaxConferenceAgendaTitle() *TenantUpsertBulk {
	return u.Update(func(s *TenantUpsert) {
		s.ClearMaxConferenceAgendaTitle()
	})
}

// Exec executes the query.
func (u *TenantUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the TenantCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for TenantCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *TenantUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
