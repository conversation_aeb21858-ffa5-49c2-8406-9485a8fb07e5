// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/api"
	"phoenix/service/saas/model/ent/button"
	"phoenix/service/saas/model/ent/group"
	"phoenix/service/saas/model/ent/menu"
	"phoenix/service/saas/model/ent/predicate"
	"phoenix/service/saas/model/ent/role"
	"phoenix/service/saas/model/ent/tenant"
	"phoenix/service/saas/model/ent/user"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// RoleUpdate is the builder for updating Role entities.
type RoleUpdate struct {
	config
	hooks    []Hook
	mutation *RoleMutation
}

// Where appends a list predicates to the RoleUpdate builder.
func (ru *RoleUpdate) Where(ps ...predicate.Role) *RoleUpdate {
	ru.mutation.Where(ps...)
	return ru
}

// SetUpdatedAt sets the "updated_at" field.
func (ru *RoleUpdate) SetUpdatedAt(t time.Time) *RoleUpdate {
	ru.mutation.SetUpdatedAt(t)
	return ru
}

// SetStatus sets the "status" field.
func (ru *RoleUpdate) SetStatus(b bool) *RoleUpdate {
	ru.mutation.SetStatus(b)
	return ru
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ru *RoleUpdate) SetNillableStatus(b *bool) *RoleUpdate {
	if b != nil {
		ru.SetStatus(*b)
	}
	return ru
}

// ClearStatus clears the value of the "status" field.
func (ru *RoleUpdate) ClearStatus() *RoleUpdate {
	ru.mutation.ClearStatus()
	return ru
}

// SetSort sets the "sort" field.
func (ru *RoleUpdate) SetSort(u uint32) *RoleUpdate {
	ru.mutation.ResetSort()
	ru.mutation.SetSort(u)
	return ru
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (ru *RoleUpdate) SetNillableSort(u *uint32) *RoleUpdate {
	if u != nil {
		ru.SetSort(*u)
	}
	return ru
}

// AddSort adds u to the "sort" field.
func (ru *RoleUpdate) AddSort(u int32) *RoleUpdate {
	ru.mutation.AddSort(u)
	return ru
}

// SetTenantID sets the "tenant_id" field.
func (ru *RoleUpdate) SetTenantID(s string) *RoleUpdate {
	ru.mutation.SetTenantID(s)
	return ru
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (ru *RoleUpdate) SetNillableTenantID(s *string) *RoleUpdate {
	if s != nil {
		ru.SetTenantID(*s)
	}
	return ru
}

// SetDeletedAt sets the "deleted_at" field.
func (ru *RoleUpdate) SetDeletedAt(t time.Time) *RoleUpdate {
	ru.mutation.SetDeletedAt(t)
	return ru
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (ru *RoleUpdate) SetNillableDeletedAt(t *time.Time) *RoleUpdate {
	if t != nil {
		ru.SetDeletedAt(*t)
	}
	return ru
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (ru *RoleUpdate) ClearDeletedAt() *RoleUpdate {
	ru.mutation.ClearDeletedAt()
	return ru
}

// SetName sets the "name" field.
func (ru *RoleUpdate) SetName(s string) *RoleUpdate {
	ru.mutation.SetName(s)
	return ru
}

// SetNillableName sets the "name" field if the given value is not nil.
func (ru *RoleUpdate) SetNillableName(s *string) *RoleUpdate {
	if s != nil {
		ru.SetName(*s)
	}
	return ru
}

// SetCode sets the "code" field.
func (ru *RoleUpdate) SetCode(s string) *RoleUpdate {
	ru.mutation.SetCode(s)
	return ru
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (ru *RoleUpdate) SetNillableCode(s *string) *RoleUpdate {
	if s != nil {
		ru.SetCode(*s)
	}
	return ru
}

// SetDefaultRouter sets the "default_router" field.
func (ru *RoleUpdate) SetDefaultRouter(s string) *RoleUpdate {
	ru.mutation.SetDefaultRouter(s)
	return ru
}

// SetNillableDefaultRouter sets the "default_router" field if the given value is not nil.
func (ru *RoleUpdate) SetNillableDefaultRouter(s *string) *RoleUpdate {
	if s != nil {
		ru.SetDefaultRouter(*s)
	}
	return ru
}

// SetRemark sets the "remark" field.
func (ru *RoleUpdate) SetRemark(s string) *RoleUpdate {
	ru.mutation.SetRemark(s)
	return ru
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (ru *RoleUpdate) SetNillableRemark(s *string) *RoleUpdate {
	if s != nil {
		ru.SetRemark(*s)
	}
	return ru
}

// SetOrganizationID sets the "organization_id" field.
func (ru *RoleUpdate) SetOrganizationID(s string) *RoleUpdate {
	ru.mutation.SetOrganizationID(s)
	return ru
}

// SetNillableOrganizationID sets the "organization_id" field if the given value is not nil.
func (ru *RoleUpdate) SetNillableOrganizationID(s *string) *RoleUpdate {
	if s != nil {
		ru.SetOrganizationID(*s)
	}
	return ru
}

// SetParentID sets the "parent_id" field.
func (ru *RoleUpdate) SetParentID(s string) *RoleUpdate {
	ru.mutation.SetParentID(s)
	return ru
}

// SetNillableParentID sets the "parent_id" field if the given value is not nil.
func (ru *RoleUpdate) SetNillableParentID(s *string) *RoleUpdate {
	if s != nil {
		ru.SetParentID(*s)
	}
	return ru
}

// ClearParentID clears the value of the "parent_id" field.
func (ru *RoleUpdate) ClearParentID() *RoleUpdate {
	ru.mutation.ClearParentID()
	return ru
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (ru *RoleUpdate) SetTenant(t *Tenant) *RoleUpdate {
	return ru.SetTenantID(t.ID)
}

// AddButtonIDs adds the "buttons" edge to the Button entity by IDs.
func (ru *RoleUpdate) AddButtonIDs(ids ...string) *RoleUpdate {
	ru.mutation.AddButtonIDs(ids...)
	return ru
}

// AddButtons adds the "buttons" edges to the Button entity.
func (ru *RoleUpdate) AddButtons(b ...*Button) *RoleUpdate {
	ids := make([]string, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return ru.AddButtonIDs(ids...)
}

// AddMenuIDs adds the "menus" edge to the Menu entity by IDs.
func (ru *RoleUpdate) AddMenuIDs(ids ...string) *RoleUpdate {
	ru.mutation.AddMenuIDs(ids...)
	return ru
}

// AddMenus adds the "menus" edges to the Menu entity.
func (ru *RoleUpdate) AddMenus(m ...*Menu) *RoleUpdate {
	ids := make([]string, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return ru.AddMenuIDs(ids...)
}

// AddAPIIDs adds the "apis" edge to the API entity by IDs.
func (ru *RoleUpdate) AddAPIIDs(ids ...string) *RoleUpdate {
	ru.mutation.AddAPIIDs(ids...)
	return ru
}

// AddApis adds the "apis" edges to the API entity.
func (ru *RoleUpdate) AddApis(a ...*API) *RoleUpdate {
	ids := make([]string, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return ru.AddAPIIDs(ids...)
}

// AddGroupIDs adds the "groups" edge to the Group entity by IDs.
func (ru *RoleUpdate) AddGroupIDs(ids ...string) *RoleUpdate {
	ru.mutation.AddGroupIDs(ids...)
	return ru
}

// AddGroups adds the "groups" edges to the Group entity.
func (ru *RoleUpdate) AddGroups(g ...*Group) *RoleUpdate {
	ids := make([]string, len(g))
	for i := range g {
		ids[i] = g[i].ID
	}
	return ru.AddGroupIDs(ids...)
}

// AddUserIDs adds the "users" edge to the User entity by IDs.
func (ru *RoleUpdate) AddUserIDs(ids ...string) *RoleUpdate {
	ru.mutation.AddUserIDs(ids...)
	return ru
}

// AddUsers adds the "users" edges to the User entity.
func (ru *RoleUpdate) AddUsers(u ...*User) *RoleUpdate {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return ru.AddUserIDs(ids...)
}

// SetParent sets the "parent" edge to the Role entity.
func (ru *RoleUpdate) SetParent(r *Role) *RoleUpdate {
	return ru.SetParentID(r.ID)
}

// AddChildIDs adds the "children" edge to the Role entity by IDs.
func (ru *RoleUpdate) AddChildIDs(ids ...string) *RoleUpdate {
	ru.mutation.AddChildIDs(ids...)
	return ru
}

// AddChildren adds the "children" edges to the Role entity.
func (ru *RoleUpdate) AddChildren(r ...*Role) *RoleUpdate {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return ru.AddChildIDs(ids...)
}

// Mutation returns the RoleMutation object of the builder.
func (ru *RoleUpdate) Mutation() *RoleMutation {
	return ru.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (ru *RoleUpdate) ClearTenant() *RoleUpdate {
	ru.mutation.ClearTenant()
	return ru
}

// ClearButtons clears all "buttons" edges to the Button entity.
func (ru *RoleUpdate) ClearButtons() *RoleUpdate {
	ru.mutation.ClearButtons()
	return ru
}

// RemoveButtonIDs removes the "buttons" edge to Button entities by IDs.
func (ru *RoleUpdate) RemoveButtonIDs(ids ...string) *RoleUpdate {
	ru.mutation.RemoveButtonIDs(ids...)
	return ru
}

// RemoveButtons removes "buttons" edges to Button entities.
func (ru *RoleUpdate) RemoveButtons(b ...*Button) *RoleUpdate {
	ids := make([]string, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return ru.RemoveButtonIDs(ids...)
}

// ClearMenus clears all "menus" edges to the Menu entity.
func (ru *RoleUpdate) ClearMenus() *RoleUpdate {
	ru.mutation.ClearMenus()
	return ru
}

// RemoveMenuIDs removes the "menus" edge to Menu entities by IDs.
func (ru *RoleUpdate) RemoveMenuIDs(ids ...string) *RoleUpdate {
	ru.mutation.RemoveMenuIDs(ids...)
	return ru
}

// RemoveMenus removes "menus" edges to Menu entities.
func (ru *RoleUpdate) RemoveMenus(m ...*Menu) *RoleUpdate {
	ids := make([]string, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return ru.RemoveMenuIDs(ids...)
}

// ClearApis clears all "apis" edges to the API entity.
func (ru *RoleUpdate) ClearApis() *RoleUpdate {
	ru.mutation.ClearApis()
	return ru
}

// RemoveAPIIDs removes the "apis" edge to API entities by IDs.
func (ru *RoleUpdate) RemoveAPIIDs(ids ...string) *RoleUpdate {
	ru.mutation.RemoveAPIIDs(ids...)
	return ru
}

// RemoveApis removes "apis" edges to API entities.
func (ru *RoleUpdate) RemoveApis(a ...*API) *RoleUpdate {
	ids := make([]string, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return ru.RemoveAPIIDs(ids...)
}

// ClearGroups clears all "groups" edges to the Group entity.
func (ru *RoleUpdate) ClearGroups() *RoleUpdate {
	ru.mutation.ClearGroups()
	return ru
}

// RemoveGroupIDs removes the "groups" edge to Group entities by IDs.
func (ru *RoleUpdate) RemoveGroupIDs(ids ...string) *RoleUpdate {
	ru.mutation.RemoveGroupIDs(ids...)
	return ru
}

// RemoveGroups removes "groups" edges to Group entities.
func (ru *RoleUpdate) RemoveGroups(g ...*Group) *RoleUpdate {
	ids := make([]string, len(g))
	for i := range g {
		ids[i] = g[i].ID
	}
	return ru.RemoveGroupIDs(ids...)
}

// ClearUsers clears all "users" edges to the User entity.
func (ru *RoleUpdate) ClearUsers() *RoleUpdate {
	ru.mutation.ClearUsers()
	return ru
}

// RemoveUserIDs removes the "users" edge to User entities by IDs.
func (ru *RoleUpdate) RemoveUserIDs(ids ...string) *RoleUpdate {
	ru.mutation.RemoveUserIDs(ids...)
	return ru
}

// RemoveUsers removes "users" edges to User entities.
func (ru *RoleUpdate) RemoveUsers(u ...*User) *RoleUpdate {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return ru.RemoveUserIDs(ids...)
}

// ClearParent clears the "parent" edge to the Role entity.
func (ru *RoleUpdate) ClearParent() *RoleUpdate {
	ru.mutation.ClearParent()
	return ru
}

// ClearChildren clears all "children" edges to the Role entity.
func (ru *RoleUpdate) ClearChildren() *RoleUpdate {
	ru.mutation.ClearChildren()
	return ru
}

// RemoveChildIDs removes the "children" edge to Role entities by IDs.
func (ru *RoleUpdate) RemoveChildIDs(ids ...string) *RoleUpdate {
	ru.mutation.RemoveChildIDs(ids...)
	return ru
}

// RemoveChildren removes "children" edges to Role entities.
func (ru *RoleUpdate) RemoveChildren(r ...*Role) *RoleUpdate {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return ru.RemoveChildIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (ru *RoleUpdate) Save(ctx context.Context) (int, error) {
	if err := ru.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, ru.sqlSave, ru.mutation, ru.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ru *RoleUpdate) SaveX(ctx context.Context) int {
	affected, err := ru.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (ru *RoleUpdate) Exec(ctx context.Context) error {
	_, err := ru.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ru *RoleUpdate) ExecX(ctx context.Context) {
	if err := ru.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ru *RoleUpdate) defaults() error {
	if _, ok := ru.mutation.UpdatedAt(); !ok {
		if role.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized role.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := role.UpdateDefaultUpdatedAt()
		ru.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (ru *RoleUpdate) check() error {
	if _, ok := ru.mutation.TenantID(); ru.mutation.TenantCleared() && !ok {
		return errors.New(`ent: clearing a required unique edge "Role.tenant"`)
	}
	return nil
}

func (ru *RoleUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := ru.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(role.Table, role.Columns, sqlgraph.NewFieldSpec(role.FieldID, field.TypeString))
	if ps := ru.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ru.mutation.UpdatedAt(); ok {
		_spec.SetField(role.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := ru.mutation.Status(); ok {
		_spec.SetField(role.FieldStatus, field.TypeBool, value)
	}
	if ru.mutation.StatusCleared() {
		_spec.ClearField(role.FieldStatus, field.TypeBool)
	}
	if value, ok := ru.mutation.Sort(); ok {
		_spec.SetField(role.FieldSort, field.TypeUint32, value)
	}
	if value, ok := ru.mutation.AddedSort(); ok {
		_spec.AddField(role.FieldSort, field.TypeUint32, value)
	}
	if value, ok := ru.mutation.DeletedAt(); ok {
		_spec.SetField(role.FieldDeletedAt, field.TypeTime, value)
	}
	if ru.mutation.DeletedAtCleared() {
		_spec.ClearField(role.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := ru.mutation.Name(); ok {
		_spec.SetField(role.FieldName, field.TypeString, value)
	}
	if value, ok := ru.mutation.Code(); ok {
		_spec.SetField(role.FieldCode, field.TypeString, value)
	}
	if value, ok := ru.mutation.DefaultRouter(); ok {
		_spec.SetField(role.FieldDefaultRouter, field.TypeString, value)
	}
	if value, ok := ru.mutation.Remark(); ok {
		_spec.SetField(role.FieldRemark, field.TypeString, value)
	}
	if value, ok := ru.mutation.OrganizationID(); ok {
		_spec.SetField(role.FieldOrganizationID, field.TypeString, value)
	}
	if ru.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   role.TenantTable,
			Columns: []string{role.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ru.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   role.TenantTable,
			Columns: []string{role.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ru.mutation.ButtonsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.ButtonsTable,
			Columns: role.ButtonsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(button.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ru.mutation.RemovedButtonsIDs(); len(nodes) > 0 && !ru.mutation.ButtonsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.ButtonsTable,
			Columns: role.ButtonsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(button.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ru.mutation.ButtonsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.ButtonsTable,
			Columns: role.ButtonsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(button.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ru.mutation.MenusCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.MenusTable,
			Columns: role.MenusPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ru.mutation.RemovedMenusIDs(); len(nodes) > 0 && !ru.mutation.MenusCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.MenusTable,
			Columns: role.MenusPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ru.mutation.MenusIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.MenusTable,
			Columns: role.MenusPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ru.mutation.ApisCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.ApisTable,
			Columns: role.ApisPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(api.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ru.mutation.RemovedApisIDs(); len(nodes) > 0 && !ru.mutation.ApisCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.ApisTable,
			Columns: role.ApisPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(api.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ru.mutation.ApisIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.ApisTable,
			Columns: role.ApisPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(api.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ru.mutation.GroupsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.GroupsTable,
			Columns: role.GroupsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(group.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ru.mutation.RemovedGroupsIDs(); len(nodes) > 0 && !ru.mutation.GroupsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.GroupsTable,
			Columns: role.GroupsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(group.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ru.mutation.GroupsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.GroupsTable,
			Columns: role.GroupsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(group.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ru.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.UsersTable,
			Columns: role.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ru.mutation.RemovedUsersIDs(); len(nodes) > 0 && !ru.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.UsersTable,
			Columns: role.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ru.mutation.UsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.UsersTable,
			Columns: role.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ru.mutation.ParentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   role.ParentTable,
			Columns: []string{role.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ru.mutation.ParentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   role.ParentTable,
			Columns: []string{role.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ru.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   role.ChildrenTable,
			Columns: []string{role.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ru.mutation.RemovedChildrenIDs(); len(nodes) > 0 && !ru.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   role.ChildrenTable,
			Columns: []string{role.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ru.mutation.ChildrenIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   role.ChildrenTable,
			Columns: []string{role.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, ru.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{role.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	ru.mutation.done = true
	return n, nil
}

// RoleUpdateOne is the builder for updating a single Role entity.
type RoleUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *RoleMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (ruo *RoleUpdateOne) SetUpdatedAt(t time.Time) *RoleUpdateOne {
	ruo.mutation.SetUpdatedAt(t)
	return ruo
}

// SetStatus sets the "status" field.
func (ruo *RoleUpdateOne) SetStatus(b bool) *RoleUpdateOne {
	ruo.mutation.SetStatus(b)
	return ruo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (ruo *RoleUpdateOne) SetNillableStatus(b *bool) *RoleUpdateOne {
	if b != nil {
		ruo.SetStatus(*b)
	}
	return ruo
}

// ClearStatus clears the value of the "status" field.
func (ruo *RoleUpdateOne) ClearStatus() *RoleUpdateOne {
	ruo.mutation.ClearStatus()
	return ruo
}

// SetSort sets the "sort" field.
func (ruo *RoleUpdateOne) SetSort(u uint32) *RoleUpdateOne {
	ruo.mutation.ResetSort()
	ruo.mutation.SetSort(u)
	return ruo
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (ruo *RoleUpdateOne) SetNillableSort(u *uint32) *RoleUpdateOne {
	if u != nil {
		ruo.SetSort(*u)
	}
	return ruo
}

// AddSort adds u to the "sort" field.
func (ruo *RoleUpdateOne) AddSort(u int32) *RoleUpdateOne {
	ruo.mutation.AddSort(u)
	return ruo
}

// SetTenantID sets the "tenant_id" field.
func (ruo *RoleUpdateOne) SetTenantID(s string) *RoleUpdateOne {
	ruo.mutation.SetTenantID(s)
	return ruo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (ruo *RoleUpdateOne) SetNillableTenantID(s *string) *RoleUpdateOne {
	if s != nil {
		ruo.SetTenantID(*s)
	}
	return ruo
}

// SetDeletedAt sets the "deleted_at" field.
func (ruo *RoleUpdateOne) SetDeletedAt(t time.Time) *RoleUpdateOne {
	ruo.mutation.SetDeletedAt(t)
	return ruo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (ruo *RoleUpdateOne) SetNillableDeletedAt(t *time.Time) *RoleUpdateOne {
	if t != nil {
		ruo.SetDeletedAt(*t)
	}
	return ruo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (ruo *RoleUpdateOne) ClearDeletedAt() *RoleUpdateOne {
	ruo.mutation.ClearDeletedAt()
	return ruo
}

// SetName sets the "name" field.
func (ruo *RoleUpdateOne) SetName(s string) *RoleUpdateOne {
	ruo.mutation.SetName(s)
	return ruo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (ruo *RoleUpdateOne) SetNillableName(s *string) *RoleUpdateOne {
	if s != nil {
		ruo.SetName(*s)
	}
	return ruo
}

// SetCode sets the "code" field.
func (ruo *RoleUpdateOne) SetCode(s string) *RoleUpdateOne {
	ruo.mutation.SetCode(s)
	return ruo
}

// SetNillableCode sets the "code" field if the given value is not nil.
func (ruo *RoleUpdateOne) SetNillableCode(s *string) *RoleUpdateOne {
	if s != nil {
		ruo.SetCode(*s)
	}
	return ruo
}

// SetDefaultRouter sets the "default_router" field.
func (ruo *RoleUpdateOne) SetDefaultRouter(s string) *RoleUpdateOne {
	ruo.mutation.SetDefaultRouter(s)
	return ruo
}

// SetNillableDefaultRouter sets the "default_router" field if the given value is not nil.
func (ruo *RoleUpdateOne) SetNillableDefaultRouter(s *string) *RoleUpdateOne {
	if s != nil {
		ruo.SetDefaultRouter(*s)
	}
	return ruo
}

// SetRemark sets the "remark" field.
func (ruo *RoleUpdateOne) SetRemark(s string) *RoleUpdateOne {
	ruo.mutation.SetRemark(s)
	return ruo
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (ruo *RoleUpdateOne) SetNillableRemark(s *string) *RoleUpdateOne {
	if s != nil {
		ruo.SetRemark(*s)
	}
	return ruo
}

// SetOrganizationID sets the "organization_id" field.
func (ruo *RoleUpdateOne) SetOrganizationID(s string) *RoleUpdateOne {
	ruo.mutation.SetOrganizationID(s)
	return ruo
}

// SetNillableOrganizationID sets the "organization_id" field if the given value is not nil.
func (ruo *RoleUpdateOne) SetNillableOrganizationID(s *string) *RoleUpdateOne {
	if s != nil {
		ruo.SetOrganizationID(*s)
	}
	return ruo
}

// SetParentID sets the "parent_id" field.
func (ruo *RoleUpdateOne) SetParentID(s string) *RoleUpdateOne {
	ruo.mutation.SetParentID(s)
	return ruo
}

// SetNillableParentID sets the "parent_id" field if the given value is not nil.
func (ruo *RoleUpdateOne) SetNillableParentID(s *string) *RoleUpdateOne {
	if s != nil {
		ruo.SetParentID(*s)
	}
	return ruo
}

// ClearParentID clears the value of the "parent_id" field.
func (ruo *RoleUpdateOne) ClearParentID() *RoleUpdateOne {
	ruo.mutation.ClearParentID()
	return ruo
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (ruo *RoleUpdateOne) SetTenant(t *Tenant) *RoleUpdateOne {
	return ruo.SetTenantID(t.ID)
}

// AddButtonIDs adds the "buttons" edge to the Button entity by IDs.
func (ruo *RoleUpdateOne) AddButtonIDs(ids ...string) *RoleUpdateOne {
	ruo.mutation.AddButtonIDs(ids...)
	return ruo
}

// AddButtons adds the "buttons" edges to the Button entity.
func (ruo *RoleUpdateOne) AddButtons(b ...*Button) *RoleUpdateOne {
	ids := make([]string, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return ruo.AddButtonIDs(ids...)
}

// AddMenuIDs adds the "menus" edge to the Menu entity by IDs.
func (ruo *RoleUpdateOne) AddMenuIDs(ids ...string) *RoleUpdateOne {
	ruo.mutation.AddMenuIDs(ids...)
	return ruo
}

// AddMenus adds the "menus" edges to the Menu entity.
func (ruo *RoleUpdateOne) AddMenus(m ...*Menu) *RoleUpdateOne {
	ids := make([]string, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return ruo.AddMenuIDs(ids...)
}

// AddAPIIDs adds the "apis" edge to the API entity by IDs.
func (ruo *RoleUpdateOne) AddAPIIDs(ids ...string) *RoleUpdateOne {
	ruo.mutation.AddAPIIDs(ids...)
	return ruo
}

// AddApis adds the "apis" edges to the API entity.
func (ruo *RoleUpdateOne) AddApis(a ...*API) *RoleUpdateOne {
	ids := make([]string, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return ruo.AddAPIIDs(ids...)
}

// AddGroupIDs adds the "groups" edge to the Group entity by IDs.
func (ruo *RoleUpdateOne) AddGroupIDs(ids ...string) *RoleUpdateOne {
	ruo.mutation.AddGroupIDs(ids...)
	return ruo
}

// AddGroups adds the "groups" edges to the Group entity.
func (ruo *RoleUpdateOne) AddGroups(g ...*Group) *RoleUpdateOne {
	ids := make([]string, len(g))
	for i := range g {
		ids[i] = g[i].ID
	}
	return ruo.AddGroupIDs(ids...)
}

// AddUserIDs adds the "users" edge to the User entity by IDs.
func (ruo *RoleUpdateOne) AddUserIDs(ids ...string) *RoleUpdateOne {
	ruo.mutation.AddUserIDs(ids...)
	return ruo
}

// AddUsers adds the "users" edges to the User entity.
func (ruo *RoleUpdateOne) AddUsers(u ...*User) *RoleUpdateOne {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return ruo.AddUserIDs(ids...)
}

// SetParent sets the "parent" edge to the Role entity.
func (ruo *RoleUpdateOne) SetParent(r *Role) *RoleUpdateOne {
	return ruo.SetParentID(r.ID)
}

// AddChildIDs adds the "children" edge to the Role entity by IDs.
func (ruo *RoleUpdateOne) AddChildIDs(ids ...string) *RoleUpdateOne {
	ruo.mutation.AddChildIDs(ids...)
	return ruo
}

// AddChildren adds the "children" edges to the Role entity.
func (ruo *RoleUpdateOne) AddChildren(r ...*Role) *RoleUpdateOne {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return ruo.AddChildIDs(ids...)
}

// Mutation returns the RoleMutation object of the builder.
func (ruo *RoleUpdateOne) Mutation() *RoleMutation {
	return ruo.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (ruo *RoleUpdateOne) ClearTenant() *RoleUpdateOne {
	ruo.mutation.ClearTenant()
	return ruo
}

// ClearButtons clears all "buttons" edges to the Button entity.
func (ruo *RoleUpdateOne) ClearButtons() *RoleUpdateOne {
	ruo.mutation.ClearButtons()
	return ruo
}

// RemoveButtonIDs removes the "buttons" edge to Button entities by IDs.
func (ruo *RoleUpdateOne) RemoveButtonIDs(ids ...string) *RoleUpdateOne {
	ruo.mutation.RemoveButtonIDs(ids...)
	return ruo
}

// RemoveButtons removes "buttons" edges to Button entities.
func (ruo *RoleUpdateOne) RemoveButtons(b ...*Button) *RoleUpdateOne {
	ids := make([]string, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return ruo.RemoveButtonIDs(ids...)
}

// ClearMenus clears all "menus" edges to the Menu entity.
func (ruo *RoleUpdateOne) ClearMenus() *RoleUpdateOne {
	ruo.mutation.ClearMenus()
	return ruo
}

// RemoveMenuIDs removes the "menus" edge to Menu entities by IDs.
func (ruo *RoleUpdateOne) RemoveMenuIDs(ids ...string) *RoleUpdateOne {
	ruo.mutation.RemoveMenuIDs(ids...)
	return ruo
}

// RemoveMenus removes "menus" edges to Menu entities.
func (ruo *RoleUpdateOne) RemoveMenus(m ...*Menu) *RoleUpdateOne {
	ids := make([]string, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return ruo.RemoveMenuIDs(ids...)
}

// ClearApis clears all "apis" edges to the API entity.
func (ruo *RoleUpdateOne) ClearApis() *RoleUpdateOne {
	ruo.mutation.ClearApis()
	return ruo
}

// RemoveAPIIDs removes the "apis" edge to API entities by IDs.
func (ruo *RoleUpdateOne) RemoveAPIIDs(ids ...string) *RoleUpdateOne {
	ruo.mutation.RemoveAPIIDs(ids...)
	return ruo
}

// RemoveApis removes "apis" edges to API entities.
func (ruo *RoleUpdateOne) RemoveApis(a ...*API) *RoleUpdateOne {
	ids := make([]string, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return ruo.RemoveAPIIDs(ids...)
}

// ClearGroups clears all "groups" edges to the Group entity.
func (ruo *RoleUpdateOne) ClearGroups() *RoleUpdateOne {
	ruo.mutation.ClearGroups()
	return ruo
}

// RemoveGroupIDs removes the "groups" edge to Group entities by IDs.
func (ruo *RoleUpdateOne) RemoveGroupIDs(ids ...string) *RoleUpdateOne {
	ruo.mutation.RemoveGroupIDs(ids...)
	return ruo
}

// RemoveGroups removes "groups" edges to Group entities.
func (ruo *RoleUpdateOne) RemoveGroups(g ...*Group) *RoleUpdateOne {
	ids := make([]string, len(g))
	for i := range g {
		ids[i] = g[i].ID
	}
	return ruo.RemoveGroupIDs(ids...)
}

// ClearUsers clears all "users" edges to the User entity.
func (ruo *RoleUpdateOne) ClearUsers() *RoleUpdateOne {
	ruo.mutation.ClearUsers()
	return ruo
}

// RemoveUserIDs removes the "users" edge to User entities by IDs.
func (ruo *RoleUpdateOne) RemoveUserIDs(ids ...string) *RoleUpdateOne {
	ruo.mutation.RemoveUserIDs(ids...)
	return ruo
}

// RemoveUsers removes "users" edges to User entities.
func (ruo *RoleUpdateOne) RemoveUsers(u ...*User) *RoleUpdateOne {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return ruo.RemoveUserIDs(ids...)
}

// ClearParent clears the "parent" edge to the Role entity.
func (ruo *RoleUpdateOne) ClearParent() *RoleUpdateOne {
	ruo.mutation.ClearParent()
	return ruo
}

// ClearChildren clears all "children" edges to the Role entity.
func (ruo *RoleUpdateOne) ClearChildren() *RoleUpdateOne {
	ruo.mutation.ClearChildren()
	return ruo
}

// RemoveChildIDs removes the "children" edge to Role entities by IDs.
func (ruo *RoleUpdateOne) RemoveChildIDs(ids ...string) *RoleUpdateOne {
	ruo.mutation.RemoveChildIDs(ids...)
	return ruo
}

// RemoveChildren removes "children" edges to Role entities.
func (ruo *RoleUpdateOne) RemoveChildren(r ...*Role) *RoleUpdateOne {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return ruo.RemoveChildIDs(ids...)
}

// Where appends a list predicates to the RoleUpdate builder.
func (ruo *RoleUpdateOne) Where(ps ...predicate.Role) *RoleUpdateOne {
	ruo.mutation.Where(ps...)
	return ruo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (ruo *RoleUpdateOne) Select(field string, fields ...string) *RoleUpdateOne {
	ruo.fields = append([]string{field}, fields...)
	return ruo
}

// Save executes the query and returns the updated Role entity.
func (ruo *RoleUpdateOne) Save(ctx context.Context) (*Role, error) {
	if err := ruo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, ruo.sqlSave, ruo.mutation, ruo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (ruo *RoleUpdateOne) SaveX(ctx context.Context) *Role {
	node, err := ruo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (ruo *RoleUpdateOne) Exec(ctx context.Context) error {
	_, err := ruo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (ruo *RoleUpdateOne) ExecX(ctx context.Context) {
	if err := ruo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (ruo *RoleUpdateOne) defaults() error {
	if _, ok := ruo.mutation.UpdatedAt(); !ok {
		if role.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized role.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := role.UpdateDefaultUpdatedAt()
		ruo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (ruo *RoleUpdateOne) check() error {
	if _, ok := ruo.mutation.TenantID(); ruo.mutation.TenantCleared() && !ok {
		return errors.New(`ent: clearing a required unique edge "Role.tenant"`)
	}
	return nil
}

func (ruo *RoleUpdateOne) sqlSave(ctx context.Context) (_node *Role, err error) {
	if err := ruo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(role.Table, role.Columns, sqlgraph.NewFieldSpec(role.FieldID, field.TypeString))
	id, ok := ruo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "Role.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := ruo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, role.FieldID)
		for _, f := range fields {
			if !role.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != role.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := ruo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := ruo.mutation.UpdatedAt(); ok {
		_spec.SetField(role.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := ruo.mutation.Status(); ok {
		_spec.SetField(role.FieldStatus, field.TypeBool, value)
	}
	if ruo.mutation.StatusCleared() {
		_spec.ClearField(role.FieldStatus, field.TypeBool)
	}
	if value, ok := ruo.mutation.Sort(); ok {
		_spec.SetField(role.FieldSort, field.TypeUint32, value)
	}
	if value, ok := ruo.mutation.AddedSort(); ok {
		_spec.AddField(role.FieldSort, field.TypeUint32, value)
	}
	if value, ok := ruo.mutation.DeletedAt(); ok {
		_spec.SetField(role.FieldDeletedAt, field.TypeTime, value)
	}
	if ruo.mutation.DeletedAtCleared() {
		_spec.ClearField(role.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := ruo.mutation.Name(); ok {
		_spec.SetField(role.FieldName, field.TypeString, value)
	}
	if value, ok := ruo.mutation.Code(); ok {
		_spec.SetField(role.FieldCode, field.TypeString, value)
	}
	if value, ok := ruo.mutation.DefaultRouter(); ok {
		_spec.SetField(role.FieldDefaultRouter, field.TypeString, value)
	}
	if value, ok := ruo.mutation.Remark(); ok {
		_spec.SetField(role.FieldRemark, field.TypeString, value)
	}
	if value, ok := ruo.mutation.OrganizationID(); ok {
		_spec.SetField(role.FieldOrganizationID, field.TypeString, value)
	}
	if ruo.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   role.TenantTable,
			Columns: []string{role.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ruo.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   role.TenantTable,
			Columns: []string{role.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ruo.mutation.ButtonsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.ButtonsTable,
			Columns: role.ButtonsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(button.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ruo.mutation.RemovedButtonsIDs(); len(nodes) > 0 && !ruo.mutation.ButtonsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.ButtonsTable,
			Columns: role.ButtonsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(button.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ruo.mutation.ButtonsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.ButtonsTable,
			Columns: role.ButtonsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(button.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ruo.mutation.MenusCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.MenusTable,
			Columns: role.MenusPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ruo.mutation.RemovedMenusIDs(); len(nodes) > 0 && !ruo.mutation.MenusCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.MenusTable,
			Columns: role.MenusPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ruo.mutation.MenusIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.MenusTable,
			Columns: role.MenusPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ruo.mutation.ApisCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.ApisTable,
			Columns: role.ApisPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(api.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ruo.mutation.RemovedApisIDs(); len(nodes) > 0 && !ruo.mutation.ApisCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.ApisTable,
			Columns: role.ApisPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(api.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ruo.mutation.ApisIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.ApisTable,
			Columns: role.ApisPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(api.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ruo.mutation.GroupsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.GroupsTable,
			Columns: role.GroupsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(group.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ruo.mutation.RemovedGroupsIDs(); len(nodes) > 0 && !ruo.mutation.GroupsCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.GroupsTable,
			Columns: role.GroupsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(group.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ruo.mutation.GroupsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.GroupsTable,
			Columns: role.GroupsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(group.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ruo.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.UsersTable,
			Columns: role.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ruo.mutation.RemovedUsersIDs(); len(nodes) > 0 && !ruo.mutation.UsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.UsersTable,
			Columns: role.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ruo.mutation.UsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.UsersTable,
			Columns: role.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ruo.mutation.ParentCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   role.ParentTable,
			Columns: []string{role.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ruo.mutation.ParentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   role.ParentTable,
			Columns: []string{role.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if ruo.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   role.ChildrenTable,
			Columns: []string{role.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ruo.mutation.RemovedChildrenIDs(); len(nodes) > 0 && !ruo.mutation.ChildrenCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   role.ChildrenTable,
			Columns: []string{role.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := ruo.mutation.ChildrenIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   role.ChildrenTable,
			Columns: []string{role.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &Role{config: ruo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, ruo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{role.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	ruo.mutation.done = true
	return _node, nil
}
