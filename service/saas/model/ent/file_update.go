// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/file"
	"phoenix/service/saas/model/ent/predicate"
	"phoenix/service/saas/model/ent/tenant"
	"phoenix/service/saas/model/ent/user"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// FileUpdate is the builder for updating File entities.
type FileUpdate struct {
	config
	hooks    []Hook
	mutation *FileMutation
}

// Where appends a list predicates to the FileUpdate builder.
func (fu *FileUpdate) Where(ps ...predicate.File) *FileUpdate {
	fu.mutation.Where(ps...)
	return fu
}

// SetUpdatedAt sets the "updated_at" field.
func (fu *FileUpdate) SetUpdatedAt(t time.Time) *FileUpdate {
	fu.mutation.SetUpdatedAt(t)
	return fu
}

// SetStatus sets the "status" field.
func (fu *FileUpdate) SetStatus(b bool) *FileUpdate {
	fu.mutation.SetStatus(b)
	return fu
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (fu *FileUpdate) SetNillableStatus(b *bool) *FileUpdate {
	if b != nil {
		fu.SetStatus(*b)
	}
	return fu
}

// ClearStatus clears the value of the "status" field.
func (fu *FileUpdate) ClearStatus() *FileUpdate {
	fu.mutation.ClearStatus()
	return fu
}

// SetSort sets the "sort" field.
func (fu *FileUpdate) SetSort(u uint32) *FileUpdate {
	fu.mutation.ResetSort()
	fu.mutation.SetSort(u)
	return fu
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (fu *FileUpdate) SetNillableSort(u *uint32) *FileUpdate {
	if u != nil {
		fu.SetSort(*u)
	}
	return fu
}

// AddSort adds u to the "sort" field.
func (fu *FileUpdate) AddSort(u int32) *FileUpdate {
	fu.mutation.AddSort(u)
	return fu
}

// SetTenantID sets the "tenant_id" field.
func (fu *FileUpdate) SetTenantID(s string) *FileUpdate {
	fu.mutation.SetTenantID(s)
	return fu
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (fu *FileUpdate) SetNillableTenantID(s *string) *FileUpdate {
	if s != nil {
		fu.SetTenantID(*s)
	}
	return fu
}

// SetDeletedAt sets the "deleted_at" field.
func (fu *FileUpdate) SetDeletedAt(t time.Time) *FileUpdate {
	fu.mutation.SetDeletedAt(t)
	return fu
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (fu *FileUpdate) SetNillableDeletedAt(t *time.Time) *FileUpdate {
	if t != nil {
		fu.SetDeletedAt(*t)
	}
	return fu
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (fu *FileUpdate) ClearDeletedAt() *FileUpdate {
	fu.mutation.ClearDeletedAt()
	return fu
}

// SetUUID sets the "uuid" field.
func (fu *FileUpdate) SetUUID(s string) *FileUpdate {
	fu.mutation.SetUUID(s)
	return fu
}

// SetNillableUUID sets the "uuid" field if the given value is not nil.
func (fu *FileUpdate) SetNillableUUID(s *string) *FileUpdate {
	if s != nil {
		fu.SetUUID(*s)
	}
	return fu
}

// SetName sets the "name" field.
func (fu *FileUpdate) SetName(s string) *FileUpdate {
	fu.mutation.SetName(s)
	return fu
}

// SetNillableName sets the "name" field if the given value is not nil.
func (fu *FileUpdate) SetNillableName(s *string) *FileUpdate {
	if s != nil {
		fu.SetName(*s)
	}
	return fu
}

// SetOriginName sets the "origin_name" field.
func (fu *FileUpdate) SetOriginName(s string) *FileUpdate {
	fu.mutation.SetOriginName(s)
	return fu
}

// SetNillableOriginName sets the "origin_name" field if the given value is not nil.
func (fu *FileUpdate) SetNillableOriginName(s *string) *FileUpdate {
	if s != nil {
		fu.SetOriginName(*s)
	}
	return fu
}

// SetFileType sets the "file_type" field.
func (fu *FileUpdate) SetFileType(u uint8) *FileUpdate {
	fu.mutation.ResetFileType()
	fu.mutation.SetFileType(u)
	return fu
}

// SetNillableFileType sets the "file_type" field if the given value is not nil.
func (fu *FileUpdate) SetNillableFileType(u *uint8) *FileUpdate {
	if u != nil {
		fu.SetFileType(*u)
	}
	return fu
}

// AddFileType adds u to the "file_type" field.
func (fu *FileUpdate) AddFileType(u int8) *FileUpdate {
	fu.mutation.AddFileType(u)
	return fu
}

// SetSize sets the "size" field.
func (fu *FileUpdate) SetSize(u uint64) *FileUpdate {
	fu.mutation.ResetSize()
	fu.mutation.SetSize(u)
	return fu
}

// SetNillableSize sets the "size" field if the given value is not nil.
func (fu *FileUpdate) SetNillableSize(u *uint64) *FileUpdate {
	if u != nil {
		fu.SetSize(*u)
	}
	return fu
}

// AddSize adds u to the "size" field.
func (fu *FileUpdate) AddSize(u int64) *FileUpdate {
	fu.mutation.AddSize(u)
	return fu
}

// SetPath sets the "path" field.
func (fu *FileUpdate) SetPath(s string) *FileUpdate {
	fu.mutation.SetPath(s)
	return fu
}

// SetNillablePath sets the "path" field if the given value is not nil.
func (fu *FileUpdate) SetNillablePath(s *string) *FileUpdate {
	if s != nil {
		fu.SetPath(*s)
	}
	return fu
}

// SetUserID sets the "user_id" field.
func (fu *FileUpdate) SetUserID(s string) *FileUpdate {
	fu.mutation.SetUserID(s)
	return fu
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (fu *FileUpdate) SetNillableUserID(s *string) *FileUpdate {
	if s != nil {
		fu.SetUserID(*s)
	}
	return fu
}

// ClearUserID clears the value of the "user_id" field.
func (fu *FileUpdate) ClearUserID() *FileUpdate {
	fu.mutation.ClearUserID()
	return fu
}

// SetHash sets the "hash" field.
func (fu *FileUpdate) SetHash(s string) *FileUpdate {
	fu.mutation.SetHash(s)
	return fu
}

// SetNillableHash sets the "hash" field if the given value is not nil.
func (fu *FileUpdate) SetNillableHash(s *string) *FileUpdate {
	if s != nil {
		fu.SetHash(*s)
	}
	return fu
}

// SetOpenStatus sets the "open_status" field.
func (fu *FileUpdate) SetOpenStatus(u uint8) *FileUpdate {
	fu.mutation.ResetOpenStatus()
	fu.mutation.SetOpenStatus(u)
	return fu
}

// SetNillableOpenStatus sets the "open_status" field if the given value is not nil.
func (fu *FileUpdate) SetNillableOpenStatus(u *uint8) *FileUpdate {
	if u != nil {
		fu.SetOpenStatus(*u)
	}
	return fu
}

// AddOpenStatus adds u to the "open_status" field.
func (fu *FileUpdate) AddOpenStatus(u int8) *FileUpdate {
	fu.mutation.AddOpenStatus(u)
	return fu
}

// ClearOpenStatus clears the value of the "open_status" field.
func (fu *FileUpdate) ClearOpenStatus() *FileUpdate {
	fu.mutation.ClearOpenStatus()
	return fu
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (fu *FileUpdate) SetTenant(t *Tenant) *FileUpdate {
	return fu.SetTenantID(t.ID)
}

// SetUser sets the "user" edge to the User entity.
func (fu *FileUpdate) SetUser(u *User) *FileUpdate {
	return fu.SetUserID(u.ID)
}

// AddAvatarUserIDs adds the "avatar_users" edge to the User entity by IDs.
func (fu *FileUpdate) AddAvatarUserIDs(ids ...string) *FileUpdate {
	fu.mutation.AddAvatarUserIDs(ids...)
	return fu
}

// AddAvatarUsers adds the "avatar_users" edges to the User entity.
func (fu *FileUpdate) AddAvatarUsers(u ...*User) *FileUpdate {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return fu.AddAvatarUserIDs(ids...)
}

// Mutation returns the FileMutation object of the builder.
func (fu *FileUpdate) Mutation() *FileMutation {
	return fu.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (fu *FileUpdate) ClearTenant() *FileUpdate {
	fu.mutation.ClearTenant()
	return fu
}

// ClearUser clears the "user" edge to the User entity.
func (fu *FileUpdate) ClearUser() *FileUpdate {
	fu.mutation.ClearUser()
	return fu
}

// ClearAvatarUsers clears all "avatar_users" edges to the User entity.
func (fu *FileUpdate) ClearAvatarUsers() *FileUpdate {
	fu.mutation.ClearAvatarUsers()
	return fu
}

// RemoveAvatarUserIDs removes the "avatar_users" edge to User entities by IDs.
func (fu *FileUpdate) RemoveAvatarUserIDs(ids ...string) *FileUpdate {
	fu.mutation.RemoveAvatarUserIDs(ids...)
	return fu
}

// RemoveAvatarUsers removes "avatar_users" edges to User entities.
func (fu *FileUpdate) RemoveAvatarUsers(u ...*User) *FileUpdate {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return fu.RemoveAvatarUserIDs(ids...)
}

// Save executes the query and returns the number of nodes affected by the update operation.
func (fu *FileUpdate) Save(ctx context.Context) (int, error) {
	if err := fu.defaults(); err != nil {
		return 0, err
	}
	return withHooks(ctx, fu.sqlSave, fu.mutation, fu.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (fu *FileUpdate) SaveX(ctx context.Context) int {
	affected, err := fu.Save(ctx)
	if err != nil {
		panic(err)
	}
	return affected
}

// Exec executes the query.
func (fu *FileUpdate) Exec(ctx context.Context) error {
	_, err := fu.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (fu *FileUpdate) ExecX(ctx context.Context) {
	if err := fu.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (fu *FileUpdate) defaults() error {
	if _, ok := fu.mutation.UpdatedAt(); !ok {
		if file.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized file.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := file.UpdateDefaultUpdatedAt()
		fu.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (fu *FileUpdate) check() error {
	if _, ok := fu.mutation.TenantID(); fu.mutation.TenantCleared() && !ok {
		return errors.New(`ent: clearing a required unique edge "File.tenant"`)
	}
	return nil
}

func (fu *FileUpdate) sqlSave(ctx context.Context) (n int, err error) {
	if err := fu.check(); err != nil {
		return n, err
	}
	_spec := sqlgraph.NewUpdateSpec(file.Table, file.Columns, sqlgraph.NewFieldSpec(file.FieldID, field.TypeString))
	if ps := fu.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := fu.mutation.UpdatedAt(); ok {
		_spec.SetField(file.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := fu.mutation.Status(); ok {
		_spec.SetField(file.FieldStatus, field.TypeBool, value)
	}
	if fu.mutation.StatusCleared() {
		_spec.ClearField(file.FieldStatus, field.TypeBool)
	}
	if value, ok := fu.mutation.Sort(); ok {
		_spec.SetField(file.FieldSort, field.TypeUint32, value)
	}
	if value, ok := fu.mutation.AddedSort(); ok {
		_spec.AddField(file.FieldSort, field.TypeUint32, value)
	}
	if value, ok := fu.mutation.DeletedAt(); ok {
		_spec.SetField(file.FieldDeletedAt, field.TypeTime, value)
	}
	if fu.mutation.DeletedAtCleared() {
		_spec.ClearField(file.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := fu.mutation.UUID(); ok {
		_spec.SetField(file.FieldUUID, field.TypeString, value)
	}
	if value, ok := fu.mutation.Name(); ok {
		_spec.SetField(file.FieldName, field.TypeString, value)
	}
	if value, ok := fu.mutation.OriginName(); ok {
		_spec.SetField(file.FieldOriginName, field.TypeString, value)
	}
	if value, ok := fu.mutation.FileType(); ok {
		_spec.SetField(file.FieldFileType, field.TypeUint8, value)
	}
	if value, ok := fu.mutation.AddedFileType(); ok {
		_spec.AddField(file.FieldFileType, field.TypeUint8, value)
	}
	if value, ok := fu.mutation.Size(); ok {
		_spec.SetField(file.FieldSize, field.TypeUint64, value)
	}
	if value, ok := fu.mutation.AddedSize(); ok {
		_spec.AddField(file.FieldSize, field.TypeUint64, value)
	}
	if value, ok := fu.mutation.Path(); ok {
		_spec.SetField(file.FieldPath, field.TypeString, value)
	}
	if value, ok := fu.mutation.Hash(); ok {
		_spec.SetField(file.FieldHash, field.TypeString, value)
	}
	if value, ok := fu.mutation.OpenStatus(); ok {
		_spec.SetField(file.FieldOpenStatus, field.TypeUint8, value)
	}
	if value, ok := fu.mutation.AddedOpenStatus(); ok {
		_spec.AddField(file.FieldOpenStatus, field.TypeUint8, value)
	}
	if fu.mutation.OpenStatusCleared() {
		_spec.ClearField(file.FieldOpenStatus, field.TypeUint8)
	}
	if fu.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   file.TenantTable,
			Columns: []string{file.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := fu.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   file.TenantTable,
			Columns: []string{file.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if fu.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   file.UserTable,
			Columns: []string{file.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := fu.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   file.UserTable,
			Columns: []string{file.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if fu.mutation.AvatarUsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   file.AvatarUsersTable,
			Columns: []string{file.AvatarUsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := fu.mutation.RemovedAvatarUsersIDs(); len(nodes) > 0 && !fu.mutation.AvatarUsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   file.AvatarUsersTable,
			Columns: []string{file.AvatarUsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := fu.mutation.AvatarUsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   file.AvatarUsersTable,
			Columns: []string{file.AvatarUsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if n, err = sqlgraph.UpdateNodes(ctx, fu.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{file.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return 0, err
	}
	fu.mutation.done = true
	return n, nil
}

// FileUpdateOne is the builder for updating a single File entity.
type FileUpdateOne struct {
	config
	fields   []string
	hooks    []Hook
	mutation *FileMutation
}

// SetUpdatedAt sets the "updated_at" field.
func (fuo *FileUpdateOne) SetUpdatedAt(t time.Time) *FileUpdateOne {
	fuo.mutation.SetUpdatedAt(t)
	return fuo
}

// SetStatus sets the "status" field.
func (fuo *FileUpdateOne) SetStatus(b bool) *FileUpdateOne {
	fuo.mutation.SetStatus(b)
	return fuo
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableStatus(b *bool) *FileUpdateOne {
	if b != nil {
		fuo.SetStatus(*b)
	}
	return fuo
}

// ClearStatus clears the value of the "status" field.
func (fuo *FileUpdateOne) ClearStatus() *FileUpdateOne {
	fuo.mutation.ClearStatus()
	return fuo
}

// SetSort sets the "sort" field.
func (fuo *FileUpdateOne) SetSort(u uint32) *FileUpdateOne {
	fuo.mutation.ResetSort()
	fuo.mutation.SetSort(u)
	return fuo
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableSort(u *uint32) *FileUpdateOne {
	if u != nil {
		fuo.SetSort(*u)
	}
	return fuo
}

// AddSort adds u to the "sort" field.
func (fuo *FileUpdateOne) AddSort(u int32) *FileUpdateOne {
	fuo.mutation.AddSort(u)
	return fuo
}

// SetTenantID sets the "tenant_id" field.
func (fuo *FileUpdateOne) SetTenantID(s string) *FileUpdateOne {
	fuo.mutation.SetTenantID(s)
	return fuo
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableTenantID(s *string) *FileUpdateOne {
	if s != nil {
		fuo.SetTenantID(*s)
	}
	return fuo
}

// SetDeletedAt sets the "deleted_at" field.
func (fuo *FileUpdateOne) SetDeletedAt(t time.Time) *FileUpdateOne {
	fuo.mutation.SetDeletedAt(t)
	return fuo
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableDeletedAt(t *time.Time) *FileUpdateOne {
	if t != nil {
		fuo.SetDeletedAt(*t)
	}
	return fuo
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (fuo *FileUpdateOne) ClearDeletedAt() *FileUpdateOne {
	fuo.mutation.ClearDeletedAt()
	return fuo
}

// SetUUID sets the "uuid" field.
func (fuo *FileUpdateOne) SetUUID(s string) *FileUpdateOne {
	fuo.mutation.SetUUID(s)
	return fuo
}

// SetNillableUUID sets the "uuid" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableUUID(s *string) *FileUpdateOne {
	if s != nil {
		fuo.SetUUID(*s)
	}
	return fuo
}

// SetName sets the "name" field.
func (fuo *FileUpdateOne) SetName(s string) *FileUpdateOne {
	fuo.mutation.SetName(s)
	return fuo
}

// SetNillableName sets the "name" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableName(s *string) *FileUpdateOne {
	if s != nil {
		fuo.SetName(*s)
	}
	return fuo
}

// SetOriginName sets the "origin_name" field.
func (fuo *FileUpdateOne) SetOriginName(s string) *FileUpdateOne {
	fuo.mutation.SetOriginName(s)
	return fuo
}

// SetNillableOriginName sets the "origin_name" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableOriginName(s *string) *FileUpdateOne {
	if s != nil {
		fuo.SetOriginName(*s)
	}
	return fuo
}

// SetFileType sets the "file_type" field.
func (fuo *FileUpdateOne) SetFileType(u uint8) *FileUpdateOne {
	fuo.mutation.ResetFileType()
	fuo.mutation.SetFileType(u)
	return fuo
}

// SetNillableFileType sets the "file_type" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableFileType(u *uint8) *FileUpdateOne {
	if u != nil {
		fuo.SetFileType(*u)
	}
	return fuo
}

// AddFileType adds u to the "file_type" field.
func (fuo *FileUpdateOne) AddFileType(u int8) *FileUpdateOne {
	fuo.mutation.AddFileType(u)
	return fuo
}

// SetSize sets the "size" field.
func (fuo *FileUpdateOne) SetSize(u uint64) *FileUpdateOne {
	fuo.mutation.ResetSize()
	fuo.mutation.SetSize(u)
	return fuo
}

// SetNillableSize sets the "size" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableSize(u *uint64) *FileUpdateOne {
	if u != nil {
		fuo.SetSize(*u)
	}
	return fuo
}

// AddSize adds u to the "size" field.
func (fuo *FileUpdateOne) AddSize(u int64) *FileUpdateOne {
	fuo.mutation.AddSize(u)
	return fuo
}

// SetPath sets the "path" field.
func (fuo *FileUpdateOne) SetPath(s string) *FileUpdateOne {
	fuo.mutation.SetPath(s)
	return fuo
}

// SetNillablePath sets the "path" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillablePath(s *string) *FileUpdateOne {
	if s != nil {
		fuo.SetPath(*s)
	}
	return fuo
}

// SetUserID sets the "user_id" field.
func (fuo *FileUpdateOne) SetUserID(s string) *FileUpdateOne {
	fuo.mutation.SetUserID(s)
	return fuo
}

// SetNillableUserID sets the "user_id" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableUserID(s *string) *FileUpdateOne {
	if s != nil {
		fuo.SetUserID(*s)
	}
	return fuo
}

// ClearUserID clears the value of the "user_id" field.
func (fuo *FileUpdateOne) ClearUserID() *FileUpdateOne {
	fuo.mutation.ClearUserID()
	return fuo
}

// SetHash sets the "hash" field.
func (fuo *FileUpdateOne) SetHash(s string) *FileUpdateOne {
	fuo.mutation.SetHash(s)
	return fuo
}

// SetNillableHash sets the "hash" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableHash(s *string) *FileUpdateOne {
	if s != nil {
		fuo.SetHash(*s)
	}
	return fuo
}

// SetOpenStatus sets the "open_status" field.
func (fuo *FileUpdateOne) SetOpenStatus(u uint8) *FileUpdateOne {
	fuo.mutation.ResetOpenStatus()
	fuo.mutation.SetOpenStatus(u)
	return fuo
}

// SetNillableOpenStatus sets the "open_status" field if the given value is not nil.
func (fuo *FileUpdateOne) SetNillableOpenStatus(u *uint8) *FileUpdateOne {
	if u != nil {
		fuo.SetOpenStatus(*u)
	}
	return fuo
}

// AddOpenStatus adds u to the "open_status" field.
func (fuo *FileUpdateOne) AddOpenStatus(u int8) *FileUpdateOne {
	fuo.mutation.AddOpenStatus(u)
	return fuo
}

// ClearOpenStatus clears the value of the "open_status" field.
func (fuo *FileUpdateOne) ClearOpenStatus() *FileUpdateOne {
	fuo.mutation.ClearOpenStatus()
	return fuo
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (fuo *FileUpdateOne) SetTenant(t *Tenant) *FileUpdateOne {
	return fuo.SetTenantID(t.ID)
}

// SetUser sets the "user" edge to the User entity.
func (fuo *FileUpdateOne) SetUser(u *User) *FileUpdateOne {
	return fuo.SetUserID(u.ID)
}

// AddAvatarUserIDs adds the "avatar_users" edge to the User entity by IDs.
func (fuo *FileUpdateOne) AddAvatarUserIDs(ids ...string) *FileUpdateOne {
	fuo.mutation.AddAvatarUserIDs(ids...)
	return fuo
}

// AddAvatarUsers adds the "avatar_users" edges to the User entity.
func (fuo *FileUpdateOne) AddAvatarUsers(u ...*User) *FileUpdateOne {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return fuo.AddAvatarUserIDs(ids...)
}

// Mutation returns the FileMutation object of the builder.
func (fuo *FileUpdateOne) Mutation() *FileMutation {
	return fuo.mutation
}

// ClearTenant clears the "tenant" edge to the Tenant entity.
func (fuo *FileUpdateOne) ClearTenant() *FileUpdateOne {
	fuo.mutation.ClearTenant()
	return fuo
}

// ClearUser clears the "user" edge to the User entity.
func (fuo *FileUpdateOne) ClearUser() *FileUpdateOne {
	fuo.mutation.ClearUser()
	return fuo
}

// ClearAvatarUsers clears all "avatar_users" edges to the User entity.
func (fuo *FileUpdateOne) ClearAvatarUsers() *FileUpdateOne {
	fuo.mutation.ClearAvatarUsers()
	return fuo
}

// RemoveAvatarUserIDs removes the "avatar_users" edge to User entities by IDs.
func (fuo *FileUpdateOne) RemoveAvatarUserIDs(ids ...string) *FileUpdateOne {
	fuo.mutation.RemoveAvatarUserIDs(ids...)
	return fuo
}

// RemoveAvatarUsers removes "avatar_users" edges to User entities.
func (fuo *FileUpdateOne) RemoveAvatarUsers(u ...*User) *FileUpdateOne {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return fuo.RemoveAvatarUserIDs(ids...)
}

// Where appends a list predicates to the FileUpdate builder.
func (fuo *FileUpdateOne) Where(ps ...predicate.File) *FileUpdateOne {
	fuo.mutation.Where(ps...)
	return fuo
}

// Select allows selecting one or more fields (columns) of the returned entity.
// The default is selecting all fields defined in the entity schema.
func (fuo *FileUpdateOne) Select(field string, fields ...string) *FileUpdateOne {
	fuo.fields = append([]string{field}, fields...)
	return fuo
}

// Save executes the query and returns the updated File entity.
func (fuo *FileUpdateOne) Save(ctx context.Context) (*File, error) {
	if err := fuo.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, fuo.sqlSave, fuo.mutation, fuo.hooks)
}

// SaveX is like Save, but panics if an error occurs.
func (fuo *FileUpdateOne) SaveX(ctx context.Context) *File {
	node, err := fuo.Save(ctx)
	if err != nil {
		panic(err)
	}
	return node
}

// Exec executes the query on the entity.
func (fuo *FileUpdateOne) Exec(ctx context.Context) error {
	_, err := fuo.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (fuo *FileUpdateOne) ExecX(ctx context.Context) {
	if err := fuo.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (fuo *FileUpdateOne) defaults() error {
	if _, ok := fuo.mutation.UpdatedAt(); !ok {
		if file.UpdateDefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized file.UpdateDefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := file.UpdateDefaultUpdatedAt()
		fuo.mutation.SetUpdatedAt(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (fuo *FileUpdateOne) check() error {
	if _, ok := fuo.mutation.TenantID(); fuo.mutation.TenantCleared() && !ok {
		return errors.New(`ent: clearing a required unique edge "File.tenant"`)
	}
	return nil
}

func (fuo *FileUpdateOne) sqlSave(ctx context.Context) (_node *File, err error) {
	if err := fuo.check(); err != nil {
		return _node, err
	}
	_spec := sqlgraph.NewUpdateSpec(file.Table, file.Columns, sqlgraph.NewFieldSpec(file.FieldID, field.TypeString))
	id, ok := fuo.mutation.ID()
	if !ok {
		return nil, &ValidationError{Name: "id", err: errors.New(`ent: missing "File.id" for update`)}
	}
	_spec.Node.ID.Value = id
	if fields := fuo.fields; len(fields) > 0 {
		_spec.Node.Columns = make([]string, 0, len(fields))
		_spec.Node.Columns = append(_spec.Node.Columns, file.FieldID)
		for _, f := range fields {
			if !file.ValidColumn(f) {
				return nil, &ValidationError{Name: f, err: fmt.Errorf("ent: invalid field %q for query", f)}
			}
			if f != file.FieldID {
				_spec.Node.Columns = append(_spec.Node.Columns, f)
			}
		}
	}
	if ps := fuo.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	if value, ok := fuo.mutation.UpdatedAt(); ok {
		_spec.SetField(file.FieldUpdatedAt, field.TypeTime, value)
	}
	if value, ok := fuo.mutation.Status(); ok {
		_spec.SetField(file.FieldStatus, field.TypeBool, value)
	}
	if fuo.mutation.StatusCleared() {
		_spec.ClearField(file.FieldStatus, field.TypeBool)
	}
	if value, ok := fuo.mutation.Sort(); ok {
		_spec.SetField(file.FieldSort, field.TypeUint32, value)
	}
	if value, ok := fuo.mutation.AddedSort(); ok {
		_spec.AddField(file.FieldSort, field.TypeUint32, value)
	}
	if value, ok := fuo.mutation.DeletedAt(); ok {
		_spec.SetField(file.FieldDeletedAt, field.TypeTime, value)
	}
	if fuo.mutation.DeletedAtCleared() {
		_spec.ClearField(file.FieldDeletedAt, field.TypeTime)
	}
	if value, ok := fuo.mutation.UUID(); ok {
		_spec.SetField(file.FieldUUID, field.TypeString, value)
	}
	if value, ok := fuo.mutation.Name(); ok {
		_spec.SetField(file.FieldName, field.TypeString, value)
	}
	if value, ok := fuo.mutation.OriginName(); ok {
		_spec.SetField(file.FieldOriginName, field.TypeString, value)
	}
	if value, ok := fuo.mutation.FileType(); ok {
		_spec.SetField(file.FieldFileType, field.TypeUint8, value)
	}
	if value, ok := fuo.mutation.AddedFileType(); ok {
		_spec.AddField(file.FieldFileType, field.TypeUint8, value)
	}
	if value, ok := fuo.mutation.Size(); ok {
		_spec.SetField(file.FieldSize, field.TypeUint64, value)
	}
	if value, ok := fuo.mutation.AddedSize(); ok {
		_spec.AddField(file.FieldSize, field.TypeUint64, value)
	}
	if value, ok := fuo.mutation.Path(); ok {
		_spec.SetField(file.FieldPath, field.TypeString, value)
	}
	if value, ok := fuo.mutation.Hash(); ok {
		_spec.SetField(file.FieldHash, field.TypeString, value)
	}
	if value, ok := fuo.mutation.OpenStatus(); ok {
		_spec.SetField(file.FieldOpenStatus, field.TypeUint8, value)
	}
	if value, ok := fuo.mutation.AddedOpenStatus(); ok {
		_spec.AddField(file.FieldOpenStatus, field.TypeUint8, value)
	}
	if fuo.mutation.OpenStatusCleared() {
		_spec.ClearField(file.FieldOpenStatus, field.TypeUint8)
	}
	if fuo.mutation.TenantCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   file.TenantTable,
			Columns: []string{file.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := fuo.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   file.TenantTable,
			Columns: []string{file.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if fuo.mutation.UserCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   file.UserTable,
			Columns: []string{file.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := fuo.mutation.UserIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   file.UserTable,
			Columns: []string{file.UserColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	if fuo.mutation.AvatarUsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   file.AvatarUsersTable,
			Columns: []string{file.AvatarUsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := fuo.mutation.RemovedAvatarUsersIDs(); len(nodes) > 0 && !fuo.mutation.AvatarUsersCleared() {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   file.AvatarUsersTable,
			Columns: []string{file.AvatarUsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Clear = append(_spec.Edges.Clear, edge)
	}
	if nodes := fuo.mutation.AvatarUsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   file.AvatarUsersTable,
			Columns: []string{file.AvatarUsersColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges.Add = append(_spec.Edges.Add, edge)
	}
	_node = &File{config: fuo.config}
	_spec.Assign = _node.assignValues
	_spec.ScanValues = _node.scanValues
	if err = sqlgraph.UpdateNode(ctx, fuo.driver, _spec); err != nil {
		if _, ok := err.(*sqlgraph.NotFoundError); ok {
			err = &NotFoundError{file.Label}
		} else if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	fuo.mutation.done = true
	return _node, nil
}
