// Code generated by ent, DO NOT EDIT.

package tenantuserinfo

import (
	"time"

	"entgo.io/ent"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

const (
	// Label holds the string label denoting the tenantuserinfo type in the database.
	Label = "tenant_user_info"
	// FieldID holds the string denoting the id field in the database.
	FieldID = "id"
	// FieldCreatedAt holds the string denoting the created_at field in the database.
	FieldCreatedAt = "created_at"
	// FieldUpdatedAt holds the string denoting the updated_at field in the database.
	FieldUpdatedAt = "updated_at"
	// FieldSort holds the string denoting the sort field in the database.
	FieldSort = "sort"
	// FieldDeletedAt holds the string denoting the deleted_at field in the database.
	FieldDeletedAt = "deleted_at"
	// FieldTenantID holds the string denoting the tenant_id field in the database.
	FieldTenantID = "tenant_id"
	// FieldUserID holds the string denoting the user_id field in the database.
	FieldUserID = "user_id"
	// FieldExtra holds the string denoting the extra field in the database.
	FieldExtra = "extra"
	// EdgeTenantUserInfoTenant holds the string denoting the tenant_user_info_tenant edge name in mutations.
	EdgeTenantUserInfoTenant = "tenant_user_info_tenant"
	// EdgeTenantUserInfoUser holds the string denoting the tenant_user_info_user edge name in mutations.
	EdgeTenantUserInfoUser = "tenant_user_info_user"
	// Table holds the table name of the tenantuserinfo in the database.
	Table = "saas_tenant_user_info"
	// TenantUserInfoTenantTable is the table that holds the tenant_user_info_tenant relation/edge.
	TenantUserInfoTenantTable = "saas_tenant_user_info"
	// TenantUserInfoTenantInverseTable is the table name for the Tenant entity.
	// It exists in this package in order to avoid circular dependency with the "tenant" package.
	TenantUserInfoTenantInverseTable = "saas_tenant"
	// TenantUserInfoTenantColumn is the table column denoting the tenant_user_info_tenant relation/edge.
	TenantUserInfoTenantColumn = "tenant_id"
	// TenantUserInfoUserTable is the table that holds the tenant_user_info_user relation/edge.
	TenantUserInfoUserTable = "saas_tenant_user_info"
	// TenantUserInfoUserInverseTable is the table name for the User entity.
	// It exists in this package in order to avoid circular dependency with the "user" package.
	TenantUserInfoUserInverseTable = "saas_user"
	// TenantUserInfoUserColumn is the table column denoting the tenant_user_info_user relation/edge.
	TenantUserInfoUserColumn = "user_id"
)

// Columns holds all SQL columns for tenantuserinfo fields.
var Columns = []string{
	FieldID,
	FieldCreatedAt,
	FieldUpdatedAt,
	FieldSort,
	FieldDeletedAt,
	FieldTenantID,
	FieldUserID,
	FieldExtra,
}

// ForeignKeys holds the SQL foreign-keys that are owned by the "saas_tenant_user_info"
// table and are not defined as standalone fields in the schema.
var ForeignKeys = []string{
	"user_tenant_infos",
}

// ValidColumn reports if the column name is valid (part of the table columns).
func ValidColumn(column string) bool {
	for i := range Columns {
		if column == Columns[i] {
			return true
		}
	}
	for i := range ForeignKeys {
		if column == ForeignKeys[i] {
			return true
		}
	}
	return false
}

// Note that the variables below are initialized by the runtime
// package on the initialization of the application. Therefore,
// it should be imported in the main as follows:
//
//	import _ "phoenix/service/saas/model/ent/runtime"
var (
	Hooks        [1]ent.Hook
	Interceptors [1]ent.Interceptor
	// DefaultCreatedAt holds the default value on creation for the "created_at" field.
	DefaultCreatedAt func() time.Time
	// DefaultUpdatedAt holds the default value on creation for the "updated_at" field.
	DefaultUpdatedAt func() time.Time
	// UpdateDefaultUpdatedAt holds the default value on update for the "updated_at" field.
	UpdateDefaultUpdatedAt func() time.Time
	// DefaultSort holds the default value on creation for the "sort" field.
	DefaultSort uint32
	// DefaultExtra holds the default value on creation for the "extra" field.
	DefaultExtra string
	// DefaultID holds the default value on creation for the "id" field.
	DefaultID func() string
)

// OrderOption defines the ordering options for the TenantUserInfo queries.
type OrderOption func(*sql.Selector)

// ByID orders the results by the id field.
func ByID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldID, opts...).ToFunc()
}

// ByCreatedAt orders the results by the created_at field.
func ByCreatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldCreatedAt, opts...).ToFunc()
}

// ByUpdatedAt orders the results by the updated_at field.
func ByUpdatedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUpdatedAt, opts...).ToFunc()
}

// BySort orders the results by the sort field.
func BySort(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldSort, opts...).ToFunc()
}

// ByDeletedAt orders the results by the deleted_at field.
func ByDeletedAt(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldDeletedAt, opts...).ToFunc()
}

// ByTenantID orders the results by the tenant_id field.
func ByTenantID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldTenantID, opts...).ToFunc()
}

// ByUserID orders the results by the user_id field.
func ByUserID(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldUserID, opts...).ToFunc()
}

// ByExtra orders the results by the extra field.
func ByExtra(opts ...sql.OrderTermOption) OrderOption {
	return sql.OrderByField(FieldExtra, opts...).ToFunc()
}

// ByTenantUserInfoTenantField orders the results by tenant_user_info_tenant field.
func ByTenantUserInfoTenantField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTenantUserInfoTenantStep(), sql.OrderByField(field, opts...))
	}
}

// ByTenantUserInfoUserField orders the results by tenant_user_info_user field.
func ByTenantUserInfoUserField(field string, opts ...sql.OrderTermOption) OrderOption {
	return func(s *sql.Selector) {
		sqlgraph.OrderByNeighborTerms(s, newTenantUserInfoUserStep(), sql.OrderByField(field, opts...))
	}
}
func newTenantUserInfoTenantStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TenantUserInfoTenantInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, TenantUserInfoTenantTable, TenantUserInfoTenantColumn),
	)
}
func newTenantUserInfoUserStep() *sqlgraph.Step {
	return sqlgraph.NewStep(
		sqlgraph.From(Table, FieldID),
		sqlgraph.To(TenantUserInfoUserInverseTable, FieldID),
		sqlgraph.Edge(sqlgraph.M2O, false, TenantUserInfoUserTable, TenantUserInfoUserColumn),
	)
}
