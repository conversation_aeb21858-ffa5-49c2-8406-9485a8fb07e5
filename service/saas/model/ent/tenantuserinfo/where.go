// Code generated by ent, DO NOT EDIT.

package tenantuserinfo

import (
	"phoenix/service/saas/model/ent/predicate"
	"time"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
)

// ID filters vertices based on their ID field.
func ID(id string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldEQ(FieldID, id))
}

// IDEQ applies the EQ predicate on the ID field.
func IDEQ(id string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldEQ(FieldID, id))
}

// IDNEQ applies the NEQ predicate on the ID field.
func IDNEQ(id string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldNEQ(FieldID, id))
}

// IDIn applies the In predicate on the ID field.
func IDIn(ids ...string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldIn(FieldID, ids...))
}

// IDNotIn applies the NotIn predicate on the ID field.
func IDNotIn(ids ...string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldNotIn(FieldID, ids...))
}

// IDGT applies the GT predicate on the ID field.
func IDGT(id string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldGT(FieldID, id))
}

// IDGTE applies the GTE predicate on the ID field.
func IDGTE(id string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldGTE(FieldID, id))
}

// IDLT applies the LT predicate on the ID field.
func IDLT(id string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldLT(FieldID, id))
}

// IDLTE applies the LTE predicate on the ID field.
func IDLTE(id string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldLTE(FieldID, id))
}

// IDEqualFold applies the EqualFold predicate on the ID field.
func IDEqualFold(id string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldEqualFold(FieldID, id))
}

// IDContainsFold applies the ContainsFold predicate on the ID field.
func IDContainsFold(id string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldContainsFold(FieldID, id))
}

// CreatedAt applies equality check predicate on the "created_at" field. It's identical to CreatedAtEQ.
func CreatedAt(v time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldEQ(FieldCreatedAt, v))
}

// UpdatedAt applies equality check predicate on the "updated_at" field. It's identical to UpdatedAtEQ.
func UpdatedAt(v time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldEQ(FieldUpdatedAt, v))
}

// Sort applies equality check predicate on the "sort" field. It's identical to SortEQ.
func Sort(v uint32) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldEQ(FieldSort, v))
}

// DeletedAt applies equality check predicate on the "deleted_at" field. It's identical to DeletedAtEQ.
func DeletedAt(v time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldEQ(FieldDeletedAt, v))
}

// TenantID applies equality check predicate on the "tenant_id" field. It's identical to TenantIDEQ.
func TenantID(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldEQ(FieldTenantID, v))
}

// UserID applies equality check predicate on the "user_id" field. It's identical to UserIDEQ.
func UserID(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldEQ(FieldUserID, v))
}

// Extra applies equality check predicate on the "extra" field. It's identical to ExtraEQ.
func Extra(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldEQ(FieldExtra, v))
}

// CreatedAtEQ applies the EQ predicate on the "created_at" field.
func CreatedAtEQ(v time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldEQ(FieldCreatedAt, v))
}

// CreatedAtNEQ applies the NEQ predicate on the "created_at" field.
func CreatedAtNEQ(v time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldNEQ(FieldCreatedAt, v))
}

// CreatedAtIn applies the In predicate on the "created_at" field.
func CreatedAtIn(vs ...time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldIn(FieldCreatedAt, vs...))
}

// CreatedAtNotIn applies the NotIn predicate on the "created_at" field.
func CreatedAtNotIn(vs ...time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldNotIn(FieldCreatedAt, vs...))
}

// CreatedAtGT applies the GT predicate on the "created_at" field.
func CreatedAtGT(v time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldGT(FieldCreatedAt, v))
}

// CreatedAtGTE applies the GTE predicate on the "created_at" field.
func CreatedAtGTE(v time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldGTE(FieldCreatedAt, v))
}

// CreatedAtLT applies the LT predicate on the "created_at" field.
func CreatedAtLT(v time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldLT(FieldCreatedAt, v))
}

// CreatedAtLTE applies the LTE predicate on the "created_at" field.
func CreatedAtLTE(v time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldLTE(FieldCreatedAt, v))
}

// UpdatedAtEQ applies the EQ predicate on the "updated_at" field.
func UpdatedAtEQ(v time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldEQ(FieldUpdatedAt, v))
}

// UpdatedAtNEQ applies the NEQ predicate on the "updated_at" field.
func UpdatedAtNEQ(v time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldNEQ(FieldUpdatedAt, v))
}

// UpdatedAtIn applies the In predicate on the "updated_at" field.
func UpdatedAtIn(vs ...time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldIn(FieldUpdatedAt, vs...))
}

// UpdatedAtNotIn applies the NotIn predicate on the "updated_at" field.
func UpdatedAtNotIn(vs ...time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldNotIn(FieldUpdatedAt, vs...))
}

// UpdatedAtGT applies the GT predicate on the "updated_at" field.
func UpdatedAtGT(v time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldGT(FieldUpdatedAt, v))
}

// UpdatedAtGTE applies the GTE predicate on the "updated_at" field.
func UpdatedAtGTE(v time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldGTE(FieldUpdatedAt, v))
}

// UpdatedAtLT applies the LT predicate on the "updated_at" field.
func UpdatedAtLT(v time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldLT(FieldUpdatedAt, v))
}

// UpdatedAtLTE applies the LTE predicate on the "updated_at" field.
func UpdatedAtLTE(v time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldLTE(FieldUpdatedAt, v))
}

// SortEQ applies the EQ predicate on the "sort" field.
func SortEQ(v uint32) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldEQ(FieldSort, v))
}

// SortNEQ applies the NEQ predicate on the "sort" field.
func SortNEQ(v uint32) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldNEQ(FieldSort, v))
}

// SortIn applies the In predicate on the "sort" field.
func SortIn(vs ...uint32) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldIn(FieldSort, vs...))
}

// SortNotIn applies the NotIn predicate on the "sort" field.
func SortNotIn(vs ...uint32) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldNotIn(FieldSort, vs...))
}

// SortGT applies the GT predicate on the "sort" field.
func SortGT(v uint32) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldGT(FieldSort, v))
}

// SortGTE applies the GTE predicate on the "sort" field.
func SortGTE(v uint32) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldGTE(FieldSort, v))
}

// SortLT applies the LT predicate on the "sort" field.
func SortLT(v uint32) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldLT(FieldSort, v))
}

// SortLTE applies the LTE predicate on the "sort" field.
func SortLTE(v uint32) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldLTE(FieldSort, v))
}

// DeletedAtEQ applies the EQ predicate on the "deleted_at" field.
func DeletedAtEQ(v time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldEQ(FieldDeletedAt, v))
}

// DeletedAtNEQ applies the NEQ predicate on the "deleted_at" field.
func DeletedAtNEQ(v time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldNEQ(FieldDeletedAt, v))
}

// DeletedAtIn applies the In predicate on the "deleted_at" field.
func DeletedAtIn(vs ...time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldIn(FieldDeletedAt, vs...))
}

// DeletedAtNotIn applies the NotIn predicate on the "deleted_at" field.
func DeletedAtNotIn(vs ...time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldNotIn(FieldDeletedAt, vs...))
}

// DeletedAtGT applies the GT predicate on the "deleted_at" field.
func DeletedAtGT(v time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldGT(FieldDeletedAt, v))
}

// DeletedAtGTE applies the GTE predicate on the "deleted_at" field.
func DeletedAtGTE(v time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldGTE(FieldDeletedAt, v))
}

// DeletedAtLT applies the LT predicate on the "deleted_at" field.
func DeletedAtLT(v time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldLT(FieldDeletedAt, v))
}

// DeletedAtLTE applies the LTE predicate on the "deleted_at" field.
func DeletedAtLTE(v time.Time) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldLTE(FieldDeletedAt, v))
}

// DeletedAtIsNil applies the IsNil predicate on the "deleted_at" field.
func DeletedAtIsNil() predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldIsNull(FieldDeletedAt))
}

// DeletedAtNotNil applies the NotNil predicate on the "deleted_at" field.
func DeletedAtNotNil() predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldNotNull(FieldDeletedAt))
}

// TenantIDEQ applies the EQ predicate on the "tenant_id" field.
func TenantIDEQ(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldEQ(FieldTenantID, v))
}

// TenantIDNEQ applies the NEQ predicate on the "tenant_id" field.
func TenantIDNEQ(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldNEQ(FieldTenantID, v))
}

// TenantIDIn applies the In predicate on the "tenant_id" field.
func TenantIDIn(vs ...string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldIn(FieldTenantID, vs...))
}

// TenantIDNotIn applies the NotIn predicate on the "tenant_id" field.
func TenantIDNotIn(vs ...string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldNotIn(FieldTenantID, vs...))
}

// TenantIDGT applies the GT predicate on the "tenant_id" field.
func TenantIDGT(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldGT(FieldTenantID, v))
}

// TenantIDGTE applies the GTE predicate on the "tenant_id" field.
func TenantIDGTE(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldGTE(FieldTenantID, v))
}

// TenantIDLT applies the LT predicate on the "tenant_id" field.
func TenantIDLT(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldLT(FieldTenantID, v))
}

// TenantIDLTE applies the LTE predicate on the "tenant_id" field.
func TenantIDLTE(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldLTE(FieldTenantID, v))
}

// TenantIDContains applies the Contains predicate on the "tenant_id" field.
func TenantIDContains(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldContains(FieldTenantID, v))
}

// TenantIDHasPrefix applies the HasPrefix predicate on the "tenant_id" field.
func TenantIDHasPrefix(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldHasPrefix(FieldTenantID, v))
}

// TenantIDHasSuffix applies the HasSuffix predicate on the "tenant_id" field.
func TenantIDHasSuffix(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldHasSuffix(FieldTenantID, v))
}

// TenantIDIsNil applies the IsNil predicate on the "tenant_id" field.
func TenantIDIsNil() predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldIsNull(FieldTenantID))
}

// TenantIDNotNil applies the NotNil predicate on the "tenant_id" field.
func TenantIDNotNil() predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldNotNull(FieldTenantID))
}

// TenantIDEqualFold applies the EqualFold predicate on the "tenant_id" field.
func TenantIDEqualFold(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldEqualFold(FieldTenantID, v))
}

// TenantIDContainsFold applies the ContainsFold predicate on the "tenant_id" field.
func TenantIDContainsFold(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldContainsFold(FieldTenantID, v))
}

// UserIDEQ applies the EQ predicate on the "user_id" field.
func UserIDEQ(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldEQ(FieldUserID, v))
}

// UserIDNEQ applies the NEQ predicate on the "user_id" field.
func UserIDNEQ(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldNEQ(FieldUserID, v))
}

// UserIDIn applies the In predicate on the "user_id" field.
func UserIDIn(vs ...string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldIn(FieldUserID, vs...))
}

// UserIDNotIn applies the NotIn predicate on the "user_id" field.
func UserIDNotIn(vs ...string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldNotIn(FieldUserID, vs...))
}

// UserIDGT applies the GT predicate on the "user_id" field.
func UserIDGT(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldGT(FieldUserID, v))
}

// UserIDGTE applies the GTE predicate on the "user_id" field.
func UserIDGTE(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldGTE(FieldUserID, v))
}

// UserIDLT applies the LT predicate on the "user_id" field.
func UserIDLT(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldLT(FieldUserID, v))
}

// UserIDLTE applies the LTE predicate on the "user_id" field.
func UserIDLTE(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldLTE(FieldUserID, v))
}

// UserIDContains applies the Contains predicate on the "user_id" field.
func UserIDContains(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldContains(FieldUserID, v))
}

// UserIDHasPrefix applies the HasPrefix predicate on the "user_id" field.
func UserIDHasPrefix(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldHasPrefix(FieldUserID, v))
}

// UserIDHasSuffix applies the HasSuffix predicate on the "user_id" field.
func UserIDHasSuffix(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldHasSuffix(FieldUserID, v))
}

// UserIDIsNil applies the IsNil predicate on the "user_id" field.
func UserIDIsNil() predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldIsNull(FieldUserID))
}

// UserIDNotNil applies the NotNil predicate on the "user_id" field.
func UserIDNotNil() predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldNotNull(FieldUserID))
}

// UserIDEqualFold applies the EqualFold predicate on the "user_id" field.
func UserIDEqualFold(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldEqualFold(FieldUserID, v))
}

// UserIDContainsFold applies the ContainsFold predicate on the "user_id" field.
func UserIDContainsFold(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldContainsFold(FieldUserID, v))
}

// ExtraEQ applies the EQ predicate on the "extra" field.
func ExtraEQ(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldEQ(FieldExtra, v))
}

// ExtraNEQ applies the NEQ predicate on the "extra" field.
func ExtraNEQ(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldNEQ(FieldExtra, v))
}

// ExtraIn applies the In predicate on the "extra" field.
func ExtraIn(vs ...string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldIn(FieldExtra, vs...))
}

// ExtraNotIn applies the NotIn predicate on the "extra" field.
func ExtraNotIn(vs ...string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldNotIn(FieldExtra, vs...))
}

// ExtraGT applies the GT predicate on the "extra" field.
func ExtraGT(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldGT(FieldExtra, v))
}

// ExtraGTE applies the GTE predicate on the "extra" field.
func ExtraGTE(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldGTE(FieldExtra, v))
}

// ExtraLT applies the LT predicate on the "extra" field.
func ExtraLT(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldLT(FieldExtra, v))
}

// ExtraLTE applies the LTE predicate on the "extra" field.
func ExtraLTE(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldLTE(FieldExtra, v))
}

// ExtraContains applies the Contains predicate on the "extra" field.
func ExtraContains(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldContains(FieldExtra, v))
}

// ExtraHasPrefix applies the HasPrefix predicate on the "extra" field.
func ExtraHasPrefix(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldHasPrefix(FieldExtra, v))
}

// ExtraHasSuffix applies the HasSuffix predicate on the "extra" field.
func ExtraHasSuffix(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldHasSuffix(FieldExtra, v))
}

// ExtraIsNil applies the IsNil predicate on the "extra" field.
func ExtraIsNil() predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldIsNull(FieldExtra))
}

// ExtraNotNil applies the NotNil predicate on the "extra" field.
func ExtraNotNil() predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldNotNull(FieldExtra))
}

// ExtraEqualFold applies the EqualFold predicate on the "extra" field.
func ExtraEqualFold(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldEqualFold(FieldExtra, v))
}

// ExtraContainsFold applies the ContainsFold predicate on the "extra" field.
func ExtraContainsFold(v string) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.FieldContainsFold(FieldExtra, v))
}

// HasTenantUserInfoTenant applies the HasEdge predicate on the "tenant_user_info_tenant" edge.
func HasTenantUserInfoTenant() predicate.TenantUserInfo {
	return predicate.TenantUserInfo(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, TenantUserInfoTenantTable, TenantUserInfoTenantColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTenantUserInfoTenantWith applies the HasEdge predicate on the "tenant_user_info_tenant" edge with a given conditions (other predicates).
func HasTenantUserInfoTenantWith(preds ...predicate.Tenant) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(func(s *sql.Selector) {
		step := newTenantUserInfoTenantStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// HasTenantUserInfoUser applies the HasEdge predicate on the "tenant_user_info_user" edge.
func HasTenantUserInfoUser() predicate.TenantUserInfo {
	return predicate.TenantUserInfo(func(s *sql.Selector) {
		step := sqlgraph.NewStep(
			sqlgraph.From(Table, FieldID),
			sqlgraph.Edge(sqlgraph.M2O, false, TenantUserInfoUserTable, TenantUserInfoUserColumn),
		)
		sqlgraph.HasNeighbors(s, step)
	})
}

// HasTenantUserInfoUserWith applies the HasEdge predicate on the "tenant_user_info_user" edge with a given conditions (other predicates).
func HasTenantUserInfoUserWith(preds ...predicate.User) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(func(s *sql.Selector) {
		step := newTenantUserInfoUserStep()
		sqlgraph.HasNeighborsWith(s, step, func(s *sql.Selector) {
			for _, p := range preds {
				p(s)
			}
		})
	})
}

// And groups predicates with the AND operator between them.
func And(predicates ...predicate.TenantUserInfo) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.AndPredicates(predicates...))
}

// Or groups predicates with the OR operator between them.
func Or(predicates ...predicate.TenantUserInfo) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.OrPredicates(predicates...))
}

// Not applies the not operator on the given predicate.
func Not(p predicate.TenantUserInfo) predicate.TenantUserInfo {
	return predicate.TenantUserInfo(sql.NotPredicates(p))
}
