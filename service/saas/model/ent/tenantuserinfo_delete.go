// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"phoenix/service/saas/model/ent/predicate"
	"phoenix/service/saas/model/ent/tenantuserinfo"

	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// TenantUserInfoDelete is the builder for deleting a TenantUserInfo entity.
type TenantUserInfoDelete struct {
	config
	hooks    []Hook
	mutation *TenantUserInfoMutation
}

// Where appends a list predicates to the TenantUserInfoDelete builder.
func (tuid *TenantUserInfoDelete) Where(ps ...predicate.TenantUserInfo) *TenantUserInfoDelete {
	tuid.mutation.Where(ps...)
	return tuid
}

// Exec executes the deletion query and returns how many vertices were deleted.
func (tuid *TenantUserInfoDelete) Exec(ctx context.Context) (int, error) {
	return withHooks(ctx, tuid.sqlExec, tuid.mutation, tuid.hooks)
}

// ExecX is like Exec, but panics if an error occurs.
func (tuid *TenantUserInfoDelete) ExecX(ctx context.Context) int {
	n, err := tuid.Exec(ctx)
	if err != nil {
		panic(err)
	}
	return n
}

func (tuid *TenantUserInfoDelete) sqlExec(ctx context.Context) (int, error) {
	_spec := sqlgraph.NewDeleteSpec(tenantuserinfo.Table, sqlgraph.NewFieldSpec(tenantuserinfo.FieldID, field.TypeString))
	if ps := tuid.mutation.predicates; len(ps) > 0 {
		_spec.Predicate = func(selector *sql.Selector) {
			for i := range ps {
				ps[i](selector)
			}
		}
	}
	affected, err := sqlgraph.DeleteNodes(ctx, tuid.driver, _spec)
	if err != nil && sqlgraph.IsConstraintError(err) {
		err = &ConstraintError{msg: err.Error(), wrap: err}
	}
	tuid.mutation.done = true
	return affected, err
}

// TenantUserInfoDeleteOne is the builder for deleting a single TenantUserInfo entity.
type TenantUserInfoDeleteOne struct {
	tuid *TenantUserInfoDelete
}

// Where appends a list predicates to the TenantUserInfoDelete builder.
func (tuido *TenantUserInfoDeleteOne) Where(ps ...predicate.TenantUserInfo) *TenantUserInfoDeleteOne {
	tuido.tuid.mutation.Where(ps...)
	return tuido
}

// Exec executes the deletion query.
func (tuido *TenantUserInfoDeleteOne) Exec(ctx context.Context) error {
	n, err := tuido.tuid.Exec(ctx)
	switch {
	case err != nil:
		return err
	case n == 0:
		return &NotFoundError{tenantuserinfo.Label}
	default:
		return nil
	}
}

// ExecX is like Exec, but panics if an error occurs.
func (tuido *TenantUserInfoDeleteOne) ExecX(ctx context.Context) {
	if err := tuido.Exec(ctx); err != nil {
		panic(err)
	}
}
