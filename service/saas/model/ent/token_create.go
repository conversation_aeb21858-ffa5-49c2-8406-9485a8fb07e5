// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/token"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
)

// TokenCreate is the builder for creating a Token entity.
type TokenCreate struct {
	config
	mutation *TokenMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (tc *TokenCreate) SetCreatedAt(t time.Time) *TokenCreate {
	tc.mutation.SetCreatedAt(t)
	return tc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (tc *TokenCreate) SetNillableCreatedAt(t *time.Time) *TokenCreate {
	if t != nil {
		tc.SetCreatedAt(*t)
	}
	return tc
}

// SetUpdatedAt sets the "updated_at" field.
func (tc *TokenCreate) SetUpdatedAt(t time.Time) *TokenCreate {
	tc.mutation.SetUpdatedAt(t)
	return tc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (tc *TokenCreate) SetNillableUpdatedAt(t *time.Time) *TokenCreate {
	if t != nil {
		tc.SetUpdatedAt(*t)
	}
	return tc
}

// SetStatus sets the "status" field.
func (tc *TokenCreate) SetStatus(b bool) *TokenCreate {
	tc.mutation.SetStatus(b)
	return tc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (tc *TokenCreate) SetNillableStatus(b *bool) *TokenCreate {
	if b != nil {
		tc.SetStatus(*b)
	}
	return tc
}

// SetUID sets the "uid" field.
func (tc *TokenCreate) SetUID(s string) *TokenCreate {
	tc.mutation.SetUID(s)
	return tc
}

// SetToken sets the "token" field.
func (tc *TokenCreate) SetToken(s string) *TokenCreate {
	tc.mutation.SetToken(s)
	return tc
}

// SetSource sets the "source" field.
func (tc *TokenCreate) SetSource(s string) *TokenCreate {
	tc.mutation.SetSource(s)
	return tc
}

// SetExpiredAt sets the "expired_at" field.
func (tc *TokenCreate) SetExpiredAt(t time.Time) *TokenCreate {
	tc.mutation.SetExpiredAt(t)
	return tc
}

// SetTenantID sets the "tenant_id" field.
func (tc *TokenCreate) SetTenantID(s string) *TokenCreate {
	tc.mutation.SetTenantID(s)
	return tc
}

// SetNillableTenantID sets the "tenant_id" field if the given value is not nil.
func (tc *TokenCreate) SetNillableTenantID(s *string) *TokenCreate {
	if s != nil {
		tc.SetTenantID(*s)
	}
	return tc
}

// SetDeviceKind sets the "device_kind" field.
func (tc *TokenCreate) SetDeviceKind(s string) *TokenCreate {
	tc.mutation.SetDeviceKind(s)
	return tc
}

// SetNillableDeviceKind sets the "device_kind" field if the given value is not nil.
func (tc *TokenCreate) SetNillableDeviceKind(s *string) *TokenCreate {
	if s != nil {
		tc.SetDeviceKind(*s)
	}
	return tc
}

// SetIP sets the "ip" field.
func (tc *TokenCreate) SetIP(s string) *TokenCreate {
	tc.mutation.SetIP(s)
	return tc
}

// SetNillableIP sets the "ip" field if the given value is not nil.
func (tc *TokenCreate) SetNillableIP(s *string) *TokenCreate {
	if s != nil {
		tc.SetIP(*s)
	}
	return tc
}

// SetID sets the "id" field.
func (tc *TokenCreate) SetID(s string) *TokenCreate {
	tc.mutation.SetID(s)
	return tc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (tc *TokenCreate) SetNillableID(s *string) *TokenCreate {
	if s != nil {
		tc.SetID(*s)
	}
	return tc
}

// Mutation returns the TokenMutation object of the builder.
func (tc *TokenCreate) Mutation() *TokenMutation {
	return tc.mutation
}

// Save creates the Token in the database.
func (tc *TokenCreate) Save(ctx context.Context) (*Token, error) {
	tc.defaults()
	return withHooks(ctx, tc.sqlSave, tc.mutation, tc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (tc *TokenCreate) SaveX(ctx context.Context) *Token {
	v, err := tc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tc *TokenCreate) Exec(ctx context.Context) error {
	_, err := tc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tc *TokenCreate) ExecX(ctx context.Context) {
	if err := tc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (tc *TokenCreate) defaults() {
	if _, ok := tc.mutation.CreatedAt(); !ok {
		v := token.DefaultCreatedAt()
		tc.mutation.SetCreatedAt(v)
	}
	if _, ok := tc.mutation.UpdatedAt(); !ok {
		v := token.DefaultUpdatedAt()
		tc.mutation.SetUpdatedAt(v)
	}
	if _, ok := tc.mutation.Status(); !ok {
		v := token.DefaultStatus
		tc.mutation.SetStatus(v)
	}
	if _, ok := tc.mutation.ID(); !ok {
		v := token.DefaultID()
		tc.mutation.SetID(v)
	}
}

// check runs all checks and user-defined validators on the builder.
func (tc *TokenCreate) check() error {
	if _, ok := tc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Token.created_at"`)}
	}
	if _, ok := tc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Token.updated_at"`)}
	}
	if _, ok := tc.mutation.UID(); !ok {
		return &ValidationError{Name: "uid", err: errors.New(`ent: missing required field "Token.uid"`)}
	}
	if _, ok := tc.mutation.Token(); !ok {
		return &ValidationError{Name: "token", err: errors.New(`ent: missing required field "Token.token"`)}
	}
	if _, ok := tc.mutation.Source(); !ok {
		return &ValidationError{Name: "source", err: errors.New(`ent: missing required field "Token.source"`)}
	}
	if _, ok := tc.mutation.ExpiredAt(); !ok {
		return &ValidationError{Name: "expired_at", err: errors.New(`ent: missing required field "Token.expired_at"`)}
	}
	return nil
}

func (tc *TokenCreate) sqlSave(ctx context.Context) (*Token, error) {
	if err := tc.check(); err != nil {
		return nil, err
	}
	_node, _spec := tc.createSpec()
	if err := sqlgraph.CreateNode(ctx, tc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected Token.ID type: %T", _spec.ID.Value)
		}
	}
	tc.mutation.id = &_node.ID
	tc.mutation.done = true
	return _node, nil
}

func (tc *TokenCreate) createSpec() (*Token, *sqlgraph.CreateSpec) {
	var (
		_node = &Token{config: tc.config}
		_spec = sqlgraph.NewCreateSpec(token.Table, sqlgraph.NewFieldSpec(token.FieldID, field.TypeString))
	)
	_spec.OnConflict = tc.conflict
	if id, ok := tc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := tc.mutation.CreatedAt(); ok {
		_spec.SetField(token.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := tc.mutation.UpdatedAt(); ok {
		_spec.SetField(token.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := tc.mutation.Status(); ok {
		_spec.SetField(token.FieldStatus, field.TypeBool, value)
		_node.Status = value
	}
	if value, ok := tc.mutation.UID(); ok {
		_spec.SetField(token.FieldUID, field.TypeString, value)
		_node.UID = value
	}
	if value, ok := tc.mutation.Token(); ok {
		_spec.SetField(token.FieldToken, field.TypeString, value)
		_node.Token = value
	}
	if value, ok := tc.mutation.Source(); ok {
		_spec.SetField(token.FieldSource, field.TypeString, value)
		_node.Source = value
	}
	if value, ok := tc.mutation.ExpiredAt(); ok {
		_spec.SetField(token.FieldExpiredAt, field.TypeTime, value)
		_node.ExpiredAt = value
	}
	if value, ok := tc.mutation.TenantID(); ok {
		_spec.SetField(token.FieldTenantID, field.TypeString, value)
		_node.TenantID = value
	}
	if value, ok := tc.mutation.DeviceKind(); ok {
		_spec.SetField(token.FieldDeviceKind, field.TypeString, value)
		_node.DeviceKind = value
	}
	if value, ok := tc.mutation.IP(); ok {
		_spec.SetField(token.FieldIP, field.TypeString, value)
		_node.IP = value
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Token.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.TokenUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (tc *TokenCreate) OnConflict(opts ...sql.ConflictOption) *TokenUpsertOne {
	tc.conflict = opts
	return &TokenUpsertOne{
		create: tc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Token.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (tc *TokenCreate) OnConflictColumns(columns ...string) *TokenUpsertOne {
	tc.conflict = append(tc.conflict, sql.ConflictColumns(columns...))
	return &TokenUpsertOne{
		create: tc,
	}
}

type (
	// TokenUpsertOne is the builder for "upsert"-ing
	//  one Token node.
	TokenUpsertOne struct {
		create *TokenCreate
	}

	// TokenUpsert is the "OnConflict" setter.
	TokenUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *TokenUpsert) SetUpdatedAt(v time.Time) *TokenUpsert {
	u.Set(token.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *TokenUpsert) UpdateUpdatedAt() *TokenUpsert {
	u.SetExcluded(token.FieldUpdatedAt)
	return u
}

// SetStatus sets the "status" field.
func (u *TokenUpsert) SetStatus(v bool) *TokenUpsert {
	u.Set(token.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *TokenUpsert) UpdateStatus() *TokenUpsert {
	u.SetExcluded(token.FieldStatus)
	return u
}

// ClearStatus clears the value of the "status" field.
func (u *TokenUpsert) ClearStatus() *TokenUpsert {
	u.SetNull(token.FieldStatus)
	return u
}

// SetUID sets the "uid" field.
func (u *TokenUpsert) SetUID(v string) *TokenUpsert {
	u.Set(token.FieldUID, v)
	return u
}

// UpdateUID sets the "uid" field to the value that was provided on create.
func (u *TokenUpsert) UpdateUID() *TokenUpsert {
	u.SetExcluded(token.FieldUID)
	return u
}

// SetToken sets the "token" field.
func (u *TokenUpsert) SetToken(v string) *TokenUpsert {
	u.Set(token.FieldToken, v)
	return u
}

// UpdateToken sets the "token" field to the value that was provided on create.
func (u *TokenUpsert) UpdateToken() *TokenUpsert {
	u.SetExcluded(token.FieldToken)
	return u
}

// SetSource sets the "source" field.
func (u *TokenUpsert) SetSource(v string) *TokenUpsert {
	u.Set(token.FieldSource, v)
	return u
}

// UpdateSource sets the "source" field to the value that was provided on create.
func (u *TokenUpsert) UpdateSource() *TokenUpsert {
	u.SetExcluded(token.FieldSource)
	return u
}

// SetExpiredAt sets the "expired_at" field.
func (u *TokenUpsert) SetExpiredAt(v time.Time) *TokenUpsert {
	u.Set(token.FieldExpiredAt, v)
	return u
}

// UpdateExpiredAt sets the "expired_at" field to the value that was provided on create.
func (u *TokenUpsert) UpdateExpiredAt() *TokenUpsert {
	u.SetExcluded(token.FieldExpiredAt)
	return u
}

// SetTenantID sets the "tenant_id" field.
func (u *TokenUpsert) SetTenantID(v string) *TokenUpsert {
	u.Set(token.FieldTenantID, v)
	return u
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *TokenUpsert) UpdateTenantID() *TokenUpsert {
	u.SetExcluded(token.FieldTenantID)
	return u
}

// ClearTenantID clears the value of the "tenant_id" field.
func (u *TokenUpsert) ClearTenantID() *TokenUpsert {
	u.SetNull(token.FieldTenantID)
	return u
}

// SetDeviceKind sets the "device_kind" field.
func (u *TokenUpsert) SetDeviceKind(v string) *TokenUpsert {
	u.Set(token.FieldDeviceKind, v)
	return u
}

// UpdateDeviceKind sets the "device_kind" field to the value that was provided on create.
func (u *TokenUpsert) UpdateDeviceKind() *TokenUpsert {
	u.SetExcluded(token.FieldDeviceKind)
	return u
}

// ClearDeviceKind clears the value of the "device_kind" field.
func (u *TokenUpsert) ClearDeviceKind() *TokenUpsert {
	u.SetNull(token.FieldDeviceKind)
	return u
}

// SetIP sets the "ip" field.
func (u *TokenUpsert) SetIP(v string) *TokenUpsert {
	u.Set(token.FieldIP, v)
	return u
}

// UpdateIP sets the "ip" field to the value that was provided on create.
func (u *TokenUpsert) UpdateIP() *TokenUpsert {
	u.SetExcluded(token.FieldIP)
	return u
}

// ClearIP clears the value of the "ip" field.
func (u *TokenUpsert) ClearIP() *TokenUpsert {
	u.SetNull(token.FieldIP)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Token.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(token.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *TokenUpsertOne) UpdateNewValues() *TokenUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(token.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(token.FieldCreatedAt)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Token.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *TokenUpsertOne) Ignore() *TokenUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *TokenUpsertOne) DoNothing() *TokenUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the TokenCreate.OnConflict
// documentation for more info.
func (u *TokenUpsertOne) Update(set func(*TokenUpsert)) *TokenUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&TokenUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *TokenUpsertOne) SetUpdatedAt(v time.Time) *TokenUpsertOne {
	return u.Update(func(s *TokenUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *TokenUpsertOne) UpdateUpdatedAt() *TokenUpsertOne {
	return u.Update(func(s *TokenUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetStatus sets the "status" field.
func (u *TokenUpsertOne) SetStatus(v bool) *TokenUpsertOne {
	return u.Update(func(s *TokenUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *TokenUpsertOne) UpdateStatus() *TokenUpsertOne {
	return u.Update(func(s *TokenUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *TokenUpsertOne) ClearStatus() *TokenUpsertOne {
	return u.Update(func(s *TokenUpsert) {
		s.ClearStatus()
	})
}

// SetUID sets the "uid" field.
func (u *TokenUpsertOne) SetUID(v string) *TokenUpsertOne {
	return u.Update(func(s *TokenUpsert) {
		s.SetUID(v)
	})
}

// UpdateUID sets the "uid" field to the value that was provided on create.
func (u *TokenUpsertOne) UpdateUID() *TokenUpsertOne {
	return u.Update(func(s *TokenUpsert) {
		s.UpdateUID()
	})
}

// SetToken sets the "token" field.
func (u *TokenUpsertOne) SetToken(v string) *TokenUpsertOne {
	return u.Update(func(s *TokenUpsert) {
		s.SetToken(v)
	})
}

// UpdateToken sets the "token" field to the value that was provided on create.
func (u *TokenUpsertOne) UpdateToken() *TokenUpsertOne {
	return u.Update(func(s *TokenUpsert) {
		s.UpdateToken()
	})
}

// SetSource sets the "source" field.
func (u *TokenUpsertOne) SetSource(v string) *TokenUpsertOne {
	return u.Update(func(s *TokenUpsert) {
		s.SetSource(v)
	})
}

// UpdateSource sets the "source" field to the value that was provided on create.
func (u *TokenUpsertOne) UpdateSource() *TokenUpsertOne {
	return u.Update(func(s *TokenUpsert) {
		s.UpdateSource()
	})
}

// SetExpiredAt sets the "expired_at" field.
func (u *TokenUpsertOne) SetExpiredAt(v time.Time) *TokenUpsertOne {
	return u.Update(func(s *TokenUpsert) {
		s.SetExpiredAt(v)
	})
}

// UpdateExpiredAt sets the "expired_at" field to the value that was provided on create.
func (u *TokenUpsertOne) UpdateExpiredAt() *TokenUpsertOne {
	return u.Update(func(s *TokenUpsert) {
		s.UpdateExpiredAt()
	})
}

// SetTenantID sets the "tenant_id" field.
func (u *TokenUpsertOne) SetTenantID(v string) *TokenUpsertOne {
	return u.Update(func(s *TokenUpsert) {
		s.SetTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *TokenUpsertOne) UpdateTenantID() *TokenUpsertOne {
	return u.Update(func(s *TokenUpsert) {
		s.UpdateTenantID()
	})
}

// ClearTenantID clears the value of the "tenant_id" field.
func (u *TokenUpsertOne) ClearTenantID() *TokenUpsertOne {
	return u.Update(func(s *TokenUpsert) {
		s.ClearTenantID()
	})
}

// SetDeviceKind sets the "device_kind" field.
func (u *TokenUpsertOne) SetDeviceKind(v string) *TokenUpsertOne {
	return u.Update(func(s *TokenUpsert) {
		s.SetDeviceKind(v)
	})
}

// UpdateDeviceKind sets the "device_kind" field to the value that was provided on create.
func (u *TokenUpsertOne) UpdateDeviceKind() *TokenUpsertOne {
	return u.Update(func(s *TokenUpsert) {
		s.UpdateDeviceKind()
	})
}

// ClearDeviceKind clears the value of the "device_kind" field.
func (u *TokenUpsertOne) ClearDeviceKind() *TokenUpsertOne {
	return u.Update(func(s *TokenUpsert) {
		s.ClearDeviceKind()
	})
}

// SetIP sets the "ip" field.
func (u *TokenUpsertOne) SetIP(v string) *TokenUpsertOne {
	return u.Update(func(s *TokenUpsert) {
		s.SetIP(v)
	})
}

// UpdateIP sets the "ip" field to the value that was provided on create.
func (u *TokenUpsertOne) UpdateIP() *TokenUpsertOne {
	return u.Update(func(s *TokenUpsert) {
		s.UpdateIP()
	})
}

// ClearIP clears the value of the "ip" field.
func (u *TokenUpsertOne) ClearIP() *TokenUpsertOne {
	return u.Update(func(s *TokenUpsert) {
		s.ClearIP()
	})
}

// Exec executes the query.
func (u *TokenUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for TokenCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *TokenUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *TokenUpsertOne) ID(ctx context.Context) (id string, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: TokenUpsertOne.ID is not supported by MySQL driver. Use TokenUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *TokenUpsertOne) IDX(ctx context.Context) string {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// TokenCreateBulk is the builder for creating many Token entities in bulk.
type TokenCreateBulk struct {
	config
	err      error
	builders []*TokenCreate
	conflict []sql.ConflictOption
}

// Save creates the Token entities in the database.
func (tcb *TokenCreateBulk) Save(ctx context.Context) ([]*Token, error) {
	if tcb.err != nil {
		return nil, tcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(tcb.builders))
	nodes := make([]*Token, len(tcb.builders))
	mutators := make([]Mutator, len(tcb.builders))
	for i := range tcb.builders {
		func(i int, root context.Context) {
			builder := tcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*TokenMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, tcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = tcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, tcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, tcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (tcb *TokenCreateBulk) SaveX(ctx context.Context) []*Token {
	v, err := tcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (tcb *TokenCreateBulk) Exec(ctx context.Context) error {
	_, err := tcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (tcb *TokenCreateBulk) ExecX(ctx context.Context) {
	if err := tcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Token.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.TokenUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (tcb *TokenCreateBulk) OnConflict(opts ...sql.ConflictOption) *TokenUpsertBulk {
	tcb.conflict = opts
	return &TokenUpsertBulk{
		create: tcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Token.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (tcb *TokenCreateBulk) OnConflictColumns(columns ...string) *TokenUpsertBulk {
	tcb.conflict = append(tcb.conflict, sql.ConflictColumns(columns...))
	return &TokenUpsertBulk{
		create: tcb,
	}
}

// TokenUpsertBulk is the builder for "upsert"-ing
// a bulk of Token nodes.
type TokenUpsertBulk struct {
	create *TokenCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Token.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(token.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *TokenUpsertBulk) UpdateNewValues() *TokenUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(token.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(token.FieldCreatedAt)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Token.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *TokenUpsertBulk) Ignore() *TokenUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *TokenUpsertBulk) DoNothing() *TokenUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the TokenCreateBulk.OnConflict
// documentation for more info.
func (u *TokenUpsertBulk) Update(set func(*TokenUpsert)) *TokenUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&TokenUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *TokenUpsertBulk) SetUpdatedAt(v time.Time) *TokenUpsertBulk {
	return u.Update(func(s *TokenUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *TokenUpsertBulk) UpdateUpdatedAt() *TokenUpsertBulk {
	return u.Update(func(s *TokenUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetStatus sets the "status" field.
func (u *TokenUpsertBulk) SetStatus(v bool) *TokenUpsertBulk {
	return u.Update(func(s *TokenUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *TokenUpsertBulk) UpdateStatus() *TokenUpsertBulk {
	return u.Update(func(s *TokenUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *TokenUpsertBulk) ClearStatus() *TokenUpsertBulk {
	return u.Update(func(s *TokenUpsert) {
		s.ClearStatus()
	})
}

// SetUID sets the "uid" field.
func (u *TokenUpsertBulk) SetUID(v string) *TokenUpsertBulk {
	return u.Update(func(s *TokenUpsert) {
		s.SetUID(v)
	})
}

// UpdateUID sets the "uid" field to the value that was provided on create.
func (u *TokenUpsertBulk) UpdateUID() *TokenUpsertBulk {
	return u.Update(func(s *TokenUpsert) {
		s.UpdateUID()
	})
}

// SetToken sets the "token" field.
func (u *TokenUpsertBulk) SetToken(v string) *TokenUpsertBulk {
	return u.Update(func(s *TokenUpsert) {
		s.SetToken(v)
	})
}

// UpdateToken sets the "token" field to the value that was provided on create.
func (u *TokenUpsertBulk) UpdateToken() *TokenUpsertBulk {
	return u.Update(func(s *TokenUpsert) {
		s.UpdateToken()
	})
}

// SetSource sets the "source" field.
func (u *TokenUpsertBulk) SetSource(v string) *TokenUpsertBulk {
	return u.Update(func(s *TokenUpsert) {
		s.SetSource(v)
	})
}

// UpdateSource sets the "source" field to the value that was provided on create.
func (u *TokenUpsertBulk) UpdateSource() *TokenUpsertBulk {
	return u.Update(func(s *TokenUpsert) {
		s.UpdateSource()
	})
}

// SetExpiredAt sets the "expired_at" field.
func (u *TokenUpsertBulk) SetExpiredAt(v time.Time) *TokenUpsertBulk {
	return u.Update(func(s *TokenUpsert) {
		s.SetExpiredAt(v)
	})
}

// UpdateExpiredAt sets the "expired_at" field to the value that was provided on create.
func (u *TokenUpsertBulk) UpdateExpiredAt() *TokenUpsertBulk {
	return u.Update(func(s *TokenUpsert) {
		s.UpdateExpiredAt()
	})
}

// SetTenantID sets the "tenant_id" field.
func (u *TokenUpsertBulk) SetTenantID(v string) *TokenUpsertBulk {
	return u.Update(func(s *TokenUpsert) {
		s.SetTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *TokenUpsertBulk) UpdateTenantID() *TokenUpsertBulk {
	return u.Update(func(s *TokenUpsert) {
		s.UpdateTenantID()
	})
}

// ClearTenantID clears the value of the "tenant_id" field.
func (u *TokenUpsertBulk) ClearTenantID() *TokenUpsertBulk {
	return u.Update(func(s *TokenUpsert) {
		s.ClearTenantID()
	})
}

// SetDeviceKind sets the "device_kind" field.
func (u *TokenUpsertBulk) SetDeviceKind(v string) *TokenUpsertBulk {
	return u.Update(func(s *TokenUpsert) {
		s.SetDeviceKind(v)
	})
}

// UpdateDeviceKind sets the "device_kind" field to the value that was provided on create.
func (u *TokenUpsertBulk) UpdateDeviceKind() *TokenUpsertBulk {
	return u.Update(func(s *TokenUpsert) {
		s.UpdateDeviceKind()
	})
}

// ClearDeviceKind clears the value of the "device_kind" field.
func (u *TokenUpsertBulk) ClearDeviceKind() *TokenUpsertBulk {
	return u.Update(func(s *TokenUpsert) {
		s.ClearDeviceKind()
	})
}

// SetIP sets the "ip" field.
func (u *TokenUpsertBulk) SetIP(v string) *TokenUpsertBulk {
	return u.Update(func(s *TokenUpsert) {
		s.SetIP(v)
	})
}

// UpdateIP sets the "ip" field to the value that was provided on create.
func (u *TokenUpsertBulk) UpdateIP() *TokenUpsertBulk {
	return u.Update(func(s *TokenUpsert) {
		s.UpdateIP()
	})
}

// ClearIP clears the value of the "ip" field.
func (u *TokenUpsertBulk) ClearIP() *TokenUpsertBulk {
	return u.Update(func(s *TokenUpsert) {
		s.ClearIP()
	})
}

// Exec executes the query.
func (u *TokenUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the TokenCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for TokenCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *TokenUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
