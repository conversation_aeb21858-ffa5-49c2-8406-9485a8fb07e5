// Code generated by ent, DO NOT EDIT.

package ent

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/model/ent/api"
	"phoenix/service/saas/model/ent/button"
	"phoenix/service/saas/model/ent/group"
	"phoenix/service/saas/model/ent/menu"
	"phoenix/service/saas/model/ent/role"
	"phoenix/service/saas/model/ent/tenant"
	"phoenix/service/saas/model/ent/user"
	"time"

	"entgo.io/ent/dialect"
	"entgo.io/ent/dialect/sql"
	"entgo.io/ent/dialect/sql/sqlgraph"
	"entgo.io/ent/schema/field"
	uuid "github.com/gofrs/uuid/v5"
)

// RoleCreate is the builder for creating a Role entity.
type RoleCreate struct {
	config
	mutation *RoleMutation
	hooks    []Hook
	conflict []sql.ConflictOption
}

// SetCreatedAt sets the "created_at" field.
func (rc *RoleCreate) SetCreatedAt(t time.Time) *RoleCreate {
	rc.mutation.SetCreatedAt(t)
	return rc
}

// SetNillableCreatedAt sets the "created_at" field if the given value is not nil.
func (rc *RoleCreate) SetNillableCreatedAt(t *time.Time) *RoleCreate {
	if t != nil {
		rc.SetCreatedAt(*t)
	}
	return rc
}

// SetUpdatedAt sets the "updated_at" field.
func (rc *RoleCreate) SetUpdatedAt(t time.Time) *RoleCreate {
	rc.mutation.SetUpdatedAt(t)
	return rc
}

// SetNillableUpdatedAt sets the "updated_at" field if the given value is not nil.
func (rc *RoleCreate) SetNillableUpdatedAt(t *time.Time) *RoleCreate {
	if t != nil {
		rc.SetUpdatedAt(*t)
	}
	return rc
}

// SetStatus sets the "status" field.
func (rc *RoleCreate) SetStatus(b bool) *RoleCreate {
	rc.mutation.SetStatus(b)
	return rc
}

// SetNillableStatus sets the "status" field if the given value is not nil.
func (rc *RoleCreate) SetNillableStatus(b *bool) *RoleCreate {
	if b != nil {
		rc.SetStatus(*b)
	}
	return rc
}

// SetSort sets the "sort" field.
func (rc *RoleCreate) SetSort(u uint32) *RoleCreate {
	rc.mutation.SetSort(u)
	return rc
}

// SetNillableSort sets the "sort" field if the given value is not nil.
func (rc *RoleCreate) SetNillableSort(u *uint32) *RoleCreate {
	if u != nil {
		rc.SetSort(*u)
	}
	return rc
}

// SetTenantID sets the "tenant_id" field.
func (rc *RoleCreate) SetTenantID(s string) *RoleCreate {
	rc.mutation.SetTenantID(s)
	return rc
}

// SetDeletedAt sets the "deleted_at" field.
func (rc *RoleCreate) SetDeletedAt(t time.Time) *RoleCreate {
	rc.mutation.SetDeletedAt(t)
	return rc
}

// SetNillableDeletedAt sets the "deleted_at" field if the given value is not nil.
func (rc *RoleCreate) SetNillableDeletedAt(t *time.Time) *RoleCreate {
	if t != nil {
		rc.SetDeletedAt(*t)
	}
	return rc
}

// SetName sets the "name" field.
func (rc *RoleCreate) SetName(s string) *RoleCreate {
	rc.mutation.SetName(s)
	return rc
}

// SetNillableName sets the "name" field if the given value is not nil.
func (rc *RoleCreate) SetNillableName(s *string) *RoleCreate {
	if s != nil {
		rc.SetName(*s)
	}
	return rc
}

// SetCode sets the "code" field.
func (rc *RoleCreate) SetCode(s string) *RoleCreate {
	rc.mutation.SetCode(s)
	return rc
}

// SetUID sets the "uid" field.
func (rc *RoleCreate) SetUID(u uuid.UUID) *RoleCreate {
	rc.mutation.SetUID(u)
	return rc
}

// SetNillableUID sets the "uid" field if the given value is not nil.
func (rc *RoleCreate) SetNillableUID(u *uuid.UUID) *RoleCreate {
	if u != nil {
		rc.SetUID(*u)
	}
	return rc
}

// SetDefaultRouter sets the "default_router" field.
func (rc *RoleCreate) SetDefaultRouter(s string) *RoleCreate {
	rc.mutation.SetDefaultRouter(s)
	return rc
}

// SetNillableDefaultRouter sets the "default_router" field if the given value is not nil.
func (rc *RoleCreate) SetNillableDefaultRouter(s *string) *RoleCreate {
	if s != nil {
		rc.SetDefaultRouter(*s)
	}
	return rc
}

// SetRemark sets the "remark" field.
func (rc *RoleCreate) SetRemark(s string) *RoleCreate {
	rc.mutation.SetRemark(s)
	return rc
}

// SetNillableRemark sets the "remark" field if the given value is not nil.
func (rc *RoleCreate) SetNillableRemark(s *string) *RoleCreate {
	if s != nil {
		rc.SetRemark(*s)
	}
	return rc
}

// SetOrganizationID sets the "organization_id" field.
func (rc *RoleCreate) SetOrganizationID(s string) *RoleCreate {
	rc.mutation.SetOrganizationID(s)
	return rc
}

// SetNillableOrganizationID sets the "organization_id" field if the given value is not nil.
func (rc *RoleCreate) SetNillableOrganizationID(s *string) *RoleCreate {
	if s != nil {
		rc.SetOrganizationID(*s)
	}
	return rc
}

// SetParentID sets the "parent_id" field.
func (rc *RoleCreate) SetParentID(s string) *RoleCreate {
	rc.mutation.SetParentID(s)
	return rc
}

// SetNillableParentID sets the "parent_id" field if the given value is not nil.
func (rc *RoleCreate) SetNillableParentID(s *string) *RoleCreate {
	if s != nil {
		rc.SetParentID(*s)
	}
	return rc
}

// SetID sets the "id" field.
func (rc *RoleCreate) SetID(s string) *RoleCreate {
	rc.mutation.SetID(s)
	return rc
}

// SetNillableID sets the "id" field if the given value is not nil.
func (rc *RoleCreate) SetNillableID(s *string) *RoleCreate {
	if s != nil {
		rc.SetID(*s)
	}
	return rc
}

// SetTenant sets the "tenant" edge to the Tenant entity.
func (rc *RoleCreate) SetTenant(t *Tenant) *RoleCreate {
	return rc.SetTenantID(t.ID)
}

// AddButtonIDs adds the "buttons" edge to the Button entity by IDs.
func (rc *RoleCreate) AddButtonIDs(ids ...string) *RoleCreate {
	rc.mutation.AddButtonIDs(ids...)
	return rc
}

// AddButtons adds the "buttons" edges to the Button entity.
func (rc *RoleCreate) AddButtons(b ...*Button) *RoleCreate {
	ids := make([]string, len(b))
	for i := range b {
		ids[i] = b[i].ID
	}
	return rc.AddButtonIDs(ids...)
}

// AddMenuIDs adds the "menus" edge to the Menu entity by IDs.
func (rc *RoleCreate) AddMenuIDs(ids ...string) *RoleCreate {
	rc.mutation.AddMenuIDs(ids...)
	return rc
}

// AddMenus adds the "menus" edges to the Menu entity.
func (rc *RoleCreate) AddMenus(m ...*Menu) *RoleCreate {
	ids := make([]string, len(m))
	for i := range m {
		ids[i] = m[i].ID
	}
	return rc.AddMenuIDs(ids...)
}

// AddAPIIDs adds the "apis" edge to the API entity by IDs.
func (rc *RoleCreate) AddAPIIDs(ids ...string) *RoleCreate {
	rc.mutation.AddAPIIDs(ids...)
	return rc
}

// AddApis adds the "apis" edges to the API entity.
func (rc *RoleCreate) AddApis(a ...*API) *RoleCreate {
	ids := make([]string, len(a))
	for i := range a {
		ids[i] = a[i].ID
	}
	return rc.AddAPIIDs(ids...)
}

// AddGroupIDs adds the "groups" edge to the Group entity by IDs.
func (rc *RoleCreate) AddGroupIDs(ids ...string) *RoleCreate {
	rc.mutation.AddGroupIDs(ids...)
	return rc
}

// AddGroups adds the "groups" edges to the Group entity.
func (rc *RoleCreate) AddGroups(g ...*Group) *RoleCreate {
	ids := make([]string, len(g))
	for i := range g {
		ids[i] = g[i].ID
	}
	return rc.AddGroupIDs(ids...)
}

// AddUserIDs adds the "users" edge to the User entity by IDs.
func (rc *RoleCreate) AddUserIDs(ids ...string) *RoleCreate {
	rc.mutation.AddUserIDs(ids...)
	return rc
}

// AddUsers adds the "users" edges to the User entity.
func (rc *RoleCreate) AddUsers(u ...*User) *RoleCreate {
	ids := make([]string, len(u))
	for i := range u {
		ids[i] = u[i].ID
	}
	return rc.AddUserIDs(ids...)
}

// SetParent sets the "parent" edge to the Role entity.
func (rc *RoleCreate) SetParent(r *Role) *RoleCreate {
	return rc.SetParentID(r.ID)
}

// AddChildIDs adds the "children" edge to the Role entity by IDs.
func (rc *RoleCreate) AddChildIDs(ids ...string) *RoleCreate {
	rc.mutation.AddChildIDs(ids...)
	return rc
}

// AddChildren adds the "children" edges to the Role entity.
func (rc *RoleCreate) AddChildren(r ...*Role) *RoleCreate {
	ids := make([]string, len(r))
	for i := range r {
		ids[i] = r[i].ID
	}
	return rc.AddChildIDs(ids...)
}

// Mutation returns the RoleMutation object of the builder.
func (rc *RoleCreate) Mutation() *RoleMutation {
	return rc.mutation
}

// Save creates the Role in the database.
func (rc *RoleCreate) Save(ctx context.Context) (*Role, error) {
	if err := rc.defaults(); err != nil {
		return nil, err
	}
	return withHooks(ctx, rc.sqlSave, rc.mutation, rc.hooks)
}

// SaveX calls Save and panics if Save returns an error.
func (rc *RoleCreate) SaveX(ctx context.Context) *Role {
	v, err := rc.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (rc *RoleCreate) Exec(ctx context.Context) error {
	_, err := rc.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (rc *RoleCreate) ExecX(ctx context.Context) {
	if err := rc.Exec(ctx); err != nil {
		panic(err)
	}
}

// defaults sets the default values of the builder before save.
func (rc *RoleCreate) defaults() error {
	if _, ok := rc.mutation.CreatedAt(); !ok {
		if role.DefaultCreatedAt == nil {
			return fmt.Errorf("ent: uninitialized role.DefaultCreatedAt (forgotten import ent/runtime?)")
		}
		v := role.DefaultCreatedAt()
		rc.mutation.SetCreatedAt(v)
	}
	if _, ok := rc.mutation.UpdatedAt(); !ok {
		if role.DefaultUpdatedAt == nil {
			return fmt.Errorf("ent: uninitialized role.DefaultUpdatedAt (forgotten import ent/runtime?)")
		}
		v := role.DefaultUpdatedAt()
		rc.mutation.SetUpdatedAt(v)
	}
	if _, ok := rc.mutation.Status(); !ok {
		v := role.DefaultStatus
		rc.mutation.SetStatus(v)
	}
	if _, ok := rc.mutation.Sort(); !ok {
		v := role.DefaultSort
		rc.mutation.SetSort(v)
	}
	if _, ok := rc.mutation.Name(); !ok {
		v := role.DefaultName
		rc.mutation.SetName(v)
	}
	if _, ok := rc.mutation.UID(); !ok {
		if role.DefaultUID == nil {
			return fmt.Errorf("ent: uninitialized role.DefaultUID (forgotten import ent/runtime?)")
		}
		v := role.DefaultUID()
		rc.mutation.SetUID(v)
	}
	if _, ok := rc.mutation.DefaultRouter(); !ok {
		v := role.DefaultDefaultRouter
		rc.mutation.SetDefaultRouter(v)
	}
	if _, ok := rc.mutation.Remark(); !ok {
		v := role.DefaultRemark
		rc.mutation.SetRemark(v)
	}
	if _, ok := rc.mutation.OrganizationID(); !ok {
		v := role.DefaultOrganizationID
		rc.mutation.SetOrganizationID(v)
	}
	if _, ok := rc.mutation.ID(); !ok {
		if role.DefaultID == nil {
			return fmt.Errorf("ent: uninitialized role.DefaultID (forgotten import ent/runtime?)")
		}
		v := role.DefaultID()
		rc.mutation.SetID(v)
	}
	return nil
}

// check runs all checks and user-defined validators on the builder.
func (rc *RoleCreate) check() error {
	if _, ok := rc.mutation.CreatedAt(); !ok {
		return &ValidationError{Name: "created_at", err: errors.New(`ent: missing required field "Role.created_at"`)}
	}
	if _, ok := rc.mutation.UpdatedAt(); !ok {
		return &ValidationError{Name: "updated_at", err: errors.New(`ent: missing required field "Role.updated_at"`)}
	}
	if _, ok := rc.mutation.Sort(); !ok {
		return &ValidationError{Name: "sort", err: errors.New(`ent: missing required field "Role.sort"`)}
	}
	if _, ok := rc.mutation.TenantID(); !ok {
		return &ValidationError{Name: "tenant_id", err: errors.New(`ent: missing required field "Role.tenant_id"`)}
	}
	if _, ok := rc.mutation.Name(); !ok {
		return &ValidationError{Name: "name", err: errors.New(`ent: missing required field "Role.name"`)}
	}
	if _, ok := rc.mutation.Code(); !ok {
		return &ValidationError{Name: "code", err: errors.New(`ent: missing required field "Role.code"`)}
	}
	if _, ok := rc.mutation.UID(); !ok {
		return &ValidationError{Name: "uid", err: errors.New(`ent: missing required field "Role.uid"`)}
	}
	if _, ok := rc.mutation.DefaultRouter(); !ok {
		return &ValidationError{Name: "default_router", err: errors.New(`ent: missing required field "Role.default_router"`)}
	}
	if _, ok := rc.mutation.Remark(); !ok {
		return &ValidationError{Name: "remark", err: errors.New(`ent: missing required field "Role.remark"`)}
	}
	if _, ok := rc.mutation.OrganizationID(); !ok {
		return &ValidationError{Name: "organization_id", err: errors.New(`ent: missing required field "Role.organization_id"`)}
	}
	if _, ok := rc.mutation.TenantID(); !ok {
		return &ValidationError{Name: "tenant", err: errors.New(`ent: missing required edge "Role.tenant"`)}
	}
	return nil
}

func (rc *RoleCreate) sqlSave(ctx context.Context) (*Role, error) {
	if err := rc.check(); err != nil {
		return nil, err
	}
	_node, _spec := rc.createSpec()
	if err := sqlgraph.CreateNode(ctx, rc.driver, _spec); err != nil {
		if sqlgraph.IsConstraintError(err) {
			err = &ConstraintError{msg: err.Error(), wrap: err}
		}
		return nil, err
	}
	if _spec.ID.Value != nil {
		if id, ok := _spec.ID.Value.(string); ok {
			_node.ID = id
		} else {
			return nil, fmt.Errorf("unexpected Role.ID type: %T", _spec.ID.Value)
		}
	}
	rc.mutation.id = &_node.ID
	rc.mutation.done = true
	return _node, nil
}

func (rc *RoleCreate) createSpec() (*Role, *sqlgraph.CreateSpec) {
	var (
		_node = &Role{config: rc.config}
		_spec = sqlgraph.NewCreateSpec(role.Table, sqlgraph.NewFieldSpec(role.FieldID, field.TypeString))
	)
	_spec.OnConflict = rc.conflict
	if id, ok := rc.mutation.ID(); ok {
		_node.ID = id
		_spec.ID.Value = id
	}
	if value, ok := rc.mutation.CreatedAt(); ok {
		_spec.SetField(role.FieldCreatedAt, field.TypeTime, value)
		_node.CreatedAt = value
	}
	if value, ok := rc.mutation.UpdatedAt(); ok {
		_spec.SetField(role.FieldUpdatedAt, field.TypeTime, value)
		_node.UpdatedAt = value
	}
	if value, ok := rc.mutation.Status(); ok {
		_spec.SetField(role.FieldStatus, field.TypeBool, value)
		_node.Status = value
	}
	if value, ok := rc.mutation.Sort(); ok {
		_spec.SetField(role.FieldSort, field.TypeUint32, value)
		_node.Sort = value
	}
	if value, ok := rc.mutation.DeletedAt(); ok {
		_spec.SetField(role.FieldDeletedAt, field.TypeTime, value)
		_node.DeletedAt = value
	}
	if value, ok := rc.mutation.Name(); ok {
		_spec.SetField(role.FieldName, field.TypeString, value)
		_node.Name = value
	}
	if value, ok := rc.mutation.Code(); ok {
		_spec.SetField(role.FieldCode, field.TypeString, value)
		_node.Code = value
	}
	if value, ok := rc.mutation.UID(); ok {
		_spec.SetField(role.FieldUID, field.TypeUUID, value)
		_node.UID = value
	}
	if value, ok := rc.mutation.DefaultRouter(); ok {
		_spec.SetField(role.FieldDefaultRouter, field.TypeString, value)
		_node.DefaultRouter = value
	}
	if value, ok := rc.mutation.Remark(); ok {
		_spec.SetField(role.FieldRemark, field.TypeString, value)
		_node.Remark = value
	}
	if value, ok := rc.mutation.OrganizationID(); ok {
		_spec.SetField(role.FieldOrganizationID, field.TypeString, value)
		_node.OrganizationID = value
	}
	if nodes := rc.mutation.TenantIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: false,
			Table:   role.TenantTable,
			Columns: []string{role.TenantColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(tenant.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.TenantID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := rc.mutation.ButtonsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.ButtonsTable,
			Columns: role.ButtonsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(button.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := rc.mutation.MenusIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.MenusTable,
			Columns: role.MenusPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(menu.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := rc.mutation.ApisIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.ApisTable,
			Columns: role.ApisPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(api.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := rc.mutation.GroupsIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.GroupsTable,
			Columns: role.GroupsPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(group.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := rc.mutation.UsersIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2M,
			Inverse: false,
			Table:   role.UsersTable,
			Columns: role.UsersPrimaryKey,
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(user.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := rc.mutation.ParentIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.M2O,
			Inverse: true,
			Table:   role.ParentTable,
			Columns: []string{role.ParentColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_node.ParentID = nodes[0]
		_spec.Edges = append(_spec.Edges, edge)
	}
	if nodes := rc.mutation.ChildrenIDs(); len(nodes) > 0 {
		edge := &sqlgraph.EdgeSpec{
			Rel:     sqlgraph.O2M,
			Inverse: false,
			Table:   role.ChildrenTable,
			Columns: []string{role.ChildrenColumn},
			Bidi:    false,
			Target: &sqlgraph.EdgeTarget{
				IDSpec: sqlgraph.NewFieldSpec(role.FieldID, field.TypeString),
			},
		}
		for _, k := range nodes {
			edge.Target.Nodes = append(edge.Target.Nodes, k)
		}
		_spec.Edges = append(_spec.Edges, edge)
	}
	return _node, _spec
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Role.Create().
//		SetCreatedAt(v).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.RoleUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (rc *RoleCreate) OnConflict(opts ...sql.ConflictOption) *RoleUpsertOne {
	rc.conflict = opts
	return &RoleUpsertOne{
		create: rc,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Role.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (rc *RoleCreate) OnConflictColumns(columns ...string) *RoleUpsertOne {
	rc.conflict = append(rc.conflict, sql.ConflictColumns(columns...))
	return &RoleUpsertOne{
		create: rc,
	}
}

type (
	// RoleUpsertOne is the builder for "upsert"-ing
	//  one Role node.
	RoleUpsertOne struct {
		create *RoleCreate
	}

	// RoleUpsert is the "OnConflict" setter.
	RoleUpsert struct {
		*sql.UpdateSet
	}
)

// SetUpdatedAt sets the "updated_at" field.
func (u *RoleUpsert) SetUpdatedAt(v time.Time) *RoleUpsert {
	u.Set(role.FieldUpdatedAt, v)
	return u
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *RoleUpsert) UpdateUpdatedAt() *RoleUpsert {
	u.SetExcluded(role.FieldUpdatedAt)
	return u
}

// SetStatus sets the "status" field.
func (u *RoleUpsert) SetStatus(v bool) *RoleUpsert {
	u.Set(role.FieldStatus, v)
	return u
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *RoleUpsert) UpdateStatus() *RoleUpsert {
	u.SetExcluded(role.FieldStatus)
	return u
}

// ClearStatus clears the value of the "status" field.
func (u *RoleUpsert) ClearStatus() *RoleUpsert {
	u.SetNull(role.FieldStatus)
	return u
}

// SetSort sets the "sort" field.
func (u *RoleUpsert) SetSort(v uint32) *RoleUpsert {
	u.Set(role.FieldSort, v)
	return u
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *RoleUpsert) UpdateSort() *RoleUpsert {
	u.SetExcluded(role.FieldSort)
	return u
}

// AddSort adds v to the "sort" field.
func (u *RoleUpsert) AddSort(v uint32) *RoleUpsert {
	u.Add(role.FieldSort, v)
	return u
}

// SetTenantID sets the "tenant_id" field.
func (u *RoleUpsert) SetTenantID(v string) *RoleUpsert {
	u.Set(role.FieldTenantID, v)
	return u
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *RoleUpsert) UpdateTenantID() *RoleUpsert {
	u.SetExcluded(role.FieldTenantID)
	return u
}

// SetDeletedAt sets the "deleted_at" field.
func (u *RoleUpsert) SetDeletedAt(v time.Time) *RoleUpsert {
	u.Set(role.FieldDeletedAt, v)
	return u
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *RoleUpsert) UpdateDeletedAt() *RoleUpsert {
	u.SetExcluded(role.FieldDeletedAt)
	return u
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *RoleUpsert) ClearDeletedAt() *RoleUpsert {
	u.SetNull(role.FieldDeletedAt)
	return u
}

// SetName sets the "name" field.
func (u *RoleUpsert) SetName(v string) *RoleUpsert {
	u.Set(role.FieldName, v)
	return u
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *RoleUpsert) UpdateName() *RoleUpsert {
	u.SetExcluded(role.FieldName)
	return u
}

// SetCode sets the "code" field.
func (u *RoleUpsert) SetCode(v string) *RoleUpsert {
	u.Set(role.FieldCode, v)
	return u
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *RoleUpsert) UpdateCode() *RoleUpsert {
	u.SetExcluded(role.FieldCode)
	return u
}

// SetDefaultRouter sets the "default_router" field.
func (u *RoleUpsert) SetDefaultRouter(v string) *RoleUpsert {
	u.Set(role.FieldDefaultRouter, v)
	return u
}

// UpdateDefaultRouter sets the "default_router" field to the value that was provided on create.
func (u *RoleUpsert) UpdateDefaultRouter() *RoleUpsert {
	u.SetExcluded(role.FieldDefaultRouter)
	return u
}

// SetRemark sets the "remark" field.
func (u *RoleUpsert) SetRemark(v string) *RoleUpsert {
	u.Set(role.FieldRemark, v)
	return u
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *RoleUpsert) UpdateRemark() *RoleUpsert {
	u.SetExcluded(role.FieldRemark)
	return u
}

// SetOrganizationID sets the "organization_id" field.
func (u *RoleUpsert) SetOrganizationID(v string) *RoleUpsert {
	u.Set(role.FieldOrganizationID, v)
	return u
}

// UpdateOrganizationID sets the "organization_id" field to the value that was provided on create.
func (u *RoleUpsert) UpdateOrganizationID() *RoleUpsert {
	u.SetExcluded(role.FieldOrganizationID)
	return u
}

// SetParentID sets the "parent_id" field.
func (u *RoleUpsert) SetParentID(v string) *RoleUpsert {
	u.Set(role.FieldParentID, v)
	return u
}

// UpdateParentID sets the "parent_id" field to the value that was provided on create.
func (u *RoleUpsert) UpdateParentID() *RoleUpsert {
	u.SetExcluded(role.FieldParentID)
	return u
}

// ClearParentID clears the value of the "parent_id" field.
func (u *RoleUpsert) ClearParentID() *RoleUpsert {
	u.SetNull(role.FieldParentID)
	return u
}

// UpdateNewValues updates the mutable fields using the new values that were set on create except the ID field.
// Using this option is equivalent to using:
//
//	client.Role.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(role.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *RoleUpsertOne) UpdateNewValues() *RoleUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		if _, exists := u.create.mutation.ID(); exists {
			s.SetIgnore(role.FieldID)
		}
		if _, exists := u.create.mutation.CreatedAt(); exists {
			s.SetIgnore(role.FieldCreatedAt)
		}
		if _, exists := u.create.mutation.UID(); exists {
			s.SetIgnore(role.FieldUID)
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Role.Create().
//	    OnConflict(sql.ResolveWithIgnore()).
//	    Exec(ctx)
func (u *RoleUpsertOne) Ignore() *RoleUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *RoleUpsertOne) DoNothing() *RoleUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the RoleCreate.OnConflict
// documentation for more info.
func (u *RoleUpsertOne) Update(set func(*RoleUpsert)) *RoleUpsertOne {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&RoleUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *RoleUpsertOne) SetUpdatedAt(v time.Time) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *RoleUpsertOne) UpdateUpdatedAt() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetStatus sets the "status" field.
func (u *RoleUpsertOne) SetStatus(v bool) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *RoleUpsertOne) UpdateStatus() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *RoleUpsertOne) ClearStatus() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.ClearStatus()
	})
}

// SetSort sets the "sort" field.
func (u *RoleUpsertOne) SetSort(v uint32) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.SetSort(v)
	})
}

// AddSort adds v to the "sort" field.
func (u *RoleUpsertOne) AddSort(v uint32) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.AddSort(v)
	})
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *RoleUpsertOne) UpdateSort() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateSort()
	})
}

// SetTenantID sets the "tenant_id" field.
func (u *RoleUpsertOne) SetTenantID(v string) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.SetTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *RoleUpsertOne) UpdateTenantID() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateTenantID()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *RoleUpsertOne) SetDeletedAt(v time.Time) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *RoleUpsertOne) UpdateDeletedAt() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *RoleUpsertOne) ClearDeletedAt() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.ClearDeletedAt()
	})
}

// SetName sets the "name" field.
func (u *RoleUpsertOne) SetName(v string) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *RoleUpsertOne) UpdateName() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateName()
	})
}

// SetCode sets the "code" field.
func (u *RoleUpsertOne) SetCode(v string) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.SetCode(v)
	})
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *RoleUpsertOne) UpdateCode() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateCode()
	})
}

// SetDefaultRouter sets the "default_router" field.
func (u *RoleUpsertOne) SetDefaultRouter(v string) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.SetDefaultRouter(v)
	})
}

// UpdateDefaultRouter sets the "default_router" field to the value that was provided on create.
func (u *RoleUpsertOne) UpdateDefaultRouter() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateDefaultRouter()
	})
}

// SetRemark sets the "remark" field.
func (u *RoleUpsertOne) SetRemark(v string) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *RoleUpsertOne) UpdateRemark() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateRemark()
	})
}

// SetOrganizationID sets the "organization_id" field.
func (u *RoleUpsertOne) SetOrganizationID(v string) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.SetOrganizationID(v)
	})
}

// UpdateOrganizationID sets the "organization_id" field to the value that was provided on create.
func (u *RoleUpsertOne) UpdateOrganizationID() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateOrganizationID()
	})
}

// SetParentID sets the "parent_id" field.
func (u *RoleUpsertOne) SetParentID(v string) *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.SetParentID(v)
	})
}

// UpdateParentID sets the "parent_id" field to the value that was provided on create.
func (u *RoleUpsertOne) UpdateParentID() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateParentID()
	})
}

// ClearParentID clears the value of the "parent_id" field.
func (u *RoleUpsertOne) ClearParentID() *RoleUpsertOne {
	return u.Update(func(s *RoleUpsert) {
		s.ClearParentID()
	})
}

// Exec executes the query.
func (u *RoleUpsertOne) Exec(ctx context.Context) error {
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for RoleCreate.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *RoleUpsertOne) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}

// Exec executes the UPSERT query and returns the inserted/updated ID.
func (u *RoleUpsertOne) ID(ctx context.Context) (id string, err error) {
	if u.create.driver.Dialect() == dialect.MySQL {
		// In case of "ON CONFLICT", there is no way to get back non-numeric ID
		// fields from the database since MySQL does not support the RETURNING clause.
		return id, errors.New("ent: RoleUpsertOne.ID is not supported by MySQL driver. Use RoleUpsertOne.Exec instead")
	}
	node, err := u.create.Save(ctx)
	if err != nil {
		return id, err
	}
	return node.ID, nil
}

// IDX is like ID, but panics if an error occurs.
func (u *RoleUpsertOne) IDX(ctx context.Context) string {
	id, err := u.ID(ctx)
	if err != nil {
		panic(err)
	}
	return id
}

// RoleCreateBulk is the builder for creating many Role entities in bulk.
type RoleCreateBulk struct {
	config
	err      error
	builders []*RoleCreate
	conflict []sql.ConflictOption
}

// Save creates the Role entities in the database.
func (rcb *RoleCreateBulk) Save(ctx context.Context) ([]*Role, error) {
	if rcb.err != nil {
		return nil, rcb.err
	}
	specs := make([]*sqlgraph.CreateSpec, len(rcb.builders))
	nodes := make([]*Role, len(rcb.builders))
	mutators := make([]Mutator, len(rcb.builders))
	for i := range rcb.builders {
		func(i int, root context.Context) {
			builder := rcb.builders[i]
			builder.defaults()
			var mut Mutator = MutateFunc(func(ctx context.Context, m Mutation) (Value, error) {
				mutation, ok := m.(*RoleMutation)
				if !ok {
					return nil, fmt.Errorf("unexpected mutation type %T", m)
				}
				if err := builder.check(); err != nil {
					return nil, err
				}
				builder.mutation = mutation
				var err error
				nodes[i], specs[i] = builder.createSpec()
				if i < len(mutators)-1 {
					_, err = mutators[i+1].Mutate(root, rcb.builders[i+1].mutation)
				} else {
					spec := &sqlgraph.BatchCreateSpec{Nodes: specs}
					spec.OnConflict = rcb.conflict
					// Invoke the actual operation on the latest mutation in the chain.
					if err = sqlgraph.BatchCreate(ctx, rcb.driver, spec); err != nil {
						if sqlgraph.IsConstraintError(err) {
							err = &ConstraintError{msg: err.Error(), wrap: err}
						}
					}
				}
				if err != nil {
					return nil, err
				}
				mutation.id = &nodes[i].ID
				mutation.done = true
				return nodes[i], nil
			})
			for i := len(builder.hooks) - 1; i >= 0; i-- {
				mut = builder.hooks[i](mut)
			}
			mutators[i] = mut
		}(i, ctx)
	}
	if len(mutators) > 0 {
		if _, err := mutators[0].Mutate(ctx, rcb.builders[0].mutation); err != nil {
			return nil, err
		}
	}
	return nodes, nil
}

// SaveX is like Save, but panics if an error occurs.
func (rcb *RoleCreateBulk) SaveX(ctx context.Context) []*Role {
	v, err := rcb.Save(ctx)
	if err != nil {
		panic(err)
	}
	return v
}

// Exec executes the query.
func (rcb *RoleCreateBulk) Exec(ctx context.Context) error {
	_, err := rcb.Save(ctx)
	return err
}

// ExecX is like Exec, but panics if an error occurs.
func (rcb *RoleCreateBulk) ExecX(ctx context.Context) {
	if err := rcb.Exec(ctx); err != nil {
		panic(err)
	}
}

// OnConflict allows configuring the `ON CONFLICT` / `ON DUPLICATE KEY` clause
// of the `INSERT` statement. For example:
//
//	client.Role.CreateBulk(builders...).
//		OnConflict(
//			// Update the row with the new values
//			// the was proposed for insertion.
//			sql.ResolveWithNewValues(),
//		).
//		// Override some of the fields with custom
//		// update values.
//		Update(func(u *ent.RoleUpsert) {
//			SetCreatedAt(v+v).
//		}).
//		Exec(ctx)
func (rcb *RoleCreateBulk) OnConflict(opts ...sql.ConflictOption) *RoleUpsertBulk {
	rcb.conflict = opts
	return &RoleUpsertBulk{
		create: rcb,
	}
}

// OnConflictColumns calls `OnConflict` and configures the columns
// as conflict target. Using this option is equivalent to using:
//
//	client.Role.Create().
//		OnConflict(sql.ConflictColumns(columns...)).
//		Exec(ctx)
func (rcb *RoleCreateBulk) OnConflictColumns(columns ...string) *RoleUpsertBulk {
	rcb.conflict = append(rcb.conflict, sql.ConflictColumns(columns...))
	return &RoleUpsertBulk{
		create: rcb,
	}
}

// RoleUpsertBulk is the builder for "upsert"-ing
// a bulk of Role nodes.
type RoleUpsertBulk struct {
	create *RoleCreateBulk
}

// UpdateNewValues updates the mutable fields using the new values that
// were set on create. Using this option is equivalent to using:
//
//	client.Role.Create().
//		OnConflict(
//			sql.ResolveWithNewValues(),
//			sql.ResolveWith(func(u *sql.UpdateSet) {
//				u.SetIgnore(role.FieldID)
//			}),
//		).
//		Exec(ctx)
func (u *RoleUpsertBulk) UpdateNewValues() *RoleUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithNewValues())
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(s *sql.UpdateSet) {
		for _, b := range u.create.builders {
			if _, exists := b.mutation.ID(); exists {
				s.SetIgnore(role.FieldID)
			}
			if _, exists := b.mutation.CreatedAt(); exists {
				s.SetIgnore(role.FieldCreatedAt)
			}
			if _, exists := b.mutation.UID(); exists {
				s.SetIgnore(role.FieldUID)
			}
		}
	}))
	return u
}

// Ignore sets each column to itself in case of conflict.
// Using this option is equivalent to using:
//
//	client.Role.Create().
//		OnConflict(sql.ResolveWithIgnore()).
//		Exec(ctx)
func (u *RoleUpsertBulk) Ignore() *RoleUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWithIgnore())
	return u
}

// DoNothing configures the conflict_action to `DO NOTHING`.
// Supported only by SQLite and PostgreSQL.
func (u *RoleUpsertBulk) DoNothing() *RoleUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.DoNothing())
	return u
}

// Update allows overriding fields `UPDATE` values. See the RoleCreateBulk.OnConflict
// documentation for more info.
func (u *RoleUpsertBulk) Update(set func(*RoleUpsert)) *RoleUpsertBulk {
	u.create.conflict = append(u.create.conflict, sql.ResolveWith(func(update *sql.UpdateSet) {
		set(&RoleUpsert{UpdateSet: update})
	}))
	return u
}

// SetUpdatedAt sets the "updated_at" field.
func (u *RoleUpsertBulk) SetUpdatedAt(v time.Time) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.SetUpdatedAt(v)
	})
}

// UpdateUpdatedAt sets the "updated_at" field to the value that was provided on create.
func (u *RoleUpsertBulk) UpdateUpdatedAt() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateUpdatedAt()
	})
}

// SetStatus sets the "status" field.
func (u *RoleUpsertBulk) SetStatus(v bool) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.SetStatus(v)
	})
}

// UpdateStatus sets the "status" field to the value that was provided on create.
func (u *RoleUpsertBulk) UpdateStatus() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateStatus()
	})
}

// ClearStatus clears the value of the "status" field.
func (u *RoleUpsertBulk) ClearStatus() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.ClearStatus()
	})
}

// SetSort sets the "sort" field.
func (u *RoleUpsertBulk) SetSort(v uint32) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.SetSort(v)
	})
}

// AddSort adds v to the "sort" field.
func (u *RoleUpsertBulk) AddSort(v uint32) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.AddSort(v)
	})
}

// UpdateSort sets the "sort" field to the value that was provided on create.
func (u *RoleUpsertBulk) UpdateSort() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateSort()
	})
}

// SetTenantID sets the "tenant_id" field.
func (u *RoleUpsertBulk) SetTenantID(v string) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.SetTenantID(v)
	})
}

// UpdateTenantID sets the "tenant_id" field to the value that was provided on create.
func (u *RoleUpsertBulk) UpdateTenantID() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateTenantID()
	})
}

// SetDeletedAt sets the "deleted_at" field.
func (u *RoleUpsertBulk) SetDeletedAt(v time.Time) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.SetDeletedAt(v)
	})
}

// UpdateDeletedAt sets the "deleted_at" field to the value that was provided on create.
func (u *RoleUpsertBulk) UpdateDeletedAt() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateDeletedAt()
	})
}

// ClearDeletedAt clears the value of the "deleted_at" field.
func (u *RoleUpsertBulk) ClearDeletedAt() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.ClearDeletedAt()
	})
}

// SetName sets the "name" field.
func (u *RoleUpsertBulk) SetName(v string) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.SetName(v)
	})
}

// UpdateName sets the "name" field to the value that was provided on create.
func (u *RoleUpsertBulk) UpdateName() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateName()
	})
}

// SetCode sets the "code" field.
func (u *RoleUpsertBulk) SetCode(v string) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.SetCode(v)
	})
}

// UpdateCode sets the "code" field to the value that was provided on create.
func (u *RoleUpsertBulk) UpdateCode() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateCode()
	})
}

// SetDefaultRouter sets the "default_router" field.
func (u *RoleUpsertBulk) SetDefaultRouter(v string) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.SetDefaultRouter(v)
	})
}

// UpdateDefaultRouter sets the "default_router" field to the value that was provided on create.
func (u *RoleUpsertBulk) UpdateDefaultRouter() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateDefaultRouter()
	})
}

// SetRemark sets the "remark" field.
func (u *RoleUpsertBulk) SetRemark(v string) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.SetRemark(v)
	})
}

// UpdateRemark sets the "remark" field to the value that was provided on create.
func (u *RoleUpsertBulk) UpdateRemark() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateRemark()
	})
}

// SetOrganizationID sets the "organization_id" field.
func (u *RoleUpsertBulk) SetOrganizationID(v string) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.SetOrganizationID(v)
	})
}

// UpdateOrganizationID sets the "organization_id" field to the value that was provided on create.
func (u *RoleUpsertBulk) UpdateOrganizationID() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateOrganizationID()
	})
}

// SetParentID sets the "parent_id" field.
func (u *RoleUpsertBulk) SetParentID(v string) *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.SetParentID(v)
	})
}

// UpdateParentID sets the "parent_id" field to the value that was provided on create.
func (u *RoleUpsertBulk) UpdateParentID() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.UpdateParentID()
	})
}

// ClearParentID clears the value of the "parent_id" field.
func (u *RoleUpsertBulk) ClearParentID() *RoleUpsertBulk {
	return u.Update(func(s *RoleUpsert) {
		s.ClearParentID()
	})
}

// Exec executes the query.
func (u *RoleUpsertBulk) Exec(ctx context.Context) error {
	if u.create.err != nil {
		return u.create.err
	}
	for i, b := range u.create.builders {
		if len(b.conflict) != 0 {
			return fmt.Errorf("ent: OnConflict was set for builder %d. Set it on the RoleCreateBulk instead", i)
		}
	}
	if len(u.create.conflict) == 0 {
		return errors.New("ent: missing options for RoleCreateBulk.OnConflict")
	}
	return u.create.Exec(ctx)
}

// ExecX is like Exec, but panics if an error occurs.
func (u *RoleUpsertBulk) ExecX(ctx context.Context) {
	if err := u.create.Exec(ctx); err != nil {
		panic(err)
	}
}
