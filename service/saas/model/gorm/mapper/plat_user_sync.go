package mapper

import (
	"context"
	"gorm.io/gorm"
	"time"
)

const TableNamePlatUserSync = "plat_user_sync"

type PlatUserSyncClient struct {
	DB *gorm.DB
}

func NewPlatUserSyncClient(db *gorm.DB) *PlatUserSyncClient {
	return &PlatUserSyncClient{
		DB: db,
	}
}

// QueryByPlatUserID 平台用户id和平台代码查询
func (s *PlatUserSyncClient) QueryByPlatUserID(ctx context.Context, platUserID, platCode string) (result PlatUserSync, err error) {
	err = s.DB.WithContext(ctx).Where("plat_user_id = ? and plat_code = ?", platUserID, platCode).First(&result).Error
	return
}

type PlatUserSync struct {
	PlatUserID string    `gorm:"primaryKey;type:varchar(100);not null;comment:平台用户id"`
	PlatCode   string    `gorm:"primaryKey;type:varchar(50);not null;comment:平台代码"`
	UserID     string    `gorm:"type:varchar(64);not null;comment:用户id"`
	Username   string    `gorm:"type:varchar(255);comment:用户名"`
	CreatedAt  time.Time `gorm:"default:CURRENT_TIMESTAMP;onUpdate:CURRENT_TIMESTAMP;comment:创建时间"`
	UpdatedAt  time.Time `gorm:"null;comment:更新时间"`
}

// TableName 返回模型对应的数据库表名
func (PlatUserSync) TableName() string {
	return TableNamePlatUserSync
}
