package mapper

import (
	"time"

	"golang.org/x/net/context"
	"gorm.io/gorm"
)

const TableNameWorkflowTemplateNode = "workflow_template_node"

const (
	WorkflowTemplateNodeKindApprove          = "approve"    // 审批
	WorkflowTemplateNodeKindApproveForwarded = "approve_cc" // 审批抄送
	WorkflowTemplateSigningKindAnd           = "and"        // 会签
	WorkflowTemplateSigningKindOr            = "or"         // 或签

	WorkflowTemplateAssignKind = "cc" // 抄送
)

type WorkflowTemplateNodeClient struct {
	DB *gorm.DB
}

func NewWorkflowTemplateNodeClient(db *gorm.DB) *WorkflowTemplateNodeClient {
	return &WorkflowTemplateNodeClient{DB: db}
}

func (c *WorkflowTemplateNodeClient) BatchCreateWithTx(ctx context.Context, tx *gorm.DB, nodes []WorkflowTemplateNode) error {
	return tx.WithContext(ctx).Create(&nodes).Error
}

func (c *WorkflowTemplateNodeClient) GetByVersionID(ctx context.Context, versionID string) (nodes []WorkflowTemplateNode, err error) {
	return nodes, c.DB.WithContext(ctx).Where("version_id = ?", versionID).Find(&nodes).Error
}

type WorkflowTemplateNode struct {
	ID           string    `gorm:"primaryKey;type:varchar(64);not null;comment:主键ID"`
	VersionID    string    `gorm:"type:varchar(64);not null;comment:模板版本 id"`
	Name         string    `gorm:"type:varchar(256);not null;comment:名称"`
	Kind         string    `gorm:"type:varchar(20);not null;comment:环节类型 审批-approval,抄送-approval_cc"`
	SigningKind  string    `gorm:"type:varchar(10);not null;comment:签署类型 或-or,与-and"`
	CCKind       string    `gorm:"type:varchar(20);not null;comment:抄送类型 岗位-position,角色-role,指定-designate,自定义-custom,group_designate-集团指定"`
	ApprovalKind string    `gorm:"type:varchar(20);not null;comment:审批类型 岗位-position,角色-role,指定-designate,自定义-custom,group_designate-集团指定"`
	CreatedAt    time.Time `gorm:"default:CURRENT_TIMESTAMP;comment:创建时间"`
	NodeOrder    int       `gorm:"type:int(11);not null;comment:节点顺序"`
	SallyNodeID  string    `gorm:"type:varchar(64);not null;comment:sally节点id"`
}

// TableName 返回模型对应的数据库表名
func (WorkflowTemplateNode) TableName() string {
	return TableNameWorkflowTemplateNode
}
