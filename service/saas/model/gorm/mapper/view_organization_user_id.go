package mapper

import (
	"context"
	"gorm.io/gorm"
)

const (
	TableNameViewOrganizationUserId = "view_organization_user_id"
)

// ViewOrganizationUserIdClient 客户端
type ViewOrganizationUserIdClient struct {
	DB *gorm.DB `name:"saas_db"`
}

func NewViewOrganizationUserIdClient(db *gorm.DB) *ViewOrganizationUserIdClient {
	return &ViewOrganizationUserIdClient{
		DB: (*gorm.DB)(db),
	}
}

// GetOrgAllUserIds 获取本级和子级所有用户id
func (g *ViewOrganizationUserIdClient) GetOrgAllUserIds(ctx context.Context, orgId string) (userIds []string, err error) {

	err = g.DB.Table(TableNameViewOrganizationUserId).Where("organization_id = ?", orgId).Select("user_id").Group("user_id").Find(&userIds).Error
	if err != nil {
		return nil, err
	}
	return userIds, nil
}

// GetOrgAllOrgIds 获取本级和子级所有组织id
func (g *ViewOrganizationUserIdClient) GetOrgAllOrgIds(ctx context.Context, orgId string) (orgIds []string, err error) {
	err = g.DB.Table(TableNameViewOrganizationUserId).
		Select("user_belong_org_id").
		Where("organization_id = ?", orgId).
		Group("user_belong_org_id").
		Find(&orgIds).Error
	if err != nil {
		return nil, err
	}
	return orgIds, nil
}

func (g *ViewOrganizationUserIdClient) GetAllOrgsAndUsersByOrgId(ctx context.Context, orgID string) ([]ViewOrganizationUserId, error) {
	var viewOrganizationUserId []ViewOrganizationUserId
	err := g.DB.Table(TableNameViewOrganizationUserId).Where("organization_id = ?", orgID).Find(&viewOrganizationUserId).Error
	if err != nil {
		return nil, err
	}
	return viewOrganizationUserId, nil
}

type ViewOrganizationUserId struct {
	OrganizationID  string `gorm:"column:organization_id"` // 祖先组织id
	UserBelongOrgID string `gorm:"user_belong_org_id"`     // 当前组织id
	UserID          string `gorm:"user_id"`                // 用户id
}

func (ViewOrganizationUserId) TableName() string {
	return TableNameViewOrganizationUserId
}
