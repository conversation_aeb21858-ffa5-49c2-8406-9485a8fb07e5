package mapper

import (
	"time"

	"golang.org/x/net/context"
	"gorm.io/gorm"
)

const TableNameWorkflowTemplateVersion = "workflow_template_version"
const (
	WorkflowTemplateVersionKindCustom     = "custom"     // 自定义
	WorkflowTemplateVersionKindDepartment = "department" // 部门

	WorkflowTemplateVersionAutoApproveKindClose    = "close"    // 关闭
	WorkflowTemplateVersionAutoApproveKindAdjacent = "adjacent" // 相邻
	WorkflowTemplateVersionAutoApproveKindAny      = "any"      // 任意
)

type WorkflowTemplateVersionClient struct {
	DB *gorm.DB
}

func NewWorkflowTemplateVersionClient(db *gorm.DB) *WorkflowTemplateVersionClient {
	return &WorkflowTemplateVersionClient{DB: db}
}

func (c *WorkflowTemplateVersionClient) BatchCreateWithTx(ctx context.Context, tx *gorm.DB, versions []WorkflowTemplateVersion) error {
	return tx.WithContext(ctx).Create(versions).Error
}

func (c *WorkflowTemplateVersionClient) GetMaxVersionNo(ctx context.Context, mainID string) (maxVersionNo int, err error) {
	err = c.DB.WithContext(ctx).Table(TableNameWorkflowTemplateVersion).Select("IFNULL(MAX(version_no), 0) as version_no").Where("main_id = ?", mainID).Find(&maxVersionNo).Error
	if err != nil {
		return 0, err
	}

	return
}

func (c *WorkflowTemplateVersionClient) GetByID(ctx context.Context, id string) (version WorkflowTemplateVersion, err error) {
	return version, c.DB.WithContext(ctx).Where("id = ?", id).First(&version).Error
}

func (c *WorkflowTemplateVersionClient) GetByBusinessID(ctx context.Context, businessID string) (version WorkflowTemplateVersion, err error) {
	return version, c.DB.WithContext(ctx).Where("business_id = ?", businessID).First(&version).Error
}

type WorkflowTemplateVersionPageParam struct {
	MainID   string `json:"mainId"`
	Page     int    `json:"page"`
	PageSize int    `json:"pageSize"`
	NoPage   bool   `json:"noPage"`
}

// 分页查询
func (c *WorkflowTemplateVersionClient) Page(ctx context.Context, param WorkflowTemplateVersionPageParam) (versions []WorkflowTemplateVersion, total int64, err error) {
	query := c.DB.WithContext(ctx).Table(TableNameWorkflowTemplateVersion).Where("main_id = ?", param.MainID).Count(&total).Order("version_no desc")

	if !param.NoPage {
		query = query.Offset((param.Page - 1) * param.PageSize).Limit(param.PageSize)
	}

	err = query.Find(&versions).Error
	if err != nil {
		return nil, 0, err
	}

	return
}

func (c *WorkflowTemplateVersionClient) GetByPresetID(ctx context.Context, presetID string) (versions []WorkflowTemplateVersion, err error) {
	return versions, c.DB.WithContext(ctx).Where("preset_id = ?", presetID).Find(&versions).Error
}

type WorkflowTemplateVersion struct {
	ID                string    `gorm:"primaryKey;type:varchar(64);not null;comment:主键ID"`
	MainID            string    `gorm:"type:varchar(64);not null;comment:模板 id"`
	Name              string    `gorm:"type:varchar(256);not null;comment:模板名称"`
	VersionNo         int       `gorm:"type:int(11);not null;comment:版本号"`
	Kind              string    `gorm:"type:varchar(10);not null;comment:审批形式 部门-department 自定义-custom"`
	TimeoutWarnStatus bool      `gorm:"type:tinyint(1);not null;comment:超时预警"`
	TimeoutHour       int       `gorm:"type:int(11);not null;comment:超时小时"`
	AutoApproveKind   string    `gorm:"type:varchar(10);not null;comment:自动审批类型 close-关闭,adjacent-相邻,any-任意"`
	SallyTemplateID   string    `gorm:"type:varchar(64);not null;comment:sally模板 id"`
	CreatedBy         string    `gorm:"type:varchar(64);not null;comment:创建人"`
	CreatedAt         time.Time `gorm:"default:CURRENT_TIMESTAMP;comment:创建时间"`
}

// TableName 返回模型对应的数据库表名
func (WorkflowTemplateVersion) TableName() string {
	return TableNameWorkflowTemplateVersion
}
