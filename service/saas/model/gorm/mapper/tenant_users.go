package mapper

import (
	"gorm.io/gorm"
)

const TableNameTenantUser = "tenant_users"

type TenantUserClient struct {
	DB *gorm.DB `name:"saas_db"`
}

func NewTenantUserClient(db *gorm.DB) *TenantUserClient {
	return &TenantUserClient{
		DB: (*gorm.DB)(db),
	}
}

// TenantUser mapped from table <tenant_users>
type TenantUser struct {
	TenantID string `gorm:"column:tenant_id;primaryKey" json:"tenant_id"`
	UserID   string `gorm:"column:user_id;primaryKey" json:"user_id"`
}

// TableName TenantUser's table name
func (*TenantUser) TableName() string {
	return TableNameTenantUser
}
