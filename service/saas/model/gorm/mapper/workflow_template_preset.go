package mapper

import (
	"time"

	"golang.org/x/net/context"
	"gorm.io/gorm"
)

const TableNameWorkflowTemplatePreset = "workflow_template_preset"
const (
	WorkflowTemplatePresetKindDepartment = "department" // 部门
	WorkflowTemplatePresetKindGroup      = "group"      // 集团
)

type WorkflowTemplatePresetClient struct {
	DB *gorm.DB
}

func NewWorkflowTemplatePresetClient(db *gorm.DB) *WorkflowTemplatePresetClient {
	return &WorkflowTemplatePresetClient{DB: db}
}

func (c *WorkflowTemplatePresetClient) Create(ctx context.Context, presetTemplate WorkflowTemplatePreset) error {
	return c.DB.WithContext(ctx).Create(&presetTemplate).Error
}

func (c *WorkflowTemplatePresetClient) Update(ctx context.Context, presetTemplate WorkflowTemplatePreset) error {
	return c.DB.WithContext(ctx).Model(&WorkflowTemplatePreset{}).Where("id = ?", presetTemplate.ID).Updates(presetTemplate).Error
}

func (c *WorkflowTemplatePresetClient) Save(ctx context.Context, presetTemplate WorkflowTemplatePreset) error {
	return c.DB.WithContext(ctx).Save(&presetTemplate).Error
}

func (c *WorkflowTemplatePresetClient) GetByID(ctx context.Context, id string) (presetTemplate WorkflowTemplatePreset, err error) {
	return presetTemplate, c.DB.Where("id = ?", id).First(&presetTemplate).Error
}

type WorkflowTemplatePresetPageParam struct {
	Search   string
	Page     uint64
	PageSize uint64
	NoPage   bool
	TenantID string
}

func (c *WorkflowTemplatePresetClient) Page(ctx context.Context, param WorkflowTemplatePresetPageParam) (presetTemplates []WorkflowTemplatePreset, total int64, err error) {
	query := c.DB.Model(&WorkflowTemplatePreset{}).Where("tenant_id = ?", param.TenantID)
	if param.Search != "" {
		query = query.Where("name LIKE ?", "%"+param.Search+"%")
	}
	query = query.Count(&total).Order("created_at DESC")
	if !param.NoPage {
		query = query.Offset(int((param.Page - 1) * param.PageSize)).Limit(int(param.PageSize))
	}

	err = query.Find(&presetTemplates).Error
	if err != nil {
		return nil, 0, err
	}

	return presetTemplates, total, nil
}

// 通过 business_id 和 kind 查询信息
func (c *WorkflowTemplatePresetClient) GetByBusinessIDAndKind(ctx context.Context, businessID string) (presetTemplate WorkflowTemplatePreset, err error) {
	err = c.DB.WithContext(ctx).Where("business_id = ? AND deleted_at IS NULL", businessID).Find(&presetTemplate).Error
	return presetTemplate, err
}

type WorkflowTemplatePreset struct {
	ID         string     `gorm:"primaryKey;type:varchar(64);not null;comment:主键ID"`
	Name       string     `gorm:"type:varchar(255);not null;comment:流程名称"`
	Kind       string     `gorm:"type:varchar(20);not null;comment:流程类型 部门department ,集团group"`
	BusinessID string     `gorm:"type:varchar(64);not null;comment:流程唯一序列号(业务 id)"`
	CreatedBy  string     `gorm:"type:varchar(64);not null;comment:创建人"`
	UpdatedBy  string     `gorm:"type:varchar(64);comment:更新人"`
	TenantID   string     `gorm:"type:varchar(64);not null;comment:租户 id"`
	CreatedAt  time.Time  `gorm:"default:CURRENT_TIMESTAMP;comment:创建时间"`
	UpdatedAt  time.Time  `gorm:"default:CURRENT_TIMESTAMP;onUpdate:CURRENT_TIMESTAMP;comment:更新时间"`
	DeletedAt  *time.Time `gorm:"comment:删除时间（软删除标识）"`
}

// TableName 返回模型对应的数据库表名
func (WorkflowTemplatePreset) TableName() string {
	return TableNameWorkflowTemplatePreset
}
