/*
-- phoenix.workflow_start_callback_config definition

CREATE TABLE `workflow_start_callback_config` (

	`id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
	`business_id` varchar(100) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '业务ID',
	`business_name` varchar(200) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '业务名称',
	`callback_url` varchar(500) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '回调接口URL',
	`callback_method` varchar(10) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'POST' COMMENT 'HTTP请求方法',
	`auth_type` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'NONE' COMMENT '认证类型:NONE,API_KEY,TOKEN',
	`auth_config` json COLLATE utf8mb4_unicode_ci COMMENT '认证配置(JSON格式)',
	`timeout_ms` int NOT NULL DEFAULT '5000' COMMENT '超时时间(毫秒)',
	`retry_count` int NOT NULL DEFAULT '3' COMMENT '重试次数',
	`is_enabled` tinyint(1) NOT NULL DEFAULT '1' COMMENT '是否启用:0-禁用,1-启用',
	`failure_policy` varchar(20) COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'ALLOW' COMMENT '失败策略:ALLOW-允许继续,DENY-拒绝',
	`description` varchar(500) COLLATE utf8mb4_unicode_ci DEFAULT NULL COMMENT '描述信息',
	`created_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
	`updated_at` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
	PRIMARY KEY (`id`),
	UNIQUE KEY `uk_business_id` (`business_id`),
	KEY `idx_is_enabled` (`is_enabled`)

) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='审批前置回调接口配置表';
*/
package mapper

import (
	"context"
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// WorkflowStartCallbackConfigClient is a client for the workflow_start_callback_config table.
type WorkflowStartCallbackConfigClient struct {
	db *gorm.DB
}

// NewWorkflowStartCallbackConfigClient creates a new client for the workflow_start_callback_config table.
func NewWorkflowStartCallbackConfigClient(db *gorm.DB) *WorkflowStartCallbackConfigClient {
	return &WorkflowStartCallbackConfigClient{db: db}
}

// GetByBusinessID gets a callback config by business id.
func (c *WorkflowStartCallbackConfigClient) GetByBusinessID(ctx context.Context, businessID string) (*WorkflowStartCallbackConfig, error) {
	var config WorkflowStartCallbackConfig
	if err := c.db.WithContext(ctx).Where("business_id = ?", businessID).First(&config).Error; err != nil {
		return nil, err
	}
	return &config, nil
}

// WorkflowStartCallbackConfig 工作流发起预处理回调配置
type WorkflowStartCallbackConfig struct {
	ID             int64           `json:"id" gorm:"primaryKey"`
	BusinessID     string          `json:"business_id" gorm:"column:business_id;uniqueIndex;comment:业务ID"`
	BusinessName   string          `json:"business_name" gorm:"column:business_name;comment:业务名称"`
	CallbackURL    string          `json:"callback_url" gorm:"column:callback_url;comment:回调接口URL"`
	CallbackMethod string          `json:"callback_method" gorm:"column:callback_method;comment:HTTP请求方法"`
	AuthType       string          `json:"auth_type" gorm:"column:auth_type;comment:认证类型:NONE,API_KEY,TOKEN"`
	AuthConfig     json.RawMessage `json:"auth_config" gorm:"column:auth_config;type:text;comment:认证配置(JSON格式)"`
	TimeoutMs      int             `json:"timeout_ms" gorm:"column:timeout_ms;comment:超时时间(毫秒)"`
	RetryCount     int             `json:"retry_count" gorm:"column:retry_count;comment:重试次数"`
	IsEnabled      bool            `json:"is_enabled" gorm:"column:is_enabled;comment:是否启用:0-禁用,1-启用"`
	FailurePolicy  string          `json:"failure_policy" gorm:"column:failure_policy;comment:失败策略:ALLOW-允许继续,DENY-拒绝"`
	Description    string          `json:"description" gorm:"column:description;comment:描述信息"`
	CreatedAt      time.Time       `json:"created_at" gorm:"column:created_at;comment:创建时间"`
}

func (WorkflowStartCallbackConfig) TableName() string {
	return "workflow_start_callback_config"
}
