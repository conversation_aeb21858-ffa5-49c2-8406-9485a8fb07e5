package mapper

import (
	"context"
	"time"

	"gorm.io/gorm"
)

const (
	OperationLogTableName = "operation_logs"
)

type OperationLogClient struct {
	db *gorm.DB
}

func NewOperationLogClient(db *gorm.DB) *OperationLogClient {
	return &OperationLogClient{
		db: db,
	}
}

func (s *OperationLogClient) Create(ctx context.Context, log OperationLogModel) error {
	return s.db.WithContext(ctx).Create(&log).Error
}

// 批量插入操作日志
func (s *OperationLogClient) BatchInsertOperationLogs(logs []OperationLogModel) error {
	// 使用GormDB进行事务处理
	tx := s.db.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 使用GORM自带的CreateInBatches方法进行批量插入
	if err := tx.Create(&logs).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

type OperationLogModel struct {
	UserID       string    `gorm:"column:user_id;type:varchar(18);default:null;comment:用户ID"`
	TenantID     string    `gorm:"column:tenant_id;type:varchar(18);default:null;comment:租户ID"`
	Method       string    `gorm:"column:method;type:varchar(10);not null;comment:请求方法（GET, POST等）"`
	API          string    `gorm:"column:api;type:varchar(200);not null;comment:请求的API路径"`
	IP           string    `gorm:"column:ip;type:varchar(39);default null;comment:请求的IP地址"`
	RequestBody  string    `gorm:"column:request_body;type:json;default:{};comment:请求内容"`
	ResponseBody string    `gorm:"column:response_body;type:json;default:{};comment:响应内容"`
	HttpStatus   int32     `gorm:"column:http_status;type:smallint;default:200;comment:HTTP状态码"`
	OK           bool      `gorm:"column:ok;type:tinyint(1);default:0;comment:是否成功"`
	CreatedAt    time.Time `gorm:"column:created_at;type:timestamp;default:CURRENT_TIMESTAMP"`
}

func (mo OperationLogModel) TableName() string {
	return OperationLogTableName
}
