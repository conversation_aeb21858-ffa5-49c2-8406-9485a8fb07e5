package mapper

import (
	"time"

	"golang.org/x/net/context"
	"gorm.io/gorm"
)

const TableNameWorkflowTemplateMain = "workflow_template_main"

type WorkflowTemplateMainClient struct {
	DB *gorm.DB
}

func NewWorkflowTemplateMainClient(db *gorm.DB) *WorkflowTemplateMainClient {
	return &WorkflowTemplateMainClient{DB: db}
}

type WorkflowTemplateMainPageParam struct {
	Page           int    `json:"page"`
	PageSize       int    `json:"pageSize"`
	NoPage         bool   `json:"noPage"`
	OrganizationID string `json:"organizationId"`
	Search         string `json:"search"`
}

type WorkflowTemplateMainPageItem struct {
	BusinessID string `json:"businessId"`
	WorkflowTemplateMain
	VersionNo      int    `json:"versionNo"`
	Name           string `json:"name"`
	OrganizationID string `json:"organizationId"`
}

func (c *WorkflowTemplateMainClient) Page(ctx context.Context, param WorkflowTemplateMainPageParam) (templates []WorkflowTemplateMainPageItem, total int64, err error) {
	query := c.DB.WithContext(ctx).Table(TableNameWorkflowTemplatePreset+" as t1").
		Joins("join "+TableNameWorkflowTemplateMain+" as t2 on t1.id = t2.preset_id").
		Joins("join "+TableNameWorkflowTemplateVersion+" as t3 on t2.version_id = t3.id").
		Select("t1.business_id, t2.*, t3.version_no, t3.name").Where("t2.organization_id = ?", param.OrganizationID)

	if param.Search != "" {
		query = query.Where("t1.business_id like ? or t3.name like ?", "%"+param.Search+"%", "%"+param.Search+"%")
	}
	query = query.Count(&total).Order("t2.updated_at desc")
	if !param.NoPage {
		query = query.Offset((param.Page - 1) * param.PageSize).Limit(param.PageSize)
	}

	err = query.Find(&templates).Error
	if err != nil {
		return nil, 0, err
	}

	return
}

func (c *WorkflowTemplateMainClient) GetByBusinessIDAndOrganizationID(ctx context.Context, businessID, organizationID string) (main WorkflowTemplateMain, err error) {
	return main, c.DB.WithContext(ctx).Table(TableNameWorkflowTemplateMain+" as t1").
		Joins("join "+TableNameWorkflowTemplatePreset+" as t2 on t1.preset_id = t2.id").
		Where("t2.business_id = ? and t1.organization_id = ?", businessID, organizationID).
		Select("t1.*").
		First(&main).Error
}

func (c *WorkflowTemplateMainClient) GetByID(ctx context.Context, id string) (main WorkflowTemplateMain, err error) {
	return main, c.DB.WithContext(ctx).Where("id = ?", id).First(&main).Error
}

func (c *WorkflowTemplateMainClient) GetWorkflowTemplateMainsByPresetID(ctx context.Context, presetID string) (businesses []WorkflowTemplateMain, err error) {
	return businesses, c.DB.WithContext(ctx).Where("preset_id = ?", presetID).Find(&businesses).Error
}

func (c *WorkflowTemplateMainClient) GetOrganizationIDsByPresetID(ctx context.Context, presetID string) (organizationIDs []string, err error) {
	err = c.DB.WithContext(ctx).Table(TableNameWorkflowTemplateMain).Select("organization_id").Where("preset_id = ?", presetID).Find(&organizationIDs).Error
	if err != nil {
		return nil, err
	}

	return
}

func (c *WorkflowTemplateMainClient) BatchCreateWithTx(ctx context.Context, tx *gorm.DB, businesses []WorkflowTemplateMain) error {
	return tx.WithContext(ctx).Create(&businesses).Error
}

// 更新版本号和更新人信息
func (c *WorkflowTemplateMainClient) UpdateVersionWithTx(ctx context.Context, tx *gorm.DB, id, versionID string, updatedBy string) error {
	return tx.WithContext(ctx).Model(&WorkflowTemplateMain{}).Where("id = ?", id).Updates(map[string]interface{}{
		"updated_by": updatedBy,
		"version_id": versionID,
	}).Error
}
func (c *WorkflowTemplateMainClient) UpdateVersion(ctx context.Context, id, versionID string, updatedBy string) error {
	return c.DB.WithContext(ctx).Model(&WorkflowTemplateMain{}).Where("id = ?", id).Updates(map[string]interface{}{
		"updated_by": updatedBy,
		"version_id": versionID,
	}).Error
}

func (c *WorkflowTemplateMainClient) UpdateByID(ctx context.Context, id string, main WorkflowTemplateMain) error {
	return c.DB.WithContext(ctx).Model(&WorkflowTemplateMain{}).Where("id = ?", id).Updates(main).Error
}

type WorkflowTemplateMain struct {
	ID             string    `gorm:"primaryKey;type:varchar(64);not null;comment:主键ID"`
	PresetID       string    `gorm:"type:varchar(64);not null;comment:预设 id"`
	VersionID      string    `gorm:"type:varchar(64);not null;comment:版本 id"`
	OrganizationID string    `gorm:"type:varchar(64);not null;comment:公司或集团id"`
	CreatedAt      time.Time `gorm:"default:CURRENT_TIMESTAMP;comment:创建时间"`
	UpdatedAt      time.Time `gorm:"default:CURRENT_TIMESTAMP;onUpdate:CURRENT_TIMESTAMP;comment:更新时间"`
	CreatedBy      string    `gorm:"type:varchar(64);not null;comment:创建人"`
	UpdatedBy      string    `gorm:"type:varchar(64);comment:更新人"`
}

// TableName 返回模型对应的数据库表名
func (WorkflowTemplateMain) TableName() string {
	return TableNameWorkflowTemplateMain
}
