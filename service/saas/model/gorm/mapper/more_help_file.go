package mapper

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/gorm"
)

const TableNameMoreHelpFile = "more_help_file"

type MoreHelpFileClient struct {
	DB *gorm.DB `name:"saas_db"`
}

func NewMoreHelpFileClient(db *gorm.DB) *MoreHelpFileClient {
	return &MoreHelpFileClient{
		DB: (*gorm.DB)(db),
	}
}

// 新增更多帮助文件列表信息
func (m MoreHelpFileClient) Create(ctx context.Context, help MoreHelpFile) error {
	if err := m.DB.Create(&help).Error; err != nil {
		logc.Error(ctx, "moreHelpFile", help, "error", err)
		return err
	}
	return nil
}

// 根据ID获取文件信息
func (m MoreHelpFileClient) GetById(ctx context.Context, id string, tenantId string) (*MoreHelpFile, error) {
	var file MoreHelpFile
	err := m.DB.Where("id = ? and tenant_id = ?", id, tenantId).First(&file).Error
	if err != nil {
		logc.Error(ctx, "id", id, "error", err)
		return nil, err
	}
	return &file, nil
}

// 查询最大序号
func (m MoreHelpFileClient) GetMoreHelpFileMaxOrder(ctx context.Context, tenantId string) (moreHelpFiles *MoreHelpFile, err error) {
	err = m.DB.Where("tenant_id = ?", tenantId).Order("file_order DESC").First(&moreHelpFiles).Error
	if err != nil {
		logc.Error(ctx, "error", err)
		return nil, err
	}
	return moreHelpFiles, nil
}

// 查询更多帮助列表
func (m MoreHelpFileClient) GetMoreHelpFileList(ctx context.Context, tenantId string) (moreHelpFiles []MoreHelpFile, err error) {
	err = m.DB.Where("tenant_id = ?", tenantId).Order("file_order DESC").Find(&moreHelpFiles).Error
	if err != nil {
		logc.Error(ctx, "error", err)
		return nil, err
	}
	return moreHelpFiles, nil
}

// 根据租户ID和排序范围获取文件列表
func (m MoreHelpFileClient) GetFilesByOrderRange(ctx context.Context, tenantId string, startOrder, endOrder int64) ([]MoreHelpFile, error) {
	var files []MoreHelpFile
	err := m.DB.Where("tenant_id = ? AND file_order >= ? AND file_order <= ?", tenantId, startOrder, endOrder).
		Order("file_order ASC").Find(&files).Error
	if err != nil {
		logc.Error(ctx, "tenantId", tenantId, "startOrder", startOrder, "endOrder", endOrder, "error", err)
		return nil, err
	}
	return files, nil
}

// 批量更新排序
func (m MoreHelpFileClient) BatchUpdateOrder(ctx context.Context, files []MoreHelpFile) error {
	return m.DB.Transaction(func(tx *gorm.DB) error {
		for _, file := range files {
			if err := tx.Model(&MoreHelpFile{}).Where("id = ?", file.ID).Update("file_order", file.FileOrder).Error; err != nil {
				logc.Error(ctx, "id", file.ID, "file_order", file.FileOrder, "error", err)
				return err
			}
		}
		return nil
	})
}

// 更新数据
func (m MoreHelpFileClient) Update(ctx context.Context, help MoreHelpFile) error {
	if err := m.DB.Model(&help).Updates(&help).Error; err != nil {
		logc.Error(ctx, "moreHelpFile", help, "error", err)
		return err
	}
	return nil
}

// 删除数据
func (m MoreHelpFileClient) Delete(ctx context.Context, id string) error {
	if err := m.DB.Where("id = ?", id).Delete(&MoreHelpFile{}).Error; err != nil {
		logc.Error(ctx, "id", id, "error", err)
		return err
	}
	return nil
}

// MoreHelp mapped from table <more_help_file_list>
type MoreHelpFile struct {
	ID        string    `gorm:"column:id;type:varchar(64);primaryKey;comment:主键ID"`
	FileID    string    `gorm:"column:file_id;type:varchar(64);not null;comment:文件ID"`
	FileName  string    `gorm:"column:file_name;type:varchar(255);not null;comment:文件名称"`
	FileType  string    `gorm:"column:file_type;type:varchar(50);not null;comment:文件类型"`
	FileOrder int64     `gorm:"column:file_order;type:int(11);not null;default:0;comment:文件排序"`
	IsOpen    bool      `gorm:"column:is_open;type:tinyint(1);not null;default:1;comment:0:已关闭,1:已启用"`    // 使用 bool 类型映射 tinyint(1)
	IsDeleted bool      `gorm:"column:is_deleted;type:tinyint(1);not null;default:0;comment:0:未删除,1:已删除"` // 使用 bool 类型映射 tinyint(1)
	CreatedAt time.Time `gorm:"column:created_at;type:timestamp;not null;default:CURRENT_TIMESTAMP;comment:创建时间"`
	UpdatedAt time.Time `gorm:"column:updated_at;type:timestamp;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;comment:更新时间"`
	CreatedBy string    `gorm:"column:created_by;type:varchar(64);not null;comment:创建人"`
	UpdatedBy string    `gorm:"column:updated_by;type:varchar(64);not null;comment:更新人"`
	TenantID  string    `gorm:"column:tenant_id;type:varchar(64);not null;comment:租户ID"`
}

// TableName FmsFile's table name
func (MoreHelpFile) TableName() string {
	return TableNameMoreHelpFile
}
