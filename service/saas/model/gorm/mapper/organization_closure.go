package mapper

import (
	"context"
	"errors"

	"gorm.io/gorm"
)

const TableNameOrganizationClosure = "organization_closure"

type OrganizationClosure struct {
	AncestorID         string `gorm:"column:ancestor_id;type:varchar(64);primaryKey;not null;comment:祖先部门ID"`
	DescendantID       string `gorm:"column:descendant_id;type:varchar(64);primaryKey;not null;comment:后代部门ID"`
	Depth              uint8  `gorm:"column:depth;type:tinyint unsigned;comment:层级深度"`
	DescendantNodeType int    `gorm:"column:descendant_node_type;type:tinyint;comment:后代部门类型 1:集团 2:公司 3:部门"`
	AncestorNodeType   int    `gorm:"column:ancestor_node_type;type:tinyint;comment:祖先部门类型 1:集团 2:公司 3:部门"`
}

func (OrganizationClosure) TableName() string {
	return TableNameOrganizationClosure
}

type OrganizationClosureClient struct {
	DB *gorm.DB
}

func NewOrganizationClosureClient(db *gorm.DB) *OrganizationClosureClient {
	return &OrganizationClosureClient{DB: db}
}

// GetDescendantIds 获取指定祖先部门的所有后代部门ID
func (c *OrganizationClosureClient) GetDescendantIds(ctx context.Context, ancestorID string, descendantNodeTypes ...int) ([]string, error) {
	var ids []string
	query := c.DB.WithContext(ctx).Model(&OrganizationClosure{}).
		Where("ancestor_id = ?", ancestorID)

	if len(descendantNodeTypes) > 0 {
		query = query.Where("descendant_node_type in (?)", descendantNodeTypes)
	}

	err := query.Pluck("descendant_id", &ids).Error
	if err != nil {
		return nil, err
	}
	return ids, nil
}

func (c *OrganizationClosureClient) GetDescendants(ctx context.Context, ancestorID string, descendantNodeTypes ...int) ([]OrganizationClosure, error) {
	var orgs []OrganizationClosure
	query := c.DB.WithContext(ctx).Model(&OrganizationClosure{}).
		Where("ancestor_id = ?", ancestorID)

	if len(descendantNodeTypes) > 0 {
		query = query.Where("descendant_node_type in (?)", descendantNodeTypes)
	}

	err := query.Order("depth asc").Find(&orgs).Error
	if err != nil {
		return nil, err
	}
	return orgs, nil
}

// GetAncestors 获取指定后代部门的所有祖先部门
func (c *OrganizationClosureClient) GetAncestors(ctx context.Context, descendantID string) ([]OrganizationClosure, error) {
	var ancestors []OrganizationClosure
	err := c.DB.WithContext(ctx).Model(&OrganizationClosure{}).
		Where("descendant_id = ?", descendantID).
		Order("depth asc").
		Find(&ancestors).Error
	if err != nil {
		return nil, err
	}
	return ancestors, nil
}

// GetAncestorIds 获取指定后代部门的所有祖先部门ID
func (c *OrganizationClosureClient) GetAncestorIds(ctx context.Context, descendantID string, ancestorNodeTypes ...int) ([]string, error) {
	var ids []string
	query := c.DB.WithContext(ctx).Model(&OrganizationClosure{}).
		Where("descendant_id = ?", descendantID)
	if len(ancestorNodeTypes) > 0 {
		query = query.Where("ancestor_node_type in (?)", ancestorNodeTypes)
	}
	err := query.Pluck("ancestor_id", &ids).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return ids, nil
}

// GetAncestorGroupOrCompanyByDescendantId 根据后代id查询所属公司或者集团
func (c *OrganizationClosureClient) GetAncestorGroupOrCompanyByDescendantId(ctx context.Context, descendantID string) (OrganizationClosure, error) {
	var organizationClosure OrganizationClosure
	err := c.DB.
		Where("ancestor_node_type in (0, 1) and descendant_id = ?", descendantID).
		Order("ancestor_node_type desc").
		Limit(1).
		Find(&organizationClosure).
		Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return organizationClosure, err
	}
	return organizationClosure, nil
}
