package mapper

import (
	"context"

	"gorm.io/gorm"
)

const TableNameUserPositionsOrganizations = "user_positions_organizations"

type UserPositionsOrganizationsClient struct {
	DB *gorm.DB `name:"saas_db"`
}

func NewUserPositionsOrganizationsClient(db *gorm.DB) *UserPositionsOrganizationsClient {
	return &UserPositionsOrganizationsClient{
		DB: (*gorm.DB)(db),
	}
}

// DeleteByPositionIds 根据岗位id删除岗位绑定记录
func (c *UserPositionsOrganizationsClient) DeleteByPositionIds(ctx context.Context, positionIds []string) error {
	if err := c.DB.Where("position_id in ?", positionIds).Delete(&UserPositionsOrganizations{}).Error; err != nil {
		return err
	}
	return nil
}

// BatchDeleteByOrgIdAndUserIds 根据组织的用户id批量删除岗位绑定记录
func (c *UserPositionsOrganizationsClient) BatchDeleteByOrgIdAndUserIdsWithTx(ctx context.Context, orgId string, userIds []string, tx *gorm.DB) error {
	if err := c.DB.Where("organization_id = ? and user_id in ?", orgId, userIds).
		Delete(&UserPositionsOrganizations{}).Error; err != nil {
		return err
	}
	return nil
}

// BatchDelete 删除organizationId和userId下的所有记录
func (c *UserPositionsOrganizationsClient) BatchDelete(ctx context.Context, userId, organizationId string, tx *gorm.DB) error {
	if err := tx.Where("user_id = ? AND organization_id = ?", userId, organizationId).
		Delete(&UserPositionsOrganizations{}).Error; err != nil {
		tx.Rollback()
		return err
	}
	return nil
}

// BatchAdd 批量保存
func (c *UserPositionsOrganizationsClient) BatchAdd(ctx context.Context, records []UserPositionsOrganizations, tx *gorm.DB) error {
	if err := tx.Create(&records).Error; err != nil {
		tx.Rollback()
		return err
	}
	return nil
}

// GetOrganizationUserPositionInfo 获取组织用户的岗位信息
func (c *UserPositionsOrganizationsClient) GetOrganizationUserPositionInfo(userIds []string, organizationId string) (organizationUserPosition []OrganizationUserPosition, err error) {
	err = c.DB.Table(TableNameUserPositionsOrganizations+" upo").
		Select("upo.user_id, upo.position_id, sp.name position_name").
		Joins("left join saas_position sp on upo.position_id = sp.id").
		Where("upo.user_id in ? AND upo.organization_id = ?", userIds, organizationId).
		Scan(&organizationUserPosition).Error
	if err != nil {
		return nil, err
	}
	return organizationUserPosition, nil
}

func (c *UserPositionsOrganizationsClient) GetUserIDsByPositionIdsAndOrganizationId(positionIds []string, organizationId string) (userIDs []string, err error) {
	err = c.DB.Table(TableNameUserPositionsOrganizations+" upo").
		Joins("left join saas_position sp on upo.position_id = sp.id").
		Where("upo.position_id in (?) AND upo.organization_id = ?", positionIds, organizationId).
		Pluck("upo.user_id", &userIDs).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return make([]string, 0), nil
		}
		return nil, err
	}
	return userIDs, nil
}

type OrganizationUserPosition struct {
	UserId       string `json:"userId"`
	PositionId   string `json:"positionId"`
	PositionName string `json:"positionName"`
}

// UserPositionsOrganizations 用户岗位组织关联表
type UserPositionsOrganizations struct {
	UserId         string `gorm:"column:user_id;primary_key;NOT NULL"`
	PositionId     string `gorm:"column:position_id;primary_key;NOT NULL"`
	OrganizationId string `gorm:"column:organization_id;primary_key;NOT NULL;comment:'组织id'"`
}

func (*UserPositionsOrganizations) TableName() string {
	return TableNameUserPositionsOrganizations
}
