package mapper

import (
	"context"
	"time"

	"gorm.io/gorm"
)

const (
	LoginLogTableName = "login_logs"
)

type LoginLogClient struct {
	db *gorm.DB
}

func NewLoginLogClient(db *gorm.DB) *LoginLogClient {
	return &LoginLogClient{
		db: db,
	}
}

func (s *LoginLogClient) Create(ctx context.Context, log LoginLogModel) error {
	return s.db.WithContext(ctx).Create(log).Error
}

// 批量插入登录日志
func (s *LoginLogClient) BatchInsertLoginLogs(logs []LoginLogModel) error {
	// 使用GormDB进行事务处理
	tx := s.db.Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 使用GORM自带的CreateInBatches方法进行批量插入
	if err := tx.Create(&logs).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

type LoginLogModel struct {
	UserID        string `gorm:"column:user_id;type:varchar(18);default:null;comment:用户ID"`
	LoginUserName string `gorm:"column:login_user_name;type:varchar(100);default:null;comment:登录用户名"`
	TenantID      string `gorm:"column:tenant_id;type:varchar(18);default:null;comment:租户ID"`
	IP            string `gorm:"column:ip;type:varchar(39);default null;comment:请求的IP地址"`
	// ip归属市
	IPCity string `gorm:"column:ip_city;type:varchar(100);default null;comment:ip归属市"`
	// ip归属区
	IPDistrict string `gorm:"column:ip_district;type:varchar(50);default:null;comment:ip归属市"`
	// ip归属省份
	IPProvince string `gorm:"column:ip_province;type:varchar(100);default:null;comment:ip归属省份"`
	// ip归属省份
	CreatedAt  time.Time `gorm:"column:created_at;type:timestamp;default:CURRENT_TIMESTAMP"`
	DeviceKind string    `gorm:"column:device_kind;type:varchar(10);default:null;comment:设备类型"`
}

func (mo LoginLogModel) TableName() string {
	return LoginLogTableName
}
