package mapper

import (
	"context"
	"errors"

	"gorm.io/gorm"
)

const TableNameUserBelongCompany = "user_belong_company"

type UserBelongCompanyClient struct {
	DB *gorm.DB `name:"saas_db"`
}

func NewUserBelongCompanyClient(db *gorm.DB) *UserBelongCompanyClient {
	return &UserBelongCompanyClient{
		DB: (*gorm.DB)(db),
	}
}

// BatchDeleteByOrgIdAndUserIdsWithTx 删除用户登录组织记录
func (u *UserBelongCompanyClient) BatchDeleteByOrgIdAndUserIdsWithTx(ctx context.Context, userIds []string, organizationId string, tx *gorm.DB) error {

	err := u.DB.Where("user_id in ? and organization_id = ?", userIds, organizationId).Delete(&UserBelongCompany{}).Error

	return err
}

func (u *UserBelongCompanyClient) DeleteByTenantIdAndUserIds(ctx context.Context, tenantId string, userIds []string) error {
	err := u.DB.Where("user_id in ? and tenant_id = ?", userIds, tenantId).Delete(&UserBelongCompany{}).Error

	return err
}

func (u *UserBelongCompanyClient) DeleteByUserIds(ctx context.Context, userIds []string) error {
	err := u.DB.Where("user_id in ?", userIds).Delete(&UserBelongCompany{}).Error
	return err
}

// Save 新增用户所属集团或者公司记录
func (u *UserBelongCompanyClient) Save(ctx context.Context, userOrgLogin []UserBelongCompany) error {
	err := u.DB.WithContext(ctx).Save(&userOrgLogin).Error
	if err != nil {
		return err
	}
	return nil
}

// GetSuperiorOrganizationInfoByUserId 根据用户id查询上级组织id和名称
func (u *UserBelongCompanyClient) GetSuperiorOrganizationInfoByUserId(ctx context.Context, userId, tenantId string) ([]OrganizationInfo, error) {
	var organizationInfo []OrganizationInfo
	err := u.DB.Table(TableNameUserBelongCompany+" uol").
		Select("uol.organization_id, so.name").
		Joins("join saas_organization so on so.id = uol.organization_id").
		Where("user_id = ? and so.tenant_id = ?", userId, tenantId).
		Order("organization_node_type").
		Find(&organizationInfo).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return nil, err
	}
	return organizationInfo, nil
}

type OrganizationInfo struct {
	OrganizationId string `gorm:"column:organization_id"`
	Name           string `gorm:"column:name"`
}

// UserBelongCompany 用户上次登录组织记录表
type UserBelongCompany struct {
	UserId               string `gorm:"column:user_id;primary_key;NOT NULL;comment:'用户id'"`
	OrganizationId       string `gorm:"column:organization_id;NOT NULL;comment:'组织id(集团 or 公司)'"`
	OrganizationNodeType int    `gorm:"column:organization_node_type;NOT NULL;comment:'组织类型（0集团 1公司）'"`
	IsDefaultLogin       bool   `gorm:"column:is_default_login;NOT NULL;comment:'是否登录（0否 1是）'"`
	TenantId             string `gorm:"column:tenant_id;NOT NULL;comment:'租户id'"`
}

// TableName 表名
func (s *UserBelongCompany) TableName() string {
	return TableNameUserBelongCompany
}
