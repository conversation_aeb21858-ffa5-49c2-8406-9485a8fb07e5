package mapper

import (
	"context"
	"time"

	"gorm.io/gorm"
)

const TableNameSaasTenant = "saas_tenant"

type TenantClient struct {
	db *gorm.DB
}

func NewTenantClient(db *gorm.DB) *TenantClient {
	return &TenantClient{
		db: db,
	}
}

// CreateWithTx 创建
func (t TenantClient) CreateWithTx(ctx context.Context, tx *gorm.DB, mod SaasTenant) error {
	return tx.Create(&mod).Error
}

// UpdateWithTx 创建
func (t TenantClient) UpdateWithTx(ctx context.Context, tx *gorm.DB, mod SaasTenant) error {
	return tx.Save(&mod).Error
}

// SaasTenant represents the saas_tenant table.
type SaasTenant struct {
	ID                          string     `gorm:"column:id;type:varchar(255);primaryKey;comment:Snowflake ID | 全局唯一ID" json:"id"`
	CreatedAt                   *time.Time `gorm:"column:created_at;type:timestamp;comment:Created Time | 创建时间" json:"created_at"`
	UpdatedAt                   *time.Time `gorm:"column:updated_at;type:timestamp;comment:Updated Time | 更新时间" json:"updated_at"`
	UUID                        string     `gorm:"column:uuid;type:char(36);not null;comment:UUID" json:"uuid"`
	Key                         string     `gorm:"column:key;type:char(32);not null;comment:key" json:"key"`
	Secret                      string     `gorm:"column:secret;type:varchar(300);default:null;comment:secret | 授权secret" json:"secret"`
	Status                      bool       `gorm:"column:status;type:tinyint(1);default:true;comment:status true normal false ban | 状态  正常/禁用" json:"status"`
	DeletedAt                   *time.Time `gorm:"column:deleted_at;type:timestamp;default:null;comment:Deleted Time | 删除时间（软删除标识）" json:"deleted_at"`
	Name                        string     `gorm:"column:name;type:varchar(500);default:'';comment:Tenant's name | 租户名称" json:"name"`
	IsSuper                     bool       `gorm:"column:is_super;type:tinyint(1);default:false;comment:Is super tenant | 是否是超级租户" json:"is_super"`
	ServiceStartAt              time.Time  `gorm:"column:service_start_at;type:timestamp;default:null;comment:Tenant's service start | 服务开始时间例如2023-01-01 00:00:00" json:"service_start_at"`
	ServiceEndAt                time.Time  `gorm:"column:service_end_at;type:timestamp;default:null;comment:Tenant's service end| 服务到期时间例如2023-10-10 00:00:00" json:"service_end_at"`
	AfterSalesContact           string     `gorm:"column:after_sales_contact;type:varchar(50);default:null;comment:售后联系人" json:"after_sales_contact"`
	LocationID                  string     `gorm:"column:location_id;type:varchar(64);default:null;comment:归属地ID" json:"location_id"`
	LogSaveKeepDays             int64      `gorm:"column:log_save_keep_days;type:int(11);default:null;comment:日志保留天数" json:"log_save_keep_days"`
	MaxAttendanceUserCount      int64      `gorm:"column:max_attendance_user_count;type:int(11);default:null;comment:最大列席用户数" json:"max_attendance_user_count"`
	MaxDeviceCount              int64      `gorm:"column:max_device_count;type:int(11);default:null;comment:最大设备数" json:"max_device_count"`
	MaxUploadFileSize           int64      `gorm:"column:max_upload_file_size;type:int(11);default:null;comment:最大上传文件大小" json:"max_upload_file_size"`
	MaxUserCount                int64      `gorm:"column:max_user_count;type:int(11);default:null;comment:最大用户数" json:"max_user_count"`
	Principal                   string     `gorm:"column:principal;type:varchar(50);default:null;comment:负责人" json:"principal"`
	PrincipalContactInformation string     `gorm:"column:principal_contact_information;type:varchar(50);default:null;comment:负责人联系方式" json:"principal_contact_information"`
	SaleContact                 string     `gorm:"column:sale_contact;type:varchar(50);default:null;comment:销售联系人" json:"sale_contact"`
	AiStatus                    bool       `gorm:"column:ai_status;type:tinyint(1);default:false;comment:AI状态" json:"ai_status"`
	MaxConferenceAgendaTitle    int64      `gorm:"column:max_conference_agenda_title;type:int(11);default:null;comment:最大会议议程标题字数" json:"max_conference_agenda_title"`
}

func (t *SaasTenant) TableName() string {
	return TableNameSaasTenant
}

// BeforeCreate is a hook to set default values before creating a record.
func (t *SaasTenant) BeforeCreate(tx *gorm.DB) (err error) {
	if t.CreatedAt == nil {
		now := time.Now()
		t.CreatedAt = &now
	}
	if t.UpdatedAt == nil {
		now := time.Now()
		t.UpdatedAt = &now
	}
	return
}

// BeforeUpdate is a hook to set default values before updating a record.
func (t *SaasTenant) BeforeUpdate(tx *gorm.DB) (err error) {
	if t.UpdatedAt == nil {
		now := time.Now()
		t.UpdatedAt = &now
	}
	return
}
