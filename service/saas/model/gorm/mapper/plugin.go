package mapper

import (
	"context"
	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/gorm"
	"time"
)

const TableNamePlugin = "plugin"

type PluginClient struct {
	DB *gorm.DB
}

func NewPluginClient(db *gorm.DB) *PluginClient {
	return &PluginClient{
		DB: db,
	}
}

// QueryPluginsByCond 根据条件查询插件
func (s *PluginClient) QueryPluginsByCond(ctx context.Context, cond Plugin, pageNo, pageSize int) (result []Plugin, total int64, err error) {
	if err = s.DB.WithContext(ctx).Table(TableNamePlugin).Where(&cond).Count(&total).Error; err != nil {
		logc.Error(ctx, err)
		return nil, 0, err
	}
	if total == 0 {
		return result, total, err
	}

	err = s.DB.
		Where(&cond).
		Order("created_at desc").
		Offset((pageNo - 1) * pageSize).Limit(pageSize).
		Find(&result).Error
	if err != nil {
		logc.Error(ctx, err)
	}

	return
}

// QueryPluginByID 根据主键查询插件
func (s *PluginClient) QueryPluginByID(ctx context.Context, id string) (result Plugin, err error) {
	if err = s.DB.First(&result, id).Error; err != nil {
		logc.Error(ctx, err)
	}
	return result, err
}

type Plugin struct {
	ID        string    `gorm:"column:id;primaryKey;comment:Snowflake ID | 全局唯一ID" json:"id"`
	CreatedAt time.Time `gorm:"column:created_at;comment:Created Time | 创建时间" json:"created_at"`
	UpdatedAt time.Time `gorm:"column:updated_at;comment:Updated Time | 更新时间" json:"updated_at"`
	Name      string    `gorm:"column:name;type:varchar(64);comment:插件名称" json:"name"`
	Code      string    `gorm:"column:code;type:varchar(256);comment:插件编码" json:"code"`
	Version   int64     `gorm:"column:version;type:integer;comment:插件版本" json:"version"`
	Status    int64     `gorm:"column:status;type:integer;comment:插件状态" json:"status"`
}

func NewPlugin(id, name, code string, version, status int64) Plugin {
	return Plugin{
		ID:      id,
		Name:    name,
		Code:    code,
		Version: version,
		Status:  status,
	}
}

func (*Plugin) TableName() string {
	return TableNamePlugin
}
