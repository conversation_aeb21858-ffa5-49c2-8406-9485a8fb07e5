package mapper

import (
	"time"

	"github.com/zeromicro/go-zero/core/logc"
	"golang.org/x/net/context"
	"gorm.io/gorm"
)

const TableNameDictionary = "dictionary"

type DictionaryClient struct {
	db *gorm.DB `name:"saas_db"`
}

func NewDictionaryClient(db *gorm.DB) *DictionaryClient {
	return &DictionaryClient{
		db: (*gorm.DB)(db),
	}
}

// 根据id更新字典信息
func (c *DictionaryClient) UpdateDictById(ctx context.Context, dict Dictionary) (err error) {
	if err = c.db.Table("dictionary").Where("id = ?", dict.ID).Updates(&dict).Error; err != nil {
		logc.Error(ctx, "dict", dict, "error", err)
		return err
	}
	return
}

// 新增字典列表信息
func (c *DictionaryClient) Create(ctx context.Context, dict Dictionary) (err error) {
	if err = c.db.Table("dictionary").Create(&dict).Error; err != nil {
		logc.Error(ctx, "dict", dict, "error", err)
		return err
	}
	return
}

// 根据id删除字典信息
func (c *DictionaryClient) DeleteDictById(ctx context.Context, id string) (err error) {
	if err = c.db.Table("dictionary").Where("id = ?", id).Delete(&Dictionary{}).Error; err != nil {
		logc.Error(ctx, "id", id, "error", err)
		return err
	}
	return
}

type PageInfo struct {
	Page     uint64
	PageSize uint64
	NoPage   bool
	DictType string
	Search   string
}

// 根据类型查询字典列表
func (c *DictionaryClient) GetDictByDictType(ctx context.Context, pageInfo PageInfo) (dictList []Dictionary, total int64, err error) {
	query := c.db.Table("dictionary").Where("dict_type = ? and is_deleted = ?", pageInfo.DictType, false).Count(&total).Order("created_at DESC")
	if pageInfo.NoPage {
		err = query.Find(&dictList).Error
	} else {
		offset := (pageInfo.Page - 1) * pageInfo.PageSize
		err = query.Offset(int(offset)).Limit(int(pageInfo.PageSize)).Find(&dictList).Error
	}
	if err != nil {
		logc.Error(ctx, "pageInfo", pageInfo, "error", err)
		return nil, 0, err
	}
	return dictList, total, nil
}

func (c *DictionaryClient) Page(ctx context.Context, pageInfo PageInfo) (dictList []Dictionary, total int64, err error) {
	query := c.db.Table("dictionary").Where("is_deleted = ?", false)
	if pageInfo.Search != "" {
		search := "%" + pageInfo.Search + "%"
		query = query.Where("dict_name like ? or dict_code like ? or description like ? or dict_type like ?", search, search, search, search)
	}
	query = query.Count(&total).Order("dict_type asc, dict_order asc, created_at desc")
	if pageInfo.NoPage {
		err = query.Find(&dictList).Error
	} else {
		offset := (pageInfo.Page - 1) * pageInfo.PageSize
		err = query.Offset(int(offset)).Limit(int(pageInfo.PageSize)).Find(&dictList).Error
	}
	if err != nil {
		logc.Error(ctx, "pageInfo", pageInfo, "error", err)
		return nil, 0, err
	}
	return dictList, total, nil
}

type Dictionary struct {
	ID          string    `gorm:"type:varchar(64);primaryKey;column:id;comment:id"`
	DictType    string    `gorm:"type:varchar(50);not null;column:dict_type;comment:类型"`
	DictCode    string    `gorm:"type:varchar(50);not null;column:dict_code;comment:代码"`
	DictOrder   int64     `gorm:"type:tinyint(5);not null;column:dict_order;comment:顺序"`
	DictName    string    `gorm:"type:varchar(100);not null;column:dict_name;comment:名称"`
	Extra       string    `gorm:"type:json;column:extra;comment:扩展字段"`
	Description string    `gorm:"type:text;column:description;comment:描述"`
	IsOpen      bool      `gorm:"type:tinyint(1);not null;column:is_open;comment:0:已关闭,1:已启用"`
	IsDeleted   bool      `gorm:"type:tinyint(1);not null;default:0;column:is_deleted;comment:0:未删除,1:已删除"`
	CreatedAt   time.Time `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP;column:created_at;comment:创建时间"`
	UpdatedAt   time.Time `gorm:"type:timestamp;not null;default:CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;column:updated_at;comment:更新时间"`
	CreatedBy   string    `gorm:"type:varchar(64);column:created_by;comment:创建人"`
	UpdatedBy   string    `gorm:"type:varchar(64);column:updated_by;comment:更新人"`
	TenantID    string    `gorm:"type:varchar(64);column:tenant_id;comment:租户id"`
}

// TableName 设置表名
func (Dictionary) TableName() string {
	return TableNameDictionary
}
