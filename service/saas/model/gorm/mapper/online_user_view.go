package mapper

import (
	"phoenix/service/saas/utils"
	"time"

	"golang.org/x/net/context"
	"gorm.io/gorm"
)

/*
CREATE
OR REPLACE VIEW view_online_user AS
SELECT
	st.id AS token_id,
	st.device_kind,
	st.ip,
	su.mobile,
	su.id AS user_id,
	su.username,
	su.nickname,
	st.created_at AS login_date,
	ste.id AS tenant_id,
	ste.name AS tenant_name
FROM
	saas_token st
	JOIN saas_user su ON st.uid = su.id
	JOIN saas_tenant ste ON st.tenant_id = ste.id
WHERE
	st.expired_at > now()
	AND st.STATUS = 1 AND su.is_superuser = 0
*/

type OnlineUserClient struct {
	db *gorm.DB
}

func NewOnlineUserClient(db *gorm.DB) *OnlineUserClient {
	return &OnlineUserClient{db: db}
}

func (c *OnlineUserClient) GetOnlineUserTokenIDs(ctx context.Context, tokenIds []string, tenantId string) (onlineUserViews []OnlineUserView, err error) {
	return onlineUserViews, c.db.WithContext(ctx).Where("token_id IN ? AND tenant_id = ?", tokenIds, tenantId).Order("login_date DESC").Find(&onlineUserViews).Error
}

type OnlineUserView struct { // Snowflake ID | 全局唯一ID
	DeviceKind string    `gorm:"column:device_kind;not null;comment:Device kind | 设备类型" json:"device_kind"` //   web, app
	Ip         string    `gorm:"column:ip;comment:IP | IP" json:"ip"`                                       // IP | IP
	Mobile     string    `gorm:"column:mobile;comment:Mobile | 手机号" json:"mobile"`                          // 手机号
	TenantName string    `gorm:"column:tenant_name;comment:Tenant Name | 租户名称" json:"tenant_name"`          // 租户名称
	UserId     string    `gorm:"column:user_id;comment:User ID | 用户ID" json:"user_id"`                      // 用户ID
	Username   string    `gorm:"column:username;comment:Username | 用户名" json:"username"`                    // 用户名
	Nickname   string    `gorm:"column:nickname;comment:Nickname | 昵称" json:"nickname"`                     // 昵称
	LoginDate  time.Time `gorm:"column:login_date;comment:Login Date | 登录时间" json:"login_date"`             // 登录时间
	TenantId   string    `gorm:"column:tenant_id;comment:Tenant ID | 租户ID" json:"tenant_id"`                // 租户ID
	TokenId    string    `gorm:"column:token_id;comment:Token ID | 令牌ID" json:"token_id"`                   // 令牌ID
}

func (OnlineUserView) TableName() string {
	return "view_online_user"
}

func (su *OnlineUserView) AfterFind(tx *gorm.DB) (err error) {
	sm4Tool := utils.NewSm4Tool()
	if su.Mobile != "" {
		decryptedMobile, err := sm4Tool.Decrypt(su.Mobile)
		if err != nil {
			return err
		}
		su.Mobile = string(decryptedMobile)
	}
	if su.Username != "" {
		decryptedUsername, err := sm4Tool.Decrypt(su.Username)
		if err != nil {
			return err
		}
		su.Username = string(decryptedUsername)
	}
	return nil
}
