package mapper

import (
	"time"

	"golang.org/x/net/context"
	"gorm.io/gorm"
)

const TableNameWorkflowTemplateDepartment = "workflow_template_department"

type WorkflowTemplateDepartmentClient struct {
	DB *gorm.DB
}

func NewWorkflowTemplateDepartmentClient(db *gorm.DB) *WorkflowTemplateDepartmentClient {
	return &WorkflowTemplateDepartmentClient{DB: db}
}

func (c *WorkflowTemplateDepartmentClient) BatchCreateWithTx(ctx context.Context, tx *gorm.DB, departments []WorkflowTemplateDepartment) error {
	return tx.WithContext(ctx).Create(&departments).Error
}

func (c *WorkflowTemplateDepartmentClient) GetByVersionID(ctx context.Context, versionID string) (department WorkflowTemplateDepartment, err error) {
	return department, c.DB.WithContext(ctx).Where("version_id = ?", versionID).First(&department).Error
}

type WorkflowTemplateDepartment struct {
	ID          string    `gorm:"primaryKey;type:varchar(64);not null;comment:Snowflake ID | 全局唯一ID"`
	VersionID   string    `gorm:"type:varchar(64);not null;comment:模板版本 id"`
	SigningKind string    `gorm:"type:varchar(10);not null;comment:签署类型 或-or,与-and"`
	Level       int       `gorm:"type:int(11);not null;comment:审批层级"`
	CreatedAt   time.Time `gorm:"default:CURRENT_TIMESTAMP;comment:创建时间"`
	CCKind      string    `gorm:"type:varchar(20);not null;comment:抄送者类型 岗位-position,角色-role,指定-designate,自定义-custom,group_designate-集团指定"`
	NodeKind    string    `gorm:"type:varchar(20);not null;comment:环节类型 审批-approval,抄送-approval_cc"`
}

// TableName 返回模型对应的数据库表名
func (WorkflowTemplateDepartment) TableName() string {
	return TableNameWorkflowTemplateDepartment
}
