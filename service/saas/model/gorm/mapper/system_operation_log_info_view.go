package mapper

import (
	"context"
	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/gorm"
	"time"
)

const (
	TableNameSystemOperationLogInfoView = "system_operation_log_info"
)

// SystemOperationLogInfoViewClient 客户端
type SystemOperationLogInfoViewClient struct {
	DB *gorm.DB `name:"saas_db"`
}

func NewSystemOperationLogInfoViewClient(db *gorm.DB) *SystemOperationLogInfoViewClient {
	return &SystemOperationLogInfoViewClient{
		DB: (*gorm.DB)(db),
	}
}

// SystemOperationLogInfoViewQueryPageParam 分页查询 SystemOperationLogInfoViewQueryPageParam
type SystemOperationLogInfoViewQueryPageParam struct {
	UserID    string
	TenantID  string
	APIKind   string
	APIModule string
	Search    string
	IsOK      *bool
	Method    string
	// 分页参数
	Page           uint64
	PageSize       uint64
	BeginTime      *int64
	EndTime        *int64
	NotExistUserId []string
}

// QueryPage 查询分页
func (c *SystemOperationLogInfoViewClient) QueryPage(ctx context.Context, param SystemOperationLogInfoViewQueryPageParam) (list []SystemOperationLogInfoView, total int64, err error) {
	query := c.DB.WithContext(ctx).Table(TableNameSystemOperationLogInfoView)
	if param.UserID != "" {
		query = query.Where("user_id = ?", param.UserID)
	}
	if param.TenantID != "" {
		query = query.Where("tenant_id = ?", param.TenantID)
	}
	if param.APIKind != "" {
		query = query.Where("api_kind = ?", param.APIKind)
	}
	if param.APIModule != "" {
		query = query.Where("api_module = ?", param.APIModule)
	}
	if param.IsOK != nil {
		query = query.Where("is_ok = ?", param.IsOK)
	}
	if param.Method != "" {
		query = query.Where("method = ?", param.Method)
	}
	if param.BeginTime != nil {
		query = query.Where("created_at >= ?", time.UnixMilli(*param.BeginTime))
	}
	if param.EndTime != nil {
		query = query.Where("created_at <= ?", time.UnixMilli(*param.EndTime))
	}
	if len(param.NotExistUserId) > 0 {
		query = query.Where("user_id NOT IN (?)", param.NotExistUserId)
	}
	if param.Search != "" {
		query = query.Where("user_name LIKE ? ", "%"+param.Search+"%")
	}
	if err = query.Count(&total).
		Order("created_at desc").
		Offset(int((param.Page - 1) * param.PageSize)).
		Limit(int(param.PageSize)).
		Find(&list).Error; err != nil {
		logc.Error(ctx, "query page error", err)
		return nil, 0, err
	}
	return
}

// SystemOperationLogInfoView 定义了系统操作日志信息视图模型
type SystemOperationLogInfoView struct {
	UserID         string    `gorm:"column:user_id"`
	TenantID       string    `gorm:"column:tenant_id"`
	CreatedAt      time.Time `gorm:"column:created_at;type:timestamp"`
	Method         string    `gorm:"column:method"`
	IP             string    `gorm:"column:ip"`
	API            string    `gorm:"column:api"`
	RequestBody    string    `gorm:"column:request_body;type:json"`
	ResponseBody   string    `gorm:"column:response_body;type:json"`
	IsOK           bool      `gorm:"column:is_ok;type:tinyint(1)"`
	APIKind        string    `gorm:"column:api_kind"`
	APIModule      string    `gorm:"column:api_module"`
	APIDescription string    `gorm:"column:api_description"`
	UserName       string    `gorm:"column:user_name"`
	TenantName     string    `gorm:"column:tenant_name"`
}

// TableName SystemOperationLogInfoView's table name
func (*SystemOperationLogInfoView) TableName() string {
	return TableNameSystemOperationLogInfoView
}
