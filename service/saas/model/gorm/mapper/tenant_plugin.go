package mapper

import (
	"context"
	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/gorm"
	"time"
)

const TableNameTenantPlugin = "tenant_plugin"

type TenantPluginClient struct {
	DB *gorm.DB
}

func NewTenantPluginClient(db *gorm.DB) *TenantPluginClient {
	return &TenantPluginClient{DB: db}
}

func (s *TenantPluginClient) BatchCreateWithTx(ctx context.Context, tx *gorm.DB, tps []TenantPlugin) error {
	if len(tps) == 0 {
		return nil
	}
	return tx.Save(tps).Error
}
func (s *TenantPluginClient) BatchCreate(ctx context.Context, tps []TenantPlugin) error {
	if len(tps) == 0 {
		return nil
	}
	return s.DB.WithContext(ctx).Save(tps).Error
}

func (s *TenantPluginClient) BatchDelByTenantIDWithTx(ctx context.Context, tx *gorm.DB, tenantID string) error {
	return tx.Model(&TenantPlugin{}).Where("tenant_id = ?", tenantID).Delete(&TenantPlugin{}).Error
}

// QueryTenantPluginByPluginID 查询租户插件根据插件ID
func (s *TenantPluginClient) QueryTenantPluginByPluginID(ctx context.Context, pluginID string) (result []TenantPlugin, err error) {
	err = s.DB.WithContext(ctx).Where("plugin_id = ?", pluginID).Find(&result).Error
	if err != nil {
		logc.Error(ctx, err)
	}
	return
}

func (s *TenantPluginClient) QueryPluginInfosByTenantID(ctx context.Context, tid string) (list []TenantPluginInfo, err error) {
	if err = s.DB.WithContext(ctx).Raw(`SELECT p.CODE AS plugin_code,p.NAME AS plugin_name,p.id plugin_id,tp.tenant_id
	FROM tenant_plugin tp JOIN plugin p ON tp.plugin_id = p.id WHERE tp.tenant_id=? AND p.status=1`, tid).Find(&list).Error; err != nil {
		return nil, err
	}
	return
}

type TenantPlugin struct {
	PluginID  string    `gorm:"column:plugin_id;type:varchar(64);comment:插件ID"`
	TenantID  string    `gorm:"column:tenant_id;type:varchar(64);comment:租户ID"`
	CreatedBy string    `gorm:"column:created_by;type:varchar(64);comment:创建人"`
	CreatedAt time.Time `gorm:"column:created_at;comment:Created Time | 创建时间"`
}

func (s *TenantPlugin) TableName() string {
	return TableNameTenantPlugin
}

type TenantPluginInfo struct {
	PluginCode string `gorm:"column:plugin_code"`
	TenantID   string `gorm:"column:tenant_id;"`
	PluginID   string `gorm:"column:plugin_id"`
	PluginName string `gorm:"column:plugin_name"`
}
