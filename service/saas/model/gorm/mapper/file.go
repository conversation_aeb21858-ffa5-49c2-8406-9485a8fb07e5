package mapper

import (
	"context"
	"errors"
	"github.com/zeromicro/go-zero/core/logc"
	"time"

	"gorm.io/gorm"
)

const TableNameFmsFile = "fms_file"

type FmsFileClient struct {
	DB *gorm.DB `name:"saas_db"`
}

func NewFmsFileClient(db *gorm.DB) *FmsFileClient {
	return &FmsFileClient{
		DB: (*gorm.DB)(db),
	}
}

// 根据id获取文件信息
func (c *FmsFileClient) GetById(ctx context.Context, id string) (f FmsFile, err error) {
	if err := c.DB.Where("id = ?", id).First(&f).Error; err != nil {
		logc.Error(ctx, "id", id, "error", err)
		return f, errors.Join(err, errors.New("FileClient.GetById err"))
	}
	return
}

// 根据ids批量查询
func (c *FmsFileClient) QueryFilesByIDs(ctx context.Context, ids []string) (fms []FmsFile, err error) {
	if err := c.DB.Where("id IN (?)", ids).Find(&fms).Error; err != nil {
		logc.Error(ctx, "ids", ids, "error", err)
		return fms, err
	}
	return
}

// 根据id更新内容
func (c *FmsFileClient) UpdateById(ctx context.Context, id string, fms FmsFile) (err error) {
	if err := c.DB.Model(&fms).Where("id = ?", id).Updates(fms).Error; err != nil {
		logc.Error(ctx, "id", id, "error", err)
		return err
	}
	return
}

// 新增文件信息
func (c *FmsFileClient) Create(ctx context.Context, fms FmsFile) (err error) {
	if err := c.DB.Create(&fms).Error; err != nil {
		logc.Error(ctx, "fms", fms, "error", err)
		return err
	}
	return
}

// UpdateFileByUuid 通过uuid更新数据库文件状态
func (c *FmsFileClient) UpdateFileByUuid(ctx context.Context, uuid string, status bool) (err error) {
	err = c.DB.Table("fms_file").Where("uuid = ?", uuid).Update("status", status).Error
	if err != nil {
		logc.Error(ctx, "uuid", uuid, "error", err)
		return errors.New("文件状态更新失败：" + err.Error())
	}
	return
}

// FmsFile mapped from table <fms_file>
type FmsFile struct {
	ID         string         `gorm:"column:id;primaryKey;comment:Snowflake ID | 全局唯一ID" json:"id"`                                     // Snowflake ID | 全局唯一ID
	CreatedAt  time.Time      `gorm:"column:created_at;comment:Created Time | 创建时间" json:"created_at"`                                  // Created Time | 创建时间
	UpdatedAt  time.Time      `gorm:"column:updated_at;comment:Updated Time | 更新时间" json:"updated_at"`                                  // Updated Time | 更新时间
	Status     bool           `gorm:"column:status;default:1;comment:status true normal false ban | 状态  正常/禁用" json:"status"`           // status true normal false ban | 状态  正常/禁用
	Sort       int32          `gorm:"column:sort;not null;default:1;comment:Sort number | 排序编号" json:"sort"`                            // Sort number | 排序编号
	DeletedAt  gorm.DeletedAt `gorm:"column:deleted_at;comment:Deleted Time | 删除时间（软删除标识）" json:"deleted_at"`                           // Deleted Time | 删除时间（软删除标识）
	UUID       string         `gorm:"column:uuid;not null;comment:File's UUID | 文件的UUID" json:"uuid"`                                   // File's UUID | 文件的UUID
	Name       string         `gorm:"column:name;not null;comment:File's name | 文件展示名称" json:"name"`                                    // File's name | 文件展示名称
	OriginName string         `gorm:"column:origin_name;not null;comment:File's origin name | 文件原始名称" json:"origin_name"`               // File's origin name | 文件原始名称
	FileType   int32          `gorm:"column:file_type;not null;comment:File's type | 文件类型" json:"file_type"`                            // File's type | 文件类型
	Size       int64          `gorm:"column:size;not null;comment:File's size |  文件大小" json:"size"`                                     // File's size |  文件大小
	Path       string         `gorm:"column:path;not null;comment:File's path | 文件相对路径" json:"path"`                                    // File's path | 文件相对路径
	Hash       string         `gorm:"column:hash;not null;comment:The hash of the file | 文件的 hash" json:"hash"`                         // The hash of the file | 文件的 hash
	OpenStatus int32          `gorm:"column:open_status;default:1;comment:status 1 private 2 public | 状态 1 私有 2 公开" json:"open_status"` // status 1 private 2 public | 状态 1 私有 2 公开
	TenantID   string         `gorm:"column:tenant_id;not null;comment:Tenant ID" json:"tenant_id"`                                     // Tenant ID
	UserID     string         `gorm:"column:user_id;comment:User's ID | 用户ID" json:"user_id"`                                           // User's ID | 用户ID
}

// TableName FmsFile's table name
func (*FmsFile) TableName() string {
	return TableNameFmsFile
}
