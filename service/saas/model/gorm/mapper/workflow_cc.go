package mapper

import (
	"time"

	"golang.org/x/net/context"
	"gorm.io/gorm"
)

const TableNameWorkflowCC = "workflow_cc"

type WorkflowCCClient struct {
	DB *gorm.DB
}

func NewWorkflowCCClient(db *gorm.DB) *WorkflowCCClient {
	return &WorkflowCCClient{DB: db}
}

func (c *WorkflowCCClient) BatchCreate(ctx context.Context, ccs []WorkflowCC) error {
	err := c.DB.WithContext(ctx).Create(&ccs).Error
	if err != nil {
		return err
	}
	return nil
}

type WorkflowCCPageReq struct {
	Page     int
	PageSize int
	NoPage   bool
	UserID   string
	TenantID string
}

func (c *WorkflowCCClient) Page(ctx context.Context, req WorkflowCCPageReq) (ccs []WorkflowCC, total int64, err error) {
	db := c.DB.WithContext(ctx).Model(&WorkflowCC{}).Where("user_id = ? AND tenant_id = ?", req.UserID, req.TenantID).Count(&total).Order("updated_at DESC")
	if !req.NoPage {
		db = db.Limit(req.PageSize).Offset((req.Page - 1) * req.PageSize)
	}
	err = db.Find(&ccs).Error
	return ccs, total, err
}

func (c *WorkflowCCClient) UpdateConsultStatus(ctx context.Context, ccIds []string, consultStatus bool) error {
	err := c.DB.WithContext(ctx).Model(&WorkflowCC{}).Where("id in (?)", ccIds).Update("consult_status", consultStatus).Error
	if err != nil {
		return err
	}
	return nil
}

type GetWorkflowCCsReq struct {
	UserID         string
	TenantID       string
	ConsultStatus  *bool
	CreatedAtStart int64
	CreatedAtEnd   int64
	OrganizationID string
	CCBeginTime    int64
	CCEndTime      int64
}

func (c *WorkflowCCClient) GetWorkflowCCs(ctx context.Context, req GetWorkflowCCsReq) (ccs []WorkflowCC, err error) {
	db := c.DB.WithContext(ctx).Model(&WorkflowCC{}).Where("user_id = ? AND tenant_id = ?", req.UserID, req.TenantID)
	if req.ConsultStatus != nil {
		db = db.Where("consult_status = ?", *req.ConsultStatus)
	}
	if req.CreatedAtStart != 0 {
		db = db.Where("created_at >= ?", time.UnixMilli(req.CreatedAtStart))
	}
	if req.CreatedAtEnd != 0 {
		db = db.Where("created_at <= ?", time.UnixMilli(req.CreatedAtEnd))
	}
	if req.OrganizationID != "" {
		db = db.Where("organization_id = ?", req.OrganizationID)
	}
	if req.CCBeginTime != 0 {
		db = db.Where("created_at >= ?", time.UnixMilli(req.CCBeginTime))
	}
	if req.CCEndTime != 0 {
		db = db.Where("created_at <= ?", time.UnixMilli(req.CCEndTime))
	}

	err = db.Find(&ccs).Error
	return ccs, err
}

type WorkflowCC struct {
	ID              string    `gorm:"column:id;type:varchar(64);primaryKey;comment:主键" json:"id"`
	SallyWorkflowID string    `gorm:"column:sally_workflow_id;type:varchar(64);not null;comment:sally 工作流 id" json:"sally_workflow_id"`
	VersionID       string    `gorm:"column:version_id;type:varchar(64);not null;comment:版本 id" json:"version_id"`
	UserID          string    `gorm:"column:user_id;type:varchar(64);not null;comment:抄送人 id" json:"user_id"`
	ConsultStatus   bool      `gorm:"column:consult_status;type:tinyint(1);not null;comment:咨询状态" json:"consult_status"`
	UpdatedBy       string    `gorm:"column:updated_by;type:varchar(64);comment:更新人" json:"updated_by"`
	CreatedBy       string    `gorm:"column:created_by;type:varchar(64);comment:创建人" json:"created_by"`
	UpdatedAt       time.Time `gorm:"column:updated_at;type:timestamp;autoUpdateTime;comment:更新时间" json:"updated_at"`
	CreatedAt       time.Time `gorm:"column:created_at;type:timestamp;autoCreateTime;comment:创建时间" json:"created_at"`
	TenantID        string    `gorm:"column:tenant_id;type:varchar(64);not null;comment:租户 id;index:idx_tenant_org" json:"tenant_id"`
	OrganizationID  string    `gorm:"column:organization_id;type:varchar(64);not null;comment:公司 id;index:idx_tenant_org" json:"organization_id"`
}

// TableName 设置表名
func (WorkflowCC) TableName() string {
	return TableNameWorkflowCC
}
