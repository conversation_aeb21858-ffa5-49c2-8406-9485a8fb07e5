package mapper

import (
	"context"
	"time"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/gorm"
)

const (
	TableNameSystemLoginLogInfoView = "system_login_log_info"
)

// SystemLoginLogInfoViewClient 客户端
type SystemLoginLogInfoViewClient struct {
	DB *gorm.DB `name:"saas_db"`
}

func NewSystemLoginLogInfoViewClient(db *gorm.DB) *SystemLoginLogInfoViewClient {
	return &SystemLoginLogInfoViewClient{
		DB: (*gorm.DB)(db),
	}
}

// SystemLoginLogInfoViewQueryPageParam 分页查询 SystemLoginLogInfoViewQueryPageParam
type SystemLoginLogInfoViewQueryPageParam struct {
	UserID         string
	TenantID       string
	Search         string
	DeviceKind     string
	Page           uint64
	PageSize       uint64
	BeginTime      *int64 `json:"beginTime,optional"`
	EndTime        *int64 `json:"endTime,optional"`
	Location       string `json:"location,optional"`
	NotExistUserId []string
}

// QueryPage 查询分页
func (c *SystemLoginLogInfoViewClient) QueryPage(ctx context.Context, param SystemLoginLogInfoViewQueryPageParam) (list []SystemLoginLogInfoView, total int64, err error) {
	query := c.DB.WithContext(ctx).Table(TableNameSystemLoginLogInfoView)
	if param.UserID != "" {
		query = query.Where("user_id = ?", param.UserID)
	}
	if param.TenantID != "" {
		query = query.Where("tenant_id = ?", param.TenantID)
	}
	if param.DeviceKind != "" {
		query = query.Where("device_kind = ?", param.DeviceKind)
	}

	if param.BeginTime != nil {
		query = query.Where("created_at >= ?", time.UnixMilli(*param.BeginTime))
	}
	if param.EndTime != nil {
		query = query.Where("created_at <= ?", time.UnixMilli(*param.EndTime))
	}
	if len(param.NotExistUserId) > 0 {
		query = query.Where("user_id NOT IN (?)", param.NotExistUserId)
	}
	if param.Location != "" {
		cond := "%" + param.Location + "%"
		query = query.Where("ip_province LIKE ? OR ip_city LIKE ? OR ip_district LIKE ?", cond, cond, cond)
	}
	if param.Search != "" {
		cond := "%" + param.Search + "%"
		query = query.Where(
			"user_name LIKE ? OR login_user_name LIKE ?", cond, cond)
	}
	if err = query.Count(&total).
		Order("created_at desc").
		Offset(int((param.Page - 1) * param.PageSize)).
		Limit(int(param.PageSize)).
		Find(&list).Error; err != nil {
		logc.Error(ctx, "query page error", err)
		return nil, 0, err
	}
	return
}

// SystemLoginLogInfoView 定义了系统操作日志信息视图模型
type SystemLoginLogInfoView struct {
	UserID        string    `gorm:"column:user_id"`
	TenantID      string    `gorm:"column:tenant_id"`
	CreatedAt     time.Time `gorm:"column:created_at"`
	IP            string    `gorm:"column:ip"`
	IPProvince    string    `gorm:"column:ip_province"`
	IPCity        string    `gorm:"column:ip_city"`
	IPDistrict    string    `gorm:"column:ip_district"`
	LoginUserName string    `gorm:"column:login_user_name"`
	UserName      string    `gorm:"column:user_name"`
	TenantName    string    `gorm:"column:tenant_name"`
	DeviceKind    string    `gorm:"column:device_kind"`
}

// TableName SystemLoginLogInfoView's table name
func (*SystemLoginLogInfoView) TableName() string {
	return TableNameSystemLoginLogInfoView
}
