package mapper

import (
	"golang.org/x/net/context"
	"gorm.io/gorm"
)

const TableNameWorkflowTemplateNodeReview = "workflow_template_node_review"

type WorkflowTemplateNodeReviewClient struct {
	DB *gorm.DB
}

func NewWorkflowTemplateNodeReviewClient(db *gorm.DB) *WorkflowTemplateNodeReviewClient {
	return &WorkflowTemplateNodeReviewClient{DB: db}
}

func (c *WorkflowTemplateNodeReviewClient) BatchCreateWithTx(ctx context.Context, tx *gorm.DB, reviews []WorkflowTemplateNodeReview) error {
	return tx.WithContext(ctx).Create(&reviews).Error
}

func (c *WorkflowTemplateNodeReviewClient) GetByNodeID(ctx context.Context, nodeID string) (reviews []WorkflowTemplateNodeReview, err error) {
	return reviews, c.DB.WithContext(ctx).Where("node_id = ?", nodeID).Order("kind asc").Find(&reviews).Error
}

type WorkflowTemplateNodeReview struct {
	NodeID   string `gorm:"type:varchar(64);not null;comment:节点 id"`
	Kind     uint8  `gorm:"type:tinyint(3);not null;comment:类型，1-审批者，2-抄送人"`
	ReviewID string `gorm:"type:varchar(64);not null;comment:审批者 id"`
}

// TableName 返回模型对应的数据库表名
func (WorkflowTemplateNodeReview) TableName() string {
	return TableNameWorkflowTemplateNodeReview
}
