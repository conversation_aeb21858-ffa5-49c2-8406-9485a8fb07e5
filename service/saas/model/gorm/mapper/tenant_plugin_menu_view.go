package mapper

import (
	"context"
	"gorm.io/gorm"
)

const TableNameViewTenantPluginMenu = "view_tenant_plugin_menu"

type ViewTenantPluginMenuClient struct {
	DB *gorm.DB
}

func NewViewTenantPluginMenuClient(db *gorm.DB) *ViewTenantPluginMenuClient {
	return &ViewTenantPluginMenuClient{DB: db}
}

func (s *ViewTenantPluginMenuClient) QueryFirstLevelMenuByTenantID(ctx context.Context, tid string) (menuIDs []string, err error) {
	err = s.DB.WithContext(ctx).
		Table(TableNameViewTenantPluginMenu).
		Select("menu_id").
		Where("tenant_id = ? AND (menu_parent_id IS NULL OR menu_parent_id = '')", tid).
		Find(&menuIDs).Error
	if err != nil {
		return menuIDs, err
	}
	return menuIDs, err
}

type ViewTenantPluginMenu struct {
	MenuID string `gorm:"column:menu_id;type:varchar(64);comment:菜单ID"`
}

func (s *ViewTenantPluginMenu) TableName() string {
	return TableNameViewTenantPluginMenu
}
