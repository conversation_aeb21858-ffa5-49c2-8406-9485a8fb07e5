package mapper

import (
	"golang.org/x/net/context"
	"gorm.io/gorm"
)

const TableNameWorkflowTemplateCCReview = "workflow_template_cc_review"

const (
	WorkflowTemplateCCReviewKindCC       = 2 // 抄送者
	WorkflowTemplateCCReviewKindApprover = 1 // 审批者
)

type WorkflowTemplateCCReviewClient struct {
	DB *gorm.DB
}

func NewWorkflowTemplateCCReviewClient(db *gorm.DB) *WorkflowTemplateCCReviewClient {
	return &WorkflowTemplateCCReviewClient{DB: db}
}

func (c *WorkflowTemplateCCReviewClient) BatchCreateWithTx(ctx context.Context, tx *gorm.DB, ccReviews []WorkflowTemplateCCReview) error {
	return tx.WithContext(ctx).Create(&ccReviews).Error
}

func (c *WorkflowTemplateCCReviewClient) GetByVersionID(ctx context.Context, versionID string) (ccReviews []WorkflowTemplateCCReview, err error) {
	return ccReviews, c.DB.WithContext(ctx).Where("version_id = ?", versionID).Find(&ccReviews).Error
}

func (c *WorkflowTemplateCCReviewClient) GetReviewIdsByVersionID(ctx context.Context, versionID string) (ccIds []string, err error) {
	err = c.DB.WithContext(ctx).Model(&WorkflowTemplateCCReview{}).Where("version_id = ?", versionID).Pluck("review_id", &ccIds).Error
	return
}

type WorkflowTemplateCCReview struct {
	VersionID string `gorm:"type:varchar(64);not null;comment:版本id"`
	ReviewID  string `gorm:"type:varchar(64);not null;comment:审批者id"`
}

// TableName 返回模型对应的数据库表名
func (WorkflowTemplateCCReview) TableName() string {
	return TableNameWorkflowTemplateCCReview
}
