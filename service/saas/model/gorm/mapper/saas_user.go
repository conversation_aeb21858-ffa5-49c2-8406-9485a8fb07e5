package mapper

import (
	"context"
	"fmt"
	"phoenix/service/saas/utils"
	"time"

	"gitlab.zhijiasoft.com/paperless-group/saas-common/utils/encrypt"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/gorm"
)

const TableNameSaasUser = "saas_user"

type UserClient struct {
	DB *gorm.DB `name:"saas_db"`
}

func NewUserClient(db *gorm.DB) *UserClient {
	return &UserClient{
		DB: (*gorm.DB)(db),
	}
}

// QueryUsersByCond 根据条件查询用户
func (c *UserClient) QueryUsersByCond(ctx context.Context, cond UserQueryCond) (users []SaasUser, err error) {
	db := c.DB.WithContext(ctx).Where("deleted_at IS NULL")

	if cond.IDs != nil {
		db = db.Where("id IN (?)", cond.IDs)
	}

	if cond.IgnoreIDs != nil {
		db = db.Where("id NOT IN (?)", cond.IgnoreIDs)
	}

	if cond.Nickname != "" {
		db = db.Where("nickname LIKE ?", fmt.Sprintf("%%%s%%", cond.Nickname))
	}

	// 加密
	if cond.Mobile != "" {
		if cond.Mobile, err = NewCryptoTool().encrypt(cond.Mobile); err != nil {
			return users, err
		}
	}

	if cond.DefaultTenantID != "" {
		db = db.Where("default_tenant_id = ?", cond.DefaultTenantID)
	}

	if err = db.Where(&cond.SaasUser).Find(&users).Error; err != nil {
		logc.Error(ctx, err)
		return nil, err
	}

	return users, err
}

func (c *UserClient) GetUserByMobile(ctx context.Context, mobile string) (user SaasUser, err error) {
	byteMob, err := utils.NewSm4Tool().Encrypt([]byte(mobile))
	if err != nil {
		logc.Error(ctx, err)
		return user, err
	}
	if err = c.DB.Model(&SaasUser{}).Where("mobile = ?", string(byteMob)).Find(&user).Error; err != nil {
		logc.Error(ctx, err)
		return user, err
	}
	return
}

func (c *UserClient) GetUserByMobiles(ctx context.Context, mobiles []string) (users []SaasUser, err error) {
	byteMobiles := make([]string, 0)
	for _, mobile := range mobiles {
		byteMob, err := utils.NewSm4Tool().Encrypt([]byte(mobile))
		if err != nil {
			logc.Error(ctx, err)
			return users, err
		}
		byteMobiles = append(byteMobiles, string(byteMob))
	}
	if err = c.DB.Model(&SaasUser{}).Where("mobile IN (?)", byteMobiles).Find(&users).Error; err != nil {
		logc.Error(ctx, err)
		return users, err
	}
	return
}

/** UpdateUsers 更新用户信息
* 注意，user中的deviceNo是指针类型，所以可以更新为零值
**/

func (c *UserClient) UpdateUsers(ctx context.Context, users ...SaasUser) (err error) {
	tx := c.DB.WithContext(ctx).Begin()

	for _, user := range users {
		user.Password = ""
		if err = tx.Model(&user).Updates(user).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	return tx.Commit().Error
}

func (c *UserClient) UpdatePassword(ctx context.Context, user SaasUser) (err error) {
	user.Password = encrypt.BcryptEncrypt(user.Password)

	if err = c.DB.Table(TableNameSaasUser).
		Where("id = ?", user.ID).
		Update("password", user.Password).Error; err != nil {
		logc.Error(ctx, err)
		return err
	}

	return err
}

// FindById 根据id获取
func (c *UserClient) FindById(ctx context.Context, id string) (user *SaasUser, err error) {
	if err = c.DB.Model(&SaasUser{}).Where("id = ?", id).Find(&user).Error; err != nil {
		logc.Error(ctx, err)
		return nil, err
	}
	return
}

// FindAllByTenantIds 根据租户列表获取所有用户
func (c *UserClient) FindAllByTenantIds(ctx context.Context, tenantIds []string) (users []SaasUser, err error) {
	if len(tenantIds) == 0 {
		return make([]SaasUser, 0), nil
	}
	db := c.DB.WithContext(ctx)
	subQuery := db.Select("user_id").Where("tenant_id IN (?)", tenantIds).Table(TableNameTenantUser)
	if err = db.Model(&SaasUser{}).Where("id IN (?)", subQuery).Find(&users).Error; err != nil {
		logc.Error(ctx, err)
		return nil, err
	}
	return
}

// GetAllAdminUserId 查询所有超管用户id
func (c *UserClient) GetAllAdminUserId(ctx context.Context) (userIds []string, err error) {
	db := c.DB.WithContext(ctx)
	if err = db.Model(&SaasUser{}).Where("is_superuser = ?", true).Select("id").Find(&userIds).Error; err != nil {
		logc.Error(ctx, err)
	}
	return
}

// SaasUser mapped from table <saas_user>
type SaasUser struct {
	ID              string         `gorm:"column:id;primaryKey;comment:Snowflake ID | 全局唯一ID" json:"id"`                                // Snowflake ID | 全局唯一ID
	CreatedAt       time.Time      `gorm:"column:created_at;comment:Created Time | 创建时间" json:"created_at"`                             // Created Time | 创建时间
	UpdatedAt       time.Time      `gorm:"column:updated_at;comment:Updated Time | 更新时间" json:"updated_at"`                             // Updated Time | 更新时间
	Status          *bool          `gorm:"column:status;default:1;comment:status true normal false ban | 状态  正常/禁用" json:"status"`      // status true normal false ban | 状态  正常/禁用
	DeletedAt       gorm.DeletedAt `gorm:"column:deleted_at;comment:Deleted Time | 删除时间（软删除标识）" json:"deleted_at"`                      // Deleted Time | 删除时间（软删除标识）
	Username        string         `gorm:"column:username;not null;comment:User's login name | 登录名" json:"username"`                    // User's login name | 登录名
	Password        string         `gorm:"column:password;not null;comment:Password | 密码Hash" json:"password"`                          // Password | 密码Hash
	Nickname        string         `gorm:"column:nickname;comment:Nickname | 昵称" json:"nickname"`                                       // Nickname | 昵称
	Mobile          string         `gorm:"column:mobile;comment:Mobile number | 手机号" json:"mobile"`                                     // Mobile number | 手机号
	Email           string         `gorm:"column:email;comment:Email | 邮箱号" json:"email"`                                               // Email | 邮箱号
	Gender          string         `gorm:"column:gender;not null;default:未设置;comment:gender | 性别" json:"gender"`                        // gender | 性别
	Post            string         `gorm:"column:post;comment:post | 职务" json:"post"`                                                   // post | 职务
	IsSuperuser     bool           `gorm:"column:is_superuser;not null;comment:Is Superuser | 是否超级管理员" json:"is_superuser"`             // Is Superuser | 是否超级管理员
	DefaultTenantID string         `gorm:"column:default_tenant_id;comment:Default tenant id | 默认租户ID，用于快速登录" json:"default_tenant_id"` // Default tenant id | 默认租户ID，用于快速登录
	AvatarID        string         `gorm:"column:avatar_id;comment:Avatar FIle ID | 头像文件ID" json:"avatar_id"`                           // Avatar FIle ID | 头像文件ID
	Imei            string         `gorm:"column:imei;comment:imei | 设备号" json:"imei"`
	Kind            string         `gorm:"column:kind;comment:kind | 类型" json:"kind"`
	DeviceNo        *string        `gorm:"column:device_no;comment:设备号" json:"device_no"`
}

// TableName SaasUser's table name
func (su *SaasUser) TableName() string {
	return TableNameSaasUser
}

func (su *SaasUser) AfterFind(tx *gorm.DB) (err error) {
	return NewCryptoTool().DecryptUser(su)
}

func (su *SaasUser) BeforeUpdate(tx *gorm.DB) (err error) {
	// 校验deviceNo是否重复
	if su.DeviceNo != nil && *su.DeviceNo != "" {
		var num int64
		err = tx.Table(TableNameSaasUser).
			Where("device_no = ? AND id != ? AND deleted_at IS NULL", *su.DeviceNo, su.ID).
			Count(&num).Error
		if err != nil {
			return err
		}
		if num > 0 {
			return fmt.Errorf("数据已存在: '%s'", *su.DeviceNo)
		}
	}

	// 加密
	if err = NewCryptoTool().EncryptUser(su); err != nil {
		return
	}

	// 修改加密的赋值
	if su.Username != "" {
		tx.Statement.SetColumn("username", su.Username)
	}
	if su.Mobile != "" {
		tx.Statement.SetColumn("mobile", su.Mobile)
	}
	if su.Password != "" {
		tx.Statement.SetColumn("password", su.Password)
	}

	return
}

func (su *SaasUser) BeforeCreate(tx *gorm.DB) (err error) {
	// 校验deviceNo是否重复
	if su.DeviceNo != nil && *su.DeviceNo != "" {
		var num int64
		err = tx.Table(TableNameSaasUser).
			Where("device_no = ? AND deleted_at IS NULL", *su.DeviceNo).
			Count(&num).Error
		if err != nil {
			return err
		}
		if num > 0 {
			return fmt.Errorf("数据已存在: '%s'", *su.DeviceNo)
		}
	}
	return NewCryptoTool().EncryptUser(su)
}

type CryptoTool struct {
	sm4Tool utils.Sm4ToolImpl
}

func NewCryptoTool() *CryptoTool {
	return &CryptoTool{
		sm4Tool: utils.NewSm4Tool(),
	}
}

func (c *CryptoTool) EncryptUser(user *SaasUser) error {
	var err error
	if user.Mobile != "" {
		if user.Mobile, err = c.encrypt(user.Mobile); err != nil {
			return err
		}
	}
	if user.Username != "" {
		if user.Username, err = c.encrypt(user.Username); err != nil {
			return err
		}
	}

	if user.Password != "" {
		user.Password = encrypt.BcryptEncrypt(user.Password)
	}
	return nil
}

func (c *CryptoTool) DecryptUser(user *SaasUser) error {
	mobile, err := c.Decrypt(user.Mobile)
	if err == nil {
		user.Mobile = mobile
	}

	username, err := c.Decrypt(user.Username)
	if err == nil {
		user.Username = username
	}
	return nil
}

func (c *CryptoTool) encrypt(item string) (string, error) {
	result, err := c.sm4Tool.Encrypt([]byte(item))
	if err != nil {
		return "", err
	}
	return string(result), nil
}

func (c *CryptoTool) Decrypt(item string) (string, error) {
	result, err := c.sm4Tool.Decrypt(item)
	if err != nil {
		return "", err
	}
	return string(result), nil
}

type UserQueryCond struct {
	SaasUser
	IDs       []string
	IgnoreIDs []string
}
