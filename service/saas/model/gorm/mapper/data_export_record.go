package mapper

import (
	"context"
	"gorm.io/gorm"
	"time"
)

const TableNameDataExportRecord = "data_export_record"

type DataExportRecordClient struct {
	db *gorm.DB `name:"saas_db"`
}

func NewDataExportRecordClient(db *gorm.DB) *DataExportRecordClient {
	return &DataExportRecordClient{
		db: (*gorm.DB)(db),
	}
}

// GetDataExportRecordList 查询数据导出列表
func (d *DataExportRecordClient) GetDataExportRecordList(ctx context.Context, page, pageSize uint64, noPage bool, dataExportRecord DataExportRecord) (total int64, dataExportRecords []DataExportRecord, err error) {
	query := d.db.WithContext(ctx).Table(TableNameDataExportRecord)
	if dataExportRecord.UserId != "" {
		query = query.Where("user_id = ?", dataExportRecord.UserId)
	}
	if dataExportRecord.FileName != "" {
		cond := "%" + dataExportRecord.FileName + "%"
		query = query.Where("file_name like ?", cond)
	}
	if dataExportRecord.ModuleName != "" {
		cond := "%" + dataExportRecord.ModuleName + "%"
		query = query.Where("module_name like ?", cond)
	}
	if dataExportRecord.Status != 0 {
		query = query.Where("status = ?", dataExportRecord.Status)
	}

	// 查总数
	err = query.Count(&total).Error
	if err != nil {
		return 0, nil, err
	}

	if !noPage {
		// 查询分页数据
		offset := (page - 1) * pageSize
		query = query.Offset(int(offset)).Limit(int(pageSize))
	}

	err = query.Order("created_at desc").Find(&dataExportRecords).Error
	return total, dataExportRecords, err
}

// 通过TaskId查询数据记导出录
func (d *DataExportRecordClient) GetByTaskId(ctx context.Context, taskId string) (dataExportRecord DataExportRecord, err error) {
	err = d.db.Where("task_id = ?", taskId).Find(&dataExportRecord).Error
	return dataExportRecord, err
}

// Create 新建数据导出记录
func (d *DataExportRecordClient) Create(ctx context.Context, dataExportRecord DataExportRecord) error {
	err := d.db.Create(&dataExportRecord).Error
	return err
}

// 通过task_id更改file_id 和 status
func (d *DataExportRecordClient) UpdateFileIdAndStatusByTaskId(ctx context.Context, taskId, fileId, fileName string, status uint) error {
	err := d.db.Table(TableNameDataExportRecord).
		Where("task_id = ?", taskId).
		Update("file_id", fileId).
		Update("status", status).
		Update("file_name = ?", fileName).
		Error
	return err
}

// 通过id修改状态
func (d *DataExportRecordClient) UpdateStatusById(ctx context.Context, id string) error {
	err := d.db.Table(TableNameDataExportRecord).Where("id = ?", id).Update("status", 3).Error
	return err
}

// DataExportRecord 数据导出记录表
type DataExportRecord struct {
	Id         string    `gorm:"column:id;primary_key;NOT NULL;comment:'全局唯一id'"`
	TaskId     string    `gorm:"column:task_id;NOT NULL;comment:'任务id'"`
	FileId     string    `gorm:"column:file_id;NOT NULL;comment:'文件id'"`
	FileName   string    `gorm:"column:file_name;NOT NULL;comment:'文件名称'"`
	ModuleName string    `gorm:"column:module_name;NOT NULL;comment:'模块名称'"`
	Status     uint      `gorm:"column:status;NOT NULL;comment:'状态(1导出中2可下载3已下载4导出失败)'"`
	UserId     string    `gorm:"column:user_id;NOT NULL;comment:'用户id'"`
	CreatedAt  time.Time `gorm:"column:created_at;default:CURRENT_TIMESTAMP;NOT NULL;comment:'发起时间'"`
}

// TableName 表名
func (d *DataExportRecord) TableName() string {
	return TableNameDataExportRecord
}
