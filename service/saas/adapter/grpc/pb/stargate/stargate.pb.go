// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.19.4
// source: service/saas/protos/stargate.proto

package stargate

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 来自 sso.api
type AvatarInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Url           string                 `protobuf:"bytes,2,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AvatarInfo) Reset() {
	*x = AvatarInfo{}
	mi := &file_service_saas_protos_stargate_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AvatarInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AvatarInfo) ProtoMessage() {}

func (x *AvatarInfo) ProtoReflect() protoreflect.Message {
	mi := &file_service_saas_protos_stargate_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AvatarInfo.ProtoReflect.Descriptor instead.
func (*AvatarInfo) Descriptor() ([]byte, []int) {
	return file_service_saas_protos_stargate_proto_rawDescGZIP(), []int{0}
}

func (x *AvatarInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *AvatarInfo) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type SystemPlugin struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Code          string                 `protobuf:"bytes,3,opt,name=code,proto3" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SystemPlugin) Reset() {
	*x = SystemPlugin{}
	mi := &file_service_saas_protos_stargate_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SystemPlugin) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SystemPlugin) ProtoMessage() {}

func (x *SystemPlugin) ProtoReflect() protoreflect.Message {
	mi := &file_service_saas_protos_stargate_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SystemPlugin.ProtoReflect.Descriptor instead.
func (*SystemPlugin) Descriptor() ([]byte, []int) {
	return file_service_saas_protos_stargate_proto_rawDescGZIP(), []int{1}
}

func (x *SystemPlugin) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SystemPlugin) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SystemPlugin) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type PhoenixTenantInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PhoenixTenantInfo) Reset() {
	*x = PhoenixTenantInfo{}
	mi := &file_service_saas_protos_stargate_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PhoenixTenantInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PhoenixTenantInfo) ProtoMessage() {}

func (x *PhoenixTenantInfo) ProtoReflect() protoreflect.Message {
	mi := &file_service_saas_protos_stargate_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PhoenixTenantInfo.ProtoReflect.Descriptor instead.
func (*PhoenixTenantInfo) Descriptor() ([]byte, []int) {
	return file_service_saas_protos_stargate_proto_rawDescGZIP(), []int{2}
}

func (x *PhoenixTenantInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *PhoenixTenantInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type UserInfo struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Status          bool                   `protobuf:"varint,2,opt,name=status,proto3" json:"status,omitempty"`
	DefaultTenantId string                 `protobuf:"bytes,3,opt,name=default_tenant_id,json=defaultTenantId,proto3" json:"default_tenant_id,omitempty"`
	Username        string                 `protobuf:"bytes,4,opt,name=username,proto3" json:"username,omitempty"`
	Nickname        string                 `protobuf:"bytes,5,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Mobile          string                 `protobuf:"bytes,6,opt,name=mobile,proto3" json:"mobile,omitempty"`
	Email           string                 `protobuf:"bytes,7,opt,name=email,proto3" json:"email,omitempty"`
	Gender          string                 `protobuf:"bytes,8,opt,name=gender,proto3" json:"gender,omitempty"`
	Post            string                 `protobuf:"bytes,9,opt,name=post,proto3" json:"post,omitempty"`
	Avatar          *AvatarInfo            `protobuf:"bytes,10,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Kind            string                 `protobuf:"bytes,11,opt,name=kind,proto3" json:"kind,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *UserInfo) Reset() {
	*x = UserInfo{}
	mi := &file_service_saas_protos_stargate_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserInfo) ProtoMessage() {}

func (x *UserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_service_saas_protos_stargate_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserInfo.ProtoReflect.Descriptor instead.
func (*UserInfo) Descriptor() ([]byte, []int) {
	return file_service_saas_protos_stargate_proto_rawDescGZIP(), []int{3}
}

func (x *UserInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UserInfo) GetStatus() bool {
	if x != nil {
		return x.Status
	}
	return false
}

func (x *UserInfo) GetDefaultTenantId() string {
	if x != nil {
		return x.DefaultTenantId
	}
	return ""
}

func (x *UserInfo) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *UserInfo) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *UserInfo) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *UserInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *UserInfo) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *UserInfo) GetPost() string {
	if x != nil {
		return x.Post
	}
	return ""
}

func (x *UserInfo) GetAvatar() *AvatarInfo {
	if x != nil {
		return x.Avatar
	}
	return nil
}

func (x *UserInfo) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

type FreePwdAuthReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Token         string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	ClientId      string                 `protobuf:"bytes,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	UserCode      string                 `protobuf:"bytes,3,opt,name=user_code,json=userCode,proto3" json:"user_code,omitempty"`
	Ts            int64                  `protobuf:"varint,4,opt,name=ts,proto3" json:"ts,omitempty"`
	ReqId         string                 `protobuf:"bytes,5,opt,name=req_id,json=reqId,proto3" json:"req_id,omitempty"`
	Sign          string                 `protobuf:"bytes,6,opt,name=sign,proto3" json:"sign,omitempty"`
	PhoneNumber   string                 `protobuf:"bytes,7,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FreePwdAuthReq) Reset() {
	*x = FreePwdAuthReq{}
	mi := &file_service_saas_protos_stargate_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FreePwdAuthReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FreePwdAuthReq) ProtoMessage() {}

func (x *FreePwdAuthReq) ProtoReflect() protoreflect.Message {
	mi := &file_service_saas_protos_stargate_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FreePwdAuthReq.ProtoReflect.Descriptor instead.
func (*FreePwdAuthReq) Descriptor() ([]byte, []int) {
	return file_service_saas_protos_stargate_proto_rawDescGZIP(), []int{4}
}

func (x *FreePwdAuthReq) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *FreePwdAuthReq) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *FreePwdAuthReq) GetUserCode() string {
	if x != nil {
		return x.UserCode
	}
	return ""
}

func (x *FreePwdAuthReq) GetTs() int64 {
	if x != nil {
		return x.Ts
	}
	return 0
}

func (x *FreePwdAuthReq) GetReqId() string {
	if x != nil {
		return x.ReqId
	}
	return ""
}

func (x *FreePwdAuthReq) GetSign() string {
	if x != nil {
		return x.Sign
	}
	return ""
}

func (x *FreePwdAuthReq) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

type FreePwdAuthResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccessToken   string                 `protobuf:"bytes,1,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	Expire        uint64                 `protobuf:"varint,2,opt,name=expire,proto3" json:"expire,omitempty"`
	User          *UserInfo              `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	SystemPlugins []*SystemPlugin        `protobuf:"bytes,4,rep,name=system_plugins,json=systemPlugins,proto3" json:"system_plugins,omitempty"`
	TenantInfos   []*PhoenixTenantInfo   `protobuf:"bytes,5,rep,name=tenant_infos,json=tenantInfos,proto3" json:"tenant_infos,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FreePwdAuthResp) Reset() {
	*x = FreePwdAuthResp{}
	mi := &file_service_saas_protos_stargate_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FreePwdAuthResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FreePwdAuthResp) ProtoMessage() {}

func (x *FreePwdAuthResp) ProtoReflect() protoreflect.Message {
	mi := &file_service_saas_protos_stargate_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FreePwdAuthResp.ProtoReflect.Descriptor instead.
func (*FreePwdAuthResp) Descriptor() ([]byte, []int) {
	return file_service_saas_protos_stargate_proto_rawDescGZIP(), []int{5}
}

func (x *FreePwdAuthResp) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *FreePwdAuthResp) GetExpire() uint64 {
	if x != nil {
		return x.Expire
	}
	return 0
}

func (x *FreePwdAuthResp) GetUser() *UserInfo {
	if x != nil {
		return x.User
	}
	return nil
}

func (x *FreePwdAuthResp) GetSystemPlugins() []*SystemPlugin {
	if x != nil {
		return x.SystemPlugins
	}
	return nil
}

func (x *FreePwdAuthResp) GetTenantInfos() []*PhoenixTenantInfo {
	if x != nil {
		return x.TenantInfos
	}
	return nil
}

// 来自 auth.api
type SendCaptchaReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ImgCaptchaId  string                 `protobuf:"bytes,1,opt,name=img_captcha_id,json=imgCaptchaId,proto3" json:"img_captcha_id,omitempty"`
	ImgCaptcha    string                 `protobuf:"bytes,2,opt,name=img_captcha,json=imgCaptcha,proto3" json:"img_captcha,omitempty"`
	Account       string                 `protobuf:"bytes,3,opt,name=account,proto3" json:"account,omitempty"`
	Kind          string                 `protobuf:"bytes,4,opt,name=kind,proto3" json:"kind,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendCaptchaReq) Reset() {
	*x = SendCaptchaReq{}
	mi := &file_service_saas_protos_stargate_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendCaptchaReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendCaptchaReq) ProtoMessage() {}

func (x *SendCaptchaReq) ProtoReflect() protoreflect.Message {
	mi := &file_service_saas_protos_stargate_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendCaptchaReq.ProtoReflect.Descriptor instead.
func (*SendCaptchaReq) Descriptor() ([]byte, []int) {
	return file_service_saas_protos_stargate_proto_rawDescGZIP(), []int{6}
}

func (x *SendCaptchaReq) GetImgCaptchaId() string {
	if x != nil {
		return x.ImgCaptchaId
	}
	return ""
}

func (x *SendCaptchaReq) GetImgCaptcha() string {
	if x != nil {
		return x.ImgCaptcha
	}
	return ""
}

func (x *SendCaptchaReq) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *SendCaptchaReq) GetKind() string {
	if x != nil {
		return x.Kind
	}
	return ""
}

type SendCaptchaResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendCaptchaResp) Reset() {
	*x = SendCaptchaResp{}
	mi := &file_service_saas_protos_stargate_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendCaptchaResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendCaptchaResp) ProtoMessage() {}

func (x *SendCaptchaResp) ProtoReflect() protoreflect.Message {
	mi := &file_service_saas_protos_stargate_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendCaptchaResp.ProtoReflect.Descriptor instead.
func (*SendCaptchaResp) Descriptor() ([]byte, []int) {
	return file_service_saas_protos_stargate_proto_rawDescGZIP(), []int{7}
}

type GenerateImgCaptchaReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenerateImgCaptchaReq) Reset() {
	*x = GenerateImgCaptchaReq{}
	mi := &file_service_saas_protos_stargate_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateImgCaptchaReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateImgCaptchaReq) ProtoMessage() {}

func (x *GenerateImgCaptchaReq) ProtoReflect() protoreflect.Message {
	mi := &file_service_saas_protos_stargate_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateImgCaptchaReq.ProtoReflect.Descriptor instead.
func (*GenerateImgCaptchaReq) Descriptor() ([]byte, []int) {
	return file_service_saas_protos_stargate_proto_rawDescGZIP(), []int{8}
}

type GenerateImgCaptchaResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CaptchaId     string                 `protobuf:"bytes,1,opt,name=captcha_id,json=captchaId,proto3" json:"captcha_id,omitempty"`
	CaptchaBase64 string                 `protobuf:"bytes,2,opt,name=captcha_base64,json=captchaBase64,proto3" json:"captcha_base64,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenerateImgCaptchaResp) Reset() {
	*x = GenerateImgCaptchaResp{}
	mi := &file_service_saas_protos_stargate_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenerateImgCaptchaResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenerateImgCaptchaResp) ProtoMessage() {}

func (x *GenerateImgCaptchaResp) ProtoReflect() protoreflect.Message {
	mi := &file_service_saas_protos_stargate_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenerateImgCaptchaResp.ProtoReflect.Descriptor instead.
func (*GenerateImgCaptchaResp) Descriptor() ([]byte, []int) {
	return file_service_saas_protos_stargate_proto_rawDescGZIP(), []int{9}
}

func (x *GenerateImgCaptchaResp) GetCaptchaId() string {
	if x != nil {
		return x.CaptchaId
	}
	return ""
}

func (x *GenerateImgCaptchaResp) GetCaptchaBase64() string {
	if x != nil {
		return x.CaptchaBase64
	}
	return ""
}

type LoginUserInfo struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	DefaultTenantId string                 `protobuf:"bytes,2,opt,name=default_tenant_id,json=defaultTenantId,proto3" json:"default_tenant_id,omitempty"`
	Username        string                 `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Nickname        string                 `protobuf:"bytes,4,opt,name=nickname,proto3" json:"nickname,omitempty"`
	Mobile          string                 `protobuf:"bytes,5,opt,name=mobile,proto3" json:"mobile,omitempty"`
	Email           string                 `protobuf:"bytes,6,opt,name=email,proto3" json:"email,omitempty"`
	Gender          string                 `protobuf:"bytes,7,opt,name=gender,proto3" json:"gender,omitempty"`
	Post            string                 `protobuf:"bytes,8,opt,name=post,proto3" json:"post,omitempty"`
	AvatarUrl       string                 `protobuf:"bytes,9,opt,name=avatar_url,json=avatarUrl,proto3" json:"avatar_url,omitempty"`
	IsAdmin         bool                   `protobuf:"varint,10,opt,name=is_admin,json=isAdmin,proto3" json:"is_admin,omitempty"`
	DeviceKind      string                 `protobuf:"bytes,11,opt,name=device_kind,json=deviceKind,proto3" json:"device_kind,omitempty"`
	IsVirtualUser   bool                   `protobuf:"varint,12,opt,name=is_virtual_user,json=isVirtualUser,proto3" json:"is_virtual_user,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *LoginUserInfo) Reset() {
	*x = LoginUserInfo{}
	mi := &file_service_saas_protos_stargate_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginUserInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginUserInfo) ProtoMessage() {}

func (x *LoginUserInfo) ProtoReflect() protoreflect.Message {
	mi := &file_service_saas_protos_stargate_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginUserInfo.ProtoReflect.Descriptor instead.
func (*LoginUserInfo) Descriptor() ([]byte, []int) {
	return file_service_saas_protos_stargate_proto_rawDescGZIP(), []int{10}
}

func (x *LoginUserInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *LoginUserInfo) GetDefaultTenantId() string {
	if x != nil {
		return x.DefaultTenantId
	}
	return ""
}

func (x *LoginUserInfo) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *LoginUserInfo) GetNickname() string {
	if x != nil {
		return x.Nickname
	}
	return ""
}

func (x *LoginUserInfo) GetMobile() string {
	if x != nil {
		return x.Mobile
	}
	return ""
}

func (x *LoginUserInfo) GetEmail() string {
	if x != nil {
		return x.Email
	}
	return ""
}

func (x *LoginUserInfo) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *LoginUserInfo) GetPost() string {
	if x != nil {
		return x.Post
	}
	return ""
}

func (x *LoginUserInfo) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

func (x *LoginUserInfo) GetIsAdmin() bool {
	if x != nil {
		return x.IsAdmin
	}
	return false
}

func (x *LoginUserInfo) GetDeviceKind() string {
	if x != nil {
		return x.DeviceKind
	}
	return ""
}

func (x *LoginUserInfo) GetIsVirtualUser() bool {
	if x != nil {
		return x.IsVirtualUser
	}
	return false
}

type LoginReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Account       string                 `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	CaptchaId     string                 `protobuf:"bytes,3,opt,name=captcha_id,json=captchaId,proto3" json:"captcha_id,omitempty"`
	Captcha       string                 `protobuf:"bytes,4,opt,name=captcha,proto3" json:"captcha,omitempty"`
	LoginKind     string                 `protobuf:"bytes,5,opt,name=login_kind,json=loginKind,proto3" json:"login_kind,omitempty"`
	DeviceKind    string                 `protobuf:"bytes,6,opt,name=device_kind,json=deviceKind,proto3" json:"device_kind,omitempty"`
	Ip            string                 `protobuf:"bytes,7,opt,name=ip,proto3" json:"ip,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginReq) Reset() {
	*x = LoginReq{}
	mi := &file_service_saas_protos_stargate_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginReq) ProtoMessage() {}

func (x *LoginReq) ProtoReflect() protoreflect.Message {
	mi := &file_service_saas_protos_stargate_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginReq.ProtoReflect.Descriptor instead.
func (*LoginReq) Descriptor() ([]byte, []int) {
	return file_service_saas_protos_stargate_proto_rawDescGZIP(), []int{11}
}

func (x *LoginReq) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *LoginReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *LoginReq) GetCaptchaId() string {
	if x != nil {
		return x.CaptchaId
	}
	return ""
}

func (x *LoginReq) GetCaptcha() string {
	if x != nil {
		return x.Captcha
	}
	return ""
}

func (x *LoginReq) GetLoginKind() string {
	if x != nil {
		return x.LoginKind
	}
	return ""
}

func (x *LoginReq) GetDeviceKind() string {
	if x != nil {
		return x.DeviceKind
	}
	return ""
}

func (x *LoginReq) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

type LoginResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccessToken   string                 `protobuf:"bytes,1,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"`
	Expire        int64                  `protobuf:"varint,2,opt,name=expire,proto3" json:"expire,omitempty"`
	User          *LoginUserInfo         `protobuf:"bytes,3,opt,name=user,proto3" json:"user,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoginResp) Reset() {
	*x = LoginResp{}
	mi := &file_service_saas_protos_stargate_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoginResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoginResp) ProtoMessage() {}

func (x *LoginResp) ProtoReflect() protoreflect.Message {
	mi := &file_service_saas_protos_stargate_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoginResp.ProtoReflect.Descriptor instead.
func (*LoginResp) Descriptor() ([]byte, []int) {
	return file_service_saas_protos_stargate_proto_rawDescGZIP(), []int{12}
}

func (x *LoginResp) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

func (x *LoginResp) GetExpire() int64 {
	if x != nil {
		return x.Expire
	}
	return 0
}

func (x *LoginResp) GetUser() *LoginUserInfo {
	if x != nil {
		return x.User
	}
	return nil
}

type TokenAuthenticationReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccessToken   string                 `protobuf:"bytes,1,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"` // 令牌
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TokenAuthenticationReq) Reset() {
	*x = TokenAuthenticationReq{}
	mi := &file_service_saas_protos_stargate_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TokenAuthenticationReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenAuthenticationReq) ProtoMessage() {}

func (x *TokenAuthenticationReq) ProtoReflect() protoreflect.Message {
	mi := &file_service_saas_protos_stargate_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenAuthenticationReq.ProtoReflect.Descriptor instead.
func (*TokenAuthenticationReq) Descriptor() ([]byte, []int) {
	return file_service_saas_protos_stargate_proto_rawDescGZIP(), []int{13}
}

func (x *TokenAuthenticationReq) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

type TokenAuthenticationResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	User          *LoginUserInfo         `protobuf:"bytes,1,opt,name=user,proto3" json:"user,omitempty"` // 用户信息
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TokenAuthenticationResp) Reset() {
	*x = TokenAuthenticationResp{}
	mi := &file_service_saas_protos_stargate_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TokenAuthenticationResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TokenAuthenticationResp) ProtoMessage() {}

func (x *TokenAuthenticationResp) ProtoReflect() protoreflect.Message {
	mi := &file_service_saas_protos_stargate_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TokenAuthenticationResp.ProtoReflect.Descriptor instead.
func (*TokenAuthenticationResp) Descriptor() ([]byte, []int) {
	return file_service_saas_protos_stargate_proto_rawDescGZIP(), []int{14}
}

func (x *TokenAuthenticationResp) GetUser() *LoginUserInfo {
	if x != nil {
		return x.User
	}
	return nil
}

type ResetPasswordReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Account       string                 `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`   // 账号
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"` // 新密码
	Captcha       string                 `protobuf:"bytes,3,opt,name=captcha,proto3" json:"captcha,omitempty"`   // 验证码
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResetPasswordReq) Reset() {
	*x = ResetPasswordReq{}
	mi := &file_service_saas_protos_stargate_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResetPasswordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetPasswordReq) ProtoMessage() {}

func (x *ResetPasswordReq) ProtoReflect() protoreflect.Message {
	mi := &file_service_saas_protos_stargate_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetPasswordReq.ProtoReflect.Descriptor instead.
func (*ResetPasswordReq) Descriptor() ([]byte, []int) {
	return file_service_saas_protos_stargate_proto_rawDescGZIP(), []int{15}
}

func (x *ResetPasswordReq) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *ResetPasswordReq) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *ResetPasswordReq) GetCaptcha() string {
	if x != nil {
		return x.Captcha
	}
	return ""
}

type ResetPasswordResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResetPasswordResp) Reset() {
	*x = ResetPasswordResp{}
	mi := &file_service_saas_protos_stargate_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResetPasswordResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResetPasswordResp) ProtoMessage() {}

func (x *ResetPasswordResp) ProtoReflect() protoreflect.Message {
	mi := &file_service_saas_protos_stargate_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResetPasswordResp.ProtoReflect.Descriptor instead.
func (*ResetPasswordResp) Descriptor() ([]byte, []int) {
	return file_service_saas_protos_stargate_proto_rawDescGZIP(), []int{16}
}

type ChangePasswordReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Account       string                 `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`                            // 账号
	OldPassword   string                 `protobuf:"bytes,2,opt,name=old_password,json=oldPassword,proto3" json:"old_password,omitempty"` // 旧密码
	NewPassword   string                 `protobuf:"bytes,3,opt,name=new_password,json=newPassword,proto3" json:"new_password,omitempty"` // 新密码
	Captcha       string                 `protobuf:"bytes,4,opt,name=captcha,proto3" json:"captcha,omitempty"`                            // 验证码
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangePasswordReq) Reset() {
	*x = ChangePasswordReq{}
	mi := &file_service_saas_protos_stargate_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangePasswordReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangePasswordReq) ProtoMessage() {}

func (x *ChangePasswordReq) ProtoReflect() protoreflect.Message {
	mi := &file_service_saas_protos_stargate_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangePasswordReq.ProtoReflect.Descriptor instead.
func (*ChangePasswordReq) Descriptor() ([]byte, []int) {
	return file_service_saas_protos_stargate_proto_rawDescGZIP(), []int{17}
}

func (x *ChangePasswordReq) GetAccount() string {
	if x != nil {
		return x.Account
	}
	return ""
}

func (x *ChangePasswordReq) GetOldPassword() string {
	if x != nil {
		return x.OldPassword
	}
	return ""
}

func (x *ChangePasswordReq) GetNewPassword() string {
	if x != nil {
		return x.NewPassword
	}
	return ""
}

func (x *ChangePasswordReq) GetCaptcha() string {
	if x != nil {
		return x.Captcha
	}
	return ""
}

type ChangePasswordResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ChangePasswordResp) Reset() {
	*x = ChangePasswordResp{}
	mi := &file_service_saas_protos_stargate_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ChangePasswordResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ChangePasswordResp) ProtoMessage() {}

func (x *ChangePasswordResp) ProtoReflect() protoreflect.Message {
	mi := &file_service_saas_protos_stargate_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ChangePasswordResp.ProtoReflect.Descriptor instead.
func (*ChangePasswordResp) Descriptor() ([]byte, []int) {
	return file_service_saas_protos_stargate_proto_rawDescGZIP(), []int{18}
}

type LogoutReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	AccessToken   string                 `protobuf:"bytes,1,opt,name=access_token,json=accessToken,proto3" json:"access_token,omitempty"` // 令牌
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LogoutReq) Reset() {
	*x = LogoutReq{}
	mi := &file_service_saas_protos_stargate_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogoutReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogoutReq) ProtoMessage() {}

func (x *LogoutReq) ProtoReflect() protoreflect.Message {
	mi := &file_service_saas_protos_stargate_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogoutReq.ProtoReflect.Descriptor instead.
func (*LogoutReq) Descriptor() ([]byte, []int) {
	return file_service_saas_protos_stargate_proto_rawDescGZIP(), []int{19}
}

func (x *LogoutReq) GetAccessToken() string {
	if x != nil {
		return x.AccessToken
	}
	return ""
}

type LogoutResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LogoutResp) Reset() {
	*x = LogoutResp{}
	mi := &file_service_saas_protos_stargate_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LogoutResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LogoutResp) ProtoMessage() {}

func (x *LogoutResp) ProtoReflect() protoreflect.Message {
	mi := &file_service_saas_protos_stargate_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LogoutResp.ProtoReflect.Descriptor instead.
func (*LogoutResp) Descriptor() ([]byte, []int) {
	return file_service_saas_protos_stargate_proto_rawDescGZIP(), []int{20}
}

type GetOnlineUserTokenIDsReq struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOnlineUserTokenIDsReq) Reset() {
	*x = GetOnlineUserTokenIDsReq{}
	mi := &file_service_saas_protos_stargate_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOnlineUserTokenIDsReq) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnlineUserTokenIDsReq) ProtoMessage() {}

func (x *GetOnlineUserTokenIDsReq) ProtoReflect() protoreflect.Message {
	mi := &file_service_saas_protos_stargate_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnlineUserTokenIDsReq.ProtoReflect.Descriptor instead.
func (*GetOnlineUserTokenIDsReq) Descriptor() ([]byte, []int) {
	return file_service_saas_protos_stargate_proto_rawDescGZIP(), []int{21}
}

type GetOnlineUserTokenIDsResp struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TokenIds      []string               `protobuf:"bytes,1,rep,name=token_ids,json=tokenIds,proto3" json:"token_ids,omitempty"` // 令牌id
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetOnlineUserTokenIDsResp) Reset() {
	*x = GetOnlineUserTokenIDsResp{}
	mi := &file_service_saas_protos_stargate_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetOnlineUserTokenIDsResp) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetOnlineUserTokenIDsResp) ProtoMessage() {}

func (x *GetOnlineUserTokenIDsResp) ProtoReflect() protoreflect.Message {
	mi := &file_service_saas_protos_stargate_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetOnlineUserTokenIDsResp.ProtoReflect.Descriptor instead.
func (*GetOnlineUserTokenIDsResp) Descriptor() ([]byte, []int) {
	return file_service_saas_protos_stargate_proto_rawDescGZIP(), []int{22}
}

func (x *GetOnlineUserTokenIDsResp) GetTokenIds() []string {
	if x != nil {
		return x.TokenIds
	}
	return nil
}

var File_service_saas_protos_stargate_proto protoreflect.FileDescriptor

const file_service_saas_protos_stargate_proto_rawDesc = "" +
	"\n" +
	"\"service/saas/protos/stargate.proto\x12\bstargate\".\n" +
	"\n" +
	"AvatarInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x10\n" +
	"\x03url\x18\x02 \x01(\tR\x03url\"F\n" +
	"\fSystemPlugin\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x12\n" +
	"\x04code\x18\x03 \x01(\tR\x04code\"7\n" +
	"\x11PhoenixTenantInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"\xb2\x02\n" +
	"\bUserInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x16\n" +
	"\x06status\x18\x02 \x01(\bR\x06status\x12*\n" +
	"\x11default_tenant_id\x18\x03 \x01(\tR\x0fdefaultTenantId\x12\x1a\n" +
	"\busername\x18\x04 \x01(\tR\busername\x12\x1a\n" +
	"\bnickname\x18\x05 \x01(\tR\bnickname\x12\x16\n" +
	"\x06mobile\x18\x06 \x01(\tR\x06mobile\x12\x14\n" +
	"\x05email\x18\a \x01(\tR\x05email\x12\x16\n" +
	"\x06gender\x18\b \x01(\tR\x06gender\x12\x12\n" +
	"\x04post\x18\t \x01(\tR\x04post\x12,\n" +
	"\x06avatar\x18\n" +
	" \x01(\v2\x14.stargate.AvatarInfoR\x06avatar\x12\x12\n" +
	"\x04kind\x18\v \x01(\tR\x04kind\"\xbe\x01\n" +
	"\x0eFreePwdAuthReq\x12\x14\n" +
	"\x05token\x18\x01 \x01(\tR\x05token\x12\x1b\n" +
	"\tclient_id\x18\x02 \x01(\tR\bclientId\x12\x1b\n" +
	"\tuser_code\x18\x03 \x01(\tR\buserCode\x12\x0e\n" +
	"\x02ts\x18\x04 \x01(\x03R\x02ts\x12\x15\n" +
	"\x06req_id\x18\x05 \x01(\tR\x05reqId\x12\x12\n" +
	"\x04sign\x18\x06 \x01(\tR\x04sign\x12!\n" +
	"\fphone_number\x18\a \x01(\tR\vphoneNumber\"\xf3\x01\n" +
	"\x0fFreePwdAuthResp\x12!\n" +
	"\faccess_token\x18\x01 \x01(\tR\vaccessToken\x12\x16\n" +
	"\x06expire\x18\x02 \x01(\x04R\x06expire\x12&\n" +
	"\x04user\x18\x03 \x01(\v2\x12.stargate.UserInfoR\x04user\x12=\n" +
	"\x0esystem_plugins\x18\x04 \x03(\v2\x16.stargate.SystemPluginR\rsystemPlugins\x12>\n" +
	"\ftenant_infos\x18\x05 \x03(\v2\x1b.stargate.PhoenixTenantInfoR\vtenantInfos\"\x85\x01\n" +
	"\x0eSendCaptchaReq\x12$\n" +
	"\x0eimg_captcha_id\x18\x01 \x01(\tR\fimgCaptchaId\x12\x1f\n" +
	"\vimg_captcha\x18\x02 \x01(\tR\n" +
	"imgCaptcha\x12\x18\n" +
	"\aaccount\x18\x03 \x01(\tR\aaccount\x12\x12\n" +
	"\x04kind\x18\x04 \x01(\tR\x04kind\"\x11\n" +
	"\x0fSendCaptchaResp\"\x17\n" +
	"\x15GenerateImgCaptchaReq\"^\n" +
	"\x16GenerateImgCaptchaResp\x12\x1d\n" +
	"\n" +
	"captcha_id\x18\x01 \x01(\tR\tcaptchaId\x12%\n" +
	"\x0ecaptcha_base64\x18\x02 \x01(\tR\rcaptchaBase64\"\xe0\x02\n" +
	"\rLoginUserInfo\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12*\n" +
	"\x11default_tenant_id\x18\x02 \x01(\tR\x0fdefaultTenantId\x12\x1a\n" +
	"\busername\x18\x03 \x01(\tR\busername\x12\x1a\n" +
	"\bnickname\x18\x04 \x01(\tR\bnickname\x12\x16\n" +
	"\x06mobile\x18\x05 \x01(\tR\x06mobile\x12\x14\n" +
	"\x05email\x18\x06 \x01(\tR\x05email\x12\x16\n" +
	"\x06gender\x18\a \x01(\tR\x06gender\x12\x12\n" +
	"\x04post\x18\b \x01(\tR\x04post\x12\x1d\n" +
	"\n" +
	"avatar_url\x18\t \x01(\tR\tavatarUrl\x12\x19\n" +
	"\bis_admin\x18\n" +
	" \x01(\bR\aisAdmin\x12\x1f\n" +
	"\vdevice_kind\x18\v \x01(\tR\n" +
	"deviceKind\x12&\n" +
	"\x0fis_virtual_user\x18\f \x01(\bR\risVirtualUser\"\xc9\x01\n" +
	"\bLoginReq\x12\x18\n" +
	"\aaccount\x18\x01 \x01(\tR\aaccount\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\x12\x1d\n" +
	"\n" +
	"captcha_id\x18\x03 \x01(\tR\tcaptchaId\x12\x18\n" +
	"\acaptcha\x18\x04 \x01(\tR\acaptcha\x12\x1d\n" +
	"\n" +
	"login_kind\x18\x05 \x01(\tR\tloginKind\x12\x1f\n" +
	"\vdevice_kind\x18\x06 \x01(\tR\n" +
	"deviceKind\x12\x0e\n" +
	"\x02ip\x18\a \x01(\tR\x02ip\"s\n" +
	"\tLoginResp\x12!\n" +
	"\faccess_token\x18\x01 \x01(\tR\vaccessToken\x12\x16\n" +
	"\x06expire\x18\x02 \x01(\x03R\x06expire\x12+\n" +
	"\x04user\x18\x03 \x01(\v2\x17.stargate.LoginUserInfoR\x04user\";\n" +
	"\x16TokenAuthenticationReq\x12!\n" +
	"\faccess_token\x18\x01 \x01(\tR\vaccessToken\"F\n" +
	"\x17TokenAuthenticationResp\x12+\n" +
	"\x04user\x18\x01 \x01(\v2\x17.stargate.LoginUserInfoR\x04user\"b\n" +
	"\x10ResetPasswordReq\x12\x18\n" +
	"\aaccount\x18\x01 \x01(\tR\aaccount\x12\x1a\n" +
	"\bpassword\x18\x02 \x01(\tR\bpassword\x12\x18\n" +
	"\acaptcha\x18\x03 \x01(\tR\acaptcha\"\x13\n" +
	"\x11ResetPasswordResp\"\x8d\x01\n" +
	"\x11ChangePasswordReq\x12\x18\n" +
	"\aaccount\x18\x01 \x01(\tR\aaccount\x12!\n" +
	"\fold_password\x18\x02 \x01(\tR\voldPassword\x12!\n" +
	"\fnew_password\x18\x03 \x01(\tR\vnewPassword\x12\x18\n" +
	"\acaptcha\x18\x04 \x01(\tR\acaptcha\"\x14\n" +
	"\x12ChangePasswordResp\".\n" +
	"\tLogoutReq\x12!\n" +
	"\faccess_token\x18\x01 \x01(\tR\vaccessToken\"\f\n" +
	"\n" +
	"LogoutResp\"\x1a\n" +
	"\x18GetOnlineUserTokenIDsReq\"8\n" +
	"\x19GetOnlineUserTokenIDsResp\x12\x1b\n" +
	"\ttoken_ids\x18\x01 \x03(\tR\btokenIds2I\n" +
	"\x03Sso\x12B\n" +
	"\vFreePwdAuth\x12\x18.stargate.FreePwdAuthReq\x1a\x19.stargate.FreePwdAuthResp2\xfd\x03\n" +
	"\x04Auth\x12B\n" +
	"\vSendCaptcha\x12\x18.stargate.SendCaptchaReq\x1a\x19.stargate.SendCaptchaResp\x12W\n" +
	"\x12GenerateImgCaptcha\x12\x1f.stargate.GenerateImgCaptchaReq\x1a .stargate.GenerateImgCaptchaResp\x120\n" +
	"\x05Login\x12\x12.stargate.LoginReq\x1a\x13.stargate.LoginResp\x12Z\n" +
	"\x13TokenAuthentication\x12 .stargate.TokenAuthenticationReq\x1a!.stargate.TokenAuthenticationResp\x12H\n" +
	"\rResetPassword\x12\x1a.stargate.ResetPasswordReq\x1a\x1b.stargate.ResetPasswordResp\x12K\n" +
	"\x0eChangePassword\x12\x1b.stargate.ChangePasswordReq\x1a\x1c.stargate.ChangePasswordResp\x123\n" +
	"\x06Logout\x12\x13.stargate.LogoutReq\x1a\x14.stargate.LogoutResp2h\n" +
	"\x04User\x12`\n" +
	"\x15GetOnlineUserTokenIDs\x12\".stargate.GetOnlineUserTokenIDsReq\x1a#.stargate.GetOnlineUserTokenIDsRespB\fZ\n" +
	"./stargateb\x06proto3"

var (
	file_service_saas_protos_stargate_proto_rawDescOnce sync.Once
	file_service_saas_protos_stargate_proto_rawDescData []byte
)

func file_service_saas_protos_stargate_proto_rawDescGZIP() []byte {
	file_service_saas_protos_stargate_proto_rawDescOnce.Do(func() {
		file_service_saas_protos_stargate_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_service_saas_protos_stargate_proto_rawDesc), len(file_service_saas_protos_stargate_proto_rawDesc)))
	})
	return file_service_saas_protos_stargate_proto_rawDescData
}

var file_service_saas_protos_stargate_proto_msgTypes = make([]protoimpl.MessageInfo, 23)
var file_service_saas_protos_stargate_proto_goTypes = []any{
	(*AvatarInfo)(nil),                // 0: stargate.AvatarInfo
	(*SystemPlugin)(nil),              // 1: stargate.SystemPlugin
	(*PhoenixTenantInfo)(nil),         // 2: stargate.PhoenixTenantInfo
	(*UserInfo)(nil),                  // 3: stargate.UserInfo
	(*FreePwdAuthReq)(nil),            // 4: stargate.FreePwdAuthReq
	(*FreePwdAuthResp)(nil),           // 5: stargate.FreePwdAuthResp
	(*SendCaptchaReq)(nil),            // 6: stargate.SendCaptchaReq
	(*SendCaptchaResp)(nil),           // 7: stargate.SendCaptchaResp
	(*GenerateImgCaptchaReq)(nil),     // 8: stargate.GenerateImgCaptchaReq
	(*GenerateImgCaptchaResp)(nil),    // 9: stargate.GenerateImgCaptchaResp
	(*LoginUserInfo)(nil),             // 10: stargate.LoginUserInfo
	(*LoginReq)(nil),                  // 11: stargate.LoginReq
	(*LoginResp)(nil),                 // 12: stargate.LoginResp
	(*TokenAuthenticationReq)(nil),    // 13: stargate.TokenAuthenticationReq
	(*TokenAuthenticationResp)(nil),   // 14: stargate.TokenAuthenticationResp
	(*ResetPasswordReq)(nil),          // 15: stargate.ResetPasswordReq
	(*ResetPasswordResp)(nil),         // 16: stargate.ResetPasswordResp
	(*ChangePasswordReq)(nil),         // 17: stargate.ChangePasswordReq
	(*ChangePasswordResp)(nil),        // 18: stargate.ChangePasswordResp
	(*LogoutReq)(nil),                 // 19: stargate.LogoutReq
	(*LogoutResp)(nil),                // 20: stargate.LogoutResp
	(*GetOnlineUserTokenIDsReq)(nil),  // 21: stargate.GetOnlineUserTokenIDsReq
	(*GetOnlineUserTokenIDsResp)(nil), // 22: stargate.GetOnlineUserTokenIDsResp
}
var file_service_saas_protos_stargate_proto_depIdxs = []int32{
	0,  // 0: stargate.UserInfo.avatar:type_name -> stargate.AvatarInfo
	3,  // 1: stargate.FreePwdAuthResp.user:type_name -> stargate.UserInfo
	1,  // 2: stargate.FreePwdAuthResp.system_plugins:type_name -> stargate.SystemPlugin
	2,  // 3: stargate.FreePwdAuthResp.tenant_infos:type_name -> stargate.PhoenixTenantInfo
	10, // 4: stargate.LoginResp.user:type_name -> stargate.LoginUserInfo
	10, // 5: stargate.TokenAuthenticationResp.user:type_name -> stargate.LoginUserInfo
	4,  // 6: stargate.Sso.FreePwdAuth:input_type -> stargate.FreePwdAuthReq
	6,  // 7: stargate.Auth.SendCaptcha:input_type -> stargate.SendCaptchaReq
	8,  // 8: stargate.Auth.GenerateImgCaptcha:input_type -> stargate.GenerateImgCaptchaReq
	11, // 9: stargate.Auth.Login:input_type -> stargate.LoginReq
	13, // 10: stargate.Auth.TokenAuthentication:input_type -> stargate.TokenAuthenticationReq
	15, // 11: stargate.Auth.ResetPassword:input_type -> stargate.ResetPasswordReq
	17, // 12: stargate.Auth.ChangePassword:input_type -> stargate.ChangePasswordReq
	19, // 13: stargate.Auth.Logout:input_type -> stargate.LogoutReq
	21, // 14: stargate.User.GetOnlineUserTokenIDs:input_type -> stargate.GetOnlineUserTokenIDsReq
	5,  // 15: stargate.Sso.FreePwdAuth:output_type -> stargate.FreePwdAuthResp
	7,  // 16: stargate.Auth.SendCaptcha:output_type -> stargate.SendCaptchaResp
	9,  // 17: stargate.Auth.GenerateImgCaptcha:output_type -> stargate.GenerateImgCaptchaResp
	12, // 18: stargate.Auth.Login:output_type -> stargate.LoginResp
	14, // 19: stargate.Auth.TokenAuthentication:output_type -> stargate.TokenAuthenticationResp
	16, // 20: stargate.Auth.ResetPassword:output_type -> stargate.ResetPasswordResp
	18, // 21: stargate.Auth.ChangePassword:output_type -> stargate.ChangePasswordResp
	20, // 22: stargate.Auth.Logout:output_type -> stargate.LogoutResp
	22, // 23: stargate.User.GetOnlineUserTokenIDs:output_type -> stargate.GetOnlineUserTokenIDsResp
	15, // [15:24] is the sub-list for method output_type
	6,  // [6:15] is the sub-list for method input_type
	6,  // [6:6] is the sub-list for extension type_name
	6,  // [6:6] is the sub-list for extension extendee
	0,  // [0:6] is the sub-list for field type_name
}

func init() { file_service_saas_protos_stargate_proto_init() }
func file_service_saas_protos_stargate_proto_init() {
	if File_service_saas_protos_stargate_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_service_saas_protos_stargate_proto_rawDesc), len(file_service_saas_protos_stargate_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   23,
			NumExtensions: 0,
			NumServices:   3,
		},
		GoTypes:           file_service_saas_protos_stargate_proto_goTypes,
		DependencyIndexes: file_service_saas_protos_stargate_proto_depIdxs,
		MessageInfos:      file_service_saas_protos_stargate_proto_msgTypes,
	}.Build()
	File_service_saas_protos_stargate_proto = out.File
	file_service_saas_protos_stargate_proto_goTypes = nil
	file_service_saas_protos_stargate_proto_depIdxs = nil
}
