// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v3.19.4
// source: service/saas/protos/stargate.proto

package stargate

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Sso_FreePwdAuth_FullMethodName = "/stargate.Sso/FreePwdAuth"
)

// SsoClient is the client API for Sso service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// sso分组
type SsoClient interface {
	// 用户免密认证
	FreePwdAuth(ctx context.Context, in *FreePwdAuthReq, opts ...grpc.CallOption) (*FreePwdAuthResp, error)
}

type ssoClient struct {
	cc grpc.ClientConnInterface
}

func NewSsoClient(cc grpc.ClientConnInterface) SsoClient {
	return &ssoClient{cc}
}

func (c *ssoClient) FreePwdAuth(ctx context.Context, in *FreePwdAuthReq, opts ...grpc.CallOption) (*FreePwdAuthResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FreePwdAuthResp)
	err := c.cc.Invoke(ctx, Sso_FreePwdAuth_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SsoServer is the server API for Sso service.
// All implementations must embed UnimplementedSsoServer
// for forward compatibility.
//
// sso分组
type SsoServer interface {
	// 用户免密认证
	FreePwdAuth(context.Context, *FreePwdAuthReq) (*FreePwdAuthResp, error)
	mustEmbedUnimplementedSsoServer()
}

// UnimplementedSsoServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedSsoServer struct{}

func (UnimplementedSsoServer) FreePwdAuth(context.Context, *FreePwdAuthReq) (*FreePwdAuthResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FreePwdAuth not implemented")
}
func (UnimplementedSsoServer) mustEmbedUnimplementedSsoServer() {}
func (UnimplementedSsoServer) testEmbeddedByValue()             {}

// UnsafeSsoServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SsoServer will
// result in compilation errors.
type UnsafeSsoServer interface {
	mustEmbedUnimplementedSsoServer()
}

func RegisterSsoServer(s grpc.ServiceRegistrar, srv SsoServer) {
	// If the following call pancis, it indicates UnimplementedSsoServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Sso_ServiceDesc, srv)
}

func _Sso_FreePwdAuth_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FreePwdAuthReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SsoServer).FreePwdAuth(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Sso_FreePwdAuth_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SsoServer).FreePwdAuth(ctx, req.(*FreePwdAuthReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Sso_ServiceDesc is the grpc.ServiceDesc for Sso service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Sso_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "stargate.Sso",
	HandlerType: (*SsoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "FreePwdAuth",
			Handler:    _Sso_FreePwdAuth_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service/saas/protos/stargate.proto",
}

const (
	Auth_SendCaptcha_FullMethodName         = "/stargate.Auth/SendCaptcha"
	Auth_GenerateImgCaptcha_FullMethodName  = "/stargate.Auth/GenerateImgCaptcha"
	Auth_Login_FullMethodName               = "/stargate.Auth/Login"
	Auth_TokenAuthentication_FullMethodName = "/stargate.Auth/TokenAuthentication"
	Auth_ResetPassword_FullMethodName       = "/stargate.Auth/ResetPassword"
	Auth_ChangePassword_FullMethodName      = "/stargate.Auth/ChangePassword"
	Auth_Logout_FullMethodName              = "/stargate.Auth/Logout"
)

// AuthClient is the client API for Auth service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// auth分组
type AuthClient interface {
	// 发送验证码
	SendCaptcha(ctx context.Context, in *SendCaptchaReq, opts ...grpc.CallOption) (*SendCaptchaResp, error)
	// 生成图片验证码
	GenerateImgCaptcha(ctx context.Context, in *GenerateImgCaptchaReq, opts ...grpc.CallOption) (*GenerateImgCaptchaResp, error)
	// 用户登录
	Login(ctx context.Context, in *LoginReq, opts ...grpc.CallOption) (*LoginResp, error)
	// Token认证
	TokenAuthentication(ctx context.Context, in *TokenAuthenticationReq, opts ...grpc.CallOption) (*TokenAuthenticationResp, error)
	// 重置密码
	ResetPassword(ctx context.Context, in *ResetPasswordReq, opts ...grpc.CallOption) (*ResetPasswordResp, error)
	// 修改密码
	ChangePassword(ctx context.Context, in *ChangePasswordReq, opts ...grpc.CallOption) (*ChangePasswordResp, error)
	// 登出
	Logout(ctx context.Context, in *LogoutReq, opts ...grpc.CallOption) (*LogoutResp, error)
}

type authClient struct {
	cc grpc.ClientConnInterface
}

func NewAuthClient(cc grpc.ClientConnInterface) AuthClient {
	return &authClient{cc}
}

func (c *authClient) SendCaptcha(ctx context.Context, in *SendCaptchaReq, opts ...grpc.CallOption) (*SendCaptchaResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SendCaptchaResp)
	err := c.cc.Invoke(ctx, Auth_SendCaptcha_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) GenerateImgCaptcha(ctx context.Context, in *GenerateImgCaptchaReq, opts ...grpc.CallOption) (*GenerateImgCaptchaResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GenerateImgCaptchaResp)
	err := c.cc.Invoke(ctx, Auth_GenerateImgCaptcha_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) Login(ctx context.Context, in *LoginReq, opts ...grpc.CallOption) (*LoginResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LoginResp)
	err := c.cc.Invoke(ctx, Auth_Login_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) TokenAuthentication(ctx context.Context, in *TokenAuthenticationReq, opts ...grpc.CallOption) (*TokenAuthenticationResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TokenAuthenticationResp)
	err := c.cc.Invoke(ctx, Auth_TokenAuthentication_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) ResetPassword(ctx context.Context, in *ResetPasswordReq, opts ...grpc.CallOption) (*ResetPasswordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ResetPasswordResp)
	err := c.cc.Invoke(ctx, Auth_ResetPassword_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) ChangePassword(ctx context.Context, in *ChangePasswordReq, opts ...grpc.CallOption) (*ChangePasswordResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ChangePasswordResp)
	err := c.cc.Invoke(ctx, Auth_ChangePassword_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *authClient) Logout(ctx context.Context, in *LogoutReq, opts ...grpc.CallOption) (*LogoutResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LogoutResp)
	err := c.cc.Invoke(ctx, Auth_Logout_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AuthServer is the server API for Auth service.
// All implementations must embed UnimplementedAuthServer
// for forward compatibility.
//
// auth分组
type AuthServer interface {
	// 发送验证码
	SendCaptcha(context.Context, *SendCaptchaReq) (*SendCaptchaResp, error)
	// 生成图片验证码
	GenerateImgCaptcha(context.Context, *GenerateImgCaptchaReq) (*GenerateImgCaptchaResp, error)
	// 用户登录
	Login(context.Context, *LoginReq) (*LoginResp, error)
	// Token认证
	TokenAuthentication(context.Context, *TokenAuthenticationReq) (*TokenAuthenticationResp, error)
	// 重置密码
	ResetPassword(context.Context, *ResetPasswordReq) (*ResetPasswordResp, error)
	// 修改密码
	ChangePassword(context.Context, *ChangePasswordReq) (*ChangePasswordResp, error)
	// 登出
	Logout(context.Context, *LogoutReq) (*LogoutResp, error)
	mustEmbedUnimplementedAuthServer()
}

// UnimplementedAuthServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAuthServer struct{}

func (UnimplementedAuthServer) SendCaptcha(context.Context, *SendCaptchaReq) (*SendCaptchaResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SendCaptcha not implemented")
}
func (UnimplementedAuthServer) GenerateImgCaptcha(context.Context, *GenerateImgCaptchaReq) (*GenerateImgCaptchaResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenerateImgCaptcha not implemented")
}
func (UnimplementedAuthServer) Login(context.Context, *LoginReq) (*LoginResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Login not implemented")
}
func (UnimplementedAuthServer) TokenAuthentication(context.Context, *TokenAuthenticationReq) (*TokenAuthenticationResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TokenAuthentication not implemented")
}
func (UnimplementedAuthServer) ResetPassword(context.Context, *ResetPasswordReq) (*ResetPasswordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResetPassword not implemented")
}
func (UnimplementedAuthServer) ChangePassword(context.Context, *ChangePasswordReq) (*ChangePasswordResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ChangePassword not implemented")
}
func (UnimplementedAuthServer) Logout(context.Context, *LogoutReq) (*LogoutResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Logout not implemented")
}
func (UnimplementedAuthServer) mustEmbedUnimplementedAuthServer() {}
func (UnimplementedAuthServer) testEmbeddedByValue()              {}

// UnsafeAuthServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AuthServer will
// result in compilation errors.
type UnsafeAuthServer interface {
	mustEmbedUnimplementedAuthServer()
}

func RegisterAuthServer(s grpc.ServiceRegistrar, srv AuthServer) {
	// If the following call pancis, it indicates UnimplementedAuthServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Auth_ServiceDesc, srv)
}

func _Auth_SendCaptcha_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SendCaptchaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).SendCaptcha(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_SendCaptcha_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).SendCaptcha(ctx, req.(*SendCaptchaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_GenerateImgCaptcha_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenerateImgCaptchaReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).GenerateImgCaptcha(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_GenerateImgCaptcha_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).GenerateImgCaptcha(ctx, req.(*GenerateImgCaptchaReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_Login_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LoginReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).Login(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_Login_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).Login(ctx, req.(*LoginReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_TokenAuthentication_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TokenAuthenticationReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).TokenAuthentication(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_TokenAuthentication_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).TokenAuthentication(ctx, req.(*TokenAuthenticationReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_ResetPassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResetPasswordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).ResetPassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_ResetPassword_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).ResetPassword(ctx, req.(*ResetPasswordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_ChangePassword_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ChangePasswordReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).ChangePassword(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_ChangePassword_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).ChangePassword(ctx, req.(*ChangePasswordReq))
	}
	return interceptor(ctx, in, info, handler)
}

func _Auth_Logout_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LogoutReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AuthServer).Logout(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Auth_Logout_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AuthServer).Logout(ctx, req.(*LogoutReq))
	}
	return interceptor(ctx, in, info, handler)
}

// Auth_ServiceDesc is the grpc.ServiceDesc for Auth service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Auth_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "stargate.Auth",
	HandlerType: (*AuthServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SendCaptcha",
			Handler:    _Auth_SendCaptcha_Handler,
		},
		{
			MethodName: "GenerateImgCaptcha",
			Handler:    _Auth_GenerateImgCaptcha_Handler,
		},
		{
			MethodName: "Login",
			Handler:    _Auth_Login_Handler,
		},
		{
			MethodName: "TokenAuthentication",
			Handler:    _Auth_TokenAuthentication_Handler,
		},
		{
			MethodName: "ResetPassword",
			Handler:    _Auth_ResetPassword_Handler,
		},
		{
			MethodName: "ChangePassword",
			Handler:    _Auth_ChangePassword_Handler,
		},
		{
			MethodName: "Logout",
			Handler:    _Auth_Logout_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service/saas/protos/stargate.proto",
}

const (
	User_GetOnlineUserTokenIDs_FullMethodName = "/stargate.User/GetOnlineUserTokenIDs"
)

// UserClient is the client API for User service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// 用户分组
type UserClient interface {
	// 获取在线令牌id
	GetOnlineUserTokenIDs(ctx context.Context, in *GetOnlineUserTokenIDsReq, opts ...grpc.CallOption) (*GetOnlineUserTokenIDsResp, error)
}

type userClient struct {
	cc grpc.ClientConnInterface
}

func NewUserClient(cc grpc.ClientConnInterface) UserClient {
	return &userClient{cc}
}

func (c *userClient) GetOnlineUserTokenIDs(ctx context.Context, in *GetOnlineUserTokenIDsReq, opts ...grpc.CallOption) (*GetOnlineUserTokenIDsResp, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetOnlineUserTokenIDsResp)
	err := c.cc.Invoke(ctx, User_GetOnlineUserTokenIDs_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// UserServer is the server API for User service.
// All implementations must embed UnimplementedUserServer
// for forward compatibility.
//
// 用户分组
type UserServer interface {
	// 获取在线令牌id
	GetOnlineUserTokenIDs(context.Context, *GetOnlineUserTokenIDsReq) (*GetOnlineUserTokenIDsResp, error)
	mustEmbedUnimplementedUserServer()
}

// UnimplementedUserServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedUserServer struct{}

func (UnimplementedUserServer) GetOnlineUserTokenIDs(context.Context, *GetOnlineUserTokenIDsReq) (*GetOnlineUserTokenIDsResp, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetOnlineUserTokenIDs not implemented")
}
func (UnimplementedUserServer) mustEmbedUnimplementedUserServer() {}
func (UnimplementedUserServer) testEmbeddedByValue()              {}

// UnsafeUserServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to UserServer will
// result in compilation errors.
type UnsafeUserServer interface {
	mustEmbedUnimplementedUserServer()
}

func RegisterUserServer(s grpc.ServiceRegistrar, srv UserServer) {
	// If the following call pancis, it indicates UnimplementedUserServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&User_ServiceDesc, srv)
}

func _User_GetOnlineUserTokenIDs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetOnlineUserTokenIDsReq)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(UserServer).GetOnlineUserTokenIDs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: User_GetOnlineUserTokenIDs_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(UserServer).GetOnlineUserTokenIDs(ctx, req.(*GetOnlineUserTokenIDsReq))
	}
	return interceptor(ctx, in, info, handler)
}

// User_ServiceDesc is the grpc.ServiceDesc for User service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var User_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "stargate.User",
	HandlerType: (*UserServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetOnlineUserTokenIDs",
			Handler:    _User_GetOnlineUserTokenIDs_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "service/saas/protos/stargate.proto",
}
