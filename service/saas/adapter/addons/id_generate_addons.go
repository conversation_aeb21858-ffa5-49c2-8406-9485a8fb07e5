package addons

import (
	"strconv"

	"github.com/bwmarrin/snowflake"
)

type IDGeneratorAddons interface {
	GenerateID() int64
	GenerateIDString() string
}

type IDGeneratorConf struct {
	Epoch    int64
	NodeBits uint8
	StepBits uint8
	Node     int64
}

type IDGeneratorImpl struct {
	node *snowflake.Node
}

func (i *IDGeneratorImpl) GenerateID() int64 {
	return int64(i.node.Generate())
}

func (i *IDGeneratorImpl) GenerateIDString() string {
	return strconv.FormatInt(int64(i.node.Generate()), 10)
}

func NewIdGeneratorAddonsImpl(s IDGeneratorConf) IDGeneratorAddons {
	generator := newSnowflakeGenerator(s)
	return &IDGeneratorImpl{
		node: generator,
	}
}

// NewSnowflakeGenerator 初始化雪花算法生成器
func newSnowflakeGenerator(s IDGeneratorConf) *snowflake.Node {
	// 元年
	snowflake.Epoch = s.Epoch
	// 节点bit位
	snowflake.NodeBits = s.NodeBits
	// 步骤bit位
	snowflake.StepBits = s.StepBits

	node, err := snowflake.NewNode(s.Node)
	if err != nil {
		panic(err)
	}
	return node
}
