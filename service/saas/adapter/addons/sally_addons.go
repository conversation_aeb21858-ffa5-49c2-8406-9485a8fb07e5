package addons

import (
	"context"
	"time"

	"gitee.com/leonscript/sally/v2"
	"gitee.com/leonscript/sally/v2/domain/aggregate"
	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/gorm"
)

const (
	// 审批状态
	WorkflowStatusPassed      = "passed"      // 审批通过
	WorkflowStatusRejected    = "rejected"    // 审批拒绝
	WorkflowStatusUnderReview = "underReview" // 审批中
	WorkflowStatusNotStarted  = "notStarted"  // 未开始
	WorkflowStatusCancelled   = "cancelled"   // 已撤销
)

// 审批策略枚举
const (
	WorkflowTemplateApprovalStrategyOr  = "or"  // 或批策略：任意一人通过即可通过，任意一人拒绝即拒绝
	WorkflowTemplateApprovalStrategyAnd = "and" // 与批策略：所有人通过才能通过，任意一人拒绝即拒绝
)

// 自动审批策略枚举

const (
	WorkflowTemplateAutoApprovalStrategyAdjacent = "adjacent"
	WorkflowTemplateAutoApprovalStrategyAny      = "any"
	WorkflowTemplateAutoApprovalStrategyClose    = "close"
)

// WorkflowEventHandler 工作流事件处理器
type WorkflowEventHandler interface {
	// 工作流通过事件
	HandleWorkflowPassed(ctx context.Context, querier WorkflowQuerier, event WorkflowEvent) error
	// 工作流拒绝事件
	HandleWorkflowRejected(ctx context.Context, querier WorkflowQuerier, event WorkflowEvent) error
	// 工作流节点通过事件
	HandleWorkflowNodePassed(ctx context.Context, querier WorkflowQuerier, event WorkflowEvent) error
	// 工作流撤销事件
	HandleWorkflowCanceled(ctx context.Context, querier WorkflowQuerier, event WorkflowEvent) error
	// 审批流程启动成功
	HandleWorkflowStartSuccess(ctx context.Context, querier WorkflowQuerier, event WorkflowEvent) error
	// 审批流程启动失败
	HandleWorkflowStartFailed(ctx context.Context, querier WorkflowQuerier, event WorkflowEvent) error
}

// WorkflowQuerier 工作流查询器
type WorkflowQuerier interface {
	GetWorkflow(ctx context.Context, workflowID string) (workflow SallyGetWorkflowsResult, err error)
	GetWorkflows(ctx context.Context, req SallyGetWorkflowsParams) (workflows []SallyGetWorkflowsResult, total int64, err error)
	GetNode(ctx context.Context, nodeID string) (node SallyActivatedNode, err error)
	GetNodeReviewTasks(ctx context.Context, req SallyGetNodeReviewTasksRequest) (tasks []SallyNodeReviewTask, total int64, err error)
	GetWorkflowProgress(ctx context.Context, workflowID string) (progress SallyWorkflowProgress, err error)
}

// WorkflowReviewerQuerier 工作流审批人查询器
type WorkflowReviewerQuerier interface {
	GetReviewerIDs(ctx context.Context, roleKind int, reviewerNos []string, params map[string]any) (userIDs []string, err error)
}

// WorkflowManager 工作流管理器
type WorkflowManager interface {
	// 新增工作流模板
	AddWorkflowTemplate(ctx context.Context, config WorkflowTemplateConfig) (templateID string, nodeTemplateIDs []string, err error)
	RegisterWorkflowEventProcessor(ctx context.Context, handler WorkflowEventHandler)
	RegisterWorkflowReviewerQuerier(ctx context.Context, querier WorkflowReviewerQuerier)
	// 启动工作流
	StartWorkflow(ctx context.Context, req SallyStartWorkflowRequest) (err error)
	// 暂存工作流草稿
	SaveWorkflowDraft(ctx context.Context, req SallyStartWorkflowRequest) (err error)
	// 重启工作流
	RestartWorkflow(ctx context.Context, req SallyRestartWorkflowRequest) (err error)
	// 更新工作流内容
	UpdateWorkflowContent(ctx context.Context, workflowID, formContent string, businessParams map[string]any) (err error)
	// 移除工作流
	RemoveWorkflow(ctx context.Context, workflowID string) (err error)
	// 撤销工作流
	CancelWorkflow(ctx context.Context, workflowID string) (err error)
	// 更改节点办理人员
	ChangeNodeReviewer(ctx context.Context, req SallyChangeNodeReviewerRequest) (err error)
	// 添加同环节办理人员
	AddNodeReviewer(ctx context.Context, req SallyAddNodeReviewerRequest) (err error)

	Review(ctx context.Context, req SallyReviewRequest) (err error)

	// 预发起
	PreStartWorkflow(ctx context.Context, req SallyStartWorkflowRequest) (flowID string, err error)
}

type SallyAddons interface {
	WorkflowManager
	WorkflowQuerier
}

type SallyAddonsImpl struct {
	sallyService aggregate.ClerkService
	db           *gorm.DB
}

func NewSallyAddonsImpl(db *gorm.DB) SallyAddons {
	return &SallyAddonsImpl{
		db: db,
	}
}

type WorkflowEvent struct {
	WorkflowID   string
	NodeID       string
	ReviewTaskID string
}

// WorkflowTemplateConfig 工作流模板配置
type WorkflowTemplateConfig struct {
	// Name 工作流模板名称
	Name string `json:"name"`

	// NodeTemplateConfigs 节点模板配置
	// 根据顺序决定谁是第一个节点，然后每个节点的下一个节点也是根据顺序来判断
	NodeTemplateConfigs []NodeTemplateConfig `json:"nodeTemplateConfigs"`

	// AutoApprovalConfig 自动审批配置
	AutoApprovalConfig AutoApprovalConfig `json:"autoApprovalConfig"`

	//
}

// AutoApprovalConfig 自动审批配置
type AutoApprovalConfig struct {
	// Strategy 自动审批策略
	// 自动审批举例：如果发起人/本层节点审批人和下一个节点的审批人是同一个人，那么下一个节点的审批任务将自动审批通过
	Strategy string `json:"strategy"`
}

// 流程状态枚举类型转换
func flowStatusToSallyStatus(status string) int {
	switch status {
	case WorkflowStatusUnderReview:
		return 1
	case WorkflowStatusPassed:
		return 2
	case WorkflowStatusRejected:
		return -3
	case WorkflowStatusCancelled:
		return -5
	}
	return 0
}

func sallyStatusToFlowStatus(status int) string {
	switch status {
	case 1:
		return WorkflowStatusUnderReview
	case 2:
		return WorkflowStatusPassed
	case -3:
		return WorkflowStatusRejected
	case -5:
		return WorkflowStatusCancelled
	}
	return ""
}

// NodeTemplateConfig 节点模板配置
type NodeTemplateConfig struct {
	// NodeTemplateName 节点模板名称
	NodeTemplateName string `json:"nodeTemplateName"`

	// Kind 节点类型，1:审批类型节点，2:条件类型节点
	// 审批类型节点：该节点会自动发送审批任务，需要进行审批才能通过该节点（传统意义上的"审批节点"）
	// 条件类型节点：该节点作为条件判断节点，根据条件判断是否需要流转到下一个节点（可以理解为流程图中的菱形）
	NodeTemplateKind int `json:"nodeTemplateKind"`

	// WorkflowTemplateID 所属工作流模板的ID
	WorkflowTemplateID string `json:"workflowTemplateId"`

	// 节点审批配置
	ReviewerConfig NodeTemplateReviewerConfig `json:"nodeTemplateReviewerConfig"`

	// BusinessParams 业务参数
	BusinessParams map[string]any `json:"businessParams"`
}

// NodeTemplateReviewerConfig 节点模板审批人配置
type NodeTemplateReviewerConfig struct {
	// Kind 类型 1：指定好了用户id
	// sally认为其它数字类型都表明是角色类型的审批人。都会在需要时调用hook获取（Kind会作为hook的参数）
	Kind int `json:"kind"`
	// ReviewerNos 业务审批者编号列表
	// 该字段会在hook中用于查询ReviewerID，多个编号以逗号分隔
	ReviewerNos []string `json:"reviewerNos"`
	// ReviewerIDs 业务审批人ID列表
	// ApprovalStrategy 审批策略，空值或OR表示或批，AND表示与批
	ApprovalStrategy string `json:"approvalStrategy"`
}

func (h *SallyAddonsImpl) AddWorkflowTemplate(ctx context.Context, config WorkflowTemplateConfig) (templateID string, nodeTemplateIDs []string, err error) {
	sallyTemplateConfig := h.buildWorkflowTemplateConfig(config)
	templateID, nodeTemplateIDs, err = h.sallyService.AddWorkflowTemplate(ctx, sallyTemplateConfig)
	return templateID, nodeTemplateIDs, err
}

func (h *SallyAddonsImpl) RegisterWorkflowEventProcessor(ctx context.Context, handler WorkflowEventHandler) {
	ch := h.sallyService.SubscribeEvents(ctx)
	go func() {
		for {
			select {
			case <-ctx.Done():
				logc.Infof(ctx, "RegisterWorkflowEventProcessor: context cancelled, exiting event loop")
				return
			case event, ok := <-ch:
				if !ok {
					logc.Infof(ctx, "RegisterWorkflowEventProcessor: event channel closed, exiting event loop")
					return
				}
				go h.processWorkflowEvent(ctx, handler, event)
			}
		}
	}()
}

// processWorkflowEvent 统一处理工作流事件，包含异常捕获和日志
func (h *SallyAddonsImpl) processWorkflowEvent(ctx context.Context, handler WorkflowEventHandler, event aggregate.Event) {
	defer func() {
		if r := recover(); r != nil {
			logc.Errorf(ctx, "workflowEvent panic: %v, event: %+v", r, event)
		}
	}()
	workflowEvent := WorkflowEvent{
		WorkflowID:   event.WorkflowID,
		NodeID:       event.NodeID,
		ReviewTaskID: event.ReviewTaskID,
	}
	logc.Infof(ctx, "workflowEvent: %+v", workflowEvent)
	switch event.Kind {
	case aggregate.EventKindWorkflowPassed:
		if err := handler.HandleWorkflowPassed(ctx, h, workflowEvent); err != nil {
			logc.Errorf(ctx, "HandleWorkflowPassed error: %v, event: %+v", err, workflowEvent)
		}
	case aggregate.EventKindWorkflowRejected:
		if err := handler.HandleWorkflowRejected(ctx, h, workflowEvent); err != nil {
			logc.Errorf(ctx, "HandleWorkflowRejected error: %v, event: %+v", err, workflowEvent)
		}
	case aggregate.EventKindNodePassed:
		if err := handler.HandleWorkflowNodePassed(ctx, h, workflowEvent); err != nil {
			logc.Errorf(ctx, "HandleWorkflowNodePassed error: %v, event: %+v", err, workflowEvent)
		}

	case aggregate.EventKindWorkflowCanceled:
		if err := handler.HandleWorkflowCanceled(ctx, h, workflowEvent); err != nil {
			logc.Errorf(ctx, "HandleWorkflowCanceled error: %v, event: %+v", err, workflowEvent)
		}
	case aggregate.EventKindWorkflowStartFailed:
		if err := handler.HandleWorkflowStartFailed(ctx, h, workflowEvent); err != nil {
			logc.Errorf(ctx, "HandleWorkflowStartFailed error: %v, event: %+v", err, workflowEvent)
		}
	case aggregate.EventKindWorkflowStartSuccess:
		if err := handler.HandleWorkflowStartSuccess(ctx, h, workflowEvent); err != nil {
			logc.Errorf(ctx, "HandleWorkflowStartSuccess error: %v, event: %+v", err, workflowEvent)
		}
	}
}

func (h *SallyAddonsImpl) RegisterWorkflowReviewerQuerier(ctx context.Context, querier WorkflowReviewerQuerier) {
	//  触发初始化
	sallyImpl := &sallyImpl{
		WorkflowReviewerQuerier: querier,
	}
	sallyService, err := sally.Init(h.db, sallyImpl)
	if err != nil {
		panic(err)
	}
	h.sallyService = sallyService
}

// 查询工作流
func (h *SallyAddonsImpl) GetWorkflow(ctx context.Context, workflowID string) (workflow SallyGetWorkflowsResult, err error) {
	aggWorkflow, err := h.sallyService.GetWorkflow(ctx, workflowID)
	if err != nil {
		logc.Errorf(ctx, "GetWorkflow error: %v, workflowID: %s", err, workflowID)
		return workflow, err
	}
	workflow = toSallyGetWorkflowsResult(aggWorkflow)
	return workflow, nil
}

// 查询工作流列表
func (h *SallyAddonsImpl) GetWorkflows(ctx context.Context, req SallyGetWorkflowsParams) (workflows []SallyGetWorkflowsResult, total int64, err error) {
	aggReq := buildGetWorkflowsRequest(req)
	results, total, err := h.sallyService.GetWorkflows(ctx, aggReq)
	if err != nil {
		return nil, 0, err
	}

	for _, result := range results {
		workflows = append(workflows, toSallyGetWorkflowsResult(result))
	}

	return workflows, total, err
}

func (*SallyAddonsImpl) buildWorkflowTemplateConfig(config WorkflowTemplateConfig) aggregate.WorkflowTemplateConfig {
	nodeTemplateConfigs := make([]aggregate.NodeTemplateConfig, 0, len(config.NodeTemplateConfigs))
	for _, nodeTemplateConfig := range config.NodeTemplateConfigs {
		nodeTemplateConfigs = append(nodeTemplateConfigs, aggregate.NodeTemplateConfig{
			NodeTemplateName:   nodeTemplateConfig.NodeTemplateName,
			NodeTemplateKind:   nodeTemplateConfig.NodeTemplateKind,
			WorkflowTemplateID: nodeTemplateConfig.WorkflowTemplateID,
			BusinessParams:     nodeTemplateConfig.BusinessParams,
			ReviewerConfig: aggregate.NodeTemplateReviewerConfig{
				Kind:             nodeTemplateConfig.ReviewerConfig.Kind,
				Reviewers:        nodeTemplateConfig.ReviewerConfig.ReviewerNos,
				ApprovalStrategy: convertApprovalStrategyToSallyApprovalStrategy(nodeTemplateConfig.ReviewerConfig.ApprovalStrategy),
			},
		})
	}
	sallyTemplateConfig := aggregate.WorkflowTemplateConfig{
		Name:                config.Name,
		NodeTemplateConfigs: nodeTemplateConfigs,
		AutoApprovalConfig: aggregate.AutoApprovalConfig{
			Enable:   config.AutoApprovalConfig.Strategy != WorkflowTemplateAutoApprovalStrategyClose,
			Strategy: convertApproverKindToSallyReviewerKind(config.AutoApprovalConfig.Strategy),
		},
	}
	return sallyTemplateConfig
}

// 启动审批流
func (h *SallyAddonsImpl) StartWorkflow(ctx context.Context, req SallyStartWorkflowRequest) (err error) {
	aggReq := buildStartWorkflowRequest(req)
	return h.sallyService.StartWorkflow(ctx, aggReq)
}

// 暂存工作流草稿
func (h *SallyAddonsImpl) SaveWorkflowDraft(ctx context.Context, req SallyStartWorkflowRequest) (err error) {
	aggReq := buildStartWorkflowRequest(req)
	return h.sallyService.SaveWorkflowDraft(ctx, aggReq)
}

// 重启审批流
func (h *SallyAddonsImpl) RestartWorkflow(ctx context.Context, req SallyRestartWorkflowRequest) (err error) {
	aggReq := buildRestartWorkflowRequest(req)
	return h.sallyService.RestartWorkflow(ctx, aggReq)
}

// 更新工作流内容
func (h *SallyAddonsImpl) UpdateWorkflowContent(ctx context.Context, workflowID, formContent string, businessParams map[string]any) (err error) {
	return h.sallyService.UpdateWorkflowContent(ctx, workflowID, formContent, businessParams)
}

// 移除工作流
func (h *SallyAddonsImpl) RemoveWorkflow(ctx context.Context, workflowID string) (err error) {
	return h.sallyService.RemoveWorkflow(ctx, workflowID)
}

// 撤销工作流
func (h *SallyAddonsImpl) CancelWorkflow(ctx context.Context, workflowID string) (err error) {
	return h.sallyService.CancelWorkflow(ctx, workflowID)
}

// 获取节点详情
func (h *SallyAddonsImpl) GetNode(ctx context.Context, nodeID string) (node SallyActivatedNode, err error) {
	aggNode, err := h.sallyService.GetNode(ctx, nodeID)
	if err != nil {
		return node, err
	}
	node = toSallyActivatedNode(aggNode)
	return node, nil
}

// 审批
func (h *SallyAddonsImpl) Review(ctx context.Context, req SallyReviewRequest) (err error) {
	aggReq := buildReviewRequest(req)
	return h.sallyService.Review(ctx, aggReq)
}

// 获取审批任务列表
func (h *SallyAddonsImpl) GetNodeReviewTasks(ctx context.Context, req SallyGetNodeReviewTasksRequest) (tasks []SallyNodeReviewTask, total int64, err error) {
	aggReq := buildGetNodeReviewTasksRequest(req)
	aggTasks, total, err := h.sallyService.GetNodeReviewTasks(ctx, aggReq)
	if err != nil {
		return nil, 0, err
	}
	for _, t := range aggTasks {
		tasks = append(tasks, toSallyNodeReviewTask(t))
	}
	return tasks, total, nil
}

// 更改节点办理人员
func (h *SallyAddonsImpl) ChangeNodeReviewer(ctx context.Context, req SallyChangeNodeReviewerRequest) (err error) {
	aggReq := buildChangeNodeReviewerRequest(req)
	return h.sallyService.ChangeNodeReviewer(ctx, aggReq)
}

// 添加同环节办理人员
func (h *SallyAddonsImpl) AddNodeReviewer(ctx context.Context, req SallyAddNodeReviewerRequest) (err error) {
	aggReq := buildAddNodeReviewerRequest(req)
	return h.sallyService.AddNodeReviewer(ctx, aggReq)
}

// 预发起
func (h *SallyAddonsImpl) PreStartWorkflow(ctx context.Context, req SallyStartWorkflowRequest) (flowID string, err error) {
	return h.sallyService.GenerateWorkflowID(ctx)
}

// sallyImpl 工作流实现
type sallyImpl struct {
	WorkflowReviewerQuerier
}

// GetReviewerIDs 获取审批人id
func (h *sallyImpl) GetReviewerIDs(ctx context.Context, roleKind int, reviewerNos []string, formContent string, params aggregate.BusinessParams) (userIDs []string, err error) {
	return h.WorkflowReviewerQuerier.GetReviewerIDs(ctx, roleKind, reviewerNos, params)
}

// HandlePassedWorkflow 审批通过工作流
func (h *sallyImpl) HandlePassedWorkflow(ctx context.Context, businessID, formContent string, params aggregate.BusinessParams, businessCode string, nodeReviewTasks []aggregate.NodeReviewTask) (err error) {

	return err
}

// HandleRejectedWorkflow 审批拒绝工作流
func (h *sallyImpl) HandleRejectedWorkflow(ctx context.Context, workflowID, businessID, formContent string, params aggregate.BusinessParams, businessCode string) (err error) {

	return nil
}

// NotifyNextReviewers 下一个审批人
func (h *sallyImpl) NotifyNextReviewers(ctx context.Context, businessID, formContent string, params aggregate.BusinessParams, businessCode string, nextNodeReviewerIDs []string) (err error) {
	return nil
}

// HandlePassedNode 整个节点被审批通过后执行
// param reviewTaskID 节点中被审批通过的审批任务ID
// param reviewerID 审批上面reviewTaskID任务的人员ID
func (h *sallyImpl) HandlePassedNode(ctx context.Context, workflowID, nodeID, reviewTaskID, reviewerID string) (err error) {
	return nil
}

// HandleRejectedNode 整个节点审批拒绝后执行
func (h *sallyImpl) HandleRejectedNode(ctx context.Context, workflowID, nodeID, reviewTaskID, reviewerID string) (err error) {
	return nil
}

// HandleCrearedReviewTasks 创建审批任务后调用
// 当一个节点审批结束后并流转到了下个节点，下个节点创建完成后，会进行下个节点的所属审批任务创建
func (h *sallyImpl) HandleCrearedReviewTasks(ctx context.Context, businessID, formContent, businessCode, nextNodeID string, params aggregate.BusinessParams, nextNodeReviewTasks []aggregate.NodeReviewTask) (err error) {
	return nil
}

type SallyGetWorkflowsParams struct {
	SallyPageCond
	SallyCommonSearchCond
	SponsorID string

	BusinessCode string
	BusinessID   string

	WorkflowName   string
	WorkflowStatus string
	// CurrentNodeReviewerIDs 当前节点审批人ID列表
	CurrentNodeReviewerIDs []string
	// ReviewedByUserIDs 已审批过的用户ID列表
	ReviewedByUserIDs []string
	// BusinessParams 业务参数
	BusinessParams map[string]any
}

type SallyPageCond struct {
	NoPage   bool `json:"noPage"`
	PageNo   int  `json:"pageNo"`
	PageSize int  `json:"pageSize"`
}

type SallyCommonSearchCond struct {
	FormContentSearch string `json:"formContentSearch"`
	// CreatedAtStart 创建时间开始范围
	CreatedAtStart time.Time `json:"createdAtStart"`
	// CreatedAtEnd 创建时间结束范围
	CreatedAtEnd time.Time `json:"createdAtEnd"`
	// IDs 查询ID列表，需与aggregate层同步
	IDs []string `json:"ids"`
	// UpdatedAtStart 更新时间开始范围，需与aggregate层同步
	UpdatedAtStart time.Time `json:"updatedAtStart"`
	// UpdatedAtEnd 更新时间结束范围，需与aggregate层同步
	UpdatedAtEnd time.Time `json:"updatedAtEnd"`
}

type SallyGetWorkflowsResult struct {
	ID                     string
	FormContent            string
	Name                   string
	BusinessID             string
	BusinessCode           string
	CurrentNodeID          string
	CurrentNodeName        string
	CurrentNodeTemplateID  string
	CurrentNodeReviewewIDs []string
	Status                 string
	BusinessParams         map[string]any
	Conclusion             string
	CreatedAt              time.Time
	UpdatedAt              time.Time
	SponsorID              string
	CurrentNodeReviewTasks []SallyNodeReviewTask
}

type SallyNodeReviewTask struct {
	ID                string
	Comment           string
	Extra             map[string]any
	WorkflowID        string
	WorkflowName      string
	WorkflowCreatedAt time.Time
	NodeID            string
	NodeName          string
	NodeCreatedAt     time.Time
	Status            string
	FormContent       string
	ReviewerID        string
	CreatedAt         time.Time
	UpdatedAt         time.Time
}

// SallyChangeNodeReviewerRequest 更改节点办理人员请求
// 对应 aggregate.ChangeNodeReviewerRequest
type SallyChangeNodeReviewerRequest struct {
	TaskID        string
	NewReviewerID string
}

// SallyAddNodeReviewerRequest 添加同环节办理人员请求
// 对应 aggregate.AddNodeReviewerRequest
type SallyAddNodeReviewerRequest struct {
	NodeID     string
	ReviewerID string
}

// SallyChangeNodeReviewerRequest -> aggregate.ChangeNodeReviewerRequest
func buildChangeNodeReviewerRequest(req SallyChangeNodeReviewerRequest) aggregate.ChangeNodeReviewerRequest {
	return aggregate.ChangeNodeReviewerRequest{
		TaskID:        req.TaskID,
		NewReviewerID: req.NewReviewerID,
	}
}

// SallyAddNodeReviewerRequest -> aggregate.AddNodeReviewerRequest
func buildAddNodeReviewerRequest(req SallyAddNodeReviewerRequest) aggregate.AddNodeReviewerRequest {
	return aggregate.AddNodeReviewerRequest{
		NodeID:     req.NodeID,
		ReviewerID: req.ReviewerID,
	}
}

// --- 参数/结果转换函数，统一放在文件底部 ---

// SallyStartWorkflowRequest -> aggregate.StartWorkFlowRequest
func buildStartWorkflowRequest(req SallyStartWorkflowRequest) aggregate.StartWorkFlowRequest {
	return aggregate.StartWorkFlowRequest{
		WorkflowID:            req.WorkflowID,
		BusinessID:            req.BusinessID,
		BusinessCode:          req.BusinessCode,
		WorkflowTemplateID:    req.WorkflowTemplateID,
		FormContent:           req.FormContent,
		SponsorID:             req.SponsorID,
		Conclusion:            req.Conclusion,
		BusinessParams:        req.BusinessParams,
		GlobalConditionParams: req.GlobalConditionParams,
	}
}

// SallyRestartWorkflowRequest -> aggregate.RestartWorkFlowRequest
func buildRestartWorkflowRequest(req SallyRestartWorkflowRequest) aggregate.RestartWorkFlowRequest {
	return aggregate.RestartWorkFlowRequest{
		BusinessID:     req.BusinessID,
		BusinessCode:   req.BusinessCode,
		WorkflowID:     req.WorkflowID,
		FormContent:    req.FormContent,
		SponsorID:      req.SponsorID,
		BusinessParams: req.BusinessParams,
	}
}

// SallyReviewRequest -> aggregate.ReviewRequest
func buildReviewRequest(req SallyReviewRequest) aggregate.ReviewRequest {
	var businessParams aggregate.BusinessParams = req.Extra
	if businessParams == nil {
		businessParams = make(aggregate.BusinessParams)
	}
	businessParams.SetText(req.Comment)

	return aggregate.ReviewRequest{
		NodeReviewerTaskID: req.NodeReviewerTaskID,
		Comment:            businessParams,
		Status:             flowApprovalTaskStatusToSallyStatus(req.Status),
	}
}

// SallyGetNodeReviewTasksRequest -> aggregate.GetNodeReviewTasksRequest
func buildGetNodeReviewTasksRequest(req SallyGetNodeReviewTasksRequest) aggregate.GetNodeReviewTasksRequest {
	return aggregate.GetNodeReviewTasksRequest{
		PageCond: aggregate.PageCond{
			NoPage:   req.NoPage,
			PageNo:   req.PageNo,
			PageSize: req.PageSize,
		},
		CommonSearchCond: aggregate.CommonSearchCond{
			FormContentSearch: req.FormContentSearch,
			CreatedAtStart:    req.CreatedAtStart,
			CreatedAtEnd:      req.CreatedAtEnd,
			UpdatedAtStart:    req.UpdatedAtStart,
			UpdatedAtEnd:      req.UpdatedAtEnd,
		},
		ReviewerID:             req.ReviewerID,
		BusinessCode:           req.BusinessCode,
		BusinessID:             req.BusinessID,
		WorkflowID:             req.WorkflowID,
		WorkflowName:           req.WorkflowName,
		NodeID:                 req.NodeID,
		WorkflowCreateUser:     req.WorkflowCreateUser,
		WorkflowCreatedAtStart: req.WorkflowCreatedAtStart,
		WorkflowCreatedAtEnd:   req.WorkflowCreatedAtEnd,
		WorkflowBusinessParams: req.WorkflowBusinessParams,
		WorkflowSponsorID:      req.WorkflowSponsorID,
		WorkflowStatus:         flowStatusToSallyStatus(req.FlowStatus),
		Statuses:               flowTasksStatusesToSallyStatuses(req.Statuses),
	}
}

// aggregate.ActivatedNode -> SallyActivatedNode
func toSallyActivatedNode(node aggregate.ActivatedNode) SallyActivatedNode {
	return SallyActivatedNode{
		ID:                         node.ID,
		WorkflowID:                 node.WorkflowID,
		NodeTemplateID:             node.NodeTemplateID,
		Status:                     node.Status,
		CreatedAt:                  node.CreatedAt,
		UpdatedAt:                  node.UpdatedAt,
		NodeTemplateName:           node.NodeTemplateName,
		NodeTemplateKind:           node.NodeTemplateKind,
		ApprovalStrategy:           node.ApprovalStrategy,
		NextNodeTemplateID:         node.NextNodeTemplateID,
		NodeTemplateBusinessParams: node.NodeTemplateBusinessParams,
		NodeReviewTasks:            toSallyNodeReviewTasks(node.NodeReviewTasks),
	}
}

// aggregate.NodeReviewTask -> SallyNodeReviewTask
func toSallyNodeReviewTask(task aggregate.NodeReviewTask) SallyNodeReviewTask {
	return SallyNodeReviewTask{
		ID:                task.ID,
		Comment:           task.Comment.GetText(),
		Extra:             task.Comment,
		WorkflowID:        task.WorkflowID,
		WorkflowName:      task.WorkflowName,
		WorkflowCreatedAt: task.WorkflowCreatedAt,
		NodeID:            task.NodeID,
		NodeName:          task.NodeName,
		NodeCreatedAt:     task.NodeCreatedAt,
		Status:            sallyNodeStatusToStatus(task.Status),
		FormContent:       task.FormContent,
		ReviewerID:        task.ReviewerID,
		CreatedAt:         task.CreatedAt,
		UpdatedAt:         task.UpdatedAt,
	}
}

func toSallyNodeReviewTasks(tasks []aggregate.NodeReviewTask) []SallyNodeReviewTask {
	var result []SallyNodeReviewTask
	for _, t := range tasks {
		result = append(result, toSallyNodeReviewTask(t))
	}
	return result
}

// --- 新增参数结构体定义 ---
type SallyStartWorkflowRequest struct {
	WorkflowID            string
	BusinessID            string
	BusinessCode          string
	WorkflowTemplateID    string
	FormContent           string
	SponsorID             string
	Conclusion            string
	BusinessParams        map[string]any
	GlobalConditionParams map[string]any
}

type SallyRestartWorkflowRequest struct {
	BusinessID     string
	BusinessCode   string
	WorkflowID     string
	FormContent    string
	SponsorID      string
	BusinessParams map[string]any
}

type SallyReviewRequest struct {
	NodeReviewerTaskID string
	Comment            string
	Extra              map[string]any
	Status             string
}

type SallyGetNodeReviewTasksRequest struct {
	NoPage            bool
	PageNo            int
	PageSize          int
	FormContentSearch string
	CreatedAtStart    time.Time
	CreatedAtEnd      time.Time
	ReviewerID        string
	BusinessCode      string
	BusinessID        string
	WorkflowID        string
	WorkflowName      string
	NodeID            string
	// 新增字段
	WorkflowCreateUser     string
	WorkflowCreatedAtStart time.Time
	WorkflowCreatedAtEnd   time.Time
	WorkflowBusinessParams map[string]any
	WorkflowSponsorID      string
	UpdatedAtStart         time.Time
	UpdatedAtEnd           time.Time
	FlowStatus             string
	Statuses               []string
}

type SallyActivatedNode struct {
	ID                         string
	WorkflowID                 string
	NodeTemplateID             string
	Status                     int
	CreatedAt                  time.Time
	UpdatedAt                  time.Time
	NodeTemplateName           string
	NodeTemplateKind           int
	ApprovalStrategy           string
	NextNodeTemplateID         string
	NodeTemplateBusinessParams map[string]any
	NodeReviewTasks            []SallyNodeReviewTask
}

// --- 新增转换函数 ---
// aggregate.ActivatedWorkflow -> SallyGetWorkflowsResult
func toSallyGetWorkflowsResult(agg aggregate.ActivatedWorkflow) SallyGetWorkflowsResult {
	result := SallyGetWorkflowsResult{
		ID:                     agg.ID,
		FormContent:            agg.FormContent,
		Name:                   agg.Name,
		BusinessID:             agg.BusinessID,
		BusinessCode:           agg.BusinessCode,
		CurrentNodeID:          agg.CurrentNodeID,
		CurrentNodeName:        agg.CurrentNodeName,
		CurrentNodeTemplateID:  agg.CurrentNodeTemplateID,
		CurrentNodeReviewewIDs: agg.CurrentNodeReviewewIDs,
		Status:                 sallyStatusToFlowStatus(agg.Status),
		BusinessParams:         agg.BusinessParams,
		Conclusion:             agg.Conclusion,
		CreatedAt:              agg.CreatedAt,
		UpdatedAt:              agg.UpdatedAt,
		SponsorID:              agg.SponsorID,
		CurrentNodeReviewTasks: make([]SallyNodeReviewTask, 0, len(agg.CurrentNodeReviewTasks)),
	}
	for _, reviewTask := range agg.CurrentNodeReviewTasks {
		result.CurrentNodeReviewTasks = append(result.CurrentNodeReviewTasks, toSallyNodeReviewTask(reviewTask))
	}
	return result
}

// SallyGetWorkflowsParams -> aggregate.GetWorkflowsRequest
func buildGetWorkflowsRequest(req SallyGetWorkflowsParams) aggregate.GetWorkflowsRequest {
	return aggregate.GetWorkflowsRequest{
		PageCond: aggregate.PageCond{
			NoPage:   req.NoPage,
			PageNo:   req.PageNo,
			PageSize: req.PageSize,
		},
		CommonSearchCond: aggregate.CommonSearchCond{
			IDs:               req.IDs,
			FormContentSearch: req.FormContentSearch,
			CreatedAtStart:    req.CreatedAtStart,
			CreatedAtEnd:      req.CreatedAtEnd,
			UpdatedAtStart:    req.UpdatedAtStart,
			UpdatedAtEnd:      req.UpdatedAtEnd,
		},
		SponsorID:              req.SponsorID,
		BusinessCode:           req.BusinessCode,
		BusinessID:             req.BusinessID,
		WorkflowName:           req.WorkflowName,
		WorkflowStatus:         flowStatusToSallyStatus(req.WorkflowStatus),
		CurrentNodeReviewerIDs: req.CurrentNodeReviewerIDs,
		ReviewedByUserIDs:      req.ReviewedByUserIDs,
		BusinessParams:         req.BusinessParams,
	}
}

func convertApproverKindToSallyReviewerKind(kind string) aggregate.AutoApprovalStrategy {
	switch kind {
	case WorkflowTemplateAutoApprovalStrategyAdjacent:
		return aggregate.AutoApprovalStrategyAdjacent
	case WorkflowTemplateAutoApprovalStrategyAny:
		return aggregate.AutoApprovalStrategyAny
	}
	return aggregate.AutoApprovalStrategyAny
}

func convertApprovalStrategyToSallyApprovalStrategy(strategy string) aggregate.ApprovalStrategy {
	switch strategy {
	case WorkflowTemplateApprovalStrategyOr:
		return aggregate.ApprovalStrategyOr
	case WorkflowTemplateApprovalStrategyAnd:
		return aggregate.ApprovalStrategyAnd
	}
	return aggregate.ApprovalStrategyOr
}

func convertApprovalStrategyToApprovalStrategy(strategy aggregate.ApprovalStrategy) string {
	switch strategy {
	case aggregate.ApprovalStrategyOr:
		return WorkflowTemplateApprovalStrategyOr
	case aggregate.ApprovalStrategyAnd:
		return WorkflowTemplateApprovalStrategyAnd
	}
	return ""
}

// Status 1:未审核 2:审核通过 -1:审核未通过
func flowTaskStatusToSallyStatus(status string) int {
	switch status {
	case WorkflowStatusPassed:
		return 2
	case WorkflowStatusRejected:
		return -1
	}
	return 1
}

// Status 1:未审核 2:审核通过 -1:审核未通过
// pass-通过, reject-拒绝
func flowApprovalTaskStatusToSallyStatus(status string) int {
	switch status {
	case "pass":
		return aggregate.ReviewPassed
	case "reject":
		return aggregate.ReviewRejected
	}
	return aggregate.ReviewRejected
}

// Status 1:未审核 2:审核通过 -1:审核未通过
func sallyNodeStatusToStatus(status int) string {
	switch status {
	case 2:
		return WorkflowStatusPassed
	case -1:
		return WorkflowStatusRejected
	}
	return WorkflowStatusUnderReview
}

// --- WorkflowProgress相关结构体 ---
type SallyWorkflowProgress struct {
	WorkflowID     string
	WorkflowName   string
	Status         string
	SponsorID      string
	FormContent    string
	BusinessParams map[string]any
	NodeProgresses []SallyNodeProgress
}

type SallyNodeProgress struct {
	NodeTemplateID    string
	NodeTemplateName  string
	NodeID            string
	Status            string
	ApprovalStrategy  string
	CreatedAt         time.Time
	UpdatedAt         time.Time
	ReviewTasks       []SallyNodeProgressTask
	TemplateReviewers []SallyTemplateReviewer
}

type SallyNodeProgressTask struct {
	TaskID     string
	ReviewerID string
	Status     string
	Comment    string
	Extra      map[string]any
	CreatedAt  time.Time
	UpdatedAt  time.Time
}

type SallyTemplateReviewer struct {
	ID             string
	Kind           int
	NodeTemplateID string
	ReviewerNos    string
	ReviewerIDs    string
}

// --- WorkflowProgress转换函数 ---
func toSallyWorkflowProgress(p aggregate.WorkflowProgress) SallyWorkflowProgress {
	return SallyWorkflowProgress{
		WorkflowID:     p.WorkflowID,
		WorkflowName:   p.WorkflowName,
		Status:         sallyStatusToFlowStatus(p.Status),
		SponsorID:      p.SponsorID,
		FormContent:    p.FormContent,
		BusinessParams: p.BusinessParams,
		NodeProgresses: toSallyNodeProgresses(p.NodeProgresses),
	}
}

func toSallyNodeProgresses(nodes []aggregate.NodeProgress) []SallyNodeProgress {
	result := make([]SallyNodeProgress, 0, len(nodes))
	for _, n := range nodes {
		result = append(result, toSallyNodeProgress(n))
	}
	return result
}

func toSallyNodeProgress(n aggregate.NodeProgress) SallyNodeProgress {
	return SallyNodeProgress{
		NodeTemplateID:    n.NodeTemplateID,
		NodeTemplateName:  n.NodeTemplateName,
		NodeID:            n.NodeID,
		Status:            sallyNodeStatusToStatus(n.Status),
		ApprovalStrategy:  convertApprovalStrategyToApprovalStrategy(aggregate.ApprovalStrategy(n.ApprovalStrategy)),
		CreatedAt:         n.CreatedAt,
		UpdatedAt:         n.UpdatedAt,
		ReviewTasks:       toSallyNodeProgressTasks(n.ReviewTasks),
		TemplateReviewers: toSallyTemplateReviewers(n.TemplateReviewers),
	}
}

func toSallyNodeProgressTasks(tasks []aggregate.NodeProgressTask) []SallyNodeProgressTask {
	result := make([]SallyNodeProgressTask, 0, len(tasks))
	for _, t := range tasks {
		result = append(result, toSallyNodeProgressTask(t))
	}
	return result
}

func toSallyNodeProgressTask(t aggregate.NodeProgressTask) SallyNodeProgressTask {

	return SallyNodeProgressTask{
		TaskID:     t.TaskID,
		ReviewerID: t.ReviewerID,
		Status:     sallyNodeStatusToStatus(t.Status),
		Comment:    t.Comment.GetText(),
		Extra:      t.Comment,
		CreatedAt:  t.CreatedAt,
		UpdatedAt:  t.UpdatedAt,
	}
}

func toSallyTemplateReviewers(trs []aggregate.TemplateReviewer) []SallyTemplateReviewer {
	result := make([]SallyTemplateReviewer, 0, len(trs))
	for _, tr := range trs {
		result = append(result, toSallyTemplateReviewer(tr))
	}
	return result
}

func toSallyTemplateReviewer(tr aggregate.TemplateReviewer) SallyTemplateReviewer {
	return SallyTemplateReviewer{
		ID:             tr.ID,
		Kind:           tr.Kind,
		NodeTemplateID: tr.NodeTemplateID,
		ReviewerNos:    tr.ReviewerNos,
		ReviewerIDs:    tr.ReviewerIDs,
	}
}

// --- WorkflowProgress方法 ---
func (h *SallyAddonsImpl) GetWorkflowProgress(ctx context.Context, workflowID string) (progress SallyWorkflowProgress, err error) {
	aggProgress, err := h.sallyService.GetWorkflowProgress(ctx, workflowID)
	if err != nil {
		return progress, err
	}
	progress = toSallyWorkflowProgress(aggProgress)
	return progress, nil
}

func flowTasksStatusesToSallyStatuses(statuses []string) []int {
	result := make([]int, 0, len(statuses))
	for _, status := range statuses {
		result = append(result, flowTaskStatusToSallyStatus(status))
	}
	return result
}
