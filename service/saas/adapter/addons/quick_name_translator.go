package addons

import (
	"context"
	"fmt"
	"phoenix/service/saas/model/ent"
	"phoenix/service/saas/model/ent/file"
	"phoenix/service/saas/model/ent/organization"
	"phoenix/service/saas/model/ent/schema"
	"phoenix/service/saas/model/ent/user"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"gorm.io/gorm"
)

const (
	QuickNameTranslatorUserKey = "quick_name_translator:user:%s"
	QuickNameTranslatorOrgKey  = "quick_name_translator:org:%s"
	QuickNameTranslatorFileKey = "quick_name_translator:file:%s"
)

type QuickNameTranslator interface {
	TranslateUserNickname(ctx context.Context, userID string) string
	TranslateOrganizationName(ctx context.Context, organizationID string) string
	TranslateFileName(ctx context.Context, fileID string) string
}

type QuickNameTranslatorImpl struct {
	redis *redis.Redis
}

func NewQuickNameTranslatorImpl(redis *redis.Redis) QuickNameTranslator {
	return &QuickNameTranslatorImpl{
		redis: redis,
	}
}

func (q *QuickNameTranslatorImpl) TranslateUserNickname(ctx context.Context, userID string) string {
	nickname, err := q.redis.GetCtx(ctx, fmt.Sprintf(QuickNameTranslatorUserKey, userID))
	if err != nil {
		logc.Errorw(ctx, "get user nickname from redis error", logx.LogField{Key: "error", Value: err})
		return ""
	}
	return nickname
}

func (q *QuickNameTranslatorImpl) TranslateOrganizationName(ctx context.Context, organizationID string) string {
	organizationName, err := q.redis.GetCtx(ctx, fmt.Sprintf(QuickNameTranslatorOrgKey, organizationID))
	if err != nil {
		logc.Errorw(ctx, "get organization name from redis error", logx.LogField{Key: "error", Value: err})
		return ""
	}
	return organizationName
}

func (q *QuickNameTranslatorImpl) TranslateFileName(ctx context.Context, fileID string) string {
	fileName, err := q.redis.GetCtx(ctx, fmt.Sprintf(QuickNameTranslatorFileKey, fileID))
	if err != nil {
		logc.Errorw(ctx, "get file name from redis error", logx.LogField{Key: "error", Value: err})
		return ""
	}
	return fileName
}

// 缓存初始化加载器

type CacheInitLoader struct {
	redis  *redis.Redis
	gormDB *gorm.DB
	entDB  *ent.Client
}

func NewCacheInitLoader(redis *redis.Redis, gormDB *gorm.DB, entDB *ent.Client) *CacheInitLoader {
	return &CacheInitLoader{
		redis:  redis,
		gormDB: gormDB,
		entDB:  entDB,
	}
}

// init cache
func (c *CacheInitLoader) InitCache(ctx context.Context) error {
	users, err := c.entDB.User.Query().Select(user.FieldID, user.FieldNickname).All(schema.SkipTenantInject(schema.SkipSoftDelete(ctx)))
	if err != nil {
		return err
	}
	for _, user := range users {
		c.redis.SetCtx(ctx, fmt.Sprintf(QuickNameTranslatorUserKey, user.ID), user.Nickname)
	}

	organizations, err := c.entDB.Organization.Query().Select(organization.FieldID, organization.FieldName).All(schema.SkipTenantInject(schema.SkipSoftDelete(ctx)))
	if err != nil {
		return err
	}
	for _, organization := range organizations {
		c.redis.SetCtx(ctx, fmt.Sprintf(QuickNameTranslatorOrgKey, organization.ID), organization.Name)
	}

	files, err := c.entDB.File.Query().Select(file.FieldID, file.FieldName).All(schema.SkipTenantInject(schema.SkipSoftDelete(ctx)))
	if err != nil {
		return err
	}
	for _, file := range files {
		c.redis.SetCtx(ctx, fmt.Sprintf(QuickNameTranslatorFileKey, file.ID), file.Name)
	}
	return nil
}

// 更新用户昵称
func (c *CacheInitLoader) UpdateUserNickname(ctx context.Context, userID string, nickname string) error {
	c.redis.SetCtx(ctx, fmt.Sprintf(QuickNameTranslatorUserKey, userID), nickname)
	return nil
}

// 删除用户昵称
func (c *CacheInitLoader) DeleteUserNickname(ctx context.Context, userID string) error {
	c.redis.DelCtx(ctx, fmt.Sprintf(QuickNameTranslatorUserKey, userID))
	return nil
}

// 更新组织名称
func (c *CacheInitLoader) UpdateOrganizationName(ctx context.Context, organizationID string, name string) error {
	c.redis.SetCtx(ctx, fmt.Sprintf(QuickNameTranslatorOrgKey, organizationID), name)
	return nil
}

// 删除组织名称
func (c *CacheInitLoader) DeleteOrganizationName(ctx context.Context, organizationID string) error {
	c.redis.DelCtx(ctx, fmt.Sprintf(QuickNameTranslatorOrgKey, organizationID))
	return nil
}

// 更新文件名称
func (c *CacheInitLoader) UpdateFileName(ctx context.Context, fileID string, name string) error {
	c.redis.SetCtx(ctx, fmt.Sprintf(QuickNameTranslatorFileKey, fileID), name)
	return nil
}

// 删除文件名称
func (c *CacheInitLoader) DeleteFileName(ctx context.Context, fileID string) error {
	c.redis.DelCtx(ctx, fmt.Sprintf(QuickNameTranslatorFileKey, fileID))
	return nil
}
