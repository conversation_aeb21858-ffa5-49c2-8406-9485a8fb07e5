package kqs

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"testing"

	"github.com/google/uuid"
)

func TestProducer_SendMessage(t *testing.T) {
	cfg := &KafkaConfig{
		Brokers: []string{"192.168.110.74:9092"},
	}
	producer := NewKafkaProducer(cfg)
	defer producer.Close()

	err := producer.SendMessage(context.Background(), "test", []byte("key"), []byte("hello kafka-go"), nil)
	if err != nil {
		t.Logf("SendMessage error: %v (如未启动Kafka服务可忽略)", err)
	}
}

func TestProducer_SendDataExportMessage(t *testing.T) {
	cfg := &KafkaConfig{
		Brokers: []string{"192.168.110.74:9092"},
	}
	producer := NewKafkaProducer(cfg)
	defer producer.Close()
	u := uuid.New()
	uStr := u.String()
	id := strings.ReplaceAll(uStr, "-", "")
	fmt.Println(id)

	msg := map[string]interface{}{
		"task_id":     id,
		"file_id":     "573496933281197718",
		"file_name":   "测试文档.docx",
		"status":      1,
		"module_name": "test",
		"user_id":     "570845193729765014",
	}
	value, err := json.Marshal(msg)
	if err != nil {
		t.Fatalf("序列化消息失败: %v", err)
	}

	err = producer.SendMessage(context.Background(), "sit_workflow_passed_SIGNATURE_APPROVAL", nil, value, nil)
	if err != nil {
		t.Logf("SendMessage error: %v (如未启动Kafka服务可忽略)", err)
	}
}
