package kqs

import (
	"context"
	"log"
	"phoenix/service/saas/adapter/addons"

	"github.com/segmentio/kafka-go"
)

// MessageHandler defines the interface for handling Kafka messages.
type MessageHandler interface {
	Handle(ctx context.Context, message []byte, idGenerator addons.IDGeneratorAddons) error
}

// KafkaConsumer is a generic Kafka consumer adapter.
type KafkaConsumer struct {
	readerConfig kafka.ReaderConfig
	handler      MessageHandler
}

// NewKafkaConsumer creates a new generic KafkaConsumer.
func NewKafkaConsumer(readerConfig kafka.ReaderConfig, handler MessageHandler) *KafkaConsumer {
	return &KafkaConsumer{
		readerConfig: readerConfig,
		handler:      handler,
	}
}

// Start starts the Kafka consumer.
func (c *KafkaConsumer) Start(ctx context.Context, idGenerator addons.IDGeneratorAddons) {
	log.Printf("[KafkaConsumer] 启动监听Topic: %s ...", c.readerConfig.Topic)

	r := kafka.NewReader(c.readerConfig)
	defer r.Close()

	for {
		m, err := r.ReadMessage(ctx)
		if err != nil {
			log.Printf("读取Kafka消息失败: %v", err)
			// Depending on error type, you might want to continue or break
			// For now, let's break on error to prevent tight loop on persistent errors
			break
		}

		err = c.handler.Handle(ctx, m.Value, idGenerator)
		if err != nil {
			log.Printf("处理Kafka消息失败: %v", err)
		}
	}
}
