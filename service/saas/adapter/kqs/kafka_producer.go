package kqs

import (
	"context"
	"log"

	"github.com/segmentio/kafka-go"
)

// KafkaConfig 定义Kafka相关配置
type KafkaConfig struct {
	Brokers []string `yaml:"brokers"`
}

type KafkaProducer struct {
	brokers []string
	writers map[string]*kafka.Writer // 按topic复用writer
}

// NewProducer 根据配置创建Producer实例
func NewKafkaProducer(cfg *KafkaConfig) *KafkaProducer {
	return &KafkaProducer{
		brokers: cfg.Brokers,
		writers: make(map[string]*kafka.Writer),
	}
}

// SendSimpleMessage 发送消息到指定topic
func (p *KafkaProducer) SendSimpleMessage(ctx context.Context, topic string, key, value []byte) error {
	return p.SendMessage(ctx, topic, key, value, nil)
}

// SendMessageWithHeaders 发送消息到指定topic
func (p *KafkaProducer) SendMessage(ctx context.Context, topic string, key, value []byte, headers map[string]string) error {
	writer, ok := p.writers[topic]
	if !ok {
		writer = &kafka.Writer{
			Addr:     kafka.TCP(p.brokers...),
			Topic:    topic,
			Balancer: &kafka.LeastBytes{},
		}
		p.writers[topic] = writer
	}

	headersList := make([]kafka.Header, 0, len(headers))
	for k, v := range headers {
		headersList = append(headersList, kafka.Header{Key: k, Value: []byte(v)})
	}

	msg := kafka.Message{
		Key:     key,
		Value:   value,
		Headers: headersList,
	}
	err := writer.WriteMessages(ctx, msg)
	if err != nil {
		log.Printf("Kafka消息发送失败，topic=%s, err=%v", topic, err)
	}
	return err
}

// Close 关闭所有writer资源
func (p *KafkaProducer) Close() error {
	var firstErr error
	for topic, writer := range p.writers {
		if err := writer.Close(); err != nil && firstErr == nil {
			log.Printf("关闭topic %s writer失败: %v", topic, err)
			firstErr = err
		}
	}
	return firstErr
}
