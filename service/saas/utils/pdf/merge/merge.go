package merge

import (
	"fmt"
	"os"
	"os/exec"
	"path"
	"runtime"

	"gitlab.zhijiasoft.com/paperless-group/saas-common/utils/file"
)

type MergeFileInfo struct {
	FilePath  string
	FileSize  uint64
	FileType  string
	FileHash  string
	FiletName string
}

func MergePDF(mergeSetPath []string, savePath string) (*MergeFileInfo, error) {
	var mergeFileInfo *MergeFileInfo
	pwd, _ := os.Getwd()

	// 获取部署环境信息
	systemName := runtime.GOOS
	systemArch := runtime.GOARCH

	mainHandlerFileName := "merge2pdf_linux-amd64.OS"

	if systemName == "windows" {
		mainHandlerFileName = "merge2pdf_windows-amd64.exe"
	}

	if systemName == "darwin" {
		mainHandlerFileName = "merge2pdf_darwin-amd64.OS"
	}

	if systemName == "linux" {
		if systemArch == "arm64" {
			mainHandlerFileName = "merge2pdf_linux-arm64.architecture"
		}
	}

	mainHandlerFilePath := path.Join(pwd, "tools/merge2pdf/", mainHandlerFileName)

	mergeFileTotal := 0

	args := []string{}
	args = append(args, savePath)
	for _, mergeSetPathItem := range mergeSetPath {
		// 验证文件是否存在
		if _, err := os.Stat(mergeSetPathItem); err != nil {
			continue
		}
		mergeFileTotal++
		args = append(args, mergeSetPathItem)
	}

	if mergeFileTotal == 0 {
		return nil, fmt.Errorf("合并的文件不存在")
	}

	cmd := exec.Command(mainHandlerFilePath, args...)
	err := cmd.Run()
	if err != nil {
		fmt.Println("合并文件错误:", err)
		return nil, fmt.Errorf("合并文件失败,服务器内部错误")
	}

	fileInfo, err := os.Stat(savePath)
	if err != nil {
		fmt.Println("获取转换后文件信息错误:", err)
		return nil, err
	}

	fileHash, err := file.CalculateFileHash(savePath)
	if err != nil {
		fmt.Println("转换前文件hash参数加载错误:", err)
		return nil, err
	}

	mergeFileInfo = &MergeFileInfo{
		FilePath:  savePath,
		FileSize:  uint64(fileInfo.Size()),
		FileType:  ".pdf",
		FileHash:  fileHash,
		FiletName: fileInfo.Name(),
	}

	return mergeFileInfo, nil
}
