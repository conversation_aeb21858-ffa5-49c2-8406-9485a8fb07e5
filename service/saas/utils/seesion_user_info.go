package utils

import (
	"context"
	"strconv"

	"github.com/zeromicro/go-zero/core/logc"
)

const (
	UserIdKey         = "UserId"
	TenantIdKey       = "TenantId"
	IsAdminUnitKey    = "IsAdminUnit"
	IsSuperAdminKey   = "IsSuperAdmin"
	DeviceKindKey     = "DeviceKind"
	IsVirtualUserKey  = "IsVirtualUser"
	MobileKey         = "Mobile"
	TraceIdKey        = "TraceId"
	OrganizationIdKey = "OrganizationId"
)

type UserLoginInfo struct {
	// 登录用户id
	UserId string
	// 租户id
	TenantId string
	// 设备类型
	DeviceKind int
	// 是否虚拟用户
	IsVirtualUser bool
	// 手机号
	Mobile string
	// 组织id
	OrganizationId string

	// 是否超级管理员
	IsSuperAdmin bool
}

func NewUserLoginInfo(userId, tenantId, mobile, organizationId string, deviceKind int, isVirtualUser bool) *UserLoginInfo {
	return &UserLoginInfo{
		UserId:         userId,
		TenantId:       tenantId,
		DeviceKind:     deviceKind,
		IsVirtualUser:  isVirtualUser,
		Mobile:         mobile,
		OrganizationId: organizationId,
	}
}

func GetCurrentLoginUser(ctx context.Context) *UserLoginInfo {
	return &UserLoginInfo{
		UserId:         GetContextUserID(ctx),
		TenantId:       GetContextTenantID(ctx),
		DeviceKind:     GetContextDeviceKind(ctx),
		IsVirtualUser:  GetContextIsVirtualUser(ctx),
		Mobile:         GetContextMobile(ctx),
		OrganizationId: GetContextOrganizationID(ctx),
		IsSuperAdmin:   GetContextIsSuperAdmin(ctx),
	}
}

func GetContextDeviceKind(ctx context.Context) int {
	deviceKind, ok := ctx.Value(DeviceKindKey).(int)
	if !ok {
		return 0
	}
	return deviceKind
}
func GetContextUserID(ctx context.Context) string {
	userId, ok := ctx.Value(UserIdKey).(string)
	if !ok {
		logc.Errorw(ctx, "current session no user id info")
		return ""
	}
	return userId
}

// GetContextTenantID 获取登录租户ID
func GetContextTenantID(ctx context.Context) string {
	tenantId, ok := ctx.Value(TenantIdKey).(string)
	if !ok {
		logc.Errorw(ctx, "current session no user tenant id info")
		return ""
	}
	return tenantId
}

func GetContextIsVirtualUser(ctx context.Context) bool {
	isVirtualUser := ctx.Value(IsVirtualUserKey)
	if isVirtualUser == nil {
		return false
	}
	if isVirtualUserStr, ok := isVirtualUser.(string); !ok {
		result, _ := strconv.ParseBool(isVirtualUserStr)
		return result
	}

	if isVirtualUserBool, ok := isVirtualUser.(bool); ok {
		return isVirtualUserBool
	}

	return false
}

func GetContextIsSuperAdmin(ctx context.Context) bool {
	isSuperAdmin := ctx.Value(IsSuperAdminKey)
	if isSuperAdmin == nil {
		return false
	}
	if isSuperAdminStr, ok := isSuperAdmin.(string); !ok {
		result, _ := strconv.ParseBool(isSuperAdminStr)
		return result
	}

	if isSuperAdminBool, ok := isSuperAdmin.(bool); ok {
		return isSuperAdminBool
	}

	return false
}

func GetContextMobile(ctx context.Context) string {
	mobile, ok := ctx.Value(MobileKey).(string)
	if !ok {
		return ""
	}
	return mobile
}

func GetContextTraceId(ctx context.Context) string {
	traceId, ok := ctx.Value(TraceIdKey).(string)
	if !ok {
		return ""
	}
	return traceId
}

func GetContextOrganizationID(ctx context.Context) string {
	organizationId, ok := ctx.Value(OrganizationIdKey).(string)
	if !ok {
		return ""
	}
	return organizationId
}

func (u *UserLoginInfo) SetContext(ctx context.Context) context.Context {
	ctx = context.WithValue(ctx, UserIdKey, u.UserId)
	ctx = context.WithValue(ctx, TenantIdKey, u.TenantId)
	ctx = context.WithValue(ctx, DeviceKindKey, u.DeviceKind)
	ctx = context.WithValue(ctx, IsVirtualUserKey, u.IsVirtualUser)
	ctx = context.WithValue(ctx, MobileKey, u.Mobile)
	ctx = context.WithValue(ctx, OrganizationIdKey, u.OrganizationId)
	ctx = context.WithValue(ctx, IsSuperAdminKey, u.IsSuperAdmin)
	return ctx
}
