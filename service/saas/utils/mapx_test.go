package utils

import (
	"reflect"
	"testing"
)

func TestParseIntParam(t *testing.T) {
	tests := []struct {
		name string
		m    map[string]interface{}
		key  string
		want int64
		ok   bool
	}{
		// 后续补充具体测试用例
		{
			name: "test1",
			m:    map[string]interface{}{"key": 1},
			key:  "key",
			want: 1,
			ok:   true,
		},
		{
			name: "test2",
			m:    map[string]interface{}{"key": "1"},
			key:  "key",
			want: 1,
			ok:   true,
		},
		{
			name: "test3",
			m:    map[string]interface{}{"key": 1.1},
			key:  "key",
			want: 1,
			ok:   true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, ok := ParseIntParam(tt.m, tt.key)
			if got != tt.want || ok != tt.ok {
				t.Errorf("ParseIntParam() = (%v, %v), want (%v, %v)", got, ok, tt.want, tt.ok)
			}
		})
	}
}

func TestParseStringSliceParam(t *testing.T) {
	t.Run("key exists and is []string", func(t *testing.T) {
		m := map[string]interface{}{"a": []string{"x", "y"}}
		res, ok := ParseStringSliceParam(m, "a")
		if !ok || !reflect.DeepEqual(res, []string{"x", "y"}) {
			t.Errorf("expected [x y], true; got %v, %v", res, ok)
		}
	})

	t.Run("key exists but is not []string", func(t *testing.T) {
		m := map[string]interface{}{"a": 123}
		res, ok := ParseStringSliceParam(m, "a")
		if ok || res != nil {
			t.Errorf("expected nil, false; got %v, %v", res, ok)
		}
	})

	t.Run("key does not exist", func(t *testing.T) {
		m := map[string]interface{}{"b": []string{"z"}}
		res, ok := ParseStringSliceParam(m, "a")
		if ok || res != nil {
			t.Errorf("expected nil, false; got %v, %v", res, ok)
		}
	})

	t.Run("key exists and is nil", func(t *testing.T) {
		m := map[string]interface{}{"a": nil}
		res, ok := ParseStringSliceParam(m, "a")
		if ok || res != nil {
			t.Errorf("expected nil, false; got %v, %v", res, ok)
		}
	})

	t.Run("key exists and is []interface{} with strings", func(t *testing.T) {
		m := map[string]interface{}{"a": []interface{}{"x", "y"}}
		res, ok := ParseStringSliceParam(m, "a")
		if ok || res != nil {
			t.Errorf("expected nil, false; got %v, %v", res, ok)
		}
	})

	t.Run("key exists and is []interface{} with all strings", func(t *testing.T) {
		m := map[string]interface{}{"a": []interface{}{"x", "y"}}
		res, ok := ParseStringSliceParam(m, "a")
		if !ok || !reflect.DeepEqual(res, []string{"x", "y"}) {
			t.Errorf("expected [x y], true; got %v, %v", res, ok)
		}
	})
}
