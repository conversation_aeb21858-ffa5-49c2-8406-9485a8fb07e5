package utils

import (
	"encoding/base64"
	"encoding/hex"
	"strings"

	"github.com/tjfoc/gmsm/sm4"
)

type Sm4Tool interface {
	Decrypt(encryptedData string) (result []byte, err error)
	Encrypt(originContent []byte) (result []byte, err error)
}

type Sm4ToolImpl struct {
	key []byte
}

func NewSm4Tool() Sm4ToolImpl {
	return Sm4ToolImpl{
		key: []byte("9r17u127a9z64h4p"),
	}
}

func (s Sm4ToolImpl) Decrypt(encryptedData string) (result []byte, err error) {
	// 校验是不是合法加密数据
	if s.isInvalid(encryptedData) {
		return
	}

	// 解码Base64字符串
	decodedBytes, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return
	}

	// 解码后的十六进制转回字符串
	b, err := hex.DecodeString(string(decodedBytes))
	if err != nil {
		return
	}

	// 设置iv
	if err = sm4.SetIV(s.key); err != nil {
		return
	}
	// 对内容进行解密
	return sm4.Sm4Cbc(s.key, b, false) //sm4Ecb模式pksc7填充解密
}

func (s Sm4ToolImpl) Encrypt(originContent []byte) (result []byte, err error) {
	// 设置iv
	if err = sm4.SetIV(s.key); err != nil {
		return
	}
	// 对内容进行加密
	encryptedContent, err := sm4.Sm4Cbc(s.key, originContent, true) //sm4Ecb模式pksc7填充加密
	if err != nil {
		return
	}

	// 加密后进行十六进制转化
	hexContent := hex.EncodeToString(encryptedContent)

	// 再进行base64编码
	base64Content := []byte(base64.StdEncoding.EncodeToString([]byte(hexContent)))

	return base64Content, err
}

func (s Sm4ToolImpl) EncryptStr(originContent string) (result string) {
	// 设置iv
	if err := sm4.SetIV(s.key); err != nil {
		return
	}
	// 对内容进行加密
	encryptedContent, err := sm4.Sm4Cbc(s.key, []byte(originContent), true) //sm4Ecb模式pksc7填充加密
	if err != nil {
		return
	}

	// 加密后进行十六进制转化
	hexContent := hex.EncodeToString(encryptedContent)

	// 再进行base64编码
	base64Content := []byte(base64.StdEncoding.EncodeToString([]byte(hexContent)))
	return string(base64Content)
}

func (s Sm4ToolImpl) isInvalid(encryptedData string) (result bool) {
	return encryptedData == "" || strings.Contains(encryptedData, "YzYyMTE1MTgwZjQzNjc5NTFjMGZhZjZjOWQzZjI4OTQ=")
}
