package tokenbox

import (
	"context"
	"fmt"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"strings"
)

const validToken = "validToken"

type tokenBox struct {
	rds *redis.Redis
}

var box tokenBox

func InitTokenBox(host, mode, pass string) {
	conf := redis.RedisConf{
		Host: host,
		Type: mode,
		Pass: pass,
		Tls:  false,
	}

	rds := redis.MustNewRedis(conf)
	if rds == nil {
		panic("redis init failed")
	}

	box = tokenBox{
		rds: rds,
	}
}

func IsValidToken(token string) (bool, error) {
	return box.rds.Exists(GetValidTokenKey(token))
}

func GetValidTokenKey(token string) string {
	return fmt.Sprintf("%s:%s", validToken, token)
}

func GetValidTokens(ctx context.Context) (result []string, err error) {
	cursor := uint64(0)
	match := fmt.Sprintf("%s:*", validToken)
	for {
		tokens, nextCursor, err := box.rds.ScanCtx(ctx, cursor, match, 10)
		if err != nil {
			return result, err
		}
		cursor = nextCursor

		result = append(result, tokens...)

		if cursor == 0 {
			break
		}
	}

	for i := range result {
		result[i] = strings.Replace(result[i], validToken+":", "", 1)
	}

	return result, err

}
