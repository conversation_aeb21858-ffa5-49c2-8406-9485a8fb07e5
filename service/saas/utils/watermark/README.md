# PDF水印工具

这是一个功能强大的PDF水印工具，支持重复水印和图章两种模式，完全兼容中文字体。

## 功能特性

### 🎯 核心功能
- **重复水印模式**: 在整个页面重复显示水印文本
- **图章模式**: 在指定位置添加单个水印图章
- **中文支持**: 完全支持中文字体和文本
- **灵活配置**: 支持字体、颜色、透明度、旋转角度等多种参数

### 🌟 图章功能特性
- **单个图章**: 在指定位置添加单个图章
- **重复图章**: 在页面上重复显示图章，支持自定义间距
- **多种位置**: 支持6种预设位置和自定义位置
- **交错排列**: 重复图章支持交错排列效果

### 📍 图章位置选项
- 居中 (Center)
- 左上角 (TopLeft)
- 右上角 (TopRight)
- 左下角 (BottomLeft)
- 右下角 (BottomRight)
- 自定义位置 (Custom)

## 快速开始

### 基本用法

```go
import "phoenix-zhong-yi-testing/service/saas/utils/watermark"

// 打开PDF文件
file, err := os.Open("document.pdf")
if err != nil {
    log.Fatal(err)
}
defer file.Close()

// 配置水印选项
opts := watermark.WatermarkOptions{
    WatermarkTxt: "机密文件",
    FontSize:     48,
    Type:         watermark.WatermarkTypeStamp,
    StampPosition: watermark.StampPositionCenter,
}

// 添加水印
result, err := watermark.AddWatermark(file, opts)
if err != nil {
    log.Fatal(err)
}
```

### 配置选项详解

```go
type WatermarkOptions struct {
    WatermarkTxt  string        // 水印文本
    FontSize      int           // 字号（pt）
    FontName      string        // 字体名称，支持中文字体
    ColorR        float64       // 颜色R (0-255 或 0-1)
    ColorG        float64       // 颜色G (0-255 或 0-1)
    ColorB        float64       // 颜色B (0-255 或 0-1)
    Opacity       float64       // 透明度 (0-1)
    Rotation      float64       // 旋转角度 (-180~180)
    XOffset       int           // 水平偏移
    YOffset       int           // 垂直偏移
    Type          WatermarkType // 水印类型
    StampPosition StampPosition // 图章位置
    StampX        float64       // 自定义X坐标
    StampY        float64       // 自定义Y坐标
    IsRepeated    bool          // 是否重复显示（图章模式）
    RepeatSpaceX  float64       // 重复图章水平间距
    RepeatSpaceY  float64       // 重复图章垂直间距
}
```

## 使用示例

### 1. 中文图章（居中）

```go
opts := watermark.WatermarkOptions{
    WatermarkTxt:  "机密文件",
    FontSize:      48,
    FontName:      watermark.DefaultFontName,
    ColorR:        255, // 红色
    ColorG:        0,
    ColorB:        0,
    Opacity:       0.8,
    Type:          watermark.WatermarkTypeStamp,
    StampPosition: watermark.StampPositionCenter,
}
```

### 2. 英文图章（右下角，旋转）

```go
opts := watermark.WatermarkOptions{
    WatermarkTxt:  "CONFIDENTIAL",
    FontSize:      36,
    FontName:      watermark.AlternateFontName,
    ColorR:        0, // 蓝色
    ColorG:        0,
    ColorB:        255,
    Opacity:       0.6,
    Rotation:      -45, // 逆时针45度
    Type:          watermark.WatermarkTypeStamp,
    StampPosition: watermark.StampPositionBottomRight,
}
```

### 3. 自定义位置图章

```go
opts := watermark.WatermarkOptions{
    WatermarkTxt:  "审核通过",
    FontSize:      32,
    FontName:      watermark.DefaultFontName,
    ColorR:        0, // 绿色
    ColorG:        128,
    ColorB:        0,
    Opacity:       0.9,
    Rotation:      15,
    Type:          watermark.WatermarkTypeStamp,
    StampPosition: watermark.StampPositionCustom,
    StampX:        200, // 自定义X坐标
    StampY:        300, // 自定义Y坐标
}
```

### 4. 重复图章模式

```go
opts := watermark.WatermarkOptions{
    WatermarkTxt:  "机密",
    FontSize:      28,
    FontName:      watermark.DefaultFontName,
    ColorR:        255, // 红色
    ColorG:        0,
    ColorB:        0,
    Opacity:       0.4,
    Rotation:      -30,
    Type:          watermark.WatermarkTypeStamp,
    StampPosition: watermark.StampPositionCenter,
    IsRepeated:    true,    // 启用重复模式
    RepeatSpaceX:  200,     // 水平间距200pt
    RepeatSpaceY:  150,     // 垂直间距150pt
}
```

### 5. 重复中文水印（传统模式）

```go
opts := watermark.WatermarkOptions{
    WatermarkTxt: "凤凰科技有限公司",
    FontSize:     20,
    FontName:     watermark.DefaultFontName,
    ColorR:       128, // 灰色
    ColorG:       128,
    ColorB:       128,
    Opacity:      0.3,
    Rotation:     -30,
    Type:         watermark.WatermarkTypeRepeated,
    XOffset:      50, // 水平间距
    YOffset:      30, // 垂直间距
}
```

## 字体支持

### 智能字体选择

系统会根据操作系统自动选择最合适的中文字体：

#### 内置字体常量
- `DefaultFontName`: "SimSun" - 默认中文字体名称
- `AlternateFontName`: "Arial" - 英文备用字体
- `MacOSChineseFontPath`: macOS 系统中文字体路径
- `WindowsChineseFontName`: Windows 系统中文字体
- `LinuxChineseFontName`: Linux 系统中文字体

#### 自动字体选择
```go
// 不指定字体，系统自动选择
opts := watermark.WatermarkOptions{
    WatermarkTxt: "机密文件", // 系统会自动选择中文字体
}

// 或者使用推荐字体
opts.FontName = watermark.GetRecommendedFont("机密文件")
```

#### 字体验证
```go
// 验证字体是否可用
valid, recommended := watermark.ValidateFont("SimSun")
if !valid {
    opts.FontName = recommended // 使用推荐的替代字体
}
```

#### 自定义字体
您可以指定任何系统支持的字体名称或路径：

```go
// 字体名称
opts.FontName = "Microsoft YaHei" // 微软雅黑
opts.FontName = "KaiTi"          // 楷体

// 字体文件路径（推荐用于确保兼容性）
opts.FontName = "/System/Library/Fonts/STHeiti Medium.ttc" // macOS
opts.FontName = "C:\\Windows\\Fonts\\simsun.ttc"          // Windows
```

## 重复图章功能详解

重复图章是图章模式的扩展功能，可以在页面上按指定间距重复显示图章，同时保持图章的所有特性。

### 🔄 重复模式特性

1. **基于位置重复**: 以指定的图章位置为起点，向右下方向重复
2. **自定义间距**: 可以设置水平和垂直间距
3. **交错排列**: 奇数行自动偏移，形成交错效果
4. **智能边界**: 自动计算重复数量，确保覆盖整个页面

### 📐 重复图章配置

```go
// 启用重复图章的关键配置
opts := watermark.WatermarkOptions{
    Type:         watermark.WatermarkTypeStamp, // 必须是图章模式
    IsRepeated:   true,                         // 启用重复
    RepeatSpaceX: 200,                          // 水平间距
    RepeatSpaceY: 150,                          // 垂直间距
}
```

### 🎯 重复起始位置

重复图章支持所有图章位置作为起始点：

#### 从中心开始重复
```go
opts.StampPosition = watermark.StampPositionCenter
opts.IsRepeated = true
// 从页面中心开始，向四周重复
```

#### 从左上角开始重复
```go
opts.StampPosition = watermark.StampPositionTopLeft
opts.IsRepeated = true
// 从左上角开始，向右下重复
```

#### 从自定义位置开始重复
```go
opts.StampPosition = watermark.StampPositionCustom
opts.StampX = 100  // 起始X坐标
opts.StampY = 200  // 起始Y坐标
opts.IsRepeated = true
// 从指定坐标开始重复
```

### 🔧 间距计算

如果不设置重复间距，系统会自动计算：

```go
// 自动计算公式
RepeatSpaceX = 文本宽度 * 0.8 + 100pt
RepeatSpaceY = 字体大小 + 80pt
```

### 💡 使用建议

1. **机密文档**: 使用小间距密集重复，增强防伪效果
2. **草稿文档**: 使用大间距稀疏重复，不影响阅读
3. **审核文档**: 使用中等间距，平衡美观和功能

### 🎨 视觉效果对比

| 模式 | 特点 | 适用场景 |
|------|------|----------|
| 单个图章 | 简洁明了 | 正式文档、签章 |
| 重复图章 | 覆盖全面 | 防伪、标识 |
| 传统水印 | 背景效果 | 版权保护 |

## 颜色配置

支持两种颜色格式：

### RGB值 (0-255)
```go
opts.ColorR = 255 // 红色分量
opts.ColorG = 128 // 绿色分量
opts.ColorB = 0   // 蓝色分量
```

### 标准化值 (0-1)
```go
opts.ColorR = 1.0 // 红色分量
opts.ColorG = 0.5 // 绿色分量
opts.ColorB = 0.0 // 蓝色分量
```

## 测试

运行测试以验证功能：

```bash
# 运行所有测试
go test -v

# 运行特定测试
go test -v -run TestAddPDFStamp

# 查看测试覆盖率
go test -cover
```

## 字体问题故障排除

### 常见字体错误

#### 错误：`pdfcpu: font SimSun not available`
**原因**: 系统中没有安装 SimSun 字体

**解决方案**:
```go
// 方案1：使用智能字体选择（推荐）
opts.FontName = watermark.GetRecommendedFont("机密文件")

// 方案2：使用字体文件路径
opts.FontName = "/System/Library/Fonts/STHeiti Medium.ttc" // macOS

// 方案3：验证并使用备用字体
valid, recommended := watermark.ValidateFont("SimSun")
if !valid {
    opts.FontName = recommended
}
```

#### 错误：字体文件路径不存在
**解决方案**:
```go
// 使用系统自动检测
opts.FontName = "" // 留空，系统会自动选择合适的字体
```

### 不同操作系统的字体配置

#### macOS 系统
```go
// 推荐使用系统字体路径
opts.FontName = "/System/Library/Fonts/STHeiti Medium.ttc"
// 或者
opts.FontName = "/System/Library/Fonts/PingFang.ttc"
```

#### Windows 系统
```go
// 使用字体名称
opts.FontName = "SimSun"
// 或使用字体文件路径
opts.FontName = "C:\\Windows\\Fonts\\simsun.ttc"
```

#### Linux 系统
```go
// 使用开源中文字体
opts.FontName = "WenQuanYi Micro Hei"
// 或使用字体文件路径
opts.FontName = "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc"
```

### 字体测试工具

```go
// 测试字体是否可用
func testFont(fontName string) {
    valid, recommended := watermark.ValidateFont(fontName)
    if valid {
        fmt.Printf("✅ 字体 %s 可用\n", fontName)
    } else {
        fmt.Printf("❌ 字体 %s 不可用，推荐使用: %s\n", fontName, recommended)
    }
}
```

## 注意事项

1. **字体兼容性**: 使用智能字体选择功能，系统会自动选择合适的字体
2. **坐标系统**: 使用PDF标准坐标系（左下角为原点）
3. **文件格式**: 目前仅支持PDF格式
4. **性能考虑**: 大文件处理时建议适当调整参数以平衡质量和性能
5. **字体路径**: 推荐使用绝对路径指定字体文件，确保跨环境兼容性

## 错误处理

```go
result, err := watermark.AddWatermark(file, opts)
if err != nil {
    switch {
    case strings.Contains(err.Error(), "font"):
        log.Println("字体相关错误，请检查字体设置")
    case strings.Contains(err.Error(), "page"):
        log.Println("页面处理错误，请检查PDF文件")
    default:
        log.Printf("未知错误: %v", err)
    }
    return
}
```

## 更新日志

### v2.2.0
- 🚀 新增智能字体选择功能
- 🔧 修复 `pdfcpu: font SimSun not available` 错误
- ✨ 支持跨平台字体自动检测
- ✨ 新增字体验证和推荐功能
- 🎯 根据文本内容自动推荐合适字体
- 📝 完善字体问题故障排除文档
- 🧪 新增字体相关单元测试

### v2.1.0
- ✨ 新增重复图章功能
- ✨ 支持图章交错排列效果
- ✨ 新增自定义重复间距配置
- ✨ 支持从任意位置开始重复
- 🔧 优化图章位置计算算法
- 📝 完善重复图章使用文档和示例

### v2.0.0
- ✨ 新增图章模式支持
- ✨ 完全支持中文字体和文本
- ✨ 新增多种图章位置选项
- ✨ 新增自定义位置功能
- 🐛 修复字体名称未定义的问题
- 📝 完善测试用例和文档

### v1.0.0
- ✨ 基础重复水印功能
- ✨ 支持基本配置选项
