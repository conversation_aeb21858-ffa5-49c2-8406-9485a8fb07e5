# PDF水印工具

这是一个功能强大的PDF水印工具，支持重复水印和图章两种模式，完全兼容中文字体。

## 功能特性

### 🎯 核心功能
- **重复水印模式**: 在整个页面重复显示水印文本
- **图章模式**: 在指定位置添加单个水印图章
- **中文支持**: 完全支持中文字体和文本
- **灵活配置**: 支持字体、颜色、透明度、旋转角度等多种参数

### 🌟 图章位置选项
- 居中 (Center)
- 左上角 (TopLeft)
- 右上角 (TopRight)
- 左下角 (BottomLeft)
- 右下角 (BottomRight)
- 自定义位置 (Custom)

## 快速开始

### 基本用法

```go
import "phoenix-zhong-yi-testing/service/saas/utils/watermark"

// 打开PDF文件
file, err := os.Open("document.pdf")
if err != nil {
    log.Fatal(err)
}
defer file.Close()

// 配置水印选项
opts := watermark.WatermarkOptions{
    WatermarkTxt: "机密文件",
    FontSize:     48,
    Type:         watermark.WatermarkTypeStamp,
    StampPosition: watermark.StampPositionCenter,
}

// 添加水印
result, err := watermark.AddWatermark(file, opts)
if err != nil {
    log.Fatal(err)
}
```

### 配置选项详解

```go
type WatermarkOptions struct {
    WatermarkTxt  string        // 水印文本
    FontSize      int           // 字号（pt）
    FontName      string        // 字体名称，支持中文字体
    ColorR        float64       // 颜色R (0-255 或 0-1)
    ColorG        float64       // 颜色G (0-255 或 0-1)
    ColorB        float64       // 颜色B (0-255 或 0-1)
    Opacity       float64       // 透明度 (0-1)
    Rotation      float64       // 旋转角度 (-180~180)
    XOffset       int           // 水平偏移
    YOffset       int           // 垂直偏移
    Type          WatermarkType // 水印类型
    StampPosition StampPosition // 图章位置
    StampX        float64       // 自定义X坐标
    StampY        float64       // 自定义Y坐标
}
```

## 使用示例

### 1. 中文图章（居中）

```go
opts := watermark.WatermarkOptions{
    WatermarkTxt:  "机密文件",
    FontSize:      48,
    FontName:      watermark.DefaultFontName,
    ColorR:        255, // 红色
    ColorG:        0,
    ColorB:        0,
    Opacity:       0.8,
    Type:          watermark.WatermarkTypeStamp,
    StampPosition: watermark.StampPositionCenter,
}
```

### 2. 英文图章（右下角，旋转）

```go
opts := watermark.WatermarkOptions{
    WatermarkTxt:  "CONFIDENTIAL",
    FontSize:      36,
    FontName:      watermark.AlternateFontName,
    ColorR:        0, // 蓝色
    ColorG:        0,
    ColorB:        255,
    Opacity:       0.6,
    Rotation:      -45, // 逆时针45度
    Type:          watermark.WatermarkTypeStamp,
    StampPosition: watermark.StampPositionBottomRight,
}
```

### 3. 自定义位置图章

```go
opts := watermark.WatermarkOptions{
    WatermarkTxt:  "审核通过",
    FontSize:      32,
    FontName:      watermark.DefaultFontName,
    ColorR:        0, // 绿色
    ColorG:        128,
    ColorB:        0,
    Opacity:       0.9,
    Rotation:      15,
    Type:          watermark.WatermarkTypeStamp,
    StampPosition: watermark.StampPositionCustom,
    StampX:        200, // 自定义X坐标
    StampY:        300, // 自定义Y坐标
}
```

### 4. 重复中文水印

```go
opts := watermark.WatermarkOptions{
    WatermarkTxt: "凤凰科技有限公司",
    FontSize:     20,
    FontName:     watermark.DefaultFontName,
    ColorR:       128, // 灰色
    ColorG:       128,
    ColorB:       128,
    Opacity:      0.3,
    Rotation:     -30,
    Type:         watermark.WatermarkTypeRepeated,
    XOffset:      50, // 水平间距
    YOffset:      30, // 垂直间距
}
```

## 字体支持

### 内置字体常量
- `DefaultFontName`: "SimSun" - 支持中文的默认字体
- `AlternateFontName`: "Arial" - 英文备用字体

### 自定义字体
您可以指定任何系统支持的字体名称：

```go
opts.FontName = "Microsoft YaHei" // 微软雅黑
opts.FontName = "KaiTi"          // 楷体
opts.FontName = "Times New Roman" // 英文字体
```

## 颜色配置

支持两种颜色格式：

### RGB值 (0-255)
```go
opts.ColorR = 255 // 红色分量
opts.ColorG = 128 // 绿色分量
opts.ColorB = 0   // 蓝色分量
```

### 标准化值 (0-1)
```go
opts.ColorR = 1.0 // 红色分量
opts.ColorG = 0.5 // 绿色分量
opts.ColorB = 0.0 // 蓝色分量
```

## 测试

运行测试以验证功能：

```bash
# 运行所有测试
go test -v

# 运行特定测试
go test -v -run TestAddPDFStamp

# 查看测试覆盖率
go test -cover
```

## 注意事项

1. **字体兼容性**: 确保系统安装了指定的字体，特别是中文字体
2. **坐标系统**: 使用PDF标准坐标系（左下角为原点）
3. **文件格式**: 目前仅支持PDF格式
4. **性能考虑**: 大文件处理时建议适当调整参数以平衡质量和性能

## 错误处理

```go
result, err := watermark.AddWatermark(file, opts)
if err != nil {
    switch {
    case strings.Contains(err.Error(), "font"):
        log.Println("字体相关错误，请检查字体设置")
    case strings.Contains(err.Error(), "page"):
        log.Println("页面处理错误，请检查PDF文件")
    default:
        log.Printf("未知错误: %v", err)
    }
    return
}
```

## 更新日志

### v2.0.0
- ✨ 新增图章模式支持
- ✨ 完全支持中文字体和文本
- ✨ 新增多种图章位置选项
- ✨ 新增自定义位置功能
- 🐛 修复字体名称未定义的问题
- 📝 完善测试用例和文档

### v1.0.0
- ✨ 基础重复水印功能
- ✨ 支持基本配置选项
