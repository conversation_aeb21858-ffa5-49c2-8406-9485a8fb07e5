# PDF水印工具 - 字体初始化版本

这是一个功能强大的PDF水印工具，使用 `sync.Once` 确保字体只初始化一次，完美解决中文字体兼容性问题。

## 🚀 核心特性

- **一次性字体初始化**: 使用 `sync.Once` 确保字体配置只执行一次
- **全局配置管理**: `Configuration` 对象全局唯一，避免重复初始化错误
- **自动字体检测**: 根据操作系统自动选择合适的中文字体
- **自定义字体支持**: 支持指定字体目录和字体文件
- **完全兼容中文**: 解决 `pdfcpu: font SimSun not available` 等字体问题
- **线程安全**: 多协程环境下安全使用
- **状态监控**: 提供配置状态检查和错误监控功能

## 📦 快速开始

### 基本用法

```go
import "phoenix-zhong-yi-testing/service/saas/utils/watermark"

// 方法1：自动初始化（推荐）
opts := watermark.WatermarkOptions{
    WatermarkTxt: "机密文件",
    FontSize:     32,
    ColorR:       255,
    ColorG:       0,
    ColorB:       0,
    Opacity:      0.8,
}

result, err := watermark.AddWatermark(file, opts)
```

### 自定义字体配置

```go
// 方法2：自定义字体配置
fontConfig := &watermark.FontConfig{
    FontDir:  "/System/Library/Fonts",
    FontPath: "/System/Library/Fonts/STHeiti Medium.ttc",
    FontName: "STHeiti",
}

opts := watermark.WatermarkOptions{
    WatermarkTxt: "机密文件",
    FontSize:     32,
    FontConfig:   fontConfig,
    ColorR:       255,
    ColorG:       0,
    ColorB:       0,
    Opacity:      0.8,
}

result, err := watermark.AddWatermark(file, opts)
```

## 🔧 字体初始化API

### 自动初始化

```go
// 自动检测并初始化系统字体和全局配置
err := watermark.AutoInitFont()
if err != nil {
    log.Printf("字体初始化警告: %v", err)
}

// 确保配置已初始化（推荐）
err := watermark.EnsureConfigurationInitialized()
if err != nil {
    log.Printf("配置初始化警告: %v", err)
}
```

### 手动初始化

```go
// 手动指定字体配置
config := watermark.FontConfig{
    FontDir:  "/path/to/fonts",
    FontPath: "/path/to/fonts/font.ttf",
    FontName: "CustomFont",
}

err := watermark.InitFont(config)
if err != nil {
    log.Printf("字体初始化失败: %v", err)
}
```

### 获取系统字体配置

```go
// 获取当前系统的默认字体配置
config := watermark.GetSystemFontConfig()
fmt.Printf("系统字体: %s\n", config.FontName)
fmt.Printf("字体路径: %s\n", config.FontPath)
```

### 全局配置管理

```go
// 检查配置是否已初始化
if watermark.IsConfigurationInitialized() {
    fmt.Println("配置已初始化")
}

// 获取全局配置对象
config := watermark.GetGlobalConfiguration()

// 获取配置状态信息
status := watermark.GetConfigurationStatus()
fmt.Printf("配置状态: %+v\n", status)

// 重置配置（仅测试环境）
watermark.ResetConfiguration()
```

## 📋 配置选项

### WatermarkOptions

```go
type WatermarkOptions struct {
    WatermarkTxt string      // 水印文本
    FontSize     int         // 字号（pt）
    FontName     string      // 字体名称
    FontConfig   *FontConfig // 字体配置（可选）
    ColorR       float64     // 颜色R (0-255)
    ColorG       float64     // 颜色G (0-255)
    ColorB       float64     // 颜色B (0-255)
    Opacity      float64     // 透明度 (0-1)
    Rotation     float64     // 旋转角度 (-180~180)
    XOffset      int         // 水平偏移
    YOffset      int         // 垂直偏移
}
```

### FontConfig

```go
type FontConfig struct {
    FontDir  string // 字体目录路径
    FontPath string // 字体文件路径
    FontName string // 字体名称
}
```

## 🌍 跨平台支持

### macOS
```go
config := watermark.FontConfig{
    FontDir:  "/System/Library/Fonts",
    FontPath: "/System/Library/Fonts/STHeiti Medium.ttc",
    FontName: "STHeiti",
}
```

### Windows
```go
config := watermark.FontConfig{
    FontDir:  "C:\\Windows\\Fonts",
    FontPath: "C:\\Windows\\Fonts\\simsun.ttc",
    FontName: "SimSun",
}
```

### Linux
```go
config := watermark.FontConfig{
    FontDir:  "/usr/share/fonts/truetype",
    FontPath: "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
    FontName: "WenQuanYi Micro Hei",
}
```

## 💡 使用场景

### 企业文档管理
```go
opts := watermark.WatermarkOptions{
    WatermarkTxt: "凤凰科技有限公司",
    FontSize:     24,
    ColorR:       128,
    ColorG:       128,
    ColorB:       128,
    Opacity:      0.3,
}
```

### 机密文档标识
```go
opts := watermark.WatermarkOptions{
    WatermarkTxt: "机密",
    FontSize:     48,
    ColorR:       255,
    ColorG:       0,
    ColorB:       0,
    Opacity:      0.8,
}
```

### 审核状态标记
```go
opts := watermark.WatermarkOptions{
    WatermarkTxt: "已审核",
    FontSize:     36,
    ColorR:       0,
    ColorG:       128,
    ColorB:       0,
    Opacity:      0.7,
}
```

## 🔍 故障排除

### 常见问题

**Q: 出现 `pdfcpu: font SimSun not available` 错误**
A: 使用自动字体初始化或指定系统存在的字体路径

```go
// 解决方案1：自动初始化
err := watermark.AutoInitFont()

// 解决方案2：指定字体路径
config := watermark.FontConfig{
    FontPath: "/System/Library/Fonts/STHeiti Medium.ttc",
    FontName: "STHeiti",
}
err := watermark.InitFont(config)
```

**Q: 字体初始化失败**
A: 检查字体文件路径是否存在，或使用系统默认字体

```go
// 检查字体文件
if _, err := os.Stat("/path/to/font.ttf"); err != nil {
    log.Printf("字体文件不存在: %v", err)
}

// 使用系统配置
config := watermark.GetSystemFontConfig()
```

**Q: 重复初始化导致错误**
A: 使用全局配置管理，避免重复创建 Configuration 对象

```go
// 错误做法：每次都创建新的配置
// config := model.NewDefaultConfiguration() // 会导致重复初始化错误

// 正确做法：使用全局配置
config := watermark.GetGlobalConfiguration()

// 或者确保配置已初始化
err := watermark.EnsureConfigurationInitialized()
```

## 🧪 测试

```bash
# 运行所有测试
go test -v

# 运行字体初始化测试
go test -v -run TestFontInitialization

# 运行水印功能测试
go test -v -run TestWatermarkWithFontInit
```

## 📝 更新日志

### v3.1.0
- 🚀 新增全局 Configuration 对象管理
- 🔧 修复重复初始化导致的错误
- ✨ 新增配置状态监控功能
- ✨ 支持配置重置（测试环境）
- 📝 完善全局配置管理文档
- 🧪 新增全局配置测试用例

### v3.0.0
- ✨ 新增 sync.Once 字体初始化机制
- 🚀 完美解决中文字体兼容性问题
- ✨ 支持自动和手动字体配置
- 🔧 优化跨平台字体检测
- 📝 完善文档和示例代码
- 🧪 新增完整的测试用例

## 📄 许可证

MIT License

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

---

**注意**: 使用 `sync.Once` 确保字体配置在整个应用生命周期中只初始化一次，提高性能并避免重复配置。
