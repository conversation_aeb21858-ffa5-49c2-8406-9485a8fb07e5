package main

import (
	"fmt"
	"log"
	"os"

	"phoenix-zhong-yi-testing/service/saas/utils/watermark"
)

// 演示使用 sync.Once 进行字体初始化的示例程序
func main() {
	fmt.Println("=== PDF水印字体初始化演示 ===")

	// 示例1：自动初始化系统字体
	demonstrateAutoInit()

	// 示例2：自定义字体配置
	demonstrateCustomFontConfig()

	// 示例3：多次调用验证 sync.Once 效果
	demonstrateOnceEffect()

	// 示例4：实际使用场景
	demonstrateRealUsage()
}

// demonstrateAutoInit 演示自动字体初始化
func demonstrateAutoInit() {
	fmt.Println("\n--- 自动字体初始化演示 ---")

	// 自动初始化系统字体
	err := watermark.AutoInitFont()
	if err != nil {
		log.Printf("自动初始化失败: %v", err)
	} else {
		fmt.Println("✅ 自动字体初始化成功")
	}

	// 获取系统字体配置信息
	config := watermark.GetSystemFontConfig()
	fmt.Printf("系统字体配置:\n")
	fmt.Printf("  字体目录: %s\n", config.FontDir)
	fmt.Printf("  字体路径: %s\n", config.FontPath)
	fmt.Printf("  字体名称: %s\n", config.FontName)
}

// demonstrateCustomFontConfig 演示自定义字体配置
func demonstrateCustomFontConfig() {
	fmt.Println("\n--- 自定义字体配置演示 ---")

	// 方案1：使用系统字体路径
	config1 := watermark.FontConfig{
		FontDir:  "/System/Library/Fonts",
		FontPath: "/System/Library/Fonts/STHeiti Medium.ttc",
		FontName: "STHeiti",
	}

	err := watermark.InitFont(config1)
	if err != nil {
		log.Printf("自定义字体初始化失败: %v", err)
	} else {
		fmt.Println("✅ 自定义字体初始化成功")
	}

	// 方案2：仅指定字体名称（适用于已安装的字体）
	config2 := watermark.FontConfig{
		FontName: "Arial",
	}

	err = watermark.InitFont(config2)
	if err != nil {
		log.Printf("字体名称设置失败: %v", err)
	} else {
		fmt.Println("✅ 字体名称设置成功")
	}

	// 方案3：自定义字体目录和文件
	customFontDir := "/path/to/custom/fonts"
	customFontPath := "/path/to/custom/fonts/custom.ttf"
	
	config3 := watermark.FontConfig{
		FontDir:  customFontDir,
		FontPath: customFontPath,
		FontName: "CustomFont",
	}

	fmt.Printf("尝试加载自定义字体:\n")
	fmt.Printf("  字体目录: %s\n", config3.FontDir)
	fmt.Printf("  字体文件: %s\n", config3.FontPath)
	fmt.Printf("  字体名称: %s\n", config3.FontName)

	err = watermark.InitFont(config3)
	if err != nil {
		log.Printf("自定义字体加载失败（预期）: %v", err)
	} else {
		fmt.Println("✅ 自定义字体加载成功")
	}
}

// demonstrateOnceEffect 演示 sync.Once 的效果
func demonstrateOnceEffect() {
	fmt.Println("\n--- sync.Once 效果演示 ---")

	// 多次调用字体初始化，验证只执行一次
	configs := []watermark.FontConfig{
		{FontName: "Arial"},
		{FontName: "Times"},
		{FontName: "Helvetica"},
	}

	fmt.Println("多次调用字体初始化...")
	for i, config := range configs {
		fmt.Printf("第 %d 次调用: 字体 %s\n", i+1, config.FontName)
		err := watermark.InitFont(config)
		if err != nil {
			log.Printf("初始化失败: %v", err)
		} else {
			fmt.Printf("  ✅ 调用成功\n")
		}
	}

	fmt.Println("注意: 由于 sync.Once 的作用，实际只有第一次调用会执行初始化逻辑")
}

// demonstrateRealUsage 演示实际使用场景
func demonstrateRealUsage() {
	fmt.Println("\n--- 实际使用场景演示 ---")

	// 场景1：使用默认字体配置
	fmt.Println("场景1: 使用默认字体配置")
	opts1 := watermark.WatermarkOptions{
		WatermarkTxt: "默认字体水印",
		FontSize:     24,
		ColorR:       255,
		ColorG:       0,
		ColorB:       0,
		Opacity:      0.7,
	}
	fmt.Printf("配置: %+v\n", opts1)

	// 场景2：指定字体名称
	fmt.Println("\n场景2: 指定字体名称")
	opts2 := watermark.WatermarkOptions{
		WatermarkTxt: "指定字体水印",
		FontSize:     28,
		FontName:     "Arial",
		ColorR:       0,
		ColorG:       128,
		ColorB:       0,
		Opacity:      0.8,
	}
	fmt.Printf("配置: %+v\n", opts2)

	// 场景3：使用自定义字体配置
	fmt.Println("\n场景3: 使用自定义字体配置")
	fontConfig := &watermark.FontConfig{
		FontDir:  "/System/Library/Fonts",
		FontPath: "/System/Library/Fonts/STHeiti Medium.ttc",
		FontName: "STHeiti",
	}

	opts3 := watermark.WatermarkOptions{
		WatermarkTxt: "自定义字体水印",
		FontSize:     32,
		FontConfig:   fontConfig,
		ColorR:       0,
		ColorG:       0,
		ColorB:       255,
		Opacity:      0.9,
	}
	fmt.Printf("配置: %+v\n", opts3)

	// 场景4：中文水印配置
	fmt.Println("\n场景4: 中文水印配置")
	chineseFontConfig := &watermark.FontConfig{
		FontName: "SimSun", // 或者使用系统检测到的中文字体
	}

	opts4 := watermark.WatermarkOptions{
		WatermarkTxt: "机密文件",
		FontSize:     36,
		FontConfig:   chineseFontConfig,
		ColorR:       255,
		ColorG:       0,
		ColorB:       0,
		Opacity:      0.8,
	}
	fmt.Printf("配置: %+v\n", opts4)

	fmt.Println("\n✅ 所有场景配置完成")
}

// 如果需要实际处理PDF文件，可以取消注释以下函数
/*
func processWithFontInit(filename string) {
	fmt.Printf("\n--- 处理PDF文件: %s ---\n", filename)

	// 1. 首先初始化字体
	fontConfig := watermark.FontConfig{
		FontDir:  "/System/Library/Fonts",
		FontPath: "/System/Library/Fonts/STHeiti Medium.ttc",
		FontName: "STHeiti",
	}

	err := watermark.InitFont(fontConfig)
	if err != nil {
		log.Printf("字体初始化失败: %v", err)
		return
	}

	// 2. 打开PDF文件
	file, err := os.Open(filename)
	if err != nil {
		log.Printf("打开文件失败: %v", err)
		return
	}
	defer file.Close()

	// 3. 配置水印选项
	opts := watermark.WatermarkOptions{
		WatermarkTxt: "机密文件",
		FontSize:     32,
		FontName:     "STHeiti", // 使用已初始化的字体
		ColorR:       255,
		ColorG:       0,
		ColorB:       0,
		Opacity:      0.8,
	}

	// 4. 添加水印
	result, err := watermark.AddWatermark(file, opts)
	if err != nil {
		log.Printf("添加水印失败: %v", err)
		return
	}

	// 5. 保存结果
	// ... 保存逻辑

	fmt.Println("✅ PDF水印处理完成")
}
*/

// 演示错误处理
func demonstrateErrorHandling() {
	fmt.Println("\n--- 错误处理演示 ---")

	// 尝试使用不存在的字体目录
	invalidConfig := watermark.FontConfig{
		FontDir:  "/nonexistent/directory",
		FontPath: "/nonexistent/directory/font.ttf",
		FontName: "NonexistentFont",
	}

	err := watermark.InitFont(invalidConfig)
	if err != nil {
		fmt.Printf("❌ 预期的错误: %v\n", err)
	} else {
		fmt.Println("⚠️  意外成功")
	}

	// 即使初始化失败，后续调用仍然可以工作（使用默认配置）
	fmt.Println("尝试使用默认配置...")
	err = watermark.AutoInitFont()
	if err != nil {
		fmt.Printf("默认配置也失败: %v\n", err)
	} else {
		fmt.Println("✅ 默认配置成功")
	}
}
