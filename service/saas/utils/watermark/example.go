package main

import (
	"fmt"
	"log"
	"os"

	"phoenix-zhong-yi-testing/service/saas/utils/watermark"
)

// 演示如何使用水印功能的示例程序
func main() {
	// 示例1：添加中文图章（居中）
	addChineseStampCenter()

	// 示例2：添加英文图章（右下角）
	addEnglishStampBottomRight()

	// 示例3：添加自定义位置图章
	addCustomPositionStamp()

	// 示例4：添加重复中文水印
	addRepeatedChineseWatermark()

	// 示例5：添加多种位置的图章
	addMultiplePositionStamps()
}

// addChineseStampCenter 添加居中的中文图章
func addChineseStampCenter() {
	fmt.Println("正在添加居中的中文图章...")

	file, err := os.Open("test.pdf")
	if err != nil {
		log.Printf("打开文件失败: %v", err)
		return
	}
	defer file.Close()

	opts := watermark.WatermarkOptions{
		WatermarkTxt:  "机密文件",                           // 中文图章文本
		FontSize:      48,                               // 大字号
		FontName:      watermark.DefaultFontName,        // 支持中文的字体
		ColorR:        255,                              // 红色
		ColorG:        0,
		ColorB:        0,
		Opacity:       0.8,                              // 80%透明度
		Rotation:      0,                                // 不旋转
		Type:          watermark.WatermarkTypeStamp,     // 图章模式
		StampPosition: watermark.StampPositionCenter,    // 居中位置
	}

	result, err := watermark.AddWatermark(file, opts)
	if err != nil {
		log.Printf("添加水印失败: %v", err)
		return
	}

	saveResult(result, "example_chinese_stamp_center.pdf")
	fmt.Println("中文图章添加完成！")
}

// addEnglishStampBottomRight 添加右下角的英文图章
func addEnglishStampBottomRight() {
	fmt.Println("正在添加右下角的英文图章...")

	file, err := os.Open("test.pdf")
	if err != nil {
		log.Printf("打开文件失败: %v", err)
		return
	}
	defer file.Close()

	opts := watermark.WatermarkOptions{
		WatermarkTxt:  "CONFIDENTIAL",                      // 英文图章文本
		FontSize:      36,                                  // 中等字号
		FontName:      watermark.AlternateFontName,         // 英文字体
		ColorR:        0,                                   // 蓝色
		ColorG:        0,
		ColorB:        255,
		Opacity:       0.6,                                 // 60%透明度
		Rotation:      -45,                                 // 逆时针45度旋转
		Type:          watermark.WatermarkTypeStamp,        // 图章模式
		StampPosition: watermark.StampPositionBottomRight,  // 右下角位置
	}

	result, err := watermark.AddWatermark(file, opts)
	if err != nil {
		log.Printf("添加水印失败: %v", err)
		return
	}

	saveResult(result, "example_english_stamp_bottom_right.pdf")
	fmt.Println("英文图章添加完成！")
}

// addCustomPositionStamp 添加自定义位置的图章
func addCustomPositionStamp() {
	fmt.Println("正在添加自定义位置的图章...")

	file, err := os.Open("test.pdf")
	if err != nil {
		log.Printf("打开文件失败: %v", err)
		return
	}
	defer file.Close()

	opts := watermark.WatermarkOptions{
		WatermarkTxt:  "审核通过",                           // 中文审核图章
		FontSize:      32,                               // 中等字号
		FontName:      watermark.DefaultFontName,        // 中文字体
		ColorR:        0,                                // 绿色
		ColorG:        128,
		ColorB:        0,
		Opacity:       0.9,                              // 90%透明度
		Rotation:      15,                               // 顺时针15度旋转
		Type:          watermark.WatermarkTypeStamp,     // 图章模式
		StampPosition: watermark.StampPositionCustom,    // 自定义位置
		StampX:        200,                              // X坐标
		StampY:        300,                              // Y坐标
	}

	result, err := watermark.AddWatermark(file, opts)
	if err != nil {
		log.Printf("添加水印失败: %v", err)
		return
	}

	saveResult(result, "example_custom_position_stamp.pdf")
	fmt.Println("自定义位置图章添加完成！")
}

// addRepeatedChineseWatermark 添加重复的中文水印
func addRepeatedChineseWatermark() {
	fmt.Println("正在添加重复的中文水印...")

	file, err := os.Open("test.pdf")
	if err != nil {
		log.Printf("打开文件失败: %v", err)
		return
	}
	defer file.Close()

	opts := watermark.WatermarkOptions{
		WatermarkTxt: "凤凰科技有限公司",                      // 中文公司名称
		FontSize:     20,                               // 小字号
		FontName:     watermark.DefaultFontName,        // 中文字体
		ColorR:       128,                              // 灰色
		ColorG:       128,
		ColorB:       128,
		Opacity:      0.3,                              // 30%透明度
		Rotation:     -30,                              // 逆时针30度旋转
		Type:         watermark.WatermarkTypeRepeated,  // 重复水印模式
		XOffset:      50,                               // 水平间距
		YOffset:      30,                               // 垂直间距
	}

	result, err := watermark.AddWatermark(file, opts)
	if err != nil {
		log.Printf("添加水印失败: %v", err)
		return
	}

	saveResult(result, "example_repeated_chinese_watermark.pdf")
	fmt.Println("重复中文水印添加完成！")
}

// addMultiplePositionStamps 演示在不同位置添加多个图章
func addMultiplePositionStamps() {
	fmt.Println("正在演示多个位置的图章...")

	positions := []struct {
		name     string
		position watermark.StampPosition
		text     string
		filename string
	}{
		{"左上角", watermark.StampPositionTopLeft, "草稿", "example_stamp_top_left.pdf"},
		{"右上角", watermark.StampPositionTopRight, "已审核", "example_stamp_top_right.pdf"},
		{"左下角", watermark.StampPositionBottomLeft, "存档", "example_stamp_bottom_left.pdf"},
	}

	for _, pos := range positions {
		fmt.Printf("添加%s图章...\n", pos.name)

		file, err := os.Open("test.pdf")
		if err != nil {
			log.Printf("打开文件失败: %v", err)
			continue
		}

		opts := watermark.WatermarkOptions{
			WatermarkTxt:  pos.text,
			FontSize:      28,
			FontName:      watermark.DefaultFontName,
			ColorR:        255,
			ColorG:        100,
			ColorB:        0,
			Opacity:       0.7,
			Rotation:      0,
			Type:          watermark.WatermarkTypeStamp,
			StampPosition: pos.position,
		}

		result, err := watermark.AddWatermark(file, opts)
		if err != nil {
			log.Printf("添加%s图章失败: %v", pos.name, err)
			file.Close()
			continue
		}

		saveResult(result, pos.filename)
		file.Close()
		fmt.Printf("%s图章添加完成！\n", pos.name)
	}
}

// saveResult 保存处理结果到文件
func saveResult(result interface{}, filename string) {
	// 这里简化处理，实际使用时需要根据result的类型进行适当的类型转换和保存
	fmt.Printf("结果已保存到: %s\n", filename)
}
