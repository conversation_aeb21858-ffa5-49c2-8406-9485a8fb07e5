# 字体问题快速修复指南

## 🚨 常见错误

### 错误信息：`pdfcpu: font SimSun not available`

这个错误表明系统中没有找到 SimSun 字体。

## ⚡ 快速修复

### 方法1：使用智能字体选择（推荐）

```go
// 修改前（会报错）
opts := watermark.WatermarkOptions{
    WatermarkTxt: "机密文件",
    FontName:     "SimSun", // 可能不存在
}

// 修改后（自动选择）
opts := watermark.WatermarkOptions{
    WatermarkTxt: "机密文件",
    // 不指定 FontName，系统会自动选择
}

// 或者显式使用推荐字体
opts := watermark.WatermarkOptions{
    WatermarkTxt: "机密文件",
    FontName:     watermark.GetRecommendedFont("机密文件"),
}
```

### 方法2：使用系统字体路径

#### macOS 系统
```go
opts.FontName = "/System/Library/Fonts/STHeiti Medium.ttc"
// 或者
opts.FontName = "/System/Library/Fonts/PingFang.ttc"
```

#### Windows 系统
```go
opts.FontName = "C:\\Windows\\Fonts\\simsun.ttc"
// 或者
opts.FontName = "Microsoft YaHei"
```

#### Linux 系统
```go
opts.FontName = "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc"
// 或者
opts.FontName = "WenQuanYi Micro Hei"
```

### 方法3：字体验证和自动修复

```go
// 验证字体并自动修复
opts := watermark.WatermarkOptions{
    WatermarkTxt: "机密文件",
    FontName:     "SimSun", // 可能不存在的字体
}

// 验证并修复
valid, recommended := watermark.ValidateFont(opts.FontName)
if !valid {
    opts.FontName = recommended
    fmt.Printf("字体已自动调整为: %s\n", opts.FontName)
}
```

## 🔧 完整的错误处理示例

```go
package main

import (
    "fmt"
    "log"
    "os"
    "phoenix-zhong-yi-testing/service/saas/utils/watermark"
)

func addWatermarkWithFontFix(filename string) {
    file, err := os.Open(filename)
    if err != nil {
        log.Fatalf("无法打开文件: %v", err)
    }
    defer file.Close()

    // 创建水印配置
    opts := watermark.WatermarkOptions{
        WatermarkTxt:  "机密文件",
        FontSize:      32,
        ColorR:        255,
        ColorG:        0,
        ColorB:        0,
        Opacity:       0.8,
        Type:          watermark.WatermarkTypeStamp,
        StampPosition: watermark.StampPositionCenter,
    }

    // 智能字体选择和验证
    if opts.FontName == "" {
        opts.FontName = watermark.GetRecommendedFont(opts.WatermarkTxt)
        fmt.Printf("自动选择字体: %s\n", opts.FontName)
    }

    valid, recommended := watermark.ValidateFont(opts.FontName)
    if !valid {
        fmt.Printf("字体 %s 不可用，使用推荐字体: %s\n", opts.FontName, recommended)
        opts.FontName = recommended
    }

    // 添加水印
    result, err := watermark.AddWatermark(file, opts)
    if err != nil {
        log.Fatalf("添加水印失败: %v", err)
    }

    // 保存结果
    // ... 保存逻辑
    
    fmt.Printf("✅ 成功使用字体 %s 添加水印\n", opts.FontName)
}
```

## 🎯 最佳实践

### 1. 总是使用智能字体选择
```go
// 好的做法
opts.FontName = watermark.GetRecommendedFont(opts.WatermarkTxt)

// 避免硬编码字体名称
// opts.FontName = "SimSun" // 可能在某些系统上不存在
```

### 2. 验证字体可用性
```go
valid, recommended := watermark.ValidateFont(opts.FontName)
if !valid {
    opts.FontName = recommended
}
```

### 3. 使用字体文件路径（高可靠性）
```go
// 对于生产环境，推荐使用绝对路径
switch runtime.GOOS {
case "darwin":
    opts.FontName = "/System/Library/Fonts/STHeiti Medium.ttc"
case "windows":
    opts.FontName = "C:\\Windows\\Fonts\\simsun.ttc"
case "linux":
    opts.FontName = "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc"
}
```

### 4. 错误处理
```go
result, err := watermark.AddWatermark(file, opts)
if err != nil {
    if strings.Contains(err.Error(), "font") {
        // 字体相关错误，尝试使用备用字体
        opts.FontName = watermark.AlternateFontName
        result, err = watermark.AddWatermark(file, opts)
    }
    if err != nil {
        log.Fatalf("添加水印失败: %v", err)
    }
}
```

## 🧪 测试字体可用性

创建一个简单的测试程序：

```go
package main

import (
    "fmt"
    "phoenix-zhong-yi-testing/service/saas/utils/watermark"
)

func main() {
    // 测试不同字体
    fonts := []string{
        "SimSun",
        "Arial",
        "/System/Library/Fonts/STHeiti Medium.ttc",
        "Microsoft YaHei",
        "WenQuanYi Micro Hei",
    }

    fmt.Println("字体可用性测试:")
    for _, font := range fonts {
        valid, recommended := watermark.ValidateFont(font)
        status := "❌"
        if valid {
            status = "✅"
        }
        fmt.Printf("%s %s -> 推荐: %s\n", status, font, recommended)
    }

    // 测试文本推荐
    fmt.Println("\n文本字体推荐:")
    texts := []string{"机密", "CONFIDENTIAL", "机密 CONFIDENTIAL"}
    for _, text := range texts {
        recommended := watermark.GetRecommendedFont(text)
        fmt.Printf("'%s' -> %s\n", text, recommended)
    }
}
```

## 📋 检查清单

在使用水印功能前，请确认：

- [ ] 不要硬编码字体名称
- [ ] 使用 `GetRecommendedFont()` 自动选择字体
- [ ] 使用 `ValidateFont()` 验证字体可用性
- [ ] 为不同操作系统准备备用方案
- [ ] 添加适当的错误处理
- [ ] 测试在目标环境中的字体可用性

## 🆘 仍然遇到问题？

如果按照上述方法仍然遇到字体问题：

1. **检查系统字体**: 确认系统中安装了中文字体
2. **使用绝对路径**: 直接指定字体文件的完整路径
3. **降级到英文字体**: 临时使用 `Arial` 等通用字体
4. **查看详细错误**: 检查完整的错误信息
5. **联系支持**: 提供操作系统信息和错误详情

记住：新版本的智能字体选择功能会自动处理大部分字体兼容性问题！
