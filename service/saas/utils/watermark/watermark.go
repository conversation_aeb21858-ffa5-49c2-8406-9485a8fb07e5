package watermark

import (
	"bytes"
	"fmt"
	"io"
	"log"
	"os"
	"runtime"
	"sync"

	"github.com/pdfcpu/pdfcpu/pkg/api"
	"github.com/pdfcpu/pdfcpu/pkg/font"
	"github.com/pdfcpu/pdfcpu/pkg/pdfcpu/color"
	"github.com/pdfcpu/pdfcpu/pkg/pdfcpu/model"
	"github.com/pdfcpu/pdfcpu/pkg/pdfcpu/types"
)

var (
	// fontInitOnce 确保字体只初始化一次
	fontInitOnce sync.Once
	// fontInitError 保存字体初始化错误
	fontInitError error
	// defaultFontName 默认字体名称
	defaultFontName = "SimSun"
	// globalConfiguration 全局配置对象，避免重复初始化
	globalConfiguration *model.Configuration
)

// FontConfig 字体配置结构
type FontConfig struct {
	FontDir  string // 字体目录路径
	FontPath string // 字体文件路径
	FontName string // 字体名称
}

// WatermarkOptions 定义了水印的所有可配置参数
type WatermarkOptions struct {
	WatermarkTxt string      // 水印文本
	FontSize     int         // 字号（pt）
	FontName     string      // 字体名称
	FontConfig   *FontConfig // 字体配置（可选）
	ColorR       float64     // 颜色R
	ColorG       float64     // 颜色G
	ColorB       float64     // 颜色B
	Opacity      float64     // 透明度 0~1
	Rotation     float64     // 旋转角度 -180~180
	XOffset      int         // 水平偏移
	YOffset      int         // 垂直偏移
}

// InitFont 初始化字体配置
// 使用 sync.Once 确保只初始化一次
func InitFont(config FontConfig) error {
	fontInitOnce.Do(func() {
		fontInitError = initFontOnce(config)
	})
	return fontInitError
}

// initFontOnce 实际的字体初始化函数
func initFontOnce(config FontConfig) error {
	// 初始化全局配置对象（只初始化一次）
	if globalConfiguration == nil {
		globalConfiguration = model.NewDefaultConfiguration()
		log.Printf("初始化全局PDF配置对象")
	}

	// 设置字体目录
	if config.FontDir != "" {
		font.UserFontDir = config.FontDir

		// 加载用户字体目录
		err := font.LoadUserFonts()
		if err != nil {
			return fmt.Errorf("加载用户字体目录失败: %v", err)
		}

		log.Printf("成功加载字体目录: %s", config.FontDir)
	}

	// 安装指定的字体文件
	if config.FontPath != "" && config.FontDir != "" {
		err := font.InstallTrueTypeFont(config.FontDir, config.FontPath)
		if err != nil {
			return fmt.Errorf("安装字体文件失败: %v", err)
		}

		log.Printf("成功安装字体文件: %s", config.FontPath)
	}

	// 设置默认字体名称
	if config.FontName != "" {
		defaultFontName = config.FontName
		log.Printf("设置默认字体: %s", config.FontName)
	}

	return nil
}

// GetSystemFontConfig 获取系统默认字体配置
func GetSystemFontConfig() FontConfig {
	switch runtime.GOOS {
	case "darwin": // macOS
		return FontConfig{
			FontDir:  "/System/Library/Fonts",
			FontPath: "/System/Library/Fonts/Supplemental/Songti.ttc",
			FontName: "Songti",
		}
	case "windows":
		return FontConfig{
			FontDir:  "C:\\Windows\\Fonts",
			FontPath: "C:\\Windows\\Fonts\\simsun.ttc",
			FontName: "SimSun",
		}
	case "linux":
		return FontConfig{
			FontDir:  "/usr/share/fonts/truetype",
			FontPath: "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
			FontName: "WenQuanYi Micro Hei",
		}
	default:
		return FontConfig{
			FontName: "Arial", // 备用字体
		}
	}
}

// AutoInitFont 自动初始化系统字体
func AutoInitFont() error {
	config := GetSystemFontConfig()

	// 检查字体文件是否存在
	if config.FontPath != "" {
		if _, err := os.Stat(config.FontPath); err != nil {
			// 字体文件不存在，使用默认配置
			log.Printf("字体文件不存在: %s，使用默认配置", config.FontPath)
			config = FontConfig{FontName: "Arial"}
		}
	}

	return InitFont(config)
}

// GetGlobalConfiguration 获取全局配置对象
// 如果尚未初始化，则先进行自动初始化
func GetGlobalConfiguration() *model.Configuration {
	if globalConfiguration == nil {
		// 如果全局配置未初始化，先进行自动初始化
		AutoInitFont()
	}
	return globalConfiguration
}

// EnsureConfigurationInitialized 确保配置已初始化
// 这是一个安全的初始化检查函数
func EnsureConfigurationInitialized() error {
	if globalConfiguration == nil {
		return AutoInitFont()
	}
	return nil
}

// ResetConfiguration 重置全局配置（仅用于测试）
// 注意：这会重置 sync.Once，应该谨慎使用
func ResetConfiguration() {
	globalConfiguration = nil
	fontInitOnce = sync.Once{}
	fontInitError = nil
	log.Printf("全局配置已重置")
}

// IsConfigurationInitialized 检查配置是否已初始化
func IsConfigurationInitialized() bool {
	return globalConfiguration != nil
}

// GetConfigurationStatus 获取配置状态信息
func GetConfigurationStatus() map[string]interface{} {
	return map[string]interface{}{
		"initialized":    globalConfiguration != nil,
		"default_font":   defaultFontName,
		"init_error":     fontInitError,
		"has_init_error": fontInitError != nil,
	}
}

// AddWatermark 向文件流添加水印
// 自动初始化字体配置和全局Configuration对象
func AddWatermark(originalFile io.ReadSeeker, opts WatermarkOptions) (io.Reader, error) {
	// 确保全局配置已初始化
	err := EnsureConfigurationInitialized()
	if err != nil {
		log.Printf("配置初始化警告: %v", err)
	}

	// 如果提供了字体配置，使用自定义配置初始化
	if opts.FontConfig != nil {
		err := InitFont(*opts.FontConfig)
		if err != nil {
			log.Printf("字体初始化警告: %v", err)
		}
	} else {
		// 否则使用自动初始化
		err := AutoInitFont()
		if err != nil {
			log.Printf("自动字体初始化警告: %v", err)
		}
	}

	return addPDFWatermark(originalFile, opts)
}

// 添加水印
func addPDFWatermark(reader io.ReadSeeker, config WatermarkOptions) (io.ReadCloser, error) {
	// --- 最终的、经过完整调试的完美实现 ---

	// 1. 为零值参数设置默认值
	if config.FontSize == 0 {
		config.FontSize = 16
	}
	if config.FontName == "" {
		config.FontName = defaultFontName
	}
	if config.ColorR == 0 && config.ColorG == 0 && config.ColorB == 0 {
		config.ColorR = 128
		config.ColorG = 128
		config.ColorB = 128
	}
	if config.Opacity == 0 {
		config.Opacity = 0.2
	}
	if config.Rotation == 0 {
		config.Rotation = -15
	}

	// 2. 获取页面尺寸。
	dims, err := api.PageDims(reader, nil)
	if err != nil {
		return nil, err
	}
	if _, err := reader.Seek(0, io.SeekStart); err != nil {
		return nil, err
	}

	// 3. 定义布局和视觉参数。
	watermarks := make(map[int][]*model.Watermark)
	// 根据内容和间距计算总块大小
	// 0.6 是一个估算的字符平均宽度系数
	blockWidth := float64(config.FontSize*len(config.WatermarkTxt))*0.6 + float64(config.XOffset)
	blockHeight := float64(config.FontSize) + float64(config.YOffset)

	// 4. 遍历页面，生成水印。
	for i, dim := range dims {
		var pageWatermarks []*model.Watermark
		cols := int(dim.Width/blockWidth) + 2
		rows := int(dim.Height/blockHeight) + 2

		for r := 0; r < rows; r++ {
			for c := 0; c < cols; c++ {
				// 为每个水印创建独立实例。
				wm := model.DefaultWatermarkConfig()
				wm.TextString = config.WatermarkTxt
				wm.FontSize = config.FontSize
				wm.FontName = config.FontName

				// Normalize color
				red, green, blue := config.ColorR, config.ColorG, config.ColorB
				if red > 1 || green > 1 || blue > 1 {
					red /= 255
					green /= 255
					blue /= 255
				}
				wm.Color = color.SimpleColor{R: float32(red), G: float32(green), B: float32(blue)}
				wm.Rotation = config.Rotation
				wm.Opacity = config.Opacity
				wm.OnTop = true
				wm.ScaleAbs = true
				wm.UserRotOrDiagonal = true // 确保自定义旋转生效
				wm.Diagonal = model.NoDiagonal

				// 5. 使用被证明可行的 Pos/Dx/Dy 系统，并采用最简单、最安全的坐标计算。
				wm.Pos = types.BottomLeft // 所有水印都基于左下角。
				offsetX := float64(c) * blockWidth
				// 为奇数行添加水平偏移以实现交错。
				if r%2 == 1 {
					offsetX += blockWidth / 2
				}
				wm.Dx = offsetX
				wm.Dy = float64(r) * blockHeight

				pageWatermarks = append(pageWatermarks, wm)
			}
		}
		watermarks[i+1] = pageWatermarks
	}

	// 6. 执行操作。
	outBuffer := new(bytes.Buffer)
	err = api.AddWatermarksSliceMap(reader, outBuffer, watermarks, nil)
	if err != nil {
		return nil, err
	}

	return io.NopCloser(outBuffer), nil
}
