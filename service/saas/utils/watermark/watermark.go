package watermark

import (
	"bytes"
	"io"

	"github.com/pdfcpu/pdfcpu/pkg/api"
	"github.com/pdfcpu/pdfcpu/pkg/pdfcpu/color"
	"github.com/pdfcpu/pdfcpu/pkg/pdfcpu/model"
	"github.com/pdfcpu/pdfcpu/pkg/pdfcpu/types"
)

// WatermarkType 定义水印类型
type WatermarkType int

const (
	// WatermarkTypeRepeated 重复水印模式（默认）
	WatermarkTypeRepeated WatermarkType = iota
	// WatermarkTypeStamp 图章模式
	WatermarkTypeStamp
)

// StampPosition 定义图章位置
type StampPosition int

const (
	// StampPositionCenter 居中
	StampPositionCenter StampPosition = iota
	// StampPositionTopLeft 左上角
	StampPositionTopLeft
	// StampPositionTopRight 右上角
	StampPositionTopRight
	// StampPositionBottomLeft 左下角
	StampPositionBottomLeft
	// StampPositionBottomRight 右下角
	StampPositionBottomRight
	// StampPositionCustom 自定义位置
	StampPositionCustom
)

// 支持中文的字体名称常量
const (
	// DefaultFontName 默认字体（支持中文）
	DefaultFontName = "SimSun"
	// AlternateFontName 备用字体
	AlternateFontName = "Arial"
)

// WatermarkOptions 定义了水印的所有可配置参数
type WatermarkOptions struct {
	WatermarkTxt  string        // 水印文本
	FontSize      int           // 字号（pt）
	FontName      string        // 字体名称，支持中文字体
	ColorR        float64       // 颜色R
	ColorG        float64       // 颜色G
	ColorB        float64       // 颜色B
	Opacity       float64       // 透明度 0~1
	Rotation      float64       // 旋转角度 -180~180
	XOffset       int           // 水平偏移
	YOffset       int           // 垂直偏移
	Type          WatermarkType // 水印类型：重复模式或图章模式
	StampPosition StampPosition // 图章位置（仅在图章模式下有效）
	StampX        float64       // 自定义图章X坐标（仅在StampPositionCustom时有效）
	StampY        float64       // 自定义图章Y坐标（仅在StampPositionCustom时有效）
	IsRepeated    bool          // 是否重复显示（图章模式下有效）
	RepeatSpaceX  float64       // 重复图章的水平间距
	RepeatSpaceY  float64       // 重复图章的垂直间距
}

// AddWatermark 向文件流添加水印
// 支持重复水印和图章两种模式，兼容中文字体
func AddWatermark(originalFile io.ReadSeeker, opts WatermarkOptions) (io.Reader, error) {
	// 根据水印类型选择不同的处理方式
	switch opts.Type {
	case WatermarkTypeStamp:
		return addPDFStamp(originalFile, opts)
	default:
		return addPDFWatermark(originalFile, opts)
	}
}

// setDefaultOptions 为水印选项设置默认值
func setDefaultOptions(config *WatermarkOptions) {
	if config.FontSize == 0 {
		config.FontSize = 16
	}
	if config.FontName == "" {
		config.FontName = DefaultFontName
	}
	if config.ColorR == 0 && config.ColorG == 0 && config.ColorB == 0 {
		config.ColorR = 128
		config.ColorG = 128
		config.ColorB = 128
	}
	if config.Opacity == 0 {
		config.Opacity = 0.2
	}
	if config.Rotation == 0 {
		config.Rotation = -15
	}
	// 为图章重复模式设置默认间距
	if config.Type == WatermarkTypeStamp && config.IsRepeated {
		if config.RepeatSpaceX == 0 {
			config.RepeatSpaceX = float64(config.FontSize*len(config.WatermarkTxt))*0.8 + 100
		}
		if config.RepeatSpaceY == 0 {
			config.RepeatSpaceY = float64(config.FontSize) + 80
		}
	}
}

// 添加重复水印
func addPDFWatermark(reader io.ReadSeeker, config WatermarkOptions) (io.ReadCloser, error) {
	// 设置默认值
	setDefaultOptions(&config)

	// 2. 获取页面尺寸。
	dims, err := api.PageDims(reader, nil)
	if err != nil {
		return nil, err
	}
	if _, err := reader.Seek(0, io.SeekStart); err != nil {
		return nil, err
	}

	// 3. 定义布局和视觉参数。
	watermarks := make(map[int][]*model.Watermark)
	// 根据内容和间距计算总块大小
	// 0.6 是一个估算的字符平均宽度系数
	blockWidth := float64(config.FontSize*len(config.WatermarkTxt))*0.6 + float64(config.XOffset)
	blockHeight := float64(config.FontSize) + float64(config.YOffset)

	// 4. 遍历页面，生成水印。
	for i, dim := range dims {
		var pageWatermarks []*model.Watermark
		cols := int(dim.Width/blockWidth) + 2
		rows := int(dim.Height/blockHeight) + 2

		for r := 0; r < rows; r++ {
			for c := 0; c < cols; c++ {
				// 为每个水印创建独立实例。
				wm := model.DefaultWatermarkConfig()
				wm.TextString = config.WatermarkTxt
				wm.FontSize = config.FontSize
				wm.FontName = "/System/Library/Fonts/STHeiti Medium.ttc"

				// Normalize color
				red, green, blue := config.ColorR, config.ColorG, config.ColorB
				if red > 1 || green > 1 || blue > 1 {
					red /= 255
					green /= 255
					blue /= 255
				}
				wm.Color = color.SimpleColor{R: float32(red), G: float32(green), B: float32(blue)}
				wm.Rotation = config.Rotation
				wm.Opacity = config.Opacity
				wm.OnTop = true
				wm.ScaleAbs = true
				wm.UserRotOrDiagonal = true // 确保自定义旋转生效
				wm.Diagonal = model.NoDiagonal

				// 5. 使用被证明可行的 Pos/Dx/Dy 系统，并采用最简单、最安全的坐标计算。
				wm.Pos = types.BottomLeft // 所有水印都基于左下角。
				offsetX := float64(c) * blockWidth
				// 为奇数行添加水平偏移以实现交错。
				if r%2 == 1 {
					offsetX += blockWidth / 2
				}
				wm.Dx = offsetX
				wm.Dy = float64(r) * blockHeight

				pageWatermarks = append(pageWatermarks, wm)
			}
		}
		watermarks[i+1] = pageWatermarks
	}

	// 6. 执行操作。
	outBuffer := new(bytes.Buffer)
	err = api.AddWatermarksSliceMap(reader, outBuffer, watermarks, nil)
	if err != nil {
		return nil, err
	}

	return io.NopCloser(outBuffer), nil
}

// addPDFStamp 添加图章模式的水印
// 支持单个图章或重复图章模式，兼容中文字体
func addPDFStamp(reader io.ReadSeeker, config WatermarkOptions) (io.ReadCloser, error) {
	// 设置默认值
	setDefaultOptions(&config)

	// 获取页面尺寸
	dims, err := api.PageDims(reader, nil)
	if err != nil {
		return nil, err
	}
	if _, err := reader.Seek(0, io.SeekStart); err != nil {
		return nil, err
	}

	// 创建水印配置
	watermarks := make(map[int][]*model.Watermark)

	// 遍历每一页，添加图章
	for i, dim := range dims {
		var pageWatermarks []*model.Watermark

		if config.IsRepeated {
			// 重复图章模式
			pageWatermarks = createRepeatedStamps(dim, config)
		} else {
			// 单个图章模式
			pageWatermarks = createSingleStamp(dim, config)
		}

		watermarks[i+1] = pageWatermarks
	}

	// 执行水印添加操作
	outBuffer := new(bytes.Buffer)
	err = api.AddWatermarksSliceMap(reader, outBuffer, watermarks, nil)
	if err != nil {
		return nil, err
	}

	return io.NopCloser(outBuffer), nil
}

// createSingleStamp 创建单个图章
func createSingleStamp(dim types.Dim, config WatermarkOptions) []*model.Watermark {
	wm := createWatermarkConfig(config)

	// 根据图章位置计算坐标
	x, y := calculateStampPosition(config.StampPosition, dim.Width, dim.Height, config)
	wm.Pos = types.BottomLeft
	wm.Dx = x
	wm.Dy = y

	return []*model.Watermark{wm}
}

// createRepeatedStamps 创建重复图章
func createRepeatedStamps(dim types.Dim, config WatermarkOptions) []*model.Watermark {
	var pageWatermarks []*model.Watermark

	// 计算重复图章的行列数
	cols := int(dim.Width/config.RepeatSpaceX) + 2
	rows := int(dim.Height/config.RepeatSpaceY) + 2

	// 获取基准位置
	baseX, baseY := calculateStampPosition(config.StampPosition, dim.Width, dim.Height, config)

	for r := 0; r < rows; r++ {
		for c := 0; c < cols; c++ {
			wm := createWatermarkConfig(config)

			// 计算当前图章的位置
			x := baseX + float64(c)*config.RepeatSpaceX
			y := baseY + float64(r)*config.RepeatSpaceY

			// 为奇数行添加水平偏移以实现交错效果
			if r%2 == 1 {
				x += config.RepeatSpaceX / 2
			}

			wm.Pos = types.BottomLeft
			wm.Dx = x
			wm.Dy = y

			pageWatermarks = append(pageWatermarks, wm)
		}
	}

	return pageWatermarks
}

// createWatermarkConfig 创建水印配置
func createWatermarkConfig(config WatermarkOptions) *model.Watermark {
	wm := model.DefaultWatermarkConfig()
	wm.TextString = config.WatermarkTxt
	wm.FontSize = config.FontSize
	wm.FontName = config.FontName

	// 标准化颜色值
	red, green, blue := config.ColorR, config.ColorG, config.ColorB
	if red > 1 || green > 1 || blue > 1 {
		red /= 255
		green /= 255
		blue /= 255
	}
	wm.Color = color.SimpleColor{R: float32(red), G: float32(green), B: float32(blue)}
	wm.Rotation = config.Rotation
	wm.Opacity = config.Opacity
	wm.OnTop = true
	wm.ScaleAbs = true
	wm.UserRotOrDiagonal = true
	wm.Diagonal = model.NoDiagonal

	return wm
}

// calculateStampPosition 根据图章位置枚举计算实际坐标
// 返回基于页面左下角的坐标值
func calculateStampPosition(position StampPosition, pageWidth, pageHeight float64, config WatermarkOptions) (float64, float64) {
	// 估算文本宽度和高度（用于居中计算）
	textWidth := float64(config.FontSize*len(config.WatermarkTxt)) * 0.6
	textHeight := float64(config.FontSize)

	var x, y float64

	switch position {
	case StampPositionCenter:
		// 居中
		x = (pageWidth - textWidth) / 2
		y = (pageHeight - textHeight) / 2
	case StampPositionTopLeft:
		// 左上角
		x = 50 // 留一些边距
		y = pageHeight - textHeight - 50
	case StampPositionTopRight:
		// 右上角
		x = pageWidth - textWidth - 50
		y = pageHeight - textHeight - 50
	case StampPositionBottomLeft:
		// 左下角
		x = 50
		y = 50
	case StampPositionBottomRight:
		// 右下角
		x = pageWidth - textWidth - 50
		y = 50
	case StampPositionCustom:
		// 自定义位置
		x = config.StampX
		y = config.StampY
	default:
		// 默认居中
		x = (pageWidth - textWidth) / 2
		y = (pageHeight - textHeight) / 2
	}

	// 应用偏移量
	x += float64(config.XOffset)
	y += float64(config.YOffset)

	return x, y
}
