package watermark

import (
	"bytes"
	"io"

	"github.com/pdfcpu/pdfcpu/pkg/api"
	"github.com/pdfcpu/pdfcpu/pkg/pdfcpu/color"
	"github.com/pdfcpu/pdfcpu/pkg/pdfcpu/model"
	"github.com/pdfcpu/pdfcpu/pkg/pdfcpu/types"
)

// WatermarkOptions 定义了水印的所有可配置参数
type WatermarkOptions struct {
	WatermarkTxt string  // 水印文本
	FontSize     int     // 字号（pt）
	ColorR       float64 // 颜色R
	ColorG       float64 // 颜色G
	ColorB       float64 // 颜色B
	Opacity      float64 // 透明度 0~1
	Rotation     float64 // 旋转角度 -180~180
	XOffset      int     // 水平偏移
	YOffset      int     // 垂直偏移
}

// AddWatermark 向文件流添加水印
func AddWatermark(originalFile io.ReadSeeker, opts WatermarkOptions) (io.Reader, error) {
	return addPDFWatermark(originalFile, opts)
}

// 添加水印
func addPDFWatermark(reader io.ReadSeeker, config WatermarkOptions) (io.ReadCloser, error) {
	// --- 最终的、经过完整调试的完美实现 ---

	// 1. 为零值参数设置默认值
	if config.FontSize == 0 {
		config.FontSize = 16
	}
	if config.ColorR == 0 && config.ColorG == 0 && config.ColorB == 0 {
		config.ColorR = 128
		config.ColorG = 128
		config.ColorB = 128
	}
	if config.Opacity == 0 {
		config.Opacity = 0.2
	}
	if config.Rotation == 0 {
		config.Rotation = -15
	}

	// 2. 获取页面尺寸。
	dims, err := api.PageDims(reader, nil)
	if err != nil {
		return nil, err
	}
	if _, err := reader.Seek(0, io.SeekStart); err != nil {
		return nil, err
	}

	// 3. 定义布局和视觉参数。
	watermarks := make(map[int][]*model.Watermark)
	// 根据内容和间距计算总块大小
	// 0.6 是一个估算的字符平均宽度系数
	blockWidth := float64(config.FontSize*len(config.WatermarkTxt))*0.6 + float64(config.XOffset)
	blockHeight := float64(config.FontSize) + float64(config.YOffset)

	// 4. 遍历页面，生成水印。
	for i, dim := range dims {
		var pageWatermarks []*model.Watermark
		cols := int(dim.Width/blockWidth) + 2
		rows := int(dim.Height/blockHeight) + 2

		for r := 0; r < rows; r++ {
			for c := 0; c < cols; c++ {
				// 为每个水印创建独立实例。
				wm := model.DefaultWatermarkConfig()
				wm.TextString = config.WatermarkTxt
				wm.FontSize = config.FontSize

				// Normalize color
				red, green, blue := config.ColorR, config.ColorG, config.ColorB
				if red > 1 || green > 1 || blue > 1 {
					red /= 255
					green /= 255
					blue /= 255
				}
				wm.Color = color.SimpleColor{R: float32(red), G: float32(green), B: float32(blue)}
				wm.Rotation = config.Rotation
				wm.Opacity = config.Opacity
				wm.OnTop = true
				wm.ScaleAbs = true
				wm.UserRotOrDiagonal = true // 确保自定义旋转生效
				wm.Diagonal = model.NoDiagonal

				// 5. 使用被证明可行的 Pos/Dx/Dy 系统，并采用最简单、最安全的坐标计算。
				wm.Pos = types.BottomLeft // 所有水印都基于左下角。
				offsetX := float64(c) * blockWidth
				// 为奇数行添加水平偏移以实现交错。
				if r%2 == 1 {
					offsetX += blockWidth / 2
				}
				wm.Dx = offsetX
				wm.Dy = float64(r) * blockHeight

				pageWatermarks = append(pageWatermarks, wm)
			}
		}
		watermarks[i+1] = pageWatermarks
	}

	// 6. 执行操作。
	outBuffer := new(bytes.Buffer)
	err = api.AddWatermarksSliceMap(reader, outBuffer, watermarks, nil)
	if err != nil {
		return nil, err
	}

	return io.NopCloser(outBuffer), nil
}
