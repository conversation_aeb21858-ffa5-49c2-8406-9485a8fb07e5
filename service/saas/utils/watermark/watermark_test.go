package watermark

import (
	"bytes"
	"io"
	"os"
	"testing"

	"github.com/pdfcpu/pdfcpu/pkg/api"
	"github.com/pdfcpu/pdfcpu/pkg/pdfcpu/color"
	"github.com/pdfcpu/pdfcpu/pkg/pdfcpu/model"
	"github.com/pdfcpu/pdfcpu/pkg/pdfcpu/types"
)

// TestAddPDFWatermark 对PDF水印功能进行全面的单元测试
func TestAddPDFWatermark2(t *testing.T) {
	pdfFile, err := os.Open("test.pdf")
	if err != nil {
		t.Fatalf("Failed to open test PDF: %v", err)
	}
	defer pdfFile.Close()

	opts := WatermarkOptions{
		WatermarkTxt: "Phoenix-Go-Zero",
	}

	result, err := addPDFWatermark(pdfFile, opts)
	if err != nil {
		t.Fatalf("Failed to add watermark: %v", err)
	}

	buf := new(bytes.Buffer)
	_, err = io.Copy(buf, result)
	if err != nil {
		t.Fatalf("Failed to copy result to buffer: %v", err)
	}

	os.WriteFile("test_watermark_temp2.pdf", buf.Bytes(), 0644)
}

// TestAddPDFWatermark 对PDF水印功能进行全面的单元测试
func TestAddPDFWatermark3(t *testing.T) {
	pdfFile, err := os.Open("test.pdf")
	if err != nil {
		t.Fatalf("Failed to open test PDF: %v", err)
	}
	defer pdfFile.Close()

	// --- 最终的、经过完整调试的完美实现 ---

	// 1. 获取页面尺寸。
	dims, err := api.PageDims(pdfFile, nil)
	if err != nil {
		t.Fatalf("could not get page dimensions: %v", err)
	}
	if _, err := pdfFile.Seek(0, io.SeekStart); err != nil {
		t.Fatalf("failed to seek to start: %v", err)
	}

	// 2. 定义最终的布局和视觉参数。
	watermarks := make(map[int][]*model.Watermark)
	blockWidth := 500.0
	blockHeight := 120.0

	// 3. 遍历页面，生成水印。
	for i, dim := range dims {
		var pageWatermarks []*model.Watermark
		cols := int(dim.Width/blockWidth) + 1
		rows := int(dim.Height/blockHeight) + 1

		for r := 0; r < rows; r++ {
			for c := 0; c < cols; c++ {
				// 为每个水印创建独立实例。
				wm := model.DefaultWatermarkConfig()
				wm.TextString = "Phoenix-Go-Zero"
				wm.FontSize = 40
				wm.Color = color.SimpleColor{R: 1, G: 0, B: 0}
				wm.Rotation = -30
				wm.Opacity = 1.0
				wm.OnTop = true
				wm.ScaleAbs = true
				wm.UserRotOrDiagonal = true // 确保自定义旋转生效
				wm.Diagonal = model.NoDiagonal

				// 4. 使用被证明可行的 Pos/Dx/Dy 系统，并采用最简单、最安全的坐标计算。
				wm.Pos = types.BottomLeft // 所有水印都基于左下角。
				offsetX := float64(c) * blockWidth
				// 为奇数行添加水平偏移以实现交错。
				if r%2 == 1 {
					offsetX += blockWidth / 2
				}
				wm.Dx = offsetX
				wm.Dy = float64(r) * blockHeight

				pageWatermarks = append(pageWatermarks, wm)
			}
		}
		watermarks[i+1] = pageWatermarks
	}

	// 5. 执行操作。
	outBuffer := new(bytes.Buffer)
	err = api.AddWatermarksSliceMap(pdfFile, outBuffer, watermarks, nil)
	if err != nil {
		t.Fatalf("Failed to add watermark: %v", err)
	}

	buf := new(bytes.Buffer)
	_, err = io.Copy(buf, outBuffer)
	if err != nil {
		t.Fatalf("Failed to copy result to buffer: %v", err)
	}

	os.WriteFile("test_watermark_temp3.pdf", buf.Bytes(), 0644)
}

// TestFontInitialization 测试字体初始化功能
func TestFontInitialization(t *testing.T) {
	t.Run("测试自动字体初始化", func(t *testing.T) {
		err := AutoInitFont()
		// 不管成功失败，都不应该panic
		if err != nil {
			t.Logf("自动字体初始化警告: %v", err)
		}
	})

	t.Run("测试自定义字体配置", func(t *testing.T) {
		config := FontConfig{
			FontName: "Arial",
		}

		err := InitFont(config)
		// 不管成功失败，都不应该panic
		if err != nil {
			t.Logf("字体初始化警告: %v", err)
		}
	})

	t.Run("测试系统字体配置获取", func(t *testing.T) {
		config := GetSystemFontConfig()
		if config.FontName == "" {
			t.Error("系统字体配置的字体名称不应为空")
		}
	})

	t.Run("测试多次初始化（sync.Once效果）", func(t *testing.T) {
		config1 := FontConfig{FontName: "Arial"}
		config2 := FontConfig{FontName: "Times"}

		// 多次调用应该不会panic
		InitFont(config1)
		InitFont(config2)
		InitFont(config1)

		// 如果到这里没有panic，测试通过
		t.Log("多次初始化测试通过")
	})
}

// TestWatermarkWithFontInit 测试带字体初始化的水印功能
func TestWatermarkWithFontInit(t *testing.T) {
	if _, err := os.Stat("test.pdf"); err != nil {
		t.Skip("跳过测试，未找到test.pdf文件")
		return
	}

	t.Run("测试自动字体初始化水印", func(t *testing.T) {
		pdfFile, err := os.Open("test.pdf")
		if err != nil {
			t.Fatalf("打开PDF文件失败: %v", err)
		}
		defer pdfFile.Close()

		opts := WatermarkOptions{
			WatermarkTxt: "自动字体测试",
			FontSize:     24,
			ColorR:       255,
			ColorG:       0,
			ColorB:       0,
			Opacity:      0.7,
		}

		result, err := AddWatermark(pdfFile, opts)
		if err != nil {
			t.Fatalf("添加水印失败: %v", err)
		}

		if result == nil {
			t.Fatal("水印结果不应为nil")
		}

		buf := new(bytes.Buffer)
		_, err = io.Copy(buf, result)
		if err != nil {
			t.Fatalf("复制结果失败: %v", err)
		}

		err = os.WriteFile("test_auto_font_watermark.pdf", buf.Bytes(), 0644)
		if err != nil {
			t.Fatalf("保存文件失败: %v", err)
		}

		t.Log("自动字体水印测试完成")
	})

	t.Run("测试自定义字体配置水印", func(t *testing.T) {
		pdfFile, err := os.Open("test.pdf")
		if err != nil {
			t.Fatalf("打开PDF文件失败: %v", err)
		}
		defer pdfFile.Close()

		fontConfig := &FontConfig{
			FontName: "Arial",
		}

		opts := WatermarkOptions{
			WatermarkTxt: "自定义字体测试",
			FontSize:     28,
			FontConfig:   fontConfig,
			ColorR:       0,
			ColorG:       128,
			ColorB:       0,
			Opacity:      0.8,
		}

		result, err := AddWatermark(pdfFile, opts)
		if err != nil {
			t.Fatalf("添加水印失败: %v", err)
		}

		if result == nil {
			t.Fatal("水印结果不应为nil")
		}

		buf := new(bytes.Buffer)
		_, err = io.Copy(buf, result)
		if err != nil {
			t.Fatalf("复制结果失败: %v", err)
		}

		err = os.WriteFile("test_custom_font_watermark.pdf", buf.Bytes(), 0644)
		if err != nil {
			t.Fatalf("保存文件失败: %v", err)
		}

		t.Log("自定义字体水印测试完成")
	})
}
