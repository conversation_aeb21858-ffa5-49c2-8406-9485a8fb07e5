package watermark

import (
	"bytes"
	"io"
	"os"
	"runtime"
	"testing"

	"github.com/pdfcpu/pdfcpu/pkg/api"
	"github.com/pdfcpu/pdfcpu/pkg/pdfcpu/color"
	"github.com/pdfcpu/pdfcpu/pkg/pdfcpu/model"
	"github.com/pdfcpu/pdfcpu/pkg/pdfcpu/types"
	. "github.com/smartystreets/goconvey/convey"
)

// TestAddPDFWatermark 对PDF水印功能进行全面的单元测试
func TestAddPDFWatermark2(t *testing.T) {
	pdfFile, err := os.Open("test.pdf")
	if err != nil {
		t.Skipf("跳过测试，未找到测试文件: %v", err)
		return
	}
	defer pdfFile.Close()

	opts := WatermarkOptions{
		WatermarkTxt: "Phoenix-Go-Zero",
	}

	result, err := addPDFWatermark(pdfFile, opts)
	if err != nil {
		t.Fatalf("Failed to add watermark: %v", err)
	}

	buf := new(bytes.Buffer)
	_, err = io.Copy(buf, result)
	if err != nil {
		t.Fatalf("Failed to copy result to buffer: %v", err)
	}

	os.WriteFile("test_watermark_temp2.pdf", buf.Bytes(), 0644)
}

// TestAddPDFWatermark 对PDF水印功能进行全面的单元测试
func TestAddPDFWatermark3(t *testing.T) {
	pdfFile, err := os.Open("test.pdf")
	if err != nil {
		t.Fatalf("Failed to open test PDF: %v", err)
	}
	defer pdfFile.Close()

	// --- 最终的、经过完整调试的完美实现 ---

	// 1. 获取页面尺寸。
	dims, err := api.PageDims(pdfFile, nil)
	if err != nil {
		t.Fatalf("could not get page dimensions: %v", err)
	}
	if _, err := pdfFile.Seek(0, io.SeekStart); err != nil {
		t.Fatalf("failed to seek to start: %v", err)
	}

	// 2. 定义最终的布局和视觉参数。
	watermarks := make(map[int][]*model.Watermark)
	blockWidth := 500.0
	blockHeight := 120.0

	// 3. 遍历页面，生成水印。
	for i, dim := range dims {
		var pageWatermarks []*model.Watermark
		cols := int(dim.Width/blockWidth) + 1
		rows := int(dim.Height/blockHeight) + 1

		for r := 0; r < rows; r++ {
			for c := 0; c < cols; c++ {
				// 为每个水印创建独立实例。
				wm := model.DefaultWatermarkConfig()
				wm.TextString = "Phoenix-Go-Zero"
				wm.FontSize = 40
				wm.Color = color.SimpleColor{R: 1, G: 0, B: 0}
				wm.Rotation = -30
				wm.Opacity = 1.0
				wm.OnTop = true
				wm.ScaleAbs = true
				wm.UserRotOrDiagonal = true // 确保自定义旋转生效
				wm.Diagonal = model.NoDiagonal

				// 4. 使用被证明可行的 Pos/Dx/Dy 系统，并采用最简单、最安全的坐标计算。
				wm.Pos = types.BottomLeft // 所有水印都基于左下角。
				offsetX := float64(c) * blockWidth
				// 为奇数行添加水平偏移以实现交错。
				if r%2 == 1 {
					offsetX += blockWidth / 2
				}
				wm.Dx = offsetX
				wm.Dy = float64(r) * blockHeight

				pageWatermarks = append(pageWatermarks, wm)
			}
		}
		watermarks[i+1] = pageWatermarks
	}

	// 5. 执行操作。
	outBuffer := new(bytes.Buffer)
	err = api.AddWatermarksSliceMap(pdfFile, outBuffer, watermarks, nil)
	if err != nil {
		t.Fatalf("Failed to add watermark: %v", err)
	}

	buf := new(bytes.Buffer)
	_, err = io.Copy(buf, outBuffer)
	if err != nil {
		t.Fatalf("Failed to copy result to buffer: %v", err)
	}

	os.WriteFile("test_watermark_temp3.pdf", buf.Bytes(), 0644)
}

// TestAddPDFStamp 测试图章模式水印功能
func TestAddPDFStamp(t *testing.T) {
	Convey("测试PDF图章功能", t, func() {
		Convey("测试中文图章 - 居中位置", func() {
			pdfFile, err := os.Open("test.pdf")
			if err != nil {

				return
			}
			defer pdfFile.Close()

			opts := WatermarkOptions{
				WatermarkTxt:  "机密文件",
				FontSize:      48,
				FontName:      DefaultFontName,
				ColorR:        255,
				ColorG:        0,
				ColorB:        0,
				Opacity:       0.8,
				Rotation:      0,
				Type:          WatermarkTypeStamp,
				StampPosition: StampPositionCenter,
			}

			result, err := AddWatermark(pdfFile, opts)
			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)

			buf := new(bytes.Buffer)
			_, err = io.Copy(buf, result)
			So(err, ShouldBeNil)

			err = os.WriteFile("test_stamp_center_chinese.pdf", buf.Bytes(), 0644)
			So(err, ShouldBeNil)
		})

		Convey("测试英文图章 - 右下角位置", func() {
			pdfFile, err := os.Open("test.pdf")
			So(err, ShouldBeNil)
			defer pdfFile.Close()

			opts := WatermarkOptions{
				WatermarkTxt:  "CONFIDENTIAL",
				FontSize:      36,
				FontName:      AlternateFontName,
				ColorR:        0,
				ColorG:        0,
				ColorB:        255,
				Opacity:       0.6,
				Rotation:      -45,
				Type:          WatermarkTypeStamp,
				StampPosition: StampPositionBottomRight,
			}

			result, err := AddWatermark(pdfFile, opts)
			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)

			buf := new(bytes.Buffer)
			_, err = io.Copy(buf, result)
			So(err, ShouldBeNil)

			err = os.WriteFile("test_stamp_bottom_right_english.pdf", buf.Bytes(), 0644)
			So(err, ShouldBeNil)
		})

		Convey("测试自定义位置图章", func() {
			pdfFile, err := os.Open("test.pdf")
			So(err, ShouldBeNil)
			defer pdfFile.Close()

			opts := WatermarkOptions{
				WatermarkTxt:  "审核通过",
				FontSize:      32,
				FontName:      DefaultFontName,
				ColorR:        0,
				ColorG:        128,
				ColorB:        0,
				Opacity:       0.9,
				Rotation:      15,
				Type:          WatermarkTypeStamp,
				StampPosition: StampPositionCustom,
				StampX:        200,
				StampY:        300,
			}

			result, err := AddWatermark(pdfFile, opts)
			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)

			buf := new(bytes.Buffer)
			_, err = io.Copy(buf, result)
			So(err, ShouldBeNil)

			err = os.WriteFile("test_stamp_custom_position.pdf", buf.Bytes(), 0644)
			So(err, ShouldBeNil)
		})

		Convey("测试重复图章模式", func() {
			pdfFile, err := os.Open("test.pdf")
			So(err, ShouldBeNil)
			defer pdfFile.Close()

			opts := WatermarkOptions{
				WatermarkTxt:  "机密",
				FontSize:      24,
				FontName:      DefaultFontName,
				ColorR:        255,
				ColorG:        0,
				ColorB:        0,
				Opacity:       0.4,
				Rotation:      -30,
				Type:          WatermarkTypeStamp,
				StampPosition: StampPositionCenter,
				IsRepeated:    true,
				RepeatSpaceX:  200,
				RepeatSpaceY:  150,
			}

			result, err := AddWatermark(pdfFile, opts)
			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)

			buf := new(bytes.Buffer)
			_, err = io.Copy(buf, result)
			So(err, ShouldBeNil)

			err = os.WriteFile("test_repeated_stamp.pdf", buf.Bytes(), 0644)
			So(err, ShouldBeNil)
		})

		Convey("测试重复图章模式 - 左上角起始", func() {
			pdfFile, err := os.Open("test.pdf")
			So(err, ShouldBeNil)
			defer pdfFile.Close()

			opts := WatermarkOptions{
				WatermarkTxt:  "草稿",
				FontSize:      20,
				FontName:      DefaultFontName,
				ColorR:        128,
				ColorG:        128,
				ColorB:        128,
				Opacity:       0.3,
				Rotation:      45,
				Type:          WatermarkTypeStamp,
				StampPosition: StampPositionTopLeft,
				IsRepeated:    true,
				RepeatSpaceX:  180,
				RepeatSpaceY:  120,
			}

			result, err := AddWatermark(pdfFile, opts)
			So(err, ShouldBeNil)
			So(result, ShouldNotBeNil)

			buf := new(bytes.Buffer)
			_, err = io.Copy(buf, result)
			So(err, ShouldBeNil)

			err = os.WriteFile("test_repeated_stamp_top_left.pdf", buf.Bytes(), 0644)
			So(err, ShouldBeNil)
		})
	})
}

// TestRepeatedWatermarkWithChinese 测试重复水印的中文支持
func TestRepeatedWatermarkWithChinese(t *testing.T) {
	Convey("测试重复水印中文支持", t, func() {
		pdfFile, err := os.Open("test.pdf")
		So(err, ShouldBeNil)
		defer pdfFile.Close()

		opts := WatermarkOptions{
			WatermarkTxt: "凤凰科技",
			FontSize:     20,
			FontName:     DefaultFontName,
			ColorR:       128,
			ColorG:       128,
			ColorB:       128,
			Opacity:      0.3,
			Rotation:     -30,
			Type:         WatermarkTypeRepeated,
		}

		result, err := AddWatermark(pdfFile, opts)
		So(err, ShouldBeNil)
		So(result, ShouldNotBeNil)

		buf := new(bytes.Buffer)
		_, err = io.Copy(buf, result)
		So(err, ShouldBeNil)

		err = os.WriteFile("test_repeated_chinese.pdf", buf.Bytes(), 0644)
		So(err, ShouldBeNil)
	})
}

// TestStampPositionCalculation 测试图章位置计算功能
func TestStampPositionCalculation(t *testing.T) {
	Convey("测试图章位置计算", t, func() {
		config := WatermarkOptions{
			FontSize: 24,
			XOffset:  10,
			YOffset:  20,
		}
		pageWidth := 595.0  // A4宽度
		pageHeight := 842.0 // A4高度

		Convey("测试居中位置", func() {
			x, y := calculateStampPosition(StampPositionCenter, pageWidth, pageHeight, config)
			So(x, ShouldBeGreaterThan, 0)
			So(y, ShouldBeGreaterThan, 0)
			So(x, ShouldBeLessThan, pageWidth)
			So(y, ShouldBeLessThan, pageHeight)
		})

		Convey("测试自定义位置", func() {
			config.StampX = 100
			config.StampY = 200
			x, y := calculateStampPosition(StampPositionCustom, pageWidth, pageHeight, config)
			So(x, ShouldEqual, 110) // 100 + XOffset(10)
			So(y, ShouldEqual, 220) // 200 + YOffset(20)
		})
	})
}

// TestWatermarkOptionsValidation 测试水印选项验证和默认值设置
func TestWatermarkOptionsValidation(t *testing.T) {
	Convey("测试水印选项验证", t, func() {
		Convey("测试默认值设置", func() {
			config := WatermarkOptions{
				WatermarkTxt: "测试",
			}

			setDefaultOptions(&config)

			So(config.FontSize, ShouldEqual, 16)
			So(config.FontName, ShouldEqual, DefaultFontName)
			So(config.Opacity, ShouldEqual, 0.2)
			So(config.Rotation, ShouldEqual, -15)
		})

		Convey("测试重复图章默认间距", func() {
			config := WatermarkOptions{
				WatermarkTxt: "机密",
				FontSize:     24,
				Type:         WatermarkTypeStamp,
				IsRepeated:   true,
			}

			setDefaultOptions(&config)

			So(config.RepeatSpaceX, ShouldBeGreaterThan, 0)
			So(config.RepeatSpaceY, ShouldBeGreaterThan, 0)
		})

		Convey("测试颜色标准化", func() {
			// 测试RGB值大于1的情况
			config := WatermarkOptions{
				ColorR: 255,
				ColorG: 128,
				ColorB: 64,
			}

			wm := createWatermarkConfig(config)

			So(wm.Color.R, ShouldEqual, 1.0)       // 255/255
			So(wm.Color.G, ShouldEqual, 0.5019608) // 128/255
			So(wm.Color.B, ShouldEqual, 0.2509804) // 64/255
		})
	})
}

// TestCreateWatermarkConfig 测试水印配置创建
func TestCreateWatermarkConfig(t *testing.T) {
	Convey("测试水印配置创建", t, func() {
		config := WatermarkOptions{
			WatermarkTxt: "测试图章",
			FontSize:     32,
			FontName:     DefaultFontName,
			ColorR:       255,
			ColorG:       0,
			ColorB:       0,
			Opacity:      0.8,
			Rotation:     45,
		}

		wm := createWatermarkConfig(config)

		So(wm.TextString, ShouldEqual, "测试图章")
		So(wm.FontSize, ShouldEqual, 32)
		So(wm.FontName, ShouldEqual, DefaultFontName)
		So(wm.Opacity, ShouldEqual, 0.8)
		So(wm.Rotation, ShouldEqual, 45)
		So(wm.OnTop, ShouldBeTrue)
		So(wm.ScaleAbs, ShouldBeTrue)
		So(wm.UserRotOrDiagonal, ShouldBeTrue)
	})
}

// TestFontValidation 测试字体验证功能
func TestFontValidation(t *testing.T) {
	Convey("测试字体验证功能", t, func() {
		Convey("测试字体路径验证", func() {
			// 测试存在的字体文件（macOS）
			if runtime.GOOS == "darwin" {
				valid, recommended := ValidateFont("/System/Library/Fonts/STHeiti Medium.ttc")
				if valid {
					So(valid, ShouldBeTrue)
					So(recommended, ShouldEqual, "/System/Library/Fonts/STHeiti Medium.ttc")
				}
			}

			// 测试不存在的字体文件
			valid, recommended := ValidateFont("/nonexistent/font.ttf")
			So(valid, ShouldBeFalse)
			So(recommended, ShouldNotBeEmpty)
		})

		Convey("测试字体名称验证", func() {
			valid, recommended := ValidateFont("Arial")
			So(valid, ShouldBeTrue)
			So(recommended, ShouldEqual, "Arial")
		})
	})
}

// TestGetRecommendedFont 测试字体推荐功能
func TestGetRecommendedFont(t *testing.T) {
	Convey("测试字体推荐功能", t, func() {
		Convey("测试中文文本推荐", func() {
			font := GetRecommendedFont("机密文件")
			So(font, ShouldNotEqual, AlternateFontName)
			// 应该推荐中文字体
		})

		Convey("测试英文文本推荐", func() {
			font := GetRecommendedFont("CONFIDENTIAL")
			So(font, ShouldEqual, AlternateFontName)
		})

		Convey("测试中英文混合文本推荐", func() {
			font := GetRecommendedFont("机密 CONFIDENTIAL")
			So(font, ShouldNotEqual, AlternateFontName)
			// 包含中文，应该推荐中文字体
		})

		Convey("测试空文本推荐", func() {
			font := GetRecommendedFont("")
			So(font, ShouldEqual, AlternateFontName)
		})
	})
}

// TestGetAvailableChineseFont 测试中文字体获取
func TestGetAvailableChineseFont(t *testing.T) {
	Convey("测试中文字体获取", t, func() {
		font := getAvailableChineseFont()
		So(font, ShouldNotBeEmpty)

		// 根据操作系统验证返回的字体
		switch runtime.GOOS {
		case "darwin":
			// macOS 应该返回系统字体路径或备用字体
			So(font, ShouldNotEqual, "")
		case "windows":
			So(font, ShouldEqual, WindowsChineseFontName)
		case "linux":
			So(font, ShouldEqual, LinuxChineseFontName)
		default:
			So(font, ShouldEqual, AlternateFontName)
		}
	})
}
