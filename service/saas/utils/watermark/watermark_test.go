package watermark

import (
	"bytes"
	"io"
	"os"
	"testing"

	"github.com/pdfcpu/pdfcpu/pkg/api"
	"github.com/pdfcpu/pdfcpu/pkg/pdfcpu/color"
	"github.com/pdfcpu/pdfcpu/pkg/pdfcpu/model"
	"github.com/pdfcpu/pdfcpu/pkg/pdfcpu/types"
)

// TestAddPDFWatermark 对PDF水印功能进行全面的单元测试
func TestAddPDFWatermark2(t *testing.T) {
	pdfFile, err := os.Open("test.pdf")
	if err != nil {
		t.Fatalf("Failed to open test PDF: %v", err)
	}
	defer pdfFile.Close()

	opts := WatermarkOptions{
		WatermarkTxt: "Phoenix-Go-Zero",
	}

	result, err := addPDFWatermark(pdfFile, opts)
	if err != nil {
		t.Fatalf("Failed to add watermark: %v", err)
	}

	buf := new(bytes.Buffer)
	_, err = io.Copy(buf, result)
	if err != nil {
		t.Fatalf("Failed to copy result to buffer: %v", err)
	}

	os.WriteFile("test_watermark_temp2.pdf", buf.Bytes(), 0644)
}

// TestAddPDFWatermark 对PDF水印功能进行全面的单元测试
func TestAddPDFWatermark3(t *testing.T) {
	pdfFile, err := os.Open("test.pdf")
	if err != nil {
		t.Fatalf("Failed to open test PDF: %v", err)
	}
	defer pdfFile.Close()

	// --- 最终的、经过完整调试的完美实现 ---

	// 1. 获取页面尺寸。
	dims, err := api.PageDims(pdfFile, nil)
	if err != nil {
		t.Fatalf("could not get page dimensions: %v", err)
	}
	if _, err := pdfFile.Seek(0, io.SeekStart); err != nil {
		t.Fatalf("failed to seek to start: %v", err)
	}

	// 2. 定义最终的布局和视觉参数。
	watermarks := make(map[int][]*model.Watermark)
	blockWidth := 500.0
	blockHeight := 120.0

	// 3. 遍历页面，生成水印。
	for i, dim := range dims {
		var pageWatermarks []*model.Watermark
		cols := int(dim.Width/blockWidth) + 1
		rows := int(dim.Height/blockHeight) + 1

		for r := 0; r < rows; r++ {
			for c := 0; c < cols; c++ {
				// 为每个水印创建独立实例。
				wm := model.DefaultWatermarkConfig()
				wm.TextString = "Phoenix-Go-Zero"
				wm.FontSize = 40
				wm.Color = color.SimpleColor{R: 1, G: 0, B: 0}
				wm.Rotation = -30
				wm.Opacity = 1.0
				wm.OnTop = true
				wm.ScaleAbs = true
				wm.UserRotOrDiagonal = true // 确保自定义旋转生效
				wm.Diagonal = model.NoDiagonal

				// 4. 使用被证明可行的 Pos/Dx/Dy 系统，并采用最简单、最安全的坐标计算。
				wm.Pos = types.BottomLeft // 所有水印都基于左下角。
				offsetX := float64(c) * blockWidth
				// 为奇数行添加水平偏移以实现交错。
				if r%2 == 1 {
					offsetX += blockWidth / 2
				}
				wm.Dx = offsetX
				wm.Dy = float64(r) * blockHeight

				pageWatermarks = append(pageWatermarks, wm)
			}
		}
		watermarks[i+1] = pageWatermarks
	}

	// 5. 执行操作。
	outBuffer := new(bytes.Buffer)
	err = api.AddWatermarksSliceMap(pdfFile, outBuffer, watermarks, nil)
	if err != nil {
		t.Fatalf("Failed to add watermark: %v", err)
	}

	buf := new(bytes.Buffer)
	_, err = io.Copy(buf, outBuffer)
	if err != nil {
		t.Fatalf("Failed to copy result to buffer: %v", err)
	}

	os.WriteFile("test_watermark_temp3.pdf", buf.Bytes(), 0644)
}
