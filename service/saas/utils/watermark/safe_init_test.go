package watermark

import (
	"testing"
)

// TestSafeInitFont 测试安全字体初始化
func TestSafeInitFont(t *testing.T) {
	t.Run("测试安全字体初始化", func(t *testing.T) {
		// 重置配置以确保测试环境干净
		ResetConfiguration()

		err := SafeInitFont()
		if err != nil {
			t.Logf("安全字体初始化警告: %v", err)
		}

		// 检查配置是否已初始化
		if !IsConfigurationInitialized() {
			t.Error("配置应该已经初始化")
		}

		t.Log("安全字体初始化测试完成")
	})

	t.Run("测试简化字体初始化", func(t *testing.T) {
		// 重置配置
		ResetConfiguration()

		err := SimpleInitFont("Arial")
		if err != nil {
			t.Logf("简化字体初始化警告: %v", err)
		}

		// 检查配置是否已初始化
		if !IsConfigurationInitialized() {
			t.Error("配置应该已经初始化")
		}

		t.Log("简化字体初始化测试完成")
	})

	t.Run("测试多种字体备选方案", func(t *testing.T) {
		// 重置配置
		ResetConfiguration()

		testFonts := []string{"Arial", "Helvetica", "Times", "Courier", "NonExistentFont"}

		for _, fontName := range testFonts {
			// 重置配置以测试每种字体
			ResetConfiguration()

			err := SimpleInitFont(fontName)
			if err != nil {
				t.Logf("字体 %s 初始化警告: %v", fontName, err)
			} else {
				t.Logf("字体 %s 初始化成功", fontName)
			}

			// 检查配置状态
			status := GetConfigurationStatus()
			t.Logf("字体 %s 配置状态: %+v", fontName, status)
		}
	})

	t.Run("测试错误恢复机制", func(t *testing.T) {
		// 重置配置
		ResetConfiguration()

		// 尝试使用无效配置
		invalidConfig := FontConfig{
			FontDir:  "/nonexistent/directory",
			FontPath: "/nonexistent/file.ttf",
			FontName: "NonExistentFont",
		}

		err := InitFont(invalidConfig)
		if err != nil {
			t.Logf("预期的无效配置错误: %v", err)
		}

		// 检查是否仍然可以使用安全初始化恢复
		err = SafeInitFont()
		if err != nil {
			t.Logf("安全初始化恢复警告: %v", err)
		}

		// 验证配置状态
		if !IsConfigurationInitialized() {
			t.Error("安全初始化后配置应该已初始化")
		}

		t.Log("错误恢复机制测试完成")
	})
}

// TestFontInitializationStrategies 测试不同的字体初始化策略
func TestFontInitializationStrategies(t *testing.T) {
	strategies := []struct {
		name string
		fn   func() error
	}{
		{"AutoInitFont", AutoInitFont},
		{"SafeInitFont", SafeInitFont},
		{"SimpleInitFont-Arial", func() error { return SimpleInitFont("Arial") }},
		{"SimpleInitFont-Helvetica", func() error { return SimpleInitFont("Helvetica") }},
	}

	for _, strategy := range strategies {
		t.Run(strategy.name, func(t *testing.T) {
			// 重置配置
			ResetConfiguration()

			err := strategy.fn()
			if err != nil {
				t.Logf("策略 %s 警告: %v", strategy.name, err)
			} else {
				t.Logf("策略 %s 成功", strategy.name)
			}

			// 检查配置状态
			status := GetConfigurationStatus()
			t.Logf("策略 %s 配置状态: %+v", strategy.name, status)

			// 验证配置是否已初始化
			if !IsConfigurationInitialized() {
				t.Errorf("策略 %s 执行后配置应该已初始化", strategy.name)
			}
		})
	}
}

// TestConfigurationRobustness 测试配置的健壮性
func TestConfigurationRobustness(t *testing.T) {
	t.Run("测试重复初始化", func(t *testing.T) {
		// 重置配置
		ResetConfiguration()

		// 多次初始化应该不会出错
		for i := 0; i < 5; i++ {
			err := SafeInitFont()
			if err != nil {
				t.Logf("第 %d 次初始化警告: %v", i+1, err)
			}
		}

		// 验证配置状态
		if !IsConfigurationInitialized() {
			t.Error("多次初始化后配置应该已初始化")
		}

		t.Log("重复初始化测试完成")
	})

	t.Run("测试并发初始化", func(t *testing.T) {
		// 重置配置
		ResetConfiguration()

		// 启动多个goroutine同时初始化
		done := make(chan bool, 10)
		for i := 0; i < 10; i++ {
			go func(id int) {
				err := SafeInitFont()
				if err != nil {
					t.Logf("Goroutine %d 初始化警告: %v", id, err)
				}
				done <- true
			}(i)
		}

		// 等待所有goroutine完成
		for i := 0; i < 10; i++ {
			<-done
		}

		// 验证配置状态
		if !IsConfigurationInitialized() {
			t.Error("并发初始化后配置应该已初始化")
		}

		t.Log("并发初始化测试完成")
	})
}

// BenchmarkFontInitialization 基准测试字体初始化性能
func BenchmarkFontInitialization(b *testing.B) {
	b.Run("SafeInitFont", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			ResetConfiguration()
			SafeInitFont()
		}
	})

	b.Run("SimpleInitFont", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			ResetConfiguration()
			SimpleInitFont("Arial")
		}
	})

	b.Run("AutoInitFont", func(b *testing.B) {
		for i := 0; i < b.N; i++ {
			ResetConfiguration()
			AutoInitFont()
		}
	})
}

// TestMinimalInitialization 测试最小化初始化
func TestMinimalInitialization(t *testing.T) {
	t.Run("测试空配置初始化", func(t *testing.T) {
		// 重置配置
		ResetConfiguration()

		// 使用空配置初始化
		err := InitFont(FontConfig{})
		if err != nil {
			t.Logf("空配置初始化警告: %v", err)
		}

		// 验证配置状态
		if !IsConfigurationInitialized() {
			t.Error("空配置初始化后配置应该已初始化")
		}

		status := GetConfigurationStatus()
		t.Logf("空配置状态: %+v", status)

		t.Log("最小化初始化测试完成")
	})
}
