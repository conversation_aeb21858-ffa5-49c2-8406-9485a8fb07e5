package main

import (
	"fmt"
	"log"

	"phoenix-zhong-yi-testing/service/saas/utils/watermark"
)

// 演示安全字体初始化的使用示例
func main() {
	fmt.Println("=== PDF水印安全字体初始化演示 ===")

	// 示例1：最简单的使用方式（推荐）
	demonstrateSimpleUsage()

	// 示例2：安全初始化策略
	demonstrateSafeInitialization()

	// 示例3：错误处理和恢复
	demonstrateErrorRecovery()

	// 示例4：不同初始化策略对比
	demonstrateInitializationStrategies()
}

// demonstrateSimpleUsage 演示最简单的使用方式
func demonstrateSimpleUsage() {
	fmt.Println("\n--- 最简单的使用方式 ---")

	// 直接使用，系统会自动处理所有初始化
	opts := watermark.WatermarkOptions{
		WatermarkTxt: "机密文件",
		FontSize:     32,
		ColorR:       255,
		ColorG:       0,
		ColorB:       0,
		Opacity:      0.8,
	}

	fmt.Printf("水印配置: %+v\n", opts)
	fmt.Println("✅ 配置完成，可以直接使用 AddWatermark() 函数")
	fmt.Println("系统会自动处理字体初始化，无需手动配置")
}

// demonstrateSafeInitialization 演示安全初始化策略
func demonstrateSafeInitialization() {
	fmt.Println("\n--- 安全初始化策略演示 ---")

	// 方法1：使用安全初始化
	fmt.Println("方法1: 安全初始化（推荐）")
	err := watermark.SafeInitFont()
	if err != nil {
		log.Printf("安全初始化警告: %v", err)
	} else {
		fmt.Println("✅ 安全初始化成功")
	}

	// 检查初始化状态
	if watermark.IsConfigurationInitialized() {
		fmt.Println("✅ 配置已成功初始化")
		status := watermark.GetConfigurationStatus()
		fmt.Printf("配置状态: %+v\n", status)
	}

	// 方法2：简化初始化
	fmt.Println("\n方法2: 简化初始化")
	watermark.ResetConfiguration() // 重置以演示
	err = watermark.SimpleInitFont("Arial")
	if err != nil {
		log.Printf("简化初始化警告: %v", err)
	} else {
		fmt.Println("✅ 简化初始化成功")
	}
}

// demonstrateErrorRecovery 演示错误处理和恢复
func demonstrateErrorRecovery() {
	fmt.Println("\n--- 错误处理和恢复演示 ---")

	// 重置配置以模拟初始状态
	watermark.ResetConfiguration()

	// 尝试使用可能失败的配置
	fmt.Println("尝试使用可能失败的配置...")
	invalidConfig := watermark.FontConfig{
		FontDir:  "/nonexistent/directory",
		FontPath: "/nonexistent/file.ttf",
		FontName: "NonExistentFont",
	}

	err := watermark.InitFont(invalidConfig)
	if err != nil {
		fmt.Printf("❌ 预期的配置失败: %v\n", err)
	}

	// 使用安全初始化进行恢复
	fmt.Println("使用安全初始化进行恢复...")
	err = watermark.SafeInitFont()
	if err != nil {
		fmt.Printf("⚠️  安全初始化警告: %v\n", err)
	} else {
		fmt.Println("✅ 安全初始化恢复成功")
	}

	// 验证恢复状态
	if watermark.IsConfigurationInitialized() {
		fmt.Println("✅ 配置已成功恢复")
	} else {
		fmt.Println("❌ 配置恢复失败")
	}
}

// demonstrateInitializationStrategies 演示不同的初始化策略
func demonstrateInitializationStrategies() {
	fmt.Println("\n--- 不同初始化策略对比 ---")

	strategies := []struct {
		name        string
		description string
		fn          func() error
	}{
		{
			name:        "SafeInitFont",
			description: "安全初始化，提供多种备选方案",
			fn:          watermark.SafeInitFont,
		},
		{
			name:        "AutoInitFont",
			description: "自动初始化，使用系统字体配置",
			fn:          watermark.AutoInitFont,
		},
		{
			name:        "SimpleInitFont",
			description: "简化初始化，只设置字体名称",
			fn:          func() error { return watermark.SimpleInitFont("Arial") },
		},
	}

	for i, strategy := range strategies {
		fmt.Printf("\n策略 %d: %s\n", i+1, strategy.name)
		fmt.Printf("描述: %s\n", strategy.description)

		// 重置配置以测试每种策略
		watermark.ResetConfiguration()

		// 执行策略
		err := strategy.fn()
		if err != nil {
			fmt.Printf("结果: ⚠️  警告 - %v\n", err)
		} else {
			fmt.Printf("结果: ✅ 成功\n")
		}

		// 显示配置状态
		status := watermark.GetConfigurationStatus()
		fmt.Printf("配置状态: %+v\n", status)
	}
}

// demonstrateProductionUsage 演示生产环境使用建议
func demonstrateProductionUsage() {
	fmt.Println("\n--- 生产环境使用建议 ---")

	fmt.Println("建议1: 应用启动时预初始化")
	fmt.Println("在应用启动时调用 SafeInitFont() 进行预初始化")

	fmt.Println("\n建议2: 使用默认配置")
	fmt.Println("大多数情况下，直接使用 AddWatermark() 即可，无需手动配置")

	fmt.Println("\n建议3: 错误处理")
	fmt.Println("字体初始化失败不会阻止水印功能，系统会自动降级")

	fmt.Println("\n建议4: 监控配置状态")
	fmt.Println("可以使用 GetConfigurationStatus() 监控配置状态")

	// 演示生产环境代码
	fmt.Println("\n生产环境示例代码:")
	fmt.Println(`
// 应用启动时
func init() {
    err := watermark.SafeInitFont()
    if err != nil {
        log.Printf("字体初始化警告: %v", err)
    }
}

// 使用时
func addWatermark(file io.ReadSeeker, text string) (io.Reader, error) {
    opts := watermark.WatermarkOptions{
        WatermarkTxt: text,
        FontSize:     24,
        Opacity:      0.7,
    }
    return watermark.AddWatermark(file, opts)
}
`)
}

// demonstrateDebugging 演示调试和故障排除
func demonstrateDebugging() {
	fmt.Println("\n--- 调试和故障排除 ---")

	fmt.Println("1. 检查配置状态:")
	status := watermark.GetConfigurationStatus()
	for key, value := range status {
		fmt.Printf("   %s: %v\n", key, value)
	}

	fmt.Println("\n2. 检查系统字体配置:")
	systemConfig := watermark.GetSystemFontConfig()
	fmt.Printf("   字体目录: %s\n", systemConfig.FontDir)
	fmt.Printf("   字体路径: %s\n", systemConfig.FontPath)
	fmt.Printf("   字体名称: %s\n", systemConfig.FontName)

	fmt.Println("\n3. 测试不同字体:")
	testFonts := []string{"Arial", "Helvetica", "Times", "Courier"}
	for _, fontName := range testFonts {
		watermark.ResetConfiguration()
		err := watermark.SimpleInitFont(fontName)
		status := "✅"
		if err != nil {
			status = "❌"
		}
		fmt.Printf("   %s %s\n", status, fontName)
	}

	fmt.Println("\n4. 如果所有方法都失败:")
	fmt.Println("   - 检查系统是否安装了基本字体")
	fmt.Println("   - 尝试使用 SimpleInitFont(\"Arial\")")
	fmt.Println("   - 查看详细错误日志")
	fmt.Println("   - 联系技术支持")
}

// 完整的演示程序
func fullDemo() {
	fmt.Println("=== 完整演示程序 ===")

	demonstrateSimpleUsage()
	demonstrateSafeInitialization()
	demonstrateErrorRecovery()
	demonstrateInitializationStrategies()
	demonstrateProductionUsage()
	demonstrateDebugging()

	fmt.Println("\n=== 演示完成 ===")
	fmt.Println("推荐使用 SafeInitFont() 进行字体初始化")
	fmt.Println("或者直接使用 AddWatermark()，系统会自动处理初始化")
}
