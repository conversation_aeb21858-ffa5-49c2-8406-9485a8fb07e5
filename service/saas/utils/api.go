package utils

import (
	"encoding/json"
	"os"
)

type API struct {
	Path        string `json:"path"`
	Method      string `json:"method"`
	Description string `json:"description"`
	ApiGroup    string `json:"apiGroup"`
}

func GetAPIsFromJsonFile(path string) (*[]API, error) {
	var apis []API
	f, err := os.Open(path)

	if err != nil {
		return nil, err
	}

	defer f.Close()

	decoder := json.NewDecoder(f)
	err = decoder.Decode(&apis)
	if err != nil {
		return nil, err
	}

	return &apis, nil
}

func GetAPIsFromJsonString(apisStr string) (*[]API, error) {
	var apis []API

	err := json.Unmarshal([]byte(apisStr), &apis)
	if err != nil {
		return nil, err
	}

	return &apis, nil
}
