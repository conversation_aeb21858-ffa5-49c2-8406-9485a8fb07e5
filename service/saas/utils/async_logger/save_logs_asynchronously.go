package async_logger

import (
	"context"
	"encoding/json"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"gorm.io/gorm"
	"phoenix/service/saas/model/gorm/mapper"
	"time"
)

type AsyncLoggerConf struct {
	LogSavingTiming int64
	BatchSize       int
}

type OperationLogClient struct {
	GormDB      *gorm.DB
	Redis       *redis.Redis
	AsyncLogger *AsyncLoggerConf
}

func NewOperationLogProcessor(gormDB *gorm.DB, redis *redis.Redis, asyncLogger *AsyncLoggerConf) *OperationLogClient {
	return &OperationLogClient{
		GormDB:      gormDB,
		Redis:       redis,
		AsyncLogger: asyncLogger,
	}
}

func (l *OperationLogClient) StartOperationLogProcessor() {
	ctx := context.Background()

	go func() {
		ticker := time.NewTicker(time.Duration(l.AsyncLogger.LogSavingTiming) * time.Second) // 每 LogSavingTiming 秒处理一次
		defer ticker.Stop()

		for {
			select {
			case <-ticker.C:
				// 定时处理Redis中的日志数据
				l.processRedisLogs()
			case <-ctx.Done():
				// 上下文取消时退出
				logx.Info("操作日志处理器协程退出")
				return
			}
		}
	}()

	logx.Info("操作日志处理器协程已启动")
}

// 处理Redis中的日志数据
func (l *OperationLogClient) processRedisLogs() {
	batchSize := l.AsyncLogger.BatchSize // 每批处理 BatchSize 条日志
	logs := make([]SystemOperationLogInfo, 0, batchSize)

	// 从Redis list中批量获取日志数据
	for i := 0; i < batchSize; i++ {
		// 从Redis list右侧弹出元素
		result, err := l.Redis.Rpop("system_operation_logs")
		if err != nil {
			if err.Error() == "redis: nil" {
				break // 没有更多数据
			}
			logx.Errorf("Redis弹出操作日志失败: %v", err)
			break
		}

		var log SystemOperationLogInfo
		if err := json.Unmarshal([]byte(result), &log); err != nil {
			logx.Errorf("反序列化操作日志失败: %v, 原始数据: %s", err, result)
			continue
		}

		logs = append(logs, log)
	}

	// 如果有日志数据，进行批量存储
	if len(logs) > 0 {
		if err := l.batchInsertOperationLogs(logs); err != nil {
			logx.Errorf("批量插入操作日志失败: %v", err)
		} else {
			logx.Infof("成功批量插入 %d 条操作日志", len(logs))
		}
	}
}

// 批量插入操作日志到数据库
func (l *OperationLogClient) batchInsertOperationLogs(logs []SystemOperationLogInfo) error {
	if len(logs) == 0 {
		return nil
	}
	logInfo := l.logReqToLogInfo(logs)
	// 进行批量存储
	OperationLogClient := mapper.NewOperationLogClient(l.GormDB)
	err := OperationLogClient.BatchInsertOperationLogs(logInfo)
	if err != nil {
		return err
	}
	return err
}

// 数据转换
func (l *OperationLogClient) logReqToLogInfo(logReq []SystemOperationLogInfo) (logInfo []mapper.OperationLogModel) {
	for _, v := range logReq {
		if v.RequestBody == "" {
			v.RequestBody = "{}"
		}
		if v.ResponseBody == "" {
			v.ResponseBody = "{}"
		}
		logModel := mapper.OperationLogModel{
			UserID:       v.UserID,
			TenantID:     v.TenantID,
			Method:       v.Method,
			API:          v.API,
			IP:           v.IP,
			RequestBody:  v.RequestBody,
			ResponseBody: v.ResponseBody,
			HttpStatus:   v.HttpStatus,
			CreatedAt:    time.UnixMilli(v.CreatedAt),
			OK:           v.HttpStatus == 200,
		}
		logInfo = append(logInfo, logModel)
	}
	return logInfo
}

type SystemOperationLogInfo struct {
	UserID       string `json:"userId"`
	TenantID     string `json:"tenantId"`
	Method       string `json:"method"`
	API          string `json:"api"`
	RequestBody  string `json:"request_body"`
	ResponseBody string `json:"response_body"`
	IP           string `json:"ip"`
	HttpStatus   int32  `json:"httpStatus"`
	CreatedAt    int64  `json:"createdAt"`
}
