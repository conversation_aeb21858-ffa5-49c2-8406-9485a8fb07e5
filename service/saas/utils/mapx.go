package utils

import (
	"fmt"
	"strconv"
)

// ParseStringParam 从map中安全提取string类型参数
func ParseStringParam(m map[string]interface{}, key string) (string, bool) {
	if v, ok := m[key]; ok {
		if s, ok := v.(string); ok {
			return s, true
		}
	}
	return "", false
}

// ParseStringSliceParam 从map中安全提取[]string类型参数
func ParseStringSliceParam(m map[string]interface{}, key string) ([]string, bool) {
	if v, ok := m[key]; ok {
		if s, ok := v.([]string); ok {
			return s, true
		}
		if arr, ok := v.([]interface{}); ok {
			res := make([]string, 0, len(arr))
			for _, elem := range arr {
				str, ok := elem.(string)
				if !ok {
					return nil, false
				}
				res = append(res, str)
			}
			return res, true
		}
	}
	return nil, false
}

// ParseIntParam 从map中安全提取int64类型参数
func ParseIntParam(m map[string]interface{}, key string) (int64, bool) {
	if v, ok := m[key]; ok {
		switch v.(type) {
		case int, int8, int16, int32, int64:
			i, err := strconv.ParseInt(fmt.Sprintf("%v", v), 10, 64)
			if err != nil {
				return 0, false
			}
			return i, true
		case float32, float64:
			i, err := strconv.ParseFloat(fmt.Sprintf("%v", v), 64)
			if err != nil {
				return 0, false
			}
			return int64(i), true

		case complex64, complex128:
			i, err := strconv.ParseComplex(fmt.Sprintf("%v", v), 64)
			if err != nil {
				return 0, false
			}
			return int64(real(i)), true

		case string:
			i, err := strconv.Atoi(v.(string))
			if err != nil {
				return 0, false
			}
			return int64(i), true
		}
	}
	return 0, false
}
