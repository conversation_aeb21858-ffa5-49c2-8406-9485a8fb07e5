package file_crypto

import (
	"github.com/pdfcpu/pdfcpu/pkg/api"
	"github.com/pdfcpu/pdfcpu/pkg/pdfcpu/model"
	"os"
	"path/filepath"
)

func PDFAes256LockFile(filePath, password string) (err error) {

	inputFile, err := os.Open(filePath)
	if err != nil {
		return err
	}

	tempFile, err := os.CreateTemp(filepath.Dir(filePath), "")
	if err != nil {
		inputFile.Close()
		return
	}
	defer func() {
		if err != nil {
			_ = inputFile.Close()
			_ = tempFile.Close()
			_ = os.Remove(tempFile.Name())
		}
		if err := tempFile.Close(); err != nil {
			return
		}
		if err := inputFile.Close(); err != nil {
			return
		}
		if err := os.Rename(tempFile.Name(), filePath); err != nil {
			return
		}
	}()

	if err = api.Encrypt(inputFile, tempFile, model.NewAESConfiguration(password, password, 256)); err != nil {
		return
	}
	return
}
