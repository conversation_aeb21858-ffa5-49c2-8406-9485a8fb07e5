package utils

import (
	"testing"

	"github.com/smartystreets/goconvey/convey"
)

func TestNewSm4Tool(t *testing.T) {
	sm4Tool := NewSm4Tool()
	encryptedData := `MDUxMGVmZDlmNGE1ODFhNzFjYmFhMWViYTgwNDA5YTlkMDRkMWVhODk3MDg3ZjkxOTI4OTZiYWUzNmM1Y2IyNWYzOTMwZjg2YTRkODU0NDEwMzg4NjliYTM3ZTlmN2Y5Yjc0OTAxNmQ4ZWY4NWE0ZjI3ZTljMmY5MDZmOGIxZWMwMjBhMjJmNGYyNjUxODc5MmY2OGM1ODA1ZTYwOTVjZjgzNDE0MDlhMTM4NWNhODM1YzkwNjZkYjBmYjBhNmViMGRiOTBjODk0MzJjNjgzZjM0YTNjODZiMGU4OGFlNWM4MGM5MzVkYjU1MzYzNTU1ODllOGVmOGU4MjNmOTEzNzQyM2NjYjIzYmVjMTA3Y2JiZTJlNDFlZTkxOWExZGJiZGJmNmNjNGJlZTFjMzhkZTU2MGRmODMwYjJlMmViNjg3YzQ2YzdhMTZiNTBmNTMzMzFjZDk4NjUwYzZjNzEyYWI2M2YxMDAzYmIwM2IxNTg3ODY4NjY4ODliNmY2OTQ0MmRlYTViNzNlNTcwMjYzOTQ3ODczNmIyM2QwNWYzMmMyNWFkMmExNDQxMGRmYTg2MzFjZjQyNjY4OWVkYjQ0ZWQ0MTVlMDRhYzI1ZTI5OTIxNTAwODZhMDAyOWIzNWMyMTg0YmI5NDYwZTJiNjY2YzVhMDUwMDYyZDNkNjFjNmFkN2ZiYzJlNGMwYjMwNDQ4Mjk3NDUyYjFmZGNhOWY4ZTkyZmM5MzAyM2ExYTA2ZjY5ZGFiNTNlMDBiZjVkMzJmYjllMGFlZDFhN2YxNzNiODQ2YjE3ZDM1N2UxYmI3ODlkZWE4Mzk4NjU2YWYzZDEzMDU1MDE0ZmY2ODlhOWM3Y2Y2OWE2ZWFjNjU0Yjc3M2E3OWZhYTI5ZTRlMzMyODhmMmRmMWQ2MGI3OTA5MzMzMjU2NGQ5NTVhYmNmN2MxZjNjMjQ1OTNlMDRjMTVkOWNmZDM3NjBkNjY4ODgwZWJjYjJhZDgwOTNlNDYwNDkwYzlmMmExNTAzZTM2ODk4ZGMzMzUzMDJiNDFiMjQ2MTdjNGY5YTllMGNmNmI2YzI1OGQ5NGM0Mjk0MWM0NDBkNzk2MGIxMjAzNjI3MmEyYzdkYjljYmQ3ODg1OGI0YjA2OTUxYzE0MzdkNzZjYjg4YzY3`
	plainText := `{"url":"/saas/api/v1/auth/login","method":"post","data":{"p_data":"ZmNmMjNjYWFjMmU1ZTg0MmNiZDc2MDQyZjg2ZDI3NjczNDFiZGE0NjgyMDU2OGQ2ZGU5MDY1YzE2OTZmN2FkMTA0ODViMDA1ZGQzY2NhMTU2M2RhZWY2YTI5YWFmZGVmMDVkY2ZkNTkxYTI1MWQ3NjdjMjY0ZGQ4YWY0NmYwZTliNTc1YWYxYzg5OWVkYWY1M2JmM2NmY2ExOGRlMWEzOGM0ZDE3ZDEyMWQ2NzIzNDY4NTNlNWFkNWU5MTIxZjNlMmNiZGQxODQxODRkZTFmOTI5OTdiZDk4MDU3YTNhNWQ1NTFlZWZjZDc2YmVlZmJlNDBmMWEzZWUyODdlZGI1NQ=="},"query":{}}`

	convey.<PERSON>vey("test decrypt", t, func() {
		result, err := sm4Tool.Decrypt(encryptedData)
		convey.So(err, convey.ShouldBeNil)
		convey.So(string(result), convey.ShouldEqual, plainText)
	})

	convey.<PERSON>vey("test encrypt", t, func() {
		result, err := sm4Tool.Encrypt([]byte(plainText))
		convey.So(err, convey.ShouldBeNil)
		convey.So(string(result), convey.ShouldEqual, encryptedData)
	})

	convey.<PERSON>vey("test encrypt str", t, func() {
		result := sm4Tool.EncryptStr(plainText)
		convey.So(result, convey.ShouldEqual, encryptedData)
	})
}
