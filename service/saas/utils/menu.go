package utils

import (
	"encoding/json"
	"os"
)

type Permission struct {
	Name string `json:"name"`
	Code string `json:"code"`
}

type Meta struct {
	Title       string       `json:"title"`
	Icon        string       `json:"icon"`
	Hidden      bool         `json:"hidden"`
	HiddenInTab bool         `json:"hiddenInTab"`
	IsActive    bool         `json:"isActive"`
	IsFullPage  bool         `json:"isFullPage"`
	Fixed       bool         `json:"fixed"`
	Permissions []Permission `json:"permissions"`
}

type Menu struct {
	Path      string `json:"path"`
	Name      string `json:"name"`
	Meta      Meta   `json:"meta"`
	Component string `json:"component"`
	Children  []Menu `json:"children"`
	Redirect  string `json:"redirect"`
}

func GetMenusFromJsonFile(path string) (*[]Menu, error) {
	var menus []Menu
	f, err := os.Open(path)

	if err != nil {
		return nil, err
	}

	defer f.Close()

	decoder := json.NewDecoder(f)
	err = decoder.Decode(&menus)
	if err != nil {
		return nil, err
	}

	return &menus, nil
}

func GetMenusFromJsonString(menusStr string) (*[]Menu, error) {
	var menus []Menu

	err := json.Unmarshal([]byte(menusStr), &menus)
	if err != nil {
		return nil, err
	}

	return &menus, nil
}
