package minio

import (
	"context"
	"fmt"
	"io"
	"net/url"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
)

type MinioConf struct {
	Endpoint        string
	AccessKeyID     string
	SecretAccessKey string
	UseSSL          bool
	BucketName      string
	PreSignedExpiry int64
}

type MinioClient struct {
	client          *minio.Client
	bucketName      string
	preSignedExpiry int64
}

func NewMinioClient(conf *MinioConf) (*MinioClient, error) {
	// 初始化 MinIO 客户端
	client, err := minio.New(conf.Endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(conf.AccessKeyID, conf.SecretAccessKey, ""),
		Secure: conf.UseSSL,
	})
	if err != nil {
		return nil, err
	}

	return &MinioClient{
		client:          client,
		bucketName:      conf.BucketName,
		preSignedExpiry: conf.PreSignedExpiry,
	}, nil
}

// GeneratePreSignedPutURL 生成用于上传文件的预签名URL 使用默认过期时间
func (m *MinioClient) GeneratePreSignedPutURL(ctx context.Context, objectName string) (string, error) {
	expiry := time.Duration(m.preSignedExpiry) * time.Second
	// 生成预签名URL，用于上传文件
	preSignedURL, err := m.client.PresignedPutObject(ctx, m.bucketName, objectName, expiry)
	if err != nil {
		return "", err
	}

	return preSignedURL.String(), nil
}

// GenerateDownloadPreSignedURL 生成下载文件的预签名URL 使用默认过期时间
func (m *MinioClient) GenerateDownloadPreSignedURL(ctx context.Context, path, name string) (string, error) {
	expiry := time.Duration(m.preSignedExpiry) * time.Second
	// 生成预签名 URL
	presignedURL, err := m.client.Presign(ctx, "GET", m.bucketName, path, expiry, url.Values{
		"response-content-disposition": {fmt.Sprintf("attachment; filename=\"%s\"", name)},
	})
	if err != nil {
		return "", fmt.Errorf("failed to generate pre-signed URL: %v", err)
	}

	return presignedURL.String(), nil
}

// GenerateDownloadPreSignedURLex 生成下载文件的预签名URL 使用参数过期时间
func (m *MinioClient) GenerateDownloadPreSignedURLex(ctx context.Context, path, name string, expires int64) (string, error) {
	expiry := time.Duration(expires) * time.Second
	// 生成预签名 URL
	presignedURL, err := m.client.Presign(ctx, "GET", m.bucketName, path, expiry, url.Values{
		"response-content-disposition": {fmt.Sprintf("attachment; filename=\"%s\"", name)},
	})
	if err != nil {
		return "", fmt.Errorf("failed to generate pre-signed URL: %v", err)
	}

	return presignedURL.String(), nil
}

// GenerateViewPreSignedGetURL 生成用于预览文件的预签名URL 使用默认过期时间
func (m *MinioClient) GenerateViewPreSignedGetURL(ctx context.Context, path string, name string) (string, error) {
	expiry := time.Duration(m.preSignedExpiry) * time.Second
	preSignedURL, err := m.client.PresignedGetObject(ctx, m.bucketName, path, expiry, url.Values{
		"response-content-disposition": {fmt.Sprintf("inline; filename=\"%s\"", name)},
	})
	if err != nil {
		return "", fmt.Errorf("failed to generate pre-signed URL: %v", err)
	}

	return preSignedURL.String(), nil
}

// GenerateViewPreSignedGetURLex 生成用于预览文件的预签名URL 使用参数过期时间
func (m *MinioClient) GenerateViewPreSignedGetURLex(ctx context.Context, path string, name string, expires int64) (string, error) {
	expiry := time.Duration(expires) * time.Second
	preSignedURL, err := m.client.PresignedGetObject(ctx, m.bucketName, path, expiry, url.Values{
		"response-content-disposition": {fmt.Sprintf("inline; filename=\"%s\"", name)},
	})
	if err != nil {
		return "", fmt.Errorf("failed to generate pre-signed URL: %v", err)
	}

	return preSignedURL.String(), nil
}

// EnsureBucketExists 确保存储桶存在
func (m *MinioClient) EnsureBucketExists(ctx context.Context) error {
	exists, err := m.client.BucketExists(ctx, m.bucketName)
	if err != nil {
		return err
	}

	if !exists {
		err = m.client.MakeBucket(ctx, m.bucketName, minio.MakeBucketOptions{})
		if err != nil {
			return err
		}
	}

	return nil
}

// GetObject 直接从存储桶中获取文件对象
func (m *MinioClient) GetObject(ctx context.Context, objectName string) (*minio.Object, error) {
	return m.client.GetObject(ctx, m.bucketName, objectName, minio.GetObjectOptions{})
}

// PutObject 从一个 io.Reader 上传文件对象.
func (m *MinioClient) PutObject(ctx context.Context, objectName string, reader io.Reader, objectSize int64, contentType string) (minio.UploadInfo, error) {
	return m.client.PutObject(ctx, m.bucketName, objectName, reader, objectSize, minio.PutObjectOptions{ContentType: contentType})
}
