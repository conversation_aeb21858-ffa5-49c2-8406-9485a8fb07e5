package bootstrap

import (
	"context"
	"phoenix/service/saas/adapter/addons"
	"phoenix/service/saas/adapter/kqs"
	"phoenix/service/saas/api/internal/config"
	"phoenix/service/saas/api/internal/errs"
	"phoenix/service/saas/api/internal/handler"
	"phoenix/service/saas/api/internal/logic/workflow"
	"phoenix/service/saas/api/internal/svc"
	_ "phoenix/service/saas/model/ent/runtime"

	"gitee.com/damengde/gzconfigcenter"
	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/rest/httpx"
)

func NewServer(ctx context.Context, c gzconfigcenter.ConfigManager[config.Config]) (*rest.Server, *svc.ServiceContext, error) {

	s := rest.MustNewServer(c.GetConfig().RestConf)

	svcCtx := svc.NewServiceContext(ctx, c)
	initSallyAddons(ctx, svcCtx)
	// 自定义错误 ErrorHandlerCtx
	httpx.SetErrorHandlerCtx(errs.ErrHandler)

	// 注册路由
	handler.RegisterHandlers(s, svcCtx)
	return s, svcCtx, nil
}

func initSallyAddons(ctx context.Context, svcCtx *svc.ServiceContext) addons.SallyAddons {
	referenceImpl := workflow.NewGetWorkflowNodeReviewerReferenceImpl(svcCtx.GormDB, svcCtx.DB)
	sallyAddons := addons.NewSallyAddonsImpl(svcCtx.GormDB)
	sallyAddons.RegisterWorkflowReviewerQuerier(ctx, referenceImpl)
	sallyAddons.RegisterWorkflowEventProcessor(ctx, workflow.NewWorkflowEventHandler(svcCtx.GormDB, svcCtx.DB, svcCtx.IDGenerator, initKafkaProducer(svcCtx.Config.KafkaConf), svcCtx.Config))
	svcCtx.SallyAddons = sallyAddons
	return sallyAddons
}

func initKafkaProducer(c config.KafkaConf) *kqs.KafkaProducer {
	producer := kqs.NewKafkaProducer(&kqs.KafkaConfig{
		Brokers: c.Brokers,
	})
	return producer
}
