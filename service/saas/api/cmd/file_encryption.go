package main

import (
	"flag"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"phoenix/service/saas/api/bootstrap"
	"phoenix/service/saas/utils/file_crypto"

	"github.com/zeromicro/go-zero/core/logc"
)

var (
	configFile = flag.String("f", "etc/phoenix.yaml", "the config file")
)

func main() {
	// 获取cli参数
	flag.Parse()

	defer logc.Close()

	var ps = make(map[string]string)
	// 加载配置文件
	c := bootstrap.NewConfig(*configFile)
	err := filepath.Walk(c.UploadConf.StorePath, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			fmt.Println(fmt.Sprintf("Error Walk file[%s]:%s", path, err.Error()))
			return nil
		}
		if info.IsDir() {
			return nil
		}
		all, err := readFileBytes(path)
		if err != nil {
			return nil
		}
		aes, err := file_crypto.AesEncrypt(all, file_crypto.PwdKey)
		if err != nil {
			fmt.Println(fmt.Sprintf("Error Encrypting file[%s]:%s", path, err.Error()))
			return nil
		}
		nfn := path + ".enc"
		file, err := os.Create(nfn)
		if err != nil {
			fmt.Println(fmt.Sprintf("Error Create file[%s]:%s", path+".enc", err.Error()))
			return nil
		}
		defer file.Close()
		_, err = file.Write(aes)
		if err != nil {
			fmt.Println(fmt.Sprintf("Error Write file[%s]:%s", path, err.Error()))
			return nil
		}
		ps[path] = nfn
		if err := os.Remove(path); err != nil {
			fmt.Println(fmt.Sprintf("Error Remove file[%s]:%s", path, err.Error()))
			return nil
		}
		fmt.Println(fmt.Sprintf("Successed Encrypt file[%s]", path))
		return nil
	})
	if err != nil {
		fmt.Println("Error walking directory:", err)
		fmt.Println("")
		fmt.Println("--------------------------------------------------")
		fmt.Println("---------------Fails Encrypt file-----------------")
		fmt.Println("--------------------------------------------------")
	}

	//重命名文件
	for k, v := range ps {
		os.Rename(v, k)
	}
	fmt.Println("")
	fmt.Println("--------------------------------------------------")
	fmt.Println("--------------Successes Encrypt file--------------")
	fmt.Println("--------------------------------------------------")
}

func readFileBytes(path string) ([]byte, error) {
	file, err := os.Open(path)
	if err != nil {
		fmt.Println(fmt.Sprintf("Error Open file[%s]:%s", path, err.Error()))
		return nil, err
	}
	defer file.Close()
	all, err := io.ReadAll(file)
	if err != nil {
		fmt.Println(fmt.Sprintf("Error ReadAll Bytes file[%s]:%s", path, err.Error()))
		return nil, err
	}
	return all, nil
}
