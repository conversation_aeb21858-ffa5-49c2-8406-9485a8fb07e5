{"common": {"success": "successfully", "failed": "failed", "updateSuccess": "Update successfully", "updateFailed": "Update failed", "createSuccess": "Create successfully", "createFailed": "Create failed", "deleteSuccess": "Delete successfully", "deleteFailed": "Delete failed", "targetNotExist": "Target does not exist", "databaseError": "Database error", "redisError": "Redis error", "permissionDeny": "User does not have permission to access this interface", "constraintError": "Operation failed: Data conflict", "validationError": "Operation failed: Validation failed", "notSingularError": "Operation failed: Data not unique"}, "init": {"alreadyInit": "The database had been initialized.", "initializeIsRunning": "The initialization is running..."}, "login": {"wrongCaptcha": "Wrong captcha"}}