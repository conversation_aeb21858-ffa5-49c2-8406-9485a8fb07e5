package svc

import (
	"context"
	"phoenix/service/saas/adapter/addons"
	"phoenix/service/saas/api/internal/config"
	i18n2 "phoenix/service/saas/api/internal/i18n"
	"phoenix/service/saas/api/internal/middleware"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/services/workflow/callback"
	"phoenix/service/saas/api/internal/utils"
	"phoenix/service/saas/model"
	"phoenix/service/saas/model/ent"
	"phoenix/service/saas/utils/minio"
	"phoenix/service/saas/utils/tokenbox"

	"gitee.com/damengde/gzconfigcenter"
	"github.com/casbin/casbin/v2"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/zrpc"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	commonMiddleware "gitlab.zhijiasoft.com/paperless-group/saas-common/middleware"
	"google.golang.org/grpc"
	"gorm.io/gorm"
)

type ServiceContext struct {
	Config                  config.Config
	ConfigManager           gzconfigcenter.ConfigManager[config.Config]
	Casbin                  *casbin.Enforcer
	Redis                   *redis.Redis
	Authority               rest.Middleware
	AuthMiddleware          rest.Middleware
	Trans                   *i18n.Translator
	SaasService             services.SaasService
	Authenticators          []commonMiddleware.Authenticator
	CaptchaUtil             *utils.CaptchaUtil
	TraceMiddleware         rest.Middleware
	DB                      *ent.Client
	GormDB                  *gorm.DB
	MinioClient             *minio.MinioClient
	StargateRpcConn         *grpc.ClientConn
	IDGenerator             addons.IDGeneratorAddons
	SallyAddons             addons.SallyAddons
	QuickNameTranslator     addons.QuickNameTranslator
	CacheInitLoader         *addons.CacheInitLoader
	WorkflowCallbackService callback.Service
}

func NewServiceContext(ctx context.Context, c gzconfigcenter.ConfigManager[config.Config]) *ServiceContext {

	rds := redis.MustNewRedis(c.GetConfig().Redis)

	cbn := c.GetConfig().CasbinConf.MustNewCasbinWithRedisWatcher(c.GetConfig().DatabaseConf.Type, c.GetConfig().DatabaseConf.GetDSN(), c.GetConfig().Redis)

	trans := i18n.NewTranslator(i18n2.LocaleFS)

	//初始化minio
	minioClient := initMinio(c)

	// 初始化token校验工具
	tokenbox.InitTokenBox(c.GetConfig().Redis.Host, c.GetConfig().Redis.Type, c.GetConfig().Redis.Pass)

	db, gormDB := initDB(c)

	stargateRpcConn := zrpc.MustNewClient(c.GetConfig().StargateRpc).Conn()

	snowflake := initIdGenerator(c)

	// 初始化缓存初始化加载器
	cacheInitLoader := addons.NewCacheInitLoader(rds, gormDB, db)
	cacheInitLoader.InitCache(context.Background())
	// 初始化快速名称翻译器
	quickNameTranslator := addons.NewQuickNameTranslatorImpl(rds)

	callbackService := callback.NewService(gormDB)

	return &ServiceContext{
		Config:                  c.GetConfig(),
		Redis:                   rds,
		Authority:               middleware.NewTokenAuthMiddleware(c).Handle,
		AuthMiddleware:          middleware.NewHeaderForwardAuthMiddleware(c).Handle, //默认开启
		TraceMiddleware:         middleware.NewTraceMiddleware().Handle,
		Trans:                   trans,
		Casbin:                  cbn,
		CaptchaUtil:             utils.InitStoreAndDriver(c.GetConfig().Captcha, rds),
		DB:                      db,
		SaasService:             services.NewImpl(db, rds, gormDB, cacheInitLoader),
		GormDB:                  gormDB,
		ConfigManager:           c,
		MinioClient:             minioClient,
		StargateRpcConn:         stargateRpcConn,
		IDGenerator:             snowflake,
		QuickNameTranslator:     quickNameTranslator,
		CacheInitLoader:         cacheInitLoader,
		WorkflowCallbackService: callbackService,
	}
}

func initIdGenerator(c gzconfigcenter.ConfigManager[config.Config]) addons.IDGeneratorAddons {
	snowflake := addons.NewIdGeneratorAddonsImpl(addons.IDGeneratorConf{
		Node:     c.GetConfig().SnowflakeConf.Node,
		Epoch:    c.GetConfig().SnowflakeConf.Epoch,
		NodeBits: c.GetConfig().SnowflakeConf.NodeBits,
		StepBits: c.GetConfig().SnowflakeConf.StepBits,
	})
	return snowflake
}

func initDB(c gzconfigcenter.ConfigManager[config.Config]) (*ent.Client, *gorm.DB) {
	dbConfig := model.DBConfig{
		Username:     c.GetConfig().DatabaseConf.Username,
		Password:     c.GetConfig().DatabaseConf.Password,
		Host:         c.GetConfig().DatabaseConf.Host,
		Port:         c.GetConfig().DatabaseConf.Port,
		DBName:       c.GetConfig().DatabaseConf.DBName,
		Type:         c.GetConfig().DatabaseConf.Type,
		MaxOpenConns: c.GetConfig().DatabaseConf.MaxOpenConns,
	}
	entDB := model.NewEntDB(dbConfig)
	gormDB := model.NewGormDB(dbConfig)
	return entDB, gormDB
}

func initMinio(c gzconfigcenter.ConfigManager[config.Config]) *minio.MinioClient {
	minioClient, err := minio.NewMinioClient(c.GetConfig().MinioConf)
	if err != nil {
		panic("init minioClient fail:" + err.Error())
	}

	// 确保存储桶存在
	err = minioClient.EnsureBucketExists(context.Background())
	if err != nil {
		panic("The bucket doesn't exist:" + err.Error())
	}
	return minioClient
}
