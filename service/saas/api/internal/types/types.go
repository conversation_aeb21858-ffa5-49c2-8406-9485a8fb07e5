// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.6

package types

type APICreateInfo struct {
	Status      bool   `json:"status,optional"`
	Path        string `json:"path,optional"`
	Description string `json:"description,optional"`
	ApiGroup    string `json:"apiGroup,optional"`
	Method      string `json:"method,optional"`
	Kind        string `json:"kind,optional"`
	Module      string `json:"module,optional"`
}

type APIInfo struct {
	BaseIDInfo
	Status      bool   `json:"status,optional"`
	Path        string `json:"path,optional"`
	Description string `json:"description,optional"`
	ApiGroup    string `json:"apiGroup,optional"`
	Method      string `json:"method,optional"`
	Kind        string `json:"kind,optional"`
	Module      string `json:"module,optional"`
}

type APIInfoResp struct {
	BaseDataInfo
	Data APIInfo `json:"data"`
}

type APIListInfo struct {
	BaseListInfo
	Data []APIInfo `json:"data"`
}

type APIListReq struct {
	PageInfo
	Path        string `json:"path,optional"`
	Description string `json:"description,optional"`
	ApiGroup    string `json:"apiGroup,optional"`
	Kind        string `json:"kind,optional"`
	Module      string `json:"module,optional"`
	CommonSearchInfo
}

type APIListResp struct {
	BaseDataInfo
	Data APIListInfo `json:"data"`
}

type AddOrganizationLeaderAndAdminReq struct {
	OrganizationID string   `json:"organizationId"`
	LeaderIds      []string `json:"leaderIds,optional"`
	AdminIds       []string `json:"adminIds,optional"`
}

type AddOrganizationUserReq struct {
	Status         bool     `json:"status,optional"`
	Username       string   `json:"username"`
	Password       string   `json:"password,omitempty,optional"`
	Nickname       string   `json:"nickname,optional"`
	Mobile         string   `json:"mobile,optional"`
	Email          string   `json:"email,optional"`
	Gender         string   `json:"gender,optional"`
	Post           string   `json:"post,optional"`
	AvatarID       string   `json:"avatarId,optional"`
	OrganizationId string   `json:"organizationId"`
	Sort           uint32   `json:"sort,optional"`
	Extra          string   `json:"extra,optional"`
	Kind           string   `json:"kind,optional"`
	DeviceNo       string   `json:"deviceNo,optional"`
	PositionIds    []string `json:"positionIds,optional"`
}

type AddTenantUserReq struct {
	Status   bool   `json:"status,optional"`
	Username string `json:"username"`
	Password string `json:"password,omitempty,optional"`
	Nickname string `json:"nickname,optional"`
	Mobile   string `json:"mobile,optional"`
	Email    string `json:"email,optional"`
	Gender   string `json:"gender,optional"`
	Post     string `json:"post,optional"`
	AvatarID string `json:"avatarId,optional"`
	TenantId string `json:"tenantId"`
	Sort     uint32 `json:"sort,optional"`
	Extra    string `json:"extra,optional"`
	Kind     string `json:"kind,optional"`
	Imei     string `json:"imei,optional"`
	DeviceNo string `json:"deviceNo,optional"`
}

type ApiAuthorityInfo struct {
	Id     string `json:"id" validate:"required"`
	Path   string `json:"path" validate="required,max=80"`
	Method string `json:"method" validate="required,min=3,max=4"`
}

type ApiAuthorityListInfo struct {
	BaseListInfo
	Data []ApiAuthorityInfo `json:"data"`
}

type ApiAuthorityListResp struct {
	BaseDataInfo
	Data ApiAuthorityListInfo `json:"data"`
}

type AuthenticateReq struct {
	Token string `json:"token"`
}

type AuthenticateResp struct {
	BaseDataInfo
	Data AuthenticateResult `json:"data"`
}

type AuthenticateResult struct {
	IsValid   bool                 `json:"isValid"`
	IsCreated bool                 `json:"isCreated"`
	UserID    string               `json:"userId"`
	TenantID  string               `json:"tenantId"`
	User      SuccessLoginUserInfo `json:"user"`
}

type AvatarInfo struct {
	Id   string `json:"id"`
	Name string `json:"name"`
	Url  string `json:"url"`
}

type BaseDataInfo struct {
	Code int         `json:"code"`
	Msg  string      `json:"msg"`
	Data interface{} `json:"data"`
}

type BaseIDInfo struct {
	Id string `json:"id"`
}

type BaseListInfo struct {
	Total uint64 `json:"total"`
	Data  string `json:"data"`
}

type BaseMsgResp struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

type BaseUUIDInfo struct {
	Id        string `json:"id"`
	CreatedAt int64  `json:"createdAt,optional"`
	UpdatedAt int64  `json:"updatedAt,optional"`
}

type ButtonCreateReq struct {
	Sort   uint32 `json:"sort,optional"`
	Name   string `json:"name,optional"`
	Code   string `json:"code,optional"`
	MenuId string `json:"menuId,optional"`
}

type ButtonInfo struct {
	BaseIDInfo
	Sort     uint32 `json:"sort,optional"`
	Name     string `json:"name,optional"`
	Code     string `json:"code,optional"`
	NodeType string `json:"nodeType,optional" default:"button"`
	MenuId   string `json:"menuId,optional"`
}

type ButtonInfoResp struct {
	BaseDataInfo
	Data ButtonInfo `json:"data"`
}

type ButtonListInfo struct {
	BaseListInfo
	Data []ButtonInfo `json:"data"`
}

type ButtonListReq struct {
	PageInfo
	Name   string `json:"name,optional" form:"name,optional"`
	Code   string `json:"code,optional" form:"code,optional"`
	MenuId string `json:"menuId,optional"  form:"menuId,optional"`
}

type ButtonListResp struct {
	BaseDataInfo
	Data ButtonListInfo `json:"data"`
}

type CaptchaInfo struct {
	CaptchaId string `json:"captchaId"`
	ImgPath   string `json:"imgPath"`
}

type CaptchaResp struct {
	BaseDataInfo
	Data CaptchaInfo `json:"data"`
}

type CheckUserHasRoleCodeReq struct {
	UserId   string `form:"userId"`
	RoleCode string `form:"roleCode"`
}

type CheckUserHasRoleCodeResp struct {
	BaseDataInfo
	Data bool `json:"data"`
}

type CommonSearchInfo struct {
	Search string `json:"search,optional" form:"search,optional"`
}

type CreateDictInfo struct {
	ID string `json:"id"`
}

type CreateDictResp struct {
	BaseDataInfo
	Data CreateDictInfo `json:"data"`
}

type CreateMoreHelpFileReq struct {
	FileID string `json:"fileId"`
}

type CreateOrUpdateApiAuthorityReq struct {
	RoleId string             `json:"roleId" validate:"required"`
	Data   []ApiAuthorityInfo `json:"data"`
}

type CustomWorkflowNodeInfo struct {
	NodeID       string   `json:"nodeId,optional"`
	NodeName     string   `json:"nodeName"`
	NodeKind     string   `json:"nodeKind"`
	SigningKind  string   `json:"signingKind"`
	ApprovalKind string   `json:"approvalKind"`
	ApproverIds  []string `json:"approverIds,optional"`
	CCKind       string   `json:"ccKind,optional"`
	CCIds        []string `json:"ccIds,optional"`
}

type DataExportRecordInfo struct {
	ID         string `json:"id,optional"`
	TaskID     string `json:"taskId"`
	FileID     string `json:"fileId,optional"`
	FileName   string `json:"fileName,optional"`
	ModuleName string `json:"moduleName,optional"`
	Status     uint   `json:"status"`
	UserID     string `json:"userId,optional"`
	CreatedAt  int64  `json:"createdAt,optional"`
}

type DataExportRecordListInfo struct {
	Total int64                  `json:"total"`
	Data  []DataExportRecordInfo `json:"data"`
}

type DataExportRecordListReq struct {
	PageInfo
	Search     string `form:"search,optional"`
	ModuleName string `form:"moduleName,optional"`
	Status     uint   `form:"status,optional"`
}

type DataExportRecordListResp struct {
	BaseDataInfo
	Data DataExportRecordListInfo `json:"data"`
}

type DeleteDictByIdReq struct {
	ID string `json:"id"`
}

type DeleteMoreHelpFileReq struct {
	ID string `json:"id"`
}

type DeleteOrganizationLeaderAndAdminReq struct {
	OrganizationID string   `json:"organizationId"`
	LeaderIds      []string `json:"leaderIds,optional"`
	AdminIds       []string `json:"adminIds,optional"`
}

type DeleteOrganizationUserInfoByIdsReq struct {
	IDsReq
	OrganizationId string `json:"organizationId"`
}

type DeleteTenantUserInfoByIdsReq struct {
	IDsReq
	TenantId string `json:"tenantId"`
}

type DepartmentWorkflowNodeInfo struct {
	Level       int      `json:"level,optional"`
	NodeKind    string   `json:"nodeKind"`
	SigningKind string   `json:"signingKind"`
	CCKind      string   `json:"ccKind,optional"`
	CCIds       []string `json:"ccIds,optional"`
}

type DictList struct {
	BaseListInfo
	Data []DictionaryInfo `json:"data"`
}

type DictListReq struct {
	PageInfo
	DictType string `form:"dictType"`
}

type DictListResp struct {
	BaseDataInfo
	Data DictList `json:"data"`
}

type DictPageData struct {
	BaseListInfo
	Data []DictionaryInfo `json:"data"`
}

type DictPageReq struct {
	PageInfo
	CommonSearchInfo
}

type DictPageResp struct {
	BaseDataInfo
	Data DictPageData `json:"data"`
}

type DictionaryInfo struct {
	ID          string                 `json:"id,optional"`
	DictType    string                 `json:"dictType"`
	DictCode    string                 `json:"dictCode"`
	DictOrder   int64                  `json:"dictOrder,optional"`
	DictName    string                 `json:"dictName,optional"`
	Description string                 `json:"description,optional"`
	Extra       map[string]interface{} `json:"extra,optional"`
	IsOpen      bool                   `json:"isOpen,default=true"`
	CreatedAt   int64                  `json:"createdAt,optional"`
	UpdatedAt   int64                  `json:"updatedAt,optional"`
}

type DragSortMoreHelpFileReq struct {
	ID       string `json:"id"`
	TargetID string `json:"targetId"`
	Position string `json:"position"`
}

type DragSortOrganizationUserReq struct {
	OrganizationId string `json:"organizationId"`
	UserId         string `json:"userId"`
	TargetId       string `json:"targetId"`
	Position       string `json:"position"`
}

type EmptyData struct {
}

type FileInfo struct {
	BaseIDInfo
	UUID       string `json:"UUID"`
	UserID     string `json:"userId"`
	OriginName string `json:"originName"`
	Name       string `json:"name"`
	FileType   uint8  `json:"fileType"`
	OpenStatus uint8  `json:"openStatus"`
	Size       uint64 `json:"size"`
	Path       string `json:"path"`
	Hash       string `json:"hash"`
	Url        string `json:"url"`
}

type FileListInfo struct {
	BaseListInfo
	Data []FileInfo `json:"data"`
}

type FileListReq struct {
	PageInfo
	FileType uint8    `json:"fileType,optional" validate:"omitempty,alpha,max=10"`
	FileName string   `json:"fileName,optional" validate:"max=50"`
	Period   []string `json:"period,optional"`
}

type FileListResp struct {
	BaseDataInfo
	Data FileListInfo `json:"data"`
}

type FileUploadSeccussCallBackReq struct {
	EventName string `json:"EventName"`
	Key       string `json:"Key"`
}

type ForceLogoutReq struct {
	TokenId string `json:"tokenId"`
}

type ForceLogoutResp struct {
}

type GenerateFileDownloadPreSignedUrlInfo struct {
	FileDownloadPreSignedUrl string `json:"fileDownloadPreSignedUrl"`
}

type GenerateFileDownloadPreSignedUrlReq struct {
	Id                   string `json:"id"`
	ExpirationTimeSecond int64  `json:"expirationTimeSecond,optional"`
	LoadType             int    `json:"loadType,optional"`
}

type GenerateFileDownloadPreSignedUrlResp struct {
	BaseDataInfo
	Data GenerateFileDownloadPreSignedUrlInfo `json:"data"`
}

type GenerateFileUploadPreSignedUrlInfo struct {
	Id                     string `json:"id"`
	FileType               string `json:"fileType`
	FileUploadPreSignedUrl string `json:"fileUploadPreSignedUrl"`
}

type GenerateFileUploadPreSignedUrlReq struct {
	FileName string `json:"fileName"`
	FileSize int64  `json:"fileSize"`
}

type GenerateFileUploadPreSignedUrlResp struct {
	BaseDataInfo
	Data GenerateFileUploadPreSignedUrlInfo `json:"data"`
}

type GenerateWatermarkedFileInfo struct {
	Id         string `json:"id"`
	PreviewUrl string `json:"previewUrl"`
}

type GenerateWatermarkedFileReq struct {
	Id            string  `json:"id"`
	WatermarkText string  `json:"watermarkText"`
	FontSize      int     `json:"fontSize,optional"`
	Color         string  `json:"color,optional"`
	Opacity       float64 `json:"opacity,optional"`
	Rotation      float64 `json:"rotation,optional"`
	XOffset       int     `json:"xOffset,optional"`
	YOffset       int     `json:"yOffset,optional"`
}

type GenerateWatermarkedFileResp struct {
	BaseDataInfo
	Data GenerateWatermarkedFileInfo `json:"data"`
}

type GetFileResp struct {
	BaseDataInfo
	Data FileInfo `json:"data"`
}

type GetGroupUsersReq struct {
	GroupId string `path:"groupId"`
	CommonSearchInfo
}

type GetGroupUsersResp struct {
	BaseDataInfo
	Data GroupUsersList `json:"data"`
}

type GetLoginLogItemResp struct {
	CreatedAt     int64   `json:"createdAt,optional"`
	DeviceKind    string  `json:"deviceKind,optional"`
	IP            string  `json:"ip,optional"`
	IPCity        string  `json:"ipCity,optional"`
	IPDistrict    string  `json:"ipDistrict,optional"`
	IPProvince    string  `json:"ipProvince,optional"`
	LoginUserName string  `json:"loginUserName"`
	TenantName    *string `json:"tenantName,optional"`
	UserName      *string `json:"userName,optional"`
}

type GetLoginLogsInfo struct {
	PageInfo
	BaseListInfo
	Data []GetLoginLogItemResp `json:"data"`
}

type GetLoginLogsReq struct {
	PageInfo
	CommonSearchInfo
	DeviceKind string `form:"deviceKind,optional"`
	BeginTime  *int64 `form:"beginTime,optional"`
	EndTime    *int64 `form:"endTime,optional"`
	UserID     string `form:"userId,optional"`
	Location   string `form:"location,optional"`
}

type GetLoginLogsResp struct {
	BaseDataInfo
	Data GetLoginLogsInfo `json:"data"`
}

type GetOnlineUsersReq struct {
}

type GetOnlineUsersResponse struct {
	OnlineUsers []OnlineUserInfo `json:"data"`
}

type GetOperationLogItemResp struct {
	API            string `json:"api,optional"`
	APIDescription string `json:"apiDescription,optional"`
	APIKind        string `json:"apiKind,optional"`
	APIModule      string `json:"apiModule,optional"`
	CreatedAt      int64  `json:"createdAt,optional"`
	IP             string `json:"ip,optional"`
	IsOK           bool   `json:"isOK,optional"`
	Method         string `json:"method,optional"`
	RequestBody    string `json:"requestBody,optional"`
	ResponseBody   string `json:"responseBody,optional"`
	TenantName     string `json:"tenantName,optional"`
	UserName       string `json:"userName,optional"`
}

type GetOperationLogsInfo struct {
	PageInfo
	BaseListInfo
	Data []GetOperationLogItemResp `json:"data"`
}

type GetOperationLogsReq struct {
	PageInfo
	APIKind   string `form:"apiKind,optional"`
	APIModule string `form:"apiModule,optional"`
	IsOK      *bool  `form:"isOK,optional"`
	Method    string `form:"method,optional"`
	CommonSearchInfo
	BeginTime *int64 `form:"beginTime,optional"`
	EndTime   *int64 `form:"endTime,optional"`
	UserID    string `form:"userId,optional"`
}

type GetOperationLogsResp struct {
	BaseDataInfo
	Data GetOperationLogsInfo `json:"data"`
}

type GetOrganizationUserInfoByIdReq struct {
	IDPathReq
	OrganizationId string `json:"organizationId,optional" form:"organizationId"`
}

type GetOrganizationUsersReq struct {
	OrganizationId string `json:"organizationId,optional" path:"organizationId"`
	CommonSearchInfo
}

type GetOrgsAndUsersInfo struct {
	OrgID  string `json:"orgId"`
	UserID string `json:"userId"`
}

type GetOrgsAndUsersReq struct {
	OrgID string `form:"orgId"`
}

type GetOrgsAndUsersResp struct {
	BaseDataInfo
	Data []GetOrgsAndUsersInfo `json:"data"`
}

type GetRoleUsersByRoleCodeReq struct {
	RoleCode string `form:"roleCode"`
}

type GetRoleUsersByRoleCodeResp struct {
	BaseDataInfo
	Data []UserInfo `json:"data"`
}

type GetRoleUsersReq struct {
	RoleId string `path:"roleId"`
	CommonSearchInfo
}

type GetRoleUsersResp struct {
	BaseDataInfo
	Data RoleUsersList `json:"data"`
}

type GetRolesByUserIDReq struct {
	UserID string `form:"userId,optional"`
}

type GetTenantUserInfoByIdReq struct {
	IDPathReq
	TenantId string `json:"tenantId,optional" form:"tenantId"`
}

type GetUserBelongDepartmentResp struct {
	BaseDataInfo
	Data []GetUserOrganizationInfo `json:"data"`
}

type GetUserGroupsAndCompaniesResp struct {
	BaseDataInfo
	Data []UserGroupsAndCompaniesInfo `json:"data"`
}

type GetUserListByMobileReq struct {
	Mobiles []string `json:"mobiles"`
}

type GetUserListByMobileResp struct {
	BaseDataInfo
	Data []UserInfo `json:"data"`
}

type GetUserOrganizationByIdReq struct {
	IDPathReq
}

type GetUserOrganizationByIdResp struct {
	BaseDataInfo
	Data []*GetUserOrganizationInfo `json:"data"`
}

type GetUserOrganizationInfo struct {
	Id   string `json:"id"`
	Name string `json:"name,optional"`
}

type GroupCreateReq struct {
	Status      bool   `json:"status,optional"`
	Sort        uint32 `json:"sort,optional"`
	TenantId    string `json:"tenantId,optional"`
	GroupTypeId string `json:"groupTypeId,optional"`
	Name        string `json:"name,optional"`
	Code        string `json:"code,optional"`
	Remark      string `json:"remark,optional"`
}

type GroupInfo struct {
	BaseIDInfo
	Status        bool   `json:"status,optional"`
	Sort          uint32 `json:"sort,optional"`
	TenantId      string `json:"tenantId,optional"`
	GroupTypeId   string `json:"groupTypeId,optional"`
	GroupTypeName string `json:"groupTypeName,optional"`
	Name          string `json:"name,optional"`
	Code          string `json:"code,optional"`
	Remark        string `json:"remark,optional"`
}

type GroupInfoResp struct {
	BaseDataInfo
	Data GroupInfo `json:"data"`
}

type GroupListInfo struct {
	BaseListInfo
	Data []GroupInfo `json:"data"`
}

type GroupListReq struct {
	PageInfo
	Name        string `json:"name,optional"`
	Code        string `json:"code,optional"`
	GroupTypeId string `json:"groupTypeId,optional"`
	TenantId    string `json:"tenantId,optional"`
	Remark      string `json:"remark,optional"`
	CommonSearchInfo
}

type GroupListResp struct {
	BaseDataInfo
	Data GroupListInfo `json:"data"`
}

type GroupTypeCreateReq struct {
	Status   bool   `json:"status,optional"`
	Sort     uint32 `json:"sort,optional"`
	TenantId string `json:"tenantId,optional"`
	Name     string `json:"name,optional"`
	Code     string `json:"code,optional"`
	Remark   string `json:"remark,optional"`
}

type GroupTypeInfo struct {
	BaseIDInfo
	Status   bool   `json:"status,optional"`
	Sort     uint32 `json:"sort,optional"`
	TenantId string `json:"tenantId,optional"`
	Name     string `json:"name,optional"`
	Code     string `json:"code,optional"`
	Remark   string `json:"remark,optional"`
}

type GroupTypeInfoLite struct {
	Id       string              `json:"id,optional"`
	Name     string              `json:"name,optional"`
	Level    uint32              `json:"level,optional"`
	Type     uint32              `json:"type,optional"`
	Children []GroupTypeInfoLite `json:"children,optional"`
}

type GroupTypeInfoResp struct {
	BaseDataInfo
	Data GroupTypeInfo `json:"data"`
}

type GroupTypeListInfo struct {
	BaseListInfo
	Data []GroupTypeInfo `json:"data"`
}

type GroupTypeListReq struct {
	PageInfo
	Name     string `json:"name,optional"`
	Code     string `json:"code,optional"`
	TenantId string `json:"tenantId,optional"`
	Remark   string `json:"remark,optional"`
	CommonSearchInfo
}

type GroupTypeListResp struct {
	BaseDataInfo
	Data GroupTypeListInfo `json:"data"`
}

type GroupTypeTreeReq struct {
	CommonSearchInfo
}

type GroupTypeTreeResp struct {
	BaseDataInfo
	Data []GroupTypeInfoLite `json:"data"`
}

type GroupUsersList struct {
	Data []UserInfo `json:"data"`
}

type IDPathReq struct {
	Id string `path:"id"`
}

type IDReq struct {
	Id string `json:"id" validate:"number"`
}

type IDsReq struct {
	Ids []string `json:"ids"`
}

type LoginReq struct {
	Username   string `json:"username" validate:"required"`
	Password   string `json:"password" validate:"required"`
	CaptchaId  string `json:"captchaId,optional"`
	Captcha    string `json:"captcha,optional"`
	Method     int64  `json:"method"`
	Mobile     string `json:"mobile"`
	DeviceNo   string `json:"deviceNo,optional"`
	DeviceKind int64  `json:"deviceKind"`
	DeviceKey  string `json:"deviceKey,optional"`
	Imei       string `json:"imei,optional"`
}

type LoginResp struct {
	BaseDataInfo
	Data SuccessLoginInfo `json:"data"`
}

type MenuAuthorityInfoReq struct {
	RoleId    string   `json:"roleId" validate:"required"`
	MenuIds   []string `json:"menuIds" validate:"required"`
	ButtonIds []string `json:"buttonIds" validate:"required"`
}

type MenuAuthorityInfoResp struct {
	BaseDataInfo
	Data MenuAuthorityInfoReq `json:"data"`
}

type MenuCreateInfo struct {
	Sort        uint32 `json:"sort,optional"`
	Name        string `json:"name"`
	Title       string `json:"title"`
	Icon        string `json:"icon,optional"`
	ParentId    string `json:"parentId,optional"`
	Url         string `json:"url"`
	Redirect    string `json:"redirect,optional"`
	Component   string `json:"component"`
	IsActive    bool   `json:"isActive,optional"`
	Hidden      bool   `json:"hidden,optional"`
	HiddenInTab bool   `json:"hiddenInTab,optional"`
	Fixed       bool   `json:"fixed,optional"`
	Remark      string `json:"remark,optional"`
	Meta        string `json:"meta,optional"`
	IsFullPage  bool   `json:"isFullPage,optional"`
}

type MenuCreateReq struct {
	MenuCreateInfo
}

type MenuInfo struct {
	BaseIDInfo
	Sort        uint32       `json:"sort,optional"`
	Name        string       `json:"name,optional"`
	Title       string       `json:"title,optional"`
	Icon        string       `json:"icon,optional"`
	ParentId    string       `json:"parentId,optional"`
	MenuType    uint32       `json:"menuType,optional"`
	Url         string       `json:"url,optional"`
	Redirect    string       `json:"redirect,optional"`
	Component   string       `json:"component,optional"`
	IsActive    bool         `json:"isActive,optional"`
	Hidden      bool         `json:"hidden,optional"`
	HiddenInTab bool         `json:"hiddenInTab,optional"`
	Fixed       bool         `json:"fixed,optional"`
	Remark      string       `json:"remark,optional"`
	NodeType    string       `json:"nodeType,optional" default:"menu"`
	Meta        string       `json:"meta,optional"`
	Children    []MenuInfo   `json:"children,optional"`
	Permissions []ButtonInfo `json:"permissions,optional"`
	IsFullPage  bool         `json:"isFullPage,optional"`
}

type MenuInfoResp struct {
	BaseDataInfo
	Data MenuInfo `json:"data"`
}

type MenuListInfo struct {
	BaseListInfo
	Data []MenuInfo `json:"data"`
}

type MenuListReq struct {
	PageInfo
	ParentId string `json:"parentId,optional"`
	Name     string `json:"name,optional"`
	Title    string `json:"title,optional"`
	Icon     string `json:"icon,optional"`
	CommonSearchInfo
}

type MenuListResp struct {
	BaseDataInfo
	Data MenuListInfo `json:"data"`
}

type MenuUpdateReq struct {
	IDReq
	MenuCreateInfo
}

type MoreHelpFileInfo struct {
	ID        string `json:"id"`
	FileID    string `json:"fileId"`
	FileName  string `json:"fileName"`
	FileType  string `json:"fileType"`
	IsOpen    bool   `json:"isOpen,optional"`
	CreatedAt int64  `json:"createdAt,optional"`
	UpdatedAt int64  `json:"updatedAt,optional"`
	CreatedBy string `json:"createdBy,optional"`
	UpdatedBy string `json:"updatedBy,optional"`
}

type MoreHelpFileListInfo struct {
	BaseListInfo
	Data []MoreHelpFileInfo `json:"data"`
}

type MoreHelpFileListResp struct {
	BaseDataInfo
	Data MoreHelpFileListInfo `json:"data"`
}

type OnlineUserInfo struct {
	DeviceKind string `json:"deviceKind"`
	IP         string `json:"ip"`
	IPCity     string `json:"ipCity"`
	IPDistrict string `json:"ipDistrict"`
	IPProvince string `json:"ipProvince"`
	Mobile     string `json:"mobile"`
	TenantName string `json:"tenantName"`
	UserID     string `json:"userId"`
	Username   string `json:"username"`
	Nickname   string `json:"nickname"`
	LoginDate  int64  `json:"loginDate"`
	TokenId    string `json:"tokenId"`
}

type OptionalBaseIDInfo struct {
	Id string `json:"id,optional"`
}

type OrganizationAdmins struct {
	AdminID   string `json:"adminId"`
	AdminName string `json:"adminName"`
}

type OrganizationCreateReq struct {
	Status   bool   `json:"status"`
	Sort     uint32 `json:"sort,optional"`
	TenantId string `json:"tenantId,optional"`
	Name     string `json:"name"`
	Code     string `json:"code"`
	NodeType uint32 `json:"nodeType,optional"`
	Leader   string `json:"leader,optional"`
	Phone    string `json:"phone,optional"`
	Email    string `json:"email,optional"`
	Remark   string `json:"remark,optional"`
	ParentId string `json:"parentId,optional"`
}

type OrganizationInfo struct {
	BaseIDInfo
	Status              bool                  `json:"status,optional"`
	Sort                uint32                `json:"sort,optional"`
	TenantId            string                `json:"tenantId,optional"`
	Name                string                `json:"name,optional"`
	Ancestors           string                `json:"ancestors,optional"`
	Code                string                `json:"code,optional"`
	NodeType            uint32                `json:"nodeType,optional"`
	Leader              string                `json:"leader,optional"`
	Phone               string                `json:"phone,optional"`
	Email               string                `json:"email,optional"`
	Remark              string                `json:"remark,optional"`
	ParentId            string                `json:"parentId,optional"`
	Children            []OrganizationInfo    `json:"children,optional"`
	UserCount           int64                 `json:"userCount,optional"`
	OrganizationLeaders []OrganizationLeaders `json:"organizationLeaders,optional"`
	OrganizationAdmins  []OrganizationAdmins  `json:"organizationAdmins,optional"`
}

type OrganizationInfoResp struct {
	BaseDataInfo
	Data OrganizationInfo `json:"data"`
}

type OrganizationLeaders struct {
	LeaderID   string `json:"leaderId"`
	LeaderName string `json:"leaderName"`
}

type OrganizationListByCodesReq struct {
	Codes []string `json:"codes"`
}

type OrganizationListByCodesResp struct {
	BaseDataInfo
	Data []OrganizationInfo `json:"data"`
}

type OrganizationListByNodeTypesReq struct {
	NodeTypes []uint32 `json:"nodeTypes"`
}

type OrganizationListByNodeTypesResp struct {
	BaseDataInfo
	Data []OrganizationInfo `json:"data"`
}

type OrganizationListInfo struct {
	BaseListInfo
	Data []OrganizationInfo `json:"data"`
}

type OrganizationListReq struct {
	PageInfo
	Name      string `json:"name,optional"`
	Ancestors string `json:"ancestors,optional"`
	Code      string `json:"code,optional"`
	TenantId  string `json:"tenantId,optional"`
	Extra     string `json:"extra,optional"`
	ParentID  string `form:"parentId,optional"`
	NoParent  bool   `form:"noParent,optional"`
	CommonSearchInfo
}

type OrganizationListResp struct {
	BaseDataInfo
	Data OrganizationListInfo `json:"data"`
}

type OrganizationUpdateReq struct {
	IDReq
	OrganizationCreateReq
}

type OrganizationUser struct {
	UserInfo
	Sort                     uint32                         `json:"sort,optional"`
	Extra                    string                         `json:"Extra,optional"`
	IsLeader                 bool                           `json:"isLeader,optional"`
	IsAdmin                  bool                           `json:"isAdmin,optional"`
	OrganizationUserPosition []OrganizationUserPositionInfo `json:"organizationUserPosition"`
}

type OrganizationUserInfo struct {
	ParentId          string                  `json:"parentId,optional"`
	OrgID             string                  `json:"orgId,optional"`
	OrgName           string                  `json:"orgName,optional"`
	OrganizationUsers []OrganizationUsers     `json:"userInfo,optional"`
	Children          []*OrganizationUserInfo `json:"children,optional"`
}

type OrganizationUserInfoInfo struct {
	BaseIDInfo
	Sort           uint32 `json:"sort,optional"`
	OrganizationId string `json:"organizationId,optional"`
	UserId         string `json:"userId,optional"`
	Extra          string `json:"extra,optional"`
}

type OrganizationUserInfoInfoResp struct {
	BaseDataInfo
	Data OrganizationUser `json:"data"`
}

type OrganizationUserInfoListInfo struct {
	BaseListInfo
	Data []OrganizationUser `json:"data"`
}

type OrganizationUserInfoListReq struct {
	PageInfo
	OrganizationId string `json:"organizationId,optional" form:"organizationId,optional"`
	CommonSearchInfo
}

type OrganizationUserInfoListResp struct {
	BaseDataInfo
	Data OrganizationUserInfoListInfo `json:"data"`
}

type OrganizationUserInfoTreeReq struct {
	OrganizationId      string `json:"organizationId,optional" form:"organizationId,optional"`
	DescendantNodeTypes []int  `json:"descendantNodeTypes,optional" form:"descendantNodeTypes,optional"`
	CommonSearchInfo
}

type OrganizationUserInfoTreeResp struct {
	BaseDataInfo
	Data []*OrganizationUserInfo `json:"data"`
}

type OrganizationUserPositionInfo struct {
	PositionId   string `json:"positionId"`
	PositionName string `json:"positionName"`
}

type OrganizationUsers struct {
	UserID   string `json:"userId,optional"`
	Nickname string `json:"nickname,optional"`
}

type PageInfo struct {
	Page     uint64 `json:"page,optional" validate:"number" form:"page,optional"`
	PageSize uint64 `json:"pageSize,optional" validate:"number,max=100000" form:"pageSize,optional"`
	NoPage   bool   `json:"noPage,optional" form:"noPage,optional"`
}

type PasswordFreeLoginReq struct {
	UserID        string `json:"userId,optional"`
	Mobile        string `json:"mobile,optional"`
	Username      string `json:"username,optional"`
	PlatUserId    string `json:"platUserId,optional"`
	PlatCode      string `json:"platCode,optional"`
	IsVirtualUser bool   `json:"isVirtualUser,optional"`
}

type PersonMixinInfo struct {
	CreatedBy string `json:"createdBy,optional"`
	UpdatedBy string `json:"updatedBy,optional"`
}

type PositionCreateReq struct {
	Status   bool   `json:"status,optional"`
	Sort     uint32 `json:"sort,optional"`
	TenantId string `json:"tenantId,optional"`
	Name     string `json:"name,optional"`
	Code     string `json:"code,optional"`
	Remark   string `json:"remark,optional"`
}

type PositionInfo struct {
	BaseIDInfo
	Status         bool   `json:"status,optional"`
	Sort           uint32 `json:"sort,optional"`
	TenantId       string `json:"tenantId,optional"`
	Name           string `json:"name,optional"`
	Code           string `json:"code,optional"`
	Remark         string `json:"remark,optional"`
	OrganizationID string `json:"organizationId,optional"`
}

type PositionInfoResp struct {
	BaseDataInfo
	Data PositionInfo `json:"data"`
}

type PositionListByIDsReq struct {
	PositionIds []string `json:"positionIds"`
}

type PositionListByIDsResp struct {
	BaseDataInfo
	Data []PositionInfo `json:"data"`
}

type PositionListInfo struct {
	BaseListInfo
	Data []PositionInfo `json:"data"`
}

type PositionListReq struct {
	PageInfo
	Name   string `json:"name,optional"`
	Code   string `json:"code,optional"`
	Remark string `json:"remark,optional"`
	Status string `json:"status,optional" form:"status,optional"`
	CommonSearchInfo
}

type PositionListResp struct {
	BaseDataInfo
	Data PositionListInfo `json:"data"`
}

type PresetTemplateBindReq struct {
	ID              string   `json:"id"`
	OrganizationIds []string `json:"organizationIds,optional"`
}

type PresetTemplateBindResp struct {
}

type PresetTemplateDetailReq struct {
	ID string `form:"id"`
}

type PresetTemplateDetailResp struct {
	PresetTemplateInfo
}

type PresetTemplateInfo struct {
	TimeMixinInfo
	PersonMixinInfo
	ID              string   `json:"id"`
	Name            string   `json:"name"`
	Kind            string   `json:"kind"`
	BusinessId      string   `json:"businessId"`
	OrganizationIds []string `json:"organizationIds,optional"`
}

type PresetTemplateListReq struct {
	CommonSearchInfo
	PageInfo
}

type PresetTemplateListResp struct {
	BaseListInfo
	Data []PresetTemplateInfo `json:"data"`
}

type PresetTemplateSaveReq struct {
	ID         string `json:"id,optional"`
	BusinessId string `json:"businessId"`
	Name       string `json:"name"`
	Kind       string `json:"kind"`
}

type PresetTemplateSaveResp struct {
}

type RoleCreateReq struct {
	Status        bool   `json:"status,optional"`
	Sort          uint32 `json:"sort,optional"`
	TenantId      string `json:"tenantId,optional"`
	Name          string `json:"name,optional"`
	Code          string `json:"code,optional"`
	DefaultRouter string `json:"defaultRouter,optional"`
	Remark        string `json:"remark,optional"`
	ParentId      string `json:"parentId,optional"`
}

type RoleInfo struct {
	BaseIDInfo
	Status         bool   `json:"status,optional"`
	Sort           uint32 `json:"sort,optional"`
	TenantId       string `json:"tenantId,optional"`
	Name           string `json:"name,optional"`
	Code           string `json:"code,optional"`
	DefaultRouter  string `json:"defaultRouter,optional"`
	Remark         string `json:"remark,optional"`
	ParentId       string `json:"parentId,optional"`
	OrganizationId string `json:"organizationId,optional"`
}

type RoleInfoResp struct {
	BaseDataInfo
	Data RoleInfo `json:"data"`
}

type RoleListByIDsReq struct {
	RoleIds []string `json:"roleIds"`
}

type RoleListByIDsResp struct {
	BaseDataInfo
	Data []RoleInfo `json:"data"`
}

type RoleListInfo struct {
	BaseListInfo
	Data []RoleInfo `json:"data"`
}

type RoleListReq struct {
	PageInfo
	Name           string `json:"name,optional"`
	Code           string `json:"code,optional"`
	TenantId       string `json:"tenantId,optional"`
	DefaultRouter  string `json:"defaultRouter,optional"`
	OrganizationId string `json:"organizationId,optional"`
	CommonSearchInfo
}

type RoleListResp struct {
	BaseDataInfo
	Data RoleListInfo `json:"data"`
}

type RoleUsersList struct {
	Data []UserInfo `json:"data"`
}

type RolesResp struct {
	BaseDataInfo
	Data []string `json:"data"`
}

type SimpleMsg struct {
	Msg string `json:"msg"`
}

type StatusCodeReq struct {
	Ids        []string `json:"ids"`
	OpenStatus uint64   `json:"openStatus" validate:"number"`
}

type SuccessLoginInfo struct {
	AccessToken   string               `json:"accessToken"`
	Expire        uint64               `json:"expire"`
	User          SuccessLoginUserInfo `json:"user"`
	SystemPlugins []SystemPlugin       `json:"systemPlugins,optional"`
	TenantInfos   []TenantInfo         `json:"tenantInfos,optional"`
}

type SuccessLoginUserInfo struct {
	BaseIDInfo
	Status          bool       `json:"status,optional"`
	DefaultTenantId string     `json:"defaultTenantId,optional"`
	Username        string     `json:"username,optional"`
	Nickname        string     `json:"nickname,optional"`
	Mobile          string     `json:"mobile,optional"`
	Email           string     `json:"email,optional"`
	Gender          string     `json:"gender,optional"`
	Post            string     `json:"post,optional"`
	Avatar          AvatarInfo `json:"avatar,optional"`
	Kind            string     `json:"kind,optional"`
}

type SystemLoginLogCreateReq struct {
	Logs []SystemLoginLogCreateReqInfo `json:"logs"`
}

type SystemLoginLogCreateReqInfo struct {
	UserID        string `json:"userId"`
	TenantID      string `json:"tenantId"`
	LoginUsername string `json:"loginUsername"`
	IP            string `json:"ip"`
	DeviceKind    string `json:"deviceKind"`
	CreatedAt     int64  `json:"createdAt"`
}

type SystemLoginLogCreateResp struct {
}

type SystemOperationLogCreateReq struct {
	Logs []SystemOperationLogCreateReqInfo `json:"logs"`
}

type SystemOperationLogCreateReqInfo struct {
	UserID       string `json:"userId"`
	TenantID     string `json:"tenantId"`
	Method       string `json:"method"`
	API          string `json:"api"`
	RequestBody  string `json:"requestBody,optional"`
	ResponseBody string `json:"responseBody,optional"`
	IP           string `json:"ip"`
	HttpStatus   int32  `json:"httpStatus"`
	CreatedAt    int64  `json:"createdAt"`
}

type SystemOperationLogCreateResp struct {
}

type SystemPlugin struct {
	ID   string `json:"id"`
	Name string `json:"name"`
	Code string `json:"code"`
}

type TemplateDetailReq struct {
	VersionID  string `form:"versionId,optional"`
	BusinessId string `form:"businessId,optional"`
}

type TemplateDetailResp struct {
	MainID            string                      `json:"mainId"`
	VersionId         string                      `json:"versionId"`
	BusinessId        string                      `json:"businessId"`
	Name              string                      `json:"name"`
	Kind              string                      `json:"kind"`
	PresetKind        string                      `json:"presetKind"`
	AutoApproveKind   string                      `json:"autoApproveKind"`
	TimeoutWarnStatus bool                        `json:"timeoutWarnStatus"`
	TimeoutHour       int                         `json:"timeoutHour"`
	Custom            []CustomWorkflowNodeInfo    `json:"custom"`
	Department        *DepartmentWorkflowNodeInfo `json:"department"`
}

type TemplateListItemInfo struct {
	ID         string `json:"id"`
	BusinessId string `json:"businessId"`
	VersionID  string `json:"versionId"`
	Name       string `json:"name"`
	VersionNo  int    `json:"versionNo"`
	TimeMixinInfo
	PersonMixinInfo
}

type TemplateListReq struct {
	CommonSearchInfo
	PageInfo
}

type TemplateListResp struct {
	BaseListInfo
	Data []TemplateListItemInfo `json:"data"`
}

type TemplateVersionEnableReq struct {
	VersionID string `json:"versionId"`
}

type TemplateVersionEnableResp struct {
}

type TemplateVersionInfo struct {
	ID        string `json:"id"`
	VersionNo int    `json:"versionNo"`
	Status    bool   `json:"status"`
	TimeMixinInfo
	PersonMixinInfo
}

type TemplateVersionListReq struct {
	ID string `form:"id"`
	CommonSearchInfo
	PageInfo
}

type TemplateVersionListResp struct {
	BaseListInfo
	Data []TemplateVersionInfo `json:"data"`
}

type TenantCreateReq struct {
	AfterSalesContact           string   `json:"afterSalesContact,optional"`
	LocationID                  string   `json:"locationId,optional"`
	LogSaveKeepDays             int64    `json:"logSaveKeepDays,optional"`
	MaxAttendanceUserCount      int64    `json:"maxAttendanceUserCount,optional"`
	MaxDeviceCount              int64    `json:"maxDeviceCount,optional"`
	MaxUploadFileSize           int64    `json:"maxUploadFileSize,optional"`
	MaxUserCount                int64    `json:"maxUserCount,optional"`
	Name                        string   `json:"name,optional"`
	Principal                   string   `json:"principal,optional"`
	PrincipalContactInformation string   `json:"principalContactInformation,optional"`
	SaleContact                 string   `json:"saleContact,optional"`
	ServiceEndAt                int64    `json:"serviceEndAt,optional"`
	ServiceStartAt              int64    `json:"serviceStartAt,optional"`
	Status                      bool     `json:"status,optional"`
	SystemPlugins               []string `json:"systemPlugins,optional"`
	SecretKey                   string   `json:"secretKey,optional"`
	AiStatus                    bool     `json:"aiStatus,optional"`
	MaxConferenceAgendaTitle    int64    `json:"maxConferenceAgendaTitle,optional"`
}

type TenantInfo struct {
	BaseIDInfo
	Uuid                        string         `json:"uuid,optional"`
	Key                         string         `json:"key,optional"`
	Secret                      string         `json:"secret,optional"`
	IsSuper                     bool           `json:"isSuper,optional"`
	AfterSalesContact           string         `json:"afterSalesContact,optional"`
	LocationID                  string         `json:"locationId,optional"`
	LogSaveKeepDays             int64          `json:"logSaveKeepDays,optional"`
	MaxAttendanceUserCount      int64          `json:"maxAttendanceUserCount,optional"`
	MaxDeviceCount              int64          `json:"maxDeviceCount,optional"`
	MaxUploadFileSize           int64          `json:"maxUploadFileSize,optional"`
	MaxUserCount                int64          `json:"maxUserCount,optional"`
	Name                        string         `json:"name,optional"`
	Principal                   string         `json:"principal,optional"`
	PrincipalContactInformation string         `json:"principalContactInformation,optional"`
	SaleContact                 string         `json:"saleContact,optional"`
	ServiceEndAt                int64          `json:"serviceEndAt,optional"`
	ServiceStartAt              int64          `json:"serviceStartAt,optional"`
	Status                      bool           `json:"status,optional"`
	SystemPlugins               []SystemPlugin `json:"systemPlugins,optional"`
	AiStatus                    bool           `json:"aiStatus,optional"`
	MaxConferenceAgendaTitle    int64          `json:"maxConferenceAgendaTitle,optional"`
}

type TenantInfoResp struct {
	BaseDataInfo
	Data TenantInfo `json:"data"`
}

type TenantListInfo struct {
	BaseListInfo
	Data []TenantInfo `json:"data"`
}

type TenantListReq struct {
	PageInfo
	Extra  string `json:"extra,optional"`
	Key    string `json:"key,optional"`
	Status bool   `json:"status,optional"`
	Name   string `json:"name,optional"`
	CommonSearchInfo
}

type TenantListResp struct {
	BaseDataInfo
	Data TenantListInfo `json:"data"`
}

type TenantUpdateReq struct {
	Uuid                        string   `json:"uuid,optional"`
	Key                         string   `json:"key,optional"`
	Secret                      string   `json:"secret,optional"`
	IsSuper                     bool     `json:"isSuper,optional"`
	AfterSalesContact           string   `json:"afterSalesContact,optional"`
	ID                          string   `json:"id"`
	LocationID                  string   `json:"locationId,optional"`
	LogSaveKeepDays             int64    `json:"logSaveKeepDays,optional"`
	MaxAttendanceUserCount      int64    `json:"maxAttendanceUserCount,optional"`
	MaxDeviceCount              int64    `json:"maxDeviceCount,optional"`
	MaxUploadFileSize           int64    `json:"maxUploadFileSize,optional"`
	MaxUserCount                int64    `json:"maxUserCount,optional"`
	Name                        string   `json:"name,optional"`
	Principal                   string   `json:"principal,optional"`
	PrincipalContactInformation string   `json:"principalContactInformation,optional"`
	SaleContact                 string   `json:"saleContact,optional"`
	ServiceEndAt                int64    `json:"serviceEndAt,optional"`
	ServiceStartAt              int64    `json:"serviceStartAt,optional"`
	Status                      bool     `json:"status,optional"`
	SystemPlugins               []string `json:"systemPlugins,optional"`
	AiStatus                    bool     `json:"aiStatus,optional"`
	MaxConferenceAgendaTitle    int64    `json:"maxConferenceAgendaTitle,optional"`
}

type TenantUser struct {
	UserInfo
	Sort  uint32 `json:"sort,optional"`
	Extra string `json:"Extra,optional"`
}

type TenantUserInfoInfo struct {
	BaseIDInfo
	Sort     uint32 `json:"sort,optional"`
	TenantId string `json:"tenantId,optional"`
	UserId   string `json:"userId,optional"`
	Extra    string `json:"extra,optional"`
}

type TenantUserInfoInfoResp struct {
	BaseDataInfo
	Data TenantUser `json:"data"`
}

type TenantUserInfoListInfo struct {
	BaseListInfo
	Data []TenantUser `json:"data"`
}

type TenantUserInfoListReq struct {
	PageInfo
	TenantId string `json:"tenantId,optional" form:"tenantId"`
	Kind     string `json:"kind,optional" form:"kind"`
	CommonSearchInfo
}

type TenantUserInfoListResp struct {
	BaseDataInfo
	Data TenantUserInfoListInfo `json:"data"`
}

type TimeMixinInfo struct {
	CreatedAt int64 `json:"createdAt,optional"`
	UpdatedAt int64 `json:"updatedAt,optional"`
}

type TokenCheckInfo struct {
	IsValid bool `json:"isValid"`
}

type TokenCheckReq struct {
	Token string `json:"token"`
}

type TokenCheckResp struct {
	BaseDataInfo
	Data TokenCheckInfo `json:"data"`
}

type TokenInfo struct {
	BaseIDInfo
	Status    bool   `json:"status,optional"`
	TenantId  string `json:"tenantId,optional"`
	Uid       string `json:"uid,optional"`
	Token     string `json:"token,optional"`
	Source    string `json:"source,optional"`
	ExpiredAt int64  `json:"expiredAt,optional"`
}

type TokenInfoResp struct {
	BaseDataInfo
	Data TokenInfo `json:"data"`
}

type TokenListInfo struct {
	BaseListInfo
	Data []TokenInfo `json:"data"`
}

type TokenListReq struct {
	PageInfo
	Token    string `json:"token,optional"`
	TenantId string `json:"tenantId,optional"`
	Source   string `json:"source,optional"`
	CommonSearchInfo
}

type TokenListResp struct {
	BaseDataInfo
	Data TokenListInfo `json:"data"`
}

type TokenRefreshReq struct {
	TenantId string `json:"tenantId,optional"`
}

type UUIDReq struct {
	Id string `json:"id" validate:"len=36"`
}

type UUIDsReq struct {
	Ids []string `json:"ids"`
}

type UpdataGroupUsersReq struct {
	GroupId string   `json:"groupId"`
	UserIds []string `json:"userIds"`
	Kind    string   `json:"kind,optional"`
}

type UpdataRoleUsersReq struct {
	RoleId  string   `json:"roleId"`
	UserIds []string `json:"userIds"`
}

type UpdateDataExportRecordReq struct {
	ID string `json:"id"`
}

type UpdateFileReq struct {
	ID   string `json:"id"`
	Name string `json:"name" validate:"max=50"`
}

type UpdateMoreHelpFileReq struct {
	ID     string `json:"id"`
	FileID string `json:"fileId"`
}

type UpdateOrganizationUserReq struct {
	BaseIDInfo
	AddOrganizationUserReq
}

type UpdateOrganizationUsersReq struct {
	OrganizationId string   `json:"organizationId"`
	UserIds        []string `json:"userIds"`
}

type UpdatePermissionsReq struct {
	Menus []map[string]interface{} `json:"menus,optional"`
	Apis  []map[string]interface{} `json:"apis,optional"`
}

type UpdateTemplateReq struct {
	ID                string                     `json:"id"`
	Name              string                     `json:"name"`
	Kind              string                     `json:"kind"`
	AutoApproveKind   string                     `json:"autoApproveKind"`
	TimeoutWarnStatus bool                       `json:"timeoutWarnStatus"`
	TimeoutHour       int                        `json:"timeoutHour,optional"`
	Custom            []CustomWorkflowNodeInfo   `json:"custom,optional"`
	Department        DepartmentWorkflowNodeInfo `json:"department,optional"`
}

type UpdateTemplateResp struct {
	ID         string                     `json:"id"`
	BusinessId string                     `json:"businessId"`
	Name       string                     `json:"name"`
	Kind       string                     `json:"kind"`
	PresetKind string                     `json:"presetKind"`
	Custom     []CustomWorkflowNodeInfo   `json:"custom"`
	Department DepartmentWorkflowNodeInfo `json:"department"`
}

type UpdateTenantUserReq struct {
	BaseIDInfo
	AddTenantUserReq
}

type UpdateTenantUsersReq struct {
	TenantId string   `json:"tenantId"`
	UserIds  []string `json:"userIds"`
	Kind     string   `json:"kind"`
}

type UpdateUsersPasswordReq struct {
	OperatorPW string   `json:"operatorPW,optional"`
	NewPass    string   `json:"newPass,optional"`
	UserIds    []string `json:"userIds"`
}

type UploadInfo struct {
	Id         string `json:"id"`
	OriginName string `json:"originName"`
	Name       string `json:"name"`
	Hash       string `json:"hash"`
	Url        string `json:"url"`
}

type UploadResp struct {
	BaseDataInfo
	Data UploadInfo `json:"data"`
}

type UserByMobileReq struct {
	Mobile string `form:"mobile"`
}

type UserByMobileResp struct {
	BaseDataInfo
	Data UserInfo `json:"data"`
}

type UserCreateReq struct {
	Status          bool     `json:"status,optional"`
	Username        string   `json:"username"`
	Password        string   `json:"password,omitempty"`
	Nickname        string   `json:"nickname,optional"`
	Mobile          string   `json:"mobile,optional"`
	Email           string   `json:"email,optional"`
	Gender          string   `json:"gender,optional"`
	Post            string   `json:"post,optional"`
	AvatarId        string   `json:"avatarId,optional"`
	DefaultTenantId string   `json:"tenantId,optional"`
	DeviceNo        string   `json:"deviceNo,optional"`
	Kind            string   `json:"kind,optional"`
	Imei            string   `json:"imei,optional"`
	OrganizationIDs []string `json:"organizationIds,optional"`
}

type UserGroupsAndCompaniesInfo struct {
	Label string `json:"label"`
	Value string `json:"value"`
}

type UserInfo struct {
	BaseIDInfo
	Status          bool         `json:"status,optional"`
	Username        string       `json:"username,optional"`
	Password        string       `json:"password,optional,omitempty"`
	Nickname        string       `json:"nickname,optional"`
	Mobile          string       `json:"mobile,optional"`
	Email           string       `json:"email,optional"`
	Gender          string       `json:"gender,optional"`
	Post            string       `json:"post,optional"`
	AvatarId        string       `json:"avatarId,optional"`
	Avatar          AvatarInfo   `json:"avatar,optional"`
	DefaultTenantId string       `json:"DefaultTenantId,optional"`
	DeviceNo        string       `json:"deviceNo,optional"`
	Kind            string       `json:"kind,optional"`
	Imei            string       `json:"imei,optional"`
	TenantInfos     []TenantInfo `json:"tenantInfos,optional"`
	OrganizationIDs []string     `json:"organizationIDs,optional"`
}

type UserInfoResp struct {
	BaseDataInfo
	Data UserInfo `json:"data"`
}

type UserInfoWithExtraInfo struct {
	BaseIDInfo
	Status                   bool                   `json:"status,optional"`
	Username                 string                 `json:"username,optional"`
	Nickname                 string                 `json:"nickname,optional"`
	Mobile                   string                 `json:"mobile,optional"`
	Email                    string                 `json:"email,optional"`
	Gender                   string                 `json:"gender,optional"`
	Post                     string                 `json:"post,optional"`
	AvatarId                 string                 `json:"avatarId,optional"`
	Avatar                   AvatarInfo             `json:"avatar,optional"`
	Kind                     string                 `json:"kind,optional"`
	Imei                     string                 `json:"imei,optional"`
	DefaultTenantId          string                 `json:"DefaultTenantId,optional"`
	SSOUserInfo              map[string]interface{} `json:"ssoUserInfo,omitempty"`
	Tenants                  []TenantInfo           `json:"tenants,optional"`
	SystemPlugins            []SystemPlugin         `json:"systemPlugins,optional"`
	MaxConferenceAgendaTitle int64                  `json:"maxConferenceAgendaTitle,optional"`
	CurrentOrganization      string                 `json:"currentOrganization,optional"`
	IsAdmin                  bool                   `json:"isAdmin,optional"`
}

type UserInfoWithExtraInfoResp struct {
	BaseDataInfo
	Data UserInfoWithExtraInfo `json:"data"`
}

type UserListInfo struct {
	BaseListInfo
	Data []UserInfo `json:"data"`
}

type UserListPReq struct {
	PageInfo
	Username     string `json:"username,optional"`
	Nickname     string `json:"nickname,optional"`
	Mobile       string `json:"mobile,optional"`
	Email        string `json:"email,optional"`
	IgnoreTenant bool   `json:"ignoreTenant,optional"`
	CommonSearchInfo
	Ids []string `json:"ids,optional"`
}

type UserListPResp struct {
	BaseDataInfo
	Data UserListInfo `json:"data"`
}

type UserListReq struct {
	PageInfo
	Username     string `json:"username,optional" form:"username,optional"`
	Nickname     string `json:"nickname,optional " form:"nickname,optional"`
	Mobile       string `json:"mobile,optional " form:"mobile,optional"`
	Email        string `json:"email,optional " form:"email,optional"`
	IgnoreTenant bool   `json:"ignoreTenant,optional " form:"ignoreTenant,optional"`
	Kind         string `json:"kind,optional " form:"kind,optional"`
	Imei         string `json:"imei,optional" form:"imei,optional"`
	DeviceNo     string `json:"deviceNo,optional" form:"deviceNo,optional"`
	Status       *bool  `json:"status,optional" form:"status,optional"`
	CommonSearchInfo
}

type UserListResp struct {
	BaseDataInfo
	Data UserListInfo `json:"data"`
}

type UserNicknamesByIDsReq struct {
	IDs []string `json:"ids"`
}

type UserNicknamesByIDsResp struct {
	BaseDataInfo
	Data map[string]string `json:"data"`
}

type UserSimpleInfo struct {
	Id       string `json:"id"`
	Nickname string `json:"nickname"`
}

type UserSimpleInfoListResp struct {
	BaseDataInfo
	Data []UserSimpleInfo `json:"data"`
}

type WorkflowApproveReq struct {
	TaskId  string                 `json:"taskId"`
	Comment string                 `json:"comment"`
	Status  string                 `json:"status"`
	Extra   map[string]interface{} `json:"extra"`
}

type WorkflowApproveResp struct {
}

type WorkflowCCReadReq struct {
	CCIds []string `json:"ccIds"`
}

type WorkflowCCReadResp struct {
}

type WorkflowCancelReq struct {
	FlowId string `json:"flowId"`
}

type WorkflowCancelResp struct {
}

type WorkflowDetailReq struct {
	FlowId string `form:"flowId"`
}

type WorkflowDetailResp struct {
	FlowName                string             `json:"flowName"`
	FlowId                  string             `json:"flowId"`
	FlowStatus              string             `json:"flowStatus"`
	FlowCreatedUserNickname string             `json:"flowCreatedUserNickname"`
	FlowCreatedTime         int64              `json:"flowCreatedTime"`
	FormContent             string             `json:"formContent"`
	Nodes                   []WorkflowNodeInfo `json:"nodes"`
}

type WorkflowInitiatesInfo struct {
	FlowName                     string `json:"flowName"`
	CurrentNodeName              string `json:"currentNodeName"`
	CurrentNodeApproverNicknames string `json:"currentNodeApproverNicknames"`
	FlowCreatedTime              int64  `json:"flowCreatedTime"`
	FlowStatus                   string `json:"flowStatus"`
	FlowId                       string `json:"flowId"`
}

type WorkflowInitiatesReq struct {
	CommonSearchInfo
	PageInfo
	FlowCreatedUserId    string `form:"flowCreatedUserId,optional"`
	OrganizationId       string `form:"organizationId,optional"`
	FlowCreatedTimeBegin int64  `form:"flowCreatedTimeBegin,optional"`
	FlowCreatedTimeEnd   int64  `form:"flowCreatedTimeEnd,optional"`
	FlowStatus           string `form:"flowStatus,optional"`
	FlowNodeApproverId   string `form:"flowNodeApproverId,optional"`
}

type WorkflowInitiatesResp struct {
	BaseListInfo
	Data []WorkflowInitiatesInfo `json:"data"`
}

type WorkflowMonitorListInfo struct {
	FlowName                string                     `json:"flowName"`
	FlowCreatedUserNickname string                     `json:"flowCreatedUserNickname"`
	FlowCreatedTime         int64                      `json:"flowCreatedTime"`
	FlowStatus              string                     `json:"flowStatus"`
	CurrentNodeName         string                     `json:"currentNodeName"`
	CurrentNodeId           string                     `json:"currentNodeId"`
	CurrentNodeApprover     []WorkflowNodeReviewerInfo `json:"currentNodeApprover"`
	WarnStatus              string                     `json:"warnStatus"`
	WorkflowId              string                     `json:"workflowId"`
}

type WorkflowMonitorListReq struct {
	CommonSearchInfo
	PageInfo
	FlowCreatedUserId    string `form:"flowCreatedUserId,optional"`
	FlowCreatedTimeBegin int64  `form:"flowCreatedTimeBegin,optional"`
	FlowCreatedTimeEnd   int64  `form:"flowCreatedTimeEnd,optional"`
	FlowStatus           string `form:"flowStatus,optional"`
	FlowNodeApproverId   string `form:"flowNodeApproverId,optional"`
}

type WorkflowMonitorListResp struct {
	BaseListInfo
	Data []WorkflowMonitorListInfo `json:"data"`
}

type WorkflowMonitorNodeApproverAddReq struct {
	NodeId     string `json:"nodeId"`
	ApproverId string `json:"approverId"`
}

type WorkflowMonitorNodeApproverAddResp struct {
}

type WorkflowMonitorNodeApproverUpdateReq struct {
	TaskId     string `json:"taskId"`
	ApproverId string `json:"approverId"`
}

type WorkflowMonitorNodeApproverUpdateResp struct {
}

type WorkflowNodeInfo struct {
	NodeName    string                     `json:"nodeName"`
	NodeId      string                     `json:"nodeId"`
	Status      string                     `json:"status"`
	SigningKind string                     `json:"signingKind"`
	UpdatedAt   int64                      `json:"updatedAt"`
	Approvers   []WorkflowNodeReviewerInfo `json:"approvers"`
}

type WorkflowNodeReviewerInfo struct {
	TaskId           string                 `json:"taskId"`
	ApproverId       string                 `json:"approverId"`
	ApproverNickname string                 `json:"approverNickname"`
	Status           string                 `json:"status"`
	UpdatedAt        int64                  `json:"updatedAt"`
	Comment          string                 `json:"comment"`
	Extra            map[string]interface{} `json:"extra"`
}

type WorkflowNodeStartInfo struct {
	NodeId      string   `json:"nodeId"`
	CCIds       []string `json:"ccIds,optional"`
	ApproverIds []string `json:"approverIds,optional"`
}

type WorkflowStartReq struct {
	DepartmentId  string                  `json:"departmentId,optional"`
	FlowVersionId string                  `json:"flowVersionId"`
	BusinessId    string                  `json:"businessId"`
	FormContent   string                  `json:"formContent"`
	CCIds         []string                `json:"ccIds,optional"`
	Nodes         []WorkflowNodeStartInfo `json:"nodes,optional"`
}

type WorkflowStartResp struct {
	FlowId string `json:"flowId"`
}

type WorkflowTaskCCsInfo struct {
	CCId                    string `json:"ccId"`
	FlowName                string `json:"flowName"`
	CCTime                  int64  `json:"ccTime"`
	FlowCreatedUserNickname string `json:"flowCreatedUserNickname"`
	FlowCreatedTime         int64  `json:"flowCreatedTime"`
	FlowStatus              string `json:"flowStatus"`
	ConsultStatus           bool   `json:"consultStatus"`
	FlowId                  string `json:"flowId"`
}

type WorkflowTaskCCsReq struct {
	CommonSearchInfo
	PageInfo
	FlowCreatedUserId    string `form:"flowCreatedUserId,optional"`
	OrganizationId       string `form:"organizationId,optional"`
	CCTimeBegin          int64  `form:"ccTimeBegin,optional"`
	CCTimeEnd            int64  `form:"ccTimeEnd,optional"`
	FlowCreatedTimeBegin int64  `form:"flowCreatedTimeBegin,optional"`
	FlowCreatedTimeEnd   int64  `form:"flowCreatedTimeEnd,optional"`
	FlowStatus           string `form:"flowStatus,optional"`
	ConsultStatus        *bool  `form:"consultStatus,optional"`
}

type WorkflowTaskCCsResp struct {
	BaseListInfo
	Data []WorkflowTaskCCsInfo `json:"data"`
}

type WorkflowTaskDoneInfo struct {
	FlowName                string `json:"flowName"`
	TaskCompletedTime       int64  `json:"taskCompletedTime"`
	FlowCreatedUserNickname string `json:"flowCreatedUserNickname"`
	FlowCreatedTime         int64  `json:"flowCreatedTime"`
	FlowId                  string `json:"flowId"`
	FlowStatus              string `json:"flowStatus"`
}

type WorkflowTaskDoneReq struct {
	CommonSearchInfo
	PageInfo
	FlowCreatedUserId      string `form:"flowCreatedUserId,optional"`
	OrganizationId         string `form:"organizationId,optional"`
	TaskCompletedTimeBegin int64  `form:"taskCompletedTimeBegin,optional"`
	TaskCompletedTimeEnd   int64  `form:"taskCompletedTimeEnd,optional"`
	FlowCreatedTimeBegin   int64  `form:"flowCreatedTimeBegin,optional"`
	FlowCreatedTimeEnd     int64  `form:"flowCreatedTimeEnd,optional"`
	FlowStatus             string `form:"flowStatus,optional"`
}

type WorkflowTaskDoneResp struct {
	BaseListInfo
	Data []WorkflowTaskDoneInfo `json:"data"`
}

type WorkflowTaskTodoInfo struct {
	ID                      string `json:"id"`
	FlowName                string `json:"flowName"`
	CurrentNodeName         string `json:"currentNodeName"`
	OrganizationName        string `json:"organizationName"`
	FlowCreatedUserNickname string `json:"flowCreatedUserNickname"`
	FlowCreatedTime         int64  `json:"flowCreatedTime"`
	TaskCreatedTime         int64  `json:"taskCreatedTime"`
	FlowId                  string `json:"flowId"`
	TaskId                  string `json:"taskId"`
	NodeId                  string `json:"nodeId"`
}

type WorkflowTaskTodosReq struct {
	CommonSearchInfo
	PageInfo
	FlowCreatedUserId    string `form:"flowCreatedUserId,optional"`
	OrganizationId       string `form:"organizationId,optional"`
	TaskCreatedTimeBegin int64  `form:"taskCreatedTimeBegin,optional"`
	TaskCreatedTimeEnd   int64  `form:"taskCreatedTimeEnd,optional"`
	FlowCreatedTimeBegin int64  `form:"flowCreatedTimeBegin,optional"`
	FlowCreatedTimeEnd   int64  `form:"flowCreatedTimeEnd,optional"`
}

type WorkflowTaskTodosResp struct {
	BaseListInfo
	Data []WorkflowTaskTodoInfo `json:"data"`
}
