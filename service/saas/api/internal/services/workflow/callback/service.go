package callback

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"net/http"
	"time"

	"phoenix/service/saas/api/internal/errs"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"github.com/go-resty/resty/v2"
	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

// Request 回调请求结构
type Request struct {
	TenantID       string `json:"tenantId"`
	OrganizationID string `json:"organizationId"`
	WorkflowID     string `json:"workflowId"`
	SponsorID      string `json:"sponsorId"`
	FormContent    string `json:"formContent"`
	CreatedAt      int64  `json:"createdAt"`
	BusinessID     string `json:"businessId"` // 审批流程唯一序列号
	BusinessCode   string `json:"businessCode"`
}

// Response 回调响应结构
type Response struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
}

// Service 回调服务接口
type Service interface {
	Execute(ctx context.Context, callbackReq *Request) error
}

// service 是 Service 接口的私有实现
type service struct {
	db     *gorm.DB
	client *resty.Client
}

// NewService 创建回调服务
func NewService(db *gorm.DB) Service {
	client := resty.New()
	client.SetRetryCount(0) // 手动控制重试

	return &service{
		db:     db,
		client: client,
	}
}

// Execute 执行前置回调
func (s *service) Execute(ctx context.Context, callbackReq *Request) error {
	// 查找回调配置
	client := mapper.NewWorkflowStartCallbackConfigClient(s.db)
	config, err := client.GetByBusinessID(ctx, callbackReq.BusinessID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			logc.Infof(ctx, "未找到业务ID对应的回调配置，跳过前置回调 business_id: %s", callbackReq.BusinessID)
			return nil // 未找到配置，默认允许通过
		}
		return fmt.Errorf("查询回调配置失败: %w", err)
	}

	// 检查配置是否启用
	if !config.IsEnabled {
		logc.Infof(ctx, "回调配置已禁用，跳过前置回调 business_id: %s", callbackReq.BusinessID)
		return nil // 配置禁用，默认允许通过
	}

	// 执行回调（包含重试逻辑）
	logc.Infof(ctx, "前置回调 business_id: %s, callback_url: %s", callbackReq.BusinessID, config.CallbackURL)
	response, err := s.executeCallbackWithRetry(ctx, config, callbackReq)
	if err != nil {
		// 根据失败策略处理结果
		if config.FailurePolicy == "DENY" {
			return fmt.Errorf("前置回调失败，拒绝审批: %w", err)
		}
		logc.Errorw(ctx, "前置回调失败但采用ALLOW策略，继续审批", logx.Field("business_id", callbackReq.BusinessID), logx.Field("error", err))
		return nil // 失败但策略允许，继续
	}

	if response.Code != 0 {
		if config.FailurePolicy == "DENY" {
			return errs.New(response.Code, http.StatusOK, response.Msg)
		}
		logc.Errorw(ctx, "前置回调业务校验失败但采用ALLOW策略，继续审批", logx.Field("business_id", callbackReq.BusinessID), logx.Field("response_msg", response.Msg))
		return nil // 业务校验失败但策略允许，继续
	}

	return nil
}

// executeCallbackWithRetry 执行回调（带重试）
func (s *service) executeCallbackWithRetry(ctx context.Context, config *mapper.WorkflowStartCallbackConfig, request *Request) (*Response, error) {
	var lastError error
	var response *Response

	for attempt := 0; attempt <= config.RetryCount; attempt++ {
		if attempt > 0 {
			time.Sleep(time.Duration(attempt) * time.Second)
		}

		startTime := time.Now()
		resp, err := s.doHTTPRequest(ctx, config, request)
		executionTime := time.Since(startTime)

		if err != nil {
			lastError = err
			logc.Errorw(ctx, "回调请求失败", logx.Field("attempt", attempt+1), logx.Field("error", err), logx.Field("execution_time", executionTime))
			continue
		}

		if resp.IsSuccess() {
			if err := json.Unmarshal(resp.Body(), &response); err != nil {
				lastError = fmt.Errorf("解析回调响应失败: %w", err)
				continue
			}
			logc.Infof(ctx, "回调执行成功 business_id: %s, execution_time: %v", config.BusinessID, executionTime)
			return response, nil
		}
		lastError = fmt.Errorf("HTTP请求失败，状态码: %d, body: %s", resp.StatusCode(), string(resp.Body()))
	}

	logc.Errorf(ctx, "回调执行失败，已用尽所有重试 business_id: %s, error: %v", config.BusinessID, lastError)
	return nil, lastError
}

// doHTTPRequest 执行HTTP请求
func (s *service) doHTTPRequest(ctx context.Context, config *mapper.WorkflowStartCallbackConfig, requestData any) (*resty.Response, error) {
	s.client.SetTimeout(time.Duration(config.TimeoutMs) * time.Millisecond)
	req := s.client.R().
		SetContext(ctx).
		SetHeader("Content-Type", "application/json").
		SetHeader("X-Session-Trace-Id", utils.GetContextTraceId(ctx)).
		SetBody(requestData)

	if err := s.setAuthentication(req, config); err != nil {
		return nil, fmt.Errorf("设置认证失败: %w", err)
	}
	logc.Infof(ctx, "前置回调请求: %s, request: %v", config.CallbackURL, requestData)

	switch config.CallbackMethod {
	case "POST":
		return req.Post(config.CallbackURL)
	case "PUT":
		return req.Put(config.CallbackURL)
	default:
		return nil, fmt.Errorf("不支持的HTTP方法: %s", config.CallbackMethod)
	}
}

// setAuthentication 设置认证
func (s *service) setAuthentication(req *resty.Request, config *mapper.WorkflowStartCallbackConfig) error {
	switch config.AuthType {
	case "NONE":
		return nil
	case "TOKEN":
		var authConfig struct {
			Token string `json:"token"`
		}
		if err := json.Unmarshal(config.AuthConfig, &authConfig); err != nil {
			return err
		}
		req.SetAuthToken(authConfig.Token)
	default:
		return nil
	}
	return nil
}
