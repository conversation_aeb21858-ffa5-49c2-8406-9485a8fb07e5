package services

// APIInfo 表示 API 信息
type APIInfo struct {
	Id          string
	CreatedAt   int64
	UpdatedAt   int64
	Status      bool
	Path        string
	Description string
	ApiGroup    string
	Method      string
	Kind        string
	Module      string
}

// GetOrganizationUsersReq 表示获取组织用户的请求
type GetOrganizationUsersReq struct {
	OrganizationId string
	Search         string
	UserId         string
}

// OrganizationUserInfoListResp 表示组织用户信息列表的响应
type OrganizationUserInfoListResp struct {
	Total uint64
	Data  []*OrganizationUserInfoInfo
}

// TenantUserInfoListReq 表示租户用户信息列表的请求
type TenantUserInfoListReq struct {
	Page     uint64
	PageSize uint64
	NoPage   bool
	Search   string
}

// APIListReq 表示 API 列表的请求
type APIListReq struct {
	Page        uint64
	PageSize    uint64
	NoPage      bool
	Path        string
	Description string
	ApiGroup    string
	RoleId      string
	Search      string
	Kind        string
	Module      string
}

// GroupUserInfoLite 表示简化的组用户信息
type GroupUserInfoLite struct {
	Id       string
	Nickname string
	Avatar   *Avatar
	Gender   string
	Post     string
}

// PositionInfo 表示职位信息
type PositionInfo struct {
	Id             string
	CreatedAt      int64
	UpdatedAt      int64
	Status         bool
	Sort           uint32
	TenantId       string
	Name           string
	Code           string
	Remark         string
	OrganizationId string
}

// GetTenantUsersReq 表示获取租户用户的请求
type GetTenantUsersReq struct {
	TenantId string
	Search   string
	UserId   string
	Kind     string
	PageInfoReq
}

// TenantUserInfoInfo 表示租户用户信息
type TenantUserInfoInfo struct {
	Id        string
	CreatedAt int64
	UpdatedAt int64
	Sort      uint32
	TenantId  string
	UserId    string
	Extra     string
}

// Empty 表示空消息
type Empty struct{}

// UpdateGroupUsersReq 表示更新组用户的请求
type UpdateGroupUsersReq struct {
	GroupId string
	UserIds []string
	Kind    string
}

// GroupUser 表示组用户
type GroupUser struct {
	Id              string
	CreatedAt       int64
	Status          bool
	Username        string
	Nickname        string
	Mobile          string
	Email           string
	Avatar          *Avatar
	DefaultTenantId string
}

// GroupTypeInfo 表示组类型信息
type GroupTypeInfo struct {
	Id        string
	CreatedAt int64
	UpdatedAt int64
	Status    bool
	Sort      uint32
	TenantId  string
	Name      string
	Code      string
	Remark    string
}

// OrganizationUserInfoUserInfoListResp 表示组织用户信息用户信息列表的响应
type OrganizationUserInfoUserInfoListResp struct {
	Total uint64
	Data  []*OrganizationUserInfoUserInfo
}

// PageInfoReq 表示分页信息请求
type PageInfoReq struct {
	Page     uint64
	PageSize uint64
	NoPage   bool
}

// OrganizationUserInfoUserInfoListAllResp 表示组织用户信息用户信息列表的全部响应
type OrganizationUserInfoUserInfoListAllResp struct {
	Data []*OrganizationUserInfoOrganizationInfoLite
}

// GroupTypeListTreeResp 表示组类型列表树的响应
type GroupTypeListTreeResp struct {
	Data []*GroupTypeInfoLite
}

// TenantListResp 表示租户列表的响应
type TenantListResp struct {
	Total uint64
	Data  []*TenantInfo
}

// IDsReq 表示 Id 请求
type IDsReq struct {
	Ids []string
}

// GroupTypeListResp 表示组类型列表的响应
type GroupTypeListResp struct {
	Total uint64
	Data  []*GroupTypeInfo
}

// RoleUser 表示角色用户
type RoleUser struct {
	Id              string
	CreatedAt       int64
	Status          bool
	Username        string
	Nickname        string
	Mobile          string
	Email           string
	Avatar          *Avatar
	DefaultTenantId string
}

// UpdateUsersPasswordReq 表示更新用户密码的请求
type UpdateUsersPasswordReq struct {
	UserIds []string
	NewPass string
}

// RoleMenuAuthorityResp 表示角色菜单权限的响应
type RoleMenuAuthorityResp struct {
	MenuIds   []string
	ButtonIds []string
}

// GroupListReq 表示组列表的请求
type GroupListReq struct {
	Page     uint64
	PageSize uint64
	NoPage   bool
	Name     string
	Code     string
	Remark   string
	Search   string
}

// UUIdReq 表示 Uuid 请求
type UUIdReq struct {
	Id string
}

// GroupListResp 表示组列表的响应
type GroupListResp struct {
	Total uint64
	Data  []*GroupInfo
}

// ButtonInfo 表示按钮信息
type ButtonInfo struct {
	Id        string
	CreatedAt int64
	UpdatedAt int64
	Sort      uint32
	Name      string
	Code      string
	MenuId    string
}

// RoleWithUsersResp 表示带有用户的角色响应
type RoleWithUsersResp struct {
	Id            string
	CreatedAt     int64
	Status        bool
	Sort          uint32
	TenantId      string
	Name          string
	Code          string
	UId           string
	DefaultRouter string
	Remark        string
	ParentId      string
	Users         []*RoleUser
}

// RoleAPIAuthorityReq 表示角色 API 权限的请求
type RoleAPIAuthorityReq struct {
	RoleId string
	ApiIds []string
}

// FileListResp 表示文件列表的响应
type FileListResp struct {
	Total uint64
	Data  []*FileInfo
}

// PositionListReq 表示职位列表的请求
type PositionListReq struct {
	Page           uint64
	PageSize       uint64
	NoPage         bool
	Name           string
	Code           string
	Remark         string
	Search         string
	Status         string
	OrganizationId string
}

// DeleteTenantUserInfo 表示删除租户用户信息
type DeleteTenantUserInfo struct {
	TenantId string
	UserIds  []string
}

// BaseResp 表示基础响应
type BaseResp struct {
	Msg string
}

// OrganizationUserInfoListReq 表示组织用户信息列表的请求
type OrganizationUserInfoListReq struct {
	Page           uint64
	PageSize       uint64
	NoPage         bool
	Search         string
	OrganizationId string
}

type OrganizationUserTreeReq struct {
	OrgIds []string
}

// IDReq 表示 Id 请求
type IDReq struct {
	Id string
}

// GroupInfo 表示组信息
type GroupInfo struct {
	Id            string
	CreatedAt     int64
	UpdatedAt     int64
	Status        bool
	Sort          uint32
	TenantId      string
	GroupTypeId   string
	Name          string
	Code          string
	Remark        string
	GroupTypeName string
}

// UpdateTenantUsersReq 表示更新租户用户的请求
type UpdateTenantUsersReq struct {
	TenantId string
	UserIds  []string
	Kind     string
}

// MenuListReq 表示菜单列表的请求
type MenuListReq struct {
	Page          uint64
	PageSize      uint64
	NoPage        bool
	Name          string
	Title         string
	Icon          string
	ParentId      string
	NoParent      bool
	UserId        string
	IsSuperAdmin  bool
	IsSuperTenant bool
}

// GetTenantUsersResp 表示获取租户用户的响应
type GetTenantUsersResp struct {
	Total uint64
	Data  []*TenantUser
}

// UpdateMenusReq 表示更新菜单的请求
type UpdateMenusReq struct {
	Menus string
}

// ButtonListResp 表示按钮列表的响应
type ButtonListResp struct {
	Total uint64
	Data  []*ButtonInfo
}

// ButtonListReq 表示按钮列表的请求
type ButtonListReq struct {
	Page     uint64
	PageSize uint64
	NoPage   bool
	Name     string
	Code     string
	MenuId   string
	Search   string
}

// GroupWithUsersResp 表示带有用户的组响应
type GroupWithUsersResp struct {
	Id            string
	CreatedAt     int64
	UpdatedAt     int64
	Status        bool
	Sort          uint32
	TenantId      string
	GroupTypeId   string
	Name          string
	Code          string
	Remark        string
	GroupTypeName string
	Users         []*GroupUser
}

// OrganizationListReq 表示组织列表的请求
type OrganizationListReq struct {
	Page      uint64
	PageSize  uint64
	NoPage    bool
	Name      string
	Ancestors string
	Code      string
	ParentId  string
	NoParent  bool
	Search    string
	ParentID  string
}

// Avatar 表示头像
type Avatar struct {
	Id         string
	Name       string
	OriginName string
	Path       string
	OpenStatus uint32
}

// UpdateUserDefaultTenantReq 表示更新用户默认租户的请求
type UpdateUserDefaultTenantReq struct {
	UserId   string
	TenantId string
}

// FileInfo 表示文件信息
type FileInfo struct {
	Id         string
	CreatedAt  int64
	UpdatedAt  int64
	Status     bool
	Sort       uint32
	TenantId   string
	Uuid       string
	Name       string
	OriginName string
	FileType   uint32
	Size       uint64
	Path       string
	UserId     string
	Hash       string
	OpenStatus uint32
}

// DeleteOrganizationUserInfo 表示删除组织用户信息
type DeleteOrganizationUserInfo struct {
	OrganizationId string
	UserIds        []string
}

// TenantInfo 表示租户信息
type TenantInfo struct {
	Id        string
	CreatedAt int64
	UpdatedAt int64
	Uuid      string
	Key       string
	Secret    string
	IsSuper   bool
	// 售后联系人
	AfterSalesContact string
	// 归属地ID
	LocationID string
	// 日志保留天数
	LogSaveKeepDays int64
	// 最大列席用户数
	MaxAttendanceUserCount int64
	// 最大设备数
	MaxDeviceCount int64
	// 最大上传文件大小，MB
	MaxUploadFileSize int64
	// 最大用户数
	MaxUserCount int64
	// 租户名称
	Name string
	// 负责人
	Principal string
	// 负责人联系方式
	PrincipalContactInformation string
	// 销售联系人
	SaleContact string
	// 有效期结束时间，毫秒时间戳
	ServiceEndAt int64
	// 有效期开始时间，毫秒时间戳
	ServiceStartAt int64
	// 有效状态
	Status        bool
	SystemPlugins []SystemPluginInfo
	// AI状态
	AiStatus                 bool
	MaxConferenceAgendaTitle int64
}

// TenantCreateInfo 租户创建信息
type TenantCreateInfo struct {
	CreatedAt int64
	UpdatedAt int64
	Uuid      string
	Key       string
	Secret    string
	IsSuper   bool
	// 售后联系人
	AfterSalesContact string
	// 归属地ID
	LocationID string
	// 日志保留天数
	LogSaveKeepDays int64
	// 最大列席用户数
	MaxAttendanceUserCount int64
	// 最大设备数
	MaxDeviceCount int64
	// 最大上传文件大小，MB
	MaxUploadFileSize int64
	// 最大用户数
	MaxUserCount int64
	// 租户名称
	Name string
	// 负责人
	Principal string
	// 负责人联系方式
	PrincipalContactInformation string
	// 销售联系人
	SaleContact string
	// 有效期结束时间，毫秒时间戳
	ServiceEndAt int64
	// 有效期开始时间，毫秒时间戳
	ServiceStartAt int64
	// 有效状态
	Status                   bool
	SystemPlugins            []string
	SecretKey                string
	AiStatus                 bool
	MaxConferenceAgendaTitle int64
}

// TenantUpdateInfo 租户更新信息
type TenantUpdateInfo struct {
	ID        string
	CreatedAt int64
	UpdatedAt int64
	Uuid      string
	Key       string
	Secret    string
	IsSuper   bool
	// 售后联系人
	AfterSalesContact string
	// 归属地ID
	LocationID string
	// 日志保留天数
	LogSaveKeepDays int64
	// 最大列席用户数
	MaxAttendanceUserCount int64
	// 最大设备数
	MaxDeviceCount int64
	// 最大上传文件大小，MB
	MaxUploadFileSize int64
	// 最大用户数
	MaxUserCount int64
	// 租户名称
	Name string
	// 负责人
	Principal string
	// 负责人联系方式
	PrincipalContactInformation string
	// 销售联系人
	SaleContact string
	// 有效期结束时间，毫秒时间戳
	ServiceEndAt int64
	// 有效期开始时间，毫秒时间戳
	ServiceStartAt int64
	// 有效状态
	Status        bool
	SystemPlugins []string
	// AI状态
	AiStatus                 bool
	MaxConferenceAgendaTitle int64
}
type SystemPluginInfo struct {
	ID   string
	Name string
	Code string
}

// UserInfo 表示用户信息
type UserInfo struct {
	Id              string
	CreatedAt       int64
	UpdatedAt       int64
	Status          bool
	Username        string
	Password        string
	Nickname        string
	Mobile          string
	Email           string
	Avatar          *Avatar
	DefaultTenantId string
	IsSuperuser     bool
	Gender          string
	Post            string
	Kind            string
	DeviceNo        string
	Imei            string
	TenantInfos     []TenantInfo
	OrganizationIDs []string
}

// ChangeFileOpenStatusReq 表示更改文件打开状态的请求
type ChangeFileOpenStatusReq struct {
	Ids        []string
	OpenStatus uint32
}

// OrganizationInfo 表示组织信息
type OrganizationInfo struct {
	Id        string
	CreatedAt int64
	UpdatedAt int64
	Status    bool
	Sort      uint32
	TenantId  string
	Name      string
	Ancestors string
	Code      string
	NodeType  uint32
	Leader    string
	Phone     string
	Email     string
	Remark    string
	ParentId  string
	UserCount int64
	Children  []*OrganizationInfo
}

// OrganizationUser 表示组织用户
type OrganizationUser struct {
	Id              string
	CreatedAt       int64
	Status          bool
	Username        string
	Sort            int32
	Nickname        string
	Mobile          string
	Email           string
	Avatar          *Avatar
	DefaultTenantId string
	Extra           string
	Gender          string
	Post            string
	IsLeader        bool
	IsAdmin         bool
}

// OrganizationUserInfoOrganizationInfoLite 表示组织用户信息组织信息精简版
type OrganizationUserInfoOrganizationInfoLite struct {
	OrgID   string
	OrgName string
	Users   []*OrganizationUsers
}

type OrganizationUsers struct {
	UserID   string
	Nickname string
}

// GetUserOrganizationsInfoListReq 表示获取用户组织信息列表的请求
type GetUserOrganizationsInfoListReq struct {
	UserId string
}

// GetGroupUsersReq 表示获取组用户的请求
type GetGroupUsersReq struct {
	GroupId string
	Search  string
}

// UpdateRoleUsersReq 表示更新角色用户的请求
type UpdateRoleUsersReq struct {
	RoleId  string
	UserIds []string
}

// BaseUUIdResp 表示基础 Uuid 响应
type BaseUUIdResp struct {
	Id  string
	Msg string
}

// MenuInfo 表示菜单信息
type MenuInfo struct {
	Id          string
	CreatedAt   int64
	UpdatedAt   int64
	Sort        uint32
	TenantId    string
	Name        string
	Title       string
	Icon        string
	ParentId    string
	MenuType    uint32
	Url         string
	Redirect    string
	Component   string
	IsActive    bool
	Hidden      bool
	HiddenInTab bool
	Fixed       bool
	Remark      string
	Meta        string
	Buttons     []*ButtonInfo
	Children    []*MenuInfo
	IsFullPage  bool
}

// UserOrganizationsOrganizationInfoLite 表示用户组织组织信息精简版
type UserOrganizationsOrganizationInfoLite struct {
	Id       string
	Status   bool
	Sort     uint32
	TenantId string
	Name     string
	Code     string
	NodeType uint32
	Leader   string
	ParentId string
}

// RoleWithAPIListResp 表示带有 API 列表的角色响应
type RoleWithAPIListResp struct {
	Total uint64
	Data  []*RoleWithAPIInfo
}

// GroupTypeListReq 表示组类型列表的请求
type GroupTypeListReq struct {
	Page     uint64
	PageSize uint64
	NoPage   bool
	Name     string
	Code     string
	Remark   string
	Search   string
}

// UserWithExtraInfo 表示带有额外信息的用户
type UserWithExtraInfo struct {
	User    *UserInfo
	Roles   []*RoleInfo
	Tenants []*TenantInfo
}

// RoleMenuAuthorityReq 表示角色菜单权限的请求
type RoleMenuAuthorityReq struct {
	RoleId    string
	MenuIds   []string
	ButtonIds []string
}

// TenantUserInfoListResp 表示租户用户信息列表的响应
type TenantUserInfoListResp struct {
	Total uint64
	Data  []*TenantUserInfoInfo
}

// FileListReq 表示文件列表的请求
type FileListReq struct {
	Page       uint64
	PageSize   uint64
	NoPage     bool
	Id         string
	TenantId   string
	OriginName string
	Name       string
	Search     string
}

// GetOrganizationUsersResp 表示获取组织用户的响应
type GetOrganizationUsersResp struct {
	Total uint64
	Data  []*OrganizationUser
}

// RoleListResp 表示角色列表的响应
type RoleListResp struct {
	Total uint64
	Data  []*RoleInfo
}

// UpdateOrganizationUserInfo 表示更新组织用户信息
type UpdateOrganizationUserInfo struct {
	Sort           uint32
	OrganizationId string
	UserId         string
	Extra          string
	IsLeader       bool
	IsAdmin        bool
}

// UserOrganizationInfoListAllResp 表示用户组织信息列表的全部响应
type UserOrganizationInfoListAllResp struct {
	Data []*UserOrganizationsOrganizationInfoLite
}

// APIListResp 表示 API 列表的响应
type APIListResp struct {
	Total uint64
	Data  []*APIInfo
}

// UpdateAPIsReq 表示更新 API 的请求
type UpdateAPIsReq struct {
	Apis string
}

// GroupTypeInfoLite 表示组类型信息精简版
type GroupTypeInfoLite struct {
	Id     string
	Status bool
	Sort   uint32
	Name   string
	Code   string
	Groups []*GroupInfoLite
}

// MenuListResp 表示菜单列表的响应
type MenuListResp struct {
	Total uint64
	Data  []*MenuInfo
}

// TenantUser 表示租户用户
type TenantUser struct {
	Id              string
	CreatedAt       int64
	Status          bool
	Username        string
	Sort            int32
	Nickname        string
	Mobile          string
	Email           string
	Avatar          *Avatar
	DefaultTenantId string
	Extra           string
	Gender          string
	Post            string
	Kind            string
	Imei            string
	DeviceNo        string
}

// UserListReq 表示用户列表的请求
type UserListReq struct {
	Page         uint64
	PageSize     uint64
	Username     string
	Nickname     string
	Mobile       string
	Email        string
	IgnoreTenant bool
	Search       string
	Ids          []string
	Kind         string
	Imei         string
	DeviceNo     string
	Status       *bool
	NoPage       bool
}

// FileInfos 表示文件信息列表
type FileInfos struct {
	Data []*FileInfo
}

// OrganizationUserInfoUserInfo 表示组织用户信息用户信息
type OrganizationUserInfoUserInfo struct {
	Id              string
	CreatedAt       int64
	Status          bool
	Username        string
	Sort            int32
	Nickname        string
	Mobile          string
	Email           string
	Avatar          *Avatar
	DefaultTenantId string
	Extra           string
	Gender          string
	Post            string
	OrganizationId  string
	IsLeader        bool
	IsAdmin         bool
}

// OrganizationUserInfoUserInfoLite 表示组织用户信息用户信息精简版
type OrganizationUserInfoUserInfoLite struct {
	Id       string
	Avatar   *Avatar
	Nickname string
	Gender   string
	Mobile   string
	Email    string
	Post     string
	Status   bool
	Sort     uint32
}

// GetRoleUsersReq 表示获取角色用户的请求
type GetRoleUsersReq struct {
	RoleId string
	Search string
}

// UserListResp 表示用户列表的响应
type UserListResp struct {
	Total uint64
	Data  []*UserInfo
}

// UUIDsReq 表示 Uuid 请求列表
type UUIDsReq struct {
	Ids []string
}

// PositionListResp 表示职位列表的响应
type PositionListResp struct {
	Total uint64
	Data  []*PositionInfo
}

// TokenListResp 表示令牌列表的响应
type TokenListResp struct {
	Total uint64
	Data  []*TokenInfo
}

// RoleListReq 表示角色列表的请求
type RoleListReq struct {
	Page           uint64
	PageSize       uint64
	NoPage         bool
	Name           string
	Code           string
	DefaultRouter  string
	ParentId       string
	NoParent       bool
	Search         string
	OrganizationId string
}

// RoleWithAPIInfo 表示带有 API 信息的角色
type RoleWithAPIInfo struct {
	Id   string
	Apis []*APIInfo
}

// BaseIDResp 表示基础 Id 响应
type BaseIDResp struct {
	Id  string
	Msg string
}

// GroupTypeListTreeReq 表示组类型列表树的请求
type GroupTypeListTreeReq struct {
	Search string
}

// GroupInfoLite 表示组信息精简版
type GroupInfoLite struct {
	Id    string
	Sort  uint32
	Name  string
	Code  string
	Users []*GroupUserInfoLite
}

// OrganizationUserInfoInfo 表示组织用户信息
type OrganizationUserInfoInfo struct {
	Id             string
	CreatedAt      int64
	UpdatedAt      int64
	Sort           uint32
	OrganizationId string
	UserId         string
	Extra          string
	IsLeader       bool
	IsAdmin        bool
}

// CreateOrganizationUserInfo 表示创建组织用户信息
type CreateOrganizationUserInfo struct {
	Sort           uint32
	OrganizationId string
	UserId         string
	Extra          string
}

// RoleInfo 表示角色信息
type RoleInfo struct {
	Id             string
	CreatedAt      int64
	UpdatedAt      int64
	Status         bool
	Sort           uint32
	TenantId       string
	Name           string
	Code           string
	UId            string
	DefaultRouter  string
	Remark         string
	ParentId       string
	OrganizationId string
	Children       []*RoleInfo
}

// TenantListReq 表示租户列表的请求
type TenantListReq struct {
	Page     uint64
	PageSize uint64
	NoPage   bool
	Key      string
	Secret   string
	Name     string
	Search   string
}

// TokenInfo 表示令牌信息
type TokenInfo struct {
	Id         string
	CreatedAt  int64
	UpdatedAt  int64
	Status     bool
	TenantId   string
	Uid        string
	Token      string
	Source     string
	ExpiredAt  int64
	DeviceKind string
	IP         string
}

// TokenListReq 表示令牌列表的请求
type TokenListReq struct {
	Page     uint64
	PageSize uint64
	NoPage   bool
	Token    string
	Source   string
	Search   string
}

// IdentityReq 表示身份请求
type IdentityReq struct {
	// 可以是 mobile、email、username 中的任意一个
	Identity string
	Mobile   string
	Email    string
	Username string
	DeviceNo string
	Imei     string
}

type UpdateOrganizationUsersReq struct {
	OrganizationId  string
	OrganizationIDs []string
	UserIds         []string
	UserSorts       []UserSortInfo
}

type UserSortInfo struct {
	UserId string
	Sort   uint32
}

type OrganizationListResp struct {
	Total uint64
	Data  []*OrganizationInfo
}

type PlatUser struct {
	UserID     string
	PlatUserID string
	Username   string
	PlatCode   string
}
