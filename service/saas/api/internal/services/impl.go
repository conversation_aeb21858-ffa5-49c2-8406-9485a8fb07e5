package services

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/adapter/addons"
	"phoenix/service/saas/model/ent"
	"phoenix/service/saas/model/ent/api"
	"phoenix/service/saas/model/ent/button"
	"phoenix/service/saas/model/ent/enums"
	"phoenix/service/saas/model/ent/file"
	"phoenix/service/saas/model/ent/group"
	"phoenix/service/saas/model/ent/grouptype"
	"phoenix/service/saas/model/ent/menu"
	"phoenix/service/saas/model/ent/organization"
	"phoenix/service/saas/model/ent/organizationuserinfo"
	"phoenix/service/saas/model/ent/position"
	"phoenix/service/saas/model/ent/predicate"
	"phoenix/service/saas/model/ent/role"
	"phoenix/service/saas/model/ent/schema"
	"phoenix/service/saas/model/ent/tenant"
	"phoenix/service/saas/model/ent/tenantuserinfo"
	"phoenix/service/saas/model/ent/token"
	"phoenix/service/saas/model/ent/user"
	mapper "phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"
	"phoenix/service/saas/utils/dberrorhandler"
	"phoenix/service/saas/utils/entx"
	"phoenix/service/saas/utils/uuidx"
	"time"

	"entgo.io/ent/dialect/sql"
	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	_ "gitlab.zhijiasoft.com/paperless-group/saas-common/msg/logmsg"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/utils/encrypt"
	"gorm.io/gorm"
)

type Impl struct {
	DB              *ent.Client
	Redis           *redis.Redis
	gormDB          *gorm.DB
	cacheInitLoader *addons.CacheInitLoader
}

func NewImpl(DB *ent.Client, redis *redis.Redis, gormDB *gorm.DB, cacheInitLoader *addons.CacheInitLoader) *Impl {
	return &Impl{DB: DB, Redis: redis, gormDB: gormDB, cacheInitLoader: cacheInitLoader}
}

func (l *Impl) CreateAPI(ctx context.Context, in *APIInfo) (*BaseIDResp, error) {

	exist, err := l.DB.API.Query().Where(api.PathEQ(in.Path), api.MethodEQ(in.Method)).Exist(ctx)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	if exist {
		return nil, errors.New("该接口已被添加。")
	}

	result, err := l.DB.API.Create().
		SetStatus(in.Status).
		SetPath(in.Path).
		SetDescription(in.Description).
		SetAPIGroup(in.ApiGroup).
		SetMethod(in.Method).
		SetKind(in.Kind).
		SetModule(in.Module).
		Save(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseIDResp{Id: result.ID, Msg: i18n.CreateSuccess}, nil
}

func (l *Impl) UpdateAPI(ctx context.Context, in *APIInfo) (*BaseResp, error) {

	err := l.DB.API.UpdateOneID(in.Id).
		SetStatus(in.Status).
		SetNotEmptyPath(in.Path).
		SetNotEmptyDescription(in.Description).
		SetNotEmptyAPIGroup(in.ApiGroup).
		SetNotEmptyMethod(in.Method).
		SetNotEmptyKind(in.Kind).
		SetNotEmptyModule(in.Module).
		Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.UpdateSuccess}, nil
}

func (l *Impl) GetAPIList(ctx context.Context, in *APIListReq) (*APIListResp, error) {
	var predicates []predicate.API
	if in.Path != "" {
		predicates = append(predicates, api.PathContains(in.Path))
	}
	if in.Description != "" {
		predicates = append(predicates, api.DescriptionContains(in.Description))
	}
	if in.ApiGroup != "" {
		predicates = append(predicates, api.APIGroupContains(in.ApiGroup))
	}
	if in.RoleId != "" {
		predicates = append(predicates, api.HasRolesWith(role.IDEQ(in.RoleId)))
	}
	if in.Module != "" {
		predicates = append(predicates, api.ModuleEQ(in.Module))
	}
	if in.Kind != "" {
		predicates = append(predicates, api.KindEQ(in.Kind))
	}
	if in.Search != "" {
		predicates = append(predicates,
			api.Or(api.PathContains(in.Search), api.PathContains(in.Search), api.DescriptionContains(in.Search)))
	}
	result, err := l.DB.API.Query().Where(predicates...).Page(ctx, in.Page, in.PageSize)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	resp := &APIListResp{}
	resp.Total = result.PageDetails.Total

	for _, v := range result.List {
		resp.Data = append(resp.Data, &APIInfo{
			Id:          v.ID,
			CreatedAt:   v.CreatedAt.UnixMilli(),
			UpdatedAt:   v.UpdatedAt.UnixMilli(),
			Status:      v.Status,
			Path:        v.Path,
			Description: v.Description,
			ApiGroup:    v.APIGroup,
			Method:      v.Method,
			Kind:        v.Kind,
			Module:      v.Module,
		})
	}

	return resp, nil
}

func (l *Impl) GetAPIById(ctx context.Context, in *IDReq) (*APIInfo, error) {
	result, err := l.DB.API.Get(ctx, in.Id)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &APIInfo{
		Id:          result.ID,
		CreatedAt:   result.CreatedAt.UnixMilli(),
		UpdatedAt:   result.UpdatedAt.UnixMilli(),
		Status:      result.Status,
		Path:        result.Path,
		Description: result.Description,
		ApiGroup:    result.APIGroup,
		Method:      result.Method,
		Kind:        result.Kind,
		Module:      result.Module,
	}, nil
}

func (l *Impl) DeleteAPI(ctx context.Context, in *IDsReq) (*BaseResp, error) {
	_, err := l.DB.API.Delete().Where(api.IDIn(in.Ids...)).Exec(schema.SkipSoftDelete(ctx))

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.DeleteSuccess}, nil
}

func (l *Impl) GetMenuAuthority(ctx context.Context, in *IDReq) (*RoleMenuAuthorityResp, error) {
	menus, err := l.DB.Role.Query().Where(role.ID(in.Id)).QueryMenus().IDs(ctx)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}
	buttons, err := l.DB.Role.Query().Where(role.ID(in.Id)).QueryButtons().IDs(ctx)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	if menus == nil {
		menus = make([]string, 0)
	}
	if buttons == nil {
		buttons = make([]string, 0)
	}

	return &RoleMenuAuthorityResp{MenuIds: menus, ButtonIds: buttons}, nil
}

func (l *Impl) CreateOrUpdateMenuAuthority(ctx context.Context, in *RoleMenuAuthorityReq) (*BaseResp, error) {
	err := entx.WithTx(ctx, l.DB, func(tx *ent.Tx) error {
		err := tx.Role.UpdateOneID(in.RoleId).ClearMenus().Exec(ctx)
		if err != nil {
			return err
		}

		err = tx.Role.UpdateOneID(in.RoleId).AddMenuIDs(in.MenuIds...).Exec(ctx)
		if err != nil {
			return err
		}

		err = tx.Role.UpdateOneID(in.RoleId).ClearButtons().Exec(ctx)
		if err != nil {
			return err
		}
		err = tx.Role.UpdateOneID(in.RoleId).AddButtonIDs(in.ButtonIds...).Exec(ctx)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.UpdateSuccess}, nil
}

func (l *Impl) CreateOrUpdateAPIAuthority(ctx context.Context, in *RoleAPIAuthorityReq) (*BaseResp, error) {
	err := entx.WithTx(ctx, l.DB, func(tx *ent.Tx) error {
		err := tx.Role.UpdateOneID(in.RoleId).ClearApis().Exec(ctx)
		if err != nil {
			return err
		}

		err = tx.Role.UpdateOneID(in.RoleId).AddAPIIDs(in.ApiIds...).Exec(ctx)
		if err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.UpdateSuccess}, nil
}

func (l *Impl) InitDatabase(ctx context.Context, in *Empty) (*BaseResp, error) {
	return nil, nil
}

func (l *Impl) UpdateMenus(ctx context.Context, in *UpdateMenusReq) (*BaseResp, error) {
	err := entx.WithTx(ctx, l.DB, func(tx *ent.Tx) error {

		menuData, err := utils.GetMenusFromJsonString(in.Menus)
		if err != nil {
			return err
		}

		// 从第一层递归创建菜单和按钮，先验证是否有重复项，再进行创建或更新
		for i, v := range *menuData {
			err = l.UpsertMenuWithButton(ctx, tx, v, int32(i), "")
			if err != nil {
				return err
			}
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	return &BaseResp{}, nil
}
func (l *Impl) UpsertMenuWithButton(ctx context.Context, tx *ent.Tx, menuObj utils.Menu, sort int32, parentId string) (err error) {
	menuType := 0
	if menuObj.Component != "" {
		menuType = 1
	}
	var m *ent.Menu
	// 1. 检查并更新菜单
	m = tx.Menu.Query().Where(menu.NameEQ(menuObj.Name)).FirstX(ctx)
	if m != nil {
		s := tx.Menu.UpdateOne(m).
			SetURL(menuObj.Path).
			SetComponent(menuObj.Component).
			SetIcon(menuObj.Meta.Icon).
			SetTitle(menuObj.Meta.Title).
			SetHidden(menuObj.Meta.Hidden).
			SetHiddenInTab(menuObj.Meta.HiddenInTab).
			SetIsActive(menuObj.Meta.IsActive).
			SetIsFullPage(menuObj.Meta.IsFullPage).
			SetFixed(menuObj.Meta.Fixed).
			SetRedirect(menuObj.Redirect).
			SetSort(uint32(sort))

		if parentId != "" {
			s.SetParentID(parentId)
		}

		m, err = s.Save(ctx)

		if err != nil {
			return errorx.NewInternalError(err.Error())
		}
	} else {
		s := tx.Menu.Create().
			SetMenuType(uint32(menuType)).
			SetURL(menuObj.Path).
			SetName(menuObj.Name).
			SetComponent(menuObj.Component).
			SetSort(uint32(sort)).
			SetTitle(menuObj.Meta.Title).
			SetHidden(menuObj.Meta.Hidden).
			SetIcon(menuObj.Meta.Icon).
			SetHiddenInTab(menuObj.Meta.HiddenInTab).
			SetIsActive(menuObj.Meta.IsActive).
			SetIsFullPage(menuObj.Meta.IsFullPage).
			SetFixed(menuObj.Meta.Fixed).
			SetRedirect(menuObj.Redirect)

		if parentId != "" {
			s.SetParentID(parentId)
		}

		m, err = s.Save(ctx)

		if err != nil {
			return errorx.NewInternalError(err.Error())
		}
	}

	for pi, v := range menuObj.Meta.Permissions {
		b := tx.Button.Query().Where(button.CodeEQ(v.Code), button.MenuIDEQ(m.ID)).FirstX(ctx)
		if b != nil {
			err = tx.Button.UpdateOne(b).
				SetName(v.Name).
				SetSort(uint32(pi)).
				Exec(ctx)

			if err != nil {
				return errorx.NewInternalError(err.Error())
			}
		} else {
			_, err = tx.Button.Create().
				SetName(v.Name).
				SetCode(v.Code).
				SetSort(uint32(pi)).
				SetMenuID(m.ID).
				Save(ctx)
			if err != nil {
				return errorx.NewInternalError(err.Error())
			}
		}
	}

	if len(menuObj.Children) > 0 {
		for i, v := range menuObj.Children {
			err = l.UpsertMenuWithButton(ctx, tx, v, int32(i), m.ID)
			if err != nil {
				return err
			}
		}
	}
	return nil
}
func (l *Impl) UpdateAPIs(ctx context.Context, in *UpdateAPIsReq) (*BaseResp, error) {
	err := entx.WithTx(ctx, l.DB, func(tx *ent.Tx) error {
		apiObjs, err := utils.GetAPIsFromJsonString(in.Apis)
		if err != nil {
			return errorx.NewInternalError(err.Error())
		}

		// 遍历创建或更新API
		for _, v := range *apiObjs {
			err = l.UpsertAPIs(ctx, tx, v)
			if err != nil {
				return errorx.NewInternalError(err.Error())
			}
		}
		return nil
	})

	if err != nil {
		return nil, err
	}

	return &BaseResp{Msg: "API更新成功"}, nil
}

func (l *Impl) UpsertAPIs(ctx context.Context, tx *ent.Tx, obj utils.API) (err error) {

	b := tx.API.Query().Where(api.PathEQ(obj.Path), api.Method(obj.Method)).FirstX(ctx)
	if b != nil {
		err = tx.API.UpdateOne(b).
			SetAPIGroup(obj.ApiGroup).
			SetDescription(obj.Description).
			Exec(ctx)

		if err != nil {
			return errorx.NewInternalError(err.Error())
		}
	} else {
		_, err = tx.API.Create().
			SetAPIGroup(obj.ApiGroup).
			SetPath(obj.Path).
			SetMethod(obj.Method).
			SetDescription(obj.Description).
			Save(ctx)
		if err != nil {
			return errorx.NewInternalError(err.Error())
		}
	}

	return nil
}

func (l *Impl) CreateButton(ctx context.Context, in *ButtonInfo) (*BaseIDResp, error) {
	result, err := l.DB.Button.Create().
		SetSort(in.Sort).
		SetName(in.Name).
		SetCode(in.Code).
		SetMenuID(in.MenuId).
		Save(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseIDResp{Id: result.ID, Msg: i18n.CreateSuccess}, nil
}

func (l *Impl) UpdateButton(ctx context.Context, in *ButtonInfo) (*BaseResp, error) {
	err := l.DB.Button.UpdateOneID(in.Id).
		SetNotEmptySort(in.Sort).
		SetNotEmptyName(in.Name).
		SetNotEmptyCode(in.Code).
		SetNotEmptyMenuID(in.MenuId).
		Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.UpdateSuccess}, nil
}

func (l *Impl) GetButtonList(ctx context.Context, in *ButtonListReq) (*ButtonListResp, error) {
	var predicates []predicate.Button
	if in.Name != "" {
		predicates = append(predicates, button.NameContains(in.Name))
	}
	if in.Code != "" {
		predicates = append(predicates, button.CodeContains(in.Code))
	}
	if in.MenuId != "" {
		predicates = append(predicates, button.MenuIDEQ(in.MenuId))
	}
	result, err := l.DB.Button.Query().Where(predicates...).Page(ctx, in.Page, in.PageSize)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	resp := &ButtonListResp{}
	resp.Total = result.PageDetails.Total

	for _, v := range result.List {
		resp.Data = append(resp.Data, &ButtonInfo{
			Id:        v.ID,
			CreatedAt: v.CreatedAt.UnixMilli(),
			UpdatedAt: v.UpdatedAt.UnixMilli(),
			Sort:      v.Sort,
			Name:      v.Name,
			Code:      v.Code,
			MenuId:    v.MenuID,
		})
	}

	return resp, nil
}

func (l *Impl) GetButtonById(ctx context.Context, in *IDReq) (*ButtonInfo, error) {
	result, err := l.DB.Button.Get(ctx, in.Id)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &ButtonInfo{
		Id:        result.ID,
		CreatedAt: result.CreatedAt.UnixMilli(),
		UpdatedAt: result.UpdatedAt.UnixMilli(),
		Sort:      result.Sort,
		Name:      result.Name,
		Code:      result.Code,
		MenuId:    result.MenuID,
	}, nil
}

func (l *Impl) DeleteButton(ctx context.Context, in *IDsReq) (*BaseResp, error) {
	_, err := l.DB.Button.Delete().Where(button.IDIn(in.Ids...)).Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.DeleteSuccess}, nil
}

func (l *Impl) ChangeFileOpenStatus(ctx context.Context, in *ChangeFileOpenStatusReq) (*FileListResp, error) {
	err := l.DB.File.Update().Where(file.IDIn(in.Ids...)).SetOpenStatus(uint8(in.OpenStatus)).
		Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	result, err := l.DB.File.Query().Where(file.IDIn(in.Ids...)).All(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	resp := &FileListResp{}

	for _, v := range result {
		resp.Data = append(resp.Data, &FileInfo{
			Id:         v.ID,
			CreatedAt:  v.CreatedAt.UnixMilli(),
			UpdatedAt:  v.UpdatedAt.UnixMilli(),
			Status:     v.Status,
			Sort:       v.Sort,
			TenantId:   v.TenantID,
			Uuid:       v.UUID,
			Name:       v.Name,
			OriginName: v.OriginName,
			FileType:   uint32(v.FileType),
			Size:       v.Size,
			Path:       v.Path,
			UserId:     v.UserID,
			Hash:       v.Hash,
		})
	}

	return resp, nil
}

func (l *Impl) CreateFile(ctx context.Context, in *FileInfo) (*BaseIDResp, error) {
	result, err := l.DB.File.Create().
		SetStatus(in.Status).
		SetSort(in.Sort).
		SetTenantID(in.TenantId).
		SetUUID(in.Uuid).
		SetName(in.Name).
		SetOriginName(in.OriginName).
		SetOpenStatus(uint8(in.OpenStatus)).
		SetFileType(uint8(in.FileType)).
		SetSize(in.Size).
		SetPath(in.Path).
		SetUserID(in.UserId).
		SetHash(in.Hash).
		Save(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	// 更新缓存查询器
	err = l.cacheInitLoader.UpdateFileName(ctx, result.ID, in.Name)
	if err != nil {
		logc.Errorf(ctx, "更新文件名称缓存失败: %v", err)
	}

	return &BaseIDResp{Id: result.ID, Msg: i18n.CreateSuccess}, nil
}

func (l *Impl) UpdateFile(ctx context.Context, in *FileInfo) (*BaseResp, error) {
	err := l.DB.File.UpdateOneID(in.Id).
		SetStatus(in.Status).
		SetNotEmptySort(in.Sort).
		SetNotEmptyTenantID(in.TenantId).
		SetNotEmptyUUID(in.Uuid).
		SetNotEmptyName(in.Name).
		SetNotEmptyOriginName(in.OriginName).
		SetNotEmptyFileType(uint8(in.FileType)).
		SetNotEmptySize(in.Size).
		SetNotEmptyPath(in.Path).
		SetNotEmptyUserID(in.UserId).
		SetNotEmptyHash(in.Hash).
		Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	// 更新缓存查询器
	err = l.cacheInitLoader.UpdateFileName(ctx, in.Id, in.Name)
	if err != nil {
		logc.Errorf(ctx, "更新文件名称缓存失败: %v", err)
	}

	return &BaseResp{Msg: i18n.UpdateSuccess}, nil
}

func (l *Impl) UpdateFileByUuid(ctx context.Context, in *FileInfo) (*BaseResp, error) {
	fileClient := mapper.NewFmsFileClient(l.gormDB)
	err := fileClient.UpdateFileByUuid(ctx, in.Uuid, in.Status)
	if err != nil {
		return nil, err
	}
	return &BaseResp{Msg: i18n.UpdateSuccess}, nil
}

func (l *Impl) GetFileList(ctx context.Context, in *FileListReq) (*FileListResp, error) {
	var predicates []predicate.File
	if in.TenantId != "" {
		predicates = append(predicates, file.TenantIDContains(in.TenantId))
	}
	if in.Name != "" {
		predicates = append(predicates, file.NameContains(in.Name))
	}
	if in.OriginName != "" {
		predicates = append(predicates, file.OriginNameContains(in.OriginName))
	}
	if in.Search != "" {
		predicates = append(predicates,
			file.Or(file.NameContains(in.Search)))
	}
	result, err := l.DB.File.Query().Where(predicates...).Page(ctx, in.Page, in.PageSize)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	resp := &FileListResp{}
	resp.Total = result.PageDetails.Total

	for _, v := range result.List {
		resp.Data = append(resp.Data, &FileInfo{
			Id:         v.ID,
			CreatedAt:  v.CreatedAt.UnixMilli(),
			UpdatedAt:  v.UpdatedAt.UnixMilli(),
			Status:     v.Status,
			Sort:       v.Sort,
			TenantId:   v.TenantID,
			Uuid:       v.UUID,
			Name:       v.Name,
			OriginName: v.OriginName,
			OpenStatus: uint32(v.OpenStatus),
			FileType:   uint32(v.FileType),
			Size:       v.Size,
			Path:       v.Path,
			UserId:     v.UserID,
			Hash:       v.Hash,
		})
	}

	return resp, nil
}

func (l *Impl) GetFileById(ctx context.Context, in *IDReq) (*FileInfo, error) {
	result, err := l.DB.File.Get(schema.SkipTenantInject(ctx), in.Id)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &FileInfo{
		Id:         result.ID,
		CreatedAt:  result.CreatedAt.UnixMilli(),
		UpdatedAt:  result.UpdatedAt.UnixMilli(),
		Status:     result.Status,
		Sort:       result.Sort,
		TenantId:   result.TenantID,
		Uuid:       result.UUID,
		Name:       result.Name,
		OriginName: result.OriginName,
		OpenStatus: uint32(result.OpenStatus),
		FileType:   uint32(result.FileType),
		Size:       result.Size,
		Path:       result.Path,
		UserId:     result.UserID,
		Hash:       result.Hash,
	}, nil
}

func (l *Impl) DeleteFile(ctx context.Context, in *IDsReq) (*BaseResp, error) {
	_, err := l.DB.File.Delete().Where(file.IDIn(in.Ids...)).Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.DeleteSuccess}, nil
}

func (l *Impl) GetFileByIds(ctx context.Context, in *IDsReq) (*FileInfos, error) {
	result, err := l.DB.File.Query().Where(file.IDIn(in.Ids...)).All(ctx)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	resp := &FileInfos{}
	if len(result) > 0 {
		for _, v := range result {
			resp.Data = append(resp.Data, &FileInfo{
				Id:         v.ID,
				CreatedAt:  v.CreatedAt.UnixMilli(),
				UpdatedAt:  v.UpdatedAt.UnixMilli(),
				Status:     v.Status,
				Sort:       v.Sort,
				TenantId:   v.TenantID,
				Uuid:       v.UUID,
				Name:       v.Name,
				OriginName: v.OriginName,
				OpenStatus: uint32(v.OpenStatus),
				FileType:   uint32(v.FileType),
				Size:       v.Size,
				Path:       v.Path,
				UserId:     v.UserID,
				Hash:       v.Hash,
			})
		}
	}

	return resp, nil
}

func (l *Impl) CreateGroup(ctx context.Context, in *GroupInfo) (*BaseIDResp, error) {
	result, err := l.DB.Group.Create().
		SetStatus(in.Status).
		SetSort(in.Sort).
		SetTenantID(in.TenantId).
		SetGroupTypeID(in.GroupTypeId).
		SetName(in.Name).
		SetCode(in.Code).
		SetRemark(in.Remark).
		Save(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseIDResp{Id: result.ID, Msg: i18n.CreateSuccess}, nil
}

func (l *Impl) UpdateGroup(ctx context.Context, in *GroupInfo) (*BaseResp, error) {
	err := l.DB.Group.UpdateOneID(in.Id).
		SetStatus(in.Status).
		SetNotEmptySort(in.Sort).
		SetNotEmptyTenantID(in.TenantId).
		SetNotEmptyGroupTypeID(in.GroupTypeId).
		SetNotEmptyName(in.Name).
		SetNotEmptyCode(in.Code).
		SetNotEmptyRemark(in.Remark).
		Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.UpdateSuccess}, nil
}

func (l *Impl) GetGroupList(ctx context.Context, in *GroupListReq) (*GroupListResp, error) {
	var predicates []predicate.Group
	if in.Name != "" {
		predicates = append(predicates, group.NameContains(in.Name))
	}
	if in.Code != "" {
		predicates = append(predicates, group.CodeContains(in.Code))
	}
	if in.Remark != "" {
		predicates = append(predicates, group.RemarkContains(in.Remark))
	}
	if in.Search != "" {
		predicates = append(predicates,
			group.Or(group.NameContains(in.Search)))
	}
	result, err := l.DB.Group.Query().Where(predicates...).WithGroupType().Page(ctx, in.Page, in.PageSize)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	resp := &GroupListResp{}
	resp.Total = result.PageDetails.Total

	for _, v := range result.List {
		if v.Edges.GroupType == nil {
			continue
		}

		resp.Data = append(resp.Data, &GroupInfo{
			Id:            v.ID,
			CreatedAt:     v.CreatedAt.UnixMilli(),
			UpdatedAt:     v.UpdatedAt.UnixMilli(),
			Status:        v.Status,
			Sort:          v.Sort,
			TenantId:      v.TenantID,
			GroupTypeId:   v.GroupTypeID,
			Name:          v.Name,
			Code:          v.Code,
			Remark:        v.Remark,
			GroupTypeName: v.Edges.GroupType.Name,
		})
	}

	return resp, nil
}

func (l *Impl) GetGroupById(ctx context.Context, in *IDReq) (*GroupInfo, error) {
	result, err := l.DB.Group.Query().Where(group.IDEQ(in.Id)).WithGroupType().Only(ctx)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &GroupInfo{
		Id:            result.ID,
		CreatedAt:     result.CreatedAt.UnixMilli(),
		UpdatedAt:     result.UpdatedAt.UnixMilli(),
		Status:        result.Status,
		Sort:          result.Sort,
		TenantId:      result.TenantID,
		GroupTypeId:   result.GroupTypeID,
		Name:          result.Name,
		Code:          result.Code,
		Remark:        result.Remark,
		GroupTypeName: result.Edges.GroupType.Name,
	}, nil
}

func (l *Impl) DeleteGroup(ctx context.Context, in *IDsReq) (*BaseResp, error) {
	_, err := l.DB.Group.Delete().Where(group.IDIn(in.Ids...)).Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.DeleteSuccess}, nil
}

func (l *Impl) UpdateGroupUsers(ctx context.Context, in *UpdateGroupUsersReq) (*BaseResp, error) {
	err := l.DB.Group.
		UpdateOneID(in.GroupId).
		// 1. 直接删除Group下所有user关联
		ClearUsers().
		// 2. 重新添加user关联
		AddUserIDs(in.UserIds...).
		Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.CreateSuccess}, nil
}

func (l *Impl) GetGroupUsers(ctx context.Context, in *GetGroupUsersReq) (*GroupWithUsersResp, error) {
	result, err := l.DB.Group.Query().
		Where(group.IDEQ(in.GroupId)).
		WithUsers(func(query *ent.UserQuery) {
			query.WithAvatar()
		}).
		WithGroupType().
		Order(ent.Asc(group.FieldSort)).
		Only(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}
	var users []*GroupUser

	for _, v := range result.Edges.Users {
		users = append(users, &GroupUser{
			Id:        v.ID,
			CreatedAt: v.CreatedAt.UnixMilli(),
			Status:    v.Status,
			Username:  v.Username,
			Nickname:  v.Nickname,
			Mobile:    v.Mobile,
			Email:     v.Email,
			Avatar:    GetAvatar(v.Edges.Avatar),
		})
	}

	if len(result.Edges.Users) == 0 {
		users = make([]*GroupUser, 0)
	}

	resp := &GroupWithUsersResp{
		Id:            result.ID,
		CreatedAt:     result.CreatedAt.UnixMilli(),
		UpdatedAt:     result.UpdatedAt.UnixMilli(),
		Status:        result.Status,
		Sort:          result.Sort,
		TenantId:      result.TenantID,
		GroupTypeId:   result.GroupTypeID,
		Name:          result.Name,
		Code:          result.Code,
		Remark:        result.Remark,
		GroupTypeName: result.Edges.GroupType.Name,
		Users:         users,
	}

	return resp, nil
}

func (l *Impl) CreateGroupType(ctx context.Context, in *GroupTypeInfo) (*BaseIDResp, error) {
	// 检查code是否存在
	exist, err := l.DB.GroupType.Query().Where(grouptype.CodeEQ(in.Code)).Exist(ctx)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}
	if exist {
		return nil, errors.New("组类型CODE不能重复。")
	}

	result, err := l.DB.GroupType.Create().
		SetStatus(in.Status).
		SetSort(in.Sort).
		SetTenantID(in.TenantId).
		SetName(in.Name).
		SetCode(in.Code).
		SetRemark(in.Remark).
		Save(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseIDResp{Id: result.ID, Msg: i18n.CreateSuccess}, nil
}

func (l *Impl) UpdateGroupType(ctx context.Context, in *GroupTypeInfo) (*BaseResp, error) {
	err := l.DB.GroupType.UpdateOneID(in.Id).
		SetStatus(in.Status).
		SetNotEmptySort(in.Sort).
		SetNotEmptyTenantID(in.TenantId).
		SetNotEmptyName(in.Name).
		SetNotEmptyCode(in.Code).
		SetNotEmptyRemark(in.Remark).
		Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.UpdateSuccess}, nil
}

func (l *Impl) GetGroupTypeList(ctx context.Context, in *GroupTypeListReq) (*GroupTypeListResp, error) {
	var predicates []predicate.GroupType
	if in.Name != "" {
		predicates = append(predicates, grouptype.NameContains(in.Name))
	}
	if in.Code != "" {
		predicates = append(predicates, grouptype.CodeContains(in.Code))
	}
	if in.Remark != "" {
		predicates = append(predicates, grouptype.RemarkContains(in.Remark))
	}
	if in.Search != "" {
		predicates = append(predicates,
			grouptype.Or(grouptype.NameContains(in.Search)))
	}
	result, err := l.DB.GroupType.Query().Where(predicates...).Page(ctx, in.Page, in.PageSize)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	resp := &GroupTypeListResp{}
	resp.Total = result.PageDetails.Total

	for _, v := range result.List {
		resp.Data = append(resp.Data, &GroupTypeInfo{
			Id:        v.ID,
			CreatedAt: v.CreatedAt.UnixMilli(),
			UpdatedAt: v.UpdatedAt.UnixMilli(),
			Status:    v.Status,
			Sort:      v.Sort,
			TenantId:  v.TenantID,
			Name:      v.Name,
			Code:      v.Code,
			Remark:    v.Remark,
		})
	}

	return resp, nil
}

func (l *Impl) GetGroupTypeListTree(ctx context.Context, in *GroupTypeListTreeReq) (*GroupTypeListTreeResp, error) {
	result, err := l.DB.GroupType.Query().
		Where(grouptype.StatusEQ(true), grouptype.DeletedAtIsNil()).
		WithGroups(func(query *ent.GroupQuery) {
			query.WithUsers(func(userQuery *ent.UserQuery) {
				userQuery.Where(user.DeletedAtIsNil())
			}).
				Where(group.StatusEQ(true), group.DeletedAtIsNil()).
				Order(ent.Asc(group.FieldSort)).
				Order(ent.Asc(group.FieldID))
		}).
		Order(ent.Asc(grouptype.FieldSort)).
		Order(ent.Asc(grouptype.FieldID)).
		All(ctx)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}
	resp := &GroupTypeListTreeResp{}

	var gts []*GroupTypeInfoLite
	gts = []*GroupTypeInfoLite{}
	if len(result) > 0 {
		for _, v := range result {
			var groups []*GroupInfoLite
			groups = []*GroupInfoLite{}
			if len(v.Edges.Groups) > 0 {
				for _, w := range v.Edges.Groups {
					var users []*GroupUserInfoLite
					users = []*GroupUserInfoLite{}
					if len(w.Edges.Users) > 0 {
						for _, x := range w.Edges.Users {
							users = append(users, &GroupUserInfoLite{
								Id:       x.ID,
								Nickname: x.Nickname,
								Gender:   x.Gender.String(),
								Post:     x.Post,
							})
						}
					}
					groups = append(groups, &GroupInfoLite{
						Id:    w.ID,
						Sort:  w.Sort,
						Name:  w.Name,
						Code:  w.Code,
						Users: users,
					})
				}
			}

			gts = append(gts, &GroupTypeInfoLite{
				Id:     v.ID,
				Status: v.Status,
				Sort:   v.Sort,
				Name:   v.Name,
				Code:   v.Code,
				Groups: groups,
			})
		}
	}

	resp.Data = gts
	return resp, nil
}

func (l *Impl) GetGroupTypeById(ctx context.Context, in *IDReq) (*GroupTypeInfo, error) {
	result, err := l.DB.GroupType.Get(ctx, in.Id)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &GroupTypeInfo{
		Id:        result.ID,
		CreatedAt: result.CreatedAt.UnixMilli(),
		UpdatedAt: result.UpdatedAt.UnixMilli(),
		Status:    result.Status,
		Sort:      result.Sort,
		TenantId:  result.TenantID,
		Name:      result.Name,
		Code:      result.Code,
		Remark:    result.Remark,
	}, nil
}

func (l *Impl) DeleteGroupType(ctx context.Context, in *IDsReq) (*BaseResp, error) {
	_, err := l.DB.GroupType.Delete().Where(grouptype.IDIn(in.Ids...)).Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.DeleteSuccess}, nil
}

func (l *Impl) CreateMenu(ctx context.Context, in *MenuInfo) (*BaseIDResp, error) {
	var parent *ent.Menu
	var err error
	if in.ParentId != "" {
		parent, err = l.DB.Menu.Query().Where(menu.IDEQ(in.ParentId)).Only(ctx)
		if err != nil {
			return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
		}
	}

	q := l.DB.Menu.Create().
		SetSort(in.Sort).
		SetName(in.Name).
		SetTitle(in.Title).
		SetIcon(in.Icon).
		SetMenuType(in.MenuType).
		SetURL(in.Url).
		SetRedirect(in.Redirect).
		SetComponent(in.Component).
		SetIsActive(in.IsActive).
		SetHidden(in.Hidden).
		SetHiddenInTab(in.HiddenInTab).
		SetFixed(in.Fixed).
		SetRemark(in.Remark).
		SetMeta(in.Meta).
		SetIsFullPage(in.IsFullPage)

	if parent != nil {
		q.SetParent(parent)
	}

	result, err := q.Save(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseIDResp{Id: result.ID, Msg: i18n.CreateSuccess}, nil
}

func (l *Impl) UpdateMenu(ctx context.Context, in *MenuInfo) (*BaseResp, error) {
	err := l.DB.Menu.UpdateOneID(in.Id).
		SetNotEmptySort(in.Sort).
		SetNotEmptyName(in.Name).
		SetNotEmptyTitle(in.Title).
		SetIcon(in.Icon).
		SetNotEmptyParentID(in.ParentId).
		SetNotEmptyMenuType(in.MenuType).
		SetNotEmptyURL(in.Url).
		SetNotEmptyRedirect(in.Redirect).
		SetComponent(in.Component).
		SetIsActive(in.IsActive).
		SetHidden(in.Hidden).
		SetHiddenInTab(in.HiddenInTab).
		SetFixed(in.Fixed).
		SetNotEmptyRemark(in.Remark).
		SetNotEmptyMeta(in.Meta).
		SetIsFullPage(in.IsFullPage).
		Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.UpdateSuccess}, nil
}

func (l *Impl) GetMenuList(ctx context.Context, in *MenuListReq) (*MenuListResp, error) {
	// 如果有用户ID，则获取用户菜单
	if in.UserId != "" {
		return l.GetMenuInfoWithChildrenAndButtonsByUser(ctx, in)
	}

	// 如果是超级管理员，但不是超级租户
	var predicates []predicate.Menu
	// 超级租户会获取所有的菜单
	if in.Name != "" {
		predicates = append(predicates, menu.NameContains(in.Name))
	}
	if in.Title != "" {
		predicates = append(predicates, menu.TitleContains(in.Title))
	}
	if in.Icon != "" {
		predicates = append(predicates, menu.IconContains(in.Icon))
	}
	if in.ParentId != "" {
		predicates = append(predicates, menu.ParentIDEQ(in.ParentId))
	}
	if in.NoParent {
		predicates = append(predicates, menu.Or(menu.ParentIDEQ(""), menu.ParentIDIsNil()))
	}
	result, err := l.DB.Menu.Query().
		//WithChildren().
		WithButtons().
		Where(predicates...).
		Order(menu.BySort(sql.OrderAsc())).
		Page(ctx, in.Page, in.PageSize)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	resp := &MenuListResp{}
	resp.Total = result.PageDetails.Total

	for _, v := range result.List {
		resp.Data = append(resp.Data, GetMenuInfoWithChildrenAndButtons(ctx, v, nil, nil))
	}

	return resp, nil

}

func (l *Impl) GetTenantMenuList(ctx context.Context, in *MenuListReq) (*MenuListResp, error) {
	// 查询租户插件信息
	menuIDs, err := mapper.NewViewTenantPluginMenuClient(l.gormDB).QueryFirstLevelMenuByTenantID(ctx, utils.GetContextTenantID(ctx))
	if err != nil {
		logc.Error(ctx, "query tenant plugin fail:", err)
		return nil, err
	}
	if len(menuIDs) == 0 {
		return nil, nil
	}

	var predicates []predicate.Menu
	predicates = append(predicates, menu.Or(menu.ParentIDEQ(""), menu.ParentIDIsNil()), menu.IDIn(menuIDs...))
	result, err := l.DB.Menu.Query().
		WithButtons().
		Where(predicates...).
		Order(menu.BySort(sql.OrderAsc())).
		Page(ctx, in.Page, in.PageSize)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	resp := &MenuListResp{}
	resp.Total = result.PageDetails.Total

	for _, v := range result.List {
		resp.Data = append(resp.Data, GetMenuInfoWithChildrenAndButtons(ctx, v, nil, nil))
	}

	return resp, nil
}

func (l *Impl) GetMenuById(ctx context.Context, in *IDReq) (*MenuInfo, error) {
	result, err := l.DB.Menu.Query().WithButtons().WithChildren().Where(menu.IDEQ(in.Id)).Only(ctx)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return GetMenuInfoWithButtons(ctx, result), nil
}

func (l *Impl) DeleteMenu(ctx context.Context, in *IDsReq) (*BaseResp, error) {
	_, err := l.DB.Menu.Delete().Where(menu.IDIn(in.Ids...)).Exec(schema.SkipSoftDelete(ctx))

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.DeleteSuccess}, nil
}

func (l *Impl) CreateOrganization(ctx context.Context, in *OrganizationInfo) (*BaseIDResp, error) {
	var parent *ent.Organization
	var err error
	if in.ParentId != "" {
		parent, err = l.DB.Organization.Query().Where(organization.IDEQ(in.ParentId)).Only(ctx)
		if err != nil {
			return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
		}
	}
	q := l.DB.Organization.Create().
		SetStatus(in.Status).
		SetSort(in.Sort).
		SetTenantID(in.TenantId).
		SetName(in.Name).
		SetAncestors(in.Ancestors).
		SetCode(in.Code).
		SetNodeType(in.NodeType).
		SetLeader(in.Leader).
		SetPhone(in.Phone).
		SetEmail(in.Email).
		SetRemark(in.Remark)

	if parent != nil {
		q.SetParent(parent)
	}

	result, err := q.Save(ctx)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}
	// 更新查询器缓存
	err = l.cacheInitLoader.UpdateOrganizationName(ctx, result.ID, in.Name)
	if err != nil {
		logc.Errorf(ctx, "更新组织架构缓存失败: %v", err)
	}

	return &BaseIDResp{Id: result.ID, Msg: i18n.CreateSuccess}, nil
}

func (l *Impl) UpdateOrganization(ctx context.Context, in *OrganizationInfo) (*BaseResp, error) {
	err := l.DB.Organization.UpdateOneID(in.Id).
		SetStatus(in.Status).
		SetNotEmptySort(in.Sort).
		SetNotEmptyTenantID(in.TenantId).
		SetNotEmptyName(in.Name).
		SetNotEmptyAncestors(in.Ancestors).
		SetNotEmptyCode(in.Code).
		SetNotEmptyNodeType(in.NodeType).
		SetLeader(in.Leader).
		SetPhone(in.Phone).
		SetEmail(in.Email).
		SetRemark(in.Remark).
		SetNotEmptyParentID(in.ParentId).
		Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}
	// 更新查询器缓存
	err = l.cacheInitLoader.UpdateOrganizationName(ctx, in.Id, in.Name)
	if err != nil {
		logc.Errorf(ctx, "更新组织架构缓存失败: %v", err)
	}

	return &BaseResp{Msg: i18n.UpdateSuccess}, nil
}

func (l *Impl) UpdateOrganizationUsers(ctx context.Context, in *UpdateOrganizationUsersReq) (*BaseResp, error) {

	err := entx.WithTx(ctx, l.DB, func(tx *ent.Tx) error {
		// 新增组织架构用户附属信息表中的记录
		var ous []*ent.OrganizationUserInfoCreate
		for _, v := range in.UserSorts {
			ous = append(ous, tx.OrganizationUserInfo.Create().
				SetOrganizationID(in.OrganizationId).
				SetUserID(v.UserId).
				SetSort(v.Sort),
			)
		}

		err := tx.OrganizationUserInfo.CreateBulk(ous...).
			OnConflictColumns(organizationuserinfo.UserColumn, organizationuserinfo.OrganizationColumn).SetUpdatedAt(time.Now().Local()).
			Exec(ctx)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.CreateSuccess}, nil
}

func (l *Impl) UpdateUserOrganizations(ctx context.Context, in *UpdateOrganizationUsersReq) (*BaseResp, error) {
	if in.OrganizationId != "" {
		in.OrganizationIDs = append(in.OrganizationIDs, in.OrganizationId)
	}

	err := entx.WithTx(ctx, l.DB, func(tx *ent.Tx) error {
		// 删除organization-userInfo关系
		_, err := tx.OrganizationUserInfo.Delete().Where(organizationuserinfo.UserIDIn(in.UserIds...)).Exec(schema.SkipSoftDelete(ctx))
		if err != nil {
			return err
		}

		// 组织架构为空
		// 清空用户组织架构
		if len(in.OrganizationIDs) == 0 {
			// 组织架构为空
			// 清空用户组织架构
			for _, userID := range in.UserIds {
				err := tx.User.UpdateOneID(userID).ClearOrganizations().Exec(ctx)
				if err != nil {
					return err
				}
			}
			return nil
		} else {
			for _, userID := range in.UserIds {
				err := tx.User.
					UpdateOneID(userID).
					ClearOrganizations().
					AddOrganizationIDs(in.OrganizationIDs...).
					Exec(ctx)
				if err != nil {
					return err
				}
			}
		}

		// 处理organization-userInfo关系
		for _, organizationId := range in.OrganizationIDs {
			// 3. 确认哪些用户需要新增组织架构信息表记录
			existedUserIds := tx.OrganizationUserInfo.Query().Where(organizationuserinfo.OrganizationID(organizationId), organizationuserinfo.UserIDIn(in.UserIds...)).QueryUser().IDsX(ctx)
			var addingIds []string
			for _, v := range in.UserIds {
				exist := false
				for _, i := range existedUserIds {
					if v == i {
						exist = true
						break
					}
				}
				if !exist {
					addingIds = append(addingIds, v)
				}

			}

			// 4.新增组织架构用户附属信息表中的记录

			var ous []*ent.OrganizationUserInfoCreate

			for _, v := range addingIds {
				ous = append(ous, tx.OrganizationUserInfo.Create().
					SetOrganizationID(organizationId).
					SetUserID(v),
				)
			}

			err = tx.OrganizationUserInfo.CreateBulk(ous...).
				OnConflictColumns(organizationuserinfo.UserColumn, organizationuserinfo.OrganizationColumn).SetUpdatedAt(time.Now().Local()).
				Exec(ctx)
			if err != nil {
				return err
			}
		}

		return nil
	})

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.CreateSuccess}, nil
}
func (l *Impl) GetOrganizationTreeByOrgIds(ctx context.Context, orgId string, organizationIds []string) (os *OrganizationListResp, err error) {
	resp := &OrganizationListResp{}
	result, err := l.DB.Organization.Query().Order(organization.BySort(sql.OrderAsc()), organization.ByID(sql.OrderAsc())).Where(organization.IDIn(organizationIds...)).All(ctx)
	if err != nil {
		return nil, err
	}
	resp.Total = uint64(len(result))

	// 转换
	idDict := make(map[string]*OrganizationInfo)
	infos := make([]*OrganizationInfo, len(result))
	for i, m := range result {
		info := &OrganizationInfo{
			Id:        m.ID,
			CreatedAt: m.CreatedAt.UnixMilli(),
			UpdatedAt: m.UpdatedAt.UnixMilli(),
			Status:    m.Status,
			Sort:      m.Sort,
			Name:      m.Name,
			Leader:    m.Leader,
			Email:     m.Email,
			Phone:     m.Phone,
			Code:      m.Code,
			NodeType:  m.NodeType,
			ParentId:  m.ParentID,
			Remark:    m.Remark,
			Children:  nil,
		}
		idDict[info.Id] = info
		infos[i] = info
	}
	resp.Data = append(resp.Data, idDict[orgId])

	// 组装树结构
	for _, v := range infos {
		if v.ParentId != "" {
			parent, ok := idDict[v.ParentId]
			if !ok {
				continue
			}
			parent.Children = append(parent.Children, v)
		}
	}

	// 设置部门的用户数量
	if err = l.setUserCountToOrganizations(infos); err != nil {
		return
	}

	return resp, nil
}
func (l *Impl) GetOrganizationTree(ctx context.Context) (os *OrganizationListResp, err error) {
	resp := &OrganizationListResp{}
	result, err := l.DB.Organization.Query().Order(organization.BySort(sql.OrderAsc()), organization.ByID(sql.OrderAsc())).All(ctx)
	if err != nil {
		return nil, err
	}
	resp.Total = uint64(len(result))

	// 转换
	idDict := make(map[string]*OrganizationInfo)
	infos := make([]*OrganizationInfo, len(result))
	for i, m := range result {
		info := &OrganizationInfo{
			Id:        m.ID,
			CreatedAt: m.CreatedAt.UnixMilli(),
			UpdatedAt: m.UpdatedAt.UnixMilli(),
			Status:    m.Status,
			Sort:      m.Sort,
			Name:      m.Name,
			Leader:    m.Leader,
			Email:     m.Email,
			Phone:     m.Phone,
			Code:      m.Code,
			NodeType:  m.NodeType,
			ParentId:  m.ParentID,
			Remark:    m.Remark,
			Children:  nil,
		}
		idDict[info.Id] = info
		infos[i] = info
		if info.ParentId == "" {
			resp.Data = append(resp.Data, info)
		}
	}

	// 组装树结构
	for _, v := range infos {
		if v.ParentId != "" {
			parent, ok := idDict[v.ParentId]
			if !ok {
				continue
			}
			parent.Children = append(parent.Children, v)
		}
	}

	// 设置部门的用户数量
	if err = l.setUserCountToOrganizations(infos); err != nil {
		return
	}

	return resp, nil
}
func (l *Impl) GetOrganizationList(ctx context.Context, in *OrganizationListReq) (os *OrganizationListResp, err error) {
	var predicates []predicate.Organization
	if in.Name != "" {
		predicates = append(predicates, organization.NameContains(in.Name))
	}
	if in.Ancestors != "" {
		predicates = append(predicates, organization.AncestorsContains(in.Ancestors))
	}
	if in.Code != "" {
		predicates = append(predicates, organization.CodeContains(in.Code))
	}
	if in.Search != "" {
		predicates = append(predicates,
			organization.Or(organization.NameContains(in.Search)))
	}

	if in.ParentID != "" {
		predicates = append(predicates, organization.ParentIDEQ(in.ParentID))
	}
	// 父级查询条件只在第一层进行处理
	query := l.DB.Organization.Query().Where(predicates...)

	if in.NoParent {
		query = query.Where(organization.Or(organization.ParentIDEQ(""), organization.ParentIDIsNil()))
	}
	result, err := query.
		Order(organization.BySort(sql.OrderAsc()), organization.ByID(sql.OrderAsc())).
		Page(ctx, in.Page, in.PageSize)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	resp := &OrganizationListResp{}
	resp.Total = result.PageDetails.Total

	for _, v := range result.List {
		resp.Data = append(resp.Data, GetOrganizationInfoWithChildren(ctx, v, predicates))
	}

	return resp, nil
}
func GetOrganizationInfoWithChildren(ctx context.Context, m *ent.Organization, predicates []predicate.Organization) *OrganizationInfo {

	var children []*OrganizationInfo

	ch, _ := m.QueryChildren().Where(predicates...).Order(organization.BySort(sql.OrderAsc())).All(ctx)

	for _, v := range ch {
		children = append(children, GetOrganizationInfoWithChildren(ctx, v, predicates))
	}

	return &OrganizationInfo{
		Id:        m.ID,
		CreatedAt: m.CreatedAt.UnixMilli(),
		UpdatedAt: m.UpdatedAt.UnixMilli(),
		Status:    m.Status,
		Sort:      m.Sort,
		Name:      m.Name,
		Leader:    m.Leader,
		Email:     m.Email,
		Phone:     m.Phone,
		Code:      m.Code,
		NodeType:  m.NodeType,
		ParentId:  m.ParentID,
		Remark:    m.Remark,
		Children:  children,
	}
}

func (l *Impl) GetOrganizationUsers(ctx context.Context, in *GetOrganizationUsersReq) (*GetOrganizationUsersResp, error) {

	result, err := l.DB.Organization.Query().
		Where(organization.IDEQ(in.OrganizationId)).
		QueryUsers().Where(user.Or(user.NicknameContains(in.Search), user.UsernameEQ(in.Search), user.MobileEQ(in.Search), user.DeviceNoContains(in.Search), user.EmailContains(in.Search))).
		QueryOrganizationInfos().
		//Where(organizationUserInfoPredicates...).
		WithOrganization().
		WithUser(func(query *ent.UserQuery) {
			query.WithAvatar()
		}).
		Order(ent.Asc(organizationuserinfo.FieldSort)).
		All(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}
	var resp GetOrganizationUsersResp
	var users []*OrganizationUser
	resp.Total = uint64(len(result))

	for _, v := range result {
		users = append(users, &OrganizationUser{
			Id:        v.Edges.User.ID,
			CreatedAt: v.Edges.User.CreatedAt.UnixMilli(),
			Status:    v.Edges.User.Status,
			Username:  v.Edges.User.Username,
			Nickname:  v.Edges.User.Nickname,
			Mobile:    v.Edges.User.Mobile,
			Email:     v.Edges.User.Email,
			Avatar:    GetAvatar(v.Edges.User.Edges.Avatar),
			Sort:      int32(v.Sort),
			Extra:     v.Extra,
			IsLeader:  v.IsLeader,
			IsAdmin:   v.IsAdmin,
		})
	}

	resp.Data = users

	return &resp, nil
}

func (l *Impl) GetOrganizationById(ctx context.Context, in *IDReq) (*OrganizationInfo, error) {
	result, err := l.DB.Organization.Query().Where(organization.IDEQ(in.Id)).Only(ctx)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}
	return &OrganizationInfo{
		Id:        result.ID,
		CreatedAt: result.CreatedAt.UnixMilli(),
		UpdatedAt: result.UpdatedAt.UnixMilli(),
		Status:    result.Status,
		Sort:      result.Sort,
		Name:      result.Name,
		Leader:    result.Leader,
		Email:     result.Email,
		Phone:     result.Phone,
		Code:      result.Code,
		NodeType:  result.NodeType,
		ParentId:  result.ParentID,
		Remark:    result.Remark,
	}, nil
}

func (l *Impl) DeleteOrganization(ctx context.Context, in *IDsReq) (*BaseResp, error) {
	_, err := l.DB.Organization.Delete().Where(organization.IDIn(in.Ids...)).Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.DeleteSuccess}, nil
}

func (l *Impl) GetOrganizationListByCodes(ctx context.Context, codes []string) ([]*OrganizationInfo, error) {
	if len(codes) == 0 {
		return make([]*OrganizationInfo, 0), nil
	}

	result, err := l.DB.Organization.Query().Where(organization.CodeIn(codes...)).All(ctx)
	if err != nil {
		return nil, err
	}
	infos := make([]*OrganizationInfo, len(result))
	for i, m := range result {
		infos[i] = &OrganizationInfo{
			Id:        m.ID,
			CreatedAt: m.CreatedAt.UnixMilli(),
			UpdatedAt: m.UpdatedAt.UnixMilli(),
			Status:    m.Status,
			Sort:      m.Sort,
			Name:      m.Name,
			Leader:    m.Leader,
			Email:     m.Email,
			Phone:     m.Phone,
			Code:      m.Code,
			NodeType:  m.NodeType,
			ParentId:  m.ParentID,
			Remark:    m.Remark,
		}
	}
	return infos, nil
}

func (l *Impl) CreateOrganizationUserInfo(ctx context.Context, in *OrganizationUserInfoInfo) (*BaseIDResp, error) {
	var result *ent.OrganizationUserInfo

	err := entx.WithTx(ctx, l.DB, func(tx *ent.Tx) error {
		_, err := tx.Organization.UpdateOneID(in.OrganizationId).
			AddUserIDs(in.UserId).
			Save(ctx)

		if err != nil {
			return dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
		}

		if !tx.OrganizationUserInfo.Query().
			Where(organizationuserinfo.OrganizationIDEQ(in.OrganizationId),
				organizationuserinfo.UserIDEQ(in.UserId)).
			ExistX(ctx) {

			result, err = l.DB.OrganizationUserInfo.Create().
				SetSort(in.Sort).
				SetOrganizationID(in.OrganizationId).
				SetUserID(in.UserId).
				SetExtra(in.Extra).
				Save(ctx)

			if err != nil {
				return dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
			}
		}
		return nil
	})

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseIDResp{Id: result.ID, Msg: i18n.CreateSuccess}, nil
}

func (l *Impl) UpdateOrganizationUserInfo(ctx context.Context, in *UpdateOrganizationUserInfo) (*BaseResp, error) {
	err := l.DB.OrganizationUserInfo.
		UpdateOne(l.DB.OrganizationUserInfo.
			Query().
			Where(organizationuserinfo.OrganizationIDEQ(in.OrganizationId), organizationuserinfo.UserIDEQ(in.UserId)).
			Order(ent.Desc(organizationuserinfo.FieldCreatedAt)).
			FirstX(ctx)).
		SetNotEmptySort(in.Sort).
		SetNotEmptyExtra(in.Extra).
		Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.UpdateSuccess}, nil
}

func (l *Impl) UpdateOrganizationUserInfos(ctx context.Context, in []*UpdateOrganizationUserInfo) (*BaseResp, error) {
	err := entx.WithTx(ctx, l.DB, func(tx *ent.Tx) error {
		for _, info := range in {
			// 通过组织ID和用户ID唯一定位
			err := tx.OrganizationUserInfo.
				Update().
				Where(
					organizationuserinfo.OrganizationIDEQ(info.OrganizationId),
					organizationuserinfo.UserIDEQ(info.UserId),
				).
				SetSort(info.Sort).
				SetNotEmptyExtra(info.Extra).
				Exec(ctx)
			if err != nil {
				return err
			}
		}
		return nil
	})
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}
	return &BaseResp{Msg: i18n.UpdateSuccess}, nil
}

func (l *Impl) GetOrganizationUserInfoList(ctx context.Context, in *OrganizationUserInfoListReq) (*OrganizationUserInfoUserInfoListResp, error) {
	var predicates []predicate.OrganizationUserInfo
	if in.OrganizationId != "" {
		predicates = append(predicates, organizationuserinfo.OrganizationIDIn(in.OrganizationId))
	}
	if in.Search != "" {
		predicates = append(predicates, organizationuserinfo.HasUserWith(
			user.Or(
				user.NicknameContains(in.Search),
				user.UsernameEQ(in.Search),
				user.MobileEQ(in.Search),
				user.EmailContains(in.Search),
				user.DeviceNoContains(in.Search),
			)))
	}
	tenantId := utils.GetCurrentLoginUser(ctx).TenantId
	predicates = append(predicates, organizationuserinfo.HasUserWith(user.DeletedAtIsNil(), user.HasTenantsWith(tenant.IDEQ(tenantId), tenant.DeletedAtIsNil())))
	oq := l.DB.OrganizationUserInfo.Query().
		Where(predicates...).
		WithUser(func(query *ent.UserQuery) {
			query.WithAvatar()
		}).
		Order(ent.Desc(organizationuserinfo.FieldSort)).
		WithOrganization()

	var ous []*ent.OrganizationUserInfo
	var total uint64
	if in.NoPage {
		all, err := oq.All(ctx)
		if err != nil {
			return nil, err
		}
		total = uint64(len(all))
		ous = all
	} else {
		result, err := oq.
			Page(ctx, in.Page, in.PageSize)
		if err != nil {
			return nil, err
		}
		total = result.PageDetails.Total
		ous = result.List
	}

	resp := &OrganizationUserInfoUserInfoListResp{}
	resp.Total = total

	for _, v := range ous {
		avatar := &Avatar{}
		if v.Edges.User.Edges.Avatar != nil {
			avatar = &Avatar{
				Id:         v.Edges.User.Edges.Avatar.ID,
				Name:       v.Edges.User.Edges.Avatar.Name,
				OriginName: v.Edges.User.Edges.Avatar.OriginName,
				Path:       v.Edges.User.Edges.Avatar.Path,
				OpenStatus: uint32(v.Edges.User.Edges.Avatar.OpenStatus),
			}
		}
		resp.Data = append(resp.Data, &OrganizationUserInfoUserInfo{
			Id:              v.UserID,
			CreatedAt:       v.CreatedAt.UnixMilli(),
			Status:          v.Edges.User.Status,
			Username:        v.Edges.User.Username,
			Sort:            int32(v.Sort),
			Nickname:        v.Edges.User.Nickname,
			Mobile:          v.Edges.User.Mobile,
			Email:           v.Edges.User.Email,
			Avatar:          avatar,
			DefaultTenantId: v.Edges.User.DefaultTenantID,
			Extra:           v.Extra,
			Gender:          v.Edges.User.Gender.String(),
			Post:            v.Edges.User.Post,
			OrganizationId:  v.OrganizationID,
			IsLeader:        v.IsLeader,
			IsAdmin:         v.IsAdmin,
		})
	}

	return resp, nil
}

func (l *Impl) GetOrganizationUserInfoById(ctx context.Context, in *IDReq) (*OrganizationUserInfoInfo, error) {
	result, err := l.DB.OrganizationUserInfo.Get(ctx, in.Id)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &OrganizationUserInfoInfo{
		Id:             result.ID,
		CreatedAt:      result.CreatedAt.UnixMilli(),
		UpdatedAt:      result.UpdatedAt.UnixMilli(),
		Sort:           result.Sort,
		OrganizationId: result.OrganizationID,
		UserId:         result.UserID,
		Extra:          result.Extra,
		IsLeader:       result.IsLeader,
		IsAdmin:        result.IsAdmin,
	}, nil
}

func (l *Impl) DeleteOrganizationUserInfo(ctx context.Context, in *DeleteOrganizationUserInfo) (*BaseResp, error) {
	err := entx.WithTx(ctx, l.DB, func(tx *ent.Tx) error {
		err := tx.Organization.UpdateOneID(in.OrganizationId).
			RemoveUserIDs(in.UserIds...).
			Exec(ctx)
		if err != nil {
			return err
		}

		_, err = tx.OrganizationUserInfo.Delete().
			Where(organizationuserinfo.OrganizationIDEQ(in.OrganizationId),
				organizationuserinfo.UserIDIn(in.UserIds...)).
			Exec(ctx)
		if err != nil {
			return err
		}

		return nil

	})

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.DeleteSuccess}, nil
}

func (l *Impl) GetOrganizationUserInfoListAll(ctx context.Context, in *OrganizationUserTreeReq) (*OrganizationUserInfoUserInfoListAllResp, error) {
	// 只支持通过 OrganizationIds 批量查
	if len(in.OrgIds) == 0 {
		return &OrganizationUserInfoUserInfoListAllResp{Data: []*OrganizationUserInfoOrganizationInfoLite{}}, nil
	}
	result, err := l.DB.Organization.Query().
		Where(
			organization.StatusEQ(true),
			organization.DeletedAtIsNil(),
			organization.IDIn(in.OrgIds...),
		).
		WithOrganizationInfos(func(query *ent.OrganizationUserInfoQuery) {
			query.
				WithUser(func(userQuery *ent.UserQuery) {
					userQuery.Where(user.StatusEQ(true), user.DeletedAtIsNil())
				})
		}).
		Order(ent.Asc(organization.FieldSort)).
		Order(ent.Asc(organization.FieldID)).
		All(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	resp := &OrganizationUserInfoUserInfoListAllResp{}
	for _, v := range result {
		var users []*OrganizationUsers
		for _, w := range v.Edges.OrganizationInfos {
			if w.Edges.User != nil {
				users = append(users, &OrganizationUsers{
					UserID:   w.Edges.User.ID,
					Nickname: w.Edges.User.Nickname,
				})
			}
		}
		resp.Data = append(resp.Data, &OrganizationUserInfoOrganizationInfoLite{
			OrgID:   v.ID,
			OrgName: v.Name,
			Users:   users,
		})
	}
	return resp, nil
}

func (l *Impl) GetUserOrganizationInfoListAll(ctx context.Context, in *GetUserOrganizationsInfoListReq) (*UserOrganizationInfoListAllResp, error) {
	result, err := l.DB.OrganizationUserInfo.Query().
		Where(organizationuserinfo.UserID(in.UserId)).
		WithOrganization().
		Order(ent.Asc(organizationuserinfo.FieldSort)).
		Order(ent.Asc(organizationuserinfo.FieldID)).
		All(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	resp := &UserOrganizationInfoListAllResp{}

	for _, v := range result {
		resp.Data = append(resp.Data, &UserOrganizationsOrganizationInfoLite{
			Id:       v.OrganizationID,
			Status:   v.Edges.Organization.Status,
			Sort:     v.Sort,
			TenantId: v.Edges.Organization.TenantID,
			Name:     v.Edges.Organization.Name,
			Code:     v.Edges.Organization.Code,
			NodeType: v.Edges.Organization.NodeType,
			Leader:   v.Edges.Organization.Leader,
			ParentId: v.Edges.Organization.ParentID,
		})
	}

	return resp, nil
}

func (l *Impl) CreatePosition(ctx context.Context, in *PositionInfo) (*BaseIDResp, error) {
	result, err := l.DB.Position.Create().
		SetStatus(in.Status).
		SetSort(in.Sort).
		SetTenantID(in.TenantId).
		SetName(in.Name).
		SetCode(in.Code).
		SetRemark(in.Remark).
		SetOrganizationID(in.OrganizationId).
		Save(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseIDResp{Id: result.ID, Msg: i18n.CreateSuccess}, nil
}

func (l *Impl) UpdatePosition(ctx context.Context, in *PositionInfo) (*BaseResp, error) {
	err := l.DB.Position.UpdateOneID(in.Id).
		SetStatus(in.Status).
		SetNotEmptySort(in.Sort).
		SetNotEmptyTenantID(in.TenantId).
		SetNotEmptyName(in.Name).
		SetNotEmptyCode(in.Code).
		SetNotEmptyRemark(in.Remark).
		Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.UpdateSuccess}, nil
}

func (l *Impl) GetPositionList(ctx context.Context, in *PositionListReq) (*PositionListResp, error) {
	var predicates []predicate.Position
	predicates = append(predicates, position.OrganizationID(in.OrganizationId))
	if in.Name != "" {
		predicates = append(predicates, position.NameContains(in.Name))
	}
	if in.Code != "" {
		predicates = append(predicates, position.CodeContains(in.Code))
	}
	if in.Remark != "" {
		predicates = append(predicates, position.RemarkContains(in.Remark))
	}
	if in.Status != "" {
		if in.Status == "true" {
			predicates = append(predicates, position.Status(true))
		} else {
			predicates = append(predicates, position.Status(false))
		}
	}
	if in.Search != "" {
		predicates = append(predicates,
			position.Or(position.NameContains(in.Search)))
	}
	result, err := l.DB.Position.Query().Where(predicates...).Page(ctx, in.Page, in.PageSize)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	resp := &PositionListResp{}
	resp.Total = result.PageDetails.Total

	for _, v := range result.List {
		resp.Data = append(resp.Data, &PositionInfo{
			Id:        v.ID,
			CreatedAt: v.CreatedAt.UnixMilli(),
			UpdatedAt: v.UpdatedAt.UnixMilli(),
			Status:    v.Status,
			Sort:      v.Sort,
			TenantId:  v.TenantID,
			Name:      v.Name,
			Code:      v.Code,
			Remark:    v.Remark,
		})
	}

	return resp, nil
}

func (l *Impl) GetPositionById(ctx context.Context, in *IDReq) (*PositionInfo, error) {
	result, err := l.DB.Position.Get(ctx, in.Id)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &PositionInfo{
		Id:        result.ID,
		CreatedAt: result.CreatedAt.UnixMilli(),
		UpdatedAt: result.UpdatedAt.UnixMilli(),
		Status:    result.Status,
		Sort:      result.Sort,
		TenantId:  result.TenantID,
		Name:      result.Name,
		Code:      result.Code,
		Remark:    result.Remark,
	}, nil
}

func (l *Impl) DeletePosition(ctx context.Context, in *IDsReq) (*BaseResp, error) {
	_, err := l.DB.Position.Delete().Where(position.IDIn(in.Ids...)).Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.DeleteSuccess}, nil
}

func (l *Impl) CreateRole(ctx context.Context, in *RoleInfo) (*BaseIDResp, error) {

	// 检查code是否存在
	exist, err := l.DB.Role.Query().Where(role.CodeEQ(in.Code), role.OrganizationID(in.OrganizationId)).Exist(ctx)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}
	if exist {
		return nil, errors.New("角色CODE不能重复。")
	}

	var parent *ent.Role
	if in.ParentId != "" {
		parent, err = l.DB.Role.Query().Where(role.IDEQ(in.ParentId)).Only(ctx)
		if err != nil {
			return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
		}
	}

	q := l.DB.Role.Create().
		SetStatus(in.Status).
		SetSort(in.Sort).
		SetTenantID(in.TenantId).
		SetName(in.Name).
		SetCode(in.Code).
		SetDefaultRouter(in.DefaultRouter).
		SetRemark(in.Remark).
		SetOrganizationID(in.OrganizationId)

	if parent != nil {
		q.SetParent(parent)
	}

	result, err := q.Save(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseIDResp{Id: result.ID, Msg: i18n.CreateSuccess}, nil
}

func (l *Impl) UpdateRole(ctx context.Context, in *RoleInfo) (*BaseResp, error) {
	err := l.DB.Role.UpdateOneID(in.Id).
		SetStatus(in.Status).
		SetNotEmptySort(in.Sort).
		SetNotEmptyTenantID(in.TenantId).
		SetNotEmptyName(in.Name).
		SetNotEmptyCode(in.Code).
		SetNotEmptyDefaultRouter(in.DefaultRouter).
		SetNotEmptyRemark(in.Remark).
		SetNotEmptyParentID(in.ParentId).
		Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.UpdateSuccess}, nil
}

func (l *Impl) GetRoleList(ctx context.Context, in *RoleListReq) (*RoleListResp, error) {
	var predicates []predicate.Role
	predicates = append(predicates, role.OrganizationID(in.OrganizationId))

	if in.Name != "" {
		predicates = append(predicates, role.NameContains(in.Name))
	}
	if in.Code != "" {
		predicates = append(predicates, role.CodeContains(in.Code))
	}
	if in.DefaultRouter != "" {
		predicates = append(predicates, role.DefaultRouterContains(in.DefaultRouter))
	}
	if in.NoParent {
		predicates = append(predicates, role.Or(role.ParentIDEQ(""), role.ParentIDIsNil()))
	}
	if in.Search != "" {
		predicates = append(predicates,
			role.Or(role.NameContains(in.Search), role.CodeContains(in.Search)))
	}
	result, err := l.DB.Role.Query().
		Where(predicates...).
		Order(role.BySort(sql.OrderAsc())).
		Page(ctx, in.Page, in.PageSize)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	resp := &RoleListResp{}
	resp.Total = result.PageDetails.Total
	//for _, v := range result.List {
	//	resp.Data = append(resp.Data, GetRoleInfoWithChildren(ctx, v))
	//}

	for _, v := range result.List {
		resp.Data = append(resp.Data, &RoleInfo{
			Id:             v.ID,
			CreatedAt:      v.CreatedAt.UnixMilli(),
			UpdatedAt:      v.UpdatedAt.UnixMilli(),
			Status:         v.Status,
			Sort:           v.Sort,
			TenantId:       v.TenantID,
			Name:           v.Name,
			Code:           v.Code,
			DefaultRouter:  v.DefaultRouter,
			Remark:         v.Remark,
			ParentId:       v.ParentID,
			OrganizationId: v.OrganizationID,
		})
	}

	return resp, nil
}

func (l *Impl) GetRoleById(ctx context.Context, in *IDReq) (*RoleInfo, error) {
	result, err := l.DB.Role.Get(ctx, in.Id)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &RoleInfo{
		Id:             result.ID,
		CreatedAt:      result.CreatedAt.UnixMilli(),
		UpdatedAt:      result.UpdatedAt.UnixMilli(),
		Status:         result.Status,
		Sort:           result.Sort,
		TenantId:       result.TenantID,
		Name:           result.Name,
		Code:           result.Code,
		DefaultRouter:  result.DefaultRouter,
		Remark:         result.Remark,
		ParentId:       result.ParentID,
		OrganizationId: result.OrganizationID,
	}, nil
}

func (l *Impl) GetRoleWithAPI(ctx context.Context, in *Empty) (*RoleWithAPIListResp, error) {
	result, err := l.DB.Role.Query().WithApis().
		Order(role.BySort(sql.OrderAsc())).All(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	resp := &RoleWithAPIListResp{}
	resp.Total = uint64(len(result))

	for _, v := range result {
		resp.Data = append(resp.Data, GetRoleInfoWithAPI(ctx, v))
	}

	return resp, nil
}

func GetRoleInfoWithAPI(ctx context.Context, obj *ent.Role) *RoleWithAPIInfo {

	var apis []*APIInfo

	for _, v := range obj.Edges.Apis {
		apis = append(apis, &APIInfo{
			Id:       v.ID,
			Method:   v.Method,
			Path:     v.Path,
			ApiGroup: v.APIGroup,
		})
	}

	return &RoleWithAPIInfo{
		Id:   obj.ID,
		Apis: apis,
	}
}

func (l *Impl) DeleteRole(ctx context.Context, in *IDsReq) (*BaseResp, error) {
	_, err := l.DB.Role.Delete().Where(role.IDIn(in.Ids...)).Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.DeleteSuccess}, nil
}

func (l *Impl) UpdateRoleUsers(ctx context.Context, in *UpdateRoleUsersReq) (*BaseResp, error) {
	err := l.DB.Role.
		UpdateOneID(in.RoleId).
		// 1. 直接删除Role下所有user关联
		ClearUsers().
		// 2. 重新添加user关联
		AddUserIDs(in.UserIds...).
		Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.CreateSuccess}, nil
}

func (l *Impl) GetRoleUsers(ctx context.Context, in *GetRoleUsersReq) (*RoleWithUsersResp, error) {
	result, err := l.DB.Role.Query().
		Where(role.IDEQ(in.RoleId)).
		WithUsers(func(query *ent.UserQuery) {
			query.WithAvatar()
		}).
		Order(ent.Asc(role.FieldSort)).
		Only(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}
	var users []*RoleUser

	for _, v := range result.Edges.Users {
		users = append(users, &RoleUser{
			Id:        v.ID,
			CreatedAt: v.CreatedAt.UnixMilli(),
			Status:    v.Status,
			Username:  v.Username,
			Nickname:  v.Nickname,
			Mobile:    v.Mobile,
			Email:     v.Email,
			Avatar:    GetAvatar(v.Edges.Avatar),
		})
	}

	if len(result.Edges.Users) == 0 {
		users = make([]*RoleUser, 0)
	}

	resp := &RoleWithUsersResp{
		Id:            result.ID,
		CreatedAt:     result.CreatedAt.UnixMilli(),
		Status:        result.Status,
		Sort:          result.Sort,
		TenantId:      result.TenantID,
		Name:          result.Name,
		Code:          result.Code,
		DefaultRouter: result.DefaultRouter,
		Remark:        result.Remark,
		ParentId:      result.ParentID,
		Users:         users,
	}
	return resp, nil
}

func (l *Impl) CreateTenant(ctx context.Context, in *TenantCreateInfo) (*BaseIDResp, error) {
	result, err := l.DB.Tenant.Create().
		SetStatus(in.Status).
		SetName(in.Name).
		SetServiceStartAt(time.UnixMilli(in.ServiceStartAt)).
		SetServiceEndAt(time.UnixMilli(in.ServiceEndAt)).
		SetAfterSalesContact(in.AfterSalesContact).
		SetLocationID(in.LocationID).
		SetLogSaveKeepDays(in.LogSaveKeepDays).
		SetMaxAttendanceUserCount(in.MaxAttendanceUserCount).
		SetMaxDeviceCount(in.MaxDeviceCount).
		SetMaxUploadFileSize(in.MaxUploadFileSize).
		SetMaxUserCount(in.MaxUserCount).
		SetPrincipal(in.Principal).
		SetPrincipalContactInformation(in.PrincipalContactInformation).
		SetSaleContact(in.SaleContact).
		SetSecretKey(in.SecretKey).
		SetAiStatus(in.AiStatus).
		SetMaxConferenceAgendaTitle(in.MaxConferenceAgendaTitle).
		Save(ctx)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}
	if len(in.SystemPlugins) == 0 {
		return &BaseIDResp{Id: result.ID, Msg: i18n.CreateSuccess}, nil
	}
	plugins := make([]mapper.TenantPlugin, 0, len(in.SystemPlugins))
	t := time.Now()
	for _, v := range in.SystemPlugins {
		plugins = append(plugins, mapper.TenantPlugin{
			PluginID:  v,
			TenantID:  result.ID,
			CreatedAt: t,
			CreatedBy: utils.GetCurrentLoginUser(ctx).UserId,
		})
	}
	if err := mapper.NewTenantPluginClient(l.gormDB).BatchCreate(ctx, plugins); err != nil {
		return nil, err
	}
	return &BaseIDResp{Id: result.ID, Msg: i18n.CreateSuccess}, nil
}

func (l *Impl) UpdateTenant(ctx context.Context, in *TenantUpdateInfo) (*BaseResp, error) {
	first, err := l.DB.Tenant.Query().Where(tenant.IDEQ(in.ID)).First(ctx)
	if err != nil {
		logc.Error(ctx, err)
		return nil, errors.New("租户不存在")
	}
	// 更新租户插件信息
	gc := mapper.NewTenantPluginClient(l.gormDB)
	if err := gc.DB.Transaction(func(tx *gorm.DB) error {
		// 更新租户信息
		if err := mapper.NewTenantClient(l.gormDB).UpdateWithTx(ctx, tx, mapper.SaasTenant{
			ID:                          in.ID,
			Status:                      in.Status,
			Name:                        in.Name,
			ServiceStartAt:              time.UnixMilli(in.ServiceStartAt),
			ServiceEndAt:                time.UnixMilli(in.ServiceEndAt),
			AfterSalesContact:           in.AfterSalesContact,
			LocationID:                  in.LocationID,
			LogSaveKeepDays:             in.LogSaveKeepDays,
			MaxAttendanceUserCount:      in.MaxAttendanceUserCount,
			MaxDeviceCount:              in.MaxDeviceCount,
			MaxUploadFileSize:           in.MaxUploadFileSize,
			MaxUserCount:                in.MaxUserCount,
			Principal:                   in.Principal,
			PrincipalContactInformation: in.PrincipalContactInformation,
			SaleContact:                 in.SaleContact,
			UUID:                        first.UUID.String(),
			Key:                         first.Key,
			Secret:                      first.Secret,
			CreatedAt:                   &first.CreatedAt,
			IsSuper:                     first.IsSuper,
			AiStatus:                    in.AiStatus,
			MaxConferenceAgendaTitle:    in.MaxConferenceAgendaTitle,
		}); err != nil {
			return err
		}
		// 删除租户插件信息
		if err := gc.BatchDelByTenantIDWithTx(ctx, tx, in.ID); err != nil {
			return err
		}
		plugins := make([]mapper.TenantPlugin, 0, len(in.SystemPlugins))
		t := time.Now()
		for _, v := range in.SystemPlugins {
			plugins = append(plugins, mapper.TenantPlugin{
				PluginID:  v,
				TenantID:  in.ID,
				CreatedAt: t,
				CreatedBy: utils.GetCurrentLoginUser(ctx).UserId,
			})
		}
		// 保存租户插件信息
		if err := gc.BatchCreateWithTx(ctx, tx, plugins); err != nil {
			return err
		}

		return nil
	}); err != nil {
		return nil, err
	}
	return &BaseResp{Msg: i18n.UpdateSuccess}, nil
}

func (l *Impl) GetTenantList(ctx context.Context, in *TenantListReq) (*TenantListResp, error) {
	var predicates []predicate.Tenant
	if in.Key != "" {
		predicates = append(predicates, tenant.KeyContains(in.Key))
	}
	if in.Secret != "" {
		predicates = append(predicates, tenant.SecretContains(in.Secret))
	}
	if in.Name != "" {
		predicates = append(predicates, tenant.NameContains(in.Name))
	}
	if in.Search != "" {
		predicates = append(predicates,
			tenant.Or(tenant.NameContains(in.Search)))
	}
	tq := l.DB.Tenant.Query().Where(predicates...)
	var ts []*ent.Tenant
	var total uint64
	if in.NoPage {
		res, err := tq.All(ctx)
		if err != nil {
			return nil, err
		}
		ts = res
		total = uint64(len(res))
	} else {
		result, err := tq.Page(ctx, in.Page, in.PageSize)
		if err != nil {
			return nil, err
		}
		ts = result.List
		total = result.PageDetails.Total
	}
	resp := &TenantListResp{}
	resp.Total = total

	for _, v := range ts {

		serviceStartAt := v.ServiceStartAt.UnixMilli()
		serviceEndAt := v.ServiceEndAt.UnixMilli()

		if serviceStartAt < 0 {
			serviceStartAt = 0
		}

		if serviceEndAt < 0 {
			serviceEndAt = 0
		}

		resp.Data = append(resp.Data, &TenantInfo{
			Id:                          v.ID,
			CreatedAt:                   v.CreatedAt.UnixMilli(),
			UpdatedAt:                   v.UpdatedAt.UnixMilli(),
			Uuid:                        v.UUID.String(),
			Key:                         v.Key,
			Secret:                      v.Secret,
			Status:                      v.Status,
			Name:                        v.Name,
			IsSuper:                     v.IsSuper,
			ServiceStartAt:              serviceStartAt,
			ServiceEndAt:                serviceEndAt,
			AfterSalesContact:           v.AfterSalesContact,
			LocationID:                  v.LocationID,
			LogSaveKeepDays:             v.LogSaveKeepDays,
			MaxAttendanceUserCount:      v.MaxAttendanceUserCount,
			MaxDeviceCount:              v.MaxDeviceCount,
			MaxUploadFileSize:           v.MaxUploadFileSize,
			MaxUserCount:                v.MaxUserCount,
			Principal:                   v.Principal,
			PrincipalContactInformation: v.PrincipalContactInformation,
			SaleContact:                 v.SaleContact,
			AiStatus:                    v.AiStatus,
			MaxConferenceAgendaTitle:    v.MaxConferenceAgendaTitle,
		})
	}

	return resp, nil
}

func (l *Impl) GetTenantById(ctx context.Context, in *IDReq) (*TenantInfo, error) {
	result, err := l.DB.Tenant.Get(ctx, in.Id)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	// 查询租户插件信息
	ps, err := mapper.NewTenantPluginClient(l.gormDB).QueryPluginInfosByTenantID(ctx, in.Id)
	if err != nil {
		logc.Error(ctx, "query tenant plugin fail:", err)
		return nil, err
	}
	infos := make([]SystemPluginInfo, 0, len(ps))
	for _, p := range ps {
		infos = append(infos, SystemPluginInfo{
			ID:   p.PluginID,
			Name: p.PluginName,
			Code: p.PluginCode,
		})
	}
	return &TenantInfo{
		Id:                          result.ID,
		CreatedAt:                   result.CreatedAt.UnixMilli(),
		UpdatedAt:                   result.UpdatedAt.UnixMilli(),
		Uuid:                        result.UUID.String(),
		Key:                         result.Key,
		Secret:                      result.Secret,
		Status:                      result.Status,
		Name:                        result.Name,
		IsSuper:                     result.IsSuper,
		ServiceStartAt:              result.ServiceStartAt.UnixMilli(),
		ServiceEndAt:                result.ServiceEndAt.UnixMilli(),
		AfterSalesContact:           result.AfterSalesContact,
		LocationID:                  result.LocationID,
		LogSaveKeepDays:             result.LogSaveKeepDays,
		MaxAttendanceUserCount:      result.MaxAttendanceUserCount,
		MaxDeviceCount:              result.MaxDeviceCount,
		MaxUploadFileSize:           result.MaxUploadFileSize,
		MaxUserCount:                result.MaxUserCount,
		Principal:                   result.Principal,
		PrincipalContactInformation: result.PrincipalContactInformation,
		SaleContact:                 result.SaleContact,
		SystemPlugins:               infos,
		AiStatus:                    result.AiStatus,
		MaxConferenceAgendaTitle:    result.MaxConferenceAgendaTitle,
	}, nil
}

func (l *Impl) GetTenantByUserId(ctx context.Context, in *IDReq) (*TenantListResp, error) {
	now := time.Now()
	result, err := l.DB.User.Query().Where(user.IDEQ(in.Id)).
		WithTenants(func(query *ent.TenantQuery) {
			query.Where(tenant.ServiceEndAtGTE(now), tenant.ServiceStartAtLTE(now), tenant.StatusEQ(true))
		}).
		Only(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	resp := &TenantListResp{}
	resp.Total = uint64(len(result.Edges.Tenants))

	//// 如果用户没有默认租户，则设置第一个租户为默认租户
	//if result.DefaultTenantID == "" && len(result.Edges.Tenants) > 0 {
	//	l.DB.User.UpdateOne(result).SetDefaultTenantID(result.Edges.Tenants[0].ID).Exec(ctx)
	//}
	ts := result.Edges.Tenants
	if result.IsSuperuser {
		// 超级管理可以切换所有租户
		ts, err = l.DB.Tenant.Query().Where().All(ctx)
		if err != nil {
			return nil, err
		}
		resp.Total = uint64(len(ts))
	}

	for _, v := range ts {

		serviceStartAt := v.ServiceStartAt.UnixMilli()
		serviceEndAt := v.ServiceEndAt.UnixMilli()

		if serviceStartAt < 0 {
			serviceStartAt = 0
		}

		if serviceEndAt < 0 {
			serviceEndAt = 0
		}

		resp.Data = append(resp.Data, &TenantInfo{
			Id:             v.ID,
			CreatedAt:      v.CreatedAt.UnixMilli(),
			UpdatedAt:      v.UpdatedAt.UnixMilli(),
			Uuid:           v.UUID.String(),
			Key:            v.Key,
			Secret:         v.Secret,
			Status:         v.Status,
			Name:           v.Name,
			IsSuper:        v.IsSuper,
			ServiceStartAt: serviceStartAt,
			ServiceEndAt:   serviceEndAt,
		})
	}

	return resp, nil
}

func (l *Impl) DeleteTenant(ctx context.Context, in *IDsReq) (*BaseResp, error) {
	_, err := l.DB.Tenant.Delete().Where(tenant.IDIn(in.Ids...)).Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.DeleteSuccess}, nil
}

func (l *Impl) UpdateTenantUsers(ctx context.Context, in *UpdateTenantUsersReq) (*BaseResp, error) {
	err := entx.WithTx(ctx, l.DB, func(tx *ent.Tx) error {
		// 查询类型用户列表
		ids, err := tx.User.Query().Where(user.KindEQ(in.Kind)).WithTenants(func(query *ent.TenantQuery) {
			query.Where(tenant.IDEQ(in.TenantId))
		}).IDs(schema.SkipSoftDelete(ctx))
		if err != nil {
			return err
		}

		err = tx.Tenant.
			UpdateOneID(in.TenantId).
			// 1. 直接删除租户下所有user关联
			RemoveUserIDs(ids...).
			// 2. 重新添加user关联
			AddUserIDs(in.UserIds...).
			Exec(ctx)

		if err != nil {
			return err
		}

		// 3. 确认哪些用户需要新增租户信息表记录
		// 删除
		_, err = tx.TenantUserInfo.Delete().
			Where(tenantuserinfo.TenantIDEQ(in.TenantId), tenantuserinfo.UserIDIn(ids...)).
			Exec(schema.SkipSoftDelete(ctx))
		if err != nil {
			return err
		}
		// 4.新增租户用户附属信息表中的记录

		var ous []*ent.TenantUserInfoCreate
		for _, v := range in.UserIds {
			ous = append(ous, tx.TenantUserInfo.Create().
				SetTenantID(in.TenantId).
				SetUserID(v),
			)
		}

		err = tx.TenantUserInfo.CreateBulk(ous...).
			OnConflictColumns("tenant_id", "user_id").
			DoNothing().
			Exec(ctx)

		if err != nil {
			return err
		}
		return nil

	})

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.CreateSuccess}, nil
}

func (l *Impl) GetTenantUsers(ctx context.Context, in *GetTenantUsersReq) (*GetTenantUsersResp, error) {
	var tenantPredicates []predicate.Tenant
	var userPredicates []predicate.User
	if in.UserId != "" {
		userPredicates = append(userPredicates, user.IDEQ(in.UserId))
	} else {
		// 查询列表时屏蔽管理员
		userPredicates = append(userPredicates, user.IsSuperuserEQ(false))
	}
	if in.TenantId != "" {
		tenantPredicates = append(tenantPredicates, tenant.IDEQ(in.TenantId))
	}
	if in.Search != "" {
		userPredicates = append(userPredicates, user.Or(
			user.NicknameContains(in.Search),
			user.UsernameEQ(in.Search),
			user.MobileEQ(in.Search),
			user.EmailContains(in.Search),
			user.DeviceNoContains(in.Search),
		))
	}
	if in.Kind != "" {
		userPredicates = append(userPredicates, user.KindEQ(in.Kind))
	}

	if in.PageSize <= 0 {
		in.PageSize = 1000
	}
	if in.Page <= 0 {
		in.Page = 1
	}
	tq := l.DB.Tenant.Query().
		Where(tenantPredicates...).
		QueryUsers().Where(userPredicates...).
		WithAvatar().
		WithTenantInfos().
		Order(ent.Desc(user.FieldUpdatedAt))

	var us []*ent.User
	var total uint64

	if in.NoPage {
		result, err :=
			tq.All(schema.SkipTenantInject(ctx))
		if err != nil {
			return nil, err
		}
		total = uint64(len(result))
		us = result
	} else {
		result, err :=
			tq.Page(schema.SkipTenantInject(ctx), in.Page, in.PageSize)
		if err != nil {
			return nil, err
		}
		total = result.PageDetails.Total
		us = result.List
	}

	var resp GetTenantUsersResp
	var users []*TenantUser
	resp.Total = total

	for _, v := range us {
		var tuInfo *ent.TenantUserInfo
		if len(v.Edges.TenantInfos) > 0 {
			tuInfo = v.Edges.TenantInfos[0]
		} else {
			tuInfo = &ent.TenantUserInfo{}
		}
		users = append(users, &TenantUser{
			Id:        v.ID,
			CreatedAt: v.CreatedAt.UnixMilli(),
			Status:    v.Status,
			Username:  v.Username,
			Nickname:  v.Nickname,
			Mobile:    v.Mobile,
			Email:     v.Email,
			Avatar:    GetAvatar(v.Edges.Avatar),
			Sort:      int32(tuInfo.Sort),
			Extra:     tuInfo.Extra,
			Kind:      v.Kind,
			Imei:      v.Imei,
			DeviceNo:  v.DeviceNo,
		})
	}

	resp.Data = users

	return &resp, nil
}

func (l *Impl) CreateTenantUserInfo(ctx context.Context, in *TenantUserInfoInfo) (*BaseIDResp, error) {
	var result *ent.TenantUserInfo

	err := entx.WithTx(ctx, l.DB, func(tx *ent.Tx) error {
		_, err := tx.Tenant.UpdateOneID(in.TenantId).
			AddUserIDs(in.UserId).
			Save(ctx)

		if err != nil {
			return dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
		}

		if !tx.TenantUserInfo.Query().
			Where(tenantuserinfo.TenantIDEQ(in.TenantId),
				tenantuserinfo.UserIDEQ(in.UserId)).
			ExistX(ctx) {

			result, err = l.DB.TenantUserInfo.Create().
				SetSort(in.Sort).
				SetTenantID(in.TenantId).
				SetUserID(in.UserId).
				SetExtra(in.Extra).
				Save(ctx)

			if err != nil {
				return dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
			}
		}
		return nil
	})

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseIDResp{Id: result.ID, Msg: i18n.CreateSuccess}, nil
}

func (l *Impl) UpdateTenantUserInfo(ctx context.Context, in *TenantUserInfoInfo) (*BaseResp, error) {
	tu := l.DB.TenantUserInfo.
		Query().
		Where(tenantuserinfo.TenantIDEQ(in.TenantId), tenantuserinfo.UserIDEQ(in.UserId)).
		Order(ent.Desc(tenantuserinfo.FieldCreatedAt)).
		FirstX(ctx)

	if tu == nil {
		err := l.DB.TenantUserInfo.Create().SetTenantID(in.TenantId).
			SetUserID(in.UserId).Exec(ctx)
		if err != nil {
			return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
		}
	}
	err := l.DB.TenantUserInfo.
		UpdateOne(tu).
		SetNotEmptySort(in.Sort).
		SetNotEmptyExtra(in.Extra).
		Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.UpdateSuccess}, nil
}

func (l *Impl) GetTenantUserInfoList(ctx context.Context, in *TenantUserInfoListReq) (*TenantUserInfoListResp, error) {
	var predicates []predicate.TenantUserInfo

	result, err := l.DB.TenantUserInfo.Query().Where(predicates...).Page(ctx, in.Page, in.PageSize)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	resp := &TenantUserInfoListResp{}
	resp.Total = result.PageDetails.Total

	for _, v := range result.List {
		resp.Data = append(resp.Data, &TenantUserInfoInfo{
			Id:        v.ID,
			CreatedAt: v.CreatedAt.UnixMilli(),
			UpdatedAt: v.UpdatedAt.UnixMilli(),
			Sort:      v.Sort,
			TenantId:  v.TenantID,
			UserId:    v.UserID,
			Extra:     v.Extra,
		})
	}

	return resp, nil
}

func (l *Impl) GetTenantUserInfoById(ctx context.Context, in *IDReq) (*TenantUserInfoInfo, error) {
	result, err := l.DB.TenantUserInfo.Get(ctx, in.Id)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &TenantUserInfoInfo{
		Id:        result.ID,
		CreatedAt: result.CreatedAt.UnixMilli(),
		UpdatedAt: result.UpdatedAt.UnixMilli(),
		Sort:      result.Sort,
		TenantId:  result.TenantID,
		UserId:    result.UserID,
		Extra:     result.Extra,
	}, nil
}

func (l *Impl) DeleteTenantUserInfo(ctx context.Context, in *DeleteTenantUserInfo) (*BaseResp, error) {
	err := entx.WithTx(ctx, l.DB, func(tx *ent.Tx) error {
		err := tx.Tenant.UpdateOneID(in.TenantId).
			RemoveUserIDs(in.UserIds...).
			Exec(ctx)
		if err != nil {
			return err
		}

		_, err = tx.TenantUserInfo.Delete().
			Where(tenantuserinfo.IDIn(in.TenantId),
				tenantuserinfo.UserIDIn(in.UserIds...)).
			Exec(schema.SkipSoftDelete(ctx))
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	//删除归属关系
	err = mapper.NewUserBelongCompanyClient(l.gormDB).DeleteByTenantIdAndUserIds(ctx, in.TenantId, in.UserIds)
	if err != nil {
		return nil, err
	}

	return &BaseResp{Msg: i18n.DeleteSuccess}, nil
}

func (l *Impl) CreateToken(ctx context.Context, in *TokenInfo) (*BaseIDResp, error) {
	var err error
	var defaultTenantID string
	if in.TenantId == "" {
		defaultTenantID = in.TenantId
		//defaultTenantID, err = l.DB.User.GetX(ctx, in.Uid).
		//	QueryTenants().
		//	Where(tenant.StatusEQ(1)).FirstID(ctx)
		//if err != nil {
		//	return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
		//}
		//
		//err = l.DB.User.UpdateOneID(in.Uid).SetDefaultTenantID(defaultTenantID).Exec(ctx)
		//if err != nil {
		//	return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
		//}
	} else {
		// 检查用户是否存在
		exist, err := l.DB.User.Query().Where(user.IDEQ(in.Uid)).Exist(ctx)
		if err != nil {
			return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
		}
		if exist {
			defaultTenantID = in.TenantId
			err = l.DB.User.UpdateOneID(in.Uid).SetDefaultTenantID(in.TenantId).Exec(ctx)
			if err != nil {
				return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
			}
		}
	}

	result, err := l.DB.Token.Create().
		SetStatus(in.Status).
		SetTenantID(defaultTenantID).
		SetUID(in.Uid).
		SetToken(in.Token).
		SetSource(in.Source).
		SetExpiredAt(time.Unix(in.ExpiredAt, 0)).
		Save(schema.SkipTenantInject(ctx))

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseIDResp{Id: result.ID, Msg: i18n.CreateSuccess}, nil
}

func (l *Impl) UpdateToken(ctx context.Context, in *TokenInfo) (*BaseResp, error) {
	err := l.DB.Token.UpdateOneID(in.Id).
		SetStatus(in.Status).
		SetNotEmptyTenantID(in.TenantId).
		SetNotEmptyUID(in.Uid).
		SetNotEmptyToken(in.Token).
		SetNotEmptySource(in.Source).
		SetExpiredAt(time.UnixMilli(in.ExpiredAt)).
		Exec(schema.SkipTenantInject(ctx))

	if err != nil {
		return nil, err
	}

	return &BaseResp{Msg: i18n.UpdateSuccess}, nil
}

func (l *Impl) GetTokenList(ctx context.Context, in *TokenListReq) (*TokenListResp, error) {
	var predicates []predicate.Token
	if in.Token != "" {
		predicates = append(predicates, token.TokenContains(in.Token))
	}
	if in.Source != "" {
		predicates = append(predicates, token.SourceContains(in.Source))
	}
	if in.Search != "" {
		predicates = append(predicates,
			token.Or(token.TokenContains(in.Search), token.SourceContains(in.Search)))
	}
	result, err := l.DB.Token.Query().Where(predicates...).Page(ctx, in.Page, in.PageSize)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	resp := &TokenListResp{}
	resp.Total = result.PageDetails.Total

	for _, v := range result.List {
		resp.Data = append(resp.Data, &TokenInfo{
			Id:        v.ID,
			CreatedAt: v.CreatedAt.UnixMilli(),
			UpdatedAt: v.UpdatedAt.UnixMilli(),
			Status:    v.Status,
			TenantId:  v.TenantID,
			Uid:       v.UID,
			Token:     v.Token,
			Source:    v.Source,
			ExpiredAt: v.ExpiredAt.UnixMilli(),
		})
	}

	return resp, nil
}

func (l *Impl) GetTokenById(ctx context.Context, in *IDReq) (*TokenInfo, error) {
	token, err := l.DB.Token.Get(ctx, in.Id)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &TokenInfo{
		Id:         token.ID,
		CreatedAt:  token.CreatedAt.UnixMilli(),
		UpdatedAt:  token.UpdatedAt.UnixMilli(),
		Status:     token.Status,
		TenantId:   token.TenantID,
		Uid:        token.UID,
		Token:      token.Token,
		Source:     token.Source,
		ExpiredAt:  token.ExpiredAt.UnixMilli(),
		DeviceKind: token.DeviceKind,
		IP:         token.IP,
	}, nil
}

func (l *Impl) DeleteToken(ctx context.Context, in *IDsReq) (*BaseResp, error) {
	_, err := l.DB.Token.Delete().Where(token.IDIn(in.Ids...)).Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.DeleteSuccess}, nil
}

func (l *Impl) CreateUser(ctx context.Context, in *UserInfo) (*BaseIDResp, error) {

	if err := l.checkUserMaxCount(ctx, in.DefaultTenantId, in.Kind); err != nil {
		return nil, err
	}

	if in.Username == "" {
		return nil, errorx.NewInvalidArgumentError("【用户名】已存在，请更换信息或通过绑定方式添加用户")
	}

	if in.DeviceNo != "" {
		exist := l.DB.User.Query().Where(user.DeviceNoEQ(in.DeviceNo)).Where(user.IDNEQ(in.Id)).ExistX(ctx)
		if exist {
			return nil, errorx.NewInvalidArgumentError("【设备号】已存在，请更换信息或通过绑定方式添加用户")
		}
	}

	if in.Username != "" {
		exist := l.DB.User.Query().Where(user.UsernameEQ(in.Username)).ExistX(ctx)
		if exist {
			return nil, errorx.NewInvalidArgumentError("【用户名】已存在，请更换信息或通过绑定方式添加用户")
		}
	}

	if in.Mobile != "" {
		exist := l.DB.User.Query().Where(user.MobileEQ(in.Mobile)).ExistX(ctx)
		if exist {
			return nil, errorx.NewInvalidArgumentError("【手机号】已存在，请更换信息或通过绑定方式添加用户")
		}
	}

	if in.Email != "" {
		exist := l.DB.User.Query().Where(user.EmailEQ(in.Email)).ExistX(ctx)
		if exist {
			return nil, errorx.NewInvalidArgumentError("【邮箱】已存在，请更换信息或通过绑定方式添加用户")
		}
	}

	// 普通用户的话，imei设置为唯一码
	if in.Kind == "common" {
		in.Imei = uuidx.NewUUID().String()
	}

	q := l.DB.User.Create().
		SetStatus(in.Status).
		SetUsername(in.Username).
		SetPassword(encrypt.BcryptEncrypt(in.Password)).
		SetNickname(in.Nickname).
		SetMobile(in.Mobile).
		SetEmail(in.Email).
		SetAvatarID(in.Avatar.Id).
		SetGender(enums.Gender(in.Gender)).
		SetPost(in.Post).
		SetKind(in.Kind).
		SetImei(in.Imei).
		SetDeviceNo(in.DeviceNo)

	if in.DefaultTenantId != "" {
		q.SetDefaultTenantID(in.DefaultTenantId).
			AddTenantIDs(in.DefaultTenantId)
	} else {
		// 从context中获取tenantId
		tenantId := utils.GetContextTenantID(ctx)
		q.SetDefaultTenantID(tenantId).
			AddTenantIDs(tenantId)
	}

	result, err := q.Save(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}
	// 更新查询器缓存
	err = l.cacheInitLoader.UpdateUserNickname(ctx, result.ID, in.Nickname)
	if err != nil {
		logc.Errorf(ctx, "更新用户昵称缓存失败: %v", err)
	}

	return &BaseIDResp{Id: result.ID, Msg: i18n.CreateSuccess}, nil
}

func (l *Impl) checkUserMaxCount(ctx context.Context, tenantId, kind string) error {
	tid := tenantId
	if tid == "" {
		tid = utils.GetContextTenantID(ctx)
	}

	// 查询租户下的用户数 判断是否超过限制
	tenantInfo, err := l.DB.Tenant.Query().
		Where(tenant.IDEQ(tid)).First(ctx)
	if err != nil {
		return err
	}

	maxCount := tenantInfo.MaxUserCount
	userCount, err := l.DB.Tenant.Query().
		Where(tenant.IDEQ(tid)).
		QueryUsers().Where(user.DeletedAtIsNil(), user.KindEQ(kind)).Count(ctx)
	if err != nil {
		return err
	}

	if int64(userCount) >= maxCount {
		return errors.New("用户数超过租户限制，如有需要，请联系售后人员")
	}
	return nil
}

func (l *Impl) UpdateUser(ctx context.Context, in *UserInfo) (*BaseResp, error) {
	// 检查手机号，和用户名
	if in.Username != "" {
		exist := l.DB.User.Query().Where(user.UsernameEQ(in.Username)).Where(user.IDNEQ(in.Id)).ExistX(ctx)
		if exist {
			return nil, errorx.NewInvalidArgumentError("【用户名】已存在，请更换信息或通过绑定方式添加用户")
		}
	}

	if in.Mobile != "" {
		exist := l.DB.User.Query().Where(user.MobileEQ(in.Mobile)).Where(user.IDNEQ(in.Id)).ExistX(ctx)
		if exist {
			return nil, errorx.NewInvalidArgumentError("【手机号】已存在，请更换信息或通过绑定方式添加用户")
		}
	}

	if in.Email != "" {
		exist := l.DB.User.Query().Where(user.EmailEQ(in.Email)).Where(user.IDNEQ(in.Id)).ExistX(ctx)
		if exist {
			return nil, errorx.NewInvalidArgumentError("【邮箱】已存在，请更换信息或通过绑定方式添加用户")
		}
	}

	if in.DeviceNo != "" {
		exist := l.DB.User.Query().Where(user.DeviceNoEQ(in.DeviceNo)).Where(user.IDNEQ(in.Id)).ExistX(ctx)
		if exist {
			return nil, errorx.NewInvalidArgumentError("【设备号】已存在，请更换信息或通过绑定方式添加用户")
		}
	}

	err := l.DB.User.UpdateOneID(in.Id).
		SetStatus(in.Status).
		SetNotEmptyUsername(in.Username).
		//SetNotEmptyPassword(encrypt.BcryptEncrypt(in.Password)).
		SetNotEmptyNickname(in.Nickname).
		SetNotEmptyMobile(in.Mobile).
		SetNotEmptyEmail(in.Email).
		SetGender(enums.Gender(in.Gender)).
		SetNotEmptyPost(in.Post).
		//SetNotEmptyAvatarID(in.Avatar.Id).
		SetAvatarID(in.Avatar.Id). //设置头像为空
		SetNotEmptyDefaultTenantID(in.DefaultTenantId).
		SetDeviceNo(in.DeviceNo).
		SetNotEmptyImei(in.Imei).
		Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}
	// 更细查询器
	err = l.cacheInitLoader.UpdateUserNickname(ctx, in.Id, in.Nickname)
	if err != nil {
		logc.Errorf(ctx, "更新用户昵称缓存失败: %v", err)
	}
	return &BaseResp{Msg: i18n.UpdateSuccess}, nil
}

func (l *Impl) GetUserList(ctx context.Context, in *UserListReq) (*UserListResp, error) {
	var predicates []predicate.User
	// 忽略管理员
	predicates = append(predicates, user.IsSuperuserEQ(false))
	if in.Username != "" {
		predicates = append(predicates, user.UsernameEQ(in.Username))
	}
	if in.Nickname != "" {
		predicates = append(predicates, user.NicknameContains(in.Nickname))
	}
	if in.Mobile != "" {
		predicates = append(predicates, user.MobileEQ(in.Mobile))
	}
	if in.Email != "" {
		predicates = append(predicates, user.EmailContains(in.Email))
	}
	if in.Search != "" {
		predicates = append(predicates,
			user.Or(user.UsernameEQ(in.Search),
				user.NicknameContains(in.Search),
				user.MobileEQ(in.Search),
				user.EmailContains(in.Search),
				user.DeviceNoContains(in.Search),
			))
	}
	if in.Email != "" {
		predicates = append(predicates, user.EmailContains(in.Email))
	}
	if len(in.Ids) > 0 {
		predicates = append(predicates, user.IDIn(in.Ids...))
	}

	if in.Kind != "" {
		predicates = append(predicates, user.KindEQ(in.Kind))
	}

	if in.Imei != "" {
		predicates = append(predicates, user.ImeiContains(in.Imei))
	}

	if in.DeviceNo != "" {
		predicates = append(predicates, user.DeviceNoContains(in.DeviceNo))
	}

	if in.Status != nil {
		predicates = append(predicates, user.StatusEQ(*in.Status))
	}

	// 只返回当前租户能看到的用户
	tenantId := utils.GetCurrentLoginUser(ctx).TenantId

	// 查询租户信息
	tenantObj, err := l.DB.Tenant.Query().Where(tenant.IDEQ(tenantId)).Only(ctx)

	// 如果是超级租户，可以按参数要求忽略租户条件过滤
	var isSuperTenant bool
	if err != nil {
		isSuperTenant = false
	} else {
		isSuperTenant = tenantObj.IsSuper
	}
	predicates = append(predicates, user.HasTenantsWith(tenant.DeletedAtIsNil()))

	// 如果不是超级租户，则只返回当前租户下的用户
	if !isSuperTenant || !in.IgnoreTenant {
		predicates = append(predicates, user.HasTenantsWith(tenant.IDEQ(tenantId)))
	}

	uq := l.DB.User.Query().
		Where(predicates...).
		WithAvatar().Order(ent.Desc(user.FieldCreatedAt)).
		WithTenants()
	var users []*ent.User
	var total uint64
	if in.NoPage {
		result, err2 :=
			uq.All(schema.SkipTenantInject(ctx))
		if err2 != nil {
			return nil, err2
		}
		total = uint64(len(result))
		users = result
	} else {
		result, err2 :=
			uq.WithOrganizationInfos().Page(schema.SkipTenantInject(ctx), in.Page, in.PageSize)
		if err2 != nil {
			return nil, err2
		}
		total = result.PageDetails.Total
		users = result.List
	}

	resp := &UserListResp{
		Total: total,
	}
	for _, v := range users {
		// 租户信息
		ts := make([]TenantInfo, 0, len(v.Edges.Tenants))
		for _, t := range v.Edges.Tenants {
			ts = append(ts, TenantInfo{
				Id:             t.ID,
				Name:           t.Name,
				IsSuper:        t.IsSuper,
				Status:         t.Status,
				ServiceStartAt: t.ServiceStartAt.UnixMilli(),
				ServiceEndAt:   t.ServiceEndAt.UnixMilli(),
				CreatedAt:      t.CreatedAt.UnixMilli(),
			})
		}

		// 部门信息
		var organizationIDs []string
		for _, o := range v.Edges.OrganizationInfos {
			organizationIDs = append(organizationIDs, o.OrganizationID)
		}

		resp.Data = append(resp.Data, &UserInfo{
			Id:              v.ID,
			CreatedAt:       v.CreatedAt.UnixMilli(),
			UpdatedAt:       v.UpdatedAt.UnixMilli(),
			Status:          v.Status,
			Username:        v.Username,
			Password:        v.Password,
			Nickname:        v.Nickname,
			Mobile:          v.Mobile,
			Email:           v.Email,
			Gender:          v.Gender.String(),
			Post:            v.Post,
			Avatar:          GetAvatar(v.Edges.Avatar),
			DefaultTenantId: v.DefaultTenantID,
			IsSuperuser:     v.IsSuperuser,
			Kind:            v.Kind,
			Imei:            v.Imei,
			DeviceNo:        v.DeviceNo,
			TenantInfos:     ts,
			OrganizationIDs: organizationIDs,
		})
	}

	return resp, nil
}

func (l *Impl) GetUserById(ctx context.Context, in *IDReq) (*UserInfo, error) {
	result, err := l.DB.User.Query().Where(user.IDEQ(in.Id)).WithAvatar().First(ctx)
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &UserInfo{
		Id:              result.ID,
		CreatedAt:       result.CreatedAt.UnixMilli(),
		UpdatedAt:       result.UpdatedAt.UnixMilli(),
		Status:          result.Status,
		Username:        result.Username,
		Password:        result.Password,
		Nickname:        result.Nickname,
		Mobile:          result.Mobile,
		Email:           result.Email,
		Avatar:          GetAvatar(result.Edges.Avatar),
		IsSuperuser:     result.IsSuperuser,
		DefaultTenantId: result.DefaultTenantID,
		Gender:          result.Gender.String(),
		Post:            result.Post,
	}, nil
}

func (l *Impl) DeleteUser(ctx context.Context, in *IDsReq) (*BaseResp, error) {
	err := entx.WithTx(ctx, l.DB, func(tx *ent.Tx) error {
		users, err := tx.User.Query().Where(user.IDIn(in.Ids...)).All(ctx)
		if err != nil {
			return err
		}

		timeStr := time.Now().Local().Format("20060102150405")
		for _, u := range users {
			err = tx.User.UpdateOne(u).
				SetMobile(u.Mobile + "_deleted_" + timeStr).
				SetEmail(u.Email + "_deleted_" + timeStr).
				SetImei(u.Imei + "_deleted_" + timeStr).
				SetUsername(u.Username + "_deleted_" + timeStr).Exec(ctx)
			if err != nil {
				return err
			}
		}

		_, err = tx.User.Delete().Where(user.IDIn(in.Ids...)).Exec(ctx)
		if err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	// 删除归属关系
	err = mapper.NewUserBelongCompanyClient(l.gormDB).DeleteByUserIds(ctx, in.Ids)
	if err != nil {
		return nil, err
	}

	return &BaseResp{Msg: i18n.DeleteSuccess}, nil
}

func (l *Impl) GetUserByIdentity(ctx context.Context, in *IdentityReq) (*UserInfo, error) {
	var result *ent.User
	var err error

	switch in.Identity {

	case "username":
		if in.Username == "" {
			return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), errors.New("invalid param"), in)
		}
		result, err = l.DB.User.Query().Where(user.UsernameEQ(in.Username)).WithAvatar().First(ctx)
		if err != nil {
			return nil, err
		}

	case "mobile":
		if in.Mobile == "" {
			return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), errors.New("invalid param"), in)
		}

		result, err = l.DB.User.Query().Where(user.MobileEQ(in.Mobile)).WithAvatar().First(ctx)
		if err != nil {
			return nil, err
		}

	case "email":
		if in.Email == "" {
			return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), errors.New("invalid param"), in)
		}

		result, err = l.DB.User.Query().Where(user.EmailEQ(in.Email)).WithAvatar().First(ctx)
		if err != nil {
			return nil, err
		}
	case "deviceNo":
		// 参数有效校验
		if in.DeviceNo == "" && in.Imei == "" {
			return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), errors.New("invalid param"), in)
		}

		// 组装查询条件
		predicates := make([]predicate.User, 0, 2)
		if in.DeviceNo != "" {
			predicates = append(predicates, user.DeviceNoEQ(in.DeviceNo))
		}
		if in.Imei != "" {
			predicates = append(predicates, user.ImeiEQ(in.Imei))
		}

		// imei OR deviceNo 作为查询条件
		result, err = l.DB.User.Query().Where(user.Or(predicates...)).WithAvatar().First(ctx)
		if err != nil {
			return nil, err
		}

	default:
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), errors.New("非法的认证信息"), in)
	}

	resp := &UserInfo{
		Id:              result.ID,
		CreatedAt:       result.CreatedAt.UnixMilli(),
		UpdatedAt:       result.UpdatedAt.UnixMilli(),
		Status:          result.Status,
		Username:        result.Username,
		Password:        result.Password,
		Nickname:        result.Nickname,
		Mobile:          result.Mobile,
		Email:           result.Email,
		Gender:          result.Gender.String(),
		Post:            result.Post,
		DefaultTenantId: result.DefaultTenantID,
		IsSuperuser:     result.IsSuperuser,
		Avatar:          GetAvatar(result.Edges.Avatar),
		Kind:            result.Kind,
		Imei:            result.Imei,
		DeviceNo:        result.DeviceNo,
	}

	return resp, nil
}

func (l *Impl) GetUserListByMobiles(ctx context.Context, mobiles []string) ([]*UserInfo, error) {
	if len(mobiles) == 0 {
		return make([]*UserInfo, 0), nil
	}

	sm4 := utils.NewSm4Tool()
	// 循环加密
	encryptedMobiles := make([]string, len(mobiles))
	for i, mobile := range mobiles {
		encrypted, err := sm4.Encrypt([]byte(mobile))
		if err != nil {
			return nil, err
		}
		encryptedMobiles[i] = string(encrypted)
	}

	users, err := l.DB.User.Query().Where(user.MobileIn(encryptedMobiles...)).WithAvatar().All(ctx)
	if err != nil {
		return nil, err
	}

	resp := make([]*UserInfo, 0, len(users))
	for _, user := range users {
		resp = append(resp, &UserInfo{
			Id:        user.ID,
			CreatedAt: user.CreatedAt.UnixMilli(),
			UpdatedAt: user.UpdatedAt.UnixMilli(),
			Status:    user.Status,
			Username:  user.Username,
			Password:  user.Password,
			Nickname:  user.Nickname,
			Mobile:    user.Mobile,
		})
	}

	return resp, nil
}

func (l *Impl) GetUserWithExtraInfoByIdentity(ctx context.Context, in *IdentityReq) (*UserWithExtraInfo, error) {
	var result *ent.User
	var err error

	switch in.Identity {

	case "username":
		result, err = l.DB.User.Query().Where(user.UsernameEQ(in.Username)).WithAvatar().First(ctx)
		if err != nil {
			return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
		}

	case "mobile":
		result, err = l.DB.User.Query().Where(user.MobileEQ(in.Mobile)).WithAvatar().First(ctx)
		if err != nil {
			return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
		}

	case "email":
		result, err = l.DB.User.Query().Where(user.EmailEQ(in.Email)).WithAvatar().First(ctx)
		if err != nil {
			return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
		}

	default:
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), errors.New("非法的认证信息"), in)
	}

	// 组装角色信息
	roleInfos := make([]*RoleInfo, 0, len(result.Edges.Roles))

	for _, v := range result.Edges.Roles {
		roleInfos = append(roleInfos, &RoleInfo{
			Id:            v.ID,
			CreatedAt:     v.CreatedAt.UnixMilli(),
			UpdatedAt:     v.UpdatedAt.UnixMilli(),
			Status:        v.Status,
			Sort:          v.Sort,
			TenantId:      v.TenantID,
			Name:          v.Name,
			Code:          v.Code,
			DefaultRouter: v.DefaultRouter,
			Remark:        v.Remark,
		})
	}

	// 组装租户信息
	tenantInfos := make([]*TenantInfo, 0, len(result.Edges.Tenants))

	for _, v := range result.Edges.Tenants {
		tenantInfos = append(tenantInfos, &TenantInfo{
			Id:        v.ID,
			Name:      v.Name,
			Key:       v.Key,
			IsSuper:   v.IsSuper,
			Uuid:      v.UUID.String(),
			CreatedAt: v.CreatedAt.UnixMilli(),
			UpdatedAt: v.UpdatedAt.UnixMilli(),
			Status:    v.Status,
		})
	}

	return &UserWithExtraInfo{
		User: &UserInfo{
			Id:              result.ID,
			CreatedAt:       result.CreatedAt.UnixMilli(),
			UpdatedAt:       result.UpdatedAt.UnixMilli(),
			Status:          result.Status,
			Username:        result.Username,
			Password:        result.Password,
			Nickname:        result.Nickname,
			Mobile:          result.Mobile,
			Email:           result.Email,
			Gender:          result.Gender.String(),
			Post:            result.Post,
			Avatar:          GetAvatar(result.Edges.Avatar),
			IsSuperuser:     result.IsSuperuser,
			DefaultTenantId: result.DefaultTenantID,
		},
		Roles:   roleInfos,
		Tenants: tenantInfos,
	}, nil
}

func (l *Impl) GetUserWithExtraInfoById(ctx context.Context, in *IDReq) (*UserWithExtraInfo, error) {
	result, err := l.DB.User.Query().Where(user.IDEQ(in.Id)).WithRoles().WithTenants().WithAvatar().Only(schema.SkipTenantInject(ctx))
	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	// 组装角色信息
	roleInfos := make([]*RoleInfo, 0, len(result.Edges.Roles))

	for _, v := range result.Edges.Roles {
		roleInfos = append(roleInfos, &RoleInfo{
			Id:            v.ID,
			CreatedAt:     v.CreatedAt.UnixMilli(),
			UpdatedAt:     v.UpdatedAt.UnixMilli(),
			Status:        v.Status,
			Sort:          v.Sort,
			TenantId:      v.TenantID,
			Name:          v.Name,
			Code:          v.Code,
			DefaultRouter: v.DefaultRouter,
			Remark:        v.Remark,
		})
	}

	// 组装租户信息
	tenantInfos := make([]*TenantInfo, 0, len(result.Edges.Tenants))

	for _, v := range result.Edges.Tenants {
		tenantInfos = append(tenantInfos, &TenantInfo{
			Id:        v.ID,
			Name:      v.Name,
			Key:       v.Key,
			IsSuper:   v.IsSuper,
			Uuid:      v.UUID.String(),
			CreatedAt: v.CreatedAt.UnixMilli(),
			UpdatedAt: v.UpdatedAt.UnixMilli(),
			Status:    v.Status,
		})
	}

	return &UserWithExtraInfo{
		User: &UserInfo{
			Id:              result.ID,
			CreatedAt:       result.CreatedAt.UnixMilli(),
			UpdatedAt:       result.UpdatedAt.UnixMilli(),
			Status:          result.Status,
			Username:        result.Username,
			Password:        result.Password,
			Nickname:        result.Nickname,
			Mobile:          result.Mobile,
			Email:           result.Email,
			Gender:          result.Gender.String(),
			Post:            result.Post,
			Avatar:          GetAvatar(result.Edges.Avatar),
			IsSuperuser:     result.IsSuperuser,
			DefaultTenantId: result.DefaultTenantID,
		},
		Roles:   roleInfos,
		Tenants: tenantInfos,
	}, nil
}

func (l *Impl) UpdateUsersPassword(ctx context.Context, in *UpdateUsersPasswordReq) (*BaseResp, error) {
	// 更新指定人员的密码
	err := l.DB.User.Update().Where(user.IDIn(in.UserIds...)).
		SetNotEmptyPassword(encrypt.BcryptEncrypt(in.NewPass)).
		Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.UpdateSuccess}, nil
}

func (l *Impl) UpdateUserDefaultTenant(ctx context.Context, in *UpdateUserDefaultTenantReq) (*BaseResp, error) {
	err := l.DB.User.UpdateOneID(in.UserId).
		SetDefaultTenantID(in.TenantId).
		Exec(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	return &BaseResp{Msg: i18n.UpdateSuccess}, nil
}

func (l *Impl) GetMenuInfoWithChildrenAndButtonsByUser(ctx context.Context, in *MenuListReq) (*MenuListResp, error) {

	tenantId := utils.GetContextTenantID(ctx)
	orgId := utils.GetContextOrganizationID(ctx)
	// 组装查询条件，获取用户菜单
	// 默认注入租户和用户ID条件
	var buttonPredicates []predicate.Button
	var menuPredicates []predicate.Menu
	buttonPredicates = append(buttonPredicates, button.HasRolesWith(role.TenantIDEQ(tenantId), role.StatusEQ(true), role.DeletedAtIsNil(), role.OrganizationIDEQ(orgId), role.HasUsersWith(user.IDEQ(in.UserId))))
	menuPredicates = append(menuPredicates, menu.HasRolesWith(role.TenantIDEQ(tenantId), role.StatusEQ(true), role.DeletedAtIsNil(), role.OrganizationIDEQ(orgId), role.HasUsersWith(user.IDEQ(in.UserId))))

	result, err := l.DB.Menu.Query().
		Where(menu.Or(menu.ParentIDIsNil(), menu.ParentIDEQ("")), menu.And(menuPredicates...)).
		//WithChildren().
		WithButtons(func(query *ent.ButtonQuery) {
			query.Where(buttonPredicates...)
		}).
		Order(menu.BySort(sql.OrderAsc())).
		All(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, in)
	}

	resp := &MenuListResp{}

	for _, v := range result {
		resp.Data = append(resp.Data, GetMenuInfoWithChildrenAndButtons(ctx, v, buttonPredicates, menuPredicates))
	}

	return resp, nil
}

func (l *Impl) GetPlatUserByPlatUserIDAndPlatCode(ctx context.Context, platUserID, platCode string) (*PlatUser, error) {
	result, err := mapper.NewPlatUserSyncClient(l.gormDB).QueryByPlatUserID(ctx, platUserID, platCode)
	if err != nil {
		return nil, err
	}
	return &PlatUser{
		PlatCode:   result.PlatCode,
		PlatUserID: result.PlatUserID,
		UserID:     result.UserID,
		Username:   result.Username,
	}, nil
}

func (l *Impl) GetUserNicknamesByIDs(ctx context.Context, ids []string) (map[string]string, error) {
	users, err := l.DB.User.Query().Select(user.FieldID, user.FieldNickname).Where(user.IDIn(ids...)).All(ctx)
	if err != nil {
		return nil, err
	}

	nicknames := make(map[string]string)
	for _, user := range users {
		nicknames[user.ID] = user.Nickname
	}

	return nicknames, nil
}

func GetMenuInfoWithChildrenAndButtons(ctx context.Context, m *ent.Menu, buttonPredicates []predicate.Button, menuPredicates []predicate.Menu) *MenuInfo {

	var children []*MenuInfo
	ch, _ := m.QueryChildren().WithButtons(func(query *ent.ButtonQuery) {
		query.Where(buttonPredicates...)
	}).Where(menuPredicates...).Order(menu.BySort(sql.OrderAsc())).All(ctx)

	for _, v := range ch {
		children = append(children, GetMenuInfoWithChildrenAndButtons(ctx, v, buttonPredicates, menuPredicates))
	}

	var buttons []*ButtonInfo
	for _, v := range m.Edges.Buttons {
		buttons = append(buttons, &ButtonInfo{
			Id:        v.ID,
			CreatedAt: v.CreatedAt.UnixMilli(),
			UpdatedAt: v.UpdatedAt.UnixMilli(),
			Sort:      v.Sort,
			Name:      v.Name,
			Code:      v.Code,
			MenuId:    v.MenuID,
		})
	}

	return &MenuInfo{
		Id:          m.ID,
		CreatedAt:   m.CreatedAt.UnixMilli(),
		UpdatedAt:   m.UpdatedAt.UnixMilli(),
		Sort:        m.Sort,
		Name:        m.Name,
		Title:       m.Title,
		Icon:        m.Icon,
		ParentId:    m.ParentID,
		MenuType:    m.MenuType,
		Url:         m.URL,
		Redirect:    m.Redirect,
		Component:   m.Component,
		IsActive:    m.IsActive,
		Hidden:      m.Hidden,
		HiddenInTab: m.HiddenInTab,
		Fixed:       m.Fixed,
		Remark:      m.Remark,
		Meta:        m.Meta,
		Buttons:     buttons,
		Children:    children,
		IsFullPage:  m.IsFullPage,
	}
}
func GetMenuInfoWithButtons(ctx context.Context, m *ent.Menu) *MenuInfo {

	var buttons []*ButtonInfo
	for _, v := range m.Edges.Buttons {
		buttons = append(buttons, &ButtonInfo{
			Id:        v.ID,
			CreatedAt: v.CreatedAt.UnixMilli(),
			UpdatedAt: v.UpdatedAt.UnixMilli(),
			Sort:      v.Sort,
			Name:      v.Name,
			Code:      v.Code,
			MenuId:    v.MenuID,
		})
	}

	return &MenuInfo{
		Id:          m.ID,
		CreatedAt:   m.CreatedAt.UnixMilli(),
		UpdatedAt:   m.UpdatedAt.UnixMilli(),
		Sort:        m.Sort,
		Name:        m.Name,
		Title:       m.Title,
		Icon:        m.Icon,
		ParentId:    m.ParentID,
		MenuType:    m.MenuType,
		Url:         m.URL,
		Redirect:    m.Redirect,
		Component:   m.Component,
		IsActive:    m.IsActive,
		Hidden:      m.Hidden,
		HiddenInTab: m.HiddenInTab,
		Fixed:       m.Fixed,
		Remark:      m.Remark,
		Meta:        m.Meta,
		Buttons:     buttons,
		IsFullPage:  m.IsFullPage,
	}
}

func GetAvatar(avatar *ent.File) *Avatar {
	if avatar == nil {
		return &Avatar{}
	}
	return &Avatar{
		Id:         avatar.ID,
		Name:       avatar.Name,
		OriginName: avatar.OriginName,
		Path:       avatar.Path,
		OpenStatus: uint32(avatar.OpenStatus),
	}
}

func (l *Impl) setUserCountToOrganizations(infos []*OrganizationInfo) error {
	if len(infos) == 0 {
		return nil
	}

	// 组装组织ID列表
	orgIDs := make([]string, len(infos))
	orgIndexMap := make(map[string]int, len(infos))
	for i, info := range infos {
		orgIDs[i] = info.Id
		orgIndexMap[info.Id] = i
	}

	// 批量查询用户数量
	type UserCount struct {
		OrganizationID string `gorm:"column:organization_id"`
		Count          int64  `gorm:"column:user_count"`
	}
	var counts []UserCount

	err := l.gormDB.
		Table("view_organization_user_count").
		Select("organization_id, user_count").
		Where("organization_id IN ? AND user_count > 0", orgIDs).
		Find(&counts).Error
	if err != nil {
		return fmt.Errorf("查询组织用户数量失败: %w", err)
	}

	// 更新用户数量
	for _, count := range counts {
		if count.Count == 0 {
			continue
		}
		if idx, ok := orgIndexMap[count.OrganizationID]; ok {
			infos[idx].UserCount = count.Count
		}
	}

	return nil
}

func (l *Impl) GetRolesByUserIDAndOrgID(ctx context.Context, userID, orgID string) ([]*RoleInfo, error) {
	// 查询该用户在当前组织下的所有角色
	// 先查询该组织下的所有角色，然后过滤出包含该用户的角色
	result, err := l.DB.Role.Query().
		Where(
			role.StatusEQ(true),
			role.DeletedAtIsNil(),
			role.OrganizationIDEQ(orgID),
		).
		WithUsers(func(query *ent.UserQuery) {
			query.Where(user.IDEQ(userID))
		}).
		Order(ent.Asc(role.FieldSort)).
		All(ctx)

	if err != nil {
		return nil, dberrorhandler.DefaultEntError(logx.WithContext(ctx), err, nil)
	}

	var roles []*RoleInfo
	for _, r := range result {
		// 检查该角色是否包含指定用户
		if len(r.Edges.Users) > 0 {
			roles = append(roles, &RoleInfo{
				Id:             r.ID,
				CreatedAt:      r.CreatedAt.UnixMilli(),
				UpdatedAt:      r.UpdatedAt.UnixMilli(),
				Status:         r.Status,
				Sort:           r.Sort,
				TenantId:       r.TenantID,
				Name:           r.Name,
				Code:           r.Code,
				UId:            r.UID.String(),
				DefaultRouter:  r.DefaultRouter,
				Remark:         r.Remark,
				ParentId:       r.ParentID,
				OrganizationId: r.OrganizationID,
			})
		}
	}

	return roles, nil
}
