package services

import (
	"context"
)

type SaasService interface {
	// API management
	CreateAPI(ctx context.Context, in *APIInfo) (*BaseIDResp, error)
	UpdateAPI(ctx context.Context, in *APIInfo) (*BaseResp, error)
	GetAPIList(ctx context.Context, in *APIListReq) (*APIListResp, error)
	GetAPIById(ctx context.Context, in *IDReq) (*APIInfo, error)
	DeleteAPI(ctx context.Context, in *IDsReq) (*BaseResp, error)
	GetMenuAuthority(ctx context.Context, in *IDReq) (*RoleMenuAuthorityResp, error)
	CreateOrUpdateMenuAuthority(ctx context.Context, in *RoleMenuAuthorityReq) (*BaseResp, error)
	CreateOrUpdateAPIAuthority(ctx context.Context, in *RoleAPIAuthorityReq) (*BaseResp, error)
	InitDatabase(ctx context.Context, in *Empty) (*BaseResp, error)
	UpdateMenus(ctx context.Context, in *UpdateMenusReq) (*BaseResp, error)
	UpdateAPIs(ctx context.Context, in *UpdateAPIsReq) (*BaseResp, error)
	// Button management
	CreateButton(ctx context.Context, in *ButtonInfo) (*BaseIDResp, error)
	UpdateButton(ctx context.Context, in *ButtonInfo) (*BaseResp, error)
	GetButtonList(ctx context.Context, in *ButtonListReq) (*ButtonListResp, error)
	GetButtonById(ctx context.Context, in *IDReq) (*ButtonInfo, error)
	DeleteButton(ctx context.Context, in *IDsReq) (*BaseResp, error)
	// File management
	ChangeFileOpenStatus(ctx context.Context, in *ChangeFileOpenStatusReq) (*FileListResp, error)
	CreateFile(ctx context.Context, in *FileInfo) (*BaseIDResp, error)
	UpdateFile(ctx context.Context, in *FileInfo) (*BaseResp, error)
	UpdateFileByUuid(ctx context.Context, in *FileInfo) (*BaseResp, error)
	GetFileList(ctx context.Context, in *FileListReq) (*FileListResp, error)
	GetFileById(ctx context.Context, in *IDReq) (*FileInfo, error)
	DeleteFile(ctx context.Context, in *IDsReq) (*BaseResp, error)
	GetFileByIds(ctx context.Context, in *IDsReq) (*FileInfos, error)
	// Group management
	CreateGroup(ctx context.Context, in *GroupInfo) (*BaseIDResp, error)
	UpdateGroup(ctx context.Context, in *GroupInfo) (*BaseResp, error)
	GetGroupList(ctx context.Context, in *GroupListReq) (*GroupListResp, error)
	GetGroupById(ctx context.Context, in *IDReq) (*GroupInfo, error)
	DeleteGroup(ctx context.Context, in *IDsReq) (*BaseResp, error)
	UpdateGroupUsers(ctx context.Context, in *UpdateGroupUsersReq) (*BaseResp, error)
	GetGroupUsers(ctx context.Context, in *GetGroupUsersReq) (*GroupWithUsersResp, error)
	// GroupType management
	CreateGroupType(ctx context.Context, in *GroupTypeInfo) (*BaseIDResp, error)
	UpdateGroupType(ctx context.Context, in *GroupTypeInfo) (*BaseResp, error)
	GetGroupTypeList(ctx context.Context, in *GroupTypeListReq) (*GroupTypeListResp, error)
	GetGroupTypeListTree(ctx context.Context, in *GroupTypeListTreeReq) (*GroupTypeListTreeResp, error)
	GetGroupTypeById(ctx context.Context, in *IDReq) (*GroupTypeInfo, error)
	DeleteGroupType(ctx context.Context, in *IDsReq) (*BaseResp, error)
	// Menu management
	CreateMenu(ctx context.Context, in *MenuInfo) (*BaseIDResp, error)
	UpdateMenu(ctx context.Context, in *MenuInfo) (*BaseResp, error)
	GetMenuList(ctx context.Context, in *MenuListReq) (*MenuListResp, error)
	GetTenantMenuList(ctx context.Context, in *MenuListReq) (*MenuListResp, error)
	GetMenuById(ctx context.Context, in *IDReq) (*MenuInfo, error)
	DeleteMenu(ctx context.Context, in *IDsReq) (*BaseResp, error)
	// Organization management
	CreateOrganization(ctx context.Context, in *OrganizationInfo) (*BaseIDResp, error)
	UpdateOrganization(ctx context.Context, in *OrganizationInfo) (*BaseResp, error)
	UpdateOrganizationUsers(ctx context.Context, in *UpdateOrganizationUsersReq) (*BaseResp, error)
	UpdateUserOrganizations(ctx context.Context, in *UpdateOrganizationUsersReq) (*BaseResp, error)

	GetOrganizationList(ctx context.Context, in *OrganizationListReq) (*OrganizationListResp, error)
	GetOrganizationTree(ctx context.Context) (*OrganizationListResp, error)
	GetOrganizationTreeByOrgIds(ctx context.Context, orgId string, organizationIds []string) (*OrganizationListResp, error)
	GetOrganizationUsers(ctx context.Context, in *GetOrganizationUsersReq) (*GetOrganizationUsersResp, error)
	GetOrganizationById(ctx context.Context, in *IDReq) (*OrganizationInfo, error)
	DeleteOrganization(ctx context.Context, in *IDsReq) (*BaseResp, error)
	GetOrganizationListByCodes(ctx context.Context, codes []string) ([]*OrganizationInfo, error)
	// OrganizationUserInfo management
	CreateOrganizationUserInfo(ctx context.Context, in *OrganizationUserInfoInfo) (*BaseIDResp, error)
	UpdateOrganizationUserInfo(ctx context.Context, in *UpdateOrganizationUserInfo) (*BaseResp, error)
	UpdateOrganizationUserInfos(ctx context.Context, in []*UpdateOrganizationUserInfo) (*BaseResp, error)
	GetOrganizationUserInfoList(ctx context.Context, in *OrganizationUserInfoListReq) (*OrganizationUserInfoUserInfoListResp, error)
	GetOrganizationUserInfoById(ctx context.Context, in *IDReq) (*OrganizationUserInfoInfo, error)
	DeleteOrganizationUserInfo(ctx context.Context, in *DeleteOrganizationUserInfo) (*BaseResp, error)
	GetOrganizationUserInfoListAll(ctx context.Context, in *OrganizationUserTreeReq) (*OrganizationUserInfoUserInfoListAllResp, error)
	GetUserOrganizationInfoListAll(ctx context.Context, in *GetUserOrganizationsInfoListReq) (*UserOrganizationInfoListAllResp, error)
	// Position management
	CreatePosition(ctx context.Context, in *PositionInfo) (*BaseIDResp, error)
	UpdatePosition(ctx context.Context, in *PositionInfo) (*BaseResp, error)
	GetPositionList(ctx context.Context, in *PositionListReq) (*PositionListResp, error)
	GetPositionById(ctx context.Context, in *IDReq) (*PositionInfo, error)
	DeletePosition(ctx context.Context, in *IDsReq) (*BaseResp, error)
	// Role management
	CreateRole(ctx context.Context, in *RoleInfo) (*BaseIDResp, error)
	UpdateRole(ctx context.Context, in *RoleInfo) (*BaseResp, error)
	GetRoleList(ctx context.Context, in *RoleListReq) (*RoleListResp, error)
	GetRoleById(ctx context.Context, in *IDReq) (*RoleInfo, error)
	GetRoleWithAPI(ctx context.Context, in *Empty) (*RoleWithAPIListResp, error)
	DeleteRole(ctx context.Context, in *IDsReq) (*BaseResp, error)
	UpdateRoleUsers(ctx context.Context, in *UpdateRoleUsersReq) (*BaseResp, error)
	GetRoleUsers(ctx context.Context, in *GetRoleUsersReq) (*RoleWithUsersResp, error)
	GetRolesByUserIDAndOrgID(ctx context.Context, userID, orgID string) ([]*RoleInfo, error)
	// Tenant management
	CreateTenant(ctx context.Context, in *TenantCreateInfo) (*BaseIDResp, error)
	UpdateTenant(ctx context.Context, in *TenantUpdateInfo) (*BaseResp, error)
	GetTenantList(ctx context.Context, in *TenantListReq) (*TenantListResp, error)
	GetTenantById(ctx context.Context, in *IDReq) (*TenantInfo, error)
	GetTenantByUserId(ctx context.Context, in *IDReq) (*TenantListResp, error)
	DeleteTenant(ctx context.Context, in *IDsReq) (*BaseResp, error)
	UpdateTenantUsers(ctx context.Context, in *UpdateTenantUsersReq) (*BaseResp, error)
	GetTenantUsers(ctx context.Context, in *GetTenantUsersReq) (*GetTenantUsersResp, error)
	// TenantUserInfo management
	CreateTenantUserInfo(ctx context.Context, in *TenantUserInfoInfo) (*BaseIDResp, error)
	UpdateTenantUserInfo(ctx context.Context, in *TenantUserInfoInfo) (*BaseResp, error)
	GetTenantUserInfoList(ctx context.Context, in *TenantUserInfoListReq) (*TenantUserInfoListResp, error)
	GetTenantUserInfoById(ctx context.Context, in *IDReq) (*TenantUserInfoInfo, error)
	DeleteTenantUserInfo(ctx context.Context, in *DeleteTenantUserInfo) (*BaseResp, error)
	// Token management
	CreateToken(ctx context.Context, in *TokenInfo) (*BaseIDResp, error)
	UpdateToken(ctx context.Context, in *TokenInfo) (*BaseResp, error)
	GetTokenList(ctx context.Context, in *TokenListReq) (*TokenListResp, error)
	GetTokenById(ctx context.Context, in *IDReq) (*TokenInfo, error)
	DeleteToken(ctx context.Context, in *IDsReq) (*BaseResp, error)
	// User management
	CreateUser(ctx context.Context, in *UserInfo) (*BaseIDResp, error)
	UpdateUser(ctx context.Context, in *UserInfo) (*BaseResp, error)
	GetUserList(ctx context.Context, in *UserListReq) (*UserListResp, error)
	GetUserById(ctx context.Context, in *IDReq) (*UserInfo, error)
	DeleteUser(ctx context.Context, in *IDsReq) (*BaseResp, error)
	GetUserByIdentity(ctx context.Context, in *IdentityReq) (*UserInfo, error)
	GetUserWithExtraInfoByIdentity(ctx context.Context, in *IdentityReq) (*UserWithExtraInfo, error)
	GetUserWithExtraInfoById(ctx context.Context, in *IDReq) (*UserWithExtraInfo, error)
	UpdateUsersPassword(ctx context.Context, in *UpdateUsersPasswordReq) (*BaseResp, error)
	UpdateUserDefaultTenant(ctx context.Context, in *UpdateUserDefaultTenantReq) (*BaseResp, error)
	GetUserListByMobiles(ctx context.Context, mobiles []string) ([]*UserInfo, error)
	//GetPlatUserByPlatUserIDAndPlatCode 查询平台用户
	GetPlatUserByPlatUserIDAndPlatCode(ctx context.Context, platUserID, platCode string) (*PlatUser, error)

	GetUserNicknamesByIDs(ctx context.Context, ids []string) (map[string]string, error)
}
