package errs

import (
	"context"
	"github.com/zeromicro/go-zero/rest/httpx"
	"net/http"
)

type Body struct {
	Code    int         `json:"code"`
	Message string      `json:"msg"`
	Data    interface{} `json:"data,omitempty"`
	TraceID interface{} `json:"traceId,omitempty"`
}

type ErrorData struct {
	Detail string `json:"detail"`
}

func CommonResponse(ctx context.Context, w http.ResponseWriter, resp interface{}, err error) {
	var body Body
	if err != nil {
		httpx.ErrorCtx(ctx, w, err)
	} else {
		body.Code = OK.Code
		body.Message = OK.Message
		body.Data = resp
		body.TraceID = ctx.Value("traceId")
		httpx.OkJsonCtx(ctx, w, body)
	}
}
