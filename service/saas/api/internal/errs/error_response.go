package errs

import (
	"context"
	"errors"
	"net/http"
)

type CodeError struct {
	Code           int    `json:"code"`
	HttpStatusCode int    `json:"httpStatusCode"`
	Message        string `json:"msg"`
}

// Error 实现error的接口  然后CodeError继承一下Error方法  CodeError就为error类型的返回值
func (e *CodeError) Error() string {
	return e.Message
}

// ErrorResponse DefaultErrHandler 返回给前端的数据
func (e *CodeError) ErrorResponse() CodeError {
	return CodeError{
		Code:    e.Code,
		Message: e.Message,
	}
}

// NewCodeError 用来自定义抛出的异常，支持调用传递
func NewCodeError(code int, msg string) error {
	return &CodeError{
		Code:    code,
		Message: msg,
	}
}

// New 提供new方法，任意地方传递参数返回CodeError类型的数据
func New(code, httpStatusCode int, msg string) *CodeError {
	return &CodeError{
		Code:           code,
		Message:        msg,
		HttpStatusCode: httpStatusCode,
	}
}

// NewDefaultError 默认异常状态码函数，只需传递错误信息即可，默认返回500
func NewDefaultError(msg string) *CodeError {
	return &CodeError{
		HttpStatusCode: http.StatusOK,
		Code:           ServerError.Code,
		Message:        msg,
	}
}

// Deprecated: 废弃的函数，建议使用NewDefaultError
func DefaultErrHandler(message string) error {
	return &CodeError{
		Code:    ServerError.Code,
		Message: message,
	}
}

// ErrHandler 自定义错误返回函数 错误函数主入口
// 出参 httpStatus 错误状态码, body 错误信息
func ErrHandler(ctx context.Context, err error) (httpStatus int, body any) {
	httpStatus = http.StatusOK
	tid := ctx.Value("traceId")
	switch err.(type) {
	// 如果错误类型为CodeError，就返回错误类型的结构体
	case *CodeError:
		var c *CodeError
		errors.As(err, &c)
		return c.HttpStatusCode, Body{
			Code:    c.Code,
			Message: c.Message,
			TraceID: tid,
		}
	default:
		// 系统错误，500 错误提示
		return http.StatusOK, Body{
			Code:    ServerError.Code,
			Message: err.Error(),
			TraceID: tid,
		}
	}
}
