package errs

import "net/http"

var (
	OK                = New(0, http.StatusOK, "操作成功")
	FileDownloadError = New(404, http.StatusInternalServerError, "文件下载失败")
	ParamError        = New(400, http.StatusBadRequest, "请检查填写信息是否正确且完整")
	ServerError       = New(500, http.StatusOK, "服务器内部错误")
	TokenError        = New(401, http.StatusUnauthorized, "Token已过期")
	PreSignedUrlError = New(404, http.StatusInternalServerError, "预签名url生成错误")
	NotFound          = New(404, http.StatusNotFound, "资源不存在")
)
