package grouptype

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type GetGroupTypeByIdLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetGroupTypeByIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetGroupTypeByIdLogic {
	return &GetGroupTypeByIdLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetGroupTypeByIdLogic) GetGroupTypeById(req *types.IDReq) (resp *types.GroupTypeInfoResp, err error) {
	data, err := l.svcCtx.SaasService.GetGroupTypeById(l.ctx, &services.IDReq{Id: req.Id})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	return &types.GroupTypeInfoResp{
		BaseDataInfo: types.BaseDataInfo{
			Code: 0,
			Msg:  l.svcCtx.Trans.Trans(l.ctx, i18n.Success),
		},
		Data: types.GroupTypeInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: data.Id,
			},
			Status:   data.Status,
			Sort:     data.Sort,
			TenantId: data.TenantId,
			Name:     data.Name,
			Code:     data.Code,
			Remark:   data.Remark,
		},
	}, nil
}
