package grouptype

import (
	"context"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetGroupTypeListTreeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetGroupTypeListTreeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetGroupTypeListTreeLogic {
	return &GetGroupTypeListTreeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetGroupTypeListTreeLogic) GetGroupTypeListTree(req *types.GroupTypeTreeReq) (resp *types.GroupTypeTreeResp, err error) {
	data, err := l.svcCtx.SaasService.GetGroupTypeListTree(l.ctx, &services.GroupTypeListTreeReq{Search: req.Search})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	resp = &types.GroupTypeTreeResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	resp.Data = []types.GroupTypeInfoLite{}

	if len(data.Data) == 0 {
		return resp, nil
	}

	gtl := make([]types.GroupTypeInfoLite, len(data.Data))

	for i, v := range data.Data {
		groups := make([]types.GroupTypeInfoLite, len(v.Groups))
		for j, w := range v.Groups {
			users := make([]types.GroupTypeInfoLite, len(w.Users))
			for k, x := range w.Users {
				users[k] = types.GroupTypeInfoLite{
					Id:       x.Id,
					Name:     x.Nickname,
					Level:    uint32(3),
					Type:     uint32(2),
					Children: []types.GroupTypeInfoLite{},
				}
			}

			groups[j] = types.GroupTypeInfoLite{
				Id:       w.Id,
				Name:     w.Name,
				Level:    uint32(2),
				Type:     uint32(1),
				Children: users,
			}
		}

		gtl[i] = types.GroupTypeInfoLite{
			Id:       v.Id,
			Name:     v.Name,
			Level:    uint32(1),
			Type:     uint32(1),
			Children: groups,
		}
	}
	resp.Data = gtl

	return resp, nil
}
