package grouptype

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type UpdateGroupTypeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateGroupTypeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateGroupTypeLogic {
	return &UpdateGroupTypeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateGroupTypeLogic) UpdateGroupType(req *types.GroupTypeInfo) (resp *types.BaseDataInfo, err error) {
	data, err := l.svcCtx.SaasService.UpdateGroupType(l.ctx,
		&services.GroupTypeInfo{
			Id:       req.Id,
			Status:   req.Status,
			Sort:     req.Sort,
			TenantId: req.TenantId,
			Name:     req.Name,
			Code:     req.Code,
			Remark:   req.Remark,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, data.Msg), Data: types.EmptyData{}}, nil
}
