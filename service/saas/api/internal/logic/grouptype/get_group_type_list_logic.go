package grouptype

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type GetGroupTypeListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetGroupTypeListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetGroupTypeListLogic {
	return &GetGroupTypeListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetGroupTypeListLogic) GetGroupTypeList(req *types.GroupTypeListReq) (resp *types.GroupTypeListResp, err error) {
	data, err := l.svcCtx.SaasService.GetGroupTypeList(l.ctx,
		&services.GroupTypeListReq{
			Page:     req.Page,
			PageSize: req.PageSize,
			Name:     req.Name,
			Code:     req.Code,
			Remark:   req.Remark,
			Search:   req.Search,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	resp = &types.GroupTypeListResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	resp.Data.Total = data.Total
	resp.Data.Data = make([]types.GroupTypeInfo, 0)

	for _, v := range data.Data {
		resp.Data.Data = append(resp.Data.Data,
			types.GroupTypeInfo{
				BaseIDInfo: types.BaseIDInfo{
					Id: v.Id,
				},
				Status:   v.Status,
				Sort:     v.Sort,
				TenantId: v.TenantId,
				Name:     v.Name,
				Code:     v.Code,
				Remark:   v.Remark,
			})
	}
	return resp, nil
}
