package authority

import (
	"context"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetApiAuthorityLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetApiAuthorityLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetApiAuthorityLogic {
	return &GetApiAuthorityLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetApiAuthorityLogic) GetApiAuthority(req *types.IDReq) (resp *types.ApiAuthorityListResp, err error) {
	//roleData, err := l.svcCtx.SaasService.GetRoleById(l.ctx, &services.IDReq{Id: req.Id})
	//if err != nil {
	//	return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	//}

	data, err := l.svcCtx.SaasService.GetAPIList(l.ctx, &services.APIListReq{RoleId: req.Id, PageSize: 10000})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	resp = &types.ApiAuthorityListResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	resp.Data.Total = uint64(data.Total)

	if resp.Data.Total == 0 {
		resp.Data.Data = make([]types.ApiAuthorityInfo, 0)
	} else {
		for _, v := range data.Data {
			resp.Data.Data = append(resp.Data.Data, types.ApiAuthorityInfo{
				Id:     v.Id,
				Path:   v.Path,
				Method: v.Method,
			})
		}
	}

	//data := l.svcCtx.Casbin.GetFilteredPolicy(0, roleData.Id)
	//resp = &types.ApiAuthorityListResp{}
	//resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	//resp.Data.Total = uint64(len(data))
	//if resp.Data.Total == 0 {
	//	resp.Data.Data = make([]types.ApiAuthorityInfo, 0)
	//} else {
	//	for _, v := range data {
	//		resp.Data.Data = append(resp.Data.Data, types.ApiAuthorityInfo{
	//			Id:     v[0],
	//			Path:   v[1],
	//			Method: v[2],
	//		})
	//	}
	//}

	return resp, nil
}
