package authority

import (
	"context"
	"github.com/suyuan32/simple-admin-common/enum/errorcode"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateOrUpdateApiAuthorityLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateOrUpdateApiAuthorityLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateOrUpdateApiAuthorityLogic {
	return &CreateOrUpdateApiAuthorityLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateOrUpdateApiAuthorityLogic) CreateOrUpdateApiAuthority(req *types.CreateOrUpdateApiAuthorityReq) (resp *types.BaseDataInfo, err error) {
	data, err := l.svcCtx.SaasService.GetRoleById(l.ctx, &services.IDReq{Id: req.RoleId})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	// 向数据库更新API权限绑定信息
	var apiIds []string
	for _, v := range req.Data {
		apiIds = append(apiIds, v.Id)
	}

	_, err = l.svcCtx.SaasService.CreateOrUpdateAPIAuthority(l.ctx, &services.RoleAPIAuthorityReq{
		RoleId: req.RoleId,
		ApiIds: apiIds,
	})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	// clear old policies
	var oldPolicies [][]string
	oldPolicies = l.svcCtx.Casbin.GetFilteredPolicy(0, data.Id)
	if len(oldPolicies) != 0 {
		removeResult, err := l.svcCtx.Casbin.RemoveFilteredPolicy(0, data.Id)
		if err != nil {
			return &types.BaseDataInfo{
				Code: errorcode.Internal,
				Msg:  err.Error(),
			}, nil
		}
		if !removeResult {
			return &types.BaseDataInfo{
				Code: errorcode.Internal,
				Msg:  l.svcCtx.Trans.Trans(l.ctx, "casbin.removeFailed"),
			}, nil
		}
	}
	// add new policies
	var policies [][]string
	for _, v := range req.Data {
		policies = append(policies, []string{data.Id, v.Path, v.Method})
	}

	addResult, err := l.svcCtx.Casbin.AddPolicies(policies)
	if err != nil {
		return &types.BaseDataInfo{
			Code: errorcode.Internal,
			Msg:  l.svcCtx.Trans.Trans(l.ctx, "casbin.addFailed"),
		}, nil
	}
	if addResult {
		return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, i18n.UpdateSuccess)}, nil
	} else {
		return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, i18n.UpdateFailed)}, nil
	}
}
