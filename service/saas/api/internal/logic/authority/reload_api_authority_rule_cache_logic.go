package authority

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/enum/errorcode"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type ReloadApiAuthorityRuleCacheLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewReloadApiAuthorityRuleCacheLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReloadApiAuthorityRuleCacheLogic {
	return &ReloadApiAuthorityRuleCacheLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ReloadApiAuthorityRuleCacheLogic) ReloadApiAuthorityRuleCache() (resp *types.BaseDataInfo, err error) {

	// get all roles with api
	data, err := l.svcCtx.SaasService.GetRoleWithAPI(l.ctx, &services.Empty{})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	// clear old policies
	l.svcCtx.Casbin.ClearPolicy()
	if err != nil {
		return &types.BaseDataInfo{
			Code: errorcode.Internal,
			Msg:  l.svcCtx.Trans.Trans(l.ctx, "casbin.clearFailed"),
		}, nil
	}

	// add new policies
	var policies [][]string
	for _, v := range data.Data {
		for _, a := range v.Apis {
			policies = append(policies, []string{v.Id, a.Path, a.Method})
		}

	}

	addResult, err := l.svcCtx.Casbin.AddPolicies(policies)
	if err != nil {
		return &types.BaseDataInfo{
			Code: errorcode.Internal,
			Msg:  l.svcCtx.Trans.Trans(l.ctx, "casbin.addFailed"),
		}, nil
	}
	if addResult {
		return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, i18n.UpdateSuccess)}, nil
	} else {
		return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, i18n.UpdateFailed)}, nil
	}
}
