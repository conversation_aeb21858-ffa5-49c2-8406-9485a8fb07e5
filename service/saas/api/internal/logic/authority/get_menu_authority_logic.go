package authority

import (
	"context"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetMenuAuthorityLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetMenuAuthorityLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMenuAuthorityLogic {
	return &GetMenuAuthorityLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetMenuAuthorityLogic) GetMenuAuthority(req *types.IDReq) (resp *types.MenuAuthorityInfoResp, err error) {
	data, err := l.svcCtx.SaasService.GetMenuAuthority(l.ctx, &services.IDReq{
		Id: req.Id,
	})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	var menus, buttons []string

	if data.MenuIds == nil {
		menus = make([]string, 0)
	} else {
		menus = data.MenuIds
	}
	if data.ButtonIds == nil {
		buttons = make([]string, 0)
	} else {
		buttons = data.ButtonIds
	}

	resp = &types.MenuAuthorityInfoResp{
		BaseDataInfo: types.BaseDataInfo{
			Msg: l.svcCtx.Trans.Trans(l.ctx, i18n.Success),
		},
		Data: types.MenuAuthorityInfoReq{
			RoleId:    req.Id,
			MenuIds:   menus,
			ButtonIds: buttons,
		},
	}

	return resp, nil
}
