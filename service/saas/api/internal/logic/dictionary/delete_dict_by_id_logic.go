package dictionary

import (
	"context"
	"phoenix/service/saas/model/gorm/mapper"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteDictByIdLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteDictByIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteDictByIdLogic {
	return &DeleteDictByIdLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteDictByIdLogic) DeleteDictById(req *types.DeleteDictByIdReq) (resp *types.BaseDataInfo, err error) {
	dictionaryClient := mapper.NewDictionaryClient(l.svcCtx.GormDB)
	err = dictionaryClient.DeleteDictById(l.ctx, req.ID)
	if err != nil {
		return nil, err
	}
	return &types.BaseDataInfo{Msg: "删除成功"}, nil
}
