package dictionary

import (
	"context"
	"encoding/json"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/gorm/mapper"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
)

type DictListByDictTypeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDictListByDictTypeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DictListByDictTypeLogic {
	return &DictListByDictTypeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DictListByDictTypeLogic) DictListByDictType(req *types.DictListReq) (resp *types.DictListResp, err error) {
	// 参数封装
	dictionaryClient := mapper.NewDictionaryClient(l.svcCtx.GormDB)
	pageInfo := mapper.PageInfo{
		Page:     req.Page,
		PageSize: req.PageSize,
		NoPage:   req.NoPage,
		DictType: req.DictType,
	}
	// 获取数据
	dictList, total, err := dictionaryClient.GetDictByDictType(l.ctx, pageInfo)
	if err != nil {
		return nil, err
	}
	// 数据封装
	dictListInfo := make([]types.DictionaryInfo, len(dictList))
	for k, v := range dictList {
		dictListInfo[k] = l.convertDictionaryToInfo(v)
	}

	return &types.DictListResp{
		BaseDataInfo: types.BaseDataInfo{},
		Data: types.DictList{
			BaseListInfo: types.BaseListInfo{
				Total: uint64(total),
			},
			Data: dictListInfo,
		},
	}, nil
}

// 数据封装
func (l *DictListByDictTypeLogic) convertDictionaryToInfo(dict mapper.Dictionary) types.DictionaryInfo {
	var extra map[string]interface{}
	err := json.Unmarshal([]byte(dict.Extra), &extra)
	if err != nil {
		logc.Error(l.ctx, "convertDictionaryToInfo", "error", err)
		return types.DictionaryInfo{}
	}
	return types.DictionaryInfo{
		ID:          dict.ID,
		DictType:    dict.DictType,
		DictCode:    dict.DictCode,
		DictOrder:   dict.DictOrder,
		DictName:    dict.DictName,
		Description: dict.Description,
		Extra:       extra,
		IsOpen:      dict.IsOpen,
		CreatedAt:   dict.CreatedAt.UnixMilli(),
		UpdatedAt:   dict.UpdatedAt.UnixMilli(),
	}
}
