package dictionary

import (
	"context"
	"encoding/json"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateDictByIdLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateDictByIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDictByIdLogic {
	return &UpdateDictByIdLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateDictByIdLogic) UpdateDictById(req *types.DictionaryInfo) (resp *types.BaseDataInfo, err error) {
	dictionaryClient := mapper.NewDictionaryClient(l.svcCtx.GormDB)
	dict := l.convertDictionaryToInfo(req)
	err = dictionaryClient.UpdateDictById(l.ctx, dict)
	if err != nil {
		return nil, err
	}
	return &types.BaseDataInfo{Msg: "更新成功"}, nil
}

// 数据封装
func (l *UpdateDictByIdLogic) convertDictionaryToInfo(dict *types.DictionaryInfo) mapper.Dictionary {
	extra, err := json.Marshal(dict.Extra)
	if err != nil {
		logc.Error(l.ctx, "convertDictionaryToInfo", "error", err)
		return mapper.Dictionary{}
	}

	return mapper.Dictionary{
		ID:          dict.ID,
		DictType:    dict.DictType,
		DictCode:    dict.DictCode,
		DictOrder:   dict.DictOrder,
		DictName:    dict.DictName,
		Description: dict.Description,
		IsOpen:      dict.IsOpen,
		Extra:       string(extra),
		UpdatedBy:   utils.GetCurrentLoginUser(l.ctx).UserId,
	}
}
