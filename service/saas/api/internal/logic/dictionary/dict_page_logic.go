package dictionary

import (
	"context"
	"encoding/json"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/gorm/mapper"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
)

type DictPageLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDictPageLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DictPageLogic {
	return &DictPageLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DictPageLogic) DictPage(req *types.DictPageReq) (resp *types.DictPageResp, err error) {
	dictList, total, err := mapper.NewDictionaryClient(l.svcCtx.GormDB).Page(l.ctx, mapper.PageInfo{
		Page:     req.Page,
		PageSize: req.PageSize,
		NoPage:   req.NoPage,
		Search:   req.Search,
	})
	if err != nil {
		return nil, err
	}
	dictListInfo := make([]types.DictionaryInfo, len(dictList))
	for k, v := range dictList {
		dictListInfo[k] = l.convertDictionaryToInfo(v)
	}

	return &types.DictPageResp{
		BaseDataInfo: types.BaseDataInfo{},
		Data: types.DictPageData{
			BaseListInfo: types.BaseListInfo{
				Total: uint64(total),
			},
			Data: dictListInfo,
		},
	}, nil
}

func (l *DictPageLogic) convertDictionaryToInfo(dict mapper.Dictionary) types.DictionaryInfo {
	var extra map[string]interface{}
	err := json.Unmarshal([]byte(dict.Extra), &extra)
	if err != nil {
		logc.Error(l.ctx, "convertDictionaryToInfo", "error", err)
		return types.DictionaryInfo{}
	}
	return types.DictionaryInfo{
		ID:          dict.ID,
		DictType:    dict.DictType,
		DictCode:    dict.DictCode,
		DictOrder:   dict.DictOrder,
		DictName:    dict.DictName,
		Description: dict.Description,
		IsOpen:      dict.IsOpen,
		Extra:       extra,
		CreatedAt:   dict.CreatedAt.UnixMilli(),
		UpdatedAt:   dict.UpdatedAt.UnixMilli(),
	}
}
