package dictionary

import (
	"context"
	"encoding/json"
	"errors"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
)

type CreateDictByInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateDictByInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateDictByInfoLogic {
	return &CreateDictByInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateDictByInfoLogic) CreateDictByInfo(req *types.DictionaryInfo) (resp *types.CreateDictResp, err error) {
	dictionaryClient := mapper.NewDictionaryClient(l.svcCtx.GormDB)

	var dict mapper.Dictionary
	// 生成id
	req.ID = l.svcCtx.IDGenerator.GenerateIDString()
	// 保存文件信息到字典列表
	dict = l.convertDictionaryToInfo(req)
	err = dictionaryClient.Create(l.ctx, dict)
	if err != nil {
		return nil, errors.New("信息写入字典列表失败：" + err.Error())
	}
	return &types.CreateDictResp{
		BaseDataInfo: types.BaseDataInfo{},
		Data: types.CreateDictInfo{
			ID: req.ID,
		},
	}, nil
}

// 数据封装
func (l *CreateDictByInfoLogic) convertDictionaryToInfo(dict *types.DictionaryInfo) mapper.Dictionary {
	if dict.Extra == nil {
		dict.Extra = make(map[string]interface{})
	}

	extra, err := json.Marshal(dict.Extra)
	if err != nil {
		logc.Error(l.ctx, "convertDictionaryToInfo", "error", err)
		return mapper.Dictionary{}
	}

	userInfo := utils.GetCurrentLoginUser(l.ctx)
	return mapper.Dictionary{
		ID:          dict.ID,
		DictType:    dict.DictType,
		DictCode:    dict.DictCode,
		DictOrder:   dict.DictOrder,
		DictName:    dict.DictName,
		Extra:       string(extra),
		Description: dict.Description,
		IsOpen:      dict.IsOpen,
		CreatedBy:   userInfo.UserId,
		UpdatedBy:   userInfo.UserId,
		TenantID:    userInfo.TenantId,
	}
}
