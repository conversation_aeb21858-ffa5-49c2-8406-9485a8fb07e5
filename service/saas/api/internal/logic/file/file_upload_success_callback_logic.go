package file

import (
	"context"
	"errors"
	"path/filepath"
	"phoenix/service/saas/api/internal/services"
	"strings"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type FileUploadSuccessCallbackLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewFileUploadSuccessCallbackLogic(ctx context.Context, svcCtx *svc.ServiceContext) *FileUploadSuccessCallbackLogic {
	return &FileUploadSuccessCallbackLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *FileUploadSuccessCallbackLogic) FileUploadSuccessCallback(req *types.FileUploadSeccussCallBackReq) (resp *types.BaseDataInfo, err error) {
	filePath := req.Key
	fileName := filepath.Base(filePath)
	fileType := filepath.Ext(fileName)
	uuid := strings.TrimSuffix(fileName, fileType)
	// 根据uuid修改文件状态
	data, err := l.svcCtx.SaasService.UpdateFileByUuid(l.ctx,
		&services.FileInfo{
			Uuid:   uuid,
			Status: true,
		})
	if err != nil {
		return nil, errors.New("文件上传失败：" + err.Error())
	}
	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, data.Msg), Data: types.EmptyData{}}, nil
}
