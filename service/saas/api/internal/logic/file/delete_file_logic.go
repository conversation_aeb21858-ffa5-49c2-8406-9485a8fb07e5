package file

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type DeleteFileLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteFileLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteFileLogic {
	return &DeleteFileLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteFileLogic) DeleteFile(req *types.IDsReq) (resp *types.BaseDataInfo, err error) {
	result, err := l.svcCtx.SaasService.DeleteFile(l.ctx, &services.IDsReq{
		Ids: req.Ids,
	})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, result.Msg), Data: types.EmptyData{}}, nil
}
