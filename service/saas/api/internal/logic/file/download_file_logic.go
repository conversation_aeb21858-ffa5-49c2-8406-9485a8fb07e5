package file

import (
	"context"
	"errors"
	"os"
	"path"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/utils/file_crypto"
	"slices"
	"strings"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
)

type DownloadFileLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDownloadFileLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DownloadFileLogic {
	return &DownloadFileLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DownloadFileLogic) DownloadFile(req *types.IDPathReq) (fileBytes []byte, fileName string, err error) {
	data, err := l.svcCtx.SaasService.GetFileById(l.ctx, &services.IDReq{Id: req.Id})
	if err != nil {
		return nil, "", errorx.NewCodeErrorFromGrpcStatus(err)
	}
	if data == nil || data.Id == "" {
		return nil, "", errorx.NewCodeErrorFromGrpcStatus(errors.New("文件不存在"))
	}

	filepath := path.Join(l.svcCtx.Config.UploadConf.StorePath, data.Path)
	_, err = os.Stat(filepath)
	if err != nil {
		return nil, "", errorx.NewCodeErrorFromGrpcStatus(err)
	}

	file, err := os.ReadFile(filepath)
	if err != nil {
		return nil, "", errorx.NewCodeErrorFromGrpcStatus(err)
	}

	decrypt, err := l.fileDecrypt(file, data.Name)
	if err != nil {
		return nil, "", err
	}
	return decrypt, data.Name, err
}

func (l *DownloadFileLogic) fileDecrypt(file []byte, fileName string) ([]byte, error) {
	if slices.Contains(l.svcCtx.Config.UploadConf.FileEncryptSkipSuffix, strings.ToLower(path.Ext(fileName))) {
		return file, nil
	}
	return file_crypto.AesDecrypt(file, file_crypto.PwdKey)
}
