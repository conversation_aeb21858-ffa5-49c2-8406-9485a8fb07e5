package file

import (
	"context"
	"errors"
	"fmt"
	"phoenix/service/saas/api/internal/services"

	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GenerateDownloadPreSignedUrlLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGenerateDownloadPreSignedUrlLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GenerateDownloadPreSignedUrlLogic {
	return &GenerateDownloadPreSignedUrlLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GenerateDownloadPreSignedUrlLogic) GenerateDownloadPreSignedUrl(req *types.GenerateFileDownloadPreSignedUrlReq) (resp *types.GenerateFileDownloadPreSignedUrlResp, err error) {
	data, err := l.svcCtx.SaasService.GetFileById(l.ctx, &services.IDReq{Id: req.Id})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	if data == nil || data.Id == "" {
		return nil, errorx.NewCodeErrorFromGrpcStatus(errors.New("文件不存在"))
	}

	// 生成下载或者预览文件预签名 URL
	preSignedURL, err := l.generatePreSignedURL(data.Path, data.Name, req.LoadType, req.ExpirationTimeSecond)
	if err != nil {
		return nil, err
	}

	return &types.GenerateFileDownloadPreSignedUrlResp{
		BaseDataInfo: types.BaseDataInfo{},
		Data: types.GenerateFileDownloadPreSignedUrlInfo{
			FileDownloadPreSignedUrl: preSignedURL,
		},
	}, nil
}

// 判断是下载文件还是预览文件，生成对应url
func (l *GenerateDownloadPreSignedUrlLogic) generatePreSignedURL(path string, name string, loadType int, expirationTime int64) (preSignedURL string, err error) {
	// expirationTime 0是参数默认值，表示用户没传过期时间，使用系统默认值
	if expirationTime == 0 {
		expirationTime = l.svcCtx.Config.MinioConf.PreSignedExpiry
	}
	// nodeType 0表示下载文件，1表示预览文件
	if loadType == 0 {
		preSignedURL, err = l.svcCtx.MinioClient.GenerateDownloadPreSignedURLex(l.ctx, path, name, expirationTime)
		if err != nil {
			l.Logger.Error("failed to generate pre-signed URL", logx.Field("filePath", path), logx.Field("error", err))
			return "", fmt.Errorf("failed to generate download URL")
		}
		return preSignedURL, nil
	}
	preSignedURL, err = l.svcCtx.MinioClient.GenerateViewPreSignedGetURLex(l.ctx, path, name, expirationTime)
	if err != nil {
		l.Logger.Error("failed to generate pre-signed URL", logx.Field("filePath", path), logx.Field("error", err))
		return "", fmt.Errorf("failed to generate download URL")
	}
	return preSignedURL, nil
}
