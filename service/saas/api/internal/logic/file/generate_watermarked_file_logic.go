package file

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"path"
	"path/filepath"
	"strings"
	"time"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/utils/uuidx"
	"phoenix/service/saas/utils/watermark"

	"github.com/suyuan32/knife/core/date/format"
	"github.com/zeromicro/go-zero/core/logx"
)

type GenerateWatermarkedFileLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGenerateWatermarkedFileLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GenerateWatermarkedFileLogic {
	return &GenerateWatermarkedFileLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GenerateWatermarkedFileLogic) GenerateWatermarkedFile(req *types.GenerateWatermarkedFileReq) (resp *types.GenerateWatermarkedFileResp, err error) {
	// 1. 获取原始文件信息
	originalFileInfo, err := l.svcCtx.DB.File.Get(l.ctx, req.Id)
	if err != nil {
		l.Logger.Errorf("failed to get original file info from db for id: %s, error: %v", req.Id, err)
		return nil, fmt.Errorf("original file not found: %w", err)
	}

	// 2. 确保是PDF文件
	if ".pdf" != strings.ToLower(path.Ext(originalFileInfo.Name)) {
		return nil, fmt.Errorf("only PDF files are supported for watermarking, but got %s", originalFileInfo.Name)
	}

	// 3. 下载原始文件并读入内存
	originalObject, err := l.svcCtx.MinioClient.GetObject(l.ctx, originalFileInfo.Path)
	if err != nil {
		l.Logger.Errorf("failed to download original file from minio for path: %s, error: %v", originalFileInfo.Path, err)
		return nil, fmt.Errorf("failed to download original file: %w", err)
	}
	defer originalObject.Close()

	originalData, err := io.ReadAll(originalObject)
	if err != nil {
		l.Logger.Errorf("failed to read original file object for id: %s, error: %v", req.Id, err)
		return nil, fmt.Errorf("failed to read original file object: %w", err)
	}
	originalFileSeeker := bytes.NewReader(originalData)

	// 解析颜色参数
	var colorR, colorG, colorB float64 = 128, 128, 128 // 默认灰色
	if req.Color != "" {
		_, err := fmt.Sscanf(req.Color, "%f %f %f", &colorR, &colorG, &colorB)
		if err != nil {
			l.Logger.Errorf("failed to parse color string: %s, using default gray color", req.Color)
		}
	}

	opts := watermark.WatermarkOptions{
		WatermarkTxt: req.WatermarkText,
		FontSize:     req.FontSize,
		ColorR:       colorR,
		ColorG:       colorG,
		ColorB:       colorB,
		Opacity:      req.Opacity,
		Rotation:     req.Rotation,
		XOffset:      req.XOffset,
		YOffset:      req.YOffset,
	}

	// 5. 添加水印
	watermarkedReader, err := watermark.AddWatermark(originalFileSeeker, opts)
	if err != nil {
		l.Logger.Errorf("failed to add watermark for file id: %s, error: %v", req.Id, err)
		return nil, fmt.Errorf("failed to add watermark: %w", err)
	}

	// 生成唯一的文件名
	fileUUID := uuidx.NewUUID().String()
	// 从文件名获取文件类型
	originalFileType := strings.TrimPrefix(filepath.Ext(originalFileInfo.Name), ".")
	// 使用UUID作为文件名存储文件
	storeFileName := fileUUID + "." + originalFileType
	timeString := time.Now().Format(format.DashYearToDay)

	upLogic := NewGenerateUploadPreSignedUrlLogic(l.ctx, l.svcCtx)
	fileType, error := upLogic.checkFile(originalFileType)
	if error != nil {
		return nil, error
	}

	// 生成对象名称（使用UUID作为文件名，保持原始扩展名）
	objectName := fmt.Sprintf("%s/%s/%s/%s", l.svcCtx.Config.Name, fileType, timeString, storeFileName)

	// 6. 将处理后的文件上传到MinIO
	buf := new(bytes.Buffer)
	size, err := io.Copy(buf, watermarkedReader)
	if err != nil {
		l.Logger.Errorf("failed to buffer watermarked file for id: %s, error: %v", req.Id, err)
		return nil, fmt.Errorf("failed to buffer watermarked file: %w", err)
	}
	_, err = l.svcCtx.MinioClient.PutObject(l.ctx, objectName, bytes.NewReader(buf.Bytes()), size, "application/pdf")
	if err != nil {
		l.Logger.Errorf("failed to upload watermarked file to minio for object: %s, error: %v", objectName, err)
		return nil, fmt.Errorf("failed to upload watermarked file: %w", err)
	}

	// 7. 保存文件信息
	newFileID, err := upLogic.saveFileInfoToDb(originalFileInfo.Name, fileUUID, fileType, objectName, size)

	// 8. 生成新文件的预览URL
	previewURL, err := l.svcCtx.MinioClient.GenerateViewPreSignedGetURL(l.ctx, objectName, originalFileInfo.Name)
	if err != nil {
		l.Logger.Errorf("failed to generate preview url for object: %s, error: %v", objectName, err)
		return nil, fmt.Errorf("failed to generate preview url: %w", err)
	}
	return &types.GenerateWatermarkedFileResp{
		Data: types.GenerateWatermarkedFileInfo{
			Id:         newFileID,
			PreviewUrl: previewURL,
		},
	}, nil
}
