package file

import (
	"context"
	"errors"
	"fmt"
	"mime"
	"path/filepath"
	"phoenix/service/saas/api/internal/services"
	filex3 "phoenix/service/saas/api/internal/utils/filex"
	"phoenix/service/saas/utils"
	"phoenix/service/saas/utils/uuidx"
	"strings"
	"time"

	"github.com/suyuan32/knife/core/date/format"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GenerateUploadPreSignedUrlLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGenerateUploadPreSignedUrlLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GenerateUploadPreSignedUrlLogic {
	return &GenerateUploadPreSignedUrlLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GenerateUploadPreSignedUrlLogic) GenerateUploadPreSignedUrl(req *types.GenerateFileUploadPreSignedUrlReq) (resp *types.GenerateFileUploadPreSignedUrlResp, err error) {
	// 生成唯一的文件名
	fileUUID := uuidx.NewUUID().String()
	// 从文件名获取文件类型
	originalFileType := strings.TrimPrefix(filepath.Ext(req.FileName), ".")
	// 使用UUID作为文件名存储文件
	storeFileName := fileUUID + "." + originalFileType
	// 文件检查
	fileType, err := l.checkFile(originalFileType)
	if err != nil {
		return nil, err
	}

	// 生成预签名url
	preSignedUrl, objectName, err := l.generatePreSignedUrl(storeFileName, fileType)
	if err != nil {
		return nil, err
	}

	// 预存储文件信息
	id, err := l.saveFileInfoToDb(req.FileName, fileUUID, fileType, objectName, req.FileSize)
	if err != nil {
		return nil, err
	}

	return &types.GenerateFileUploadPreSignedUrlResp{
		BaseDataInfo: types.BaseDataInfo{},
		Data: types.GenerateFileUploadPreSignedUrlInfo{
			Id:                     id,
			FileType:               originalFileType,
			FileUploadPreSignedUrl: preSignedUrl,
		},
	}, nil
}

func (l *GenerateUploadPreSignedUrlLogic) saveFileInfoToDb(fileName, fileUUID, fileType, objectName string, fileSize int64) (string, error) {
	// 预保存文件信息到数据库（状态为false）
	userId := utils.GetCurrentLoginUser(l.ctx).UserId
	fileInfo := &services.FileInfo{
		Status:     false,
		Name:       fileName,
		OriginName: fileName,
		Uuid:       fileUUID,
		FileType:   uint32(filex3.ConvertFileTypeToUint8(fileType)),
		Path:       objectName,
		UserId:     userId,
		Hash:       "",
		OpenStatus: 1,
		Size:       uint64(fileSize),
	}
	data, err := l.svcCtx.SaasService.CreateFile(l.ctx, fileInfo)
	if err != nil {
		l.Logger.Errorf("保存文件信息到数据库失败: %v", err)
		return "", err
	}
	return data.Id, nil
}

func (l *GenerateUploadPreSignedUrlLogic) checkFile(fileType string) (string, error) {
	// 检查文件后缀名
	if fileType == "" || len(fileType) == 0 {
		return "", errors.New("reject the file which does not have suffix")
	}
	mimeType := mime.TypeByExtension("." + fileType)
	parts := strings.Split(mimeType, "/")
	if len(parts) > 0 {
		mimeType = parts[0]
	}
	if mimeType != "image" && mimeType != "video" && mimeType != "audio" {
		mimeType = "other"
	}
	return mimeType, nil
}

func (l *GenerateUploadPreSignedUrlLogic) generatePreSignedUrl(storeFileName, fileType string) (string, string, error) {

	timeString := time.Now().Format(format.DashYearToDay)

	// 生成对象名称（使用UUID作为文件名，保持原始扩展名）
	objectName := fmt.Sprintf("%s/%s/%s/%s", l.svcCtx.Config.Name, fileType, timeString, storeFileName)
	// 生成预签名URL，设置过期时间
	preSignedURL, err := l.svcCtx.MinioClient.GeneratePreSignedPutURL(l.ctx, objectName)
	if err != nil {
		l.Logger.Errorf("生成预签名URL失败: %v, objectName: %s, expiry: %v", err, objectName, l.svcCtx.Config.MinioConf.PreSignedExpiry)
		return "", "", fmt.Errorf("生成预签名URL失败: %v", err)
	}
	return preSignedURL, objectName, nil
}
