package file

import (
	"context"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path"
	"path/filepath"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	filex3 "phoenix/service/saas/api/internal/utils/filex"
	"phoenix/service/saas/utils"
	"phoenix/service/saas/utils/file_crypto"
	"phoenix/service/saas/utils/uuidx"
	"slices"
	"strings"
	"time"

	"github.com/suyuan32/knife/core/date/format"
	filex2 "github.com/suyuan32/knife/core/io/filex"
	"github.com/zeromicro/go-zero/core/logc"

	"github.com/zeromicro/go-zero/core/logx"
)

type UploadLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
	r      *http.Request
}

func NewUploadLogic(r *http.Request, svcCtx *svc.ServiceContext) *UploadLogic {
	return &UploadLogic{
		Logger: logx.WithContext(r.Context()),
		ctx:    r.Context(),
		svcCtx: svcCtx,
		r:      r,
	}
}

func (l *UploadLogic) Upload() (resp *types.UploadResp, err error) {
	// 解析文件
	file, handler, err := l.r.FormFile("file")
	if err != nil {
		logx.Error("the value of file cannot be found")
		return nil, errors.New("the value of file cannot be found")
	}
	defer file.Close()

	// 检查文件
	fileType := l.getFileType(handler)
	if err = l.checkFile(fileType, handler); err != nil {
		return nil, err
	}

	// 保存
	// 第一步：提交数据库信息
	data, err := l.saveFileInfoToDb(fileType, handler)
	if err != nil {
		return nil, err
	}
	// 第二步：保存文件
	filePath := path.Join(l.svcCtx.Config.UploadConf.StorePath, data.Path)
	if err := l.saveFileToLocal(filePath, file); err != nil {
		return nil, err
	}

	//pdf文件加锁,密码是文件id,加锁失败不报错
	if strings.ToLower(filepath.Ext(handler.Filename)) == ".pdf" {
		err = file_crypto.PDFAes256LockFile(filePath, utils.MD5Hex(data.Id))
		if err != nil {
			logc.Errorf(l.ctx, "fail to lock pdf file[%s]", filePath)
		}
	}

	//文件加密
	if err := l.fileEncrypt(filePath); err != nil {
		return nil, err
	}

	return &types.UploadResp{
		BaseDataInfo: types.BaseDataInfo{},
		Data: types.UploadInfo{
			Id:         data.Id,
			Name:       handler.Filename,
			OriginName: handler.Filename,
			Hash:       "",
			Url:        l.svcCtx.Config.FileServerConf.GenerateDownloadUrlNew(data.Id, data.Path, l.svcCtx.Config),
		},
	}, nil
}

func (l *UploadLogic) fileEncrypt(filePath string) error {
	// 跳过的文件后缀
	if slices.Contains(l.svcCtx.Config.UploadConf.FileEncryptSkipSuffix, strings.ToLower(path.Ext(filePath))) {
		return nil
	}
	if err := file_crypto.AesEncryptFile(filePath, file_crypto.PwdKey); err != nil {
		logc.Error(l.ctx, "fail to create file")
		return errors.New("fail to create file")
	}
	return nil
}

func (l *UploadLogic) getFileType(handler *multipart.FileHeader) string {
	fileType := strings.Split(handler.Header.Get("Content-Type"), "/")[0]
	if fileType != "image" && fileType != "video" && fileType != "audio" {
		fileType = "other"
	}
	return fileType
}

func (l *UploadLogic) checkFile(fileType string, handler *multipart.FileHeader) error {
	// 文件检查
	// 拒绝无后缀文件
	ext := filepath.Ext(handler.Filename)
	if ext == "" || ext == "." {
		return errors.New("reject the file which does not have suffix")
	}
	if err := filex3.CheckOverSize(l.ctx, l.svcCtx, fileType, handler.Size); err != nil {
		return errors.New("fail check file: " + err.Error())
	}
	return nil
}

func (l *UploadLogic) saveFileInfoToDb(fileType string, handler *multipart.FileHeader) (*services.FileInfo, error) {
	fileUUID := uuidx.NewUUID().String()
	storeFileName := fileUUID + filepath.Ext(handler.Filename)
	timeString := time.Now().Format(format.DashYearToDay)
	userId := utils.GetCurrentLoginUser(l.ctx).UserId
	relativePath := fmt.Sprintf("/%s/%s/%s/%s", l.svcCtx.Config.Name,
		fileType, timeString, storeFileName)
	info := &services.FileInfo{
		Name:       handler.Filename,
		OriginName: handler.Filename,
		Uuid:       fileUUID,
		FileType:   uint32(filex3.ConvertFileTypeToUint8(fileType)),
		Path:       relativePath,
		UserId:     userId,
		Hash:       "", // 暂不需要
		OpenStatus: 1,
		Size:       uint64(handler.Size),
	}
	data, err := l.svcCtx.SaasService.CreateFile(l.ctx, info)
	if err != nil {
		return nil, errors.New("fail to create file to db")
	}
	info.Id = data.Id
	return info, nil
}

func (l *UploadLogic) saveFileToLocal(filePath string, file multipart.File) error {
	dir := path.Dir(filePath)
	if err := filex2.MkdirIfNotExist(dir, filex2.SuperPerm); err != nil {
		logc.Error(l.ctx, fmt.Sprintf("failed to create directory[%s] for storing private files", dir))
		return errors.New("failed to create directory")
	}
	targetFile, err := os.Create(filePath)
	if err != nil {
		logc.Errorw(l.ctx, fmt.Sprintf("failed to create file[%s]", filePath))
		return errors.New("failed to save file")
	}
	defer targetFile.Close()
	if _, err = io.Copy(targetFile, file); err != nil {
		logc.Errorw(l.ctx, fmt.Sprintf("failed to Copy file[%s] for storing private files", filePath))
		return errors.New("failed to Copy file")
	}
	return nil
}
