package file

import (
	"context"
	"fmt"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type GetFileLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetFileLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetFileLogic {
	return &GetFileLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetFileLogic) GetFile(req *types.IDPathReq) (resp *types.GetFileResp, err error) {
	data, err := l.svcCtx.SaasService.GetFileById(l.ctx, &services.IDReq{Id: req.Id})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	// 生成预签名 URL
	presignedURL, err := l.svcCtx.MinioClient.GenerateDownloadPreSignedURL(l.ctx, data.Path, data.Name)
	if err != nil {
		l.Logger.Error("failed to generate pre-signed URL", logx.Field("filePath", data.Path), logx.Field("error", err))
		return nil, fmt.Errorf("failed to generate download URL")
	}
	return &types.GetFileResp{
		BaseDataInfo: types.BaseDataInfo{
			Code: 0,
			Msg:  l.svcCtx.Trans.Trans(l.ctx, i18n.Success),
		},
		Data: types.FileInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: data.Id,
			},
			Name:       data.Name,
			OriginName: data.OriginName,
			Size:       data.Size,
			OpenStatus: uint8(data.OpenStatus),
			FileType:   uint8(data.FileType),
			UUID:       data.Uuid,
			UserID:     data.UserId,
			Url:        presignedURL,
			Path:       data.Path,
		},
	}, nil
}
