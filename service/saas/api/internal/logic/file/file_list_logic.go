package file

import (
	"context"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
)

type FileListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewFileListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *FileListLogic {
	return &FileListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *FileListLogic) FileList(req *types.FileListReq) (resp *types.FileListResp, err error) {
	data, err := l.svcCtx.SaasService.GetFileList(l.ctx,
		&services.FileListReq{
			Page:     req.Page,
			PageSize: req.PageSize,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	resp = &types.FileListResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	resp.Data.Total = data.Total
	resp.Data.Data = []types.FileInfo{}

	for _, v := range data.Data {
		url := ""
		resp.Data.Data = append(resp.Data.Data,
			types.FileInfo{
				BaseIDInfo: types.BaseIDInfo{
					Id: v.Id,
				},
				Name:       v.Name,
				OriginName: v.OriginName,
				Size:       v.Size,
				OpenStatus: uint8(v.OpenStatus),
				FileType:   uint8(v.FileType),
				UUID:       v.Uuid,
				UserID:     v.UserId,
				Hash:       v.Hash,
				Url:        url,
			})
	}
	return resp, nil
}
