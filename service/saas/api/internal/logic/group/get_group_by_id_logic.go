package group

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type GetGroupByIdLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetGroupByIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetGroupByIdLogic {
	return &GetGroupByIdLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetGroupByIdLogic) GetGroupById(req *types.IDReq) (resp *types.GroupInfoResp, err error) {
	data, err := l.svcCtx.SaasService.GetGroupById(l.ctx, &services.IDReq{Id: req.Id})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	return &types.GroupInfoResp{
		BaseDataInfo: types.BaseDataInfo{
			Code: 0,
			Msg:  l.svcCtx.Trans.Trans(l.ctx, i18n.Success),
		},
		Data: types.GroupInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: data.Id,
			},
			Status:        data.Status,
			Sort:          data.Sort,
			TenantId:      data.TenantId,
			GroupTypeId:   data.GroupTypeId,
			Name:          data.Name,
			Code:          data.Code,
			Remark:        data.Remark,
			GroupTypeName: data.GroupTypeName,
		},
	}, nil
}
