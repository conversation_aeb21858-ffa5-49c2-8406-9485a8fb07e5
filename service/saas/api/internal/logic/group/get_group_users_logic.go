package group

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type GetGroupUsersLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetGroupUsersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetGroupUsersLogic {
	return &GetGroupUsersLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetGroupUsersLogic) GetGroupUsers(req *types.GetGroupUsersReq) (resp *types.GetGroupUsersResp, err error) {
	data, err := l.svcCtx.SaasService.GetGroupUsers(l.ctx, &services.GetGroupUsersReq{GroupId: req.GroupId, Search: req.Search})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	var users []types.UserInfo

	for _, v := range data.Users {
		users = append(users, types.UserInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: v.Id,
			},
			Status:   v.Status,
			Username: v.Username,
			//Password: v.Password,
			Nickname: v.Nickname,
			Mobile:   v.Mobile,
			Email:    v.Email,
			Avatar: types.AvatarInfo{
				Id:  v.Avatar.Id,
				Url: v.Avatar.Path,
			},
		})
	}

	if len(users) == 0 {
		users = make([]types.UserInfo, 0)
	}

	return &types.GetGroupUsersResp{
		BaseDataInfo: types.BaseDataInfo{
			Code: 0,
			Msg:  l.svcCtx.Trans.Trans(l.ctx, i18n.Success),
		},
		Data: types.GroupUsersList{
			Data: users,
		},
	}, nil
}
