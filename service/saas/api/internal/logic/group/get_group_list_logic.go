package group

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type GetGroupListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetGroupListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetGroupListLogic {
	return &GetGroupListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetGroupListLogic) GetGroupList(req *types.GroupListReq) (resp *types.GroupListResp, err error) {
	data, err := l.svcCtx.SaasService.GetGroupList(l.ctx,
		&services.GroupListReq{
			Page:     req.Page,
			PageSize: req.PageSize,
			Name:     req.Name,
			Code:     req.Code,
			Remark:   req.Remark,
			Search:   req.Search,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	resp = &types.GroupListResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	resp.Data.Total = data.Total
	resp.Data.Data = make([]types.GroupInfo, 0)

	for _, v := range data.Data {
		resp.Data.Data = append(resp.Data.Data,
			types.GroupInfo{
				BaseIDInfo: types.BaseIDInfo{
					Id: v.Id,
				},
				Status:        v.Status,
				Sort:          v.Sort,
				TenantId:      v.TenantId,
				GroupTypeId:   v.GroupTypeId,
				Name:          v.Name,
				Code:          v.Code,
				Remark:        v.Remark,
				GroupTypeName: v.GroupTypeName,
			})
	}
	return resp, nil
}
