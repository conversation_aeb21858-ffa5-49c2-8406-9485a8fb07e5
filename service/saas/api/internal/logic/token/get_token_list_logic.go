package token

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type GetTokenListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetTokenListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTokenListLogic {
	return &GetTokenListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetTokenListLogic) GetTokenList(req *types.TokenListReq) (resp *types.TokenListResp, err error) {
	data, err := l.svcCtx.SaasService.GetTokenList(l.ctx,
		&services.TokenListReq{
			Page:     req.Page,
			PageSize: req.PageSize,
			Token:    req.Token,
			Source:   req.Source,
			Search:   req.Search,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	resp = &types.TokenListResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	resp.Data.Total = data.Total
	resp.Data.Data = []types.TokenInfo{}

	for _, v := range data.Data {
		resp.Data.Data = append(resp.Data.Data,
			types.TokenInfo{
				BaseIDInfo: types.BaseIDInfo{
					Id: v.Id,
				},
				Status:    v.Status,
				TenantId:  v.TenantId,
				Uid:       v.Uid,
				Token:     v.Token,
				Source:    v.Source,
				ExpiredAt: v.ExpiredAt,
			})
	}
	return resp, nil
}
