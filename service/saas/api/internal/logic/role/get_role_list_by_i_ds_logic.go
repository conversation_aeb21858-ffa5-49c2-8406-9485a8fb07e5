package role

import (
	"context"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/ent/role"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
)

type GetRoleListByIDsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetRoleListByIDsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetRoleListByIDsLogic {
	return &GetRoleListByIDsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetRoleListByIDsLogic) GetRoleListByIDs(req *types.RoleListByIDsReq) (resp *types.RoleListByIDsResp, err error) {
	data, err := l.svcCtx.DB.Role.Query().Where(role.IDIn(req.RoleIds...)).All(l.ctx)
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	resp = &types.RoleListByIDsResp{
		Data: make([]types.RoleInfo, 0),
	}
	for _, v := range data {
		resp.Data = append(resp.Data, types.RoleInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: v.ID,
			},
			Name:           v.Name,
			Code:           v.Code,
			Remark:         v.Remark,
			Status:         v.Status,
			Sort:           v.Sort,
			OrganizationId: v.OrganizationID,
		})
	}
	return
}
