package role

import (
	"context"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/ent/role"
	"phoenix/service/saas/model/ent/user"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type CheckUserHasRoleCodeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCheckUserHasRoleCodeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CheckUserHasRoleCodeLogic {
	return &CheckUserHasRoleCodeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CheckUserHasRoleCodeLogic) CheckUserHasRoleCode(req *types.CheckUserHasRoleCodeReq) (resp *types.CheckUserHasRoleCodeResp, err error) {
	exist, err := l.svcCtx.DB.User.Query().Where(user.IDEQ(req.UserId), user.HasRolesWith(role.CodeEQ(req.RoleCode), role.StatusEQ(true), role.DeletedAtIsNil(), role.OrganizationIDEQ(utils.GetContextOrganizationID(l.ctx)))).Exist(l.ctx)
	if err != nil {
		return nil, err
	}
	return &types.CheckUserHasRoleCodeResp{
		Data: exist,
	}, nil
}
