package role

import (
	"context"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
)

type CreateRoleLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateRoleLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateRoleLogic {
	return &CreateRoleLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateRoleLogic) CreateRole(req *types.RoleCreateReq) (resp *types.BaseDataInfo, err error) {
	organizationId := utils.GetContextOrganizationID(l.ctx)
	data, err := l.svcCtx.SaasService.CreateRole(l.ctx,
		&services.RoleInfo{
			Status:         req.Status,
			Sort:           req.Sort,
			TenantId:       req.TenantId,
			Name:           req.Name,
			Code:           req.Code,
			DefaultRouter:  req.DefaultRouter,
			Remark:         req.Remark,
			ParentId:       req.ParentId,
			OrganizationId: organizationId,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, data.Msg), Data: types.EmptyData{}}, nil
}
