package role

import (
	"context"
	"phoenix/service/saas/utils"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetRolesByUserIDLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetRolesByUserIDLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetRolesByUserIDLogic {
	return &GetRolesByUserIDLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetRolesByUserIDLogic) GetRolesByUserID(req *types.GetRolesByUserIDReq) (resp *types.RolesResp, err error) {
	// 获取用户登录信息
	userLoginInfo := utils.GetCurrentLoginUser(l.ctx)
	if req.UserID == "" {
		req.UserID = userLoginInfo.UserId
	}
	orgID := userLoginInfo.OrganizationId

	// 查询该用户在当前组织下的所有角色
	roles, err := l.svcCtx.SaasService.GetRolesByUserIDAndOrgID(l.ctx, req.UserID, orgID)
	if err != nil {
		return nil, err
	}

	// 提取角色code列表
	var roleCode []string
	for _, role := range roles {
		roleCode = append(roleCode, role.Code)
	}

	return &types.RolesResp{
		BaseDataInfo: types.BaseDataInfo{},
		Data:         roleCode,
	}, nil
}
