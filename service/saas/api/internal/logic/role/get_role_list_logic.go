package role

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/utils"
)

type GetRoleListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetRoleListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetRoleListLogic {
	return &GetRoleListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetRoleListLogic) GetRoleList(req *types.RoleListReq) (resp *types.RoleListResp, err error) {
	organizationId := utils.GetContextOrganizationID(l.ctx)
	data, err := l.svcCtx.SaasService.GetRoleList(l.ctx,
		&services.RoleListReq{
			Page:           req.Page,
			PageSize:       req.PageSize,
			Name:           req.Name,
			Code:           req.Code,
			DefaultRouter:  req.DefaultRouter,
			Search:         req.Search,
			OrganizationId: organizationId,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	resp = &types.RoleListResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	resp.Data.Total = data.Total
	resp.Data.Data = make([]types.RoleInfo, 0)
	for _, v := range data.Data {
		resp.Data.Data = append(resp.Data.Data,
			types.RoleInfo{
				BaseIDInfo: types.BaseIDInfo{
					Id: v.Id,
				},
				Status:         v.Status,
				Sort:           v.Sort,
				TenantId:       v.TenantId,
				Name:           v.Name,
				Code:           v.Code,
				DefaultRouter:  v.DefaultRouter,
				Remark:         v.Remark,
				ParentId:       v.ParentId,
				OrganizationId: v.OrganizationId,
			})
	}
	return resp, nil
}
