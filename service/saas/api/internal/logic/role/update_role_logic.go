package role

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/utils"
)

type UpdateRoleLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateRoleLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateRoleLogic {
	return &UpdateRoleLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateRoleLogic) UpdateRole(req *types.RoleInfo) (resp *types.BaseDataInfo, err error) {
	organizationId := utils.GetContextOrganizationID(l.ctx)
	data, err := l.svcCtx.SaasService.UpdateRole(l.ctx,
		&services.RoleInfo{
			Id:             req.Id,
			Status:         req.Status,
			Sort:           req.Sort,
			TenantId:       req.TenantId,
			Name:           req.Name,
			Code:           req.Code,
			DefaultRouter:  req.DefaultRouter,
			Remark:         req.Remark,
			ParentId:       req.ParentId,
			OrganizationId: organizationId,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, data.Msg), Data: types.EmptyData{}}, nil
}
