package role

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type GetRoleUsersLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetRoleUsersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetRoleUsersLogic {
	return &GetRoleUsersLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetRoleUsersLogic) GetRoleUsers(req *types.GetRoleUsersReq) (resp *types.GetRoleUsersResp, err error) {
	data, err := l.svcCtx.SaasService.GetRoleUsers(l.ctx, &services.GetRoleUsersReq{RoleId: req.RoleId, Search: req.Search})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	var users []types.UserInfo

	for _, v := range data.Users {
		users = append(users, types.UserInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: v.Id,
			},
			Status:   v.Status,
			Username: v.Username,
			//Password: v.Password,
			Nickname: v.Nickname,
			Mobile:   v.Mobile,
			Email:    v.Email,
			Avatar: types.AvatarInfo{
				Id:  v.Avatar.Id,
				Url: l.svcCtx.Config.FileServerConf.GenerateDownloadUrlNew(v.Avatar.Id, v.Avatar.Path, l.svcCtx.Config),
			},
		})
	}

	if len(users) == 0 {
		users = make([]types.UserInfo, 0)
	}

	return &types.GetRoleUsersResp{
		BaseDataInfo: types.BaseDataInfo{
			Code: 0,
			Msg:  l.svcCtx.Trans.Trans(l.ctx, i18n.Success),
		},
		Data: types.RoleUsersList{
			Data: users,
		},
	}, nil
}
