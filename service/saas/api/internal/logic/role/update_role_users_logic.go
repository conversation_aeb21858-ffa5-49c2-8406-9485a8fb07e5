package role

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type UpdateRoleUsersLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateRoleUsersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateRoleUsersLogic {
	return &UpdateRoleUsersLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateRoleUsersLogic) UpdateRoleUsers(req *types.UpdataRoleUsersReq) (resp *types.BaseDataInfo, err error) {
	data, err := l.svcCtx.SaasService.UpdateRoleUsers(l.ctx,
		&services.UpdateRoleUsersReq{
			RoleId:  req.RoleId,
			UserIds: req.UserIds,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, data.Msg), Data: types.EmptyData{}}, nil
}
