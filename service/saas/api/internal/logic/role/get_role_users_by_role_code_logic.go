package role

import (
	"context"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/ent/role"
	"phoenix/service/saas/model/ent/user"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetRoleUsersByRoleCodeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetRoleUsersByRoleCodeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetRoleUsersByRoleCodeLogic {
	return &GetRoleUsersByRoleCodeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetRoleUsersByRoleCodeLogic) GetRoleUsersByRoleCode(req *types.GetRoleUsersByRoleCodeReq) (resp *types.GetRoleUsersByRoleCodeResp, err error) {
	users, err := l.svcCtx.DB.User.Query().Where(user.HasRolesWith(role.CodeEQ(req.RoleCode), role.StatusEQ(true), role.DeletedAtIsNil(), role.OrganizationIDEQ(utils.GetContextOrganizationID(l.ctx)))).All(l.ctx)
	if err != nil {
		return nil, err
	}

	var usersInfo []types.UserInfo
	for _, v := range users {
		usersInfo = append(usersInfo, types.UserInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: v.ID,
			},
			Status:   v.Status,
			Username: v.Username,
			Nickname: v.Nickname,
			Mobile:   v.Mobile,
		})
	}

	return &types.GetRoleUsersByRoleCodeResp{
		Data: usersInfo,
	}, nil
}
