package button

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type GetButtonByIdLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetButtonByIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetButtonByIdLogic {
	return &GetButtonByIdLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetButtonByIdLogic) GetButtonById(req *types.IDReq) (resp *types.ButtonInfoResp, err error) {
	data, err := l.svcCtx.SaasService.GetButtonById(l.ctx, &services.IDReq{Id: req.Id})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	return &types.ButtonInfoResp{
		BaseDataInfo: types.BaseDataInfo{
			Code: 0,
			Msg:  l.svcCtx.Trans.Trans(l.ctx, i18n.Success),
		},
		Data: types.ButtonInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: data.Id,
			},
			Sort:   data.Sort,
			Name:   data.Name,
			Code:   data.Code,
			MenuId: data.MenuId,
		},
	}, nil
}
