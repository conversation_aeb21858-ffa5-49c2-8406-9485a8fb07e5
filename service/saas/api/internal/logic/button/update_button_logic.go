package button

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type UpdateButtonLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateButtonLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateButtonLogic {
	return &UpdateButtonLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateButtonLogic) UpdateButton(req *types.ButtonInfo) (resp *types.BaseDataInfo, err error) {
	data, err := l.svcCtx.SaasService.UpdateButton(l.ctx,
		&services.ButtonInfo{
			Id:     req.Id,
			Sort:   req.Sort,
			Name:   req.Name,
			Code:   req.Code,
			MenuId: req.MenuId,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, data.Msg), Data: types.EmptyData{}}, nil
}
