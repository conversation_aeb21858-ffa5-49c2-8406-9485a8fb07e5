package button

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type GetButtonListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetButtonListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetButtonListLogic {
	return &GetButtonListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetButtonListLogic) GetButtonList(req *types.ButtonListReq) (resp *types.ButtonListResp, err error) {
	data, err := l.svcCtx.SaasService.GetButtonList(l.ctx,
		&services.ButtonListReq{
			Page:     req.Page,
			PageSize: req.PageSize,
			Name:     req.Name,
			Code:     req.Code,
			MenuId:   req.MenuId,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	resp = &types.ButtonListResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	resp.Data.Total = data.Total
	resp.Data.Data = make([]types.ButtonInfo, 0)

	for _, v := range data.Data {
		resp.Data.Data = append(resp.Data.Data,
			types.ButtonInfo{
				BaseIDInfo: types.BaseIDInfo{
					Id: v.Id,
				},
				Sort:   v.Sort,
				Name:   v.Name,
				Code:   v.Code,
				MenuId: v.MenuId,
			})
	}
	return resp, nil
}
