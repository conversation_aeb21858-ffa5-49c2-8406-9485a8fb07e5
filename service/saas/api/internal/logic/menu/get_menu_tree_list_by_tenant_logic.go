package menu

import (
	"context"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetMenuTreeListByTenantLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetMenuTreeListByTenantLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMenuTreeListByTenantLogic {
	return &GetMenuTreeListByTenantLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetMenuTreeListByTenantLogic) GetMenuTreeListByTenant() (resp *types.MenuListResp, err error) {
	data, err := l.svcCtx.SaasService.GetTenantMenuList(l.ctx,
		&services.MenuListReq{
			Page:     1,
			PageSize: 100000,
			NoParent: true,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	if data == nil {
		return resp, nil
	}

	resp = &types.MenuListResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	resp.Data.Total = data.Total
	resp.Data.Data = make([]types.MenuInfo, 0)

	for _, v := range data.Data {
		resp.Data.Data = append(resp.Data.Data, GetMenuInfoWithChildrenAndButtons(v))
	}
	return resp, nil
}
