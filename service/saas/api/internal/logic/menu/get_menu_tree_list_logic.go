package menu

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type GetMenuTreeListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetMenuTreeListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMenuTreeListLogic {
	return &GetMenuTreeListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetMenuTreeListLogic) GetMenuTreeList(req *types.MenuListReq) (resp *types.MenuListResp, err error) {
	data, err := l.svcCtx.SaasService.GetMenuList(l.ctx,
		&services.MenuListReq{
			Page:     1,
			PageSize: 100000,
			Name:     req.Name,
			Title:    req.Title,
			ParentId: req.ParentId,
			NoParent: true,
			Icon:     req.Icon,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	resp = &types.MenuListResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	resp.Data.Total = data.Total
	resp.Data.Data = make([]types.MenuInfo, 0)

	for _, v := range data.Data {
		resp.Data.Data = append(resp.Data.Data, GetMenuInfoWithChildrenAndButtons(v))
	}
	return resp, nil
}

func GetMenuInfoWithChildrenAndButtons(menu *services.MenuInfo) types.MenuInfo {

	//  初始化一个空的切片，保证返回的切片不为nil，默认为空列表
	children := make([]types.MenuInfo, 0)
	for _, v := range menu.Children {
		children = append(children, GetMenuInfoWithChildrenAndButtons(v))
	}

	//  初始化一个空的切片，保证返回的切片不为nil，默认为空列表
	buttons := make([]types.ButtonInfo, 0)
	for _, v := range menu.Buttons {
		buttons = append(buttons, types.ButtonInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: v.Id,
			},
			Sort:     v.Sort,
			Name:     v.Name,
			Code:     v.Code,
			MenuId:   v.MenuId,
			NodeType: "button",
		})
	}

	return types.MenuInfo{
		BaseIDInfo: types.BaseIDInfo{
			Id: menu.Id,
		},
		Sort:        menu.Sort,
		Name:        menu.Name,
		Title:       menu.Title,
		Icon:        menu.Icon,
		ParentId:    menu.ParentId,
		MenuType:    menu.MenuType,
		NodeType:    "menu",
		Url:         menu.Url,
		Redirect:    menu.Redirect,
		Component:   menu.Component,
		IsActive:    menu.IsActive,
		Hidden:      menu.Hidden,
		HiddenInTab: menu.HiddenInTab,
		Fixed:       menu.Fixed,
		Remark:      menu.Remark,
		Meta:        menu.Meta,
		Permissions: buttons,
		Children:    children,
		IsFullPage:  menu.IsFullPage,
	}
}
