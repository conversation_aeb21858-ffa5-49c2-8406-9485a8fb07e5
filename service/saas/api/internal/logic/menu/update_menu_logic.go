package menu

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type UpdateMenuLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateMenuLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateMenuLogic {
	return &UpdateMenuLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateMenuLogic) UpdateMenu(req *types.MenuUpdateReq) (resp *types.BaseDataInfo, err error) {
	data, err := l.svcCtx.SaasService.UpdateMenu(l.ctx,
		&services.MenuInfo{
			Id:          req.Id,
			Sort:        req.Sort,
			Name:        req.Name,
			Title:       req.Title,
			Icon:        req.Icon,
			ParentId:    req.ParentId,
			Url:         req.Url,
			Redirect:    req.Redirect,
			Component:   req.Component,
			IsActive:    req.IsActive,
			Hidden:      req.Hidden,
			HiddenInTab: req.HiddenInTab,
			Fixed:       req.Fixed,
			Remark:      req.Remark,
			Meta:        req.Meta,
			IsFullPage:  req.IsFullPage,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, data.Msg), Data: types.EmptyData{}}, nil
}
