package menu

import (
	"context"
	"encoding/json"
	"errors"
	"io"
	"mime/multipart"
	"net/http"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
)

type ImportMenuFromFileLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
	r      *http.Request
}

func NewImportMenuFromFileLogic(r *http.Request, svcCtx *svc.ServiceContext) *ImportMenuFromFileLogic {
	return &ImportMenuFromFileLogic{
		Logger: logx.WithContext(r.Context()),
		ctx:    r.Context(),
		svcCtx: svcCtx,
		r:      r,
	}
}

func (l *ImportMenuFromFileLogic) ImportMenuFromFile() (resp *types.BaseDataInfo, err error) {
	// 解析上传的文件
	file, handler, err := l.r.FormFile("file")
	if err != nil {
		logx.Error("文件上传失败：", err)
		return nil, errors.New("文件上传失败")
	}
	defer file.Close()

	// 检查文件类型
	if !l.isValidJSONFile(handler) {
		return nil, errors.New("请上传有效的JSON文件")
	}

	// 读取文件内容
	fileContent, err := io.ReadAll(file)
	if err != nil {
		logx.Error("读取文件内容失败：", err)
		return nil, errors.New("读取文件内容失败")
	}

	// 解析JSON内容
	var menuData []utils.Menu
	if err := json.Unmarshal(fileContent, &menuData); err != nil {
		logx.Error("JSON解析失败：", err)
		return nil, errors.New("JSON格式错误，请检查文件内容")
	}

	// 将菜单数据转换为JSON字符串
	menuJSON, err := json.Marshal(menuData)
	if err != nil {
		logx.Error("菜单数据序列化失败：", err)
		return nil, errors.New("菜单数据处理失败")
	}

	// 调用现有的菜单导入服务
	_, err = l.svcCtx.SaasService.UpdateMenus(l.ctx, &services.UpdateMenusReq{
		Menus: string(menuJSON),
	})
	if err != nil {
		logx.Error("菜单导入失败：", err)
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	return &types.BaseDataInfo{Msg: "菜单导入成功"}, nil
}

func (l *ImportMenuFromFileLogic) isValidJSONFile(handler *multipart.FileHeader) bool {
	// 检查文件扩展名
	fileName := handler.Filename
	if len(fileName) < 5 {
		return false
	}

	// 检查是否为.json文件
	extension := fileName[len(fileName)-5:]
	return extension == ".json"
}
