package menu

import (
	"context"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
)

type GetMenuByUserIdLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetMenuByUserIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMenuByUserIdLogic {
	return &GetMenuByUserIdLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetMenuByUserIdLogic) GetMenuByUserId(req *types.IDPathReq) (resp *types.MenuListResp, err error) {
	// 获取用户信息
	userData, err := l.svcCtx.SaasService.GetUserWithExtraInfoById(l.ctx, &services.IDReq{Id: req.Id})
	if err != nil {
		logx.Errorw("user not found", logx.Field("detail", req.Id))
		return nil, errorx.NewApiNotFoundError("用户不存在")
	}

	// 获取租户信息
	tenantInfo, err := l.svcCtx.SaasService.GetTenantById(l.ctx, &services.IDReq{Id: utils.GetContextTenantID(l.ctx)})
	if err != nil {
		logx.Errorw("tenant not found", logx.Field("detail", userData.User.DefaultTenantId))
		return nil, errorx.NewApiNotFoundError("租户不存在")
	}

	// 是否是超级管理员
	userId := ""
	if !tenantInfo.IsSuper && !userData.User.IsSuperuser {
		// 如果不是超级管理员、不是超级租户
		// 查询条件中加入用户id
		userId = req.Id
	}

	data, err := l.svcCtx.SaasService.GetMenuList(l.ctx,
		&services.MenuListReq{
			Page:          1,
			PageSize:      100000,
			UserId:        userId,
			NoParent:      true,
			IsSuperAdmin:  userData.User.IsSuperuser,
			IsSuperTenant: tenantInfo.IsSuper,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	resp = &types.MenuListResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	resp.Data.Total = data.Total
	resp.Data.Data = make([]types.MenuInfo, 0)

	for _, v := range data.Data {
		resp.Data.Data = append(resp.Data.Data, GetMenuInfoWithChildrenAndButtons(v))
	}
	return resp, nil
}
