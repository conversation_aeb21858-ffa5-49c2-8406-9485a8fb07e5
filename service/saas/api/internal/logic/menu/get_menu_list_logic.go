package menu

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type GetMenuListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetMenuListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetMenuListLogic {
	return &GetMenuListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetMenuListLogic) GetMenuList(req *types.MenuListReq) (resp *types.MenuListResp, err error) {
	data, err := l.svcCtx.SaasService.GetMenuList(l.ctx,
		&services.MenuListReq{
			Page:     req.Page,
			PageSize: req.PageSize,
			Name:     req.Name,
			Title:    req.Title,
			Icon:     req.Icon,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	resp = &types.MenuListResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	resp.Data.Total = data.Total
	resp.Data.Data = make([]types.MenuInfo, 0)

	for _, v := range data.Data {
		resp.Data.Data = append(resp.Data.Data,
			types.MenuInfo{
				BaseIDInfo: types.BaseIDInfo{
					Id: v.Id,
				},
				Sort:        v.Sort,
				Name:        v.Name,
				Title:       v.Title,
				Icon:        v.Icon,
				ParentId:    v.ParentId,
				MenuType:    v.MenuType,
				Url:         v.Url,
				Redirect:    v.Redirect,
				Component:   v.Component,
				IsActive:    v.IsActive,
				Hidden:      v.Hidden,
				HiddenInTab: v.HiddenInTab,
				Fixed:       v.Fixed,
				Remark:      v.Remark,
				Meta:        v.Meta,
				IsFullPage:  v.IsFullPage,
			})
	}
	return resp, nil
}
