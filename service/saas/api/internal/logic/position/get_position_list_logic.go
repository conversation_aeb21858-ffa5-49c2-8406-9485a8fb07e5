package position

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/utils"
)

type GetPositionListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetPositionListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPositionListLogic {
	return &GetPositionListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetPositionListLogic) GetPositionList(req *types.PositionListReq) (resp *types.PositionListResp, err error) {
	organizationId := utils.GetContextOrganizationID(l.ctx)
	data, err := l.svcCtx.SaasService.GetPositionList(l.ctx,
		&services.PositionListReq{
			Page:           req.Page,
			PageSize:       req.PageSize,
			Name:           req.Name,
			Code:           req.Code,
			Remark:         req.Remark,
			Search:         req.Search,
			Status:         req.Status,
			OrganizationId: organizationId,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	resp = &types.PositionListResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	resp.Data.Total = data.Total
	resp.Data.Data = make([]types.PositionInfo, 0)

	for _, v := range data.Data {
		resp.Data.Data = append(resp.Data.Data,
			types.PositionInfo{
				BaseIDInfo: types.BaseIDInfo{
					Id: v.Id,
				},
				Status:   v.Status,
				Sort:     v.Sort,
				TenantId: v.TenantId,
				Name:     v.Name,
				Code:     v.Code,
				Remark:   v.Remark,
			})
	}
	return resp, nil
}
