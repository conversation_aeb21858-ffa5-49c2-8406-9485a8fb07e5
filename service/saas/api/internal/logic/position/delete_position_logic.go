package position

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/gorm/mapper"
)

type DeletePositionLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeletePositionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeletePositionLogic {
	return &DeletePositionLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeletePositionLogic) DeletePosition(req *types.IDsReq) (resp *types.BaseDataInfo, err error) {
	result, err := l.svcCtx.SaasService.DeletePosition(l.ctx, &services.IDsReq{
		Ids: req.Ids,
	})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	userPositionsOrganizationsClient := mapper.NewUserPositionsOrganizationsClient(l.svcCtx.GormDB)
	err = userPositionsOrganizationsClient.DeleteByPositionIds(l.ctx, req.Ids)
	if err != nil {
		l.Logger.Errorf("删除用户岗位绑定记录失败：%v", err)
		return nil, err
	}

	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, result.Msg), Data: types.EmptyData{}}, nil
}
