package position

import (
	"context"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/ent/position"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
)

type GetPositionListByIDsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetPositionListByIDsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPositionListByIDsLogic {
	return &GetPositionListByIDsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetPositionListByIDsLogic) GetPositionListByIDs(req *types.PositionListByIDsReq) (resp *types.PositionListByIDsResp, err error) {
	data, err := l.svcCtx.DB.Position.Query().Where(position.IDIn(req.PositionIds...)).All(l.ctx)
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	resp = &types.PositionListByIDsResp{
		Data: make([]types.PositionInfo, 0),
	}
	for _, v := range data {
		resp.Data = append(resp.Data, types.PositionInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: v.ID,
			},
			Status: v.Status,
			Sort:   v.Sort,
			Name:   v.Name,
			Code:   v.Code,
			Remark: v.Remark,
		})
	}
	return
}
