package dataexportrecord

import (
	"context"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetDataExportListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetDataExportListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetDataExportListLogic {
	return &GetDataExportListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetDataExportListLogic) GetDataExportList(req *types.DataExportRecordListReq) (resp *types.DataExportRecordListResp, err error) {
	userId := utils.GetContextUserID(l.ctx)
	dataExportRecordClient := mapper.NewDataExportRecordClient(l.svcCtx.GormDB)
	total, dataExportRecordList, err := dataExportRecordClient.GetDataExportRecordList(l.ctx, req.Page, req.PageSize, req.NoPage, mapper.DataExportRecord{
		UserId:     userId,
		FileName:   req.Search,
		Status:     req.Status,
		ModuleName: req.ModuleName,
	})
	if err != nil {
		l.Logger.Errorf("获取数据导出信息列表失败：%v", err)
		return nil, err
	}
	dataExportRecordListResp := l.gormToResp(dataExportRecordList)
	return &types.DataExportRecordListResp{
		BaseDataInfo: types.BaseDataInfo{},
		Data: types.DataExportRecordListInfo{
			Total: total,
			Data:  dataExportRecordListResp,
		},
	}, nil
}

func (l *GetDataExportListLogic) gormToResp(gorm []mapper.DataExportRecord) (resp []types.DataExportRecordInfo) {
	for _, v := range gorm {
		resp = append(resp, types.DataExportRecordInfo{
			ID:         v.Id,
			TaskID:     v.TaskId,
			FileID:     v.FileId,
			FileName:   v.FileName,
			ModuleName: v.ModuleName,
			Status:     v.Status,
			UserID:     v.UserId,
			CreatedAt:  v.CreatedAt.UnixMilli(),
		})
	}
	return resp
}
