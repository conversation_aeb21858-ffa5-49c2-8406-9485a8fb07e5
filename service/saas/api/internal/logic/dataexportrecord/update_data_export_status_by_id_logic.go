package dataexportrecord

import (
	"context"
	"phoenix/service/saas/model/gorm/mapper"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateDataExportStatusByIdLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateDataExportStatusByIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateDataExportStatusByIdLogic {
	return &UpdateDataExportStatusByIdLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateDataExportStatusByIdLogic) UpdateDataExportStatusById(req *types.UpdateDataExportRecordReq) (resp *types.BaseDataInfo, err error) {
	dataExportRecordClient := mapper.NewDataExportRecordClient(l.svcCtx.GormDB)
	err = dataExportRecordClient.UpdateStatusById(l.ctx, req.ID)
	if err != nil {
		l.Logger.Errorf("数据导出状态修改失败：%v", err)
		return nil, err
	}
	return &types.BaseDataInfo{}, nil
}
