package workflow

import (
	"context"
	"errors"
	"time"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type SavePresetTemplateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSavePresetTemplateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SavePresetTemplateLogic {
	return &SavePresetTemplateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SavePresetTemplateLogic) SavePresetTemplate(req *types.PresetTemplateSaveReq) (resp *types.PresetTemplateSaveResp, err error) {
	workflowTemplatePresetClient := mapper.NewWorkflowTemplatePresetClient(l.svcCtx.GormDB)
	// 根据 BusinessID 和 Kind 查询是否存在
	template, err := workflowTemplatePresetClient.GetByBusinessIDAndKind(l.ctx, req.BusinessId, req.Kind)
	if err != nil {
		return nil, err
	}
	if template.ID != "" {
		return nil, errors.New("该模板唯一识别码已存在。")
	}

	if req.ID != "" {
		// 查询是否存在
		presetTemplate, err := workflowTemplatePresetClient.GetByID(l.ctx, req.ID)
		if err != nil {
			return nil, err
		}
		presetTemplate.Name = req.Name
		presetTemplate.BusinessID = req.BusinessId
		presetTemplate.Kind = req.Kind
		presetTemplate.UpdatedAt = time.Now()
		presetTemplate.UpdatedBy = utils.GetContextUserID(l.ctx)
		err = workflowTemplatePresetClient.Update(l.ctx, presetTemplate)
		if err != nil {
			return nil, err
		}
		return &types.PresetTemplateSaveResp{}, nil
	}

	err = workflowTemplatePresetClient.Create(l.ctx, l.buildPresetTemplate(req))
	if err != nil {
		return nil, err
	}
	return &types.PresetTemplateSaveResp{}, nil
}

func (l *SavePresetTemplateLogic) buildPresetTemplate(req *types.PresetTemplateSaveReq) mapper.WorkflowTemplatePreset {
	now := time.Now()
	user := utils.GetCurrentLoginUser(l.ctx)
	presetTemplate := mapper.WorkflowTemplatePreset{
		ID:         l.svcCtx.IDGenerator.GenerateIDString(),
		Name:       req.Name,
		BusinessID: req.BusinessId,
		Kind:       req.Kind,
		CreatedAt:  now,
		UpdatedAt:  now,
		CreatedBy:  user.UserId,
		UpdatedBy:  user.UserId,
		TenantID:   user.TenantId,
	}
	return presetTemplate
}
