package workflow

import (
	"context"
	"time"

	"phoenix/service/saas/adapter/addons"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetWorkflowTaskCCsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetWorkflowTaskCCsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetWorkflowTaskCCsLogic {
	return &GetWorkflowTaskCCsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetWorkflowTaskCCsLogic) GetWorkflowTaskCCs(req *types.WorkflowTaskCCsReq) (resp *types.WorkflowTaskCCsResp, err error) {
	ccs, err := mapper.NewWorkflowCCClient(l.svcCtx.GormDB).GetWorkflowCCs(l.ctx, mapper.GetWorkflowCCsReq{
		UserID:         utils.GetContextUserID(l.ctx),
		TenantID:       utils.GetContextTenantID(l.ctx),
		ConsultStatus:  req.ConsultStatus,
		CreatedAtStart: req.FlowCreatedTimeBegin,
		CreatedAtEnd:   req.FlowCreatedTimeEnd,
		OrganizationID: req.OrganizationId,
	})
	if err != nil {
		l.Logger.Errorf("查询抄送人失败: %v", err)
		return nil, err
	}
	if len(ccs) == 0 {
		return &types.WorkflowTaskCCsResp{}, nil
	}

	ccMap := make(map[string]mapper.WorkflowCC)
	sallyWorkflowIDs := make([]string, 0, len(ccs))
	for _, cc := range ccs {
		ccMap[cc.SallyWorkflowID] = cc
		sallyWorkflowIDs = append(sallyWorkflowIDs, cc.SallyWorkflowID)
	}

	sallyReq := l.convertToSallyReq(req, sallyWorkflowIDs)

	workflows, total, err := l.svcCtx.SallyAddons.GetWorkflows(l.ctx, sallyReq)
	if err != nil {
		return nil, err
	}

	mapWorkflows, err := l.convertToWorkflowTaskCCsInfo(workflows, ccMap)
	if err != nil {
		return nil, err
	}

	resp = &types.WorkflowTaskCCsResp{
		BaseListInfo: types.BaseListInfo{
			Total: uint64(total),
		},
		Data: mapWorkflows,
	}
	return resp, nil
}

func (l *GetWorkflowTaskCCsLogic) convertToSallyReq(req *types.WorkflowTaskCCsReq, sallyWorkflowIDs []string) addons.SallyGetWorkflowsParams {
	sallyReq := addons.SallyGetWorkflowsParams{
		SallyCommonSearchCond: addons.SallyCommonSearchCond{
			IDs: sallyWorkflowIDs,
		},
		SallyPageCond: addons.SallyPageCond{
			PageNo:   int(req.Page),
			PageSize: int(req.PageSize),
			NoPage:   req.NoPage,
		},
		SponsorID:      req.FlowCreatedUserId,
		WorkflowName:   req.Search,
		WorkflowStatus: req.FlowStatus,
	}
	if req.FlowCreatedTimeBegin != 0 {
		sallyReq.CreatedAtStart = time.UnixMilli(req.FlowCreatedTimeBegin)
	}
	if req.FlowCreatedTimeEnd != 0 {
		sallyReq.CreatedAtEnd = time.UnixMilli(req.FlowCreatedTimeEnd)
	}
	if req.FlowCreatedUserId != "" {
		sallyReq.SponsorID = req.FlowCreatedUserId
	}
	return sallyReq
}

func (l *GetWorkflowTaskCCsLogic) convertToWorkflowTaskCCsInfo(workflows []addons.SallyGetWorkflowsResult, ccMap map[string]mapper.WorkflowCC) ([]types.WorkflowTaskCCsInfo, error) {
	mapWorkflows := make([]types.WorkflowTaskCCsInfo, 0, len(workflows))
	for _, workflow := range workflows {
		cc := ccMap[workflow.ID]
		w, err := l.svcCtx.SallyAddons.GetWorkflow(l.ctx, workflow.ID)
		if err != nil {
			return nil, err
		}
		mapWorkflows = append(mapWorkflows, types.WorkflowTaskCCsInfo{
			CCId:                    cc.ID,
			FlowName:                workflow.Name,
			CCTime:                  workflow.CreatedAt.UnixMilli(),
			FlowCreatedUserNickname: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, w.SponsorID),
			FlowCreatedTime:         workflow.CreatedAt.UnixMilli(),
			FlowStatus:              workflow.Status,
			ConsultStatus:           cc.ConsultStatus,
			FlowId:                  workflow.ID,
		})
	}
	return mapWorkflows, nil
}
