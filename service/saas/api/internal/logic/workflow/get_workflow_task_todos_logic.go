package workflow

import (
	"context"
	"errors"
	"time"

	"phoenix/service/saas/adapter/addons"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/constant"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetWorkflowTaskTodosLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetWorkflowTaskTodosLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetWorkflowTaskTodosLogic {
	return &GetWorkflowTaskTodosLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetWorkflowTaskTodosLogic) GetWorkflowTaskTodos(req *types.WorkflowTaskTodosReq) (resp *types.WorkflowTaskTodosResp, err error) {
	sallyReq := l.convertToSallyReq(req)

	workflows, total, err := l.svcCtx.SallyAddons.GetNodeReviewTasks(l.ctx, sallyReq)
	if err != nil {
		return nil, err
	}

	if total == 0 {
		return &types.WorkflowTaskTodosResp{}, nil
	}

	mapWorkflows, err := l.convertToWorkflowTaskTodosInfo(workflows)
	if err != nil {
		return nil, err
	}

	resp = &types.WorkflowTaskTodosResp{
		BaseListInfo: types.BaseListInfo{
			Total: uint64(total),
		},
		Data: mapWorkflows,
	}
	return
}

func (l *GetWorkflowTaskTodosLogic) convertToSallyReq(req *types.WorkflowTaskTodosReq) addons.SallyGetNodeReviewTasksRequest {
	sallyReq := addons.SallyGetNodeReviewTasksRequest{
		NoPage:            req.NoPage,
		PageNo:            int(req.Page),
		PageSize:          int(req.PageSize),
		ReviewerID:        utils.GetContextUserID(l.ctx),
		WorkflowName:      req.Search,
		Statuses:          []string{addons.WorkflowStatusUnderReview},
		WorkflowSponsorID: req.FlowCreatedUserId,
	}
	if req.TaskCreatedTimeBegin != 0 {
		sallyReq.CreatedAtStart = time.UnixMilli(req.TaskCreatedTimeBegin)
	}
	if req.TaskCreatedTimeEnd != 0 {
		sallyReq.CreatedAtEnd = time.UnixMilli(req.TaskCreatedTimeEnd)
	}
	if req.FlowCreatedTimeBegin != 0 {
		sallyReq.WorkflowCreatedAtStart = time.UnixMilli(req.FlowCreatedTimeBegin)
	}
	if req.FlowCreatedTimeEnd != 0 {
		sallyReq.WorkflowCreatedAtEnd = time.UnixMilli(req.FlowCreatedTimeEnd)
	}
	if req.OrganizationId != "" {
		sallyReq.WorkflowBusinessParams = map[string]any{
			constant.WorkflowBusinessParamsOrganizationID: req.OrganizationId,
		}
	}
	return sallyReq
}

func (l *GetWorkflowTaskTodosLogic) convertToWorkflowTaskTodosInfo(workflows []addons.SallyNodeReviewTask) ([]types.WorkflowTaskTodoInfo, error) {
	mapWorkflows := make([]types.WorkflowTaskTodoInfo, 0, len(workflows))
	for _, workflow := range workflows {
		w, err := l.svcCtx.SallyAddons.GetWorkflow(l.ctx, workflow.WorkflowID)
		if err != nil {
			return nil, err
		}
		organizationId, ok := w.BusinessParams[constant.WorkflowBusinessParamsOrganizationID].(string)
		if !ok {
			return nil, errors.New("organizationId is not string")
		}

		currentNodeName := ""
		if workflow.Status == addons.WorkflowStatusUnderReview {
			currentNodeName = w.CurrentNodeName
		}

		mapWorkflows = append(mapWorkflows, types.WorkflowTaskTodoInfo{
			FlowName:                workflow.WorkflowName,
			CurrentNodeName:         currentNodeName,
			TaskCreatedTime:         workflow.NodeCreatedAt.UnixMilli(),
			OrganizationName:        l.svcCtx.QuickNameTranslator.TranslateOrganizationName(l.ctx, organizationId),
			FlowCreatedUserNickname: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, w.SponsorID),
			FlowCreatedTime:         workflow.WorkflowCreatedAt.UnixMilli(),
			FlowId:                  workflow.WorkflowID,
			TaskId:                  workflow.ID,
			NodeId:                  workflow.NodeID,
		})
	}
	return mapWorkflows, nil
}
