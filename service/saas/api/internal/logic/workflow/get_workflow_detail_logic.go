package workflow

import (
	"context"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"phoenix/service/saas/adapter/addons"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetWorkflowDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetWorkflowDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetWorkflowDetailLogic {
	return &GetWorkflowDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetWorkflowDetailLogic) GetWorkflowDetail(req *types.WorkflowDetailReq) (resp *types.WorkflowDetailResp, err error) {
	sallyWorkflow, err := l.svcCtx.SallyAddons.GetWorkflow(l.ctx, req.FlowId)
	if err != nil {
		l.Logger.Errorf("获取流程详情失败: %v", err)
		return nil, err
	}

	sallyWorkflowProgress, err := l.svcCtx.SallyAddons.GetWorkflowProgress(l.ctx, sallyWorkflow.ID)
	if err != nil {
		l.Logger.Errorf("获取流程详情失败: %v", err)
		return nil, err
	}

	resp = l.convertToWorkflowDetailResp(sallyWorkflow, sallyWorkflowProgress)
	return
}

func (l *GetWorkflowDetailLogic) convertToWorkflowDetailResp(sallyWorkflow addons.SallyGetWorkflowsResult, sallyWorkflowProgress addons.SallyWorkflowProgress) *types.WorkflowDetailResp {
	nodes := make([]types.WorkflowNodeInfo, 0)
	for _, node := range sallyWorkflowProgress.NodeProgresses {
		reviewers := make([]types.WorkflowNodeReviewerInfo, 0)
		status := node.Status
		if len(node.ReviewTasks) > 0 {
			for _, reviewer := range node.ReviewTasks {
				reviewers = append(reviewers, types.WorkflowNodeReviewerInfo{
					ApproverId:       reviewer.ReviewerID,
					ApproverNickname: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, reviewer.ReviewerID),
					Status:           reviewer.Status,
					UpdatedAt:        reviewer.UpdatedAt.UnixMilli(),
					Comment:          reviewer.Comment,
					Extra:            reviewer.Extra,
				})
			}

			// 最后一个节点改为撤回状态
			if sallyWorkflow.Status == addons.WorkflowStatusCancelled && len(reviewers) > 0 && reviewers[len(reviewers)-1].Status == addons.WorkflowStatusUnderReview {
				status = addons.WorkflowStatusCancelled
			}
		} else {
			status = addons.WorkflowStatusNotStarted
		}
		updatedAt := int64(0)
		if status != addons.WorkflowStatusNotStarted && status != addons.WorkflowStatusUnderReview {
			updatedAt = node.UpdatedAt.UnixMilli()
		}

		nodes = append(nodes, types.WorkflowNodeInfo{
			NodeName:    node.NodeTemplateName,
			Status:      status,
			NodeId:      node.NodeID,
			Approvers:   reviewers,
			SigningKind: node.ApprovalStrategy,
			UpdatedAt:   updatedAt,
		})
	}

	resp := &types.WorkflowDetailResp{
		FlowId:                  sallyWorkflow.ID,
		FlowName:                sallyWorkflow.Name,
		FlowStatus:              sallyWorkflow.Status,
		FlowCreatedUserNickname: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, sallyWorkflow.SponsorID),
		FlowCreatedTime:         sallyWorkflow.CreatedAt.UnixMilli(),
		FormContent:             sallyWorkflow.FormContent,
		Nodes:                   nodes,
	}
	return resp
}
