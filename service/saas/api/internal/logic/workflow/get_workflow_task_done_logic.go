package workflow

import (
	"context"
	"time"

	"phoenix/service/saas/adapter/addons"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/constant"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetWorkflowTaskDoneLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetWorkflowTaskDoneLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetWorkflowTaskDoneLogic {
	return &GetWorkflowTaskDoneLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetWorkflowTaskDoneLogic) GetWorkflowTaskDone(req *types.WorkflowTaskDoneReq) (resp *types.WorkflowTaskDoneResp, err error) {
	sallyReq := l.convertToSallyReq(req)

	workflows, total, err := l.svcCtx.SallyAddons.GetNodeReviewTasks(l.ctx, sallyReq)
	if err != nil {
		return nil, err
	}

	newWorkflows, err := l.convertToWorkflowTaskDoneResp(workflows)
	if err != nil {
		return nil, err
	}

	resp = &types.WorkflowTaskDoneResp{
		BaseListInfo: types.BaseListInfo{
			Total: uint64(total),
		},
		Data: newWorkflows,
	}
	return resp, nil
}

func (l *GetWorkflowTaskDoneLogic) convertToWorkflowTaskDoneResp(workflows []addons.SallyNodeReviewTask) ([]types.WorkflowTaskDoneInfo, error) {
	newWorkflows := make([]types.WorkflowTaskDoneInfo, 0, len(workflows))
	for _, workflow := range workflows {
		w, err := l.svcCtx.SallyAddons.GetWorkflow(l.ctx, workflow.WorkflowID)
		if err != nil {
			return nil, err
		}

		newWorkflows = append(newWorkflows, types.WorkflowTaskDoneInfo{
			FlowName:                workflow.WorkflowName,
			TaskCompletedTime:       workflow.CreatedAt.UnixMilli(),                                         // 任务完成时间
			FlowCreatedUserNickname: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, w.SponsorID), // 流程发起人
			FlowCreatedTime:         w.CreatedAt.UnixMilli(),
			FlowStatus:              w.Status,
			FlowId:                  workflow.WorkflowID,
		})
	}

	return newWorkflows, nil
}

func (l *GetWorkflowTaskDoneLogic) convertToSallyReq(req *types.WorkflowTaskDoneReq) addons.SallyGetNodeReviewTasksRequest {
	// 将 WorkflowTaskDoneReq 转换为 SallyGetNodeReviewTasksRequest
	sallyReq := addons.SallyGetNodeReviewTasksRequest{
		NoPage:            req.NoPage,
		PageNo:            int(req.Page),
		PageSize:          int(req.PageSize),
		ReviewerID:        utils.GetContextUserID(l.ctx),
		WorkflowName:      req.Search,
		WorkflowSponsorID: req.FlowCreatedUserId,
		FlowStatus:        req.FlowStatus,
		Statuses:          []string{addons.WorkflowStatusPassed, addons.WorkflowStatusRejected},
	}

	// 任务完成时间范围筛选
	if req.TaskCompletedTimeBegin != 0 {
		sallyReq.UpdatedAtStart = time.UnixMilli(req.TaskCompletedTimeBegin)
	}
	if req.TaskCompletedTimeEnd != 0 {
		sallyReq.UpdatedAtEnd = time.UnixMilli(req.TaskCompletedTimeEnd)
	}

	if req.FlowCreatedTimeBegin != 0 {
		sallyReq.WorkflowCreatedAtStart = time.UnixMilli(req.FlowCreatedTimeBegin)
	}
	if req.FlowCreatedTimeEnd != 0 {
		sallyReq.WorkflowCreatedAtEnd = time.UnixMilli(req.FlowCreatedTimeEnd)
	}

	if req.OrganizationId != "" {
		sallyReq.WorkflowBusinessParams = map[string]any{
			constant.WorkflowBusinessParamsOrganizationID: req.OrganizationId,
		}
	}

	return sallyReq
}
