package workflow

import (
	"context"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetTemplateListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetTemplateListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTemplateListLogic {
	return &GetTemplateListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetTemplateListLogic) GetTemplateList(req *types.TemplateListReq) (resp *types.TemplateListResp, err error) {
	templates, total, err := mapper.NewWorkflowTemplateMainClient(l.svcCtx.GormDB).Page(l.ctx, mapper.WorkflowTemplateMainPageParam{
		Page:           int(req.Page),
		PageSize:       int(req.PageSize),
		NoPage:         req.NoPage,
		OrganizationID: utils.GetContextOrganizationID(l.ctx),
		Search:         req.Search,
	})
	if err != nil {
		l.Logger.Errorf("Page error: %v", err)
		return nil, err
	}

	templateInfos, err := l.buid(templates)
	if err != nil {
		l.Logger.Errorf("buildTemplateInfos error: %v", err)
		return nil, err
	}

	resp = &types.TemplateListResp{
		BaseListInfo: types.BaseListInfo{
			Total: uint64(total),
		},
		Data: templateInfos,
	}

	return
}

func (l *GetTemplateListLogic) buid(templates []mapper.WorkflowTemplateMainPageItem) ([]types.TemplateListItemInfo, error) {
	// 构建
	templateInfos := make([]types.TemplateListItemInfo, len(templates))
	for i, template := range templates {

		templateInfos[i] = types.TemplateListItemInfo{
			ID:         template.ID,
			BusinessId: template.BusinessID,
			VersionID:  template.VersionID,
			Name:       template.Name,
			VersionNo:  template.VersionNo,
			TimeMixinInfo: types.TimeMixinInfo{
				CreatedAt: template.CreatedAt.UnixMilli(),
				UpdatedAt: template.UpdatedAt.UnixMilli(),
			},
			PersonMixinInfo: types.PersonMixinInfo{
				CreatedBy: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, template.CreatedBy),
				UpdatedBy: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, template.UpdatedBy),
			},
		}
	}
	return templateInfos, nil
}
