package workflow

import (
	"context"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetPresetTemplateListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetPresetTemplateListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPresetTemplateListLogic {
	return &GetPresetTemplateListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetPresetTemplateListLogic) GetPresetTemplateList(req *types.PresetTemplateListReq) (resp *types.PresetTemplateListResp, err error) {
	presetTemplates, total, err := mapper.NewWorkflowTemplatePresetClient(l.svcCtx.GormDB).Page(l.ctx, mapper.WorkflowTemplatePresetPageParam{
		Search:   req.Search,
		Page:     req.Page,
		PageSize: req.PageSize,
		NoPage:   req.NoPage,
		TenantID: utils.GetContextTenantID(l.ctx),
	})
	if err != nil {
		return nil, err
	}

	//提取用户 id
	return &types.PresetTemplateListResp{
		BaseListInfo: types.BaseListInfo{
			Total: uint64(total),
		},
		Data: l.buildPresetTemplateInfo(presetTemplates),
	}, nil
}

func (l *GetPresetTemplateListLogic) buildPresetTemplateInfo(presetTemplates []mapper.WorkflowTemplatePreset) []types.PresetTemplateInfo {

	items := make([]types.PresetTemplateInfo, len(presetTemplates))
	for i, presetTemplate := range presetTemplates {

		items[i] = types.PresetTemplateInfo{
			ID:         presetTemplate.ID,
			Name:       presetTemplate.Name,
			BusinessId: presetTemplate.BusinessID,
			Kind:       presetTemplate.Kind,
			TimeMixinInfo: types.TimeMixinInfo{
				CreatedAt: presetTemplate.CreatedAt.UnixMilli(),
				UpdatedAt: presetTemplate.UpdatedAt.UnixMilli(),
			},
			PersonMixinInfo: types.PersonMixinInfo{
				CreatedBy: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, presetTemplate.CreatedBy),
				UpdatedBy: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, presetTemplate.UpdatedBy),
			},
		}
	}
	return items
}
