package workflow

import (
	"context"
	"time"

	"phoenix/service/saas/adapter/addons"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/constant"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetWorkflowMonitorListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetWorkflowMonitorListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetWorkflowMonitorListLogic {
	return &GetWorkflowMonitorListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetWorkflowMonitorListLogic) GetWorkflowMonitorList(req *types.WorkflowMonitorListReq) (resp *types.WorkflowMonitorListResp, err error) {
	sallyReq := l.buildSallyReq(req)
	workflows, total, err := l.svcCtx.SallyAddons.GetWorkflows(l.ctx, sallyReq)
	if err != nil {
		l.Logger.Errorf("GetWorkflow error: %v", err)
		return nil, err
	}

	mapWorkflows, err := l.buildWorkflows(workflows)
	if err != nil {
		l.Logger.Errorf("BuildWorkflows error: %v", err)
		return nil, err
	}

	return &types.WorkflowMonitorListResp{
		BaseListInfo: types.BaseListInfo{
			Total: uint64(total),
		},
		Data: mapWorkflows,
	}, nil
}

func (l *GetWorkflowMonitorListLogic) buildSallyReq(req *types.WorkflowMonitorListReq) addons.SallyGetWorkflowsParams {
	sallyReq := addons.SallyGetWorkflowsParams{
		SallyPageCond: addons.SallyPageCond{
			PageNo:   int(req.Page),
			PageSize: int(req.PageSize),
		},
		SponsorID:      req.FlowCreatedUserId,
		WorkflowName:   req.Search,
		WorkflowStatus: req.FlowStatus,
	}

	if req.FlowCreatedTimeBegin != 0 {
		sallyReq.CreatedAtStart = time.UnixMilli(req.FlowCreatedTimeBegin)
	}
	if req.FlowCreatedTimeEnd != 0 {
		sallyReq.CreatedAtEnd = time.UnixMilli(req.FlowCreatedTimeEnd)
	}

	if req.FlowNodeApproverId != "" {
		sallyReq.CurrentNodeReviewerIDs = []string{req.FlowNodeApproverId}
	}
	return sallyReq
}

func (l *GetWorkflowMonitorListLogic) getNodeApproverAndMonitorStatus(workflow addons.SallyGetWorkflowsResult) (string, []types.WorkflowNodeReviewerInfo, string, error) {
	if workflow.Status != addons.WorkflowStatusUnderReview {
		return "", make([]types.WorkflowNodeReviewerInfo, 0), "", nil
	}

	// 取出超时时间
	timeout, ok := utils.ParseIntParam(workflow.BusinessParams, constant.WorkflowNodeBusinessParamsTimeout)
	if !ok {
		timeout = 0
	}

	monitorStatus := ""
	currentNodeApprover := make([]types.WorkflowNodeReviewerInfo, 0)
	if workflow.CurrentNodeID != "" {
		node, err := l.svcCtx.SallyAddons.GetNode(l.ctx, workflow.CurrentNodeID)
		if err != nil {
			return "", nil, "", err
		}

		if len(node.NodeReviewTasks) == 0 {
			monitorStatus = "notApprover"
		} else if timeout > 0 && node.CreatedAt.Before(time.Now().Add(-time.Duration(timeout)*time.Hour)) { // 超时时间大于0时才判断超时
			monitorStatus = "timeout"
		}

		for _, reviewer := range node.NodeReviewTasks {
			if reviewer.Status == addons.WorkflowStatusUnderReview {
				currentNodeApprover = append(currentNodeApprover, types.WorkflowNodeReviewerInfo{
					ApproverId:       reviewer.ReviewerID,
					ApproverNickname: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, reviewer.ReviewerID),
					Status:           reviewer.Status,
					UpdatedAt:        reviewer.UpdatedAt.UnixMilli(),
					Comment:          reviewer.Comment,
					TaskId:           reviewer.ID,
				})
			}
		}
	}

	return workflow.CurrentNodeID, currentNodeApprover, monitorStatus, nil
}

func (l *GetWorkflowMonitorListLogic) buildWorkflows(workflows []addons.SallyGetWorkflowsResult) ([]types.WorkflowMonitorListInfo, error) {
	mapWorkflows := make([]types.WorkflowMonitorListInfo, 0, len(workflows))
	for _, workflow := range workflows {

		currentNodeID, currentNodeApprover, monitorStatus, err := l.getNodeApproverAndMonitorStatus(workflow)
		if err != nil {
			return nil, err
		}

		w, err := l.svcCtx.SallyAddons.GetWorkflow(l.ctx, workflow.ID)
		if err != nil {
			return nil, err
		}

		mapWorkflows = append(mapWorkflows, types.WorkflowMonitorListInfo{
			FlowName:                workflow.Name,
			CurrentNodeName:         workflow.CurrentNodeName,
			CurrentNodeApprover:     currentNodeApprover,
			FlowCreatedTime:         workflow.CreatedAt.UnixMilli(), // 流程创建时间
			FlowStatus:              workflow.Status,
			WorkflowId:              workflow.ID,
			WarnStatus:              monitorStatus,
			CurrentNodeId:           currentNodeID,
			FlowCreatedUserNickname: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, w.SponsorID),
		})
	}
	return mapWorkflows, nil
}
