package workflow

import (
	"context"
	"errors"
	"slices"
	"time"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type BindPresetTemplateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewBindPresetTemplateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *BindPresetTemplateLogic {
	return &BindPresetTemplateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *BindPresetTemplateLogic) BindPresetTemplate(req *types.PresetTemplateBindReq) (resp *types.PresetTemplateBindResp, err error) {
	presetTemplate, err := mapper.NewWorkflowTemplatePresetClient(l.svcCtx.GormDB).GetByID(l.ctx, req.ID)
	if err != nil {
		return nil, err
	}

	// 查询已下发版本
	errn := l.checkRepeatOrganization(req)
	if errn != nil {
		return nil, errn
	}

	now := time.Now()
	// 事务保存
	err = l.svcCtx.GormDB.Transaction(func(tx *gorm.DB) error {
		versions, mains := l.build(req.OrganizationIds, presetTemplate, now)
		err = mapper.NewWorkflowTemplateVersionClient(l.svcCtx.GormDB).BatchCreateWithTx(l.ctx, tx, versions)
		if err != nil {
			return err
		}

		err = mapper.NewWorkflowTemplateMainClient(l.svcCtx.GormDB).BatchCreateWithTx(l.ctx, tx, mains)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		return nil, err
	}

	return &types.PresetTemplateBindResp{}, nil
}

func (l *BindPresetTemplateLogic) checkRepeatOrganization(req *types.PresetTemplateBindReq) (err error) {
	organizationIDs, err := mapper.NewWorkflowTemplateMainClient(l.svcCtx.GormDB).GetOrganizationIDsByPresetID(l.ctx, req.ID)
	if err != nil {
		return err
	}
	// 判断是否有重复的组织  交集判断
	for _, orgId := range req.OrganizationIds {
		if slices.Contains(organizationIDs, orgId) {
			return errors.New("重复下发组织")
		}
	}
	return nil
}

func (l *BindPresetTemplateLogic) build(orgIds []string, presetTemplate mapper.WorkflowTemplatePreset, now time.Time) ([]mapper.WorkflowTemplateVersion, []mapper.WorkflowTemplateMain) {
	versions := make([]mapper.WorkflowTemplateVersion, 0)
	mains := make([]mapper.WorkflowTemplateMain, 0)
	for _, orgId := range orgIds {
		version := l.buildWorkflowTemplateVersion(presetTemplate, now)
		main := l.buildWorkflowTemplateMain(presetTemplate, now, version.ID, orgId)
		version.MainID = main.ID
		mains = append(mains, main)
		versions = append(versions, version)
	}
	return versions, mains
}

func (l *BindPresetTemplateLogic) buildWorkflowTemplateVersion(presetTemplate mapper.WorkflowTemplatePreset, now time.Time) mapper.WorkflowTemplateVersion {
	return mapper.WorkflowTemplateVersion{
		ID:                l.svcCtx.IDGenerator.GenerateIDString(),
		Name:              presetTemplate.Name,
		VersionNo:         1,
		Kind:              mapper.WorkflowTemplateVersionKindCustom,
		TimeoutWarnStatus: true,
		TimeoutHour:       0,
		SallyTemplateID:   "",
		AutoApproveKind:   mapper.WorkflowTemplateVersionAutoApproveKindClose,
		CreatedBy:         utils.GetContextUserID(l.ctx),
		CreatedAt:         now,
	}
}

func (l *BindPresetTemplateLogic) buildWorkflowTemplateMain(presetTemplate mapper.WorkflowTemplatePreset, now time.Time, versionID string, orgId string) mapper.WorkflowTemplateMain {
	return mapper.WorkflowTemplateMain{
		ID:             l.svcCtx.IDGenerator.GenerateIDString(),
		PresetID:       presetTemplate.ID,
		VersionID:      versionID,
		OrganizationID: orgId,
		CreatedBy:      utils.GetContextUserID(l.ctx),
		UpdatedBy:      utils.GetContextUserID(l.ctx),
		CreatedAt:      now,
		UpdatedAt:      now,
	}
}
