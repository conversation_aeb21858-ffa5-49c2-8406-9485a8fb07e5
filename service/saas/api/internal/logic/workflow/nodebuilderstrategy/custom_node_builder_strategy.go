package nodebuilderstrategy

import (
	"context"
	"errors"

	"phoenix/service/saas/adapter/addons"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/constant"
	"phoenix/service/saas/model/gorm/mapper"
)

// CustomNodeBuilderStrategy 实现自定义节点构建逻辑
// 注册 key: constant.WorkflowTemplateNodeKindCustom

type CustomNodeBuilderStrategy struct {
	svcCtx *svc.ServiceContext
}

func (s *CustomNodeBuilderStrategy) BuildNodes(ctx context.Context, req *types.WorkflowStartReq, version mapper.WorkflowTemplateVersion) ([]addons.NodeTemplateConfig, error) {
	nodes, err := mapper.NewWorkflowTemplateNodeClient(s.svcCtx.GormDB).GetByVersionID(ctx, version.ID)
	if err != nil {
		return nil, err
	}
	templateNodes := make([]addons.NodeTemplateConfig, len(nodes))
	for i, tmplNode := range nodes {
		approverIds, ccIds, err := s.getNodeReviewerAndCCIds(ctx, tmplNode)
		if err != nil {
			return nil, err
		}
		// 组装节点审批人
		approverIds = append(approverIds, s.getReviewerIdsForCustomNode(tmplNode, req)...)
		if len(approverIds) == 0 {
			return nil, errors.New("审批人不能为空")
		}
		kind := s.convertApproverKindToSallyReviewerKind(tmplNode.ApprovalKind)
		templateNodes[i] = s.buildNodeTemplateConfig(tmplNode.Name, kind, approverIds, tmplNode.SigningKind, tmplNode.CCKind, ccIds)
	}
	return templateNodes, nil
}

// getNodeReviewerAndCCIds 获取节点审批人和抄送人
func (s *CustomNodeBuilderStrategy) getNodeReviewerAndCCIds(ctx context.Context, tmplNode mapper.WorkflowTemplateNode) ([]string, []string, error) {
	if tmplNode.ApprovalKind == constant.WorkflowTemplateNodeReviewerKindCustom {
		return make([]string, 0), make([]string, 0), nil
	}
	reviewerClient := mapper.NewWorkflowTemplateNodeReviewClient(s.svcCtx.GormDB)
	reviewers, err := reviewerClient.GetByNodeID(ctx, tmplNode.ID)
	if err != nil {
		return nil, nil, err
	}
	var reviewerIds []string
	var ccIds []string
	for _, reviewer := range reviewers {
		if reviewer.Kind == 1 {
			reviewerIds = append(reviewerIds, reviewer.ReviewID)
		} else if reviewer.Kind == 2 {
			ccIds = append(ccIds, reviewer.ReviewID)
		}
	}
	return reviewerIds, ccIds, nil
}

// getReviewerIdsForCustomNode 获取自定义节点的审批人
func (s *CustomNodeBuilderStrategy) getReviewerIdsForCustomNode(tmplNode mapper.WorkflowTemplateNode, req *types.WorkflowStartReq) []string {
	if tmplNode.ApprovalKind != constant.WorkflowTemplateNodeReviewerKindCustom {
		return make([]string, 0)
	}
	for _, n := range req.Nodes {
		if n.NodeId == tmplNode.ID {
			return n.ApproverIds
		}
	}
	return nil
}

// convertApproverKindToSallyReviewerKind 工具函数，string->int 映射
func (s *CustomNodeBuilderStrategy) convertApproverKindToSallyReviewerKind(kind string) int {
	switch kind {
	case constant.WorkflowTemplateNodeReviewerKindCustom, constant.WorkflowTemplateNodeReviewerKindDesignate:
		return 1
	case constant.WorkflowTemplateNodeReviewerKindPosition:
		return 2
	case constant.WorkflowTemplateNodeReviewerKindRole:
		return 3
	default:
		return 0
	}
}

// buildNodeTemplateConfig 工具函数，统一节点组装
func (s *CustomNodeBuilderStrategy) buildNodeTemplateConfig(name string, reviewerKind int, reviewerIds []string, signingKind, ccKind string, ccIds []string) addons.NodeTemplateConfig {
	c := addons.NodeTemplateConfig{
		NodeTemplateName: name,
		NodeTemplateKind: 1, // 审批类型节点普通
		ReviewerConfig: addons.NodeTemplateReviewerConfig{
			Kind:             reviewerKind,
			ReviewerNos:      reviewerIds,
			ApprovalStrategy: signingKind,
		},
	}
	if len(ccIds) > 0 {
		c.BusinessParams = map[string]any{
			constant.WorkflowNodeBusinessParamsCCIds:  ccIds,
			constant.WorkflowNodeBusinessParamsCCKind: ccKind,
		}
	}
	return c
}

func NewCustomNodeBuilderStrategy(svcCtx *svc.ServiceContext) NodeBuilderStrategy {
	return &CustomNodeBuilderStrategy{svcCtx: svcCtx}
}
