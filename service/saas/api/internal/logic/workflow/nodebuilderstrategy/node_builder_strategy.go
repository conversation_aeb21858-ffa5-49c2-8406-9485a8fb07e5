package nodebuilderstrategy

import (
	"context"

	"phoenix/service/saas/adapter/addons"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/constant"
	"phoenix/service/saas/model/gorm/mapper"
)

// NodeBuilderStrategy 节点构建策略接口
// 未来如需扩展其他节点类型，实现该接口并注册即可
// BuildNodes 参数设计为通用，便于扩展
// version/nodes 可为 nil，具体策略自定义处理
type NodeBuilderStrategy interface {
	BuildNodes(ctx context.Context, req *types.WorkflowStartReq, version mapper.WorkflowTemplateVersion) ([]addons.NodeTemplateConfig, error)
}

func NewNodeBuilderStrategy(svcCtx *svc.ServiceContext, kind string) NodeBuilderStrategy {
	switch kind {
	case constant.WorkflowTemplateNodeKindCustom:
		return NewCustomNodeBuilderStrategy(svcCtx)
	case constant.WorkflowTemplateNodeKindDepartment:
		return NewDepartmentNodeBuilderStrategy(svcCtx)
	default:
		return nil
	}
}
