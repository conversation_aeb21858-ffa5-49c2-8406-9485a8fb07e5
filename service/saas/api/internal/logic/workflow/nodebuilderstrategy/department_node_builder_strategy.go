package nodebuilderstrategy

import (
	"context"
	"errors"

	"phoenix/service/saas/adapter/addons"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/constant"
	"phoenix/service/saas/model/gorm/mapper"
)

// DepartmentNodeBuilderStrategy 实现部门节点构建逻辑
// 注册 key: constant.WorkflowTemplateNodeKindDepartment

type DepartmentNodeBuilderStrategy struct {
	svcCtx *svc.ServiceContext
}

func NewDepartmentNodeBuilderStrategy(svcCtx *svc.ServiceContext) NodeBuilderStrategy {
	return &DepartmentNodeBuilderStrategy{svcCtx: svcCtx}
}

func (s *DepartmentNodeBuilderStrategy) BuildNodes(ctx context.Context, req *types.WorkflowStartReq, version mapper.WorkflowTemplateVersion) ([]addons.NodeTemplateConfig, error) {
	if req.DepartmentId == "" {
		return nil, errors.New("部门类型流程缺少 DepartmentId")
	}
	// 查询部门模板信息
	deptClient := mapper.NewWorkflowTemplateDepartmentClient(s.svcCtx.GormDB)
	deptNode, err := deptClient.GetByVersionID(ctx, version.ID)
	if err != nil {
		return nil, err
	}
	// 查询所有祖先部门
	closureClient := mapper.NewOrganizationClosureClient(s.svcCtx.GormDB)
	ancestors, err := closureClient.GetAncestors(ctx, req.DepartmentId)
	if err != nil {
		return nil, err
	}
	var templateNodes []addons.NodeTemplateConfig
	// 到公司或者集团后停止
	isStop := false
	for i, ancestor := range ancestors {
		if isStop {
			break
		}
		nodeName := s.svcCtx.QuickNameTranslator.TranslateOrganizationName(ctx, ancestor.AncestorID) // 需通过 context 注入的 QuickNameTranslator
		templateNodes = append(templateNodes, s.buildNodeTemplateConfig(nodeName, 5, []string{ancestor.AncestorID}, deptNode.SigningKind, "", nil))
		if ancestor.AncestorNodeType == constant.OrganizationNodeTypeCompany || i >= deptNode.Level-1 {
			ccIds := []string{} // 需通过 context 注入的 getDepartmentCCIds
			if len(ccIds) > 0 {
				templateNodes[i].BusinessParams = map[string]any{
					constant.WorkflowNodeBusinessParamsCCIds:  ccIds,
					constant.WorkflowNodeBusinessParamsCCKind: deptNode.CCKind,
				}
			}
		}
		// 防止从公司部门查到集团
		if ancestor.AncestorNodeType == constant.OrganizationNodeTypeCompany {
			isStop = true
		}
	}
	if len(templateNodes) == 0 {
		return nil, errors.New("未能生成有效的部门审批节点")
	}
	return templateNodes, nil
}

// buildNodeTemplateConfig 工具函数，统一节点组装
func (s *DepartmentNodeBuilderStrategy) buildNodeTemplateConfig(name string, reviewerKind int, reviewerIds []string, signingKind, ccKind string, ccIds []string) addons.NodeTemplateConfig {
	c := addons.NodeTemplateConfig{
		NodeTemplateName: name,
		NodeTemplateKind: 1, // 审批类型节点普通
		ReviewerConfig: addons.NodeTemplateReviewerConfig{
			Kind:             reviewerKind,
			ReviewerNos:      reviewerIds,
			ApprovalStrategy: signingKind,
		},
	}
	if len(ccIds) > 0 {
		c.BusinessParams = map[string]any{
			constant.WorkflowNodeBusinessParamsCCIds:  ccIds,
			constant.WorkflowNodeBusinessParamsCCKind: ccKind,
		}
	}
	return c
}
