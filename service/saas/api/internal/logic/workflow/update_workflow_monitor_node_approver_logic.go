package workflow

import (
	"context"

	"phoenix/service/saas/adapter/addons"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateWorkflowMonitorNodeApproverLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateWorkflowMonitorNodeApproverLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateWorkflowMonitorNodeApproverLogic {
	return &UpdateWorkflowMonitorNodeApproverLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateWorkflowMonitorNodeApproverLogic) UpdateWorkflowMonitorNodeApprover(req *types.WorkflowMonitorNodeApproverUpdateReq) (resp *types.WorkflowMonitorNodeApproverUpdateResp, err error) {
	err = l.svcCtx.SallyAddons.ChangeNodeReviewer(l.ctx, addons.SallyChangeNodeReviewerRequest{
		TaskID:        req.TaskId,
		NewReviewerID: req.ApproverId,
	})
	if err != nil {
		l.Logger.Errorf("UpdateNodeApprover error: %v", err)
		return nil, err
	}

	return
}
