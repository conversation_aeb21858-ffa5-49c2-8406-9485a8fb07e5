package workflow

import (
	"context"

	"phoenix/service/saas/adapter/addons"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddWorkflowMonitorNodeApproverLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddWorkflowMonitorNodeApproverLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddWorkflowMonitorNodeApproverLogic {
	return &AddWorkflowMonitorNodeApproverLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddWorkflowMonitorNodeApproverLogic) AddWorkflowMonitorNodeApprover(req *types.WorkflowMonitorNodeApproverAddReq) (resp *types.WorkflowMonitorNodeApproverAddResp, err error) {
	err = l.svcCtx.SallyAddons.AddNodeReviewer(l.ctx, addons.SallyAddNodeReviewerRequest{
		NodeID:     req.NodeId,
		ReviewerID: req.ApproverId,
	})
	if err != nil {
		l.Logger.Errorf("AddNodeReviewer error: %v", err)
		return nil, err
	}
	return
}
