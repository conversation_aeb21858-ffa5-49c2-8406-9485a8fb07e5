package workflow

import (
	"context"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CancelWorkflowLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCancelWorkflowLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CancelWorkflowLogic {
	return &CancelWorkflowLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CancelWorkflowLogic) CancelWorkflow(req *types.WorkflowCancelReq) (resp *types.WorkflowCancelResp, err error) {
	err = l.svcCtx.SallyAddons.CancelWorkflow(l.ctx, req.FlowId)
	if err != nil {
		l.Logger.Errorf("CancelWorkflow error: %v", err)
		return nil, err
	}
	return
}
