package workflow

import (
	"context"
	"errors"

	"phoenix/service/saas/api/internal/errs"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type GetTemplateDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetTemplateDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTemplateDetailLogic {
	return &GetTemplateDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetTemplateDetailLogic) GetTemplateDetail(req *types.TemplateDetailReq) (resp *types.TemplateDetailResp, err error) {
	if req.VersionID == "" && req.BusinessId == "" {
		return nil, errors.New("versionId or businessId is required")
	}
	versionId := req.VersionID
	if versionId == "" {
		main, err := mapper.NewWorkflowTemplateMainClient(l.svcCtx.GormDB).GetByBusinessIDAndOrganizationID(l.ctx, req.BusinessId, utils.GetContextOrganizationID(l.ctx))
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, errs.NotFound
			}
			l.Logger.Errorf("Get error: %v", err)
			return nil, err
		}
		versionId = main.VersionID
	}

	version, err := mapper.NewWorkflowTemplateVersionClient(l.svcCtx.GormDB).GetByID(l.ctx, versionId)
	if err != nil {
		l.Logger.Errorf("Get error: %v", err)
		return nil, err
	}

	main, err := mapper.NewWorkflowTemplateMainClient(l.svcCtx.GormDB).GetByID(l.ctx, version.MainID)
	if err != nil {
		l.Logger.Errorf("Get error: %v", err)
		return nil, err
	}

	preset, err := mapper.NewWorkflowTemplatePresetClient(l.svcCtx.GormDB).GetByID(l.ctx, main.PresetID)
	if err != nil {
		l.Logger.Errorf("Get error: %v", err)
		return nil, err
	}

	resp = &types.TemplateDetailResp{
		VersionId:         version.ID,
		MainID:            main.ID,
		Name:              version.Name,
		Kind:              version.Kind,
		PresetKind:        preset.Kind,
		AutoApproveKind:   version.AutoApproveKind,
		TimeoutWarnStatus: version.TimeoutWarnStatus,
		TimeoutHour:       version.TimeoutHour,
		BusinessId:        preset.BusinessID,
		Custom:            make([]types.CustomWorkflowNodeInfo, 0),
	}

	if version.Kind == mapper.WorkflowTemplateVersionKindCustom {
		customNodeInfos, err := l.buildCustom(version.ID)
		if err != nil {
			l.Logger.Errorf("Get error: %v", err)
			return nil, err
		}
		resp.Custom = customNodeInfos
	} else if version.Kind == mapper.WorkflowTemplateVersionKindDepartment {
		departmentNodeInfo, err := l.buildDepartment(version.ID, preset)
		if err != nil {
			l.Logger.Errorf("Get error: %v", err)
			return nil, err
		}
		resp.Department = departmentNodeInfo
	}

	return
}

func (l *GetTemplateDetailLogic) buildCustom(versionID string) (customNodeInfos []types.CustomWorkflowNodeInfo, err error) {
	// 获取自定义节点
	customNodes, err := mapper.NewWorkflowTemplateNodeClient(l.svcCtx.GormDB).GetByVersionID(l.ctx, versionID)
	if err != nil {
		l.Logger.Errorf("Get error: %v", err)
		return nil, err
	}

	// 翻译节点类型
	for _, node := range customNodes {
		// 查询审批人和抄送人
		approvers, ccs, err := l.getNodeReviews(node.ID)
		if err != nil {
			l.Logger.Errorf("Get error: %v", err)
			return nil, err
		}

		customNodeInfos = append(customNodeInfos, types.CustomWorkflowNodeInfo{
			NodeID:       node.ID,
			NodeName:     node.Name,
			NodeKind:     node.Kind,
			SigningKind:  node.SigningKind,
			CCKind:       node.CCKind,
			ApprovalKind: node.ApprovalKind,
			ApproverIds:  approvers,
			CCIds:        ccs,
		})
	}

	return
}

func (l *GetTemplateDetailLogic) getNodeReviews(nodeID string) (approvers []string, ccs []string, err error) {
	reviews, err := mapper.NewWorkflowTemplateNodeReviewClient(l.svcCtx.GormDB).GetByNodeID(l.ctx, nodeID)
	if err != nil {
		l.Logger.Errorf("Get error: %v", err)
		return nil, nil, err
	}
	// 分开审批人和抄送人
	for _, review := range reviews {
		if review.Kind == 1 {
			approvers = append(approvers, review.ReviewID)
		} else if review.Kind == 2 {
			ccs = append(ccs, review.ReviewID)
		}
	}

	return approvers, ccs, nil
}

func (l *GetTemplateDetailLogic) buildDepartment(versionID string, _ mapper.WorkflowTemplatePreset) (departmentNodeInfo *types.DepartmentWorkflowNodeInfo, err error) {

	// 查询部门
	department, err := mapper.NewWorkflowTemplateDepartmentClient(l.svcCtx.GormDB).GetByVersionID(l.ctx, versionID)
	if err != nil {
		l.Logger.Errorf("Get error: %v", err)
		return departmentNodeInfo, err
	}

	// 查询抄送人
	ccReviews, err := mapper.NewWorkflowTemplateCCReviewClient(l.svcCtx.GormDB).GetByVersionID(l.ctx, versionID)
	if err != nil {
		l.Logger.Errorf("Get error: %v", err)
		return departmentNodeInfo, err
	}

	// 翻译抄送人
	ccIds := make([]string, len(ccReviews))
	for i, ccReview := range ccReviews {
		ccIds[i] = ccReview.ReviewID
	}

	return &types.DepartmentWorkflowNodeInfo{
		Level:       department.Level,
		NodeKind:    department.NodeKind,
		SigningKind: department.SigningKind,
		CCKind:      department.CCKind,
		CCIds:       ccIds,
	}, nil
}
