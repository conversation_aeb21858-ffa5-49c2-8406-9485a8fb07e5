package workflow

import (
	"context"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/gorm/mapper"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetPresetTemplateDetailLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetPresetTemplateDetailLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetPresetTemplateDetailLogic {
	return &GetPresetTemplateDetailLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetPresetTemplateDetailLogic) GetPresetTemplateDetail(req *types.PresetTemplateDetailReq) (resp *types.PresetTemplateDetailResp, err error) {
	presetTemplate, err := mapper.NewWorkflowTemplatePresetClient(l.svcCtx.GormDB).GetByID(l.ctx, req.ID)
	if err != nil {
		return nil, err
	}

	businesses, err := mapper.NewWorkflowTemplateMainClient(l.svcCtx.GormDB).GetWorkflowTemplateMainsByPresetID(l.ctx, presetTemplate.ID)
	if err != nil {
		return nil, err
	}

	organizationIds := make([]string, 0, len(businesses))
	for _, business := range businesses {
		organizationIds = append(organizationIds, business.OrganizationID)
	}

	return &types.PresetTemplateDetailResp{
		PresetTemplateInfo: types.PresetTemplateInfo{
			ID:         presetTemplate.ID,
			Name:       presetTemplate.Name,
			BusinessId: presetTemplate.BusinessID,
			Kind:       presetTemplate.Kind,
			TimeMixinInfo: types.TimeMixinInfo{
				CreatedAt: presetTemplate.CreatedAt.UnixMilli(),
				UpdatedAt: presetTemplate.UpdatedAt.UnixMilli(),
			},
			PersonMixinInfo: types.PersonMixinInfo{
				CreatedBy: presetTemplate.CreatedBy,
				UpdatedBy: presetTemplate.UpdatedBy,
			},
			OrganizationIds: organizationIds,
		},
	}, nil
}
