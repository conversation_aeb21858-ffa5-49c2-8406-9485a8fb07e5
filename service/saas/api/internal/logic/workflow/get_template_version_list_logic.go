package workflow

import (
	"context"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/gorm/mapper"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetTemplateVersionListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetTemplateVersionListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTemplateVersionListLogic {
	return &GetTemplateVersionListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetTemplateVersionListLogic) GetTemplateVersionList(req *types.TemplateVersionListReq) (resp *types.TemplateVersionListResp, err error) {
	main, err := mapper.NewWorkflowTemplateMainClient(l.svcCtx.GormDB).GetByID(l.ctx, req.ID)
	if err != nil {
		l.Logger.<PERSON>rrorf("Get error: %v", err)
		return nil, err
	}

	versions, total, err := mapper.NewWorkflowTemplateVersionClient(l.svcCtx.GormDB).Page(l.ctx, mapper.WorkflowTemplateVersionPageParam{
		MainID:   req.ID,
		Page:     int(req.Page),
		PageSize: int(req.PageSize),
		NoPage:   req.NoPage,
	})
	if err != nil {
		l.Logger.Errorf("Page error: %v", err)
		return nil, err
	}

	// 构建
	versionInfos, err := l.buildVersionInfos(versions, main.VersionID)
	if err != nil {
		return nil, err
	}

	resp = &types.TemplateVersionListResp{
		BaseListInfo: types.BaseListInfo{
			Total: uint64(total),
		},
		Data: versionInfos,
	}

	return
}

func (l *GetTemplateVersionListLogic) buildVersionInfos(versions []mapper.WorkflowTemplateVersion, currentVersionID string) ([]types.TemplateVersionInfo, error) {
	if len(versions) == 0 {
		return make([]types.TemplateVersionInfo, 0), nil
	}

	versionInfos := make([]types.TemplateVersionInfo, len(versions))
	for i, version := range versions {

		versionInfos[i] = types.TemplateVersionInfo{
			ID:        version.ID,
			VersionNo: version.VersionNo,
			TimeMixinInfo: types.TimeMixinInfo{
				CreatedAt: version.CreatedAt.UnixMilli(),
			},
			PersonMixinInfo: types.PersonMixinInfo{
				CreatedBy: l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, version.CreatedBy),
			},
		}
		if version.ID == currentVersionID {
			// 当前版本
			versionInfos[i].Status = true
		}
	}
	return versionInfos, nil
}
