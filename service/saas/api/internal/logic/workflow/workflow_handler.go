package workflow

import (
	"context"
	"encoding/json"
	"fmt"
	"phoenix/service/saas/adapter/addons"
	"phoenix/service/saas/adapter/kqs"
	"phoenix/service/saas/api/internal/config"
	"phoenix/service/saas/constant"
	"phoenix/service/saas/model/ent"
	"phoenix/service/saas/model/ent/position"
	"phoenix/service/saas/model/ent/role"
	"phoenix/service/saas/model/ent/schema"
	"phoenix/service/saas/model/ent/user"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/gorm"
)

// 本文件已采用单一职责分离与参数解析工具化优化，提升可维护性与扩展性。

type WorkflowEventHandler struct {
	db       *gorm.DB
	ent      *ent.Client
	idgen    addons.IDGeneratorAddons
	producer *kqs.KafkaProducer
	config   config.Config
}

func NewWorkflowEventHandler(db *gorm.DB, ent *ent.Client, idgen addons.IDGeneratorAddons, producer *kqs.KafkaProducer, config config.Config) *WorkflowEventHandler {
	return &WorkflowEventHandler{
		db:       db,
		ent:      ent,
		idgen:    idgen,
		producer: producer,
		config:   config,
	}
}

type WorkflowEventMessage struct {
	TenantID       string `json:"tenant_id"`
	OrganizationID string `json:"organization_id"`
	WorkflowID     string `json:"workflow_id"`
	SponsorID      string `json:"sponsor_id"`
	FormContent    string `json:"form_content"`
	CompletedAt    int64  `json:"completed_at"`
	CreatedAt      int64  `json:"created_at"`
	BusinessID     string `json:"business_id"`
	BusinessCode   string `json:"business_code"`
	EventType      string `json:"event_type"` // 事件类型
}

type WorkflowRejectedMessage struct {
	TenantID       string `json:"tenant_id"`
	OrganizationID string `json:"organization_id"`
	WorkflowID     string `json:"workflow_id"`
	SponsorID      string `json:"sponsor_id"`
	FormContent    string `json:"form_content"`
	BusinessID     string `json:"business_id"`
	BusinessCode   string `json:"business_code"`
	CompletedAt    int64  `json:"completed_at"`
	RejectedBy     string `json:"rejected_by"`     // 驳回人
	RejectedReason string `json:"rejected_reason"` // 驳回原因
}

func (h *WorkflowEventHandler) sendWorkflowEvent(ctx context.Context, querier addons.WorkflowQuerier, event addons.WorkflowEvent, eventType string) error {
	logc.Infof(ctx, "sendWorkflowEvent: %+v", event)
	// 查询流程信息
	workflow, err := querier.GetWorkflow(ctx, event.WorkflowID)
	if err != nil {
		return err
	}

	// 取出业务参数中的租户ID
	tenantID, ok := utils.ParseStringParam(workflow.BusinessParams, constant.WorkflowBusinessParamsTenantID)
	if !ok || tenantID == "" {
		return nil
	}

	// 取出业务参数中的组织ID
	organizationID, ok := utils.ParseStringParam(workflow.BusinessParams, constant.WorkflowBusinessParamsOrganizationID)
	if !ok || organizationID == "" {
		return nil
	}

	message := WorkflowEventMessage{
		TenantID:       tenantID,
		OrganizationID: organizationID,
		WorkflowID:     workflow.ID,
		BusinessCode:   workflow.BusinessCode,
		SponsorID:      workflow.SponsorID,
		FormContent:    workflow.FormContent,
		CompletedAt:    workflow.UpdatedAt.UnixMilli(),
		BusinessID:     workflow.BusinessID,
		CreatedAt:      workflow.CreatedAt.UnixMilli(),
		EventType:      eventType,
	}

	jsonMessage, _ := json.Marshal(message)
	// HACK: 由于环境问题，直接硬编码 topic
	topic := fmt.Sprintf(h.config.WorkflowConf.FlowEventPushTopic, message.BusinessID)
	err = h.producer.SendMessage(ctx, topic, []byte(workflow.BusinessID), jsonMessage, nil)
	if err != nil {
		logc.Errorf(ctx, "推送工作流事件消息失败, eventType: %s, err: %v", eventType, err)
		return nil
	}

	return nil
}

func (h *WorkflowEventHandler) HandleWorkflowPassed(ctx context.Context, querier addons.WorkflowQuerier, event addons.WorkflowEvent) error {
	return h.sendWorkflowEvent(ctx, querier, event, constant.WorkflowEventPassed)
}

func (h *WorkflowEventHandler) HandleWorkflowRejected(ctx context.Context, querier addons.WorkflowQuerier, event addons.WorkflowEvent) error {
	return h.sendWorkflowEvent(ctx, querier, event, constant.WorkflowEventRejected)
}

// 撤销事件
func (h *WorkflowEventHandler) HandleWorkflowCanceled(ctx context.Context, querier addons.WorkflowQuerier, event addons.WorkflowEvent) error {
	return h.sendWorkflowEvent(ctx, querier, event, constant.WorkflowEventCanceled)
}

func (h *WorkflowEventHandler) getRejectedBy(ctx context.Context, querier addons.WorkflowQuerier, event addons.WorkflowEvent) (string, error) {
	node, err := querier.GetNode(ctx, event.NodeID)
	if err != nil {
		return "", err
	}
	if len(node.NodeReviewTasks) > 0 {
		for _, reviewTask := range node.NodeReviewTasks {
			if reviewTask.Status == addons.WorkflowStatusRejected {
				return reviewTask.ReviewerID, nil
			}
		}
	}
	return "", nil
}

func (h *WorkflowEventHandler) HandleWorkflowNodePassed(ctx context.Context, querier addons.WorkflowQuerier, event addons.WorkflowEvent) error {
	// 查询流程信息
	workflow, err := querier.GetWorkflow(ctx, event.WorkflowID)
	if err != nil {
		return err
	}

	// 查询当前节点信息
	node, err := querier.GetNode(ctx, event.NodeID)
	if err != nil {
		return err
	}

	err = h.createCCs(ctx, workflow, node)
	if err != nil {
		logc.Errorf(ctx, "HandleWorkflowNodePassed: 创建抄送记录失败: %v", err)
		return err
	}

	return nil
}

func (h *WorkflowEventHandler) HandleWorkflowStartSuccess(ctx context.Context, querier addons.WorkflowQuerier, event addons.WorkflowEvent) error {
	return h.sendWorkflowEvent(ctx, querier, event, constant.WorkflowEventStartSuccess)
}

func (h *WorkflowEventHandler) HandleWorkflowStartFailed(ctx context.Context, querier addons.WorkflowQuerier, event addons.WorkflowEvent) error {
	return h.sendWorkflowEvent(ctx, querier, event, constant.WorkflowEventStartFailed)
}

// createCCs 协调抄送记录创建流程，依次参数解析、用户ID查询、记录构造与保存
func (h *WorkflowEventHandler) createCCs(ctx context.Context, workflow addons.SallyGetWorkflowsResult, node addons.SallyActivatedNode) error {
	ccKind, ccIDs, err := h.parseCCParams(node)
	if err != nil {
		return nil
	}
	if ccKind == "" || len(ccIDs) == 0 {
		return nil
	}
	userIDs, err := h.queryCCUserIDs(ctx, ccKind, ccIDs)
	if err != nil {
		logc.Errorf(ctx, "查询抄送人失败: %v", err)
		return nil
	}
	if len(userIDs) == 0 {
		return nil
	}
	ccs, err := h.buildCCRecords(workflow, node, userIDs)
	if err != nil {
		logc.Errorf(ctx, "构造抄送记录失败: %v", err)
		return err
	}
	err = h.saveCCRecords(ctx, ccs)
	if err != nil {
		logc.Errorf(ctx, "创建抄送记录失败: %v", err)
		return err
	}
	return nil
}

// parseCCParams 解析节点业务参数，提取抄送类型和ID列表
func (h *WorkflowEventHandler) parseCCParams(node addons.SallyActivatedNode) (ccKind string, ccIDs []string, err error) {
	if len(node.NodeTemplateBusinessParams) == 0 {
		return "", nil, nil
	}
	ccKindStr, ok := utils.ParseStringParam(node.NodeTemplateBusinessParams, constant.WorkflowNodeBusinessParamsCCKind)
	if !ok || ccKindStr == "" {
		return "", nil, nil
	}

	ccIDsStr, ok := utils.ParseStringSliceParam(node.NodeTemplateBusinessParams, constant.WorkflowNodeBusinessParamsCCIds)
	if !ok || len(ccIDsStr) == 0 {
		return ccKindStr, nil, nil
	}
	return ccKindStr, ccIDsStr, nil
}

// queryCCUserIDs 根据抄送类型查找实际用户ID
func (h *WorkflowEventHandler) queryCCUserIDs(ctx context.Context, ccKind string, ccIDs []string) ([]string, error) {
	switch ccKind {
	case constant.WorkflowTemplateNodeReviewerKindPosition:
		return h.ent.User.Query().Where(user.HasPositionsWith(position.IDIn(ccIDs...))).IDs(schema.SkipTenantInject(schema.SkipSoftDelete(ctx)))
	case constant.WorkflowTemplateNodeReviewerKindRole:
		return h.ent.User.Query().Where(user.HasRolesWith(role.IDIn(ccIDs...))).IDs(schema.SkipTenantInject(schema.SkipSoftDelete(ctx)))
	default:
		// 直接为用户ID
		return ccIDs, nil
	}
}

// buildCCRecords 构造抄送记录实体
func (h *WorkflowEventHandler) buildCCRecords(workflow addons.SallyGetWorkflowsResult, node addons.SallyActivatedNode, userIDs []string) ([]mapper.WorkflowCC, error) {
	ccs := make([]mapper.WorkflowCC, 0, len(userIDs))
	tenantID, _ := workflow.BusinessParams[constant.WorkflowBusinessParamsTenantID].(string)
	organizationID, _ := workflow.BusinessParams[constant.WorkflowBusinessParamsOrganizationID].(string)
	versionID, _ := workflow.BusinessParams[constant.WorkflowBusinessParamsWorkflowTemplateVersionID].(string)
	for _, userID := range userIDs {
		cc := mapper.WorkflowCC{
			ID:              h.idgen.GenerateIDString(),
			SallyWorkflowID: workflow.ID,
			VersionID:       versionID,
			UserID:          userID,
			ConsultStatus:   false,
			TenantID:        tenantID,
			OrganizationID:  organizationID,
			CreatedBy:       workflow.SponsorID,
			UpdatedBy:       workflow.SponsorID,
			CreatedAt:       node.UpdatedAt,
			UpdatedAt:       node.UpdatedAt,
		}
		ccs = append(ccs, cc)
	}
	return ccs, nil
}

// saveCCRecords 批量保存抄送记录
func (h *WorkflowEventHandler) saveCCRecords(ctx context.Context, ccs []mapper.WorkflowCC) error {
	if len(ccs) == 0 {
		return nil
	}
	return mapper.NewWorkflowCCClient(h.db).BatchCreate(ctx, ccs)
}
