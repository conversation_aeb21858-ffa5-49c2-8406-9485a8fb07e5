package workflow

import (
	"context"
	"errors"
	"time"

	"phoenix/service/saas/adapter/addons"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/constant"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"gorm.io/gorm"
)

type UpdateTemplateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateTemplateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateTemplateLogic {
	return &UpdateTemplateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateTemplateLogic) UpdateTemplate(req *types.UpdateTemplateReq) (resp *types.UpdateTemplateResp, err error) {
	// 先判断是部门还是自定义
	if req.Kind == constant.WorkflowTemplateNodeKindDepartment {
		err = l.updateDepartmentTemplate(req)
		if err != nil {
			return nil, err
		}
	} else if req.Kind == constant.WorkflowTemplateNodeKindCustom {
		err = l.updateCustomTemplate(req)
		if err != nil {
			return nil, err
		}
	} else {
		return nil, errors.New("kind is not department or custom")
	}

	return
}

func (l *UpdateTemplateLogic) updateCustomTemplate(req *types.UpdateTemplateReq) (err error) {
	// 查询最大版本号
	maxVersionNo, err := mapper.NewWorkflowTemplateVersionClient(l.svcCtx.GormDB).GetMaxVersionNo(l.ctx, req.ID)
	if err != nil {
		return
	}

	// 检查是否需要推送模板
	templateID := ""
	nodeTemplateIDs := make([]string, len(req.Custom))
	if l.isNeedPushTemplate(req) {
		// 创建工作流模板
		templateID, nodeTemplateIDs, err = l.svcCtx.SallyAddons.AddWorkflowTemplate(l.ctx, l.buildSallyTemplateConfig(req))
		if err != nil {
			return err
		}
	}

	now := time.Now()
	// 构建版本信息
	version := mapper.WorkflowTemplateVersion{
		ID:                l.svcCtx.IDGenerator.GenerateIDString(),
		MainID:            req.ID,
		Name:              req.Name,
		VersionNo:         maxVersionNo + 1,
		Kind:              mapper.WorkflowTemplateVersionKindCustom,
		TimeoutWarnStatus: req.TimeoutWarnStatus,
		TimeoutHour:       req.TimeoutHour,
		AutoApproveKind:   req.AutoApproveKind,
		SallyTemplateID:   templateID,
		CreatedBy:         utils.GetContextUserID(l.ctx),
		CreatedAt:         now,
	}

	// 构建节点信息
	nodes := make([]mapper.WorkflowTemplateNode, len(req.Custom))
	reviews := make([]mapper.WorkflowTemplateNodeReview, 0)
	for i, node := range req.Custom {
		nodes[i] = mapper.WorkflowTemplateNode{
			ID:           l.svcCtx.IDGenerator.GenerateIDString(),
			VersionID:    version.ID,
			Name:         node.NodeName,
			Kind:         node.NodeKind,
			SigningKind:  node.SigningKind,
			CCKind:       node.CCKind,
			ApprovalKind: node.ApprovalKind,
			NodeOrder:    i + 1,
			SallyNodeID:  nodeTemplateIDs[i],
			CreatedAt:    now,
		}

		// 构建审批人信息
		for _, approverId := range node.ApproverIds {
			reviews = append(reviews, mapper.WorkflowTemplateNodeReview{
				NodeID:   nodes[i].ID,
				ReviewID: approverId,
				Kind:     1,
			})
		}

		// 构建抄送人信息
		for _, ccId := range node.CCIds {
			reviews = append(reviews, mapper.WorkflowTemplateNodeReview{
				NodeID:   nodes[i].ID,
				ReviewID: ccId,
				Kind:     2,
			})
		}
	}

	return l.svcCtx.GormDB.Transaction(func(tx *gorm.DB) error {
		// 更新 main 表
		err = mapper.NewWorkflowTemplateMainClient(tx).UpdateVersionWithTx(l.ctx, tx, req.ID, version.ID, utils.GetContextUserID(l.ctx))
		if err != nil {
			return err
		}

		// 创建 version 表
		err = mapper.NewWorkflowTemplateVersionClient(tx).BatchCreateWithTx(l.ctx, tx, []mapper.WorkflowTemplateVersion{version})
		if err != nil {
			return err
		}

		// 保存节点
		if len(nodes) > 0 {
			err = mapper.NewWorkflowTemplateNodeClient(tx).BatchCreateWithTx(l.ctx, tx, nodes)
			if err != nil {
				return err
			}
		}

		// 保存审批人信息
		if len(reviews) > 0 {
			err = mapper.NewWorkflowTemplateNodeReviewClient(tx).BatchCreateWithTx(l.ctx, tx, reviews)
			if err != nil {
				return err
			}
		}

		return nil
	})

}

func (l *UpdateTemplateLogic) isNeedPushTemplate(req *types.UpdateTemplateReq) bool {
	for _, node := range req.Custom {
		// 自定义审批类型节点不推送模板
		if node.ApprovalKind == constant.WorkflowTemplateNodeReviewerKindCustom || node.CCKind == constant.WorkflowTemplateNodeReviewerKindCustom {
			return false
		}
	}
	return true
}

func (l *UpdateTemplateLogic) updateDepartmentTemplate(req *types.UpdateTemplateReq) (err error) {
	now := time.Now()
	// 查询最大版本号
	maxVersionNo, err := mapper.NewWorkflowTemplateVersionClient(l.svcCtx.GormDB).GetMaxVersionNo(l.ctx, req.ID)
	if err != nil {
		return
	}

	// 构建版本信息
	version := mapper.WorkflowTemplateVersion{
		ID:                l.svcCtx.IDGenerator.GenerateIDString(),
		MainID:            req.ID,
		Name:              req.Name,
		VersionNo:         maxVersionNo + 1,
		Kind:              mapper.WorkflowTemplateVersionKindDepartment,
		TimeoutWarnStatus: req.TimeoutWarnStatus,
		TimeoutHour:       req.TimeoutHour,
		AutoApproveKind:   req.AutoApproveKind,
		CreatedBy:         utils.GetContextUserID(l.ctx),
		CreatedAt:         now,
	}

	// 构建部门信息
	department := mapper.WorkflowTemplateDepartment{
		ID:          l.svcCtx.IDGenerator.GenerateIDString(),
		VersionID:   version.ID,
		SigningKind: req.Department.SigningKind,
		Level:       req.Department.Level,
		CCKind:      req.Department.CCKind,
		NodeKind:    req.Department.NodeKind,
		CreatedAt:   now,
	}

	ccReviews := make([]mapper.WorkflowTemplateCCReview, len(req.Department.CCIds))
	for i, ccId := range req.Department.CCIds {
		ccReviews[i] = mapper.WorkflowTemplateCCReview{
			VersionID: version.ID,
			ReviewID:  ccId,
		}
	}

	return l.svcCtx.GormDB.Transaction(func(tx *gorm.DB) error {
		// 更新 main 表
		err = mapper.NewWorkflowTemplateMainClient(tx).UpdateVersionWithTx(l.ctx, tx, req.ID, version.ID, utils.GetContextUserID(l.ctx))
		if err != nil {
			return err
		}

		// 创建 version 表
		err = mapper.NewWorkflowTemplateVersionClient(tx).BatchCreateWithTx(l.ctx, tx, []mapper.WorkflowTemplateVersion{version})
		if err != nil {
			return err
		}

		// 创建 cc 表
		if len(ccReviews) > 0 {
			err = mapper.NewWorkflowTemplateCCReviewClient(tx).BatchCreateWithTx(l.ctx, tx, ccReviews)
			if err != nil {
				return err
			}
		}

		// 创建部门表
		err = mapper.NewWorkflowTemplateDepartmentClient(tx).BatchCreateWithTx(l.ctx, tx, []mapper.WorkflowTemplateDepartment{department})
		if err != nil {
			return err
		}

		return nil
	})

}

func (l *UpdateTemplateLogic) buildSallyTemplateConfig(req *types.UpdateTemplateReq) (config addons.WorkflowTemplateConfig) {
	nodes := make([]addons.NodeTemplateConfig, len(req.Custom))
	for i, node := range req.Custom {
		nodes[i] = addons.NodeTemplateConfig{
			NodeTemplateName: node.NodeName,
			NodeTemplateKind: 1, // 审批类型节点
			ReviewerConfig: addons.NodeTemplateReviewerConfig{
				Kind:             convertApproverKindToSallyReviewerKind(node.ApprovalKind),
				ReviewerNos:      node.ApproverIds,
				ApprovalStrategy: node.SigningKind,
			},
		}
		if len(node.CCIds) > 0 {
			nodes[i].BusinessParams = map[string]any{
				constant.WorkflowNodeBusinessParamsCCIds:  node.CCIds,
				constant.WorkflowNodeBusinessParamsCCKind: node.CCKind,
			}
		}
	}
	config = addons.WorkflowTemplateConfig{
		Name:                req.Name,
		NodeTemplateConfigs: nodes,
		AutoApprovalConfig: addons.AutoApprovalConfig{
			Strategy: req.AutoApproveKind,
		},
	}
	return
}

// 转换 kind 为 int
func convertApproverKindToSallyReviewerKind(kind string) int {
	// 审批类型节点 岗位-position,角色-role,指定-designate,自定义-custom,集团指定-group_designate
	switch kind {
	case constant.WorkflowTemplateNodeReviewerKindPosition:
		return 2
	case constant.WorkflowTemplateNodeReviewerKindRole:
		return 3
	default:
		return 1
	}
}
