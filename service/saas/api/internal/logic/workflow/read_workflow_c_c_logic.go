package workflow

import (
	"context"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/gorm/mapper"

	"github.com/zeromicro/go-zero/core/logx"
)

type ReadWorkflowCCLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewReadWorkflowCCLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ReadWorkflowCCLogic {
	return &ReadWorkflowCCLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ReadWorkflowCCLogic) ReadWorkflowCC(req *types.WorkflowCCReadReq) (resp *types.WorkflowCCReadResp, err error) {
	err = mapper.NewWorkflowCCClient(l.svcCtx.GormDB).UpdateConsultStatus(l.ctx, req.CCIds, true)
	if err != nil {
		return nil, err
	}
	return &types.WorkflowCCReadResp{}, nil
}
