package workflow

import (
	"context"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type EnableTemplateVersionLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewEnableTemplateVersionLogic(ctx context.Context, svcCtx *svc.ServiceContext) *EnableTemplateVersionLogic {
	return &EnableTemplateVersionLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *EnableTemplateVersionLogic) EnableTemplateVersion(req *types.TemplateVersionEnableReq) (resp *types.TemplateVersionEnableResp, err error) {
	// 查询版本
	version, err := mapper.NewWorkflowTemplateVersionClient(l.svcCtx.GormDB).GetByID(l.ctx, req.VersionID)
	if err != nil {
		return nil, err
	}

	// 更新版本状态
	err = mapper.NewWorkflowTemplateMainClient(l.svcCtx.GormDB).UpdateVersion(l.ctx, version.MainID, req.VersionID, utils.GetContextUserID(l.ctx))
	if err != nil {
		return nil, err
	}

	return
}
