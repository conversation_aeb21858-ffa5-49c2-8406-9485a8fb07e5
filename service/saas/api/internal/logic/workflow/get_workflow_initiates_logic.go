package workflow

import (
	"context"
	"strings"
	"time"

	"phoenix/service/saas/adapter/addons"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/constant"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetWorkflowInitiatesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetWorkflowInitiatesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetWorkflowInitiatesLogic {
	return &GetWorkflowInitiatesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetWorkflowInitiatesLogic) GetWorkflowInitiates(req *types.WorkflowInitiatesReq) (resp *types.WorkflowInitiatesResp, err error) {
	sallyReq := l.buildSallyReq(req)
	workflows, total, err := l.svcCtx.SallyAddons.GetWorkflows(l.ctx, sallyReq)
	if err != nil {
		return nil, err
	}

	mapWorkflows, err := l.buildWorkflows(workflows)
	if err != nil {
		return nil, err
	}

	//返回 demo
	resp = &types.WorkflowInitiatesResp{
		BaseListInfo: types.BaseListInfo{
			Total: uint64(total),
		},
		Data: mapWorkflows,
	}
	return resp, nil
}

func (l *GetWorkflowInitiatesLogic) buildWorkflows(workflows []addons.SallyGetWorkflowsResult) ([]types.WorkflowInitiatesInfo, error) {
	mapWorkflows := make([]types.WorkflowInitiatesInfo, 0, len(workflows))
	for _, workflow := range workflows {

		// 取出当前节点审批人昵称
		var currentNodeApproverNicknamesStr string
		var currentNodeName string
		if workflow.Status == addons.WorkflowStatusUnderReview {
			// 进行中才有
			currentNodeName = workflow.CurrentNodeName
			currentNodeApproverNicknames, err := l.getNodeApproverNicknames(workflow)
			if err != nil {
				return nil, err
			}
			currentNodeApproverNicknamesStr = strings.Join(currentNodeApproverNicknames, ",")
		}

		mapWorkflows = append(mapWorkflows, types.WorkflowInitiatesInfo{
			FlowName:                     workflow.Name,
			CurrentNodeName:              currentNodeName,
			CurrentNodeApproverNicknames: currentNodeApproverNicknamesStr,
			FlowCreatedTime:              workflow.CreatedAt.UnixMilli(), // 流程创建时间
			FlowStatus:                   workflow.Status,
			FlowId:                       workflow.ID,
		})
	}
	return mapWorkflows, nil
}

func (l *GetWorkflowInitiatesLogic) buildSallyReq(req *types.WorkflowInitiatesReq) addons.SallyGetWorkflowsParams {
	sallyReq := addons.SallyGetWorkflowsParams{
		SallyPageCond: addons.SallyPageCond{
			PageNo:   int(req.Page),
			PageSize: int(req.PageSize),
		},
		SponsorID:      utils.GetContextUserID(l.ctx),
		WorkflowName:   req.Search,
		WorkflowStatus: req.FlowStatus,
	}

	if req.FlowCreatedTimeBegin != 0 {
		sallyReq.CreatedAtStart = time.UnixMilli(req.FlowCreatedTimeBegin)
	}
	if req.FlowCreatedTimeEnd != 0 {
		sallyReq.CreatedAtEnd = time.UnixMilli(req.FlowCreatedTimeEnd)
	}

	if req.OrganizationId != "" {
		sallyReq.BusinessParams = map[string]any{
			constant.WorkflowBusinessParamsOrganizationID: req.OrganizationId,
		}
	}

	if req.FlowNodeApproverId != "" {
		sallyReq.CurrentNodeReviewerIDs = []string{req.FlowNodeApproverId}
	}
	return sallyReq
}

func (l *GetWorkflowInitiatesLogic) getNodeApproverNicknames(workflow addons.SallyGetWorkflowsResult) ([]string, error) {
	if workflow.Status == addons.WorkflowStatusPassed {
		return make([]string, 0), nil
	}

	currentNodeApproverNicknames := make([]string, 0)
	if workflow.CurrentNodeID != "" {
		node, err := l.svcCtx.SallyAddons.GetNode(l.ctx, workflow.CurrentNodeID)
		if err != nil {
			return nil, err
		}

		for _, reviewer := range node.NodeReviewTasks {
			if reviewer.Status == addons.WorkflowStatusUnderReview {
				currentNodeApproverNicknames = append(currentNodeApproverNicknames, l.svcCtx.QuickNameTranslator.TranslateUserNickname(l.ctx, reviewer.ReviewerID))
			}
		}
	}

	return currentNodeApproverNicknames, nil
}
