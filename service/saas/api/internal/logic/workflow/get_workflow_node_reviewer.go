package workflow

import (
	"context"
	"phoenix/service/saas/constant"
	"phoenix/service/saas/model/ent"
	"phoenix/service/saas/model/ent/organizationuserinfo"
	"phoenix/service/saas/model/ent/role"
	"phoenix/service/saas/model/ent/user"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"gorm.io/gorm"
)

type GetWorkflowNodeReviewerReferenceImpl struct {
	db  *gorm.DB
	ent *ent.Client
}

func NewGetWorkflowNodeReviewerReferenceImpl(db *gorm.DB, ent *ent.Client) *GetWorkflowNodeReviewerReferenceImpl {
	return &GetWorkflowNodeReviewerReferenceImpl{
		db:  db,
		ent: ent,
	}
}

func (h *GetWorkflowNodeReviewerReferenceImpl) GetReviewerIDs(ctx context.Context, roleKind int, reviewerNos []string, params map[string]any) (userIDs []string, err error) {
	organizationID, ok := utils.ParseStringParam(params, constant.WorkflowBusinessParamsOrganizationID)
	if !ok {
		return nil, err
	}
	switch roleKind {
	case 2: // 岗位
		// 查询岗位下所有用户
		userIDs, err = mapper.NewUserPositionsOrganizationsClient(h.db).GetUserIDsByPositionIdsAndOrganizationId(reviewerNos, organizationID)
		if err != nil {
			return nil, err
		}
	case 3: // 角色
		// 查询角色下所有用户
		userIDs, err = h.ent.User.Query().Where(user.HasRolesWith(role.IDIn(reviewerNos...), role.OrganizationID(organizationID))).IDs(ctx)
	case 5: // 部门管理员
		userIDs, err = h.ent.User.Query().Where(user.HasOrganizationInfosWith(organizationuserinfo.OrganizationIDIn(reviewerNos...), organizationuserinfo.IsLeader(true))).IDs(ctx)
	}
	if err != nil {
		return nil, err
	}

	return userIDs, nil
}
