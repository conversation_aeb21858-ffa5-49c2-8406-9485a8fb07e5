package workflow

import (
	"context"

	"phoenix/service/saas/adapter/addons"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ApproveWorkflowLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewApproveWorkflowLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ApproveWorkflowLogic {
	return &ApproveWorkflowLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ApproveWorkflowLogic) ApproveWorkflow(req *types.WorkflowApproveReq) (resp *types.WorkflowApproveResp, err error) {
	err = l.svcCtx.SallyAddons.Review(l.ctx, addons.SallyReviewRequest{
		NodeReviewerTaskID: req.TaskId,
		Comment:            req.Comment,
		Status:             req.Status,
		Extra:              req.Extra,
	})
	if err != nil {
		return nil, err
	}
	return
}
