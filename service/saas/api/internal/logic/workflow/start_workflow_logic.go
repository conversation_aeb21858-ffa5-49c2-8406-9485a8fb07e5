package workflow

import (
	"context"
	"time"

	"phoenix/service/saas/api/internal/logic/workflow/nodebuilderstrategy"
	"phoenix/service/saas/api/internal/services/workflow/callback"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/constant"
	"phoenix/service/saas/utils"

	"phoenix/service/saas/model/gorm/mapper"

	"phoenix/service/saas/adapter/addons"

	"github.com/zeromicro/go-zero/core/logx"
)

type StartWorkflowLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewStartWorkflowLogic(ctx context.Context, svcCtx *svc.ServiceContext) *StartWorkflowLogic {
	return &StartWorkflowLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *StartWorkflowLogic) StartWorkflow(req *types.WorkflowStartReq) (resp *types.WorkflowStartResp, err error) {
	// 1. 查询模板版本详情，判断流程类型
	version, err := mapper.NewWorkflowTemplateVersionClient(l.svcCtx.GormDB).GetByID(l.ctx, req.FlowVersionId)
	if err != nil {
		l.Logger.Errorf("查询模板版本失败: %v", err)
		return nil, err
	}

	finalTemplateId, err := l.getSallyTemplateID(version, req)
	if err != nil {
		return nil, err
	}

	//  组装 Sally 流程发起参数
	startReq := l.buildStartWorkflowRequest(finalTemplateId, req, version)

	// 生成id
	flowID, err := l.svcCtx.SallyAddons.PreStartWorkflow(l.ctx, startReq)
	if err != nil {
		l.Logger.Errorf("生成流程id失败: %v", err)
		return nil, err
	}

	// 2. 执行前置回调
	callbackReq := l.buildCallbackRequest(req, flowID)
	if err := l.svcCtx.WorkflowCallbackService.Execute(l.ctx, callbackReq); err != nil {
		l.Logger.Errorf("前置回调执行失败: %v", err)
		return nil, err
	}

	startReq.WorkflowID = flowID
	if err := l.svcCtx.SallyAddons.StartWorkflow(l.ctx, startReq); err != nil {
		l.Logger.Errorf("发起流程失败: %v", err)
		return nil, err
	}
	resp = &types.WorkflowStartResp{}
	return
}

func (l *StartWorkflowLogic) buildStartWorkflowRequest(finalTemplateId string, req *types.WorkflowStartReq, version mapper.WorkflowTemplateVersion) addons.SallyStartWorkflowRequest {
	return addons.SallyStartWorkflowRequest{
		WorkflowTemplateID: finalTemplateId,
		BusinessID:         req.BusinessId,
		FormContent:        req.FormContent,
		BusinessParams: map[string]any{
			constant.WorkflowBusinessParamsOrganizationID:            utils.GetContextOrganizationID(l.ctx),
			constant.WorkflowBusinessParamsSponsorID:                 utils.GetContextUserID(l.ctx),
			constant.WorkflowBusinessParamsTenantID:                  utils.GetContextTenantID(l.ctx),
			constant.WorkflowBusinessParamsWorkflowTemplateID:        version.MainID,
			constant.WorkflowBusinessParamsWorkflowTemplateVersionID: version.ID,
			constant.WorkflowNodeBusinessParamsTimeout:               version.TimeoutHour,
		},
		SponsorID: utils.GetContextUserID(l.ctx),
	}
}

func (l *StartWorkflowLogic) buildCallbackRequest(req *types.WorkflowStartReq, flowID string) *callback.Request {
	return &callback.Request{
		TenantID:       utils.GetContextTenantID(l.ctx),
		OrganizationID: utils.GetContextOrganizationID(l.ctx),
		SponsorID:      utils.GetContextUserID(l.ctx),
		FormContent:    req.FormContent,
		CreatedAt:      time.Now().UnixMilli(),
		BusinessID:     req.BusinessId,
		WorkflowID:     flowID,
	}
}

func (l *StartWorkflowLogic) getSallyTemplateID(version mapper.WorkflowTemplateVersion, req *types.WorkflowStartReq) (string, error) {
	if !l.isNeedNewTemplate(version) {
		return version.SallyTemplateID, nil
	}
	builder := nodebuilderstrategy.NewNodeBuilderStrategy(l.svcCtx, version.Kind)
	templateNodes, err := builder.BuildNodes(l.ctx, req, version)
	if err != nil {
		l.Logger.Errorf("组装模板失败: %v", err)
		return "", err
	}

	config := addons.WorkflowTemplateConfig{
		Name:                version.Name,
		NodeTemplateConfigs: templateNodes,
		AutoApprovalConfig: addons.AutoApprovalConfig{
			Strategy: version.AutoApproveKind,
		},
	}

	tid, _, err := l.svcCtx.SallyAddons.AddWorkflowTemplate(l.ctx, config)
	if err != nil {
		l.Logger.Errorf("推送新模板到 Sally 失败: %v", err)
		return "", err
	}
	return tid, nil
}

func (l *StartWorkflowLogic) isNeedNewTemplate(version mapper.WorkflowTemplateVersion) bool {
	return version.SallyTemplateID == ""
}
