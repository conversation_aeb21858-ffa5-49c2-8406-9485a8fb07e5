package morehelp

import (
	"context"
	"path/filepath"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"
	"strings"
	"time"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type UpdateMoreHelpFileLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateMoreHelpFileLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateMoreHelpFileLogic {
	return &UpdateMoreHelpFileLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateMoreHelpFileLogic) UpdateMoreHelpFile(req *types.UpdateMoreHelpFileReq) (resp *types.BaseDataInfo, err error) {
	// 根据文件id获取文件信息
	fmsFileClient := mapper.NewFmsFileClient(l.svcCtx.GormDB)
	fileInfo, err := fmsFileClient.GetById(l.ctx, req.FileID)
	if err != nil {
		return nil, err
	}
	// 数据转换
	moreHelpFileInfo := l.reqToMoreHelpFileInfo(&fileInfo, req.ID)
	//数据更新
	moreHelpFileClient := mapper.NewMoreHelpFileClient(l.svcCtx.GormDB)
	err = moreHelpFileClient.Update(l.ctx, moreHelpFileInfo)
	if err != nil {
		return nil, err
	}
	return &types.BaseDataInfo{Msg: "更新成功"}, nil
}

func (l *UpdateMoreHelpFileLogic) reqToMoreHelpFileInfo(fileInfo *mapper.FmsFile, id string) (moreHelpFileInfo mapper.MoreHelpFile) {
	fileType := strings.TrimPrefix(filepath.Ext(fileInfo.Name), ".")
	user := utils.GetCurrentLoginUser(l.ctx)
	return mapper.MoreHelpFile{
		ID:        id,
		FileID:    fileInfo.ID,
		FileName:  fileInfo.Name,
		FileType:  fileType,
		UpdatedAt: time.Now(),
		UpdatedBy: user.UserId,
	}
}
