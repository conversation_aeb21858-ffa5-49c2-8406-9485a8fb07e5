package morehelp

import (
	"context"
	"phoenix/service/saas/model/gorm/mapper"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteMoreHelpFileLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteMoreHelpFileLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteMoreHelpFileLogic {
	return &DeleteMoreHelpFileLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteMoreHelpFileLogic) DeleteMoreHelpFile(req *types.DeleteMoreHelpFileReq) (resp *types.BaseDataInfo, err error) {
	moreHelpFileClient := mapper.NewMoreHelpFileClient(l.svcCtx.GormDB)
	err = moreHelpFileClient.Delete(l.ctx, req.ID)
	if err != nil {
		return nil, err
	}
	return &types.BaseDataInfo{Msg: "删除成功"}, nil
}
