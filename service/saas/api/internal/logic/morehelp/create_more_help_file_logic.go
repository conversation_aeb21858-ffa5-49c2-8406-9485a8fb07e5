package morehelp

import (
	"context"
	"path/filepath"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"
	"phoenix/service/saas/utils/uuidx"
	"strings"
	"time"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type CreateMoreHelpFileLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateMoreHelpFileLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateMoreHelpFileLogic {
	return &CreateMoreHelpFileLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateMoreHelpFileLogic) CreateMoreHelpFile(req *types.CreateMoreHelpFileReq) (*types.BaseDataInfo, error) {
	// 根据文件id获取文件信息
	fmsFileClient := mapper.NewFmsFileClient(l.svcCtx.GormDB)
	fileInfo, err := fmsFileClient.GetById(l.ctx, req.FileID)
	if err != nil {
		return nil, err
	}
	// 查询最大序号
	tenantId := utils.GetContextTenantID(l.ctx)
	moreHelpFileClient := mapper.NewMoreHelpFileClient(l.svcCtx.GormDB)
	file, err := moreHelpFileClient.GetMoreHelpFileMaxOrder(l.ctx, tenantId)
	fileOrder := int64(1)
	if file != nil {
		fileOrder = file.FileOrder + 1
	}
	// 数据转换
	moreHelpFile := l.fmsFileToMoreHelpInfo(&fileInfo, fileOrder)
	// 保存文件信息到更多帮助列表
	err = moreHelpFileClient.Create(l.ctx, moreHelpFile)
	if err != nil {
		return nil, err
	}
	return &types.BaseDataInfo{Msg: "上传成功"}, nil
}

func (l *CreateMoreHelpFileLogic) fmsFileToMoreHelpInfo(file *mapper.FmsFile, fileOrder int64) (moreHelpInfo mapper.MoreHelpFile) {
	fileType := strings.TrimPrefix(filepath.Ext(file.Name), ".")
	user := utils.GetCurrentLoginUser(l.ctx)
	uuid := uuidx.NewUUID()
	id := strings.ReplaceAll(uuid.String(), "-", "")
	timeNow := time.Now()
	return mapper.MoreHelpFile{
		ID:        id,
		FileID:    file.ID,
		FileName:  file.Name,
		FileType:  fileType,
		FileOrder: fileOrder,
		CreatedAt: timeNow,
		UpdatedAt: timeNow,
		CreatedBy: user.UserId,
		UpdatedBy: user.UserId,
		TenantID:  user.TenantId,
	}
}
