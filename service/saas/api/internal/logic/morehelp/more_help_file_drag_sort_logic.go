package morehelp

import (
	"context"
	"errors"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logc"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type MoreHelpFileDragSortLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMoreHelpFileDragSortLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MoreHelpFileDragSortLogic {
	return &MoreHelpFileDragSortLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MoreHelpFileDragSortLogic) MoreHelpFileDragSort(req *types.DragSortMoreHelpFileReq) (resp *types.BaseDataInfo, err error) {
	tenantId := utils.GetContextTenantID(l.ctx)
	moreHelpFileClient := mapper.NewMoreHelpFileClient(l.svcCtx.GormDB)

	// 获取拖动文件信息
	dragFile, err := moreHelpFileClient.GetById(l.ctx, req.ID, tenantId)
	if err != nil {
		return nil, errors.New("拖动文件不存在")
	}

	// 获取目标文件信息
	targetFile, err := moreHelpFileClient.GetById(l.ctx, req.TargetID, tenantId)
	if err != nil {
		return nil, errors.New("目标文件不存在")
	}

	user := utils.GetCurrentLoginUser(l.ctx)

	// 执行拖动排序逻辑 - 将拖动文件放到目标文件下面
	err = l.performDragSort(moreHelpFileClient, dragFile, targetFile, user.UserId, req.Position)
	if err != nil {
		logc.Error(l.ctx, "error", err)
		return nil, err
	}

	return &types.BaseDataInfo{Msg: "拖动排序成功"}, nil
}

func (l *MoreHelpFileDragSortLogic) performDragSort(client *mapper.MoreHelpFileClient, dragFile, targetFile *mapper.MoreHelpFile, updatedBy string, position string) error {
	// 获取当前租户的所有文件列表，按排序降序排列（file_order DESC）
	allFiles, err := client.GetMoreHelpFileList(l.ctx, dragFile.TenantID)
	if err != nil {
		return err
	}

	// 重新构建文件列表，排除拖动文件
	var filesWithoutDrag []mapper.MoreHelpFile
	for _, file := range allFiles {
		if file.ID != dragFile.ID {
			filesWithoutDrag = append(filesWithoutDrag, file)
		}
	}

	// 找到目标文件在新列表中的位置
	targetIndex := -1
	for i, file := range filesWithoutDrag {
		if file.ID == targetFile.ID {
			targetIndex = i
			break
		}
	}

	if targetIndex == -1 {
		return errors.New("目标文件位置计算错误")
	}

	// 构建新的文件列表，将拖动文件放到目标文件下面
	var newFileList []mapper.MoreHelpFile
	if position == "top" {
		// 添加目标文件和之前的文件
		newFileList = append(newFileList, filesWithoutDrag[:targetIndex]...)

		// 在后面插入拖动文件（放到目标文件下面）
		dragFile.UpdatedBy = updatedBy
		newFileList = append(newFileList, *dragFile)

		// 添加目标文件之后的文件
		if targetIndex < len(filesWithoutDrag) {
			newFileList = append(newFileList, filesWithoutDrag[targetIndex:]...)
		}
	} else if position == "bottom" {
		newFileList = append(newFileList, filesWithoutDrag[:targetIndex+1]...)

		// 在后面插入拖动文件（放到目标文件上面）
		dragFile.UpdatedBy = updatedBy
		newFileList = append(newFileList, *dragFile)

		if targetIndex+1 < len(filesWithoutDrag) {
			newFileList = append(newFileList, filesWithoutDrag[targetIndex+1:]...)
		}
	}

	// 重新分配排序值（保持DESC排序，数字大的在前面）
	// 从最大的排序值开始递减，确保拖动文件在目标文件下面
	maxOrder := int64(len(newFileList))
	for i := range newFileList {
		newFileList[i].FileOrder = maxOrder - int64(i)
	}

	// 批量更新排序
	err = client.BatchUpdateOrder(l.ctx, newFileList)
	if err != nil {
		return err
	}

	return nil
}
