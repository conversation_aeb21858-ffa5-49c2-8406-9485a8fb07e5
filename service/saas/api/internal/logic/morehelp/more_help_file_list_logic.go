package morehelp

import (
	"context"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logc"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type MoreHelpFileListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewMoreHelpFileListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *MoreHelpFileListLogic {
	return &MoreHelpFileListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *MoreHelpFileListLogic) MoreHelpFileList() (resp *types.MoreHelpFileListResp, err error) {
	tenantId := utils.GetContextTenantID(l.ctx)
	// 查询数据列表
	moreHelpFileClient := mapper.NewMoreHelpFileClient(l.svcCtx.GormDB)
	moreHelpInfos, err := moreHelpFileClient.GetMoreHelpFileList(l.ctx, tenantId)
	if err != nil {
		return nil, err
	}
	// 数据转换
	fileList, err := l.moreHelpFileToResp(moreHelpInfos)
	if err != nil {
		return nil, err
	}

	return &types.MoreHelpFileListResp{
		BaseDataInfo: types.BaseDataInfo{},
		Data: types.MoreHelpFileListInfo{
			BaseListInfo: types.BaseListInfo{
				Total: uint64(len(fileList)),
			},
			Data: fileList,
		},
	}, nil
}

func (l *MoreHelpFileListLogic) moreHelpFileToResp(moreHelpInfo []mapper.MoreHelpFile) (resp []types.MoreHelpFileInfo, err error) {
	if len(moreHelpInfo) == 0 {
		return make([]types.MoreHelpFileInfo, 0), nil
	}

	// 获取用户名称
	var ids []string
	for _, v := range moreHelpInfo {
		ids = append(ids, v.CreatedBy, v.UpdatedBy)
	}
	userName, err := l.svcCtx.SaasService.GetUserNicknamesByIDs(l.ctx, ids)
	if err != nil {
		logc.Error(l.ctx, err.Error())
		return nil, err
	}

	for _, moreHelpFile := range moreHelpInfo {
		fileInfo := types.MoreHelpFileInfo{
			ID:        moreHelpFile.ID,
			FileID:    moreHelpFile.FileID,
			FileName:  moreHelpFile.FileName,
			FileType:  moreHelpFile.FileType,
			IsOpen:    moreHelpFile.IsOpen,
			CreatedAt: moreHelpFile.CreatedAt.UnixMilli(),
			UpdatedAt: moreHelpFile.UpdatedAt.UnixMilli(),
			CreatedBy: userName[moreHelpFile.CreatedBy],
			UpdatedBy: userName[moreHelpFile.UpdatedBy],
		}
		resp = append(resp, fileInfo)
	}
	return resp, nil
}
