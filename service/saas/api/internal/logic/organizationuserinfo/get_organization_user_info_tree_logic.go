package organizationuserinfo

import (
	"context"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetOrganizationUserInfoTreeLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetOrganizationUserInfoTreeLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetOrganizationUserInfoTreeLogic {
	return &GetOrganizationUserInfoTreeLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetOrganizationUserInfoTreeLogic) GetOrganizationUserInfoTree(req *types.OrganizationUserInfoTreeReq) (resp *types.OrganizationUserInfoTreeResp, err error) {
	if req.OrganizationId == "" {
		req.OrganizationId = utils.GetContextOrganizationID(l.ctx)
	}

	// 查询组织信息
	organizationInfo, err := l.svcCtx.SaasService.GetOrganizationById(l.ctx, &services.IDReq{Id: req.OrganizationId})
	if err != nil {
		return nil, err
	}
	// 将改组织类型加入查询条件
	req.DescendantNodeTypes = append(req.DescendantNodeTypes, int(organizationInfo.NodeType))

	organizationClosure := mapper.NewOrganizationClosureClient(l.svcCtx.GormDB)
	orgIds, err := organizationClosure.GetDescendantIds(l.ctx, req.OrganizationId, req.DescendantNodeTypes...)
	if err != nil {
		return nil, err
	}

	// 1. 查询该组织及所有子级的树
	orgTree, err := l.svcCtx.SaasService.GetOrganizationTreeByOrgIds(l.ctx, req.OrganizationId, orgIds)
	if err != nil {
		return nil, err
	}

	// 2. 查询这些组织下的用户
	userListAll, err := l.svcCtx.SaasService.GetOrganizationUserInfoListAll(l.ctx, &services.OrganizationUserTreeReq{
		OrgIds: orgIds,
	})
	if err != nil {
		return nil, err
	}

	// 3. 构建 orgId -> 用户列表 map（只保留相关组织）
	userMap := make(map[string][]types.OrganizationUsers)
	for _, org := range userListAll.Data {
		var users []types.OrganizationUsers
		for _, u := range org.Users {
			users = append(users, types.OrganizationUsers{
				UserID:   u.UserID,
				Nickname: u.Nickname,
			})
		}
		userMap[org.OrgID] = users
	}

	// 4. 递归组装树结构
	var buildTree func(orgs []*services.OrganizationInfo) []*types.OrganizationUserInfo
	buildTree = func(orgs []*services.OrganizationInfo) []*types.OrganizationUserInfo {
		var result []*types.OrganizationUserInfo
		for _, org := range orgs {
			info := &types.OrganizationUserInfo{
				OrgID:             org.Id,
				OrgName:           org.Name,
				OrganizationUsers: userMap[org.Id],
			}
			if len(org.Children) > 0 {
				info.Children = buildTree(org.Children)
			}
			result = append(result, info)
		}
		return result
	}

	return &types.OrganizationUserInfoTreeResp{
		Data: buildTree(orgTree.Data),
	}, nil
}
