package organizationuserinfo

import (
	"context"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
)

type GetOrganizationUserInfoByIdLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetOrganizationUserInfoByIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetOrganizationUserInfoByIdLogic {
	return &GetOrganizationUserInfoByIdLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetOrganizationUserInfoByIdLogic) GetOrganizationUserInfoById(req *types.GetOrganizationUserInfoByIdReq) (resp *types.OrganizationUserInfoInfoResp, err error) {
	data, err := l.svcCtx.SaasService.GetOrganizationUsers(l.ctx,
		&services.GetOrganizationUsersReq{
			OrganizationId: req.OrganizationId,
			Search:         "",
			UserId:         req.Id,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	resp = &types.OrganizationUserInfoInfoResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	if len(data.Data) == 0 {
		return nil, errorx.NewCodeError(404, "用户不存在").WithCode(404)

	}

	v := data.Data[0]
	presignedURL, err := l.svcCtx.MinioClient.GenerateViewPreSignedGetURL(l.ctx, v.Avatar.Path, v.Avatar.OriginName)
	if err != nil {
		l.Logger.Errorf("获取头像失败: %s", err.Error())
	}

	var userIds []string
	for _, v := range data.Data {
		userIds = append(userIds, v.Id)
	}
	organizationUserPositionMap, err := GetOrganizationUserPosition(userIds, req.OrganizationId, l.svcCtx.GormDB)
	if err != nil {
		l.Logger.Errorf("获取组织用户岗位信息失败：%v", err)
	}

	resp.Data = types.OrganizationUser{
		UserInfo: types.UserInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: v.Id,
			},
			Status:   v.Status,
			Username: v.Username,
			//Password: v.Password,
			Nickname: v.Nickname,
			Mobile:   v.Mobile,
			Email:    v.Email,
			Gender:   v.Gender,
			Post:     v.Post,
			Avatar: types.AvatarInfo{
				Id:  v.Avatar.Id,
				Url: presignedURL,
			},
		},
		Extra:                    v.Extra,
		Sort:                     uint32(v.Sort),
		IsLeader:                 v.IsLeader,
		IsAdmin:                  v.IsAdmin,
		OrganizationUserPosition: organizationUserPositionMap[v.Id],
	}
	return resp, nil
}
