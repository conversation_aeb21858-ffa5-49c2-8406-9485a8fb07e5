package organizationuserinfo

import (
	"context"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	ent2 "phoenix/service/saas/model/ent"
	"phoenix/service/saas/model/ent/organizationuserinfo"
	"phoenix/service/saas/utils/entx"

	"github.com/zeromicro/go-zero/core/logx"
)

type AddOrganizationLeaderAndAdminLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAddOrganizationLeaderAndAdminLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AddOrganizationLeaderAndAdminLogic {
	return &AddOrganizationLeaderAndAdminLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AddOrganizationLeaderAndAdminLogic) AddOrganizationLeaderAndAdmin(req *types.AddOrganizationLeaderAndAdminReq) (resp *types.BaseDataInfo, err error) {
	// 使用事务来确保数据一致性
	err = entx.WithTx(l.ctx, l.svcCtx.DB, func(tx *ent2.Tx) error {
		// 第一步：将该组织下所有用户的is_leader和is_admin设置为false
		err = l.deleteLeaderAndAdmin(tx, req)
		if err != nil {
			return err
		}

		// 将传入的领导ID和管理员ID设置为is_leader=true
		err = l.addLeaderAndAdmin(tx, req)
		if err != nil {
			return err
		}

		return nil
	})

	if err != nil {
		l.Logger.Errorf("设置领导和管理员失败：%s", err.Error())
		return nil, err
	}

	return &types.BaseDataInfo{
		Msg: "设置组织领导和管理员成功",
	}, nil
}

func (l *AddOrganizationLeaderAndAdminLogic) deleteLeaderAndAdmin(tx *ent2.Tx, req *types.AddOrganizationLeaderAndAdminReq) (err error) {
	// 第一步：将该组织下所有用户的is_leader和is_admin设置为false
	_, err = tx.OrganizationUserInfo.Update().
		Where(organizationuserinfo.OrganizationIDEQ(req.OrganizationID)).
		SetIsLeader(false).
		SetIsAdmin(false).
		Save(l.ctx)
	if err != nil {
		return err
	}
	return nil
}

func (l *AddOrganizationLeaderAndAdminLogic) addLeaderAndAdmin(tx *ent2.Tx, req *types.AddOrganizationLeaderAndAdminReq) (err error) {
	// 将传入的领导ID设置为is_leader=true
	if len(req.LeaderIds) > 0 {
		_, err = tx.OrganizationUserInfo.Update().
			Where(
				organizationuserinfo.OrganizationIDEQ(req.OrganizationID),
				organizationuserinfo.UserIDIn(req.LeaderIds...),
			).
			SetIsLeader(true).
			Save(l.ctx)
		if err != nil {
			return err
		}
	}

	// 将传入的管理员ID设置为is_admin=true
	if len(req.AdminIds) > 0 {
		_, err = tx.OrganizationUserInfo.Update().
			Where(
				organizationuserinfo.OrganizationIDEQ(req.OrganizationID),
				organizationuserinfo.UserIDIn(req.AdminIds...),
			).
			SetIsAdmin(true).
			Save(l.ctx)
		if err != nil {
			return err
		}
	}
	return nil
}
