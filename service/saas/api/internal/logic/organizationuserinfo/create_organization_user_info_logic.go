package organizationuserinfo

import (
	"context"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/ent"
	"phoenix/service/saas/model/ent/organizationuserinfo"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gorm.io/gorm"
)

type CreateOrganizationUserInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateOrganizationUserInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateOrganizationUserInfoLogic {
	return &CreateOrganizationUserInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateOrganizationUserInfoLogic) CreateOrganizationUserInfo(req *types.AddOrganizationUserReq) (resp *types.BaseDataInfo, err error) {
	// 查询当前组织下最大序号
	maxSort, err := l.getMaxSortByOrganizationId(req.OrganizationId)
	if err != nil {
		return nil, errorx.NewCodeError(500, "查询最大序号失败")
	}
	// 最大序号加一赋值给req.Sort
	req.Sort = maxSort + 1

	user, err := l.svcCtx.SaasService.CreateUser(l.ctx,
		&services.UserInfo{
			Status:   req.Status,
			Username: req.Username,
			Password: req.Password,
			Nickname: req.Nickname,
			Mobile:   req.Mobile,
			Email:    req.Email,
			Gender:   req.Gender,
			Post:     req.Post,
			Kind:     req.Kind,
			DeviceNo: req.DeviceNo,
			Avatar: &services.Avatar{
				Id: req.AvatarID,
			},
		})

	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	data, err := l.svcCtx.SaasService.CreateOrganizationUserInfo(l.ctx,
		&services.OrganizationUserInfoInfo{
			Sort:           req.Sort,
			OrganizationId: req.OrganizationId,
			UserId:         user.Id,
			Extra:          req.Extra,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	// 保存组织用户岗位信息
	err = AddUserPositionsOrganizations(l.ctx, l.svcCtx.GormDB, user.Id, req.OrganizationId, req.PositionIds)
	if err != nil {
		l.Logger.Errorf("保存组织用户岗位信息失败：%v", err)
		return nil, err
	}

	// 保存用户所属上级组织关系
	userIds := []string{user.Id}
	err = SaveUserGroupOrCompanyRecord(l.ctx, l.svcCtx.GormDB, userIds, req.OrganizationId)
	if err != nil {
		l.Logger.Errorf("保存用户所属上级组织关系失败：%v", err)
		return nil, err
	}

	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, data.Msg), Data: user.Id}, nil
}

func SaveUserGroupOrCompanyRecord(ctx context.Context, gormDB *gorm.DB, userIds []string, orgId string) error {
	// 查询组织的上级公司或者集团
	organizationClosureClient := mapper.NewOrganizationClosureClient(gormDB)
	organizationClosure, err := organizationClosureClient.GetAncestorGroupOrCompanyByDescendantId(ctx, orgId)
	if err != nil {
		logx.Errorf("获取新用户所属组织的上级组织失败：%v", err)
		return err
	}

	// 保存用户与上级组织关系
	userBelongCompanyClient := mapper.NewUserBelongCompanyClient(gormDB)
	var userOrgLoginInfos []mapper.UserBelongCompany
	for _, userId := range userIds {
		userOrgLoginInfo := mapper.UserBelongCompany{
			UserId:               userId,
			OrganizationId:       organizationClosure.AncestorID,
			OrganizationNodeType: organizationClosure.AncestorNodeType,
			IsDefaultLogin:       false,
			TenantId:             utils.GetContextTenantID(ctx),
		}
		userOrgLoginInfos = append(userOrgLoginInfos, userOrgLoginInfo)
	}
	err = userBelongCompanyClient.Save(ctx, userOrgLoginInfos)
	if err != nil {
		logx.Errorf("添加用户所属上级组织关系失败：%v", err)
		return err
	}
	return nil
}

// AddUserPositionsOrganizations 保存组织用户岗位信息
func AddUserPositionsOrganizations(ctx context.Context, gormDB *gorm.DB, userId, organizationId string, positionIds []string) error {
	// 组装批量插入数据
	var records []mapper.UserPositionsOrganizations
	for _, positionId := range positionIds {
		records = append(records, mapper.UserPositionsOrganizations{
			UserId:         userId,
			PositionId:     positionId,
			OrganizationId: organizationId,
		})
	}

	// 批量插入
	userPositionsOrganizationsClient := mapper.NewUserPositionsOrganizationsClient(gormDB)
	// 1. 开启事务
	tx := gormDB.WithContext(ctx).Begin()
	if tx.Error != nil {
		return tx.Error
	}

	// 2. 在事务中调用 BatchDelete
	if err := userPositionsOrganizationsClient.BatchDelete(ctx, userId, organizationId, tx); err != nil {
		tx.Rollback() // 删除失败，回滚事务
		return err
	}

	if len(positionIds) == 0 {
		return tx.Commit().Error
	}

	// 3. 在事务中调用 BatchAdd
	if err := userPositionsOrganizationsClient.BatchAdd(ctx, records, tx); err != nil {
		tx.Rollback() // 插入失败，回滚事务
		return err
	}

	return tx.Commit().Error
}

// 新增方法：查询当前组织下最大Sort
func (l *CreateOrganizationUserInfoLogic) getMaxSortByOrganizationId(orgId string) (uint32, error) {
	// First try to get the maximum sort value
	var results []struct {
		Max *int
	}
	err := l.svcCtx.DB.OrganizationUserInfo.
		Query().
		Where(organizationuserinfo.OrganizationID(orgId)).
		Aggregate(ent.Max(organizationuserinfo.FieldSort)).
		Scan(l.ctx, &results)
	if err != nil {
		logc.Error(l.ctx, err)
		return 0, err
	}
	if len(results) == 0 || results[0].Max == nil {
		return 0, nil
	}
	return uint32(*results[0].Max), nil

}
