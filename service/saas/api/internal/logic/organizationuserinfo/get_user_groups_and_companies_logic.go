package organizationuserinfo

import (
	"context"
	"phoenix/service/saas/constant"
	"phoenix/service/saas/model/ent/organization"
	"phoenix/service/saas/model/ent/user"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"entgo.io/ent/dialect/sql"
	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserGroupsAndCompaniesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetUserGroupsAndCompaniesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserGroupsAndCompaniesLogic {
	return &GetUserGroupsAndCompaniesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserGroupsAndCompaniesLogic) GetUserGroupsAndCompanies() (resp *types.GetUserGroupsAndCompaniesResp, err error) {
	var userGroupsAndCompaniesInfos []types.UserGroupsAndCompaniesInfo
	// 超级管理员
	user, err := l.svcCtx.DB.User.Query().Where(user.IDEQ(utils.GetContextUserID(l.ctx))).First(l.ctx)
	if err != nil {
		l.Logger.Errorf("查询用户失败：%v", err)
		return nil, err
	}
	if user.IsSuperuser {
		// 查询全部组织
		orgs, err := l.svcCtx.DB.Organization.Query().Where(organization.NodeTypeIn(constant.OrganizationNodeTypeGroup, constant.OrganizationNodeTypeCompany)).Order(organization.ByNodeType(sql.OrderAsc()), organization.ByCreatedAt(sql.OrderDesc())).All(l.ctx)
		if err != nil {
			l.Logger.Errorf("查询组织失败：%v", err)
			return nil, err
		}

		for _, org := range orgs {
			userGroupsAndCompaniesInfos = append(userGroupsAndCompaniesInfos, types.UserGroupsAndCompaniesInfo{
				Label: org.Name,
				Value: org.ID,
			})
		}
		return &types.GetUserGroupsAndCompaniesResp{
			Data: userGroupsAndCompaniesInfos,
		}, nil
	}

	userId := utils.GetContextUserID(l.ctx)
	// 获取用户所属组织
	userBelongCompanyClient := mapper.NewUserBelongCompanyClient(l.svcCtx.GormDB)
	orgInfo, err := userBelongCompanyClient.GetSuperiorOrganizationInfoByUserId(l.ctx, userId, utils.GetContextTenantID(l.ctx))
	if err != nil {
		l.Logger.Errorf("查询用户上级组织失败：%v", err)
		return nil, err
	}
	for _, userGroupAndCompany := range orgInfo {
		userGroupsAndCompaniesInfo := types.UserGroupsAndCompaniesInfo{
			Label: userGroupAndCompany.Name,
			Value: userGroupAndCompany.OrganizationId,
		}
		userGroupsAndCompaniesInfos = append(userGroupsAndCompaniesInfos, userGroupsAndCompaniesInfo)
	}

	return &types.GetUserGroupsAndCompaniesResp{
		BaseDataInfo: types.BaseDataInfo{
			Msg: "操作成功",
		},
		Data: userGroupsAndCompaniesInfos,
	}, nil
}
