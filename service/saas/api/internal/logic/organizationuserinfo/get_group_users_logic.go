package organizationuserinfo

import (
	"context"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/constant"
	"phoenix/service/saas/model/ent/organizationuserinfo"
	"phoenix/service/saas/model/ent/user"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetGroupUsersLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetGroupUsersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetGroupUsersLogic {
	return &GetGroupUsersLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetGroupUsersLogic) GetGroupUsers() (resp *types.UserSimpleInfoListResp, err error) {
	// 查询所属集团列表
	ids, err := mapper.NewOrganizationClosureClient(l.svcCtx.GormDB).GetAncestorIds(l.ctx, utils.GetContextOrganizationID(l.ctx), constant.OrganizationNodeTypeGroup)
	if err != nil {
		return nil, err
	}
	users, err := l.svcCtx.DB.User.Query().Select(user.FieldID, user.FieldNickname).Where(user.HasOrganizationInfosWith(organizationuserinfo.OrganizationIDIn(ids...))).All(l.ctx)
	if err != nil {
		return nil, err
	}

	resp = &types.UserSimpleInfoListResp{}
	for _, user := range users {
		resp.Data = append(resp.Data, types.UserSimpleInfo{Id: user.ID, Nickname: user.Nickname})
	}
	return
}
