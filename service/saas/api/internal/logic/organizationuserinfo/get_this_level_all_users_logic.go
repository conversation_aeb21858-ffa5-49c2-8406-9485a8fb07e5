package organizationuserinfo

import (
	"context"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/gorm/mapper"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
)

type GetThisLevelAllUsersLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetThisLevelAllUsersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetThisLevelAllUsersLogic {
	return &GetThisLevelAllUsersLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetThisLevelAllUsersLogic) GetThisLevelAllUsers(req *types.OrganizationUserInfoListReq) (resp *types.OrganizationUserInfoListResp, err error) {
	// 查这些组织下所有用户
	viewOrganizationUserIdClient := mapper.NewViewOrganizationUserIdClient(l.svcCtx.GormDB)
	userIds, err := viewOrganizationUserIdClient.GetOrgAllUserIds(l.ctx, req.OrganizationId)
	if err != nil {
		l.Logger.Errorf("查询用户id失败：%s", userIds)
		return nil, err
	}
	if len(userIds) == 0 {
		return &types.OrganizationUserInfoListResp{
			BaseDataInfo: types.BaseDataInfo{Msg: "查询成功"},
		}, nil
	}

	users, err := l.svcCtx.SaasService.GetUserList(l.ctx, &services.UserListReq{
		Page:     req.Page,
		PageSize: req.PageSize,
		NoPage:   req.NoPage,
		Ids:      userIds,
		Search:   req.Search,
	})
	if err != nil {
		return nil, err
	}

	// 构建响应
	resp = &types.OrganizationUserInfoListResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	resp.Data.Total = users.Total
	resp.Data.Data = make([]types.OrganizationUser, 0)

	organizationUserPositionMap, err := GetOrganizationUserPosition(userIds, req.OrganizationId, l.svcCtx.GormDB)
	if err != nil {
		l.Logger.Errorf("获取组织用户岗位信息失败：%v", err)
	}

	for _, v := range users.Data {
		presignedURL, err := l.svcCtx.MinioClient.GenerateViewPreSignedGetURL(l.ctx, v.Avatar.Path, v.Avatar.OriginName)
		if err != nil {
			l.Logger.Errorf("头像查询失败：%s", err.Error())
		}
		resp.Data.Data = append(resp.Data.Data,
			types.OrganizationUser{
				UserInfo: types.UserInfo{
					BaseIDInfo: types.BaseIDInfo{
						Id: v.Id,
					},
					Status:   v.Status,
					Username: v.Username,
					Nickname: v.Nickname,
					Mobile:   v.Mobile,
					Email:    v.Email,
					Gender:   v.Gender,
					Post:     v.Post,
					Avatar: types.AvatarInfo{
						Id:  v.Avatar.Id,
						Url: presignedURL,
					},
				},
				OrganizationUserPosition: organizationUserPositionMap[v.Id],
			})
	}
	return resp, nil
}
