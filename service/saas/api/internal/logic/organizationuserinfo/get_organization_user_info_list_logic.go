package organizationuserinfo

import (
	"context"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/gorm/mapper"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"gorm.io/gorm"
)

type GetOrganizationUserInfoListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetOrganizationUserInfoListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetOrganizationUserInfoListLogic {
	return &GetOrganizationUserInfoListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetOrganizationUserInfoListLogic) GetOrganizationUserInfoList(req *types.OrganizationUserInfoListReq) (resp *types.OrganizationUserInfoListResp, err error) {
	data, err := l.svcCtx.SaasService.GetOrganizationUserInfoList(l.ctx,
		&services.OrganizationUserInfoListReq{
			Page:           req.Page,
			PageSize:       req.PageSize,
			Search:         req.Search,
			OrganizationId: req.OrganizationId,
			NoPage:         req.NoPage,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	resp = &types.OrganizationUserInfoListResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	resp.Data.Total = data.Total
	resp.Data.Data = make([]types.OrganizationUser, 0)

	var userIds []string
	for _, v := range data.Data {
		userIds = append(userIds, v.Id)
	}

	organizationUserPositionMap, err := GetOrganizationUserPosition(userIds, req.OrganizationId, l.svcCtx.GormDB)
	if err != nil {
		l.Logger.Errorf("获取组织用户岗位信息失败：%v", err)
	}

	for _, v := range data.Data {
		var avatar types.AvatarInfo
		if v.Avatar != nil && v.Avatar.Path != "" {
			presignedURL, err := l.svcCtx.MinioClient.GenerateViewPreSignedGetURL(l.ctx, v.Avatar.Path, v.Avatar.OriginName)
			if err != nil {
				l.Logger.Errorf("头像查询失败：%s", err.Error())
			}
			avatar = types.AvatarInfo{
				Id:  v.Avatar.Id,
				Url: presignedURL,
			}
		}

		resp.Data.Data = append(resp.Data.Data,
			types.OrganizationUser{
				UserInfo: types.UserInfo{
					BaseIDInfo: types.BaseIDInfo{
						Id: v.Id,
					},
					Status:   v.Status,
					Username: v.Username,
					Nickname: v.Nickname,
					Mobile:   v.Mobile,
					Email:    v.Email,
					Gender:   v.Gender,
					Post:     v.Post,
					Avatar:   avatar,
				},
				Extra:                    v.Extra,
				Sort:                     uint32(v.Sort),
				IsLeader:                 v.IsLeader,
				IsAdmin:                  v.IsAdmin,
				OrganizationUserPosition: organizationUserPositionMap[v.Id],
			})
	}
	return resp, nil
}

// GetOrganizationUserPosition 获取组织用户岗位信息
func GetOrganizationUserPosition(userIds []string, organizationId string, gormDB *gorm.DB) (oupsMap map[string][]types.OrganizationUserPositionInfo, err error) {
	oupsMap = make(map[string][]types.OrganizationUserPositionInfo)
	userPositionsOrganizationsClient := mapper.NewUserPositionsOrganizationsClient(gormDB)
	oups, err := userPositionsOrganizationsClient.GetOrganizationUserPositionInfo(userIds, organizationId)
	if err != nil {
		return nil, err
	}

	for _, oup := range oups {
		organizationUserPosition := types.OrganizationUserPositionInfo{
			PositionId:   oup.PositionId,
			PositionName: oup.PositionName,
		}
		oupsMap[oup.UserId] = append(oupsMap[oup.UserId], organizationUserPosition)
	}
	return oupsMap, nil
}
