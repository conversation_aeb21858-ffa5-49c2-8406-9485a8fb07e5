package organizationuserinfo

import (
	"context"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/constant"
	"phoenix/service/saas/model/ent/organizationuserinfo"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserBelongDepartmentLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetUserBelongDepartmentLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserBelongDepartmentLogic {
	return &GetUserBelongDepartmentLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserBelongDepartmentLogic) GetUserBelongDepartment() (resp *types.GetUserBelongDepartmentResp, err error) {
	// 查询所属公司下的所有部门
	ids, err := mapper.NewOrganizationClosureClient(l.svcCtx.GormDB).GetDescendantIds(l.ctx, utils.GetContextOrganizationID(l.ctx), constant.OrganizationNodeTypeDepartment)
	if err != nil {
		return nil, err
	}
	if len(ids) == 0 {
		return &types.GetUserBelongDepartmentResp{}, nil
	}

	organizationUserInfos, err := l.svcCtx.DB.OrganizationUserInfo.Query().
		Where(organizationuserinfo.OrganizationIDIn(ids...), organizationuserinfo.UserID(utils.GetContextUserID(l.ctx))).
		WithOrganization().
		All(l.ctx)

	if err != nil {
		return nil, err
	}

	departments := make([]types.GetUserOrganizationInfo, 0)
	for _, organizationUserInfo := range organizationUserInfos {
		departments = append(departments, types.GetUserOrganizationInfo{
			Id:   organizationUserInfo.Edges.Organization.ID,
			Name: organizationUserInfo.Edges.Organization.Name,
		})
	}

	return &types.GetUserBelongDepartmentResp{
		Data: departments,
	}, nil
}
