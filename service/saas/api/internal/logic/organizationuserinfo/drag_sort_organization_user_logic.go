package organizationuserinfo

import (
	"context"
	"errors"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/ent"
	"phoenix/service/saas/model/ent/organizationuserinfo"

	"github.com/zeromicro/go-zero/core/logx"
)

type DragSortOrganizationUserLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDragSortOrganizationUserLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DragSortOrganizationUserLogic {
	return &DragSortOrganizationUserLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DragSortOrganizationUserLogic) DragSortOrganizationUser(req *types.DragSortOrganizationUserReq) (resp *types.BaseDataInfo, err error) {
	// 获取当前组织下所有用户，按sort升序排列
	users, err := l.svcCtx.DB.OrganizationUserInfo.Query().
		Where(
			organizationuserinfo.OrganizationID(req.OrganizationId),
		).
		Order(ent.Asc(organizationuserinfo.FieldSort)).
		All(l.ctx)
	if err != nil {
		return nil, err
	}

	// 找到拖动用户和目标用户在原始 users 列表中的下标
	dragIndex, targetIndex := -1, -1
	for i, u := range users {
		if u.UserID == req.UserId {
			dragIndex = i
		}
		if u.UserID == req.TargetId {
			targetIndex = i
		}
	}
	if dragIndex == -1 || targetIndex == -1 {
		return nil, errors.New("用户不存在")
	}

	// 计算需要调整的区间
	var start, end int
	var startSort uint32
	if dragIndex < targetIndex {
		// 起始索引
		start = dragIndex
		// 结束索引
		end = targetIndex
		// 起始序号
		startSort = users[start].Sort
	} else {
		start = targetIndex
		end = dragIndex
		startSort = users[start].Sort
	}

	// 取出需要调整的子切片
	subList := make([]*ent.OrganizationUserInfo, 0)
	for i := start; i <= end; i++ {
		subList = append(subList, users[i])
	}

	// 移除拖动用户
	var dragUser *ent.OrganizationUserInfo
	subListOrgUserInfo := make([]*ent.OrganizationUserInfo, 0, len(subList)-1)
	for _, u := range subList {
		if u.UserID == req.UserId {
			dragUser = u
		} else {
			subListOrgUserInfo = append(subListOrgUserInfo, u)
		}
	}

	// 找到目标用户在新子切片中的下标
	targetSubIndex := -1
	for i, u := range subListOrgUserInfo {
		if u.UserID == req.TargetId {
			targetSubIndex = i
			break
		}
	}
	if dragUser == nil || targetSubIndex == -1 {
		return nil, errors.New("拖动或目标用户异常")
	}

	// 构建新顺序
	var newSubList []*ent.OrganizationUserInfo
	// 拖动用户放在目标用户的上面或者下面
	if req.Position == "top" {
		newSubList = append(newSubList, subListOrgUserInfo[:targetSubIndex+1]...)
		newSubList = append(newSubList, dragUser)
		if targetSubIndex+1 < len(subListOrgUserInfo) {
			newSubList = append(newSubList, subListOrgUserInfo[targetSubIndex+1:]...)
		}
	} else if req.Position == "bottom" {
		newSubList = append(newSubList, subListOrgUserInfo[:targetSubIndex]...)
		newSubList = append(newSubList, dragUser)
		if targetSubIndex < len(subListOrgUserInfo) {
			newSubList = append(newSubList, subListOrgUserInfo[targetSubIndex:]...)
		}
	} else {
		return nil, errors.New("未知的拖动位置")
	}

	// 只更新这个区间的 sort
	updateOrganizationUserInfos := make([]*services.UpdateOrganizationUserInfo, 0)
	for i, u := range newSubList {
		updateOrganizationUserInfos = append(updateOrganizationUserInfos, &services.UpdateOrganizationUserInfo{
			UserId:         u.UserID,
			Sort:           startSort + uint32(i), // 计算排序值
			OrganizationId: req.OrganizationId,
		})
	}

	_, err = l.svcCtx.SaasService.UpdateOrganizationUserInfos(l.ctx, updateOrganizationUserInfos)
	if err != nil {
		l.Logger.Errorf("更新组织用户排序失败: %v", err)
		return nil, err
	}

	return &types.BaseDataInfo{Msg: "拖动排序成功"}, nil
}
