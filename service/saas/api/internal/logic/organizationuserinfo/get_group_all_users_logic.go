package organizationuserinfo

import (
	"context"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/ent/user"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetGroupAllUsersLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetGroupAllUsersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetGroupAllUsersLogic {
	return &GetGroupAllUsersLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetGroupAllUsersLogic) GetGroupAllUsers() (resp *types.UserSimpleInfoListResp, err error) {
	resp = &types.UserSimpleInfoListResp{}
	users, err := l.svcCtx.DB.User.Query().Select(user.FieldID, user.FieldNickname).All(l.ctx)
	if err != nil {
		return nil, err
	}
	for _, user := range users {
		resp.Data = append(resp.Data, types.UserSimpleInfo{Id: user.ID, Nickname: user.Nickname})
	}
	return
}
