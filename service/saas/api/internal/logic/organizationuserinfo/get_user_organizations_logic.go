package organizationuserinfo

import (
	"context"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserOrganizationsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetUserOrganizationsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserOrganizationsLogic {
	return &GetUserOrganizationsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserOrganizationsLogic) GetUserOrganizations(req *types.GetUserOrganizationByIdReq) (resp *types.GetUserOrganizationByIdResp, err error) {

	data, err := l.svcCtx.SaasService.GetUserOrganizationInfoListAll(l.ctx, &services.GetUserOrganizationsInfoListReq{UserId: req.Id})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	resp = &types.GetUserOrganizationByIdResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	resp.Data = []*types.GetUserOrganizationInfo{}
	if len(data.Data) > 0 {
		for _, v := range data.Data {
			resp.Data = append(resp.Data, &types.GetUserOrganizationInfo{
				Id:   v.Id,
				Name: v.Name,
			})
		}
	}
	return resp, nil
}
