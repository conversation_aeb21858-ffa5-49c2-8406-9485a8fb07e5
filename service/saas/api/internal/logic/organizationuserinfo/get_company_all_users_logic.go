package organizationuserinfo

import (
	"context"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/constant"
	"phoenix/service/saas/model/ent/organizationuserinfo"
	"phoenix/service/saas/model/ent/user"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetCompanyAllUsersLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetCompanyAllUsersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCompanyAllUsersLogic {
	return &GetCompanyAllUsersLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetCompanyAllUsersLogic) GetCompanyAllUsers() (resp *types.UserSimpleInfoListResp, err error) {
	resp = &types.UserSimpleInfoListResp{}
	// 查询下属所有部门
	organizationId := utils.GetContextOrganizationID(l.ctx)
	orgs, err := mapper.NewOrganizationClosureClient(l.svcCtx.GormDB).GetDescendants(l.ctx, organizationId)
	if err != nil {
		return nil, err
	}

	// 到公司停止
	ids := make([]string, 0, len(orgs))
	for _, org := range orgs {
		if org.DescendantNodeType == constant.OrganizationNodeTypeCompany {
			break
		}
		ids = append(ids, org.DescendantID)
	}

	ids = append(ids, organizationId)

	users, err := l.svcCtx.DB.User.Query().Select(user.FieldID, user.FieldNickname).Where(user.HasOrganizationInfosWith(organizationuserinfo.OrganizationIDIn(ids...))).All(l.ctx)
	if err != nil {
		return nil, err
	}
	for _, user := range users {
		resp.Data = append(resp.Data, types.UserSimpleInfo{Id: user.ID, Nickname: user.Nickname})
	}

	return
}
