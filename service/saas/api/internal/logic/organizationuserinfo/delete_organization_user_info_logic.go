package organizationuserinfo

import (
	"context"
	"errors"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/ent"
	"phoenix/service/saas/model/ent/organizationuserinfo"
	"phoenix/service/saas/model/ent/user"
	"phoenix/service/saas/model/gorm/mapper"

	"github.com/zeromicro/go-zero/core/logx"
)

type DeleteOrganizationUserInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteOrganizationUserInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteOrganizationUserInfoLogic {
	return &DeleteOrganizationUserInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteOrganizationUserInfoLogic) DeleteOrganizationUserInfo(req *types.DeleteOrganizationUserInfoByIdsReq) (resp *types.BaseDataInfo, err error) {
	result, err := l.svcCtx.SaasService.DeleteOrganizationUserInfo(l.ctx, &services.DeleteOrganizationUserInfo{
		OrganizationId: req.OrganizationId,
		UserIds:        req.Ids,
	})
	if err != nil {
		return nil, errors.New("删除组织用户失败")
	}

	ancestor, err := mapper.NewOrganizationClosureClient(l.svcCtx.GormDB).GetAncestorGroupOrCompanyByDescendantId(l.ctx, req.OrganizationId)
	if err != nil {
		return nil, err
	}
	// 查询用户在公司下的部门
	ids, err := mapper.NewOrganizationClosureClient(l.svcCtx.GormDB).GetDescendantIds(l.ctx, ancestor.AncestorID)
	if err != nil {
		return nil, err
	}
	ids = append(ids, ancestor.AncestorID)

	// 用户归属部门
	users, err := l.svcCtx.DB.User.Query().
		Where(user.IDIn(req.Ids...)).WithOrganizationInfos(func(query *ent.OrganizationUserInfoQuery) {
		query.Where(organizationuserinfo.OrganizationIDIn(ids...))
	}).All(l.ctx)
	if err != nil {
		return nil, err
	}
	var deleteBelongCompnayUserIds []string
	for _, u := range users {
		// 在该公司下只有一个部门 并且就是当前要解绑的部门
		if len(u.Edges.OrganizationInfos) == 1 && u.Edges.OrganizationInfos[0].Edges.Organization.ID == req.OrganizationId {
			deleteBelongCompnayUserIds = append(deleteBelongCompnayUserIds, u.ID)
		}
	}

	// 删除用户在该组织岗位信息
	gormTx := l.svcCtx.GormDB.Begin()
	userPositionsOrganizationsClient := mapper.NewUserPositionsOrganizationsClient(l.svcCtx.GormDB)
	err = userPositionsOrganizationsClient.BatchDeleteByOrgIdAndUserIdsWithTx(l.ctx, req.OrganizationId, req.Ids, gormTx)
	if err != nil {
		gormTx.Rollback()
		l.Logger.Errorf("删除用户绑定的岗位失败：%v", err)
		return nil, err

	}

	if len(deleteBelongCompnayUserIds) > 0 {
		// 删除归属公司
		userBelongCompanyClient := mapper.NewUserBelongCompanyClient(l.svcCtx.GormDB)
		err = userBelongCompanyClient.BatchDeleteByOrgIdAndUserIdsWithTx(l.ctx, deleteBelongCompnayUserIds, ancestor.AncestorID, gormTx)
		if err != nil {
			gormTx.Rollback()
			l.Logger.Errorf("删除用户登录组织记录失败：%v", err)
			return nil, err
		}
	}
	gormTx.Commit()
	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, result.Msg), Data: types.EmptyData{}}, nil
}
