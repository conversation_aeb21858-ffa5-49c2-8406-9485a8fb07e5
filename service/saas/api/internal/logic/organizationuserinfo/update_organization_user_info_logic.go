package organizationuserinfo

import (
	"context"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
)

type UpdateOrganizationUserInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateOrganizationUserInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateOrganizationUserInfoLogic {
	return &UpdateOrganizationUserInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateOrganizationUserInfoLogic) UpdateOrganizationUserInfo(req *types.UpdateOrganizationUserReq) (resp *types.BaseDataInfo, err error) {

	_, err = l.svcCtx.SaasService.UpdateUser(l.ctx,
		&services.UserInfo{
			Id:       req.Id,
			Status:   req.Status,
			Username: req.Username,
			//Password: req.Password,
			Nickname: req.Nickname,
			Mobile:   req.Mobile,
			Email:    req.Email,
			Gender:   req.Gender,
			Post:     req.Post,
			Avatar:   &services.Avatar{Id: req.AvatarID},
			Kind:     req.Kind,
		})

	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	data, err := l.svcCtx.SaasService.UpdateOrganizationUserInfo(l.ctx,
		&services.UpdateOrganizationUserInfo{
			Sort:           req.Sort,
			OrganizationId: req.OrganizationId,
			UserId:         req.Id,
			Extra:          req.Extra,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	// 保存组织用户岗位信息
	err = AddUserPositionsOrganizations(l.ctx, l.svcCtx.GormDB, req.Id, req.OrganizationId, req.PositionIds)
	if err != nil {
		l.Logger.Errorf("保存组织用户岗位信息失败：%v", err)
		return nil, err
	}

	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, data.Msg), Data: types.EmptyData{}}, nil
}
