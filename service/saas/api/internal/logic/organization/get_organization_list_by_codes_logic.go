package organization

import (
	"context"
	"errors"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetOrganizationListByCodesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetOrganizationListByCodesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetOrganizationListByCodesLogic {
	return &GetOrganizationListByCodesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetOrganizationListByCodesLogic) GetOrganizationListByCodes(req *types.OrganizationListByCodesReq) (resp *types.OrganizationListByCodesResp, err error) {
	if len(req.Codes) == 0 {
		return nil, errors.New("codes is empty")
	}

	orgs, err := l.svcCtx.SaasService.GetOrganizationListByCodes(l.ctx, req.Codes)
	if err != nil {
		return nil, err
	}

	infos := make([]types.OrganizationInfo, len(orgs))
	for i, m := range orgs {
		infos[i] = types.OrganizationInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: m.Id,
			},
			Status:   m.Status,
			Sort:     m.Sort,
			Name:     m.Name,
			Leader:   m.Leader,
			Email:    m.Email,
			Phone:    m.Phone,
			Code:     m.Code,
			NodeType: m.NodeType,
			ParentId: m.ParentId,
			Remark:   m.Remark,
		}
	}
	resp = &types.OrganizationListByCodesResp{
		BaseDataInfo: types.BaseDataInfo{},
		Data:         infos,
	}
	return
}
