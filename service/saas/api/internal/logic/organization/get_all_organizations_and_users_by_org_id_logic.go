package organization

import (
	"context"
	"phoenix/service/saas/model/gorm/mapper"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetAllOrganizationsAndUsersByOrgIdLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetAllOrganizationsAndUsersByOrgIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAllOrganizationsAndUsersByOrgIdLogic {
	return &GetAllOrganizationsAndUsersByOrgIdLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetAllOrganizationsAndUsersByOrgIdLogic) GetAllOrganizationsAndUsersByOrgId(req *types.GetOrgsAndUsersReq) (resp *types.GetOrgsAndUsersResp, err error) {
	orgsAndUsers, err := mapper.NewViewOrganizationUserIdClient(l.svcCtx.GormDB).GetAllOrgsAndUsersByOrgId(l.ctx, req.OrgID)
	if err != nil {
		l.Logger.Errorf("根据组织id查询下级组织和用户失败：%v", err)
		return nil, err
	}
	var orgsAndUsersResp []types.GetOrgsAndUsersInfo
	for _, orgAndUser := range orgsAndUsers {
		orgsAndUsersResp = append(orgsAndUsersResp, types.GetOrgsAndUsersInfo{
			OrgID:  orgAndUser.UserBelongOrgID,
			UserID: orgAndUser.UserID,
		})
	}
	return &types.GetOrgsAndUsersResp{
		Data: orgsAndUsersResp,
	}, nil
}
