package organization

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type CreateOrganizationLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateOrganizationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateOrganizationLogic {
	return &CreateOrganizationLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateOrganizationLogic) CreateOrganization(req *types.OrganizationCreateReq) (resp *types.BaseDataInfo, err error) {
	data, err := l.svcCtx.SaasService.CreateOrganization(l.ctx,
		&services.OrganizationInfo{
			Status:   req.Status,
			Sort:     req.Sort,
			TenantId: req.TenantId,
			Name:     req.Name,
			Code:     req.Code,
			NodeType: req.NodeType,
			Leader:   req.Leader,
			Phone:    req.Phone,
			Email:    req.Email,
			Remark:   req.Remark,
			ParentId: req.ParentId,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, data.Msg), Data: types.EmptyData{}}, nil
}
