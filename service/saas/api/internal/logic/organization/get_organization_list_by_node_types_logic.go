package organization

import (
	"context"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/ent"
	"phoenix/service/saas/model/ent/organization"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetOrganizationListByNodeTypesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetOrganizationListByNodeTypesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetOrganizationListByNodeTypesLogic {
	return &GetOrganizationListByNodeTypesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetOrganizationListByNodeTypesLogic) GetOrganizationListByNodeTypes(req *types.OrganizationListByNodeTypesReq) (resp *types.OrganizationListByNodeTypesResp, err error) {
	organizations, err := l.svcCtx.DB.Organization.Query().Where(organization.NodeTypeIn(req.NodeTypes...)).
		Order(organization.ByNodeType(), organization.BySort()).All(l.ctx)
	if err != nil {
		return nil, err
	}

	organizationInfos := l.build(organizations)

	resp = &types.OrganizationListByNodeTypesResp{
		Data: organizationInfos,
	}
	return
}

func (*GetOrganizationListByNodeTypesLogic) build(organizations []*ent.Organization) []types.OrganizationInfo {
	organizationInfos := make([]types.OrganizationInfo, 0)
	for _, organization := range organizations {
		organizationInfos = append(organizationInfos, types.OrganizationInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: organization.ID,
			},

			Name:     organization.Name,
			Code:     organization.Code,
			NodeType: organization.NodeType,
			Sort:     organization.Sort,
			Status:   organization.Status,
		})
	}
	return organizationInfos
}
