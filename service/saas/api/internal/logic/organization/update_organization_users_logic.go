package organization

import (
	"context"
	"phoenix/service/saas/api/internal/logic/organizationuserinfo"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	entorguserinfo "phoenix/service/saas/model/ent/organizationuserinfo"

	"entgo.io/ent/dialect/sql"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
)

type UpdateOrganizationUsersLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateOrganizationUsersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateOrganizationUsersLogic {
	return &UpdateOrganizationUsersLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateOrganizationUsersLogic) UpdateOrganizationUsers(req *types.UpdateOrganizationUsersReq) (resp *types.BaseDataInfo, err error) {
	// 获取当前组织下最大排序号
	maxSort, _ := l.svcCtx.DB.OrganizationUserInfo.
		Query().
		Where(entorguserinfo.OrganizationID(req.OrganizationId)).
		Order(entorguserinfo.BySort(sql.OrderDesc())).
		Limit(1).
		Only(l.ctx)
	startSort := 1
	if maxSort != nil {
		startSort = int(maxSort.Sort) + 1
	}
	// 组装排序信息
	userSorts := make([]services.UserSortInfo, 0, len(req.UserIds))
	for i, userId := range req.UserIds {
		userSorts = append(userSorts, services.UserSortInfo{
			UserId: userId,
			Sort:   uint32(startSort + i),
		})
	}
	data, err := l.svcCtx.SaasService.UpdateOrganizationUsers(l.ctx,
		&services.UpdateOrganizationUsersReq{
			OrganizationId: req.OrganizationId,
			UserIds:        req.UserIds,
			UserSorts:      userSorts,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	// 保存用户和上级组织关系
	err = organizationuserinfo.SaveUserGroupOrCompanyRecord(l.ctx, l.svcCtx.GormDB, req.UserIds, req.OrganizationId)
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, data.Msg), Data: types.EmptyData{}}, nil
}
