package organization

import (
	"context"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
)

type GetOrganizationTreeListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetOrganizationTreeListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetOrganizationTreeListLogic {
	return &GetOrganizationTreeListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetOrganizationTreeListLogic) GetOrganizationTreeList(req *types.OrganizationListReq) (resp *types.OrganizationListResp, err error) {
	organizationId := utils.GetContextOrganizationID(l.ctx)

	organizationClosure := mapper.NewOrganizationClosureClient(l.svcCtx.GormDB)
	orgIds, err := organizationClosure.GetDescendantIds(l.ctx, organizationId)
	if err != nil {
		return nil, err
	}

	data, err := l.svcCtx.SaasService.GetOrganizationTreeByOrgIds(l.ctx, organizationId, orgIds)
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	resp = &types.OrganizationListResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	resp.Data.Total = data.Total
	resp.Data.Data = make([]types.OrganizationInfo, 0)
	for _, v := range data.Data {
		resp.Data.Data = append(resp.Data.Data, GetOrganizationInfoWithChildren(v))
	}
	return resp, nil

}

func GetOrganizationInfoWithChildren(o *services.OrganizationInfo) types.OrganizationInfo {

	//  初始化一个空的切片，保证返回的切片不为nil，默认为空列表
	children := make([]types.OrganizationInfo, 0)
	for _, v := range o.Children {
		children = append(children, GetOrganizationInfoWithChildren(v))
	}

	return types.OrganizationInfo{
		BaseIDInfo: types.BaseIDInfo{
			Id: o.Id,
		},
		Sort:      o.Sort,
		Status:    o.Status,
		Name:      o.Name,
		Code:      o.Code,
		Ancestors: o.Ancestors,
		NodeType:  o.NodeType,
		TenantId:  o.TenantId,
		Leader:    o.Leader,
		Phone:     o.Phone,
		Email:     o.Email,
		Remark:    o.Remark,
		ParentId:  o.ParentId,
		UserCount: o.UserCount,
		Children:  children,
	}
}
