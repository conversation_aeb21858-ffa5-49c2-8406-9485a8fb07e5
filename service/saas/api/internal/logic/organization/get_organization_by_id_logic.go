package organization

import (
	"context"
	"errors"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/ent/organizationuserinfo"
)

type GetOrganizationByIdLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetOrganizationByIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetOrganizationByIdLogic {
	return &GetOrganizationByIdLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetOrganizationByIdLogic) GetOrganizationById(req *types.IDReq) (resp *types.OrganizationInfoResp, err error) {
	data, err := l.svcCtx.SaasService.GetOrganizationById(l.ctx, &services.IDReq{Id: req.Id})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	leaders, admins, err := l.getLeaderAndAdmin(l.ctx, req.Id)
	if err != nil {
		l.Logger.Errorf(err.Error())
	}

	return &types.OrganizationInfoResp{
		BaseDataInfo: types.BaseDataInfo{
			Code: 0,
			Msg:  l.svcCtx.Trans.Trans(l.ctx, i18n.Success),
		},
		Data: types.OrganizationInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: data.Id,
			},
			Status:              data.Status,
			Sort:                data.Sort,
			TenantId:            data.TenantId,
			Name:                data.Name,
			Ancestors:           data.Ancestors,
			Code:                data.Code,
			NodeType:            data.NodeType,
			Leader:              data.Leader,
			Phone:               data.Phone,
			Email:               data.Email,
			Remark:              data.Remark,
			ParentId:            data.ParentId,
			OrganizationLeaders: leaders,
			OrganizationAdmins:  admins,
		},
	}, nil
}

// 获取组织领导和管理员
func (l *GetOrganizationByIdLogic) getLeaderAndAdmin(ctx context.Context, id string) ([]types.OrganizationLeaders, []types.OrganizationAdmins, error) {
	// 查询用户在本级组织信息，并联表查出用户信息
	organizationUser, err := l.svcCtx.DB.OrganizationUserInfo.Query().
		Where(organizationuserinfo.
			OrganizationID(id), organizationuserinfo.
			Or(organizationuserinfo.IsAdmin(true), organizationuserinfo.IsLeader(true))).
		WithUser().
		All(ctx)
	if err != nil {
		return nil, nil, errors.New("获取组织人员信息失败：" + err.Error())
	}

	var leaders []types.OrganizationLeaders
	var admins []types.OrganizationAdmins
	for _, v := range organizationUser {
		if v.IsLeader {
			leaders = append(leaders, types.OrganizationLeaders{
				LeaderID:   v.UserID,
				LeaderName: v.Edges.User.Nickname,
			})
		}

		if v.IsAdmin {
			admins = append(admins, types.OrganizationAdmins{
				AdminID:   v.UserID,
				AdminName: v.Edges.User.Nickname,
			})
		}
	}
	return leaders, admins, nil
}
