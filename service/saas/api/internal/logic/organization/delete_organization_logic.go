package organization

import (
	"context"
	"errors"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"net/http"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type DeleteOrganizationLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteOrganizationLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteOrganizationLogic {
	return &DeleteOrganizationLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteOrganizationLogic) DeleteOrganization(req *types.IDsReq) (resp *types.BaseDataInfo, err error) {
	// 判断是否可以删除
	err = l.getCanDelete(req.Ids)
	if err != nil {
		return &types.BaseDataInfo{
			Code: http.StatusInternalServerError,
			Msg:  "无法删除存在关联的组织",
		}, nil
	}

	result, err := l.svcCtx.SaasService.DeleteOrganization(l.ctx, &services.IDsReq{
		Ids: req.Ids,
	})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, result.Msg), Data: types.EmptyData{}}, nil

}

// 判断是否可以删除
func (l *DeleteOrganizationLogic) getCanDelete(ids []string) (err error) {
	for _, id := range ids {
		//判断是否有用户
		user, _ := l.svcCtx.SaasService.GetOrganizationUserInfoList(l.ctx, &services.OrganizationUserInfoListReq{OrganizationId: id})
		if user.Data != nil {
			return errors.New("无法删除有关联的组织")
		}
		//判断是否有下级组织
		org, _ := l.svcCtx.SaasService.GetOrganizationList(l.ctx, &services.OrganizationListReq{ParentID: id})
		if org.Data != nil {
			return errors.New("无法删除有关联的组织")
		}
	}
	return nil
}
