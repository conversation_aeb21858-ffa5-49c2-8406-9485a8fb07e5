package organization

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type GetOrganizationListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetOrganizationListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetOrganizationListLogic {
	return &GetOrganizationListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetOrganizationListLogic) GetOrganizationList(req *types.OrganizationListReq) (resp *types.OrganizationListResp, err error) {
	data, err := l.svcCtx.SaasService.GetOrganizationList(l.ctx,
		&services.OrganizationListReq{
			Page:      req.Page,
			PageSize:  req.PageSize,
			Name:      req.Name,
			Ancestors: req.Ancestors,
			Code:      req.Code,
			Search:    req.Search,
			ParentID:  req.ParentID,
			NoParent:  req.NoParent,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	resp = &types.OrganizationListResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	resp.Data.Total = data.Total
	resp.Data.Data = make([]types.OrganizationInfo, 0)

	for _, v := range data.Data {
		resp.Data.Data = append(resp.Data.Data,
			types.OrganizationInfo{
				BaseIDInfo: types.BaseIDInfo{
					Id: v.Id,
				},
				Status:    v.Status,
				Sort:      v.Sort,
				TenantId:  v.TenantId,
				Name:      v.Name,
				Ancestors: v.Ancestors,
				Code:      v.Code,
				NodeType:  v.NodeType,
				Leader:    v.Leader,
				Phone:     v.Phone,
				Email:     v.Email,
				Remark:    v.Remark,
				ParentId:  v.ParentId,
				UserCount: v.UserCount,
			})
	}
	return resp, nil
}
