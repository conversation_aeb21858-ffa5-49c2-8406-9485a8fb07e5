package user

import (
	"context"

	"phoenix/service/saas/adapter/grpc/pb/stargate"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type ForceLogoutLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewForceLogoutLogic(ctx context.Context, svcCtx *svc.ServiceContext) *ForceLogoutLogic {
	return &ForceLogoutLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *ForceLogoutLogic) ForceLogout(req *types.ForceLogoutReq) (resp *types.ForceLogoutResp, err error) {
	// 查询用户token
	token, err := l.svcCtx.SaasService.GetTokenById(l.ctx, &services.IDReq{
		Id: req.TokenId,
	})
	if err != nil {
		return nil, err
	}
	if token.Id == "" || !token.Status {
		return &types.ForceLogoutResp{}, nil
	}

	_, err = stargate.NewAuthClient(l.svcCtx.StargateRpcConn).Logout(l.ctx, &stargate.LogoutReq{
		AccessToken: token.Token,
	})
	if err != nil {
		return nil, err
	}
	return &types.ForceLogoutResp{}, nil
}
