package user

import (
	"context"
	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type GetUserListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetUserListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserListLogic {
	return &GetUserListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserListLogic) GetUserList(req *types.UserListReq) (resp *types.UserListResp, err error) {

	data, err := l.svcCtx.SaasService.GetUserList(l.ctx,
		&services.UserListReq{
			Page:         req.Page,
			PageSize:     req.PageSize,
			Username:     req.Username,
			Nickname:     req.Nickname,
			Mobile:       req.Mobile,
			Email:        req.Email,
			Search:       req.Search,
			IgnoreTenant: req.IgnoreTenant,
			Kind:         req.Kind,
			Imei:         req.Imei,
			DeviceNo:     req.DeviceNo,
			Status:       req.Status,
			NoPage:       req.NoPage,
		})

	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	resp = &types.UserListResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	resp.Data.Total = data.Total
	resp.Data.Data = []types.UserInfo{}

	for _, v := range data.Data {
		ts := v.TenantInfos
		tis := make([]types.TenantInfo, 0, len(ts))
		for _, t := range ts {
			tis = append(tis, types.TenantInfo{
				BaseIDInfo:     types.BaseIDInfo{Id: t.Id},
				Name:           t.Name,
				IsSuper:        t.IsSuper,
				Status:         t.Status,
				ServiceStartAt: t.ServiceStartAt,
				ServiceEndAt:   t.ServiceEndAt,
			})
		}
		resp.Data.Data = append(resp.Data.Data,
			types.UserInfo{
				BaseIDInfo: types.BaseIDInfo{
					Id: v.Id,
				},
				Status:   v.Status,
				Username: v.Username,
				//Password: v.Password,
				Nickname:        v.Nickname,
				Mobile:          v.Mobile,
				Email:           v.Email,
				Gender:          v.Gender,
				Post:            v.Post,
				AvatarId:        v.Avatar.Id,
				Avatar:          GetAvatar(l.ctx, l.svcCtx, v.Avatar),
				Kind:            v.Kind,
				Imei:            v.Imei,
				DeviceNo:        v.DeviceNo,
				DefaultTenantId: v.DefaultTenantId,
				TenantInfos:     tis,
				OrganizationIDs: v.OrganizationIDs,
			})

	}
	return resp, nil
}

func GetAvatar(ctx context.Context, svcCtx *svc.ServiceContext, avatar *services.Avatar) types.AvatarInfo {
	if avatar == nil || avatar.Path == "" {
		return types.AvatarInfo{}
	}
	presignedURL, err := svcCtx.MinioClient.GenerateDownloadPreSignedURL(ctx, avatar.Path, avatar.Name)
	if err != nil {
		logc.Error(ctx, "failed to generate pre-signed URL", logx.Field("filePath", avatar.Path), logx.Field("error", err))
		return types.AvatarInfo{}
	}
	return types.AvatarInfo{
		Id:   avatar.Id,
		Name: avatar.Name,
		Url:  presignedURL,
	}
}
