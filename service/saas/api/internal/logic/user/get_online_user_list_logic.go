package user

import (
	"context"

	"phoenix/service/saas/adapter/grpc/pb/stargate"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetOnlineUserListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetOnlineUserListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetOnlineUserListLogic {
	return &GetOnlineUserListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetOnlineUserListLogic) GetOnlineUserList(req *types.GetOnlineUsersReq) (resp *types.GetOnlineUsersResponse, err error) {
	onlineUserToken, err := stargate.NewUserClient(l.svcCtx.StargateRpcConn).GetOnlineUserTokenIDs(l.ctx, &stargate.GetOnlineUserTokenIDsReq{})
	if err != nil {
		return nil, err
	}
	if len(onlineUserToken.TokenIds) == 0 {
		return &types.GetOnlineUsersResponse{
			OnlineUsers: []types.OnlineUserInfo{},
		}, nil
	}

	onlineUserViews, err := mapper.NewOnlineUserClient(l.svcCtx.GormDB).GetOnlineUserTokenIDs(l.ctx, onlineUserToken.TokenIds, utils.GetContextTenantID(l.ctx))
	if err != nil {
		return nil, err
	}

	onlineUsers, err := utils.ArrayCopy(l.ctx, types.OnlineUserInfo{}, onlineUserViews)
	if err != nil {
		return nil, err
	}

	return &types.GetOnlineUsersResponse{
		OnlineUsers: onlineUsers,
	}, nil
}
