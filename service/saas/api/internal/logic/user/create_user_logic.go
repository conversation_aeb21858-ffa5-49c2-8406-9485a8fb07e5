package user

import (
	"context"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
)

type CreateUserLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateUserLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateUserLogic {
	return &CreateUserLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateUserLogic) CreateUser(req *types.UserCreateReq) (resp *types.BaseDataInfo, err error) {

	tenantId := utils.GetContextTenantID(l.ctx)

	if req.DefaultTenantId == "" {
		req.DefaultTenantId = tenantId
	}
	data, err := l.svcCtx.SaasService.CreateUser(l.ctx,
		&services.UserInfo{
			Status:   req.Status,
			Username: req.Username,
			Password: req.Password,
			Nickname: req.Nickname,
			Mobile:   req.Mobile,
			Email:    req.Email,
			Gender:   req.Gender,
			Post:     req.Post,
			Avatar: &services.Avatar{
				Id: req.AvatarId,
			},
			DefaultTenantId: req.DefaultTenantId,
			DeviceNo:        req.DeviceNo,
			Kind:            req.Kind,
			Imei:            req.Imei,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	if len(req.OrganizationIDs) == 0 {
		return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, data.Msg), Data: types.EmptyData{}}, nil
	}

	// 更新用户所在的部门
	_, err = l.svcCtx.SaasService.UpdateUserOrganizations(l.ctx, &services.UpdateOrganizationUsersReq{
		UserIds:         []string{data.Id},
		OrganizationIDs: req.OrganizationIDs,
	})
	if err != nil {
		return nil, err
	}

	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, data.Msg), Data: types.EmptyData{}}, nil
}
