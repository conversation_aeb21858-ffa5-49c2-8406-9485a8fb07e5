package user

import (
	"context"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/utils"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/utils/encrypt"
)

type UpdateUsersPasswordLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateUsersPasswordLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateUsersPasswordLogic {
	return &UpdateUsersPasswordLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateUsersPasswordLogic) UpdateUsersPassword(req *types.UpdateUsersPasswordReq) (resp *types.BaseDataInfo, err error) {
	if req.OperatorPW == "" {
		return nil, errorx.NewCodeError(400, "请填写正确的操作人员密码")
	}

	if req.NewPass == "" {
		return nil, errorx.NewCodeError(400, "请正确填写新密码")
	}

	userId := utils.GetContextUserID(l.ctx)
	data, err := l.svcCtx.SaasService.GetUserById(l.ctx, &services.IDReq{Id: userId})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	if !encrypt.BcryptCheck(req.OperatorPW, data.Password) {
		return nil, errorx.NewCodeError(400, "密码校验错误")
	}

	_, err = l.svcCtx.SaasService.UpdateUsersPassword(l.ctx, &services.UpdateUsersPasswordReq{
		UserIds: req.UserIds,
		NewPass: req.NewPass,
	})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, "密码修改成功"), Data: types.EmptyData{}}, nil
}
