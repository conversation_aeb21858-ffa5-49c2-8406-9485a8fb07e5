package user

import (
	"context"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	ent2 "phoenix/service/saas/model/ent"
	"phoenix/service/saas/model/ent/schema"
	"phoenix/service/saas/model/ent/tenant"
	"phoenix/service/saas/model/ent/user"
	"phoenix/service/saas/utils"
	"time"

	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
)

type GetUserInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetUserInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserInfoLogic {
	return &GetUserInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserInfoLogic) GetUserInfo() (resp *types.UserInfoWithExtraInfoResp, err error) {
	// 虚拟用户
	if utils.GetContextIsVirtualUser(l.ctx) {
		// 组装虚拟用户信息
		return l.returnVirtualUserInfo()
	}
	userId := utils.GetCurrentLoginUser(l.ctx).UserId
	now := time.Now()
	u, err := l.svcCtx.DB.User.Query().Where(user.IDEQ(userId)).
		WithTenants(func(query *ent2.TenantQuery) {
			query.Where(tenant.ServiceEndAtGTE(now), tenant.ServiceStartAtLTE(now), tenant.StatusEQ(true))
		}).WithAvatar().Only(schema.SkipTenantInject(l.ctx))
	if err != nil {
		logc.Error(l.ctx, "查询用户信息失败,异常：", err.Error())
		return nil, err
	}

	// 管理员可切换所有租户
	if u.IsSuperuser {
		u.Edges.Tenants, err = l.svcCtx.DB.Tenant.Query().Where(tenant.DeletedAtIsNil()).All(l.ctx)
		if err != nil {
			logc.Error(l.ctx, "查询租户信息失败,异常：", err.Error())
			return nil, err
		}
	}

	// 组装租户信息
	var tenantInfos []types.TenantInfo
	for _, v := range u.Edges.Tenants {
		tenantInfos = append(tenantInfos, types.TenantInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: v.ID,
			},
			Uuid:                        v.UUID.String(),
			Key:                         v.Key,
			Secret:                      v.Secret,
			Status:                      v.Status,
			Name:                        v.Name,
			IsSuper:                     v.IsSuper,
			AfterSalesContact:           v.AfterSalesContact,
			LocationID:                  v.LocationID,
			LogSaveKeepDays:             v.LogSaveKeepDays,
			MaxAttendanceUserCount:      v.MaxAttendanceUserCount,
			MaxDeviceCount:              v.MaxDeviceCount,
			MaxUploadFileSize:           v.MaxUploadFileSize,
			MaxUserCount:                v.MaxUserCount,
			Principal:                   v.Principal,
			PrincipalContactInformation: v.PrincipalContactInformation,
			SaleContact:                 v.SaleContact,
			AiStatus:                    v.AiStatus,
			MaxConferenceAgendaTitle:    v.MaxConferenceAgendaTitle,
		})
	}

	// 解决默认单位为空
	var defaultTenantID = u.DefaultTenantID
	if defaultTenantID == "" && len(tenantInfos) > 0 {
		defaultTenantID = tenantInfos[0].Id
	}

	// 查询启用插件
	// 查询默认租户信息
	dt, err := l.svcCtx.SaasService.GetTenantById(l.ctx, &services.IDReq{
		Id: defaultTenantID,
	})
	if err != nil {
		logc.Error(l.ctx, "get tenant by user id error: ", err)
		return nil, err
	}
	// 启用的系统插件
	ps := make([]types.SystemPlugin, 0, len(dt.SystemPlugins))
	for _, plugin := range dt.SystemPlugins {
		ps = append(ps, types.SystemPlugin{
			ID:   plugin.ID,
			Name: plugin.Name,
			Code: plugin.Code,
		})
	}

	avatar := types.AvatarInfo{}
	if u.Edges.Avatar != nil {
		avatar = GetAvatar(l.ctx, l.svcCtx, &services.Avatar{
			Id:   u.Edges.Avatar.ID,
			Name: u.Edges.Avatar.Name,
			Path: u.Edges.Avatar.Path,
		})
	}

	organizationId := utils.GetContextOrganizationID(l.ctx)

	return &types.UserInfoWithExtraInfoResp{
		BaseDataInfo: types.BaseDataInfo{
			Code: 0,
			Msg:  l.svcCtx.Trans.Trans(l.ctx, i18n.Success),
		},
		Data: types.UserInfoWithExtraInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: u.ID,
			},
			Status:                   u.Status,
			Username:                 u.Username,
			Nickname:                 u.Nickname,
			Mobile:                   u.Mobile,
			Email:                    u.Email,
			Gender:                   u.Gender.String(),
			Post:                     u.Post,
			AvatarId:                 u.AvatarID,
			Avatar:                   avatar,
			Kind:                     u.Kind,
			Imei:                     u.Imei,
			DefaultTenantId:          defaultTenantID,
			Tenants:                  tenantInfos,
			SystemPlugins:            ps,
			MaxConferenceAgendaTitle: dt.MaxConferenceAgendaTitle,
			CurrentOrganization:      organizationId,
			IsAdmin:                  u.IsSuperuser,
		},
	}, nil
}

func (l *GetUserInfoLogic) returnVirtualUserInfo() (*types.UserInfoWithExtraInfoResp, error) {
	mobile := utils.GetContextMobile(l.ctx)
	return &types.UserInfoWithExtraInfoResp{
		BaseDataInfo: types.BaseDataInfo{},
		Data: types.UserInfoWithExtraInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: utils.GetContextUserID(l.ctx),
			},
			Username:        mobile,
			Nickname:        mobile,
			Mobile:          mobile,
			Status:          true,
			DefaultTenantId: utils.GetContextTenantID(l.ctx),
			Tenants: []types.TenantInfo{
				{
					BaseIDInfo: types.BaseIDInfo{
						Id: utils.GetContextTenantID(l.ctx),
					},
					Name:     "虚拟租户",
					Status:   true,
					AiStatus: true,
				},
			},
		},
	}, nil
}
