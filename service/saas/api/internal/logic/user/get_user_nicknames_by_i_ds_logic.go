package user

import (
	"context"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserNicknamesByIDsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetUserNicknamesByIDsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserNicknamesByIDsLogic {
	return &GetUserNicknamesByIDsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserNicknamesByIDsLogic) GetUserNicknamesByIDs(req *types.UserNicknamesByIDsReq) (resp *types.UserNicknamesByIDsResp, err error) {
	nicknames, err := l.svcCtx.SaasService.GetUserNicknamesByIDs(l.ctx, req.IDs)
	if err != nil {
		return nil, err
	}

	resp = &types.UserNicknamesByIDsResp{
		BaseDataInfo: types.BaseDataInfo{},
		Data:         nicknames,
	}
	return
}
