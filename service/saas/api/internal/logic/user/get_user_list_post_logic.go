package user

import (
	"context"
	"phoenix/service/saas/api/internal/services"

	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserListPostLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetUserListPostLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserListPostLogic {
	return &GetUserListPostLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserListPostLogic) GetUserListPost(req *types.UserListPReq) (resp *types.UserListPResp, err error) {

	data, err := l.svcCtx.SaasService.GetUserList(l.ctx,
		&services.UserListReq{
			Page:         req.Page,
			PageSize:     req.PageSize,
			Username:     req.Username,
			Nickname:     req.Nickname,
			Mobile:       req.Mobile,
			Email:        req.Email,
			Search:       req.Search,
			Ids:          req.Ids,
			IgnoreTenant: req.IgnoreTenant,
		})

	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	resp = &types.UserListPResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	resp.Data.Total = data.Total
	resp.Data.Data = []types.UserInfo{}

	for _, v := range data.Data {
		resp.Data.Data = append(resp.Data.Data,
			types.UserInfo{
				BaseIDInfo: types.BaseIDInfo{
					Id: v.Id,
				},
				Status:   v.Status,
				Username: v.Username,
				//Password: v.Password,
				Nickname:        v.Nickname,
				Mobile:          v.Mobile,
				Email:           v.Email,
				Gender:          v.Gender,
				Post:            v.Post,
				AvatarId:        v.Avatar.Id,
				Avatar:          GetAvatar(l.ctx, l.svcCtx, v.Avatar),
				OrganizationIDs: v.OrganizationIDs,
			})
	}
	return resp, nil
}
