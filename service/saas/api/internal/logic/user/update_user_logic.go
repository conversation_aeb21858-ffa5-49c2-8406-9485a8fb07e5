package user

import (
	"context"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
)

type UpdateUserLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateUserLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateUserLogic {
	return &UpdateUserLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateUserLogic) UpdateUser(req *types.UserInfo) (resp *types.BaseDataInfo, err error) {
	data, err := l.svcCtx.SaasService.UpdateUser(l.ctx,
		&services.UserInfo{
			Id:       req.Id,
			Status:   req.Status,
			Username: req.Username,
			//Password:        req.Password,
			Nickname:        req.Nickname,
			Mobile:          req.Mobile,
			Email:           req.Email,
			Gender:          req.Gender,
			Post:            req.Post,
			Avatar:          &services.Avatar{Id: req.AvatarId},
			DefaultTenantId: req.DefaultTenantId,
			Kind:            req.Kind,
			DeviceNo:        req.DeviceNo,
			Imei:            req.Imei,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	// 更新用户所在的部门
	if len(req.OrganizationIDs) > 0 {
		data, err = l.svcCtx.SaasService.UpdateUserOrganizations(l.ctx, &services.UpdateOrganizationUsersReq{
			UserIds:         []string{req.Id},
			OrganizationIDs: req.OrganizationIDs,
		})
		if err != nil {
			return nil, errorx.NewCodeErrorFromGrpcStatus(err)
		}
	}

	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, data.Msg), Data: types.EmptyData{}}, nil
}
