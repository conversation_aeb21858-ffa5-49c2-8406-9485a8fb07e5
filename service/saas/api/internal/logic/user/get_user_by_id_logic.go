package user

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type GetUserByIdLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetUserByIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserByIdLogic {
	return &GetUserByIdLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserByIdLogic) GetUserById(req *types.IDReq) (resp *types.UserInfoResp, err error) {
	data, err := l.svcCtx.SaasService.GetUserById(l.ctx, &services.IDReq{Id: req.Id})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	presignedURL, err := l.svcCtx.MinioClient.GenerateDownloadPreSignedURL(l.ctx, data.Avatar.Path, data.Avatar.OriginName)
	if err != nil {
		l.Logger.Errorf("获取头像失败：%s", err.Error())
	}

	return &types.UserInfoResp{
		BaseDataInfo: types.BaseDataInfo{
			Code: 0,
			Msg:  l.svcCtx.Trans.Trans(l.ctx, i18n.Success),
		},
		Data: types.UserInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: data.Id,
			},
			Status:   data.Status,
			Username: data.Username,
			Password: data.Password,
			Nickname: data.Nickname,
			Mobile:   data.Mobile,
			Email:    data.Email,
			Gender:   data.Gender,
			Post:     data.Post,
			AvatarId: data.Avatar.Id,
			Avatar: types.AvatarInfo{
				Id:  data.Avatar.Id,
				Url: presignedURL,
			},
			Imei: data.Imei,
		},
	}, nil
}
