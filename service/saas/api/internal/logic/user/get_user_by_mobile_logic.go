package user

import (
	"context"

	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/ent"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
)

type GetUserByMobileLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetUserByMobileLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserByMobileLogic {
	return &GetUserByMobileLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserByMobileLogic) GetUserByMobile(req *types.UserByMobileReq) (resp *types.UserByMobileResp, err error) {

	user, err := l.svcCtx.SaasService.GetUserByIdentity(l.ctx, &services.IdentityReq{Mobile: req.Mobile, Identity: "mobile"})
	if err != nil {
		if ent.IsNotFound(err) {
			// 用户不存在
			return &types.UserByMobileResp{}, nil
		}
		return nil, err
	}

	if user == nil {
		return nil, errorx.NewCodeInvalidArgumentError("用户不存在")
	}

	// 转换租户
	tenantInfos := make([]types.TenantInfo, 0)
	for _, tenant := range user.TenantInfos {
		tenantInfos = append(tenantInfos, types.TenantInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: tenant.Id,
			},
			Name:                   tenant.Name,
			Uuid:                   tenant.Uuid,
			Key:                    tenant.Key,
			Secret:                 tenant.Secret,
			IsSuper:                tenant.IsSuper,
			AfterSalesContact:      tenant.AfterSalesContact,
			LocationID:             tenant.LocationID,
			LogSaveKeepDays:        tenant.LogSaveKeepDays,
			MaxAttendanceUserCount: tenant.MaxAttendanceUserCount,
			MaxDeviceCount:         tenant.MaxDeviceCount,
			MaxUploadFileSize:      tenant.MaxUploadFileSize,
			MaxUserCount:           tenant.MaxUserCount,
			Status:                 tenant.Status,
			AiStatus:               tenant.AiStatus,
		})
	}

	return &types.UserByMobileResp{
		Data: types.UserInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: user.Id,
			},
			Username:        user.Username,
			Mobile:          user.Mobile,
			DefaultTenantId: user.DefaultTenantId,
			Status:          user.Status,
			Nickname:        user.Nickname,
			TenantInfos:     tenantInfos,
		},
	}, nil
}
