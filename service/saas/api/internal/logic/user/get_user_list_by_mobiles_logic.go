package user

import (
	"context"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetUserListByMobilesLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetUserListByMobilesLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetUserListByMobilesLogic {
	return &GetUserListByMobilesLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetUserListByMobilesLogic) GetUserListByMobiles(req *types.GetUserListByMobileReq) (resp *types.GetUserListByMobileResp, err error) {
	users, err := l.svcCtx.SaasService.GetUserListByMobiles(l.ctx, req.Mobiles)
	if err != nil {
		return nil, err
	}

	infos := make([]types.UserInfo, len(users))
	for i, m := range users {
		infos[i] = types.UserInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: m.Id,
			},
			Status:          m.Status,
			Email:           m.Email,
			Mobile:          m.Mobile,
			Username:        m.Username,
			Nickname:        m.Nickname,
			DefaultTenantId: m.DefaultTenantId,
			DeviceNo:        m.DeviceNo,
			Kind:            m.Kind,
			Imei:            m.Imei,
		}
	}
	return &types.GetUserListByMobileResp{
		BaseDataInfo: types.BaseDataInfo{},
		Data:         infos,
	}, nil
}
