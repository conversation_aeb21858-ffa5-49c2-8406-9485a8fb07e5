package captcha

import (
	"context"
	"github.com/mojocn/base64Captcha"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/enum/errorcode"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetCaptchaLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetCaptchaLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetCaptchaLogic {
	return &GetCaptchaLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetCaptchaLogic) GetCaptcha() (resp *types.CaptchaResp, err error) {

	gen := base64Captcha.NewCaptcha(l.svcCtx.CaptchaUtil.CaptchaDriver, l.svcCtx.CaptchaUtil.CaptchaStore)
	if id, b64s, err := gen.Generate(); err != nil {
		logx.Errorw("fail to generate captcha", logx.Field("detail", err.Error()))
		return &types.CaptchaResp{
			BaseDataInfo: types.BaseDataInfo{Code: errorcode.Internal, Msg: l.svcCtx.Trans.Trans(l.ctx, i18n.Failed)},
			Data:         types.CaptchaInfo{},
		}, nil
	} else {
		resp = &types.CaptchaResp{
			BaseDataInfo: types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, i18n.Success)},
			Data: types.CaptchaInfo{
				CaptchaId: id,
				ImgPath:   b64s,
			},
		}
		return resp, nil
	}
}
