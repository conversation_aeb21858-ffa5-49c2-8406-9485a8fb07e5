package tenant

import (
	"context"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
)

type GetTenantListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetTenantListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTenantListLogic {
	return &GetTenantListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetTenantListLogic) GetTenantList(req *types.TenantListReq) (resp *types.TenantListResp, err error) {
	data, err := l.svcCtx.SaasService.GetTenantList(l.ctx,
		&services.TenantListReq{
			Page:     req.Page,
			PageSize: req.PageSize,
			Key:      req.Key,
			Name:     req.Name,
			Search:   req.Search,
			NoPage:   req.NoPage,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	resp = &types.TenantListResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	resp.Data.Total = data.Total
	resp.Data.Data = make([]types.TenantInfo, 0)

	for _, v := range data.Data {
		resp.Data.Data = append(resp.Data.Data,
			types.TenantInfo{
				BaseIDInfo: types.BaseIDInfo{
					Id: v.Id,
				},
				Uuid:                        v.Uuid,
				Key:                         v.Key,
				Secret:                      v.Secret,
				Status:                      v.Status,
				Name:                        v.Name,
				IsSuper:                     v.IsSuper,
				ServiceStartAt:              v.ServiceStartAt,
				ServiceEndAt:                v.ServiceEndAt,
				AfterSalesContact:           v.AfterSalesContact,
				LocationID:                  v.LocationID,
				LogSaveKeepDays:             v.LogSaveKeepDays,
				MaxAttendanceUserCount:      v.MaxAttendanceUserCount,
				MaxDeviceCount:              v.MaxDeviceCount,
				MaxUploadFileSize:           v.MaxUploadFileSize,
				MaxUserCount:                v.MaxUserCount,
				Principal:                   v.Principal,
				PrincipalContactInformation: v.PrincipalContactInformation,
				SaleContact:                 v.SaleContact,
				AiStatus:                    v.AiStatus,
			})
	}
	return resp, nil
}
