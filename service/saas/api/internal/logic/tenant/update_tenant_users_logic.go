package tenant

import (
	"context"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
)

type UpdateTenantUsersLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateTenantUsersLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateTenantUsersLogic {
	return &UpdateTenantUsersLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateTenantUsersLogic) UpdateTenantUsers(req *types.UpdateTenantUsersReq) (resp *types.BaseDataInfo, err error) {
	data, err := l.svcCtx.SaasService.UpdateTenantUsers(l.ctx,
		&services.UpdateTenantUsersReq{
			TenantId: req.TenantId,
			UserIds:  req.UserIds,
			Kind:     req.Kind,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, data.Msg), Data: types.EmptyData{}}, nil
}
