package tenant

import (
	"context"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
)

type UpdateTenantLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateTenantLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateTenantLogic {
	return &UpdateTenantLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateTenantLogic) UpdateTenant(req *types.TenantUpdateReq) (resp *types.BaseDataInfo, err error) {
	data, err := l.svcCtx.SaasService.UpdateTenant(l.ctx,
		&services.TenantUpdateInfo{
			ID:                          req.ID,
			Uuid:                        req.Uuid,
			Key:                         req.Key,
			Secret:                      req.Secret,
			AfterSalesContact:           req.AfterSalesContact,
			LocationID:                  req.LocationID,
			LogSaveKeepDays:             req.LogSaveKeepDays,
			MaxAttendanceUserCount:      req.MaxAttendanceUserCount,
			MaxDeviceCount:              req.MaxDeviceCount,
			MaxUploadFileSize:           req.MaxUploadFileSize,
			MaxUserCount:                req.MaxUserCount,
			Principal:                   req.Principal,
			PrincipalContactInformation: req.PrincipalContactInformation,
			SaleContact:                 req.SaleContact,
			Status:                      req.Status,
			Name:                        req.Name,
			ServiceStartAt:              req.ServiceStartAt,
			ServiceEndAt:                req.ServiceEndAt,
			SystemPlugins:               req.SystemPlugins,
			AiStatus:                    req.AiStatus,
			MaxConferenceAgendaTitle:    req.MaxConferenceAgendaTitle,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, data.Msg), Data: types.EmptyData{}}, nil
}
