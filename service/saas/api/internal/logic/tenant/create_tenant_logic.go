package tenant

import (
	"context"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
)

type CreateTenantLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateTenantLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateTenantLogic {
	return &CreateTenantLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateTenantLogic) CreateTenant(req *types.TenantCreateReq) (resp *types.BaseDataInfo, err error) {
	maxConferenceAgendaTitle := req.MaxConferenceAgendaTitle
	if req.MaxConferenceAgendaTitle == 0 {
		maxConferenceAgendaTitle = int64(300)
	}
	data, err := l.svcCtx.SaasService.CreateTenant(l.ctx,
		&services.TenantCreateInfo{
			AfterSalesContact:           req.AfterSalesContact,
			LocationID:                  req.LocationID,
			LogSaveKeepDays:             req.LogSaveKeepDays,
			MaxAttendanceUserCount:      req.MaxAttendanceUserCount,
			MaxDeviceCount:              req.MaxDeviceCount,
			MaxUploadFileSize:           req.MaxUploadFileSize,
			MaxUserCount:                req.MaxUserCount,
			Principal:                   req.Principal,
			PrincipalContactInformation: req.PrincipalContactInformation,
			SaleContact:                 req.SaleContact,
			Status:                      req.Status,
			Name:                        req.Name,
			ServiceStartAt:              req.ServiceStartAt,
			ServiceEndAt:                req.ServiceEndAt,
			SystemPlugins:               req.SystemPlugins,
			SecretKey:                   req.SecretKey,
			AiStatus:                    req.AiStatus,
			MaxConferenceAgendaTitle:    maxConferenceAgendaTitle,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, data.Msg), Data: types.EmptyData{}}, nil
}
