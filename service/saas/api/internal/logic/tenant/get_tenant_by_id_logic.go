package tenant

import (
	"context"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
)

type GetTenantByIdLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetTenantByIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTenantByIdLogic {
	return &GetTenantByIdLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetTenantByIdLogic) GetTenantById(req *types.IDReq) (resp *types.TenantInfoResp, err error) {
	data, err := l.svcCtx.SaasService.GetTenantById(l.ctx, &services.IDReq{Id: req.Id})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	// 插件信息
	infos := make([]types.SystemPlugin, 0, len(data.SystemPlugins))
	for _, p := range data.SystemPlugins {
		infos = append(infos, types.SystemPlugin{
			ID:   p.ID,
			Name: p.Name,
			Code: p.Code,
		})
	}
	return &types.TenantInfoResp{
		BaseDataInfo: types.BaseDataInfo{
			Code: 0,
			Msg:  l.svcCtx.Trans.Trans(l.ctx, i18n.Success),
		},
		Data: types.TenantInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: data.Id,
			},
			IsSuper:                     data.IsSuper,
			AfterSalesContact:           data.AfterSalesContact,
			LocationID:                  data.LocationID,
			LogSaveKeepDays:             data.LogSaveKeepDays,
			MaxAttendanceUserCount:      data.MaxAttendanceUserCount,
			MaxDeviceCount:              data.MaxDeviceCount,
			MaxUploadFileSize:           data.MaxUploadFileSize,
			MaxUserCount:                data.MaxUserCount,
			Principal:                   data.Principal,
			PrincipalContactInformation: data.PrincipalContactInformation,
			SaleContact:                 data.SaleContact,
			Status:                      data.Status,
			Name:                        data.Name,
			ServiceStartAt:              data.ServiceStartAt,
			ServiceEndAt:                data.ServiceEndAt,
			SystemPlugins:               infos,
			AiStatus:                    data.AiStatus,
			MaxConferenceAgendaTitle:    data.MaxConferenceAgendaTitle,
		},
	}, nil
}
