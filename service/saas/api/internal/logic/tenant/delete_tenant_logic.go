package tenant

import (
	"context"
	"errors"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/utils"
	"slices"

	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
)

type DeleteTenantLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteTenantLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteTenantLogic {
	return &DeleteTenantLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteTenantLogic) DeleteTenant(req *types.IDsReq) (resp *types.BaseDataInfo, err error) {

	// 禁止删除用户当前使用的租户
	if slices.Contains(req.Ids, utils.GetContextTenantID(l.ctx)) {
		return nil, errors.New("不可删除当前正在使用的租户")
	}

	result, err := l.svcCtx.SaasService.DeleteTenant(l.ctx, &services.IDsReq{
		Ids: req.Ids,
	})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, result.Msg), Data: types.EmptyData{}}, nil
}
