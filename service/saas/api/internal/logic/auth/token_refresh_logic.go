package auth

import (
	"context"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/utils"
	"phoenix/service/saas/utils/jwt"
	"time"

	"github.com/zeromicro/go-zero/core/logc"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"

	"github.com/zeromicro/go-zero/core/logx"
)

type TokenRefreshLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewTokenRefreshLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TokenRefreshLogic {
	return &TokenRefreshLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *TokenRefreshLogic) TokenRefresh(req *types.TokenRefreshReq) (resp *types.LoginResp, err error) {
	userId := utils.GetContextUserID(l.ctx)

	data, err := l.svcCtx.SaasService.GetUserById(l.ctx,
		&services.IDReq{Id: userId})
	if err != nil {
		return nil, err
	}

	tenantId := data.DefaultTenantId
	if req.TenantId != "" {
		tenantId = req.TenantId
	}

	// update user default tenant id
	_, err = l.svcCtx.SaasService.UpdateUserDefaultTenant(l.ctx,
		&services.UpdateUserDefaultTenantReq{
			UserId:   userId,
			TenantId: tenantId,
		})
	if err != nil {
		return nil, err
	}

	//token, err := jwt.NewJwtToken(l.svcCtx.Config.Auth.AccessSecret, data.Id, tenantId, time.Now().Unix(),
	//	l.svcCtx.Config.Auth.AccessExpire)
	// 查询默认租户信息
	tenantInfo, err := l.svcCtx.SaasService.GetTenantById(l.ctx, &services.IDReq{
		Id: tenantId,
	})
	if err != nil {
		logc.Error(l.ctx, "get tenant by user id error: ", err)
		return nil, err
	}
	currentTime := time.Now().Unix()
	token, err := jwt.NewJwtTokenWithOption(l.svcCtx.Config.Auth.AccessSecret,
		jwt.WithUserId(data.Id),
		jwt.WithTenantId(tenantId),
		jwt.WithIat(currentTime),
		jwt.WithExp(currentTime+l.svcCtx.Config.Auth.AccessExpire),
		jwt.WithRoleIds(""),
		jwt.WithDeviceKind(int64(utils.GetContextDeviceKind(l.ctx))), // TODO 传递登录设备类型
		jwt.WithIsAdminUnit(tenantInfo.IsSuper),
		jwt.WithIsSuperAdmin(data.IsSuperuser),
		jwt.WithIsVirtualUser(utils.GetContextIsVirtualUser(l.ctx)),
		jwt.WithMobile(utils.GetContextMobile(l.ctx)))

	if err != nil {
		return nil, err
	}

	// add token into database
	//expiredAt := time.Now().Add(time.Second * 259200).Unix()
	expiredAt := currentTime + l.svcCtx.Config.Auth.AccessExpire
	_, err = l.svcCtx.SaasService.CreateToken(l.ctx, &services.TokenInfo{
		Uid:       data.Id,
		Token:     token,
		Source:    "saas_user",
		Status:    true,
		ExpiredAt: expiredAt,
		TenantId:  tenantId,
	})

	if err != nil {
		return nil, err
	}

	resp = &types.LoginResp{
		BaseDataInfo: types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, i18n.Success)},
		Data: types.SuccessLoginInfo{
			User: types.SuccessLoginUserInfo{
				BaseIDInfo:      types.BaseIDInfo{Id: data.Id},
				Status:          data.Status,
				Username:        data.Username,
				Nickname:        data.Nickname,
				Mobile:          data.Mobile,
				Email:           data.Email,
				DefaultTenantId: tenantId,
				Avatar: types.AvatarInfo{
					Id:  data.Avatar.Id,
					Url: l.svcCtx.Config.FileServerConf.GenerateDownloadUrlNew(data.Avatar.Id, data.Avatar.Path, l.svcCtx.Config),
				},
			},
			AccessToken: token,
			Expire:      uint64(expiredAt),
		},
	}
	return resp, nil
}
