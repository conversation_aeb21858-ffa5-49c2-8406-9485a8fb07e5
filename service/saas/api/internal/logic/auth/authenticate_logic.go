package auth

import (
	"context"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type AuthenticateLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewAuthenticateLogic(ctx context.Context, svcCtx *svc.ServiceContext) *AuthenticateLogic {
	return &AuthenticateLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *AuthenticateLogic) Authenticate(req *types.AuthenticateReq) (resp *types.AuthenticateResp, err error) {
	resp = &types.AuthenticateResp{}

	for _, a := range l.svcCtx.Authenticators {
		user, err := a.CheckToken(l.ctx, req.To<PERSON>)
		if err == nil && user != nil {

			userData, err := l.svcCtx.SaasService.GetUserById(l.ctx, &services.IDReq{Id: user.GetID()})
			if err != nil || !userData.Status {
				resp.Data = types.AuthenticateResult{
					IsValid:   false,
					IsCreated: false,
					UserID:    "",
					TenantID:  "",
					User:      types.SuccessLoginUserInfo{},
				}

			} else {
				resp.Data = types.AuthenticateResult{
					IsValid:   true,
					IsCreated: false,
					UserID:    user.GetID(),
					TenantID:  user.GetTenantID(),
					User: types.SuccessLoginUserInfo{
						BaseIDInfo: types.BaseIDInfo{
							Id: userData.Id,
						},
						Status:          userData.Status,
						DefaultTenantId: userData.DefaultTenantId,
						Username:        userData.Username,
						Nickname:        userData.Nickname,
						Mobile:          userData.Mobile,
						Email:           userData.Email,
						Gender:          userData.Gender,
						Post:            userData.Post,
						Avatar: types.AvatarInfo{
							Id:  userData.Avatar.Id,
							Url: l.svcCtx.Config.FileServerConf.GenerateDownloadUrlNew(userData.Avatar.Id, userData.Avatar.Path, l.svcCtx.Config),
						},
					},
				}

				return resp, nil
			}
		}
	}
	resp.Data = types.AuthenticateResult{
		IsValid:   false,
		IsCreated: false,
		UserID:    "",
		TenantID:  "",
		User:      types.SuccessLoginUserInfo{},
	}
	return resp, nil
}
