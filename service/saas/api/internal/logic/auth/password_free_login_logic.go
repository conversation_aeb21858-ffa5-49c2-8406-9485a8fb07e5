package auth

import (
	"context"
	"fmt"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/utils/jwt"
	"phoenix/service/saas/utils/uuidx"
	"time"

	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type PasswordFreeLoginLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewPasswordFreeLoginLogic(ctx context.Context, svcCtx *svc.ServiceContext) *PasswordFreeLoginLogic {
	return &PasswordFreeLoginLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

// PasswordFreeLogin 实现免密码登录功能
// 主要流程：
// 1. 通过平台用户ID或直接用户ID获取用户信息
// 2. 验证用户状态
// 3. 获取用户关联租户
// 4. 处理默认租户逻辑
// 5. 生成JWT令牌
// 6. 创建并返回登录响应
func (l *PasswordFreeLoginLogic) PasswordFreeLogin(req *types.PasswordFreeLoginReq) (resp *types.LoginResp, err error) {

	// 如果是虚拟用户，则处理虚拟用户逻辑
	if req.IsVirtualUser {
		return l.generateVirtualLoginInfo(req)
	}

	// 初始化用户ID
	uid := req.UserID

	// 如果提供了平台用户ID，则通过平台用户ID获取实际用户ID
	if req.PlatUserId != "" {
		pu, err := l.svcCtx.SaasService.GetPlatUserByPlatUserIDAndPlatCode(l.ctx, req.PlatUserId, req.PlatCode)
		if err != nil {
			return nil, errorx.NewCodeInvalidArgumentError("用户不存在或被禁用")
		}
		uid = pu.UserID
	}

	// 通过用户ID获取用户详细信息
	user, err := l.svcCtx.SaasService.GetUserById(l.ctx, &services.IDReq{Id: uid})
	if err != nil || !user.Status {
		return nil, errorx.NewCodeInvalidArgumentError("用户不存在或被禁用")
	}

	// 获取用户关联的所有租户
	tenants, err := l.svcCtx.SaasService.GetTenantByUserId(l.ctx,
		&services.IDReq{
			Id: user.Id,
		})
	if err != nil {
		return nil, errorx.NewCodeInvalidArgumentError("登录失败")
	}
	if tenants.Total == 0 {
		return nil, errorx.NewCodeInvalidArgumentError("无可用租户")
	}

	// 租户检查：查找用户默认租户
	var tenantInfo services.TenantInfo
	if user.DefaultTenantId != "" {
		for i, datum := range tenants.Data {
			if datum.Id != user.DefaultTenantId {
				continue
			}
			tenantInfo = *tenants.Data[i]
			break
		}
	}

	// 如果默认租户未找到，则使用第一个租户并更新用户默认租户
	if tenantInfo.Id == "" {
		tenantInfo = *tenants.Data[0]
		if _, err := l.svcCtx.SaasService.UpdateUserDefaultTenant(l.ctx, &services.UpdateUserDefaultTenantReq{
			UserId:   user.Id,
			TenantId: tenantInfo.Id,
		}); err != nil {
			return nil, err
		}
	}

	// 生成并存储令牌
	token, err := l.generateAndStoreToken(*user, tenantInfo, false)
	if err != nil {
		return nil, err
	}

	// 准备系统插件信息
	ps := make([]types.SystemPlugin, 0, len(tenantInfo.SystemPlugins))
	for _, plugin := range tenantInfo.SystemPlugins {
		ps = append(ps, types.SystemPlugin{
			ID:   plugin.ID,
			Name: plugin.Name,
			Code: plugin.Code,
		})
	}

	// 准备租户信息列表
	tenantInfos := make([]types.TenantInfo, 0, len(tenants.Data))
	for _, tenant := range tenants.Data {
		tenantInfos = append(tenantInfos, types.TenantInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: tenant.Id,
			},
			Name:                   tenant.Name,
			Uuid:                   tenant.Uuid,
			Key:                    tenant.Key,
			Secret:                 tenant.Secret,
			IsSuper:                tenant.IsSuper,
			AfterSalesContact:      tenant.AfterSalesContact,
			LocationID:             tenant.LocationID,
			LogSaveKeepDays:        tenant.LogSaveKeepDays,
			MaxAttendanceUserCount: tenant.MaxAttendanceUserCount,
			MaxDeviceCount:         tenant.MaxDeviceCount,
			MaxUploadFileSize:      tenant.MaxUploadFileSize,
			MaxUserCount:           tenant.MaxUserCount,
		})
	}

	// 构建并返回登录响应
	resp = &types.LoginResp{
		BaseDataInfo: types.BaseDataInfo{},
		Data: types.SuccessLoginInfo{
			User: types.SuccessLoginUserInfo{
				BaseIDInfo:      types.BaseIDInfo{Id: user.Id},
				Status:          user.Status,
				Username:        user.Username,
				Nickname:        user.Nickname,
				Mobile:          user.Mobile,
				Email:           user.Email,
				Gender:          user.Gender,
				Post:            user.Post,
				DefaultTenantId: tenantInfo.Id,
				Avatar: types.AvatarInfo{
					Id:  user.Avatar.Id,
					Url: l.svcCtx.Config.FileServerConf.GenerateDownloadUrlNew(user.Avatar.Id, user.Avatar.Path, l.svcCtx.Config),
				},
				Kind: user.Kind,
			},
			AccessToken:   token,
			Expire:        uint64(time.Now().Unix() + l.svcCtx.Config.Auth.AccessExpire),
			SystemPlugins: ps,
			TenantInfos:   tenantInfos,
		},
	}
	return resp, nil
}

// generateAndStoreToken 生成JWT令牌并存储到数据库和Redis
// 参数:
//   - userId: 用户ID
//   - tenantInfo: 租户信息
//
// 返回:
//   - string: 生成的JWT令牌
//   - error: 错误信息
func (l *PasswordFreeLoginLogic) generateAndStoreToken(user services.UserInfo, tenantInfo services.TenantInfo, isVirtualUser bool) (string, error) {
	currentTime := time.Now().Unix()
	token, err := jwt.NewJwtTokenWithOption(l.svcCtx.Config.Auth.AccessSecret,
		jwt.WithUserId(user.Id),
		jwt.WithTenantId(tenantInfo.Id),
		jwt.WithIat(currentTime),
		jwt.WithExp(currentTime+l.svcCtx.Config.Auth.AccessExpire),
		jwt.WithRoleIds(""),
		jwt.WithIsAdminUnit(tenantInfo.IsSuper),
		jwt.WithIsSuperAdmin(tenantInfo.IsSuper),
		jwt.WithIsVirtualUser(isVirtualUser),
		jwt.WithMobile(user.Mobile),
	)

	if err != nil {
		return "", errorx.NewCodeInvalidArgumentError("登录失败")
	}

	// 创建并存储令牌信息
	expiredAt := l.svcCtx.Config.Auth.AccessExpire
	_, err = l.svcCtx.SaasService.CreateToken(l.ctx, &services.TokenInfo{
		Uid:       user.Mobile,
		Token:     token,
		Source:    "saas_user",
		Status:    true,
		ExpiredAt: currentTime + expiredAt,
		TenantId:  tenantInfo.Id,
	})
	if err != nil {
		logx.Errorw("fail to create token", logx.Field("detail", err))
		return "", errorx.NewCodeInvalidArgumentError("登录失败")
	}

	// 将令牌存入Redis
	if err := l.svcCtx.Redis.Setex(fmt.Sprintf("%s:%s", "validToken", token), "", int(expiredAt)); err != nil {
		return "", err
	}

	return token, nil
}

func (l *PasswordFreeLoginLogic) generateVirtualLoginInfo(req *types.PasswordFreeLoginReq) (resp *types.LoginResp, err error) {
	virutalTenantInfo := &services.TenantInfo{
		Id: "virtual_tenant_id",
	}

	user := &services.UserInfo{
		Id:       uuidx.NewUUID().String(),
		Username: req.Mobile,
		Nickname: req.Mobile,
		Mobile:   req.Mobile,
		Email:    "virtual_email",
		Gender:   "virtual_gender",
		Post:     "virtual_post",
		Status:   true,
	}
	token, err := l.generateAndStoreToken(*user, *virutalTenantInfo, true)
	if err != nil {
		return nil, err
	}

	// 构建并返回登录响应
	resp = &types.LoginResp{
		BaseDataInfo: types.BaseDataInfo{},
		Data: types.SuccessLoginInfo{
			User: types.SuccessLoginUserInfo{
				BaseIDInfo:      types.BaseIDInfo{Id: user.Id},
				Status:          user.Status,
				Username:        user.Username,
				Nickname:        user.Nickname,
				Mobile:          user.Mobile,
				Email:           user.Email,
				Gender:          user.Gender,
				Post:            user.Post,
				DefaultTenantId: virutalTenantInfo.Id,

				Kind: user.Kind,
			},
			AccessToken:   token,
			Expire:        uint64(time.Now().Unix() + l.svcCtx.Config.Auth.AccessExpire),
			SystemPlugins: []types.SystemPlugin{},
		},
	}

	return resp, nil
}
