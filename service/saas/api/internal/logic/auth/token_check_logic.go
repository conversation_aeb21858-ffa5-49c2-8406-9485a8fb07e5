package auth

import (
	"context"
	"github.com/golang-jwt/jwt/v4"
	"github.com/zeromicro/go-zero/core/logx"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type TokenCheckLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewTokenCheckLogic(ctx context.Context, svcCtx *svc.ServiceContext) *TokenCheckLogic {
	return &TokenCheckLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *TokenCheckLogic) TokenCheck(req *types.TokenCheckReq) (resp *types.TokenCheckResp, err error) {
	checkResult := false

	// 优先在外部对接的用户中进行验证
	checkResult, err = l.CheckInRemoteService(req.Token)

	if !checkResult {
		// 外部对接的用户校验失败，则在本地进行校验
		checkResult, err = l.CheckInLocalService(req.Token)
	}
	return &types.TokenCheckResp{
		BaseDataInfo: types.BaseDataInfo{
			Code: 200,
			Msg:  "请求成功",
		},
		Data: types.TokenCheckInfo{IsValid: checkResult},
	}, nil
}

func (l *TokenCheckLogic) CheckInLocalService(token string) (checkResult bool, err error) {

	// check jwt blacklist
	jwtResult, err := l.svcCtx.Redis.Get("token_" + token)

	if err != nil {
		return false, err
	}

	if jwtResult != "1" {
		//token not in blacklist
		checkResult = true

		// Set defaults
		claims := jwt.MapClaims{}

		// parse token
		_, err := jwt.NewParser().ParseWithClaims(token, claims, func(t *jwt.Token) (interface{}, error) {
			return l.svcCtx.Config.Auth.AccessSecret, nil
		})

		if err != nil {
			checkResult = false
		} else {
			checkResult = true
		}

	} else {
		//token in blacklist
		checkResult = false
	}

	return checkResult, nil
}

func (l *TokenCheckLogic) CheckInRemoteService(token string) (checkResult bool, err error) {
	checkResult = false
	return checkResult, nil
}
