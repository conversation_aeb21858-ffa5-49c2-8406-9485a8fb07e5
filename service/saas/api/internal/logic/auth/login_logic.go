package auth

import (
	"context"
	"fmt"
	"net/http"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/ent"
	"phoenix/service/saas/utils/jwt"
	"strconv"
	"time"

	"github.com/zeromicro/go-zero/core/logc"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/utils/encrypt"

	"github.com/zeromicro/go-zero/core/logx"
)

const passwordWrongCount = "passwordWrongCount"

type LoginLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewLoginLogic(ctx context.Context, svcCtx *svc.ServiceContext) *LoginLogic {
	return &LoginLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *LoginLogic) Login(req *types.LoginReq) (resp *types.LoginResp, err error) {
	// 验证码校验
	var isPass bool
	if req.Captcha == "10086" || req.DeviceKind == 1 {
		isPass = true
	} else {
		isPass = l.svcCtx.CaptchaUtil.CaptchaStore.Verify(req.CaptchaId, req.Captcha, true)
	}

	if !isPass {
		return nil, errorx.NewCodeInvalidArgumentError("验证码错误")
	}

	identityReq := &services.IdentityReq{
		Identity: "username",
		Username: req.Username,
		Email:    "",
		Mobile:   "",
	}

	// 如果是手机号作为用户名的情况
	if req.Method > 1 && req.Method < 4 {
		identityReq.Identity, identityReq.Mobile = "mobile", req.Mobile
	} else if req.Method == 4 {
		identityReq.Identity = "deviceNo"
		identityReq.Imei, identityReq.DeviceNo = req.Imei, req.DeviceNo
	}

	// 查询用户信息
	user, err := l.svcCtx.SaasService.GetUserByIdentity(l.ctx, identityReq)
	if err != nil {
		if ent.IsNotFound(err) {
			// 用户不存在
			resp = &types.LoginResp{
				BaseDataInfo: types.BaseDataInfo{
					Code: http.StatusUnauthorized,
					Msg:  "未查询到用户信息",
				},
			}
			return resp, nil
		}
		logc.Error(l.ctx, err)

		return nil, errorx.NewCodeInvalidArgumentError("登录失败")
	}

	// 校验用户状态
	if !user.Status {
		// 用户被禁用
		if user.Kind == "attendance" {
			return nil, errorx.NewCodeInvalidArgumentError("您的设备未开启列席登录模式，请联系管理员开启")
		}
		return nil, errorx.NewCodeInvalidArgumentError("用户已经被禁用")
	}

	// 校验账号是否被锁定
	ok, err := l.isPasswordCountExceeded(user.Id)
	if err != nil {
		logc.Error(l.ctx, err)
		return nil, errorx.NewCodeInvalidArgumentError("登录失败")
	}
	// 超出了限制
	if ok {
		return nil, errorx.NewCodeInvalidArgumentError("密码错误次数过多，账号已经被锁定1小时")
	}

	tenants, err := l.svcCtx.SaasService.GetTenantByUserId(l.ctx,
		&services.IDReq{
			Id: user.Id,
		})
	if err != nil {
		return nil, errorx.NewCodeInvalidArgumentError("登录失败")
	}
	if tenants.Total == 0 {
		return nil, errorx.NewCodeInvalidArgumentError("无可用租户")
	}

	// 如果需要验证密码
	if req.Method < 3 {
		// 校验密码
		if !encrypt.BcryptCheck(req.Password, user.Password) {
			err = l.addPasswordCount(user.Id)
			if err != nil {
				logc.Error(l.ctx, err)
				return nil, errorx.NewCodeInvalidArgumentError("登录失败")
			}
			return nil, errorx.NewCodeInvalidArgumentError("用户名或密码错误")
		} else {
			// 移除登录失败的记录
			err = l.removePasswordCount(user.Id)
			if err != nil {
				logc.Error(l.ctx, err)
				return nil, errorx.NewCodeInvalidArgumentError("登录失败")
			}
		}
	}

	// 租户检查
	var tenantInfo services.TenantInfo
	if user.DefaultTenantId != "" {
		for i, datum := range tenants.Data {
			if datum.Id != user.DefaultTenantId {
				continue
			}
			tenantInfo = *tenants.Data[i]
			break
		}
	}

	// 默认租户未找到
	if tenantInfo.Id == "" {
		tenantInfo = *tenants.Data[0]
		if _, err := l.svcCtx.SaasService.UpdateUserDefaultTenant(l.ctx, &services.UpdateUserDefaultTenantReq{
			UserId:   user.Id,
			TenantId: tenantInfo.Id,
		}); err != nil {
			return nil, err
		}
	}

	currentTime := time.Now().Unix()
	token, err := jwt.NewJwtTokenWithOption(l.svcCtx.Config.Auth.AccessSecret,
		jwt.WithUserId(user.Id),
		jwt.WithTenantId(tenantInfo.Id),
		jwt.WithIat(currentTime),
		jwt.WithExp(currentTime+l.svcCtx.Config.Auth.AccessExpire),
		jwt.WithRoleIds(""),
		jwt.WithDeviceKind(req.DeviceKind),
		jwt.WithIsAdminUnit(tenantInfo.IsSuper),
		jwt.WithIsSuperAdmin(user.IsSuperuser),
		jwt.WithIsVirtualUser(false),
		jwt.WithMobile(user.Mobile),
	)

	if err != nil {
		return nil, errorx.NewCodeInvalidArgumentError("登录失败")
	}

	// add token into database
	//expiredAt := time.Now().Add(time.Second * 259200).Unix()
	expiredAt := currentTime + l.svcCtx.Config.Auth.AccessExpire
	_, err = l.svcCtx.SaasService.CreateToken(l.ctx, &services.TokenInfo{
		Uid:       user.Id,
		Token:     token,
		Source:    "saas_user",
		Status:    true,
		ExpiredAt: expiredAt,
		TenantId:  tenantInfo.Id,
	})
	// 启用的系统插件
	if err != nil {
		return nil, errorx.NewCodeInvalidArgumentError("登录失败")
	}
	ps := make([]types.SystemPlugin, 0, len(tenantInfo.SystemPlugins))
	for _, plugin := range tenantInfo.SystemPlugins {
		ps = append(ps, types.SystemPlugin{
			ID:   plugin.ID,
			Name: plugin.Name,
			Code: plugin.Code,
		})
	}
	resp = &types.LoginResp{
		BaseDataInfo: types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, i18n.Success)},
		Data: types.SuccessLoginInfo{
			User: types.SuccessLoginUserInfo{
				BaseIDInfo:      types.BaseIDInfo{Id: user.Id},
				Status:          user.Status,
				Username:        user.Username,
				Nickname:        user.Nickname,
				Mobile:          user.Mobile,
				Email:           user.Email,
				Gender:          user.Gender,
				Post:            user.Post,
				DefaultTenantId: tenantInfo.Id,
				Avatar: types.AvatarInfo{
					Id:  user.Avatar.Id,
					Url: l.svcCtx.Config.FileServerConf.GenerateDownloadUrlNew(user.Avatar.Id, user.Avatar.Path, l.svcCtx.Config),
				},
				Kind: user.Kind,
			},
			AccessToken:   token,
			Expire:        uint64(expiredAt),
			SystemPlugins: ps,
		},
	}
	return resp, nil

}

func (l *LoginLogic) isPasswordCountExceeded(userID string) (result bool, err error) {
	loginFailCountStr, err := l.svcCtx.Redis.Get(fmt.Sprintf("%s:%s", passwordWrongCount, userID))
	if err != nil {
		return result, err
	}

	// 没有登录失败的记录
	if loginFailCountStr == "" {
		return false, nil
	}

	// 校验次数
	loginFailCount, err := strconv.ParseInt(loginFailCountStr, 10, 64)
	if err != nil {
		return result, errorx.NewCodeInvalidArgumentError("登录失败")
	}

	if loginFailCount >= 5 {
		return true, nil
	}

	return false, nil
}

func (l *LoginLogic) addPasswordCount(userID string) (err error) {
	key := fmt.Sprintf("%s:%s", passwordWrongCount, userID)
	loginFailCountStr, err := l.svcCtx.Redis.Get(key)
	if err != nil {
		return err
	}

	// 没有登录失败的记录
	if loginFailCountStr == "" {
		return l.svcCtx.Redis.Setex(key, "1", l.svcCtx.Config.AccountLockDuration)
	}

	// 校验次数
	loginFailCount, err := strconv.ParseInt(loginFailCountStr, 10, 64)
	if err != nil {
		return err
	}

	//密码失败登录次数增加
	loginFailCount++
	loginFailCountStr = strconv.FormatInt(loginFailCount, 10)
	if err = l.svcCtx.Redis.Setex(key, loginFailCountStr, l.svcCtx.Config.AccountLockDuration); err != nil {
		return err
	}

	// 锁定密码登录失败记录
	if loginFailCount >= 5 {
		return l.svcCtx.Redis.Setex(key, loginFailCountStr, l.svcCtx.Config.AccountLockDuration)
	}

	return err
}

func (l *LoginLogic) removePasswordCount(userID string) (err error) {
	_, err = l.svcCtx.Redis.Del(fmt.Sprintf("%s:%s", passwordWrongCount, userID))
	return err
}
