package tenantuserinfo

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type GetTenantUserInfoByIdLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetTenantUserInfoByIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTenantUserInfoByIdLogic {
	return &GetTenantUserInfoByIdLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetTenantUserInfoByIdLogic) GetTenantUserInfoById(req *types.GetTenantUserInfoByIdReq) (resp *types.TenantUserInfoInfoResp, err error) {
	data, err := l.svcCtx.SaasService.GetTenantUsers(l.ctx,
		&services.GetTenantUsersReq{
			TenantId: req.TenantId,
			Search:   "",
			UserId:   req.Id,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	resp = &types.TenantUserInfoInfoResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	if len(data.Data) == 0 {
		return nil, errorx.NewCodeError(404, "用户不存在").WithCode(404)

	}

	v := data.Data[0]
	resp.Data = types.TenantUser{
		UserInfo: types.UserInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: v.Id,
			},
			Status:   v.Status,
			Username: v.Username,
			//Password: v.Password,
			Nickname: v.Nickname,
			Mobile:   v.Mobile,
			Email:    v.Email,
			Gender:   v.Gender,
			Post:     v.Post,
			Avatar: types.AvatarInfo{
				Id:  v.Avatar.Id,
				Url: l.svcCtx.Config.FileServerConf.GenerateDownloadUrlNew(v.Avatar.Id, v.Avatar.Path, l.svcCtx.Config),
			},
		},
		Extra: v.Extra,
		Sort:  uint32(v.Sort),
	}
	return resp, nil
}
