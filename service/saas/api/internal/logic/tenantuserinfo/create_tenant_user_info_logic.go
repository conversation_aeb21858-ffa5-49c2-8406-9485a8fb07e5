package tenantuserinfo

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type CreateTenantUserInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateTenantUserInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateTenantUserInfoLogic {
	return &CreateTenantUserInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateTenantUserInfoLogic) CreateTenantUserInfo(req *types.AddTenantUserReq) (resp *types.BaseDataInfo, err error) {
	user, err := l.svcCtx.SaasService.CreateUser(l.ctx,
		&services.UserInfo{
			Status:          req.Status,
			Username:        req.Username,
			Password:        req.Password,
			Nickname:        req.Nickname,
			Mobile:          req.Mobile,
			Email:           req.Email,
			Gender:          req.Gender,
			Post:            req.Post,
			DefaultTenantId: req.TenantId,
			Avatar: &services.Avatar{
				Id: req.AvatarID,
			},
			Kind:     req.Kind,
			Imei:     req.Imei,
			DeviceNo: req.DeviceNo,
		})

	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	data, err := l.svcCtx.SaasService.CreateTenantUserInfo(l.ctx,
		&services.TenantUserInfoInfo{
			Sort:     req.Sort,
			TenantId: req.TenantId,
			UserId:   user.Id,
			Extra:    req.Extra,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, data.Msg), Data: types.EmptyData{}}, nil
}
