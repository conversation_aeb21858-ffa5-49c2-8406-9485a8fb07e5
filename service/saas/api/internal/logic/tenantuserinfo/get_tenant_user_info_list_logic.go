package tenantuserinfo

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/ent"
)

type GetTenantUserInfoListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetTenantUserInfoListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetTenantUserInfoListLogic {
	return &GetTenantUserInfoListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetTenantUserInfoListLogic) GetTenantUserInfoList(req *types.TenantUserInfoListReq) (resp *types.TenantUserInfoListResp, err error) {
	data, err := l.svcCtx.SaasService.GetTenantUsers(l.ctx,
		&services.GetTenantUsersReq{
			TenantId: req.TenantId,
			Search:   req.Search,
			Kind:     req.Kind,
			PageInfoReq: services.PageInfoReq{
				Page:     req.Page,
				PageSize: req.PageSize,
				NoPage:   req.NoPage,
			},
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	resp = &types.TenantUserInfoListResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	resp.Data.Total = data.Total
	resp.Data.Data = make([]types.TenantUser, 0)

	for _, v := range data.Data {
		resp.Data.Data = append(resp.Data.Data,
			types.TenantUser{
				UserInfo: types.UserInfo{
					BaseIDInfo: types.BaseIDInfo{
						Id: v.Id,
					},
					Status:   v.Status,
					Username: v.Username,
					//Password: v.Password,
					Nickname: v.Nickname,
					Mobile:   v.Mobile,
					Email:    v.Email,
					Gender:   v.Gender,
					Post:     v.Post,
					Avatar: types.AvatarInfo{
						Id:  v.Avatar.Id,
						Url: l.svcCtx.Config.FileServerConf.GenerateDownloadUrlNew(v.Avatar.Id, v.Avatar.Path, l.svcCtx.Config),
					},
					Kind:     v.Kind,
					Imei:     v.Imei,
					DeviceNo: v.DeviceNo,
				},
				Extra: v.Extra,
				Sort:  uint32(v.Sort),
			})
	}
	return resp, nil
}
func GetAvatar(avatar *ent.File) *services.Avatar {
	if avatar == nil {
		return &services.Avatar{}
	}
	return &services.Avatar{
		Id:         avatar.ID,
		Name:       avatar.Name,
		OriginName: avatar.OriginName,
		Path:       avatar.Path,
		OpenStatus: uint32(avatar.OpenStatus),
	}
}
