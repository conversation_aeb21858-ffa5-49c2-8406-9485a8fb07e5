package tenantuserinfo

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type UpdateTenantUserInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdateTenantUserInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdateTenantUserInfoLogic {
	return &UpdateTenantUserInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdateTenantUserInfoLogic) UpdateTenantUserInfo(req *types.UpdateTenantUserReq) (resp *types.BaseDataInfo, err error) {

	_, err = l.svcCtx.SaasService.UpdateUser(l.ctx,
		&services.UserInfo{
			Id:       req.Id,
			Status:   req.Status,
			Username: req.Username,
			//Password: req.Password,
			Nickname: req.Nickname,
			Mobile:   req.Mobile,
			Email:    req.Email,
			Gender:   req.Gender,
			Post:     req.Post,
			Avatar:   &services.Avatar{Id: req.AvatarID},
			Kind:     req.Kind,
			DeviceNo: req.DeviceNo,
		})

	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	data, err := l.svcCtx.SaasService.UpdateTenantUserInfo(l.ctx,
		&services.TenantUserInfoInfo{
			Sort:     req.Sort,
			TenantId: req.TenantId,
			UserId:   req.Id,
			Extra:    req.Extra,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, data.Msg), Data: types.EmptyData{}}, nil
}
