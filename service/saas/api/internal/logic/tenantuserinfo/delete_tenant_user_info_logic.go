package tenantuserinfo

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type DeleteTenantUserInfoLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewDeleteTenantUserInfoLogic(ctx context.Context, svcCtx *svc.ServiceContext) *DeleteTenantUserInfoLogic {
	return &DeleteTenantUserInfoLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *DeleteTenantUserInfoLogic) DeleteTenantUserInfo(req *types.DeleteTenantUserInfoByIdsReq) (resp *types.BaseDataInfo, err error) {
	result, err := l.svcCtx.SaasService.DeleteTenantUserInfo(l.ctx, &services.DeleteTenantUserInfo{
		TenantId: req.TenantId,
		UserIds:  req.Ids,
	})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, result.Msg), Data: types.EmptyData{}}, nil
}
