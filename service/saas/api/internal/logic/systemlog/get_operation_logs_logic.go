package systemlog

import (
	"context"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetOperationLogsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetOperationLogsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetOperationLogsLogic {
	return &GetOperationLogsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetOperationLogsLogic) GetOperationLogs(req *types.GetOperationLogsReq) (resp *types.GetOperationLogsResp, err error) {
	var pageParam mapper.SystemOperationLogInfoViewQueryPageParam
	if err = utils.StructCopy(l.ctx, &pageParam, req); err != nil {
		return nil, err
	}
	pageParam.TenantID = utils.GetContextTenantID(l.ctx)

	// 查询所有当前用户信息,普通用户需要排除超管的日志
	userClient := mapper.NewUserClient(l.svcCtx.GormDB)
	user, err := userClient.FindById(l.ctx, utils.GetContextUserID(l.ctx))
	if err != nil {
		return nil, err
	}
	if !user.IsSuperuser {
		ids, err := userClient.GetAllAdminUserId(l.ctx)
		if err != nil {
			return nil, err
		}
		pageParam.NotExistUserId = ids
	}

	client := mapper.NewSystemOperationLogInfoViewClient(l.svcCtx.GormDB)
	list, total, err := client.QueryPage(l.ctx, pageParam)
	if err != nil {
		return nil, err
	}
	rs, err := utils.ArrayCopy(l.ctx, types.GetOperationLogItemResp{}, list)
	if err != nil {
		return nil, err
	}

	return &types.GetOperationLogsResp{
		BaseDataInfo: types.BaseDataInfo{},
		Data: types.GetOperationLogsInfo{
			PageInfo: types.PageInfo{
				Page:     pageParam.Page,
				PageSize: pageParam.PageSize,
			},
			BaseListInfo: types.BaseListInfo{
				Total: uint64(total),
			},
			Data: rs,
		},
	}, nil
}
