package systemlog

import (
	"context"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"phoenix/service/saas/model/gorm/mapper"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type SaveSystemLoginLogLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSaveSystemLoginLogLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SaveSystemLoginLogLogic {
	return &SaveSystemLoginLogLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SaveSystemLoginLogLogic) SaveSystemLoginLog(req *types.SystemLoginLogCreateReq) (resp *types.SystemLoginLogCreateResp, err error) {
	loginLogModel := l.loginLogReqToLoginLogModel(req.Logs)
	loginLogClient := mapper.NewLoginLogClient(l.svcCtx.GormDB)
	// 直接存入数据
	err = loginLogClient.BatchInsertLoginLogs(loginLogModel)
	if err != nil {
		return nil, err
	}
	return &types.SystemLoginLogCreateResp{}, nil
}

// 数据转换
func (l *SaveSystemLoginLogLogic) loginLogReqToLoginLogModel(logs []types.SystemLoginLogCreateReqInfo) (loginLogModel []mapper.LoginLogModel) {
	for _, v := range logs {
		loginLog := mapper.LoginLogModel{
			UserID:        v.UserID,
			LoginUserName: v.LoginUsername,
			TenantID:      v.TenantID,
			IP:            v.IP,
			CreatedAt:     time.UnixMilli(v.CreatedAt),
			DeviceKind:    v.DeviceKind,
		}
		loginLogModel = append(loginLogModel, loginLog)
	}
	return loginLogModel
}
