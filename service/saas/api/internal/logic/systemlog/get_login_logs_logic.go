package systemlog

import (
	"context"
	"phoenix/service/saas/model/gorm/mapper"
	"phoenix/service/saas/utils"

	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/core/logx"
)

type GetLoginLogsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetLoginLogsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetLoginLogsLogic {
	return &GetLoginLogsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetLoginLogsLogic) GetLoginLogs(req *types.GetLoginLogsReq) (resp *types.GetLoginLogsResp, err error) {
	var pageParam mapper.SystemLoginLogInfoViewQueryPageParam
	if err = utils.StructCopy(l.ctx, &pageParam, req); err != nil {
		return nil, err
	}
	pageParam.TenantID = utils.GetContextTenantID(l.ctx)

	// 查询所有当前用户信息,普通用户需要排除超管的日志
	userClient := mapper.NewUserClient(l.svcCtx.GormDB)
	user, err := userClient.FindById(l.ctx, utils.GetContextUserID(l.ctx))
	if err != nil {
		return nil, err
	}
	if !user.IsSuperuser {
		ids, err := userClient.GetAllAdminUserId(l.ctx)
		if err != nil {
			return nil, err
		}
		pageParam.NotExistUserId = ids
	}

	client := mapper.NewSystemLoginLogInfoViewClient(l.svcCtx.GormDB)
	list, total, err := client.QueryPage(l.ctx, pageParam)
	if err != nil {
		return nil, err
	}
	rs, err := utils.ArrayCopy(l.ctx, types.GetLoginLogItemResp{}, list)
	if err != nil {
		return nil, err
	}

	return &types.GetLoginLogsResp{
		BaseDataInfo: types.BaseDataInfo{},
		Data: types.GetLoginLogsInfo{
			PageInfo: types.PageInfo{
				Page:     req.Page,
				PageSize: req.PageSize,
				NoPage:   req.NoPage,
			},
			BaseListInfo: types.BaseListInfo{
				Total: uint64(total),
			},
			Data: rs,
		},
	}, nil
}
