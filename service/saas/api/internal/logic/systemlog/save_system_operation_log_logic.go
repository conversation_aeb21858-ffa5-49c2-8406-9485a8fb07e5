package systemlog

import (
	"context"
	"encoding/json"
	"github.com/zeromicro/go-zero/core/logx"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type SaveSystemOperationLogLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewSaveSystemOperationLogLogic(ctx context.Context, svcCtx *svc.ServiceContext) *SaveSystemOperationLogLogic {
	return &SaveSystemOperationLogLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *SaveSystemOperationLogLogic) SaveSystemOperationLog(req *types.SystemOperationLogCreateReq) (resp *types.SystemOperationLogCreateResp, err error) {
	// 将日志数据存储到Redis list
	if err := l.storeLogsToRedis(req.Logs); err != nil {
		logx.Errorf("存储操作日志到Redis失败: %v", err)
		return nil, err
	}
	return &types.SystemOperationLogCreateResp{}, nil
}

// 将日志数据存储到Redis list
func (l *SaveSystemOperationLogLogic) storeLogsToRedis(logs []types.SystemOperationLogCreateReqInfo) error {
	for _, log := range logs {
		// 序列化日志数据
		logBytes, err := json.Marshal(log)
		if err != nil {
			logx.Errorf("序列化操作日志失败: %v, 日志: %+v", err, log)
			continue
		}

		// 将日志推送到Redis list
		_, err = l.svcCtx.Redis.Lpush("system_operation_logs", string(logBytes))
		if err != nil {
			logx.Errorf("Redis推送操作日志失败: %v, 日志: %+v", err, log)
			return err
		}
	}

	return nil
}
