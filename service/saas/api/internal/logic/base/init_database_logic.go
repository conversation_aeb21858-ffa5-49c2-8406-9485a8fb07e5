package base

import (
	"context"
	"errors"
	"github.com/suyuan32/simple-admin-common/enum/errorcode"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
	"time"

	"github.com/zeromicro/go-zero/core/logx"
)

type InitDatabaseLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewInitDatabaseLogic(ctx context.Context, svcCtx *svc.ServiceContext) *InitDatabaseLogic {
	return &InitDatabaseLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *InitDatabaseLogic) InitDatabase() (resp *types.BaseDataInfo, err error) {
	result, err := l.svcCtx.SaasService.InitDatabase(l.ctx, &services.Empty{})
	if err != nil && !errors.Is(err, status.Error(codes.DeadlineExceeded, "context deadline exceeded")) {
		return nil, err
	} else if errors.Is(err, status.Error(codes.DeadlineExceeded, "context deadline exceeded")) {
		for {
			// wait 10 second for initialization
			time.Sleep(time.Second * 5)
			if initState, err := l.svcCtx.Redis.Get("database_init_state"); err == nil {
				if initState == "1" {
					return nil, errorx.NewCodeError(errorcode.InvalidArgument,
						l.svcCtx.Trans.Trans(l.ctx, i18n.AlreadyInit))
				}
			} else {
				return nil, errorx.NewCodeError(errorcode.Internal,
					l.svcCtx.Trans.Trans(l.ctx, i18n.RedisError))
			}

			if errMsg, err := l.svcCtx.Redis.Get("database_error_msg"); err == nil {
				if errMsg != "" {
					return nil, errorx.NewCodeError(errorcode.Internal, errMsg)
				}
			} else {
				return nil, errorx.NewCodeError(errorcode.Internal,
					l.svcCtx.Trans.Trans(l.ctx, i18n.RedisError))
			}
		}
	}

	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, result.Msg)}, nil
}
