package base

import (
	"context"
	"encoding/json"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type UpdatePermissionsLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewUpdatePermissionsLogic(ctx context.Context, svcCtx *svc.ServiceContext) *UpdatePermissionsLogic {
	return &UpdatePermissionsLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *UpdatePermissionsLogic) UpdatePermissions(req *types.UpdatePermissionsReq) (resp *types.BaseDataInfo, err error) {

	if req.Apis != nil {
		// 更新接口
		jsonStr, err := json.Marshal(req.Apis)
		if err != nil {
			return nil, errorx.NewCodeError(400, "apis json marshal error")
		}
		_, err = l.svcCtx.SaasService.UpdateAPIs(l.ctx,
			&services.UpdateAPIsReq{
				Apis: string(jsonStr),
			})
		if err != nil {
			return nil, errorx.NewCodeErrorFromGrpcStatus(err)
		}
	}

	if req.Menus != nil {
		// 更新菜单
		jsonStr, err := json.Marshal(req.Menus)
		if err != nil {
			return nil, errorx.NewCodeError(400, "apis json marshal error")
		}
		_, err = l.svcCtx.SaasService.UpdateMenus(l.ctx,
			&services.UpdateMenusReq{
				Menus: string(jsonStr),
			})
		if err != nil {
			return nil, errorx.NewCodeErrorFromGrpcStatus(err)
		}
	}

	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, "更新成功"), Data: types.EmptyData{}}, nil
}
