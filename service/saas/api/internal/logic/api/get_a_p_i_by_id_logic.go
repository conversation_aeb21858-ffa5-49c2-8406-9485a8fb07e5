package api

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type GetAPIByIdLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetAPIByIdLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAPIByIdLogic {
	return &GetAPIByIdLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetAPIByIdLogic) GetAPIById(req *types.IDReq) (resp *types.APIInfoResp, err error) {
	data, err := l.svcCtx.SaasService.GetAPIById(l.ctx, &services.IDReq{Id: req.Id})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}

	return &types.APIInfoResp{
		BaseDataInfo: types.BaseDataInfo{
			Code: 0,
			Msg:  l.svcCtx.Trans.Trans(l.ctx, i18n.Success),
		},
		Data: types.APIInfo{
			BaseIDInfo: types.BaseIDInfo{
				Id: data.Id,
			},
			Status:      data.Status,
			Path:        data.Path,
			Description: data.Description,
			ApiGroup:    data.ApiGroup,
			Method:      data.Method,
			Kind:        data.Kind,
			Module:      data.Module,
		},
	}, nil
}
