package api

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/i18n"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type GetAPIListLogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewGetAPIListLogic(ctx context.Context, svcCtx *svc.ServiceContext) *GetAPIListLogic {
	return &GetAPIListLogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *GetAPIListLogic) GetAPIList(req *types.APIListReq) (resp *types.APIListResp, err error) {
	data, err := l.svcCtx.SaasService.GetAPIList(l.ctx,
		&services.APIListReq{
			Page:        req.Page,
			PageSize:    req.PageSize,
			Path:        req.Path,
			Description: req.Description,
			ApiGroup:    req.ApiGroup,
			Search:      req.Search,
			Kind:        req.Kind,
			Module:      req.Module,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	resp = &types.APIListResp{}
	resp.Msg = l.svcCtx.Trans.Trans(l.ctx, i18n.Success)
	resp.Data.Total = data.Total
	resp.Data.Data = []types.APIInfo{}

	for _, v := range data.Data {
		resp.Data.Data = append(resp.Data.Data,
			types.APIInfo{
				BaseIDInfo: types.BaseIDInfo{
					Id: v.Id,
				},
				Status:      v.Status,
				Path:        v.Path,
				Description: v.Description,
				ApiGroup:    v.ApiGroup,
				Method:      v.Method,
				Kind:        v.Kind,
				Module:      v.Module,
			})
	}
	return resp, nil
}
