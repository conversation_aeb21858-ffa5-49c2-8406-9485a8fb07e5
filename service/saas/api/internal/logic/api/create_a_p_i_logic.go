package api

import (
	"context"
	"github.com/zeromicro/go-zero/core/logx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/errorx"
	"phoenix/service/saas/api/internal/services"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

type CreateAPILogic struct {
	logx.Logger
	ctx    context.Context
	svcCtx *svc.ServiceContext
}

func NewCreateAPILogic(ctx context.Context, svcCtx *svc.ServiceContext) *CreateAPILogic {
	return &CreateAPILogic{
		Logger: logx.WithContext(ctx),
		ctx:    ctx,
		svcCtx: svcCtx,
	}
}

func (l *CreateAPILogic) CreateAPI(req *types.APICreateInfo) (resp *types.BaseDataInfo, err error) {
	data, err := l.svcCtx.SaasService.CreateAPI(l.ctx,
		&services.APIInfo{
			Status:      req.Status,
			Path:        req.Path,
			Description: req.Description,
			ApiGroup:    req.ApiGroup,
			Method:      req.Method,
			Kind:        req.Kind,
			Module:      req.Module,
		})
	if err != nil {
		return nil, errorx.NewCodeErrorFromGrpcStatus(err)
	}
	return &types.BaseDataInfo{Msg: l.svcCtx.Trans.Trans(l.ctx, data.Msg), Data: types.EmptyData{}}, nil
}
