package queue

import (
	"context"
	"phoenix/service/saas/adapter/addons"
	"phoenix/service/saas/adapter/kqs"
	"phoenix/service/saas/api/internal/config"

	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/gorm"

	"github.com/segmentio/kafka-go"
)

func Init(c config.Config, gormDB *gorm.DB, idGenerator addons.IDGeneratorAddons) {
	if len(c.KafkaConf.Consumers) == 0 {
		return
	}
	ctx := context.WithoutCancel(context.Background())
	consumerHandlerMap := registerConsumers(gormDB, c)
	for _, consumer := range c.KafkaConf.Consumers {
		// 匹配消费者
		consumerHandle, ok := consumerHandlerMap[consumer.Key]
		if !ok {
			continue
		}
		// 启动消费者
		go kqs.NewKafkaConsumer(kafka.ReaderConfig{
			Brokers:  c.KafkaConf.Brokers,
			Topic:    consumer.Topic,
			GroupID:  consumer.GroupID,
			MinBytes: c.KafkaConf.MinBytes,
			MaxBytes: c.KafkaConf.MaxBytes,
		}, consumerHandle).Start(ctx, idGenerator)
		logc.Errorf(ctx, "启动消费者: %s", consumer.Key)
	}
}

func registerConsumers(gormDB *gorm.DB, c config.Config) map[string]Consumer {
	return map[string]Consumer{
		"data_export_record": NewDateExportRecordConsumer(gormDB),
	}
}
