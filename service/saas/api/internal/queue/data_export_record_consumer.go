package queue

import (
	"context"
	"encoding/json"
	"github.com/zeromicro/go-zero/core/logc"
	"gorm.io/gorm"
	"phoenix/service/saas/adapter/addons"
	"phoenix/service/saas/model/gorm/mapper"
)

type DateExportRecordConsumer struct {
	DateExportRecordDAO *mapper.DataExportRecordClient
}

func NewDateExportRecordConsumer(db *gorm.DB) *DateExportRecordConsumer {
	return &DateExportRecordConsumer{
		DateExportRecordDAO: mapper.NewDataExportRecordClient(db),
	}
}

func (h *DateExportRecordConsumer) Handle(ctx context.Context, message []byte, idGenerator addons.IDGeneratorAddons) error {
	var msg DataExportMessage
	err := json.Unmarshal(message, &msg)
	if err != nil {
		logc.Errorf(ctx, "解析Kafka消息失败: %v", err)
		return err
	}

	h.handleSaveDateExportRecord(ctx, msg, idGenerator)
	return nil
}

type DataExportMessage struct {
	TaskID     string `json:"task_id"`
	FileID     string `json:"file_id"`
	FileName   string `json:"file_name"`
	Status     uint   `json:"status"`
	ModuleName string `json:"module_name"`
	UserID     string `json:"user_id"`
}

func (h *DateExportRecordConsumer) handleSaveDateExportRecord(ctx context.Context, data DataExportMessage, idGenerator addons.IDGeneratorAddons) {
	if data.TaskID == "" {
		logc.Errorf(ctx, "传入的task_id不能为空")
		return
	}
	if data.Status >= 5 {
		logc.Errorf(ctx, "无效的状态")
		return
	}
	dataExportRecord, err := h.DateExportRecordDAO.GetByTaskId(ctx, data.TaskID)
	if err != nil {
		logc.Errorf(ctx, "查询数据导出信息失败: %v", err)
		return
	}
	if dataExportRecord.TaskId == "" {
		err = h.DateExportRecordDAO.Create(ctx, mapper.DataExportRecord{
			Id:         idGenerator.GenerateIDString(),
			TaskId:     data.TaskID,
			FileName:   data.FileName,
			FileId:     data.FileID,
			ModuleName: data.ModuleName,
			Status:     data.Status,
			UserId:     data.UserID,
		})
		if err != nil {
			logc.Errorf(ctx, "新建数据导出信息失败: %v", err)
		}
		return
	}
	err = h.DateExportRecordDAO.UpdateFileIdAndStatusByTaskId(ctx, data.TaskID, data.FileID, data.FileName, data.Status)
	if err != nil {
		logc.Errorf(ctx, "更新数据导出文件id和状态失败: %v", err)
		return
	}
	return
}
