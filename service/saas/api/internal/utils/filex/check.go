package filex

import (
	"context"
	"errors"
	"phoenix/service/saas/api/internal/svc"
)

func CheckOverSize(ctx context.Context, svCtx *svc.ServiceContext, fileType string, size int64) error {
	if fileType == "image" && size > svCtx.Config.UploadConf.MaxImageSize {
		return errors.New("file.overSizeError")

	} else if fileType == "video" && size > svCtx.Config.UploadConf.MaxVideoSize {
		return errors.New("file.overSizeError")

	} else if fileType == "audio" && size > svCtx.Config.UploadConf.MaxAudioSize {
		return errors.New("file.overSizeError")

	} else if fileType != "image" && fileType != "video" && fileType != "audio" &&
		size > svCtx.Config.UploadConf.MaxOtherSize {
		return errors.New("file.overSizeError")
	}
	return nil
}
