package utils

import (
	"github.com/mojocn/base64Captcha"
	"github.com/zeromicro/go-zero/core/stores/redis"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/utils/captcha"
	"phoenix/service/saas/api/internal/config"
)

type CaptchaUtil struct {
	Conf          *config.Captcha
	CaptchaStore  *captcha.RedisStore
	CaptchaDriver *base64Captcha.DriverDigit
}

// InitStoreAndDriver init captcha store and driver
func InitStoreAndDriver(c config.Captcha, r *redis.Redis) *CaptchaUtil {
	captchaDriver := base64Captcha.NewDriverDigit(c.<PERSON>mg<PERSON><PERSON>, c.Img<PERSON>idth,
		c.<PERSON>, 0.7, 80)
	captchaStore := captcha.NewRedisStore(r)

	return &CaptchaUtil{
		Conf:          &c,
		CaptchaStore:  captchaStore,
		CaptchaDriver: captchaDriver,
	}
}
