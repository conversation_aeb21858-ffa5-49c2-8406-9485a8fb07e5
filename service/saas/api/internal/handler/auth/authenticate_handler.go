package auth

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"phoenix/service/saas/api/internal/logic/auth"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

// swagger:route post /saas/api/v1/auth/authenticate auth Authenticate
//
// 通过token进行认证校验
//
// 通过token进行认证校验
//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: AuthenticateReq
//
// Responses:
//  200: AuthenticateResp

func AuthenticateHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AuthenticateReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := auth.NewAuthenticateLogic(r.Context(), svcCtx)
		resp, err := l.Authenticate(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
