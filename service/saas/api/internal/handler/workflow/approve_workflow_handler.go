package workflow

import (
	"net/http"

	"phoenix/service/saas/api/internal/errs"
	"phoenix/service/saas/api/internal/logic/workflow"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func ApproveWorkflowHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.WorkflowApproveReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := workflow.NewApproveWorkflowLogic(r.Context(), svcCtx)
		resp, err := l.ApproveWorkflow(&req)
		errs.CommonResponse(r.Context(), w, resp, err)
	}
}
