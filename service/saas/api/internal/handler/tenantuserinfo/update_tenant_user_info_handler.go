package tenantuserinfo

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/tenantuserinfo"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route post /saas/api/v1/tenant_user/update tenantuserinfo UpdateTenantUserInfo
//
// Update tenant user info information | 更新TenantUserInfo
//
// Update tenant user info information | 更新TenantUserInfo
//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: UpdateTenantUserReq
//
// Responses:
//  200: BaseDataInfo

func UpdateTenantUserInfoHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UpdateTenantUserReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := tenantuserinfo.NewUpdateTenantUserInfoLogic(r.Context(), svcCtx)
		resp, err := l.UpdateTenantUserInfo(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
