package tenantuserinfo

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/tenantuserinfo"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route post /saas/api/v1/tenant_user/delete tenantuserinfo DeleteTenantUserInfo
//
// Delete tenant user info information | 删除TenantUserInfo信息
//
// Delete tenant user info information | 删除TenantUserInfo信息
//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: DeleteTenantUserInfoByIdsReq
//
// Responses:
//  200: BaseDataInfo

func DeleteTenantUserInfoHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.DeleteTenantUserInfoByIdsReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := tenantuserinfo.NewDeleteTenantUserInfoLogic(r.Context(), svcCtx)
		resp, err := l.DeleteTenantUserInfo(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
