package tenantuserinfo

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/tenantuserinfo"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route get /saas/api/v1/tenant_user/{id} tenantuserinfo GetTenantUserInfoById
//
// Get tenant user info by Id | 通过ID获取TenantUserInfo
//
// Get tenant user info by Id | 通过ID获取TenantUserInfo
//
// Parameters:
//  + name: id
//    require: true
//    in: path
//    type: string
//
// Parameters:
//  + name: tenantId
//    require: true
//    in: query
//    type: string
//
// Responses:
//  200: TenantUserInfoInfoResp

func GetTenantUserInfoByIdHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetTenantUserInfoByIdReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := tenantuserinfo.NewGetTenantUserInfoByIdLogic(r.Context(), svcCtx)
		resp, err := l.GetTenantUserInfoById(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
