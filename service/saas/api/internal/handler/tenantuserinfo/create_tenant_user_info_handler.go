package tenantuserinfo

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/tenantuserinfo"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route post /saas/api/v1/tenant_user/create tenantuserinfo CreateTenantUserInfo
//
// Create tenant user info information | 创建TenantUserInfo
//
// Create tenant user info information | 创建TenantUserInfo
//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: AddTenantUserReq
//
// Responses:
//  200: BaseDataInfo

func CreateTenantUserInfoHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AddTenantUserReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := tenantuserinfo.NewCreateTenantUserInfoLogic(r.Context(), svcCtx)
		resp, err := l.CreateTenantUserInfo(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
