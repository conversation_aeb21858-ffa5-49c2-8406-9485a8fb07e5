package group

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/group"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route get /saas/api/v1/group/users/{groupId} group GetGroupUsers
//
// Get group users | 获取Group用户
//
// Get group users | 获取Group用户
//
// Parameters:
//  + name: groupId
//    require: true
//    in: path
//    type: string
//
// Parameters:
//  + name: search
//    require: false
//    in: query
//    type: string
//
// Responses:
//  200: GroupInfoResp

func GetGroupUsersHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetGroupUsersReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := group.NewGetGroupUsersLogic(r.Context(), svcCtx)
		resp, err := l.GetGroupUsers(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
