package group

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/group"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route post /saas/api/v1/group/update_users group UpdateGroupUsers
//
// Update group users | 更新Group用户
//
// Update group users | 更新Group用户
//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: UpdataGroupUsersReq
//
// Responses:
//  200: BaseDataInfo

func UpdateGroupUsersHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UpdataGroupUsersReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := group.NewUpdateGroupUsersLogic(r.Context(), svcCtx)
		resp, err := l.UpdateGroupUsers(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
