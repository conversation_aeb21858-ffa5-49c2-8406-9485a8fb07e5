package tenant

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/tenant"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route get /saas/api/v1/tenant/list tenant GetTenantList
//
// Get tenant list | 获取Tenant列表
//
// Get tenant list | 获取Tenant列表
//
// Parameters:
//  + name: page
//    require: false
//    in: query
//    type: number
//
//  + name: pageSize
//    require: false
//    in: query
//    type: number
//
// Responses:
//  200: TenantListResp

func GetTenantListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.TenantListReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := tenant.NewGetTenantListLogic(r.Context(), svcCtx)
		resp, err := l.GetTenantList(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
