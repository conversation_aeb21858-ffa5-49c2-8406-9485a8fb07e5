package tenant

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/tenant"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route post /saas/api/v1/tenant/update_users tenant UpdateTenantUsers
//
// Update Tenant users information | 更新TenantUsers
//
// Update Tenant users information | 更新TenantUsers
//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: UpdateTenantUsersReq
//
// Responses:
//  200: BaseDataInfo

func UpdateTenantUsersHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UpdateTenantUsersReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := tenant.NewUpdateTenantUsersLogic(r.Context(), svcCtx)
		resp, err := l.UpdateTenantUsers(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
