package captcha

import (
	"net/http"
	"phoenix/service/saas/api/internal/logic/captcha"
	"phoenix/service/saas/api/internal/svc"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route get /saas/api/v1/captcha captcha GetCaptcha
//
// Get captcha | 获取验证码
//
// Get captcha | 获取验证码
//
// Responses:
//  200: CaptchaResp

func GetCaptchaHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := captcha.NewGetCaptchaLogic(r.Context(), svcCtx)
		resp, err := l.GetCaptcha()
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
