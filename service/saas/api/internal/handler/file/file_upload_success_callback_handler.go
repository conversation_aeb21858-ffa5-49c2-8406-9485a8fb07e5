package file

import (
	"net/http"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
	"phoenix/service/saas/api/internal/logic/file"
	"phoenix/service/saas/api/internal/svc"
)

func FileUploadSuccessCallbackHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.FileUploadSeccussCallBackReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}
		l := file.NewFileUploadSuccessCallbackLogic(r.Context(), svcCtx)
		resp, err := l.FileUploadSuccessCallback(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
