package file

import (
	"github.com/zeromicro/go-zero/rest/httpx"
	"net/http"
	"phoenix/service/saas/api/internal/logic/file"
	"phoenix/service/saas/api/internal/svc"
)

// swagger:route post /file/api/v1/file/upload file Upload
//
// Upload file | 上传文件
//
// Upload file | 上传文件
//
// Parameters:
//  + name: file
//    require: true
//    in: formData
//    type: file
//
// Parameters:
//  + name: hash
//    require: true
//    in: formData
//    type: string
//
// Responses:
//  200: UploadResp

func UploadHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := file.NewUploadLogic(r, svcCtx)
		resp, err := l.Upload()
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
