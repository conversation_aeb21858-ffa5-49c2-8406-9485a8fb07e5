package grouptype

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/grouptype"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route get /saas/api/v1/group_type/{id} grouptype GetGroupTypeById
//
// Get group type by Id | 通过ID获取GroupType
//
// Get group type by Id | 通过ID获取GroupType
//
// Parameters:
//  + name: id
//    require: true
//    in: path
//    type: string
//
// Responses:
//  200: GroupTypeInfoResp

func GetGroupTypeByIdHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.IDPathReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := grouptype.NewGetGroupTypeByIdLogic(r.Context(), svcCtx)
		resp, err := l.GetGroupTypeById(&types.IDReq{Id: req.Id})
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
