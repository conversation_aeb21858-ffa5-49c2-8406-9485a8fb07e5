package grouptype

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/grouptype"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route post /saas/api/v1/group_type/update grouptype UpdateGroupType
//
// Update group type information | 更新GroupType
//
// Update group type information | 更新GroupType
//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: GroupTypeInfo
//
// Responses:
//  200: BaseDataInfo

func UpdateGroupTypeHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GroupTypeInfo
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := grouptype.NewUpdateGroupTypeLogic(r.Context(), svcCtx)
		resp, err := l.UpdateGroupType(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
