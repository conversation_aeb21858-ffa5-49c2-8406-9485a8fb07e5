package grouptype

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"phoenix/service/saas/api/internal/logic/grouptype"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

// swagger:route get /saas/api/v1/group_type/list/tree grouptype GetGroupTypeListTree
//
// Get group type list tree | 获取GroupType树形
//
// Get group type list tree | 获取GroupType树形
//
//
// Responses:
//  200: GroupTypeTreeResp

func GetGroupTypeListTreeHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GroupTypeTreeReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := grouptype.NewGetGroupTypeListTreeLogic(r.Context(), svcCtx)
		resp, err := l.GetGroupTypeListTree(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
