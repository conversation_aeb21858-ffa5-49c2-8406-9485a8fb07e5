package grouptype

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/grouptype"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route post /saas/api/v1/group_type/create grouptype CreateGroupType
//
// Create group type information | 创建GroupType
//
// Create group type information | 创建GroupType
//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: GroupTypeCreateReq
//
// Responses:
//  200: BaseDataInfo

func CreateGroupTypeHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GroupTypeCreateReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := grouptype.NewCreateGroupTypeLogic(r.Context(), svcCtx)
		resp, err := l.CreateGroupType(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
