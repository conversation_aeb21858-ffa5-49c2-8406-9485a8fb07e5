package base

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/base"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route post /saas/api/v1/init/update_permissions base UpdatePermissions
//
// Update Permissions | 更新权限资源
//
// Update Permissions | 更新权限资源
//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: UpdatePermissionsReq
//
// Responses:
//  200: BaseDataInfo

func UpdatePermissionsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UpdatePermissionsReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := base.NewUpdatePermissionsLogic(r.Context(), svcCtx)
		resp, err := l.UpdatePermissions(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
