package organization

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/organization"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route get /saas/api/v1/organization/list organization GetOrganizationList
//
// Get organization list | 获取Organization列表
//
// Get organization list | 获取Organization列表
//
// Parameters:
//  + name: page
//    require: false
//    in: query
//    type: number
//
//  + name: pageSize
//    require: false
//    in: query
//    type: number
//
// Responses:
//  200: OrganizationListResp

func GetOrganizationListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.OrganizationListReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := organization.NewGetOrganizationListLogic(r.Context(), svcCtx)
		resp, err := l.GetOrganizationList(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
