package organization

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/organization"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route get /saas/api/v1/organization/tree/list organization GetOrganizationTreeList
//
// Get organization tree list | 获取 Organization 树状列表
//
// Get organization tree list | 获取 Organization 树状列表
//
//  + name: parentId
//    require: false
//    in: query
//    type: string
//
//  + name: name
//    require: false
//    in: query
//    type: string
//
// Responses:
//  200: MenuListResp

func GetOrganizationTreeListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.OrganizationListReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := organization.NewGetOrganizationTreeListLogic(r.Context(), svcCtx)
		resp, err := l.GetOrganizationTreeList(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
