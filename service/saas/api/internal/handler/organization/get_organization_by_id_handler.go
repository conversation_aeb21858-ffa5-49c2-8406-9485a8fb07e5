package organization

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/organization"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route get /saas/api/v1/organization/{id} organization GetOrganizationById
//
// Get organization by Id | 通过ID获取Organization
//
// Get organization by Id | 通过ID获取Organization
//
// Parameters:
//  + name: id
//    require: true
//    in: path
//    type: string
//
// Responses:
//  200: OrganizationInfoResp

func GetOrganizationByIdHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.IDPathReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := organization.NewGetOrganizationByIdLogic(r.Context(), svcCtx)
		resp, err := l.GetOrganizationById(&types.IDReq{Id: req.Id})
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
