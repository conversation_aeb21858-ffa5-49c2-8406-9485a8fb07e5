package organization

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/organization"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route post /saas/api/v1/organization/update_users organization UpdateOrganizationUsers
//
// Update organization users information | 更新OrganizationUsers
//
// Update organization users information | 更新OrganizationUsers
//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: UpdateOrganizationUsersReq
//
// Responses:
//  200: BaseDataInfo

func UpdateOrganizationUsersHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UpdateOrganizationUsersReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := organization.NewUpdateOrganizationUsersLogic(r.Context(), svcCtx)
		resp, err := l.UpdateOrganizationUsers(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
