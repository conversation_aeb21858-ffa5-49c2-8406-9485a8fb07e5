package button

import (
	"net/http"
	"phoenix/service/saas/api/internal/logic/button"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
)

// swagger:route post /saas/api/v1/button/create button CreateButton
//
// Create button information | 创建Button
//
// Create button information | 创建Button
//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: ButtonCreateReq
//
// Responses:
//  200: BaseDataInfo

func CreateButtonHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ButtonCreateReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := button.NewCreateButtonLogic(r.Context(), svcCtx)
		resp, err := l.CreateButton(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
