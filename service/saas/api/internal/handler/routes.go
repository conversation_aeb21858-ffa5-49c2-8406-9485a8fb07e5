// Code generated by goctl. DO NOT EDIT.
// goctl 1.7.6

package handler

import (
	"net/http"

	api "phoenix/service/saas/api/internal/handler/api"
	auth "phoenix/service/saas/api/internal/handler/auth"
	authority "phoenix/service/saas/api/internal/handler/authority"
	base "phoenix/service/saas/api/internal/handler/base"
	button "phoenix/service/saas/api/internal/handler/button"
	captcha "phoenix/service/saas/api/internal/handler/captcha"
	dataexportrecord "phoenix/service/saas/api/internal/handler/dataexportrecord"
	dictionary "phoenix/service/saas/api/internal/handler/dictionary"
	file "phoenix/service/saas/api/internal/handler/file"
	group "phoenix/service/saas/api/internal/handler/group"
	grouptype "phoenix/service/saas/api/internal/handler/grouptype"
	menu "phoenix/service/saas/api/internal/handler/menu"
	morehelp "phoenix/service/saas/api/internal/handler/morehelp"
	organization "phoenix/service/saas/api/internal/handler/organization"
	organizationuserinfo "phoenix/service/saas/api/internal/handler/organizationuserinfo"
	position "phoenix/service/saas/api/internal/handler/position"
	role "phoenix/service/saas/api/internal/handler/role"
	systemlog "phoenix/service/saas/api/internal/handler/systemlog"
	tenant "phoenix/service/saas/api/internal/handler/tenant"
	tenantuserinfo "phoenix/service/saas/api/internal/handler/tenantuserinfo"
	token "phoenix/service/saas/api/internal/handler/token"
	user "phoenix/service/saas/api/internal/handler/user"
	workflow "phoenix/service/saas/api/internal/handler/workflow"
	"phoenix/service/saas/api/internal/svc"

	"github.com/zeromicro/go-zero/rest"
)

func RegisterHandlers(server *rest.Server, serverCtx *svc.ServiceContext) {
	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthMiddleware, serverCtx.Authority, serverCtx.TraceMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/api/:id",
					Handler: api.GetAPIByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/create",
					Handler: api.CreateAPIHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/delete",
					Handler: api.DeleteAPIHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/api/list",
					Handler: api.GetAPIListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/api/update",
					Handler: api.UpdateAPIHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				// authenticate | 认证
				Method:  http.MethodPost,
				Path:    "/auth/authenticate",
				Handler: auth.AuthenticateHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/auth/login",
				Handler: auth.LoginHandler(serverCtx),
			},
			{
				Method:  http.MethodPost,
				Path:    "/auth/token_check",
				Handler: auth.TokenCheckHandler(serverCtx),
			},
		},
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthMiddleware, serverCtx.Authority, serverCtx.TraceMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/auth/token_refresh",
					Handler: auth.TokenRefreshHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.TraceMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/auth/password-free-login",
					Handler: auth.PasswordFreeLoginHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/inside/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthMiddleware, serverCtx.Authority, serverCtx.TraceMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/authority/api/create_or_update",
					Handler: authority.CreateOrUpdateApiAuthorityHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/authority/api/reload_rules",
					Handler: authority.ReloadApiAuthorityRuleCacheHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/authority/api/role/:id",
					Handler: authority.GetApiAuthorityHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/authority/menu/create_or_update",
					Handler: authority.CreateOrUpdateMenuAuthorityHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/authority/menu/role/:id",
					Handler: authority.GetMenuAuthorityHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/init/database",
				Handler: base.InitDatabaseHandler(serverCtx),
			},
		},
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthMiddleware, serverCtx.Authority, serverCtx.TraceMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/init/update_permissions",
					Handler: base.UpdatePermissionsHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthMiddleware, serverCtx.Authority, serverCtx.TraceMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/button/:id",
					Handler: button.GetButtonByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/button/create",
					Handler: button.CreateButtonHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/button/delete",
					Handler: button.DeleteButtonHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/button/list",
					Handler: button.GetButtonListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/button/update",
					Handler: button.UpdateButtonHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodGet,
				Path:    "/captcha",
				Handler: captcha.GetCaptchaHandler(serverCtx),
			},
		},
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.TraceMiddleware, serverCtx.AuthMiddleware, serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/data_export/list",
					Handler: dataexportrecord.GetDataExportListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/data_export/update/by/id",
					Handler: dataexportrecord.UpdateDataExportStatusByIdHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.TraceMiddleware, serverCtx.AuthMiddleware, serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/dict/create",
					Handler: dictionary.CreateDictByInfoHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/dict/delete",
					Handler: dictionary.DeleteDictByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/dict/type/list",
					Handler: dictionary.DictListByDictTypeHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/dict/update",
					Handler: dictionary.UpdateDictByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/dicts",
					Handler: dictionary.DictPageHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		[]rest.Route{
			{
				Method:  http.MethodPost,
				Path:    "/file/upload/success/callback",
				Handler: file.FileUploadSuccessCallbackHandler(serverCtx),
			},
		},
		rest.WithPrefix("/file/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthMiddleware, serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/file/delete",
					Handler: file.DeleteFileHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/file/download/:id",
					Handler: file.DownloadFileHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/file/download/presignedurl/generate",
					Handler: file.GenerateDownloadPreSignedUrlHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/file/get/:id",
					Handler: file.GetFileHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/file/list",
					Handler: file.FileListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/file/status",
					Handler: file.ChangePublicStatusHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/file/update",
					Handler: file.UpdateFileHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/file/upload",
					Handler: file.UploadHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/file/upload/presignedurl/generate",
					Handler: file.GenerateUploadPreSignedUrlHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/file/watermark/generate",
					Handler: file.GenerateWatermarkedFileHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/file/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthMiddleware, serverCtx.Authority, serverCtx.TraceMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/group/:id",
					Handler: group.GetGroupByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/group/create",
					Handler: group.CreateGroupHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/group/delete",
					Handler: group.DeleteGroupHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/group/list",
					Handler: group.GetGroupListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/group/update",
					Handler: group.UpdateGroupHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/group/update_users",
					Handler: group.UpdateGroupUsersHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/group/users/:groupId",
					Handler: group.GetGroupUsersHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthMiddleware, serverCtx.Authority, serverCtx.TraceMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/group_type/:id",
					Handler: grouptype.GetGroupTypeByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/group_type/create",
					Handler: grouptype.CreateGroupTypeHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/group_type/delete",
					Handler: grouptype.DeleteGroupTypeHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/group_type/list",
					Handler: grouptype.GetGroupTypeListHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/group_type/list/tree",
					Handler: grouptype.GetGroupTypeListTreeHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/group_type/update",
					Handler: grouptype.UpdateGroupTypeHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthMiddleware, serverCtx.Authority, serverCtx.TraceMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/menu/:id",
					Handler: menu.GetMenuByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/menu/create",
					Handler: menu.CreateMenuHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/menu/delete",
					Handler: menu.DeleteMenuHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/menu/get_menus_by_user/:id",
					Handler: menu.GetMenuByUserIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/menu/import/file",
					Handler: menu.ImportMenuFromFileHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/menu/list",
					Handler: menu.GetMenuListHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/menu/tenant/tree/list",
					Handler: menu.GetMenuTreeListByTenantHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/menu/tree/list",
					Handler: menu.GetMenuTreeListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/menu/update",
					Handler: menu.UpdateMenuHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.TraceMiddleware, serverCtx.AuthMiddleware, serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodPost,
					Path:    "/more-help/file/create",
					Handler: morehelp.CreateMoreHelpFileHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/more-help/file/delete",
					Handler: morehelp.DeleteMoreHelpFileHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/more-help/file/drag/sort",
					Handler: morehelp.MoreHelpFileDragSortHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/more-help/file/list",
					Handler: morehelp.MoreHelpFileListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/more-help/file/update",
					Handler: morehelp.UpdateMoreHelpFileHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthMiddleware, serverCtx.Authority, serverCtx.TraceMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/organization/:id",
					Handler: organization.GetOrganizationByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/organization/all-organizations-users",
					Handler: organization.GetAllOrganizationsAndUsersByOrgIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/organization/create",
					Handler: organization.CreateOrganizationHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/organization/delete",
					Handler: organization.DeleteOrganizationHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/organization/list",
					Handler: organization.GetOrganizationListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/organization/list/by_node_types",
					Handler: organization.GetOrganizationListByNodeTypesHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/organization/list/bycodes",
					Handler: organization.GetOrganizationListByCodesHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/organization/tree/list",
					Handler: organization.GetOrganizationTreeListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/organization/update",
					Handler: organization.UpdateOrganizationHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/organization/update_users",
					Handler: organization.UpdateOrganizationUsersHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthMiddleware, serverCtx.Authority, serverCtx.TraceMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/organization_user/:id",
					Handler: organizationuserinfo.GetOrganizationUserInfoByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/organization_user/company/all/users",
					Handler: organizationuserinfo.GetCompanyAllUsersHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/organization_user/create",
					Handler: organizationuserinfo.CreateOrganizationUserInfoHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/organization_user/delete",
					Handler: organizationuserinfo.DeleteOrganizationUserInfoHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/organization_user/group-companies",
					Handler: organizationuserinfo.GetUserGroupsAndCompaniesHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/organization_user/group/all/users",
					Handler: organizationuserinfo.GetGroupAllUsersHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/organization_user/group/users",
					Handler: organizationuserinfo.GetGroupUsersHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/organization_user/leader-admin/add",
					Handler: organizationuserinfo.AddOrganizationLeaderAndAdminHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/organization_user/level/all/user/list",
					Handler: organizationuserinfo.GetThisLevelAllUsersHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/organization_user/list",
					Handler: organizationuserinfo.GetOrganizationUserInfoListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/organization_user/list/tree",
					Handler: organizationuserinfo.GetOrganizationUserInfoTreeHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/organization_user/organization/:id",
					Handler: organizationuserinfo.GetUserOrganizationsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/organization_user/update",
					Handler: organizationuserinfo.UpdateOrganizationUserInfoHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/organization_user/user/belong/department",
					Handler: organizationuserinfo.GetUserBelongDepartmentHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/organization_user/user/drag/sort",
					Handler: organizationuserinfo.DragSortOrganizationUserHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthMiddleware, serverCtx.Authority, serverCtx.TraceMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/position/:id",
					Handler: position.GetPositionByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/position/create",
					Handler: position.CreatePositionHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/position/delete",
					Handler: position.DeletePositionHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/position/list",
					Handler: position.GetPositionListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/position/update",
					Handler: position.UpdatePositionHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/positions/by_ids",
					Handler: position.GetPositionListByIDsHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthMiddleware, serverCtx.Authority, serverCtx.TraceMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/role/:id",
					Handler: role.GetRoleByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/role/check_user_has_role_code",
					Handler: role.CheckUserHasRoleCodeHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/role/create",
					Handler: role.CreateRoleHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/role/delete",
					Handler: role.DeleteRoleHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/role/list",
					Handler: role.GetRoleListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/role/update",
					Handler: role.UpdateRoleHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/role/update_users",
					Handler: role.UpdateRoleUsersHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/role/users/:roleId",
					Handler: role.GetRoleUsersHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/role/users/rolecode",
					Handler: role.GetRoleUsersByRoleCodeHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/roles/by-user-id",
					Handler: role.GetRolesByUserIDHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/roles/by_ids",
					Handler: role.GetRoleListByIDsHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthMiddleware, serverCtx.Authority, serverCtx.TraceMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/system-log/login/list",
					Handler: systemlog.GetLoginLogsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/system-log/operation/list",
					Handler: systemlog.GetOperationLogsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/system/login/logs",
					Handler: systemlog.SaveSystemLoginLogHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/system/operation/logs",
					Handler: systemlog.SaveSystemOperationLogHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthMiddleware, serverCtx.Authority, serverCtx.TraceMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/tenant/:id",
					Handler: tenant.GetTenantByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/tenant/create",
					Handler: tenant.CreateTenantHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/tenant/delete",
					Handler: tenant.DeleteTenantHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/tenant/list",
					Handler: tenant.GetTenantListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/tenant/update",
					Handler: tenant.UpdateTenantHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/tenant/update_users",
					Handler: tenant.UpdateTenantUsersHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthMiddleware, serverCtx.Authority, serverCtx.TraceMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/tenant_user/:id",
					Handler: tenantuserinfo.GetTenantUserInfoByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/tenant_user/create",
					Handler: tenantuserinfo.CreateTenantUserInfoHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/tenant_user/delete",
					Handler: tenantuserinfo.DeleteTenantUserInfoHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/tenant_user/list",
					Handler: tenantuserinfo.GetTenantUserInfoListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/tenant_user/update",
					Handler: tenantuserinfo.UpdateTenantUserInfoHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthMiddleware, serverCtx.Authority, serverCtx.TraceMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/token/:id",
					Handler: token.GetTokenByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/token/create",
					Handler: token.CreateTokenHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/token/delete",
					Handler: token.DeleteTokenHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/token/list",
					Handler: token.GetTokenListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/token/update",
					Handler: token.UpdateTokenHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.AuthMiddleware, serverCtx.Authority, serverCtx.TraceMiddleware},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/user/:id",
					Handler: user.GetUserByIdHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/create",
					Handler: user.CreateUserHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/delete",
					Handler: user.DeleteUserHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/user/info",
					Handler: user.GetUserInfoHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/user/info/bymobile",
					Handler: user.GetUserByMobileHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/user/list",
					Handler: user.GetUserListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/list",
					Handler: user.GetUserListPostHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/list/bymobiles",
					Handler: user.GetUserListByMobilesHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/nicknames/byids",
					Handler: user.GetUserNicknamesByIDsHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/online/force_logout",
					Handler: user.ForceLogoutHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/user/online/list",
					Handler: user.GetOnlineUserListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/update",
					Handler: user.UpdateUserHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/user/update_users_password",
					Handler: user.UpdateUsersPasswordHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.TraceMiddleware, serverCtx.AuthMiddleware, serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/workflow/template-preset",
					Handler: workflow.GetPresetTemplateDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/workflow/template-preset/bind",
					Handler: workflow.BindPresetTemplateHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/workflow/template-preset/save",
					Handler: workflow.SavePresetTemplateHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/workflow/template-presets",
					Handler: workflow.GetPresetTemplateListHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.TraceMiddleware, serverCtx.AuthMiddleware, serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/workflow/template",
					Handler: workflow.GetTemplateDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/workflow/template/update",
					Handler: workflow.UpdateTemplateHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/workflow/template/version/enable",
					Handler: workflow.EnableTemplateVersionHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/workflow/template/versions",
					Handler: workflow.GetTemplateVersionListHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/workflow/templates",
					Handler: workflow.GetTemplateListHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.TraceMiddleware, serverCtx.AuthMiddleware, serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/workflow",
					Handler: workflow.GetWorkflowDetailHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/workflow/approve",
					Handler: workflow.ApproveWorkflowHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/workflow/cancel",
					Handler: workflow.CancelWorkflowHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/workflow/cc/read",
					Handler: workflow.ReadWorkflowCCHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/workflow/initiates",
					Handler: workflow.GetWorkflowInitiatesHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/workflow/start",
					Handler: workflow.StartWorkflowHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/workflow/task/ccs",
					Handler: workflow.GetWorkflowTaskCCsHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/workflow/task/done",
					Handler: workflow.GetWorkflowTaskDoneHandler(serverCtx),
				},
				{
					Method:  http.MethodGet,
					Path:    "/workflow/task/todos",
					Handler: workflow.GetWorkflowTaskTodosHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/api/v1"),
	)

	server.AddRoutes(
		rest.WithMiddlewares(
			[]rest.Middleware{serverCtx.TraceMiddleware, serverCtx.AuthMiddleware, serverCtx.Authority},
			[]rest.Route{
				{
					Method:  http.MethodGet,
					Path:    "/workflow/monitor/list",
					Handler: workflow.GetWorkflowMonitorListHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/workflow/monitor/node/approver/add",
					Handler: workflow.AddWorkflowMonitorNodeApproverHandler(serverCtx),
				},
				{
					Method:  http.MethodPost,
					Path:    "/workflow/monitor/node/approver/update",
					Handler: workflow.UpdateWorkflowMonitorNodeApproverHandler(serverCtx),
				},
			}...,
		),
		rest.WithPrefix("/saas/api/v1"),
	)
}
