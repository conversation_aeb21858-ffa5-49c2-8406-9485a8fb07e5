package authority

import (
	"net/http"
	"phoenix/service/saas/api/internal/logic/authority"
	"phoenix/service/saas/api/internal/svc"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route post /saas/api/v1/authority/api/reload_rules authority ReloadApiAuthorityRuleCache
//
// Reload API authorization cache | 重新加载API权限缓存
//
// Reload API authorization cache | 重新加载API权限缓存
//
// Responses:
//  200: BaseDataInfo

func ReloadApiAuthorityRuleCacheHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := authority.NewReloadApiAuthorityRuleCacheLogic(r.Context(), svcCtx)
		resp, err := l.ReloadApiAuthorityRuleCache()
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
