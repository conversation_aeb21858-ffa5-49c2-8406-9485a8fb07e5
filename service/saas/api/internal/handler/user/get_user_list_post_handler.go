package user

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"phoenix/service/saas/api/internal/logic/user"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

func GetUserListPostHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UserListPReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := user.NewGetUserListPostLogic(r.Context(), svcCtx)
		resp, err := l.GetUserListPost(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
