package user

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/user"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route post /saas/api/v1/user/create user CreateUser
//
// Create user information | 创建User
//
// Create user information | 创建User
//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: UserCreateReq
//
// Responses:
//  200: BaseDataInfo

func CreateUserHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UserCreateReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := user.NewCreateUserLogic(r.Context(), svcCtx)
		resp, err := l.CreateUser(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
