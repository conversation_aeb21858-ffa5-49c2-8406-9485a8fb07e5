package user

import (
	"net/http"

	"phoenix/service/saas/api/internal/errs"
	"phoenix/service/saas/api/internal/logic/user"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func GetOnlineUserListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetOnlineUsersReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := user.NewGetOnlineUserListLogic(r.Context(), svcCtx)
		resp, err := l.GetOnlineUserList(&req)
		errs.CommonResponse(r.Context(), w, resp, err)
	}
}
