package user

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/user"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route post /saas/api/v1/user/update_users_password user UpdateUsersPassword
//
// bulk Update user password | 批量更新User密码
//
// bulk Update user password | 批量更新User密码
//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: UpdateUsersPasswordReq
//
// Responses:
//  200: BaseDataInfo

func UpdateUsersPasswordHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UpdateUsersPasswordReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := user.NewUpdateUsersPasswordLogic(r.Context(), svcCtx)
		resp, err := l.UpdateUsersPassword(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
