package user

import (
	"net/http"

	"phoenix/service/saas/api/internal/errs"
	"phoenix/service/saas/api/internal/logic/user"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

func ForceLogoutHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.ForceLogoutReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := user.NewForceLogoutLogic(r.Context(), svcCtx)
		resp, err := l.ForceLogout(&req)
		errs.CommonResponse(r.Context(), w, resp, err)
	}
}
