package dataexportrecord

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"phoenix/service/saas/api/internal/logic/dataexportrecord"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

func GetDataExportListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.DataExportRecordListReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := dataexportrecord.NewGetDataExportListLogic(r.Context(), svcCtx)
		resp, err := l.GetDataExportList(&req)
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
