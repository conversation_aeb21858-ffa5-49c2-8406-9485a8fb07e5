package morehelp

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"phoenix/service/saas/api/internal/logic/morehelp"
	"phoenix/service/saas/api/internal/svc"
)

func MoreHelpFileListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := morehelp.NewMoreHelpFileListLogic(r.Context(), svcCtx)
		resp, err := l.MoreHelpFileList()
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
