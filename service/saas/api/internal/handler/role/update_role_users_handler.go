package role

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/role"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route post /saas/api/v1/role/update_users role UpdateRoleUsers
//
// Update role users | 更新Role用户
//
// Update role users | 更新Role用户
//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: UpdataRoleUsersReq
//
// Responses:
//  200: BaseDataInfo

func UpdateRoleUsersHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.UpdataRoleUsersReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := role.NewUpdateRoleUsersLogic(r.Context(), svcCtx)
		resp, err := l.UpdateRoleUsers(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
