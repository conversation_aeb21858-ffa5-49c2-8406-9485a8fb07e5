package role

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/role"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route get /saas/api/v1/role/users/{roleId} role GetRoleUsers
//
// Get role users | 获取Role用户
//
// Get role users | 获取Role用户
//
// Parameters:
//  + name: roleId
//    require: true
//    in: path
//    type: string
//
// Responses:
//  200: GetRoleUsersResp

func GetRoleUsersHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetRoleUsersReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := role.NewGetRoleUsersLogic(r.Context(), svcCtx)
		resp, err := l.GetRoleUsers(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
