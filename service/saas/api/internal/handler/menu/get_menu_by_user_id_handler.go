package menu

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/menu"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route get /saas/api/v1/menu/get_menus_by_user/{id} menu GetMenuByUserId
//
// Get menu by user Id | 通过user ID获取可用Menu
//
// Get menu by user Id | 通过user ID获取可用Menu
//
// Parameters:
//  + name: id
//    require: true
//    in: path
//    type: string
//
// Responses:
//  200: MenuInfoResp

func GetMenuByUserIdHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.IDPathReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := menu.NewGetMenuByUserIdLogic(r.Context(), svcCtx)
		resp, err := l.GetMenuByUserId(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
