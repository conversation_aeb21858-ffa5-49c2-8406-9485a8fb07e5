package menu

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/menu"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route get /saas/api/v1/menu/tree/list menu GetMenuTreeList
//
// Get menu tree list | 获取 Menu 树状列表
//
// Get menu tree list | 获取 Menu 树状列表
//
//  + name: parentId
//    require: false
//    in: query
//    type: string
//
//  + name: name
//    require: false
//    in: query
//    type: string
//
//  + name: title
//    require: false
//    in: query
//    type: string
//
// Responses:
//  200: MenuListResp

func GetMenuTreeListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.MenuListReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := menu.NewGetMenuTreeListLogic(r.Context(), svcCtx)
		resp, err := l.GetMenuTreeList(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
