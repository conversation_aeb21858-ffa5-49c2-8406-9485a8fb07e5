package menu

import (
	"net/http"
	"phoenix/service/saas/api/internal/logic/menu"
	"phoenix/service/saas/api/internal/svc"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route post /saas/api/v1/menu/import/file menu ImportMenuFromFile
//
// Import menu from JSON file | 从JSON文件导入菜单
//
// Import menu from JSON file | 从JSON文件导入菜单
//
// Parameters:
//  + name: file
//    require: true
//    in: formData
//    type: file
//
// Responses:
//  200: BaseDataInfo

func ImportMenuFromFileHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := menu.NewImportMenuFromFileLogic(r, svcCtx)
		resp, err := l.ImportMenuFromFile()
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
