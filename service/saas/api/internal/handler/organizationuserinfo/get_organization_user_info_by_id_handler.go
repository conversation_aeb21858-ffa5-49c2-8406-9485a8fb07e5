package organizationuserinfo

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/organizationuserinfo"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route get /saas/api/v1/organization_user/{id} organizationuserinfo GetOrganizationUserInfoById
//
// Get organization user info by Id | 通过ID获取OrganizationUserInfo
//
// Get organization user info by Id | 通过ID获取OrganizationUserInfo
//
// Parameters:
//  + name: id
//    require: true
//    in: path
//    type: string/
//
// Parameters:
//  + name: organizationId
//    require: true
//    in: query
//    type: string
//
// Responses:
//  200: OrganizationUserInfoInfoResp

func GetOrganizationUserInfoByIdHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetOrganizationUserInfoByIdReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := organizationuserinfo.NewGetOrganizationUserInfoByIdLogic(r.Context(), svcCtx)
		resp, err := l.GetOrganizationUserInfoById(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
