package organizationuserinfo

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/organizationuserinfo"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route get /saas/api/v1/organization_user/list organizationuserinfo GetOrganizationUserInfoList
//
// Get organization user info list | 获取OrganizationUserInfo列表
//
// Get organization user info list | 获取OrganizationUserInfo列表
//
// Parameters:
//  + name: page
//    require: false
//    in: query
//    type: number
//
//  + name: pageSize
//    require: false
//    in: query
//    type: number
//
//  + name: organizationId
//    require: true
//    in: query
//    type: string
//
// Responses:
//  200: OrganizationUserInfoListResp

func GetOrganizationUserInfoListHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.OrganizationUserInfoListReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := organizationuserinfo.NewGetOrganizationUserInfoListLogic(r.Context(), svcCtx)
		resp, err := l.GetOrganizationUserInfoList(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
