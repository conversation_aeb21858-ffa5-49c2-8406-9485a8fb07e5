package organizationuserinfo

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"phoenix/service/saas/api/internal/logic/organizationuserinfo"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

// swagger:route get /saas/api/v1/organization_user/list/tree organizationuserinfo GetOrganizationUserInfoTree
//
// Get organization user info tree | 获取OrganizationUserInfo树形
//
// Get organization user info tree | 获取OrganizationUserInfo树形
//
// Responses:
//  200: OrganizationUserInfoTreeResp

func GetOrganizationUserInfoTreeHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.OrganizationUserInfoTreeReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := organizationuserinfo.NewGetOrganizationUserInfoTreeLogic(r.Context(), svcCtx)
		resp, err := l.GetOrganizationUserInfoTree(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
