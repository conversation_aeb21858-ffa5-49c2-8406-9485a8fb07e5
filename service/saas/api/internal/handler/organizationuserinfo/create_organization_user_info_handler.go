package organizationuserinfo

import (
	"gitlab.zhijiasoft.com/paperless-group/saas-common/http_utils"
	"net/http"
	"phoenix/service/saas/api/internal/logic/organizationuserinfo"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"

	"github.com/zeromicro/go-zero/rest/httpx"
)

// swagger:route post /saas/api/v1/organization_user/create organizationuserinfo CreateOrganizationUserInfo
//
// Create organization user info information | 创建OrganizationUserInfo
//
// Create organization user info information | 创建OrganizationUserInfo
//
// Parameters:
//  + name: body
//    require: true
//    in: body
//    type: AddOrganizationUserReq
//
// Responses:
//  200: BaseDataInfo

func CreateOrganizationUserInfoHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.AddOrganizationUserReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, http_utils.RaiseValidateErr(""))
			return
		}

		l := organizationuserinfo.NewCreateOrganizationUserInfoLogic(r.Context(), svcCtx)
		resp, err := l.CreateOrganizationUserInfo(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
