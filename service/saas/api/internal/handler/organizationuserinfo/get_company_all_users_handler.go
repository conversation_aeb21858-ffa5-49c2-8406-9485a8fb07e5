package organizationuserinfo

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"
	"phoenix/service/saas/api/internal/logic/organizationuserinfo"
	"phoenix/service/saas/api/internal/svc"
)

func GetCompanyAllUsersHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		l := organizationuserinfo.NewGetCompanyAllUsersLogic(r.Context(), svcCtx)
		resp, err := l.GetCompanyAllUsers()
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
