package organizationuserinfo

import (
	"net/http"

	"github.com/zeromicro/go-zero/rest/httpx"

	"phoenix/service/saas/api/internal/logic/organizationuserinfo"
	"phoenix/service/saas/api/internal/svc"
	"phoenix/service/saas/api/internal/types"
)

// swagger:route get /saas/api/v1/organization_user/organization/{id} organizationuserinfo GetUserOrganizations
//
// Get user's Organization  | 通过用户ID获取用户绑定的组织架构
//
// Get user's Organization  | 通过用户ID获取用户绑定的组织架构
//
//
// Parameters:
//  + name: id
//    require: true
//    in: path
//    type: string
//
// Responses:
//  200: GetUserOrganizationByIdResp

func GetUserOrganizationsHandler(svcCtx *svc.ServiceContext) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		var req types.GetUserOrganizationByIdReq
		if err := httpx.Parse(r, &req); err != nil {
			httpx.ErrorCtx(r.Context(), w, err)
			return
		}

		l := organizationuserinfo.NewGetUserOrganizationsLogic(r.Context(), svcCtx)
		resp, err := l.GetUserOrganizations(&req)
		if err != nil {
			err = svcCtx.Trans.TransError(r.Context(), err)
			httpx.ErrorCtx(r.Context(), w, err)
		} else {
			httpx.OkJsonCtx(r.Context(), w, resp)
		}
	}
}
