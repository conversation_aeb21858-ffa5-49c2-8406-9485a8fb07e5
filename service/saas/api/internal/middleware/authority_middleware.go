package middleware

import (
	"context"
	"net/http"
	"phoenix/service/saas/api/internal/config"
	"phoenix/service/saas/api/internal/errs"
	"phoenix/service/saas/utils"

	"gitee.com/damengde/gzconfigcenter"
	"github.com/golang-jwt/jwt/v4"
	"github.com/zeromicro/go-zero/core/logx"
	"github.com/zeromicro/go-zero/rest/httpx"
)

const (
	jwtAudience    = "aud"
	jwtExpire      = "exp"
	jwtId          = "jti"
	jwtIssueAt     = "iat"
	jwtIssuer      = "iss"
	jwtNotBefore   = "nbf"
	jwtSubject     = "sub"
	noDetailReason = "no detail reason"
)

type TokenAuthMiddleware struct {
	Config gzconfigcenter.ConfigManager[config.Config]
}

func NewTokenAuthMiddleware(config gzconfigcenter.ConfigManager[config.Config]) *TokenAuthMiddleware {
	return &TokenAuthMiddleware{
		Config: config,
	}
}

func (m *TokenAuthMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		if !m.Config.GetConfig().Auth.OpenTokenAuth {
			next(w, r)
			return
		}

		// token是否为空
		var token string
		if token = r.URL.Query().Get("Authorization"); token == "" {
			token = r.Header.Get("Authorization")
			if token == "" {
				httpx.ErrorCtx(r.Context(), w, errs.TokenError)
				return
			}
		}

		// 解析token
		claims := jwt.MapClaims{}
		jwtToken, err := jwt.ParseWithClaims(token, claims, func(token *jwt.Token) (any, error) {
			return []byte(m.Config.GetConfig().Auth.AccessSecret), nil
		})
		if err != nil {
			httpx.ErrorCtx(r.Context(), w, errs.TokenError)
			return
		}

		// 验证token是否有效
		if !jwtToken.Valid {
			httpx.ErrorCtx(r.Context(), w, errs.TokenError)
			return
		}

		// set context
		ctx := r.Context()
		for k, v := range claims {
			switch k {
			case jwtAudience, jwtExpire, jwtId, jwtIssueAt, jwtIssuer, jwtNotBefore, jwtSubject:
				// ignore the standard claims
			default:

				if v == nil {
					continue
				}

				ctx = context.WithValue(ctx, k, v)
			}
		}

		// token放入上下文
		ctx = context.WithValue(ctx, "Token", token)

		// 日志加入用户id和租户id
		ctx = logx.ContextWithFields(ctx, logx.Field("userId", utils.GetContextUserID(ctx)),
			logx.Field("tenantId", utils.GetContextTenantID(ctx)))

		newReq := r.WithContext(ctx)

		next(w, newReq)
	}
}
