package middleware

import (
	"context"
	"net/http"
	"phoenix/service/saas/api/internal/config"
	"phoenix/service/saas/api/internal/errs"
	"phoenix/service/saas/utils"
	"strings"

	"gitee.com/damengde/gzconfigcenter"
	"github.com/zeromicro/go-zero/core/logc"
	"github.com/zeromicro/go-zero/rest/httpx"
)

const (
	SessionPrefix = "X-Session-"
)

type HeaderForwardAuthMiddleware struct {
	Config gzconfigcenter.ConfigManager[config.Config]
}

func NewHeaderForwardAuthMiddleware(config gzconfigcenter.ConfigManager[config.Config]) *HeaderForwardAuthMiddleware {
	return &HeaderForwardAuthMiddleware{
		Config: config,
	}
}

func (m *HeaderForwardAuthMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		if !m.Config.GetConfig().Auth.OpenHeaderForwardAuth {
			next(w, r)
			return
		}
		// 遍历header
		ctx := r.Context()
		for key, values := range r.Header {
			if strings.HasPrefix(key, SessionPrefix) && len(values) > 0 {
				sessionKey := strings.TrimPrefix(key, SessionPrefix)
				ctx = context.WithValue(ctx, utils.KebabToCamel(sessionKey), values[0])
			}
		}
		if utils.GetContextUserID(ctx) == "" || utils.GetContextTenantID(ctx) == "" {
			logc.Error(r.Context(), "userId or tenantId not exist")
			w.WriteHeader(http.StatusUnauthorized)
			httpx.Error(w, errs.TokenError)
			return
		}

		r = r.WithContext(ctx)
		next(w, r)
	}
}
