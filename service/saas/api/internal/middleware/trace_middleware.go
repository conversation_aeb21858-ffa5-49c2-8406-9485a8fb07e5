package middleware

import (
	"context"
	"net/http"
	"phoenix/service/saas/utils"
	"phoenix/service/saas/utils/uuidx"

	"github.com/zeromicro/go-zero/core/logx"
)

type TraceMiddleware struct {
}

func NewTraceMiddleware() *TraceMiddleware {
	return &TraceMiddleware{}
}

func (m *TraceMiddleware) Handle(next http.HandlerFunc) http.HandlerFunc {
	return func(w http.ResponseWriter, r *http.Request) {
		traceId := r.Header.Get("X-Trace-Id")
		if traceId == "" {
			traceId = uuidx.NewUUID().String()
		}
		// 日志加入traceId
		ctx := logx.ContextWithFields((context.WithValue(r.Context(), utils.TraceIdKey, traceId)), logx.Field("traceId", traceId))

		next(w, r.WithContext(ctx))
	}
}
