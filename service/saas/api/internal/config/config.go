package config

import (
	"path"
	"phoenix/service/saas/utils/async_logger"
	"phoenix/service/saas/utils/minio"
	"slices"
	"strings"

	"github.com/zeromicro/go-zero/core/stores/redis"
	"github.com/zeromicro/go-zero/rest"
	"github.com/zeromicro/go-zero/zrpc"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/config"
	"gitlab.zhijiasoft.com/paperless-group/saas-common/plugins/casbin"
)

type Config struct {
	rest.RestConf
	Auth                AuthConf
	Authenticators      config.Authenticators `json:"authenticators,optional"`
	DatabaseConf        config.DatabaseConf
	Redis               redis.RedisConf
	CasbinConf          casbin.CasbinConf
	Captcha             Captcha
	AuthorityConf       config.AuthorityConf
	UploadConf          UploadConf
	FileServerConf      FileServerConf
	AccountLockDuration int
	MinioConf           *minio.MinioConf
	AsyncLoggerConf     *async_logger.AsyncLoggerConf
	StargateRpc         zrpc.RpcClientConf
	SnowflakeConf       Snowflake
	KafkaConf           KafkaConf
	WorkflowConf        WorkflowConf
}

type Captcha struct {
	KeyLong   int // captcha length
	ImgWidth  int // captcha width
	ImgHeight int // captcha height
}

type UploadConf struct {
	MaxImageSize          int64
	MaxVideoSize          int64
	MaxAudioSize          int64
	MaxOtherSize          int64
	StorePath             string
	FileEncryptSkipSuffix []string
}

type FileServerConf struct {
	DownloadUrlPrefix     string
	OpenDownloadUrlPrefix string
	StorageStrategy       string
}

func (f *FileServerConf) GenerateDownloadUrl(fileId string) string {
	if f.DownloadUrlPrefix == "" || fileId == "" {
		return ""
	}
	return f.DownloadUrlPrefix + fileId
}
func (f *FileServerConf) GenerateDownloadUrlNew(fileId, filePath string, conf Config) string {
	if f.DownloadUrlPrefix == "" || f.OpenDownloadUrlPrefix == "" || fileId == "" {
		return ""
	}
	if slices.Contains(conf.UploadConf.FileEncryptSkipSuffix, strings.ToLower(path.Ext(filePath))) {
		return path.Join(f.OpenDownloadUrlPrefix, filePath)
	}
	return f.DownloadUrlPrefix + fileId
}

// AuthConf is a JWT config
type AuthConf struct {
	AccessSecret          string `json:",optional,env=AUTH_SECRET"`
	AccessExpire          int64  `json:",optional,env=AUTH_EXPIRE"`
	OpenTokenAuth         bool   `json:",optional,env=OPEN_TOKEN_AUTH"`
	OpenHeaderForwardAuth bool   `json:",default=true,env=OPEN_HEADER_FORWARD_AUTH"`
}
type Snowflake struct {
	//节点占位
	NodeBits uint8
	// 步骤占位
	StepBits uint8
	// 元年
	Epoch int64
	// 默认节点
	Node int64
}

type KafkaConf struct {
	Brokers   []string
	MinBytes  int
	MaxBytes  int
	Consumers []QueueConf `json:",optional"`
}

type QueueConf struct {
	Topic   string
	GroupID string
	Key     string
}

type WorkflowConf struct {
	FlowEventPushTopic string
}
