syntax = "v1"

info(
	title: "captcha api"
	desc: "captcha api"
	version: "v1.0"
)



// The information of captcha | 验证码数据
type CaptchaInfo {
	CaptchaId string `json:"captchaId"`
	ImgPath   string `json:"imgPath"`
}

// The response data of captcha | 验证码返回数据
type CaptchaResp {
    BaseDataInfo

    // The data of captcha | 验证码信息数据
    Data CaptchaInfo `json:"data"`
}

@server(
	group: captcha
	prefix: /saas/api/v1
)

service phoenix {
	// Get captcha | 获取验证码
	@handler getCaptcha
	get /captcha returns (CaptchaResp)
}