syntax = "v1"

info(
    title: "dataExportRecord"
    desc: "dataExportRecord"
)

type (
    DataExportRecordInfo {
        ID string `json:"id,optional"`
        TaskID string `json:"taskId"`
        FileID string `json:"fileId,optional"`
        FileName string `json:"fileName,optional"`
        ModuleName string `json:"moduleName,optional"`
        Status uint `json:"status"`
        UserID string `json:"userId,optional"`
        CreatedAt int64 `json:"createdAt,optional"`
    }

    DataExportRecordListReq {
        PageInfo
        Search string `form:"search,optional"`
        ModuleName string `form:"moduleName,optional"`
        Status uint `form:"status,optional"`
    }

    DataExportRecordListResp {
        BaseDataInfo
        Data DataExportRecordListInfo `json:"data"`
    }
    DataExportRecordListInfo {
        Total int64 `json:"total"`
        Data []DataExportRecordInfo `json:"data"`
    }

    UpdateDataExportRecordReq {
        ID string `json:"id"`
    }
)

@server(
    // jwt: Auth
    group: dataexportrecord
    middleware: TraceMiddleware,AuthMiddleware,Authority
    prefix: /saas/api/v1
)

service phoenix {
    // 获取数据导出记录列表
    @handler getDataExportList
    get /data_export/list (DataExportRecordListReq) returns (DataExportRecordListResp)

    // 通过uuid修改数据导出状态
    @handler updateDataExportStatusById
    post /data_export/update/by/id (UpdateDataExportRecordReq) returns (BaseDataInfo)
}