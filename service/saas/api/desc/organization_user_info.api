

type (
    // The response data of organization user info information | OrganizationUserInfo信息
    OrganizationUserInfoInfo {
        BaseIDInfo

        // Sort
        Sort uint32 `json:"sort,optional"`

        // OrganizationId
        OrganizationId string `json:"organizationId,optional"`

        // UserId
        UserId string `json:"userId,optional"`

        // Extra
        Extra string `json:"extra,optional"`
    }



    AddOrganizationUserReq {
        // Status | 状态
        Status bool `json:"status,optional"`

        // Username | 用户名（必填）
        Username string `json:"username"`

        // Password | 密码（必填）
        Password string `json:"password,omitempty,optional"`

        // Nickname | 昵称
        Nickname string `json:"nickname,optional"`

        // Mobile | 手机号
        Mobile string `json:"mobile,optional"`

        // Email | 邮箱
        Email string `json:"email,optional"`
    
        // Gender
        Gender string `json:"gender,optional"`

        // Post | 职务
        Post string `json:"post,optional"`

        // Avatar | 头像
        AvatarID string `json:"avatarId,optional"`

        // organizationId | 组织架构ID
        OrganizationId string `json:"organizationId"`

        // Sort
        Sort uint32 `json:"sort,optional"`

        // Extra
        Extra string `json:"extra,optional"`
        // Kind | 用户类型
        Kind string `json:"kind,optional"`
        // DeviceNo | 设备号
        DeviceNo string `json:"deviceNo,optional"`

        // 职位ID列表
        positionIds []string `json:"positionIds,optional"`
    }

        // update Organization user request | updateOrganizationUser请求体
    UpdateOrganizationUserReq {
        BaseIDInfo
        AddOrganizationUserReq
    }

        // The response data of organization user info list | OrganizationUserInfo列表数据
    OrganizationUserInfoListResp {
        BaseDataInfo

        // OrganizationUserInfo list data | OrganizationUserInfo列表数据
        Data OrganizationUserInfoListInfo `json:"data"`
    }

    OrganizationUserInfoTreeResp {
        BaseDataInfo
        Data []*OrganizationUserInfo `json:"data"`
    }

        // OrganizationUserInfo list data | OrganizationUserInfo列表数据
    OrganizationUserInfoListInfo {
        BaseListInfo

        // The API list data | OrganizationUserInfo列表数据
        Data []OrganizationUser `json:"data"`
    }

        // Get organization user info list request params | OrganizationUserInfo列表请求参数
    OrganizationUserInfoListReq {
        PageInfo
        OrganizationId string `json:"organizationId,optional" form:"organizationId,optional"`
        CommonSearchInfo
    }

    OrganizationUserInfoTreeReq {
        OrganizationId string `json:"organizationId,optional" form:"organizationId,optional"`
        DescendantNodeTypes []int `json:"descendantNodeTypes,optional" form:"descendantNodeTypes,optional"`
        CommonSearchInfo
    }

        // OrganizationUserInfo information response | OrganizationUserInfo信息返回体
    OrganizationUserInfoInfoResp {
        BaseDataInfo

        // OrganizationUserInfo information | OrganizationUserInfo数据
        Data OrganizationUser `json:"data"`
    }

        // Organization users | OrganizationUsers
    OrganizationUser {
        UserInfo
        // Sort
        Sort uint32 `json:"sort,optional"`
        //    Extra | 额外信息
        Extra string `json:"Extra,optional"`
        // 是否领导
        IsLeader bool `json:"isLeader,optional"`
        // 是否管理员
        IsAdmin bool `json:"isAdmin,optional"`
        // 组织用户岗位
        OrganizationUserPosition []OrganizationUserPositionInfo `json:"organizationUserPosition"`
    }

    // 组织用户岗位
    OrganizationUserPositionInfo {
        PositionId string `json:"positionId"`
        PositionName string `json:"positionName"`
    }

    OrganizationUserInfo {
        ParentId string `json:"parentId,optional"`
        OrgID string `json:"orgId,optional"`
        OrgName string `json:"orgName,optional"`
        OrganizationUsers []OrganizationUsers `json:"userInfo,optional"`
        Children []*OrganizationUserInfo `json:"children,optional"`
    }

    OrganizationUsers {
        UserID string `json:"userId,optional"`
        Nickname string `json:"nickname,optional"`
    }

//    OrganizationUserInfo {
//        Id  string  `json:"id,optional"`
//        Name string  `json:"name,optional"`
//        Code string  `json:"code,optional"`
//        Sort uint32 `json:"sort,optional"`
//        Leader string `json:"leader,optional"`
//        ParentId string  `json:"parentId,optional"`
//        Status bool `json:"status,optional"`
//        Users []OrganizationUserInfoUserInfo `json:"users,optional"`
//        Children []OrganizationUserInfo `json:"children,optional"`
//
//    }
//
//    OrganizationUserInfoUserInfo {
//        Id  string  `json:"id,optional"`
//        Nickname string  `json:"nickname,optional"`
//        Gender string  `json:"gender,optional"`
//        Sort uint32 `json:"sort,optional"`
//        Mobile string `json:"mobile,optional"`
//        Email string  `json:"email,optional"`
//        Status bool `json:"status,optional"`
//        Avatar AvatarInfo `json:"avatar,optional"`
//    }

    GetOrganizationUserInfoByIdReq {
        IDPathReq
        OrganizationId string `json:"organizationId,optional" form:"organizationId"`
    }

    DeleteOrganizationUserInfoByIdsReq {
        IDsReq
        OrganizationId string `json:"organizationId"`
    }

    GetUserOrganizationByIdReq {
        IDPathReq
    }

    GetUserOrganizationByIdResp {
        BaseDataInfo
        Data []*GetUserOrganizationInfo `json:"data"`
    }

    GetUserOrganizationInfo {
        Id  string  `json:"id"`
        Name  string `json:"name,optional"`
    }

    // 添加组织领导和管理员
    AddOrganizationLeaderAndAdminReq {
        // 组织id
        OrganizationID string `json:"organizationId"`
        // 领导id(userId)
        LeaderIds []string `json:"leaderIds,optional"`
        // 管理员id(userId)
        AdminIds []string `json:"adminIds,optional"`
    }

        // 删除组织领导和管理员
    DeleteOrganizationLeaderAndAdminReq {
        // 组织id
        OrganizationID string `json:"organizationId"`
        // 领导id(userId)
        LeaderIds []string `json:"leaderIds,optional"`
        // 管理员id(userId)
        AdminIds []string `json:"adminIds,optional"`
    }

        // 获取用户所属公司和集团
    GetUserGroupsAndCompaniesResp {
        BaseDataInfo
        data []UserGroupsAndCompaniesInfo `json:"data"`
    }

    UserGroupsAndCompaniesInfo {
        Label string `json:"label"`
        Value string `json:"value"`
    }

    DragSortOrganizationUserReq {
        // 组织id
        OrganizationId string `json:"organizationId"`
        // 用户id
        UserId string `json:"userId"`
        // 目标用户id
        TargetId string `json:"targetId"`
        // 放置位置 top：上， bottom：下
        Position string `json:"position"`
    }
)


type UserSimpleInfo {
    Id string `json:"id"`
    Nickname string `json:"nickname"`
}

type UserSimpleInfoListResp {
    BaseDataInfo
    Data []UserSimpleInfo `json:"data"`
}


type GetUserBelongDepartmentResp {
    BaseDataInfo
    Data []GetUserOrganizationInfo `json:"data"`
}

@server(
    // jwt: Auth
    group: organizationuserinfo
    middleware: AuthMiddleware,Authority,TraceMiddleware
    prefix: /saas/api/v1
)

service phoenix {
    // Create organization user info information | 创建OrganizationUserInfo
    @handler createOrganizationUserInfo
    post /organization_user/create (AddOrganizationUserReq) returns (BaseDataInfo)

    // Update organization user info information | 更新OrganizationUserInfo
    @handler updateOrganizationUserInfo
    post /organization_user/update (UpdateOrganizationUserReq) returns (BaseDataInfo)

    // Delete organization user info information | 删除OrganizationUserInfo信息
    @handler deleteOrganizationUserInfo
    post /organization_user/delete (DeleteOrganizationUserInfoByIdsReq) returns (BaseDataInfo)

    // Get organization user info list | 获取OrganizationUserInfo列表
    @handler getOrganizationUserInfoList
    get /organization_user/list (OrganizationUserInfoListReq) returns (OrganizationUserInfoListResp)

    // Get organization user info tree | 获取OrganizationUserInfo树形
    @handler getOrganizationUserInfoTree
    post /organization_user/list/tree (OrganizationUserInfoTreeReq) returns (OrganizationUserInfoTreeResp)

    // Get user's Organization  | 通过用户ID获取用户绑定的组织架构
    @handler getUserOrganizations
    get /organization_user/organization/:id (GetUserOrganizationByIdReq) returns (GetUserOrganizationByIdResp)

    // Get organization user info by ID | 通过ID获取OrganizationUserInfo
    @handler getOrganizationUserInfoById
    get /organization_user/:id (GetOrganizationUserInfoByIdReq) returns (OrganizationUserInfoInfoResp)

    // 添加组织领导和管理员
    @handler addOrganizationLeaderAndAdmin
    post /organization_user/leader-admin/add (AddOrganizationLeaderAndAdminReq) returns (BaseDataInfo)

    // 获取本级里的所有用户
    @handler getThisLevelAllUsers
    get /organization_user/level/all/user/list (OrganizationUserInfoListReq) returns (OrganizationUserInfoListResp)

    // 获取集团所有人员
    @handler getGroupAllUsers
    get /organization_user/group/all/users () returns (UserSimpleInfoListResp)

    // 获取公司所有人员
    @handler getCompanyAllUsers
    get /organization_user/company/all/users () returns (UserSimpleInfoListResp)

    // 集团本级所有人员
    @handler getGroupUsers
    get /organization_user/group/users () returns (UserSimpleInfoListResp)
  
   // 人员归属部门
   @handler getUserBelongDepartment
   get /organization_user/user/belong/department () returns (GetUserBelongDepartmentResp)

    // 获取用户所属公司和集团
    @handler GetUserGroupsAndCompanies
    get /organization_user/group-companies returns (GetUserGroupsAndCompaniesResp)

    // 拖动排序
    @handler dragSortOrganizationUser
    post /organization_user/user/drag/sort (DragSortOrganizationUserReq) returns (BaseDataInfo)

}
