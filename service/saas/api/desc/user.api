

type (
    // The response data of user information | User信息
    UserInfo {
        BaseIDInfo

        // Status
        Status bool `json:"status,optional"`

        // Username
        Username string `json:"username,optional"`

        // Password
        Password string `json:"password,optional,omitempty"`

        // Nickname
        Nickname string `json:"nickname,optional"`

        // Mobile
        Mobile string `json:"mobile,optional"`

        // Email
        Email string `json:"email,optional"`

        // Gender
        Gender string `json:"gender,optional"`

        // Post | 职务
        Post string `json:"post,optional"`

        // AvatarId
        AvatarId string `json:"avatarId,optional"`

        // Avatar
        Avatar AvatarInfo `json:"avatar,optional"`

        // DefaultTenantId
        DefaultTenantId string `json:"DefaultTenantId,optional"`

        // DeviceNo | 设备号
        DeviceNo string `json:"deviceNo,optional"`

        // Kind | 用户类型
        Kind string `json:"kind,optional"`

        // Imei | 设备唯一标识
        Imei string `json:"imei,optional"`

        //租户信息
        TenantInfos []TenantInfo `json:"tenantInfos,optional"`

        OrganizationIDs []string `json:"organizationIDs,optional"`
    }

    AvatarInfo {
        Id string `json:"id"`
        Name string `json:"name"`
        Url string `json:"url"`
    }

        // The request data of user information | UserCreateReq信息
    UserCreateReq {
        // Status | 状态
        Status bool `json:"status,optional"`

        // Username | 用户名（必填）
        Username string `json:"username"`

        // Password | 密码（必填）
        Password string `json:"password,omitempty"`

        // Nickname | 昵称
        Nickname string `json:"nickname,optional"`

        // Mobile | 手机号
        Mobile string `json:"mobile,optional"`

        // Email | 邮箱
        Email string `json:"email,optional"`

        // Gender
        Gender string `json:"gender,optional"`

        // Post | 职务
        Post string `json:"post,optional"`

        // Avatar | 头像
        AvatarId string `json:"avatarId,optional"`

        // DefaultTenantId | 默认租户ID
        DefaultTenantId string `json:"tenantId,optional"`

        // DeviceNo | 设备号
        DeviceNo string `json:"deviceNo,optional"`

        // Kind | 用户类型
        Kind string `json:"kind,optional"`

        // Imei | 设备唯一标识
        Imei string `json:"imei,optional"`

        OrganizationIDs []string `json:"organizationIds,optional"`
    }

        // The response data of user list | User列表数据
    UserListResp {
        BaseDataInfo

        // User list data | User列表数据
        Data UserListInfo `json:"data"`
    }

        // User list data | User列表数据
    UserListInfo {
        BaseListInfo

        // The API list data | User列表数据
        Data []UserInfo `json:"data"`
    }

        // Get user list request params | User列表请求参数
    UserListReq {
        PageInfo

        // Username
        Username string `json:"username,optional" form:"username,optional"`

        // Nickname
        Nickname string `json:"nickname,optional " form:"nickname,optional"`

        // Mobile
        Mobile string `json:"mobile,optional " form:"mobile,optional"`

        // Email
        Email string `json:"email,optional " form:"email,optional"`

        // IgnoreTenant | 忽略租户限制，此条件仅在当前租户为管理租户时生效
        IgnoreTenant bool `json:"ignoreTenant,optional " form:"ignoreTenant,optional"`

        Kind string `json:"kind,optional " form:"kind,optional"`

        // Imei | 设备唯一标识
        Imei string `json:"imei,optional" form:"imei,optional"`

        DeviceNo string `json:"deviceNo,optional" form:"deviceNo,optional"`

        // Status ｜ 状态
        Status *bool `json:"status,optional" form:"status,optional"`

        CommonSearchInfo
    }

        // User information response | User信息返回体
    UserInfoResp {
        BaseDataInfo

        // User information | User数据
        Data UserInfo `json:"data"`
    }


    UserInfoWithExtraInfo {
        BaseIDInfo

        // Status
        Status bool `json:"status,optional"`

        // Username
        Username string `json:"username,optional"`

        // Nickname
        Nickname string `json:"nickname,optional"`

        // Mobile
        Mobile string `json:"mobile,optional"`

        // Email
        Email string `json:"email,optional"`

        // Gender
        Gender string `json:"gender,optional"`

        // Post | 职务
        Post string `json:"post,optional"`

        // AvatarId
        AvatarId string `json:"avatarId,optional"`

        // Avatar
        Avatar AvatarInfo `json:"avatar,optional"`
        // Kind
         Kind string `json:"kind,optional"`

        // Imei | 设备唯一标识
        Imei string `json:"imei,optional"`

        // DefaultTenantId
        DefaultTenantId string `json:"DefaultTenantId,optional"`

        //    SSO Info ｜ 单点认证附属信息
        SSOUserInfo map[string]interface{} `json:"ssoUserInfo,omitempty"`

        // Tenants
        Tenants []TenantInfo `json:"tenants,optional"`
        SystemPlugins []SystemPlugin `json:"systemPlugins,optional"`
        MaxConferenceAgendaTitle int64 `json:"maxConferenceAgendaTitle,optional"`
        // 用户当前登录的组织id
        CurrentOrganization string `json:"currentOrganization,optional"`

        // 是否是管理员
        IsAdmin bool `json:"isAdmin,optional"`
    }

        // User information response | User信息返回体
    UserInfoWithExtraInfoResp {
        BaseDataInfo

        // User information | User数据
        Data UserInfoWithExtraInfo `json:"data"`
    }

        // UpdateUsersPasswordReq | 批量更新用户密码请求体
    UpdateUsersPasswordReq {
        // Operator password | 操作员密码
        operatorPW string `json:"operatorPW,optional"`
        //         new password | 新密码
        newPass string `json:"newPass,optional"`
        // User id list  | 用户ID列表
        userIds []string `json:"userIds"`
    }
        // Get user list request params | User列表请求参数
    UserListPReq {
        PageInfo

        // Username
        Username string `json:"username,optional"`

        // Nickname
        Nickname string `json:"nickname,optional"`

        // Mobile
        Mobile string `json:"mobile,optional"`

        // Email
        Email string `json:"email,optional"`

        // IgnoreTenant | 忽略租户限制，此条件仅在当前租户为管理租户时生效
        IgnoreTenant bool `json:"ignoreTenant,optional"`

        CommonSearchInfo
        Ids          []string  `json:"ids,optional"`
    }

        // The response data of user list | User列表数据
    UserListPResp {
        BaseDataInfo

        // User list data | User列表数据
        Data UserListInfo `json:"data"`
    }

    //  根据手机号查询用户
    UserByMobileReq {
        Mobile string `form:"mobile"`
    }

    //  根据手机号查询用户
    UserByMobileResp {
        BaseDataInfo   
        Data UserInfo `json:"data"`
    }

    // 根据id查询昵称
    UserNicknamesByIDsReq {
        IDs []string `json:"ids"`
    }

    // 根据id查询昵称
    UserNicknamesByIDsResp {
        BaseDataInfo
        Data map[string]string `json:"data"`
    }
)


type GetOnlineUsersReq {
}

type GetOnlineUsersResponse {
    // 登录设备列表
    OnlineUsers []OnlineUserInfo `json:"data"`
}

type OnlineUserInfo {
    // 登录设备类型
    DeviceKind string `json:"deviceKind"`
    IP         string `json:"ip"`
    IPCity     string `json:"ipCity"`
    IPDistrict string `json:"ipDistrict"`
    IPProvince string `json:"ipProvince"`
    // 手机号
    Mobile     string `json:"mobile"`
    // 所属租户
    TenantName string `json:"tenantName"`
    // 用户id
    UserID     string `json:"userId"`
    // 用户名
    Username   string `json:"username"`
    Nickname   string `json:"nickname"`
    LoginDate  int64 `json:"loginDate"`
    TokenId    string `json:"tokenId"`
}

// 强制登出
type ForceLogoutReq {
    TokenId string `json:"tokenId"`
}

type ForceLogoutResp {  

}

type GetUserListByMobileReq {
    Mobiles []string `json:"mobiles"`
}

type GetUserListByMobileResp {
    BaseDataInfo
    Data []UserInfo `json:"data"`
}

@server(
    // jwt: Auth
    group: user
    middleware: AuthMiddleware,Authority,TraceMiddleware
    prefix: /saas/api/v1
)

service phoenix {
    // Create user information | 创建User
    @handler createUser
    post /user/create (UserCreateReq) returns (BaseDataInfo)

    // Update user information | 更新User
    @handler updateUser
    post /user/update (UserInfo) returns (BaseDataInfo)

    // Delete user information | 删除User信息
    @handler deleteUser
    post /user/delete (IDsReq) returns (BaseDataInfo)

    // Get user list | 获取User列表
    @handler getUserList
    get /user/list (UserListReq) returns (UserListResp)

    // Get user list | 获取User列表 post方式
    @handler getUserListPost
    post /user/list (UserListPReq) returns (UserListPResp)

    // Get user by ID | 通过ID获取User
    @handler getUserById
    get /user/:id (IDPathReq) returns (UserInfoResp)

    // Get own user info | 获取自己的User信息
    @handler getUserInfo
    get /user/info returns (UserInfoWithExtraInfoResp)

    // bulk Update user password | 批量更新User密码
    @handler updateUsersPassword
    post /user/update_users_password (UpdateUsersPasswordReq) returns (BaseDataInfo)

    //  根据手机号查询用户
    @handler getUserByMobile
    get /user/info/bymobile (UserByMobileReq) returns (UserByMobileResp)   

    // 根据id查询昵称
    @handler getUserNicknamesByIDs
    post /user/nicknames/byids (UserNicknamesByIDsReq) returns (UserNicknamesByIDsResp)


    // 在线用户列表
    @handler getOnlineUserList
    get /user/online/list (GetOnlineUsersReq) returns (GetOnlineUsersResponse)

    // 强制登出
    @handler forceLogout
    post /user/online/force_logout (ForceLogoutReq) returns (ForceLogoutResp)

    //根据号获取用户列表
    @handler getUserListByMobiles
    post /user/list/bymobiles (GetUserListByMobileReq) returns (GetUserListByMobileResp)
}
