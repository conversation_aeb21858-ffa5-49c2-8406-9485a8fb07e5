

type (
    // The response data of role information | Role信息
    RoleInfo {
        BaseIDInfo

        // Status
        Status  bool `json:"status,optional"`

        // Sort
        Sort  uint32 `json:"sort,optional"`

        // TenantId
        TenantId  string `json:"tenantId,optional"`

        // Name
        Name  string `json:"name,optional"`

        // Code
        Code  string `json:"code,optional"`

        // DefaultRouter
        DefaultRouter  string `json:"defaultRouter,optional"`

        // Remark
        Remark  string `json:"remark,optional"`

        // ParentId
        ParentId  string `json:"parentId,optional"`

        // OrganizationId
        OrganizationId string `json:"organizationId,optional"`
    }

    // The request data of role information | RoleCreate信息
    RoleCreateReq {

        // Status
        Status  bool `json:"status,optional"`

        // Sort
        Sort  uint32 `json:"sort,optional"`

        // TenantId
        TenantId  string `json:"tenantId,optional"`

        // Name
        Name  string `json:"name,optional"`

        // Code
        Code  string `json:"code,optional"`

        // DefaultRouter
        DefaultRouter  string `json:"defaultRouter,optional"`

        // Remark
        Remark  string `json:"remark,optional"`

        // ParentId
        ParentId  string `json:"parentId,optional"`
    }

    // The response data of role list | Role列表数据
    RoleListResp {
        BaseDataInfo

        // Role list data | Role列表数据
        Data RoleListInfo `json:"data"`
    }

    // Role list data | Role列表数据
    RoleListInfo {
        BaseListInfo

        // The API list data | Role列表数据
        Data  []RoleInfo  `json:"data"`
    }

    // Get role list request params | Role列表请求参数
    RoleListReq {
        PageInfo

        // Name
        Name  string `json:"name,optional"`

        // Code
        Code  string `json:"code,optional"`

        // TenantId
        TenantId  string `json:"tenantId,optional"`

        // DefaultRouter
        DefaultRouter  string `json:"defaultRouter,optional"`

        OrganizationId string `json:"organizationId,optional"`

        CommonSearchInfo
    }

    RoleListByIDsReq {
        // RoleIds | RoleIds
        RoleIds []string `json:"roleIds"`
    }

    RoleListByIDsResp {
        BaseDataInfo
        // Role list data | Role列表数据
        Data  []RoleInfo  `json:"data"`
    }

    // Role information response | Role信息返回体
    RoleInfoResp {
        BaseDataInfo

        // Role information | Role数据
        Data RoleInfo `json:"data"`
    }

        // Get role by ID request params | 通过ID获取Role请求参数
    GetRoleUsersReq {
        // RoleId | RoleId
        RoleId string `path:"roleId"`

        CommonSearchInfo
    }

        // Update role users request params | 更新Role用户请求参数
    UpdataRoleUsersReq {
        // RoleId | RoleId
        RoleId string `json:"roleId"`

        // UserIds | 用户ID列表
        UserIds []string `json:"userIds"`
    }

        // RoleUsersList list data | RoleUsersList列表数据
    RoleUsersList {
        Data []UserInfo `json:"data"`
    }

        // Get role users response | 获取Role用户返回体
    GetRoleUsersResp {
        BaseDataInfo

        // RoleUsersList list data | RoleUsersList列表数据
        Data RoleUsersList `json:"data"`
    }

    GetRoleUsersByRoleCodeReq {
        // RoleCode | RoleCode
        RoleCode string `form:"roleCode"`
    }

    GetRoleUsersByRoleCodeResp {
        BaseDataInfo
        Data []UserInfo `json:"data"`
    }

    CheckUserHasRoleCodeReq {
        // UserId | UserId
        UserId string `form:"userId"`
        // RoleCode | RoleCode
        RoleCode string `form:"roleCode"`
    }

    CheckUserHasRoleCodeResp {
        BaseDataInfo
        Data bool `json:"data"`
    }

    GetRolesByUserIDReq {
        UserID string `form:"userId,optional"`
    }

    RolesResp {
        BaseDataInfo
        Data []string `json:"data"`
    }
)

@server(
    // jwt: Auth
    group: role
    middleware: AuthMiddleware,Authority,TraceMiddleware
    prefix: /saas/api/v1
)

service phoenix {
    // Create role information | 创建Role
    @handler createRole
    post /role/create (RoleCreateReq) returns (BaseDataInfo)

    // Update role information | 更新Role
    @handler updateRole
    post /role/update (RoleInfo) returns (BaseDataInfo)

    // Delete role information | 删除Role信息
    @handler deleteRole
    post /role/delete (IDsReq) returns (BaseDataInfo)

    // Get role list | 获取Role列表
    @handler getRoleList
    get /role/list (RoleListReq) returns (RoleListResp)

    // Get role list by IDs | 通过ID列表获取Role列表
    @handler getRoleListByIDs
    post /roles/by_ids (RoleListByIDsReq) returns (RoleListByIDsResp)

    // Get role by ID | 通过ID获取Role
    @handler getRoleById
    get /role/:id (IDPathReq) returns (RoleInfoResp)

    // Get role users | 获取Role用户
    @handler getRoleUsers
    get /role/users/:roleId (GetRoleUsersReq) returns (GetRoleUsersResp)

    // Update role users | 更新Role用户
    @handler updateRoleUsers
    post /role/update_users (UpdataRoleUsersReq) returns (BaseDataInfo)

    // 根据rolecode 获取用户列表
    @handler getRoleUsersByRoleCode
    get /role/users/rolecode (GetRoleUsersByRoleCodeReq) returns (GetRoleUsersByRoleCodeResp)

    // 检查用户是否有角色代码
    @handler checkUserHasRoleCode
    get /role/check_user_has_role_code (CheckUserHasRoleCodeReq) returns (CheckUserHasRoleCodeResp)

    @handler getRolesByUserID
    get /roles/by-user-id (GetRolesByUserIDReq) returns (RolesResp)
}
