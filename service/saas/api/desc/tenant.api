type (
	// The response data of tenant information | Tenant信息
	TenantInfo {
		BaseIDInfo
		// Uuid
		Uuid string `json:"uuid,optional"`
		// Key
		Key string `json:"key,optional"`
		// Secret
		Secret string `json:"secret,optional"`
		// IsSuper
		IsSuper bool `json:"isSuper,optional"`
		// 售后联系人                           
		AfterSalesContact string `json:"afterSalesContact,optional"`
		// 归属地ID                           
		LocationID string `json:"locationId,optional"`
		// 日志保留天数                          
		LogSaveKeepDays int64 `json:"logSaveKeepDays,optional"`
		// 最大列席用户数                         
		MaxAttendanceUserCount int64 `json:"maxAttendanceUserCount,optional"`
		// 最大设备数                           
		MaxDeviceCount int64 `json:"maxDeviceCount,optional"`
		// 最大上传文件大小，MB                     
		MaxUploadFileSize int64 `json:"maxUploadFileSize,optional"`
		// 最大用户数                           
		MaxUserCount int64 `json:"maxUserCount,optional"`
		// 租户名称                            
		Name string `json:"name,optional"`
		// 负责人                             
		Principal string `json:"principal,optional"`
		// 负责人联系方式                         
		PrincipalContactInformation string `json:"principalContactInformation,optional"`
		// 销售联系人                           
		SaleContact string `json:"saleContact,optional"`
		// 有效期结束时间，毫秒时间戳                   
		ServiceEndAt int64 `json:"serviceEndAt,optional"`
		// 有效期开始时间，毫秒时间戳                   
		ServiceStartAt int64 `json:"serviceStartAt,optional"`
		// 有效状态                            
		Status        bool           `json:"status,optional"`
		SystemPlugins []SystemPlugin `json:"systemPlugins,optional"`
		AiStatus bool `json:"aiStatus,optional"`
		// 最大会议议程标题字数
		MaxConferenceAgendaTitle int64 `json:"maxConferenceAgendaTitle,optional"`
	}
	// TenantUpdate 请求体
	TenantUpdateReq {
		// Uuid
		Uuid string `json:"uuid,optional"`
		// Key
		Key string `json:"key,optional"`
		// Secret
		Secret string `json:"secret,optional"`
		// IsSuper
		IsSuper bool `json:"isSuper,optional"`
		// 售后联系人
		AfterSalesContact string `json:"afterSalesContact,optional"`
		// 租户id，ID
		ID string `json:"id"`
		// 归属地ID
		LocationID string `json:"locationId,optional"`
		// 日志保留天数
		LogSaveKeepDays int64 `json:"logSaveKeepDays,optional"`
		// 最大列席用户数
		MaxAttendanceUserCount int64 `json:"maxAttendanceUserCount,optional"`
		// 最大设备数
		MaxDeviceCount int64 `json:"maxDeviceCount,optional"`
		// 最大上传文件大小，MB
		MaxUploadFileSize int64 `json:"maxUploadFileSize,optional"`
		// 最大用户数
		MaxUserCount int64 `json:"maxUserCount,optional"`
		// 租户名称
		Name string `json:"name,optional"`
		// 负责人
		Principal string `json:"principal,optional"`
		// 负责人联系方式
		PrincipalContactInformation string `json:"principalContactInformation,optional"`
		// 销售联系人
		SaleContact string `json:"saleContact,optional"`
		// 有效期结束时间，毫秒时间戳
		ServiceEndAt int64 `json:"serviceEndAt,optional"`
		// 有效期开始时间，毫秒时间戳
		ServiceStartAt int64 `json:"serviceStartAt,optional"`
		// 有效状态
		Status        bool     `json:"status,optional"`
		SystemPlugins []string `json:"systemPlugins,optional"`
		AiStatus bool `json:"aiStatus,optional"`
		MaxConferenceAgendaTitle int64 `json:"maxConferenceAgendaTitle,optional"`
	}
	// The request data of tenant information | TenantCreate信息
	TenantCreateReq {
		// 售后联系人
		AfterSalesContact string `json:"afterSalesContact,optional"`
		// 归属地ID
		LocationID string `json:"locationId,optional"`
		// 日志保留天数
		LogSaveKeepDays int64 `json:"logSaveKeepDays,optional"`
		// 最大列席用户数
		MaxAttendanceUserCount int64 `json:"maxAttendanceUserCount,optional"`
		// 最大设备数
		MaxDeviceCount int64 `json:"maxDeviceCount,optional"`
		// 最大上传文件大小，MB
		MaxUploadFileSize int64 `json:"maxUploadFileSize,optional"`
		// 最大用户数
		MaxUserCount int64 `json:"maxUserCount,optional"`
		// 租户名称
		Name string `json:"name,optional"`
		// 负责人
		Principal string `json:"principal,optional"`
		// 负责人联系方式
		PrincipalContactInformation string `json:"principalContactInformation,optional"`
		// 销售联系人
		SaleContact string `json:"saleContact,optional"`
		// 有效期结束时间，毫秒时间戳
		ServiceEndAt int64 `json:"serviceEndAt,optional"`
		// 有效期开始时间，毫秒时间戳
		ServiceStartAt int64 `json:"serviceStartAt,optional"`
		// 有效状态
		Status        bool     `json:"status,optional"`
		SystemPlugins []string `json:"systemPlugins,optional"`
	    SecretKey string `json:"secretKey,optional"`
		AiStatus bool `json:"aiStatus,optional"`
		MaxConferenceAgendaTitle int64 `json:"maxConferenceAgendaTitle,optional"`
	}
	// The response data of tenant list | Tenant列表数据
	TenantListResp {
		BaseDataInfo
		// Tenant list data | Tenant列表数据
		Data TenantListInfo `json:"data"`
	}
	// Tenant list data | Tenant列表数据
	TenantListInfo {
		BaseListInfo
		// The API list data | Tenant列表数据
		Data []TenantInfo `json:"data"`
	}
	// Get tenant list request params | Tenant列表请求参数
	TenantListReq {
		PageInfo
		// Extra
		Extra string `json:"extra,optional"`
		// Key
		Key string `json:"key,optional"`
		// Status
		Status bool `json:"status,optional"`
		// Name
		Name string `json:"name,optional"`
		CommonSearchInfo
	}
	// Tenant information response | Tenant信息返回体
	TenantInfoResp {
		BaseDataInfo
		// Tenant information | Tenant数据
		Data TenantInfo `json:"data"`
	}
	UpdateTenantUsersReq {
		TenantId string   `json:"tenantId"`
		UserIds  []string `json:"userIds"`
		Kind    string   `json:"kind"`
	}
	SystemPlugin {
		// 插件id
		ID string `json:"id"`
		// 插件名称
		Name string `json:"name"`
		// 插件代码
		Code string `json:"code"`

	}
)

@server (
	// jwt: Auth
	group:      tenant
	middleware: AuthMiddleware,Authority,TraceMiddleware
	prefix:     /saas/api/v1
)
service phoenix {
	// Create tenant information | 创建Tenant
	@handler createTenant
	post /tenant/create (TenantCreateReq) returns (BaseDataInfo)

	// Update tenant information | 更新Tenant
	@handler updateTenant
	post /tenant/update (TenantUpdateReq) returns (BaseDataInfo)

	// Delete tenant information | 删除Tenant信息
	@handler deleteTenant
	post /tenant/delete (IDsReq) returns (BaseDataInfo)

	// Get tenant list | 获取Tenant列表
	@handler getTenantList
	get /tenant/list (TenantListReq) returns (TenantListResp)

	// Get tenant by ID | 通过ID获取Tenant
	@handler getTenantById
	get /tenant/:id (IDPathReq) returns (TenantInfoResp)

	// Update Tenant users information | 更新TenantUsers
	@handler updateTenantUsers
	post /tenant/update_users (UpdateTenantUsersReq) returns (BaseDataInfo)
}
