

type (
    // The response data of position information | Position信息
    PositionInfo {
        BaseIDInfo

        // Status
        Status bool `json:"status,optional"`

        // Sort
        Sort uint32 `json:"sort,optional"`

        // TenantId
        TenantId string `json:"tenantId,optional"`

        // Name
        Name string `json:"name,optional"`

        // Code
        Code string `json:"code,optional"`

        // Remark
        Remark string `json:"remark,optional"`

        // 组织id(集团 or 公司)
        OrganizationID string `json:"organizationId,optional"`
    }
    // The response data of position information | Position信息
    PositionCreateReq {

        // Status
        Status bool `json:"status,optional"`

        // Sort
        Sort uint32 `json:"sort,optional"`

        // TenantId
        TenantId string `json:"tenantId,optional"`

        // Name
        Name string `json:"name,optional"`

        // Code
        Code string `json:"code,optional"`

        // Remark
        Remark string `json:"remark,optional"`
    }

        // The response data of position list | Position列表数据
    PositionListResp {
        BaseDataInfo

        // Position list data | Position列表数据
        Data PositionListInfo `json:"data"`
    }

        // Position list data | Position列表数据
    PositionListInfo {
        BaseListInfo

        // The API list data | Position列表数据
        Data []PositionInfo `json:"data"`
    }

        // Get position list request params | Position列表请求参数
    PositionListReq {
        PageInfo

        // Name
        Name string `json:"name,optional"`

        // Code
        Code string `json:"code,optional"`

        // Remark
        Remark string `json:"remark,optional"`

        // Status
        Status string `json:"status,optional" form:"status,optional"`

        CommonSearchInfo
    }

        // Position information response | Position信息返回体
    PositionInfoResp {
        BaseDataInfo

        // Position information | Position数据
        Data PositionInfo `json:"data"`
    }

    PositionListByIDsReq {
        // PositionIds | PositionIds
        PositionIds []string `json:"positionIds"`
    }

    PositionListByIDsResp {
        BaseDataInfo

        // Position list data | Position列表数据
        Data []PositionInfo `json:"data"`
    }
)

@server(
    // jwt: Auth
    group: position
    middleware: AuthMiddleware,Authority,TraceMiddleware
    prefix: /saas/api/v1
)

service phoenix {
    // Create position information | 创建Position
    @handler createPosition
    post /position/create (PositionCreateReq) returns (BaseDataInfo)

    // Update position information | 更新Position
    @handler updatePosition
    post /position/update (PositionInfo) returns (BaseDataInfo)

    // Delete position information | 删除Position信息
    @handler deletePosition
    post /position/delete (IDsReq) returns (BaseDataInfo)

    // Get position list | 获取Position列表
    @handler getPositionList
    get /position/list (PositionListReq) returns (PositionListResp)

    // Get position by ID | 通过ID获取Position
    @handler getPositionById
    get /position/:id (IDPathReq) returns (PositionInfoResp)

    // Get position list by IDs | 通过ID列表获取Position列表
    @handler getPositionListByIDs
    post /positions/by_ids (PositionListByIDsReq) returns (PositionListByIDsResp)
}
