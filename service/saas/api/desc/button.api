

type (
    // The response data of button information | Button信息
    ButtonInfo {
        BaseIDInfo

        // Sort
        Sort uint32 `json:"sort,optional"`

        // Name
        Name string `json:"name,optional"`

        // Code
        Code string `json:"code,optional"`

        // NodeType   | 节点类型（menu, button）
        NodeType string `json:"nodeType,optional" default:"button"`

        // MenuId
        MenuId string `json:"menuId,optional"`

    }
    // The response data of button information | Button信息
    ButtonCreateReq {
        // Sort
        Sort uint32 `json:"sort,optional"`

        // Name
        Name string `json:"name,optional"`

        // Code
        Code string `json:"code,optional"`

        // MenuId
        MenuId string `json:"menuId,optional"`

    }

        // The response data of button list | Button列表数据
    ButtonListResp {
        BaseDataInfo

        // Button list data | Button列表数据
        Data ButtonListInfo `json:"data"`
    }

        // Button list data | Button列表数据
    ButtonListInfo {
        BaseListInfo

        // The API list data | Button列表数据
        Data []ButtonInfo `json:"data"`
    }

        // Get button list request params | Button列表请求参数
    ButtonListReq {
        PageInfo

        // Name
        Name string `json:"name,optional" form:"name,optional"`

        // Code
        Code string `json:"code,optional" form:"code,optional"`

        // MenuId
        MenuId string `json:"menuId,optional"  form:"menuId,optional"`
    }

        // Button information response | Button信息返回体
    ButtonInfoResp {
        BaseDataInfo

        // Button information | Button数据
        Data ButtonInfo `json:"data"`
    }
)

@server(
    // jwt: Auth
    group: button
    middleware: AuthMiddleware,Authority,TraceMiddleware
    prefix: /saas/api/v1
)

service phoenix {
    // Create button information | 创建Button
    @handler createButton
    post /button/create (ButtonInfo) returns (ButtonCreateReq)

    // Update button information | 更新Button
    @handler updateButton
    post /button/update (ButtonInfo) returns (BaseDataInfo)

    // Delete button information | 删除Button信息
    @handler deleteButton
    post /button/delete (IDsReq) returns (BaseDataInfo)

    // Get button list | 获取Button列表
    @handler getButtonList
    get /button/list (ButtonListReq) returns (ButtonListResp)

    // Get button by ID | 通过ID获取Button
    @handler getButtonById
    get /button/:id (IDPathReq) returns (ButtonInfoResp)
}
