

type (
    // The response data of group type information | GroupType信息
    GroupTypeInfo {
        BaseIDInfo

        // Status
        Status  bool `json:"status,optional"`

        // Sort
        Sort  uint32 `json:"sort,optional"`

        // TenantId
        TenantId  string `json:"tenantId,optional"`

        // Name
        Name  string `json:"name,optional"`

        // Code
        Code  string `json:"code,optional"`

        // Remark
        Remark  string `json:"remark,optional"`
    }

    // The response data of group type list | GroupType列表数据
    GroupTypeListResp {
        BaseDataInfo

        // GroupType list data | GroupType列表数据
        Data GroupTypeListInfo `json:"data"`
    }

    // GroupType list data | GroupType列表数据
    GroupTypeListInfo {
        BaseListInfo

        // The API list data | GroupType列表数据
        Data  []GroupTypeInfo  `json:"data"`
    }

    // Get group type list request params | GroupType列表请求参数
    GroupTypeListReq {
        PageInfo

        // Name
        Name  string `json:"name,optional"`

        // Code
        Code  string `json:"code,optional"`

        // TenantId
        TenantId  string `json:"tenantId,optional"`

        // Remark
        Remark  string `json:"remark,optional"`

        CommonSearchInfo
    }

    // GroupTypeCreateReq | GroupTypeCreateReq
    GroupTypeCreateReq {
        // Status
        Status  bool `json:"status,optional"`

        // Sort
        Sort  uint32 `json:"sort,optional"`

        // TenantId
        TenantId  string `json:"tenantId,optional"`

        // Name
        Name  string `json:"name,optional"`

        // Code
        Code  string `json:"code,optional"`

        // Remark
        Remark  string `json:"remark,optional"`
    }

    // GroupType information response | GroupType信息返回体
    GroupTypeInfoResp {
        BaseDataInfo

        // GroupType information | GroupType数据
        Data GroupTypeInfo `json:"data"`
    }

    GroupTypeTreeReq {
        CommonSearchInfo
    }

    GroupTypeTreeResp {
        BaseDataInfo
        // The API list data | GroupType列表数据
        Data  []GroupTypeInfoLite  `json:"data"`
    }

    GroupTypeInfoLite {
        Id  string  `json:"id,optional"`
        Name  string `json:"name,optional"`
        Level uint32 `json:"level,optional"`
        Type  uint32 `json:"type,optional"`
        Children []GroupTypeInfoLite `json:"children,optional"`
    }

//    GroupInfoLite {
//        Id  string  `json:"id,optional"`
//        Sort uint32 `json:"sort,optional"`
//        Name string `json:"name,optional"`
//        Code string `json:"code,optional"`
//        Children []GroupUserInfoLite `json:"children,optional"`
//    }
//
//    GroupUserInfoLite {
//        Id  string    `json:"id,optional"`
//        Nickname string `json:"nickname,optional"`
//        Gender string `json:"gender,optional"`
//        Post string `json:"post,optional"`
//        //Avatar AvatarInfo `json:"avatar,optional"`
//    }

)

@server(
    // jwt: Auth
    group: grouptype
    middleware: AuthMiddleware,Authority,TraceMiddleware
    prefix: /saas/api/v1
)

service phoenix {
    // Create group type information | 创建GroupType
    @handler createGroupType
    post /group_type/create (GroupTypeCreateReq) returns (BaseDataInfo)

    // Update group type information | 更新GroupType
    @handler updateGroupType
    post /group_type/update (GroupTypeInfo) returns (BaseDataInfo)

    // Delete group type information | 删除GroupType信息
    @handler deleteGroupType
    post /group_type/delete (IDsReq) returns (BaseDataInfo)

    // Get group type list | 获取GroupType列表
    @handler getGroupTypeList
    get /group_type/list (GroupTypeListReq) returns (GroupTypeListResp)

    // Get group type list tree | 获取GroupType树形
    @handler getGroupTypeListTree
    get /group_type/list/tree (GroupTypeTreeReq) returns (GroupTypeTreeResp)

    // Get group type by ID | 通过ID获取GroupType
    @handler getGroupTypeById
    get /group_type/:id (IDPathReq) returns (GroupTypeInfoResp)
}
