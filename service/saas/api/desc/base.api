syntax = "v1"

// Empty data | 空数据信息
type EmptyData {
}

// The basic response with data | 基础带数据信息
type BaseDataInfo {
    // Error code | 错误代码
    Code int `json:"code"`

    // Message | 提示信息
    Msg string `json:"msg"`

    // Data | 数据
    Data interface{} `json:"data"`
}

// The basic response with data | 基础带数据信息
type BaseListInfo {
    // The total number of data | 数据总数
    Total uint64 `json:"total"`

    // Data | 数据
    Data string `json:"data"`
}

// The basic response without data | 基础不带数据信息
type BaseMsgResp {
    // Error code | 错误代码
    Code int `json:"code"`

    // Message | 提示信息
    Msg string `json:"msg"`
}

// The simplest message | 最简单的信息
// swagger:response SimpleMsg
type SimpleMsg {
    // Message | 信息
    Msg string `json:"msg"`
}

// The page request parameters | 列表请求参数
type PageInfo {
    // Page number | 第几页
    // Required: true
    // in: query
    Page uint64 `json:"page,optional" validate:"number" form:"page,optional"`

    // Page size | 单页数据行数
    // Required: true
    // Maximum: 100000
    // in: query
    PageSize uint64 `json:"pageSize,optional" validate:"number,max=100000" form:"pageSize,optional"`

    // 是否不分页，默认分页
    NoPage bool `json:"noPage,optional" form:"noPage,optional"`
}

// Basic ID request | 基础ID参数请求
type IDReq {
    // ID
    // Required: true
    Id string `json:"id" validate:"number"`
}

// Basic IDs request | 基础ID数组参数请求
type IDsReq {
    // IDs
    // Required: true
    Ids []string `json:"ids"`
}


// Basic ID request | 基础ID地址参数请求
type IDPathReq {
    // ID
    // Required: true
    Id string `path:"id"`
}

// Basic UUID request | 基础UUID参数请求
type UUIDReq {
    // ID
    // Required: true
    // Max length: 36
    Id string `json:"id" validate:"len=36"`
}

// Basic UUID array request | 基础UUID数组参数请求
type UUIDsReq {
    // Ids
    // Required: true
    Ids []string `json:"ids"`
}

// The base ID response data | 基础ID信息
type BaseIDInfo {
    // ID
    Id string `json:"id"`
}

// The base ID response data | 基础ID信息
type OptionalBaseIDInfo {
    // ID
    Id string `json:"id,optional"`
}

// The time mixin response data | 默认时间信息
type TimeMixinInfo {
    // Create date | 创建日期
    CreatedAt int64 `json:"createdAt,optional"`

    // Update date | 更新日期
    UpdatedAt int64 `json:"updatedAt,optional"`
}

// The person mixin response data | 默认人员信息
type PersonMixinInfo {
    // Create date | 创建人
    CreatedBy string `json:"createdBy,optional"`

    // Update date | 更新人
    UpdatedBy string `json:"updatedBy,optional"`
}


// The base UUID response data | 基础UUID信息
type BaseUUIDInfo {
    // ID
    Id string `json:"id"`

    // Create date | 创建日期
    CreatedAt int64 `json:"createdAt,optional"`

    // Update date | 更新日期
    UpdatedAt int64 `json:"updatedAt,optional"`
}

type CommonSearchInfo {
    //    Search | 搜索
    Search string `json:"search,optional" form:"search,optional"`
}

type UpdatePermissionsReq {
    // Menus | 菜单
    Menus []map[string]interface{} `json:"menus,optional"`
    // Apis | 接口
    Apis []map[string]interface{} `json:"apis,optional"`
}

@server(
    group: base
    prefix: /saas/api/v1
)

service phoenix {
    // Initialize database | 初始化数据库
    @handler initDatabase
    get /init/database returns (BaseDataInfo)
}


@server(
    group: base
    // jwt: Auth
    prefix: /saas/api/v1
    middleware: AuthMiddleware,Authority,TraceMiddleware
)

service phoenix {
    // Update Permissions | 更新权限资源
    @handler updatePermissions
    post /init/update_permissions (UpdatePermissionsReq) returns (BaseDataInfo)
}
