syntax = "v1"

info(
    title: "workflow"
    desc: "工作流管理"
    version: "v1.0"
)


type PresetTemplateSaveReq {
    // 模板预设ID
    ID string `json:"id,optional"`

    // 业务ID
    BusinessId string `json:"businessId"`

    // 模板名称
    Name string `json:"name"`

    // 模板类型 部门department ,集团group
    Kind string `json:"kind"`
}


type PresetTemplateSaveResp {

}


type PresetTemplateListReq {
    CommonSearchInfo
    PageInfo
}


type PresetTemplateListResp {
    BaseListInfo
    Data []PresetTemplateInfo `json:"data"`
}

type PresetTemplateInfo {
    TimeMixinInfo
    PersonMixinInfo
    // 模板ID
    ID string `json:"id"`

    // 模板名称
    Name string `json:"name"`

    // 模板类型 部门department ,集团group
    Kind string `json:"kind"`

    // 业务ID
    BusinessId string `json:"businessId"`

    // 组织ID
    OrganizationIds []string `json:"organizationIds,optional"`
}

type PresetTemplateDetailReq {
    // 模板ID
    ID string `form:"id"`
}

type PresetTemplateDetailResp {
    PresetTemplateInfo
}

type PresetTemplateBindReq {
    // 模板ID
    ID string `json:"id"`

    // 组织ID
    OrganizationIds []string `json:"organizationIds,optional"`
}

type PresetTemplateBindResp {

}


type TemplateVersionListReq {
    // 模板ID
    ID string `form:"id"`

    CommonSearchInfo
    PageInfo
}

type TemplateVersionListResp {
    BaseListInfo
    Data []TemplateVersionInfo `json:"data"`
}

type TemplateVersionInfo {
    // 版本ID
    ID string `json:"id"`

    // 版本号
    VersionNo int `json:"versionNo"`
   
    // 状态
    Status bool `json:"status"`

    // 版本名称
    TimeMixinInfo
    PersonMixinInfo
}



type TemplateListReq {
    CommonSearchInfo
    PageInfo
}

type TemplateListResp {
    BaseListInfo
    Data []TemplateListItemInfo `json:"data"`
}

type TemplateListItemInfo {
    // 模板ID
    ID string `json:"id"`

    // 业务ID
    BusinessId string `json:"businessId"`

    // 版本ID
    VersionID string `json:"versionId"`

    // 模板名称
    Name string `json:"name"`

    VersionNo int `json:"versionNo"`
    TimeMixinInfo
    PersonMixinInfo
}

type TemplateDetailReq {
    // 模板ID
    VersionID string `form:"versionId,optional"`
    // 业务ID
    BusinessId string `form:"businessId,optional"`
}

type TemplateDetailResp {
    // 模板ID
    MainID string `json:"mainId"`

    VersionId string `json:"versionId"`

    // 业务ID
    BusinessId string `json:"businessId"`

    // 模板名称
    Name string `json:"name"`

    // 模板类型 部门department ,自定义custom
    Kind string `json:"kind"`

    // 模板类型 部门department ,集团group
    PresetKind string `json:"presetKind"`
   
    // 自动审批类型 close-关闭,adjacent-相邻,any-任意
    AutoApproveKind string `json:"autoApproveKind"`

    // 超时预警
    TimeoutWarnStatus bool `json:"timeoutWarnStatus"`

    // 超时小时
    TimeoutHour int `json:"timeoutHour"`

    // 自定义
    Custom []CustomWorkflowNodeInfo `json:"custom"`

    // 部门
    Department *DepartmentWorkflowNodeInfo `json:"department"`
    
}

type CustomWorkflowNodeInfo {
    // 节点ID
    NodeID string `json:"nodeId,optional"`

    // 节点名称
    NodeName string `json:"nodeName"`

    // 节点类型 审批-approval,抄送-approval_cc
    NodeKind string `json:"nodeKind"`

    // 签名类型
    SigningKind string `json:"signingKind"`

    // 审批类型 岗位-position,角色-role,指定-designate,自定义-custom,group_designate-集团指定
    ApprovalKind string `json:"approvalKind"`

    // 审批人ID
    ApproverIds []string `json:"approverIds,optional"`
   
    // 抄送类型 岗位-position,角色-role,指定-designate,自定义-custom,group_designate-集团指定
    CCKind string `json:"ccKind,optional"`

    // 抄送人ID
    CCIds []string `json:"ccIds,optional"`
}

type DepartmentWorkflowNodeInfo {
    // 审批部门层级 
    Level int `json:"level,optional"`

    // 环节类型 审批-approval,抄送-approval_cc
    NodeKind string `json:"nodeKind"`

    // 签名类型 或-or,与-and
    SigningKind string `json:"signingKind"`

    // 抄送类型 岗位-position,角色-role,指定-designate,自定义-custom,group_designate-集团指定
    CCKind string `json:"ccKind,optional"`

    // 抄送人ID
    CCIds []string `json:"ccIds,optional"`
}

type UpdateTemplateReq {
    // 模板ID
    ID string `json:"id"`

    // 模板名称
    Name string `json:"name"`

    // 模板类型 部门depart 自定义custom
    Kind string `json:"kind"`

    // 自动审批类型 close-关闭,adjacent-相邻,any-任意
    AutoApproveKind string `json:"autoApproveKind"`

    // 超时预警
    TimeoutWarnStatus bool `json:"timeoutWarnStatus"`

    // 超时小时
    TimeoutHour int `json:"timeoutHour,optional"`

    // 自定义
    Custom []CustomWorkflowNodeInfo `json:"custom,optional"`

    // 部门
    Department DepartmentWorkflowNodeInfo `json:"department,optional"`
}

type UpdateTemplateResp {
    ID string `json:"id"`

    // 业务ID
    BusinessId string `json:"businessId"`

    // 模板名称
    Name string `json:"name"`

    // 模板类型 部门department ,自定义custom
    Kind string `json:"kind"`

    // 预设模板类型 部门department ,集团group
    PresetKind string `json:"presetKind"`

    // 自定义
    Custom []CustomWorkflowNodeInfo `json:"custom"`

    // 部门
    Department DepartmentWorkflowNodeInfo `json:"department"`
}

type WorkflowTaskTodosReq {
    CommonSearchInfo
    PageInfo
    // 创建人ID
    FlowCreatedUserId string `form:"flowCreatedUserId,optional"`

    // 组织ID
    OrganizationId string `form:"organizationId,optional"`

    // 任务创建时间开始
    TaskCreatedTimeBegin int64 `form:"taskCreatedTimeBegin,optional"`

    // 任务创建时间结束
    TaskCreatedTimeEnd int64 `form:"taskCreatedTimeEnd,optional"`

    // 流程创建时间开始
    FlowCreatedTimeBegin int64 `form:"flowCreatedTimeBegin,optional"`

    // 流程创建时间结束
    FlowCreatedTimeEnd int64 `form:"flowCreatedTimeEnd,optional"`
}

type WorkflowTaskTodosResp {
    BaseListInfo
    Data []WorkflowTaskTodoInfo `json:"data"`
}

type WorkflowTaskTodoInfo {
    // 任务ID
    ID string `json:"id"`

    // 流程名称
    FlowName string `json:"flowName"`

    // 当前节点名称
    CurrentNodeName string `json:"currentNodeName"`
 
    // 组织名称
    OrganizationName string `json:"organizationName"`

    // 创建人昵称
    FlowCreatedUserNickname string `json:"flowCreatedUserNickname"`
 
    // 流程创建时间
    FlowCreatedTime int64 `json:"flowCreatedTime"`

    // 任务创建时间
    TaskCreatedTime int64 `json:"taskCreatedTime"`

    // 流程ID
    FlowId string `json:"flowId"`

    // 任务ID
    TaskId string `json:"taskId"`

    // 节点ID
    NodeId string `json:"nodeId"`
}




type WorkflowTaskDoneReq {
    CommonSearchInfo
    PageInfo
    // 创建人ID
    FlowCreatedUserId string `form:"flowCreatedUserId,optional"`

    // 组织ID
    OrganizationId string `form:"organizationId,optional"`

    // 任务完成时间开始
    TaskCompletedTimeBegin int64 `form:"taskCompletedTimeBegin,optional"`

    // 任务完成时间结束
    TaskCompletedTimeEnd int64 `form:"taskCompletedTimeEnd,optional"`

    // 流程创建时间开始
    FlowCreatedTimeBegin int64 `form:"flowCreatedTimeBegin,optional"`

    // 流程创建时间结束
    FlowCreatedTimeEnd int64 `form:"flowCreatedTimeEnd,optional"`

    // 流程状态
    // 进行中：underReview
    // 已通过：passed
    // 已驳回：rejected
    // 已撤销：cancelled
    FlowStatus string `form:"flowStatus,optional"`
}

type WorkflowTaskDoneResp {
    BaseListInfo
    Data []WorkflowTaskDoneInfo `json:"data"`
}

type WorkflowTaskDoneInfo {
    // 流程名称
    FlowName string `json:"flowName"`

    // 任务完成时间
    TaskCompletedTime int64 `json:"taskCompletedTime"`

    // 流程创建人昵称
    FlowCreatedUserNickname string `json:"flowCreatedUserNickname"`

    // 流程创建时间
    FlowCreatedTime int64 `json:"flowCreatedTime"`

    // 流程ID
    FlowId string `json:"flowId"`

    // 流程状态 
    // 进行中：underReview
    // 已通过：passed
    // 已驳回：rejected
    // 已撤销：cancelled
    FlowStatus string `json:"flowStatus"`
}

type WorkflowInitiatesReq {
    CommonSearchInfo
    PageInfo
    // 创建人ID
    FlowCreatedUserId string `form:"flowCreatedUserId,optional"`

    // 组织ID
    OrganizationId string `form:"organizationId,optional"`

    // 流程创建时间开始
    FlowCreatedTimeBegin int64 `form:"flowCreatedTimeBegin,optional"`

    // 流程创建时间结束
    FlowCreatedTimeEnd int64 `form:"flowCreatedTimeEnd,optional"`

    // 流程状态
    FlowStatus string `form:"flowStatus,optional"`

    // 流程节点审批人ID
    FlowNodeApproverId string `form:"flowNodeApproverId,optional"`
}

type WorkflowInitiatesResp {
    BaseListInfo
    Data []WorkflowInitiatesInfo `json:"data"`
}

type WorkflowInitiatesInfo {
    // 流程名称
    FlowName string `json:"flowName"`

    // 当前节点名称
    CurrentNodeName string `json:"currentNodeName"`

    // 当前节点审批人昵称
    CurrentNodeApproverNicknames string `json:"currentNodeApproverNicknames"`

    // 流程创建时间
    FlowCreatedTime int64 `json:"flowCreatedTime"`

    // 流程状态
    // 进行中：underReview
    // 已通过：passed
    // 已驳回：rejected
    // 已撤销：cancelled
    FlowStatus string `json:"flowStatus"`

    // 流程ID
    FlowId string `json:"flowId"`
}


type WorkflowTaskCCsReq {
    CommonSearchInfo
    PageInfo
    // 创建人ID
    FlowCreatedUserId string `form:"flowCreatedUserId,optional"`

    // 组织ID
    OrganizationId string `form:"organizationId,optional"`

    // 抄送时间开始
    CCTimeBegin int64 `form:"ccTimeBegin,optional"`

    // 抄送时间结束
    CCTimeEnd int64 `form:"ccTimeEnd,optional"`

    // 流程创建时间开始
    FlowCreatedTimeBegin int64 `form:"flowCreatedTimeBegin,optional"`

    // 流程创建时间结束
    FlowCreatedTimeEnd int64 `form:"flowCreatedTimeEnd,optional"`

    // 流程状态
    FlowStatus string `form:"flowStatus,optional"`

    // 查阅状态
    ConsultStatus *bool `form:"consultStatus,optional"`
    
}

type WorkflowTaskCCsResp {
    BaseListInfo
    Data []WorkflowTaskCCsInfo `json:"data"`
}

type WorkflowTaskCCsInfo {
    // 抄送人ID
    CCId string `json:"ccId"`

    // 流程名称
    FlowName string `json:"flowName"`

    // 抄送时间
    CCTime int64 `json:"ccTime"`

    // 流程创建人昵称
    FlowCreatedUserNickname string `json:"flowCreatedUserNickname"`

    // 流程创建时间
    FlowCreatedTime int64 `json:"flowCreatedTime"`

    // 流程状态
    // 进行中：underReview
    // 已通过：passed
    // 已驳回：rejected
    // 已撤销：cancelled
    FlowStatus string `json:"flowStatus"`

    // 查阅状态
    ConsultStatus bool `json:"consultStatus"`

    // 流程ID
    FlowId string `json:"flowId"`
}



type WorkflowDetailReq {
    // 流程ID
    FlowId string `form:"flowId"`
}

type WorkflowDetailResp {
    // 流程名称
    FlowName string `json:"flowName"`

    // 流程ID
    FlowId string `json:"flowId"`

    // 流程状态
    // 节点状态 
    // 进行中：underReview
    // 已通过：passed
    // 已驳回：rejected
    // 已撤销：cancelled
    FlowStatus string `json:"flowStatus"`

    // 流程创建人昵称
    FlowCreatedUserNickname string `json:"flowCreatedUserNickname"`

    // 流程创建时间
    FlowCreatedTime int64 `json:"flowCreatedTime"`

    // 表单内容
    FormContent string `json:"formContent"`

    // 节点列表
    Nodes []WorkflowNodeInfo `json:"nodes"`
}

type WorkflowNodeInfo {

    // 节点名称
    NodeName string `json:"nodeName"`

    // 节点ID
    NodeId string `json:"nodeId"`
   
    // 节点状态 
    // 进行中：underReview
    // 已通过：passed
    // 已驳回：rejected
    // 未开始：notStarted

    Status string `json:"status"` 

    // 签署类型  或-or,与-and
    SigningKind string `json:"signingKind"`

    // 节点更新时间
    UpdatedAt int64 `json:"updatedAt"`

    // 节点审批人列表
    Approvers []WorkflowNodeReviewerInfo `json:"approvers"`
}

type WorkflowNodeReviewerInfo {
    // 任务ID
    TaskId string `json:"taskId"`

    // 审批人ID
    ApproverId string `json:"approverId"`

    // 审批人昵称
    ApproverNickname string `json:"approverNickname"`

    // 审批状态
    // 进行中：underReview
    // 已通过：passed
    // 已驳回：rejected
    Status string `json:"status"` 

    // 审批时间
    UpdatedAt int64 `json:"updatedAt"`

    // 审批意见
    Comment string `json:"comment"`

    // 扩展信息
    Extra map[string]interface{} `json:"extra"`
}

type WorkflowStartReq {

    // 发起部门ID
    DepartmentId string `json:"departmentId,optional"`

    // 模板版本ID
    FlowVersionId string `json:"flowVersionId"`

    // 业务ID
    BusinessId string `json:"businessId"`

    // 表单内容
    FormContent string `json:"formContent"`

    // 抄送人ID
    CCIds []string `json:"ccIds,optional"`

    // 节点列表
    Nodes []WorkflowNodeStartInfo `json:"nodes,optional"`
}

type WorkflowNodeStartInfo {

    // 节点ID
    NodeId string `json:"nodeId"`

    // 抄送人ID
    CCIds []string `json:"ccIds,optional"`

    // 审批人ID
    ApproverIds []string `json:"approverIds,optional"`
}

type WorkflowStartResp {
    // 流程ID
    FlowId string `json:"flowId"`
}

type WorkflowApproveReq {
    // 任务ID
    TaskId string `json:"taskId"`

    // 审批意见
    Comment string `json:"comment"`

    // 审批状态 通过-pass,驳回-reject
    Status string `json:"status"`

    // 扩展信息
    Extra map[string]interface{} `json:"extra"`
}

type WorkflowApproveResp {

}

// 版本启用
type TemplateVersionEnableReq {
    // 版本ID
    VersionID string `json:"versionId"`
}

type TemplateVersionEnableResp {
}

type WorkflowCCReadReq {
    // 任务ID
    CCIds []string `json:"ccIds"`
}

type WorkflowCCReadResp {
}

type WorkflowMonitorListReq {
    CommonSearchInfo
    PageInfo
    // 创建人ID
    FlowCreatedUserId string `form:"flowCreatedUserId,optional"`

    // 流程创建时间开始
    FlowCreatedTimeBegin int64 `form:"flowCreatedTimeBegin,optional"`

    // 流程创建时间结束
    FlowCreatedTimeEnd int64 `form:"flowCreatedTimeEnd,optional"`

    // 流程状态
    FlowStatus string `form:"flowStatus,optional"`

    // 流程节点审批人ID
    FlowNodeApproverId string `form:"flowNodeApproverId,optional"`

}   

type WorkflowMonitorListResp {
    BaseListInfo
    Data []WorkflowMonitorListInfo `json:"data"`
}

type WorkflowMonitorListInfo {
    // 流程名称
    FlowName string `json:"flowName"`

    // 流程创建人昵称
    FlowCreatedUserNickname string `json:"flowCreatedUserNickname"`

    // 流程创建时间
    FlowCreatedTime int64 `json:"flowCreatedTime"`

    // 流程状态
    FlowStatus string `json:"flowStatus"`

    // 当前节点名称
    CurrentNodeName string `json:"currentNodeName"`

    // 当前节点ID
    CurrentNodeId string `json:"currentNodeId"`

    // 当前节点审批人
    CurrentNodeApprover []WorkflowNodeReviewerInfo `json:"currentNodeApprover"`

    // 预警状态 timeout-超时预警，notApprover-无审批人预警，为空则正常
    WarnStatus string `json:"warnStatus"`
   
    // 流程ID
    WorkflowId string `json:"workflowId"`
}

type WorkflowMonitorNodeApproverAddReq {
    // 节点ID
    NodeId string `json:"nodeId"`

    // 审批人ID
    ApproverId string `json:"approverId"`
}

type WorkflowMonitorNodeApproverAddResp {

}

type WorkflowMonitorNodeApproverUpdateReq {
    // 节点ID
    TaskId string `json:"taskId"`
    
    // 审批人ID
    ApproverId string `json:"approverId"`
}

type WorkflowMonitorNodeApproverUpdateResp {

}

type WorkflowCancelReq {
    // 流程ID
    FlowId string `json:"flowId"`
}

type WorkflowCancelResp {

}


 @server(
    // jwt: Auth
    group: workflow
    middleware: TraceMiddleware,AuthMiddleware,Authority
    prefix: /saas/api/v1
)

service phoenix {
    // 保存模板
    @handler savePresetTemplate
    post /workflow/template-preset/save(PresetTemplateSaveReq) returns (PresetTemplateSaveResp)

    // 列表
    @handler getPresetTemplateList
    get /workflow/template-presets(PresetTemplateListReq) returns (PresetTemplateListResp)

    // 详情
    @handler getPresetTemplateDetail
    get /workflow/template-preset(PresetTemplateDetailReq) returns (PresetTemplateDetailResp)

    // 绑定
    @handler bindPresetTemplate
    post /workflow/template-preset/bind(PresetTemplateBindReq) returns (PresetTemplateBindResp)

}



@server(
    // jwt: Auth
    group: workflow
    middleware: TraceMiddleware,AuthMiddleware,Authority
    prefix: /saas/api/v1
)

service phoenix {
    // 更新模板
    @handler updateTemplate
    post /workflow/template/update(UpdateTemplateReq) returns (UpdateTemplateResp)

    // 列表
    @handler getTemplateList
    get /workflow/templates(TemplateListReq) returns (TemplateListResp)

    // 版本列表
    @handler getTemplateVersionList
    get /workflow/template/versions(TemplateVersionListReq) returns (TemplateVersionListResp)

    // 详情
    @handler getTemplateDetail
    get /workflow/template(TemplateDetailReq) returns (TemplateDetailResp)

    // 版本启用
    @handler enableTemplateVersion
    post /workflow/template/version/enable(TemplateVersionEnableReq) returns (TemplateVersionEnableResp)

}

@server(
    // jwt: Auth
    group: workflow
    middleware: TraceMiddleware,AuthMiddleware,Authority
    prefix: /saas/api/v1
)

service phoenix {
    // 待办列表
    @handler getWorkflowTaskTodos
    get /workflow/task/todos(WorkflowTaskTodosReq) returns (WorkflowTaskTodosResp)

    // 已完成列表
    @handler getWorkflowTaskDone
    get /workflow/task/done(WorkflowTaskDoneReq) returns (WorkflowTaskDoneResp)

    // 发起列表
    @handler getWorkflowInitiates
    get /workflow/initiates(WorkflowInitiatesReq) returns (WorkflowInitiatesResp)

    // 抄送列表
    @handler getWorkflowTaskCCs
    get /workflow/task/ccs(WorkflowTaskCCsReq) returns (WorkflowTaskCCsResp)

    // 流程详情
    @handler getWorkflowDetail
    get /workflow(WorkflowDetailReq) returns (WorkflowDetailResp)

    // 发起
    @handler startWorkflow
    post /workflow/start(WorkflowStartReq) returns (WorkflowStartResp)

    // 审批
    @handler approveWorkflow
    post /workflow/approve(WorkflowApproveReq) returns (WorkflowApproveResp)

    // 抄送已阅
    @handler readWorkflowCC
    post /workflow/cc/read(WorkflowCCReadReq) returns (WorkflowCCReadResp)

    // 撤销
    @handler cancelWorkflow
    post /workflow/cancel(WorkflowCancelReq) returns (WorkflowCancelResp)
} 

@server(
    // jwt: Auth
    group: workflow
    middleware: TraceMiddleware,AuthMiddleware,Authority
    prefix: /saas/api/v1
)

service phoenix {
    // 监控列表
    @handler getWorkflowMonitorList
    get /workflow/monitor/list(WorkflowMonitorListReq) returns (WorkflowMonitorListResp)

    // 添加节点审批人
    @handler addWorkflowMonitorNodeApprover
    post /workflow/monitor/node/approver/add(WorkflowMonitorNodeApproverAddReq) returns (WorkflowMonitorNodeApproverAddResp)

    // 更新节点审批人
    @handler updateWorkflowMonitorNodeApprover
    post /workflow/monitor/node/approver/update(WorkflowMonitorNodeApproverUpdateReq) returns (WorkflowMonitorNodeApproverUpdateResp)

}