type (
    // The response data of api information | API信息
    APIInfo {
        BaseIDInfo

        // Status
        Status bool `json:"status,optional"`

        // Path
        Path string `json:"path,optional"`

        // Description
        Description string `json:"description,optional"`

        // ApiGroup
        ApiGroup string `json:"apiGroup,optional"`

        // Method
        Method string `json:"method,optional"`
        // 类型
        Kind string `json:"kind,optional"`
        // 模块
        Module string `json:"module,optional"`
    }

        // The response data of api information | API信息
    APICreateInfo {

        // Status
        Status bool `json:"status,optional"`

        // Path
        Path string `json:"path,optional"`

        // Description
        Description string `json:"description,optional"`

        // ApiGroup
        ApiGroup string `json:"apiGroup,optional"`

        // Method
        Method string `json:"method,optional"`

        // 类型
        Kind string `json:"kind,optional"`
        // 模块
        Module string `json:"module,optional"`
    }

        // The response data of api list | API列表数据
    APIListResp {
        BaseDataInfo

        // API list data | API列表数据
        Data APIListInfo `json:"data"`
    }

        // API list data | API列表数据
    APIListInfo {
        BaseListInfo

        // The API list data | API列表数据
        Data []APIInfo `json:"data"`
    }

        // Get api list request params | API列表请求参数
    APIListReq {
        PageInfo

        // Path
        Path string `json:"path,optional"`

        // Description
        Description string `json:"description,optional"`

        // ApiGroup
        ApiGroup string `json:"apiGroup,optional"`
        // 类型
        Kind string `json:"kind,optional"`
        // 模块
        Module string `json:"module,optional"`
        CommonSearchInfo
    }

        // API information response | API信息返回体
    APIInfoResp {
        BaseDataInfo

        // API information | API数据
        Data APIInfo `json:"data"`
    }
)

@server(
    //    // jwt: Auth
    group: api
    middleware: AuthMiddleware,Authority,TraceMiddleware
    prefix: /saas/api/v1
)

service phoenix {
    // Create api information | 创建API
    @handler createAPI
    post /api/create (APICreateInfo) returns (BaseDataInfo)

    // Update api information | 更新API
    @handler updateAPI
    post /api/update (APIInfo) returns (BaseDataInfo)

    // Delete api information | 删除API信息
    @handler deleteAPI
    post /api/delete (IDsReq) returns (BaseDataInfo)

    // Get api list | 获取API列表
    @handler getAPIList
    get /api/list (APIListReq) returns (APIListResp)

    // Get api by ID | 通过ID获取API
    @handler getAPIById
    get /api/:id (IDPathReq) returns (APIInfoResp)
}
