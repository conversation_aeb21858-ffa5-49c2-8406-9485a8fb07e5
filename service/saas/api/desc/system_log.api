syntax = "v1"

info(
    title: "system log"
    desc: "system log including system log management"
    version: "v1.0"
)

type (
    GetOperationLogsReq {
        PageInfo
        // api操作类型，如新增，修改等（/galaxy/api/v1/dictionary/list/by-type接口获取）
        APIKind string `form:"apiKind,optional"`
        // api操作业务模块，如会议等（/galaxy/api/v1/dictionary/list/by-type接口获取）
        APIModule string `form:"apiModule,optional"`
        // 请求是否成功
        IsOK *bool `form:"isOK,optional"`
        // 请求方式，如POST，GET，DELETE，PUT（目前系统只有POST和GET）
        Method string `form:"method,optional"`
        // 搜索，目前支持用户昵称
        CommonSearchInfo
        BeginTime *int64 `form:"beginTime,optional"`
        // 结束时间，毫秒级时间戳
        EndTime *int64 `form:"endTime,optional"`
        UserID string `form:"userId,optional"`
    }


    GetOperationLogsResp {
        BaseDataInfo
        Data GetOperationLogsInfo `json:"data"`
    }
    GetOperationLogsInfo {
        PageInfo
        BaseListInfo
        Data []GetOperationLogItemResp `json:"data"`
    }

    GetOperationLogItemResp {
        // 接口
        API string `json:"api,optional"`
        // api描述
        APIDescription string `json:"apiDescription,optional"`
        // api类型
        APIKind string `json:"apiKind,optional"`
        // api操作业务模块
        APIModule string `json:"apiModule,optional"`
        // 创建时间，毫秒级时间戳
        CreatedAt int64 `json:"createdAt,optional"`
        // 请求方IP地址
        IP string `json:"ip,optional"`
        // 请求是否成功
        IsOK bool `json:"isOK,optional"`
        // 请求方法
        Method string `json:"method,optional"`
        // 请求参数，json字符串
        RequestBody string `json:"requestBody,optional"`
        // 响应参数，json字符串
        ResponseBody string `json:"responseBody,optional"`
        // 操作用户租户名称
        TenantName string `json:"tenantName,optional"`
        // 操作用户
        UserName string `json:"userName,optional"`
    }

    GetLoginLogsReq {
        PageInfo
        CommonSearchInfo
        DeviceKind string `form:"deviceKind,optional"`
        BeginTime *int64 `form:"beginTime,optional"`
        // 结束时间，毫秒级时间戳
        EndTime *int64 `form:"endTime,optional"`
        UserID string `form:"userId,optional"`
        // 归属地模糊搜索
        Location string `form:"location,optional"`
    }


    GetLoginLogsResp {
        BaseDataInfo
        Data GetLoginLogsInfo `json:"data"`
    }

    GetLoginLogsInfo {
        PageInfo
        BaseListInfo
        Data []GetLoginLogItemResp `json:"data"`
    }

    GetLoginLogItemResp {
        // 创建时间，毫秒级时间戳
        CreatedAt int64 `json:"createdAt,optional"`
        // 设备类型，1:App；2:Web
        DeviceKind string `json:"deviceKind,optional"`
        // 请求方IP地址
        IP string `json:"ip,optional"`
        // ip归属市
        IPCity string `json:"ipCity,optional"`
        // ip归属区
        IPDistrict string `json:"ipDistrict,optional"`
        // ip归属省份
        IPProvince string `json:"ipProvince,optional"`
        LoginUserName string `json:"loginUserName"`
        // 操作用户租户名称
        TenantName *string `json:"tenantName,optional"`
        // 操作昵称
        UserName *string `json:"userName,optional"`
    }
)


type (
    SystemOperationLogCreateReqInfo {
        // 用户id
        UserID string  `json:"userId"`
        // 租户id
        TenantID string    `json:"tenantId"`
        // 方法
        Method  string  `json:"method"`
        // API
        API  string  `json:"api"`
        // 请求体
        RequestBody  string  `json:"requestBody,optional"`
        // 响应体
        ResponseBody  string  `json:"responseBody,optional"`
        // IP
        IP  string  `json:"ip"`
        // 状态码
        HttpStatus  int32  `json:"httpStatus"`
        // 创建时间
        CreatedAt  int64  `json:"createdAt"`
    }

    SystemOperationLogCreateReq {
        // 操作日志
        Logs []SystemOperationLogCreateReqInfo `json:"logs"`
    }

    SystemOperationLogCreateResp {

    }

    SystemLoginLogCreateReqInfo {
        // 用户id
        UserID string  `json:"userId"`
        // 租户id
        TenantID string    `json:"tenantId"`
        // 登录用户名
        LoginUsername string  `json:"loginUsername"`
        // IP
        IP string  `json:"ip"`
        // 设备类型  web，app，h5
        DeviceKind string  `json:"deviceKind"`

        // 创建时间
        CreatedAt int64 `json:"createdAt"`
    }

    SystemLoginLogCreateReq {
        // 登录日志
        Logs []SystemLoginLogCreateReqInfo `json:"logs"`
    }

    SystemLoginLogCreateResp {

    }
)

@server(
    group: systemlog
    middleware: AuthMiddleware,Authority,TraceMiddleware
    prefix: /saas/api/v1
)

service phoenix {
    // Save system operation log | 保存系统操作日志
    @handler saveSystemOperationLog
    post /system/operation/logs (SystemOperationLogCreateReq) returns (SystemOperationLogCreateResp);

    // Save system login log | 保存系统登录日志
    @handler saveSystemLoginLog
    post /system/login/logs (SystemLoginLogCreateReq) returns (SystemLoginLogCreateResp);

    // Obtain system operation logs | 获取系统操作日志
    @handler GetOperationLogs
    get /system-log/operation/list (GetOperationLogsReq) returns (GetOperationLogsResp)

    // Get the system login log | 获取系统登录日志
    @handler GetLoginLogs
    get /system-log/login/list (GetLoginLogsReq) returns (GetLoginLogsResp)
}

