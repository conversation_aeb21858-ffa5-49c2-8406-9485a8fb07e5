

type (
    // The response data of tenant user info information | TenantUserInfo信息
    TenantUserInfoInfo {
        BaseIDInfo

        // Sort
        Sort uint32 `json:"sort,optional"`

        // TenantId
        TenantId string `json:"tenantId,optional"`

        // UserId
        UserId string `json:"userId,optional"`

        // Extra
        Extra string `json:"extra,optional"`
    }



    AddTenantUserReq {
        // Status | 状态
        Status bool `json:"status,optional"`

        // Username | 用户名（必填）
        Username string `json:"username"`

        // Password | 密码（必填）
        Password string `json:"password,omitempty,optional"`

        // Nickname | 昵称
        Nickname string `json:"nickname,optional"`

        // Mobile | 手机号
        Mobile string `json:"mobile,optional"`

        // Email | 邮箱
        Email string `json:"email,optional"`

        // Gender
        Gender string `json:"gender,optional"`

        // Post | 职务
        Post string `json:"post,optional"`

        // Avatar | 头像
        AvatarID string `json:"avatarId,optional"`

        // tenantId | 组织架构ID
        TenantId string `json:"tenantId"`

        // Sort
        Sort uint32 `json:"sort,optional"`

        // Extra
        Extra string `json:"extra,optional"`

        Kind string `json:"kind,optional"`

        Imei string `json:"imei,optional"`

        DeviceNo string `json:"deviceNo,optional"`
    }

        // update Tenant user request | updateTenantUser请求体
    UpdateTenantUserReq {
        BaseIDInfo
        AddTenantUserReq
    }

        // The response data of tenant user info list | TenantUserInfo列表数据
    TenantUserInfoListResp {
        BaseDataInfo

        // TenantUserInfo list data | TenantUserInfo列表数据
        Data TenantUserInfoListInfo `json:"data"`
    }

        // TenantUserInfo list data | TenantUserInfo列表数据
    TenantUserInfoListInfo {
        BaseListInfo

        // The API list data | TenantUserInfo列表数据
        Data []TenantUser `json:"data"`
    }

        // Get tenant user info list request params | TenantUserInfo列表请求参数
    TenantUserInfoListReq {
        PageInfo
        TenantId string `json:"tenantId,optional" form:"tenantId"`
        Kind string `json:"kind,optional" form:"kind"`
        CommonSearchInfo
    }

        // TenantUserInfo information response | TenantUserInfo信息返回体
    TenantUserInfoInfoResp {
        BaseDataInfo

        // TenantUserInfo information | TenantUserInfo数据
        Data TenantUser `json:"data"`
    }

        // Tenant users | TenantUsers
    TenantUser {
        UserInfo
        // Sort
        Sort uint32 `json:"sort,optional"`
        //    Extra | 额外信息
        Extra string `json:"Extra,optional"`
    }

    GetTenantUserInfoByIdReq {
        IDPathReq
        TenantId string `json:"tenantId,optional" form:"tenantId"`
    }

    DeleteTenantUserInfoByIdsReq {
        IDsReq
        TenantId string `json:"tenantId"`
    }
)

@server(
    // jwt: Auth
    group: tenantuserinfo
    middleware: AuthMiddleware,Authority,TraceMiddleware
    prefix: /saas/api/v1
)

service phoenix {
    // Create tenant user info information | 创建TenantUserInfo
    @handler createTenantUserInfo
    post /tenant_user/create (AddTenantUserReq) returns (BaseDataInfo)

    // Update tenant user info information | 更新TenantUserInfo
    @handler updateTenantUserInfo
    post /tenant_user/update (UpdateTenantUserReq) returns (BaseDataInfo)

    // Delete tenant user info information | 删除TenantUserInfo信息
    @handler deleteTenantUserInfo
    post /tenant_user/delete (DeleteTenantUserInfoByIdsReq) returns (BaseDataInfo)

    // Get tenant user info list | 获取TenantUserInfo列表
    @handler getTenantUserInfoList
    get /tenant_user/list (TenantUserInfoListReq) returns (TenantUserInfoListResp)

    // Get tenant user info by ID | 通过ID获取TenantUserInfo
    @handler getTenantUserInfoById
    get /tenant_user/:id (GetTenantUserInfoByIdReq) returns (TenantUserInfoInfoResp)
}
