syntax = "v1"

info(
    title: "dictionary management"
    desc: "dictionary management"
    version: "v1.0"
)

type (

    // 字典信息
    DictionaryInfo {
        // id
        ID          string    `json:"id,optional"`
        // 类型
        DictType    string    `json:"dictType"`
        // 字典代码
        DictCode    string    `json:"dictCode"`
        // 排序
        DictOrder   int64     `json:"dictOrder,optional"`
        // 名称
        DictName    string    `json:"dictName,optional"`
        // 描述
        Description string    `json:"description,optional"`
        // 扩展字段
        Extra  map[string]interface{}    `json:"extra,optional"`
        // 是否启用
        IsOpen      bool      `json:"isOpen,default=true"`
        // 创建时间
        CreatedAt   int64 `json:"createdAt,optional"`
        // 更新时间
        UpdatedAt   int64 `json:"updatedAt,optional"`
    }

    // 新增字典信息数据
    CreateDictResp {
        BaseDataInfo
        Data CreateDictInfo `json:"data"`
    }
    CreateDictInfo {
        // 字典id
        ID string `json:"id"`
    }

    // 根据id删除字典信息
    DeleteDictByIdReq {
        ID string  `json:"id"`
    }

    // Get dict list params | 获取字典列表参数
    DictListReq {
        PageInfo
        // 字典类型
        DictType string `form:"dictType"`
    }

        // The response data of dict information list | 字典信息列表数据
    DictListResp {
        BaseDataInfo
        // The file list data | 文件信息列表数据
        Data DictList `json:"data"`
    }

    DictList {
        BaseListInfo
        // The file list data | 文件信息列表数据
        Data []DictionaryInfo `json:"data"`
    }

    DictPageReq {
        PageInfo
       CommonSearchInfo
    }

    DictPageResp {
        BaseDataInfo
        Data DictPageData `json:"data"`
    }

    DictPageData {
        BaseListInfo
        Data []DictionaryInfo `json:"data"`
    }
)

@server (
    //    jwt : Auth
    group:      dictionary
    middleware: TraceMiddleware,AuthMiddleware,Authority
    prefix:     /saas/api/v1
)
service phoenix {

    // 根据id更改字典信息
    @handler updateDictById
    post /dict/update (DictionaryInfo) returns (BaseDataInfo)

    // 根据id删除字典信息
    @handler deleteDictById
    post /dict/delete (DeleteDictByIdReq) returns (BaseDataInfo)

    // 新增字典信息
    @handler createDictByInfo
    post /dict/create (DictionaryInfo) returns (CreateDictResp)

    // Get file dict | 根据dictType获取字典列表
    @handler dictListByDictType
    get /dict/type/list (DictListReq) returns (DictListResp)  

    // Get dict page | 获取字典分页列表
    @handler dictPage
    get /dicts (DictPageReq) returns (DictPageResp)

}
