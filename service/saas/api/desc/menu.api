type (
    // The response data of menu information | Menu信息
    MenuInfo {
        BaseIDInfo

        // Sort | 排序
        Sort uint32 `json:"sort,optional"`

        // Name | 名称
        Name string `json:"name,optional"`

        // Title | 标题
        Title string `json:"title,optional"`

        // Icon | 图标
        Icon string `json:"icon,optional"`

        // ParentId | 父级ID
        ParentId string `json:"parentId,optional"`

        // MenuType | 菜单类型
        MenuType uint32 `json:"menuType,optional"`

        // Url | 路径
        Url string `json:"url,optional"`

        // Redirect | 重定向
        Redirect string `json:"redirect,optional"`

        // Component    | 组件
        Component string `json:"component,optional"`

        // IsActive | 是否激活
        IsActive bool `json:"isActive,optional"`

        // Hidden | 是否隐藏
        Hidden bool `json:"hidden,optional"`

        // HiddenInTab | 是否隐藏在Tab
        HiddenInTab bool `json:"hiddenInTab,optional"`

        // Fixed | 是否固定
        Fixed bool `json:"fixed,optional"`

        // Remark   | 备注
        Remark string `json:"remark,optional"`

        // NodeType   | 节点类型（menu, button）
        NodeType string `json:"nodeType,optional" default:"menu"`

        // Meta | 元数据
        Meta string `json:"meta,optional"`

        // Children | 子菜单
        Children []MenuInfo `json:"children,optional"`

        // Permissions | 权限列表（包含按钮）
        Permissions []ButtonInfo `json:"permissions,optional"`

        // IsFullPage | 是否全屏
        IsFullPage bool `json:"isFullPage,optional"`
    }

        // MenuCreateInfo The response data of menu information | Menu创建信息
    MenuCreateInfo {
        // Sort | 排序
        Sort uint32 `json:"sort,optional"`

        // Name | 名称
        Name string `json:"name"`

        // Title | 标题
        Title string `json:"title"`

        // Icon | 图标
        Icon string `json:"icon,optional"`

        // ParentId | 父级ID
        ParentId string `json:"parentId,optional"`

        // MenuType | 菜单类型
        //        MenuType uint32 `json:"menuType,optional"`

        // Url | 路径
        Url string `json:"url"`

        // Redirect | 重定向
        Redirect string `json:"redirect,optional"`

        // Component    | 组件
        Component string `json:"component"`

        // IsActive | 是否激活
        IsActive bool `json:"isActive,optional"`

        // Hidden | 是否隐藏
        Hidden bool `json:"hidden,optional"`

        // HiddenInTab | 是否隐藏在Tab
        HiddenInTab bool `json:"hiddenInTab,optional"`

        // Fixed | 是否固定
        Fixed bool `json:"fixed,optional"`

        // Remark   | 备注
        Remark string `json:"remark,optional"`

        // Meta | 元数据
        Meta string `json:"meta,optional"`

        // IsFullPage | 是否全屏
        IsFullPage bool `json:"isFullPage,optional"`

    }

        // The response data of menu list | Menu列表数据
    MenuListResp {
        BaseDataInfo

        // Menu list data | Menu列表数据
        Data MenuListInfo `json:"data"`
    }

        // Menu list data | Menu列表数据
    MenuListInfo {
        BaseListInfo

        // The API list data | Menu列表数据
        Data []MenuInfo `json:"data"`
    }

        // Get menu list request params | Menu列表请求参数
    MenuListReq {
        PageInfo

        // ParentId
        ParentId string `json:"parentId,optional"`

        // Name
        Name string `json:"name,optional"`

        // Title
        Title string `json:"title,optional"`

        // Icon
        Icon string `json:"icon,optional"`

        CommonSearchInfo
    }

        // Menu information response | Menu信息返回体
    MenuInfoResp {
        BaseDataInfo

        // Menu information | Menu数据
        Data MenuInfo `json:"data"`
    }
        // Menu create request | Menu创建信息请求体
    MenuCreateReq {
        MenuCreateInfo
    }
        // Menu update request | Menu更新信息请求体
    MenuUpdateReq {
        IDReq
        MenuCreateInfo
    }
)

@server(
    // jwt: Auth
    group: menu
    middleware: AuthMiddleware,Authority,TraceMiddleware
    prefix: /saas/api/v1
)

service phoenix {
    // Create menu information | 创建Menu
    @handler createMenu
    post /menu/create (MenuCreateReq) returns (BaseDataInfo)

    // Update menu information | 更新Menu
    @handler updateMenu
    post /menu/update (MenuUpdateReq) returns (BaseDataInfo)

    // Delete menu information | 删除Menu信息
    @handler deleteMenu
    post /menu/delete (IDsReq) returns (BaseDataInfo)

    // Get menu list | 获取Menu列表
    @handler getMenuList
    get /menu/list (MenuListReq) returns (MenuListResp)

    // Get menu tree list | 获取 Menu 树状列表
    @handler getMenuTreeList
    get /menu/tree/list (MenuListReq) returns (MenuListResp)

    // Get menu by ID | 通过ID获取Menu
    @handler getMenuById
    get /menu/:id (IDPathReq) returns (MenuInfoResp)

    // Get menu by user ID | 通过user ID获取可用Menu
    @handler getMenuByUserId
    get /menu/get_menus_by_user/:id (IDPathReq) returns (MenuListResp)

    // Get menu tree list by tenant | 通过租户 获取 Menu 树状列表
    @handler getMenuTreeListByTenant
    get /menu/tenant/tree/list () returns (MenuListResp)

    // Import menu from JSON file | 从JSON文件导入菜单
    @handler importMenuFromFile
    post /menu/import/file returns (BaseDataInfo)
}
