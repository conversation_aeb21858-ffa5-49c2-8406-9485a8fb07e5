syntax = "v1"

info(
    title: "more help management"
    desc: "more help management"
    version: "v1.0"
)

type (
    MoreHelpFileInfo {
        // 列表id
        ID string `json:"id"`
        // 文件id
        FileID string `json:"fileId"`
        // 文件名称
        FileName string `json:"fileName"`
        // 文件类型
        FileType string `json:"fileType"`
        // 是否启用
        IsOpen bool `json:"isOpen,optional"`
        // 创建时间
        CreatedAt int64 `json:"createdAt,optional"`
        // 更新时间
        UpdatedAt int64 `json:"updatedAt,optional"`
        // 创建人
        CreatedBy string `json:"createdBy,optional"`
        // 更新人
        UpdatedBy string `json:"updatedBy,optional"`
    }

    // 获取更多帮助文件列表数据
    MoreHelpFileListResp {
        BaseDataInfo
        Data MoreHelpFileListInfo `json:"data"`
    }
    MoreHelpFileListInfo {
        BaseListInfo
        Data []MoreHelpFileInfo `json:"data"`
    }

    // 新增更多帮助文件信息
    CreateMoreHelpFileReq {
        // 文件id
        FileID string `json:"fileId"`
    }

    // 更新更多帮助文件信息
    UpdateMoreHelpFileReq {
        ID string `json:"id"`
        // 文件id
        FileID string `json:"fileId"`
    }

    // 删除更多帮助文件信息
    DeleteMoreHelpFileReq {
        // 更多帮助文件列表信息id
        ID string `json:"id"`
    }
    // 更多帮助文件拖动排序信息
    DragSortMoreHelpFileReq {
        // 拖动文件列表信息id
        ID string `json:"id"`
        // 目标文件列表信息id
        TargetID string `json:"targetId"`
        // 放置位置 top：上， bottom：下
        Position string `json:"position"`
    }
)

@server (
    //    jwt : Auth
    group:      morehelp
    middleware: TraceMiddleware,AuthMiddleware,Authority
    prefix:     /saas/api/v1
)

service phoenix {
    // 获取更多帮助文件列表
    @handler moreHelpFileList
    get /more-help/file/list returns (MoreHelpFileListResp)

    // 根据文件id新增更多帮助文件信息
    @handler createMoreHelpFile
    post /more-help/file/create (CreateMoreHelpFileReq) returns (BaseDataInfo)

    // 删除更多帮助文件信息
    @handler deleteMoreHelpFile
    post /more-help/file/delete (DeleteMoreHelpFileReq) returns (BaseDataInfo)

    // 根据文件id更新更多帮助文件信息
    @handler updateMoreHelpFile
    post /more-help/file/update (UpdateMoreHelpFileReq) returns (BaseDataInfo)

    // 拖动排序
    @handler moreHelpFileDragSort
    post /more-help/file/drag/sort (DragSortMoreHelpFileReq) returns (BaseDataInfo)
}
