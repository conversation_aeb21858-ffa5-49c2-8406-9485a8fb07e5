syntax = "v1"

info(
    title: "authority control"
    desc: "authority control including authority management, role access control"
    version: "v1.0"
)



type (
    // The response data of api authorization | API授权数据
    ApiAuthorityInfo {
        // API ID | APIID
        Id string `json:"id" validate:"required"`

        // API path | API 路径
        Path string `json:"path" validate="required,max=80"`

        // API method | API请求方法
        Method string `json:"method" validate="required,min=3,max=4"`
    }


        // Create or update api authorization information request | 创建或更新API授权信息
    CreateOrUpdateApiAuthorityReq {
        // Role ID | 角色ID
        RoleId string `json:"roleId" validate:"required"`

        // API authorization list | API授权列表数据
        // Required: true
        Data []ApiAuthorityInfo `json:"data"`
    }

        // The response data of api authorization list | API授权列表返回数据
    ApiAuthorityListResp {
        BaseDataInfo

        // The api authorization list data | API授权列表数据
        Data ApiAuthorityListInfo `json:"data"`
    }

        // The  data of api authorization list | API授权列表数据
    ApiAuthorityListInfo {
        BaseListInfo

        // The api authorization list data | API授权列表数据
        Data []ApiAuthorityInfo `json:"data"`
    }

        // Create or update menu authorization information request params | 创建或更新菜单(按钮)授权信息参数
    MenuAuthorityInfoReq {
        // role ID | 角色ID
        RoleId string `json:"roleId" validate:"required"`

        // menu ID array | 菜单ID数组
        MenuIds []string `json:"menuIds" validate:"required"`

        // button ID array | 菜单ID数组
        ButtonIds []string `json:"buttonIds" validate:"required"`
    }

        // Menu authorization response data | 菜单授权信息数据
    MenuAuthorityInfoResp {
        BaseDataInfo

        // The menu authorization data | 菜单授权信息数据
        Data MenuAuthorityInfoReq `json:"data"`
    }
)

@server(
    // jwt: Auth
    group: authority
    middleware: AuthMiddleware,Authority,TraceMiddleware
    prefix: /saas/api/v1
)

service phoenix {
    // Create or update API authorization information | 创建或更新API权限
    @handler createOrUpdateApiAuthority
    post /authority/api/create_or_update (CreateOrUpdateApiAuthorityReq) returns (BaseDataInfo)

    // Reload API authorization cache | 重新加载API权限缓存
    @handler reloadApiAuthorityRuleCache
    post /authority/api/reload_rules returns (BaseDataInfo)

    // Get role's API authorization list | 获取角色api权限列表
    @handler getApiAuthority
    get /authority/api/role/:id (IDPathReq) returns (ApiAuthorityListResp)

    // Create or update menu authorization information | 创建或更新菜单（按钮）权限
    @handler createOrUpdateMenuAuthority
    post /authority/menu/create_or_update (MenuAuthorityInfoReq) returns (BaseDataInfo)

    // Get role's menu authorization list | 获取角色菜单（按钮）权限列表
    @handler getMenuAuthority
    get /authority/menu/role/:id (IDPathReq) returns (MenuAuthorityInfoResp)
}
