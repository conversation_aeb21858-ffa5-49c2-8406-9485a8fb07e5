

type (
    // The response data of token information | Token信息
    TokenInfo {
        BaseIDInfo

        // Status
        Status  bool `json:"status,optional"`

        // TenantId
        TenantId  string `json:"tenantId,optional"`

        // Uid
        Uid  string `json:"uid,optional"`

        // Token
        Token  string `json:"token,optional"`

        // Source
        Source  string `json:"source,optional"`

        // ExpiredAt
        ExpiredAt  int64 `json:"expiredAt,optional"`
    }

    // The response data of token list | Token列表数据
    TokenListResp {
        BaseDataInfo

        // Token list data | Token列表数据
        Data TokenListInfo `json:"data"`
    }

    // Token list data | Token列表数据
    TokenListInfo {
        BaseListInfo

        // The API list data | Token列表数据
        Data  []TokenInfo  `json:"data"`
    }

    // Get token list request params | Token列表请求参数
    TokenListReq {
        PageInfo

        // Token
        Token  string `json:"token,optional"`

        // TenantId
        TenantId  string `json:"tenantId,optional"`

        // Source
        Source  string `json:"source,optional"`

        CommonSearchInfo
    }

    // Token information response | Token信息返回体
    TokenInfoResp {
        BaseDataInfo

        // Token information | Token数据
        Data TokenInfo `json:"data"`
    }
)

@server(
    // jwt: Auth
    group: token
    middleware: AuthMiddleware,Authority,TraceMiddleware
    prefix: /saas/api/v1
)

service phoenix {
    // Create token information | 创建Token
    @handler createToken
    post /token/create (TokenInfo) returns (BaseDataInfo)

    // Update token information | 更新Token
    @handler updateToken
    post /token/update (TokenInfo) returns (BaseDataInfo)

    // Delete token information | 删除Token信息
    @handler deleteToken
    post /token/delete (IDsReq) returns (BaseDataInfo)

    // Get token list | 获取Token列表
    @handler getTokenList
    get /token/list (TokenListReq) returns (TokenListResp)

    // Get token by ID | 通过ID获取Token
    @handler getTokenById
    get /token/:id (IDPathReq) returns (TokenInfoResp)
}
