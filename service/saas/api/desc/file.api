syntax = "v1"

info (
	title:   "file management"
	desc:    "file management"
	author:  "<PERSON>"
	email:   "<EMAIL>"
	version: "v1.0"
)


type (

	// Generate the parameters of the pre-signed url for the uploaded file | 生成上传文件预签名url参数
	GenerateFileUploadPreSignedUrlReq {
		// File name | 文件名称
		FileName string `json:"fileName"`
		// File size | 文件大小
		FileSize int64 `json:"fileSize"`
	}

		// Upload the pre-signed url data of the file | 文件上传预签名url数据
	GenerateFileUploadPreSignedUrlResp {
		BaseDataInfo
		Data GenerateFileUploadPreSignedUrlInfo `json:"data"`
	}
	GenerateFileUploadPreSignedUrlInfo {
		// File id | 文件id
		Id string `json:"id"`
		// File type | 文件类型
		FileType string `json:"fileType`
		// FileUploadPreSignedUrl | 文件上传预签名url
		FileUploadPreSignedUrl string `json:"fileUploadPreSignedUrl"`
	}

		// Return the call parameters on the file | 文件上传回调参数
	FileUploadSeccussCallBackReq {
		// 事件名称
		EventName string `json:"EventName"`
		// 文件路径
		Key       string `json:"Key"`
	}

		// Generate the pre-signed url parameters for file download | 生成文件下载预签名url参数
	GenerateFileDownloadPreSignedUrlReq {
		// id | 文件id
		Id string `json:"id"`
		// 过期时间（秒）
		ExpirationTimeSecond int64 `json:"expirationTimeSecond,optional"`
		// 类型（0下载 | 1预览）
		LoadType int `json:"loadType,optional"`
	}

		// File download pre-signed url data | 文件下载预签名url数据
	GenerateFileDownloadPreSignedUrlResp {
		BaseDataInfo
		Data GenerateFileDownloadPreSignedUrlInfo `json:"data"`
	}
	GenerateFileDownloadPreSignedUrlInfo {
		// FileDownloadPreSignedUrl | 文件下载预签名url
		FileDownloadPreSignedUrl string `json:"fileDownloadPreSignedUrl"`
	}

		// The data when upload finished | 上传完成数据
	UploadInfo {
		// ID
		// Required: true
		Id string `json:"id"`
		// FOriginName | 原始文件名称
		OriginName string `json:"originName"`
		// File name | 文件名称
		Name string `json:"name"`
		// File Hash | 文件Hash
		Hash string `json:"hash"`
		// File path | 文件路径
		Url string `json:"url"`
	}
		// The response data when upload finished | 上传完成返回的数据
	UploadResp {
		BaseDataInfo
		// The  data when upload finished | 上传完成数据
		Data UploadInfo `json:"data"`
	}
		// Update file information params | 更新文件信息参数
	UpdateFileReq {
		// ID
		// Required : true
		ID string `json:"id"`
		// File name | 文件名
		// Required : true
		// Max length: 50
		Name string `json:"name" validate:"max=50"`
	}
		// Get file list params | 获取文件列表参数
	FileListReq {
		PageInfo
		// File type | 文件类型
		// Required : true
		// Max length: 10
		FileType uint8 `json:"fileType,optional" validate:"omitempty,alpha,max=10"`
		// File name | 文件名
		// Required : true
		// Max length: 50
		FileName string `json:"fileName,optional" validate:"max=50"`
		// Create date period | 创建日期时间段
		// Required : true
		Period []string `json:"period,optional"`
	}
		// The response data of file information | 文件信息数据
	FileInfo {
		BaseIDInfo
		// UUID
		UUID string `json:"UUID"`
		// User's UUID | 用户的UUID
		UserID string `json:"userId"`
		// File origin name | 原始文件名
		OriginName string `json:"originName"`
		// File name | 文件名
		Name string `json:"name"`
		// File type | 文件类型
		FileType uint8 `json:"fileType"`
		// open status | 公开状态
		OpenStatus uint8 `json:"openStatus"`
		// File size | 文件大小
		Size uint64 `json:"size"`
		// File path | 文件路径
		Path string `json:"path"`
		// File hash | 文件hash
		Hash string `json:"hash"`
		// File path | 文件路径
		Url string `json:"url"`
	}
		// The response data of file information list | 文件信息列表数据
	FileListResp {
		BaseDataInfo
		// The file list data | 文件信息列表数据
		Data FileListInfo `json:"data"`
	}
		// The response data of file information list | 文件信息列表数据
	GetFileResp {
		BaseDataInfo
		// The file list data | 文件信息数据
		Data FileInfo `json:"data"`
	}
	FileListInfo {
		BaseListInfo
		// The file list data | 文件信息列表数据
		Data []FileInfo `json:"data"`
	}
	
	GenerateWatermarkedFileResp {
		BaseDataInfo
		Data GenerateWatermarkedFileInfo `json:"data"`
	}

	GenerateWatermarkedFileInfo {
		// The new file ID | 新文件ID
		Id string `json:"id"`
		// The preview URL for the new file | 新文件的预览URL
		PreviewUrl string `json:"previewUrl"`
	}
)

// The request params of setting boolean status | 设置状态参数
type StatusCodeReq {
	// ID
	// Required: true
	Ids []string `json:"ids"`
	// openStatus code | 状态码
	// Required: true
	OpenStatus uint64 `json:"openStatus" validate:"number"`
}

type (
	// The request params of generating a watermarked file | 生成水印文件的请求参数
	GenerateWatermarkedFileReq {
		// File ID to add watermark to | 需要加水印的文件ID
		// Required: true
		Id string `json:"id"`
		// Custom watermark text. Defaults to 'user + time' if empty. | 自定义水印文本，如果为空则默认为"用户+时间"
		WatermarkText string `json:"watermarkText"`
		// Font size in points | 字号（pt）
		FontSize int `json:"fontSize,optional"`
		// Watermark color RGB values, e.g., "128 128 128" for gray. | 水印颜色RGB值，例如 "128 128 128" 代表灰色
		Color string `json:"color,optional"`
		// Watermark opacity, e.g., 0.5 for 50%. | 水印不透明度，例如 0.5 代表 50%
		Opacity float64 `json:"opacity,optional"`
		// Rotation angle in degrees, e.g., -15 for counter-clockwise 15 degrees. | 旋转角度，例如 -15 代表逆时针旋转15度
		Rotation float64 `json:"rotation,optional"`
		// Horizontal offset in points | 水平偏移（pt）
		XOffset int `json:"xOffset,optional"`
		// Vertical offset in points | 垂直偏移（pt）
		YOffset int `json:"yOffset,optional"`
	}
)

@server (
	//    jwt : Auth
	group:      file
	prefix:     /file/api/v1
)
service phoenix {
	// Upload success callback | 上传成功后的回调接口
	@handler fileUploadSuccessCallback
	post /file/upload/success/callback (FileUploadSeccussCallBackReq) returns (BaseDataInfo)
}

@server (
	//    jwt : Auth
	group:      file
	middleware: AuthMiddleware,Authority
	prefix:     /file/api/v1
)
service phoenix {

	// Generate the pre-signed url for the uploaded file | 生成上传文件预签名url
	@handler generateUploadPreSignedUrl
	post /file/upload/presignedurl/generate (GenerateFileUploadPreSignedUrlReq) returns (GenerateFileUploadPreSignedUrlResp)

	// Upload file | 上传文件
	@handler upload
	post /file/upload returns (UploadResp)

	// Get file list | 获取文件列表
	@handler fileList
	post /file/list (FileListReq) returns (FileListResp)

	// Update file information | 更新文件信息
	@handler updateFile
	post /file/update (UpdateFileReq) returns (BaseDataInfo)

	// Delete file information | 获取文件信息
	@handler getFile
	get /file/get/:id (IDPathReq) returns (GetFileResp)

	// Delete file information | 删除文件信息
	@handler deleteFile
	post /file/delete (IDsReq) returns (BaseDataInfo)

	// Change file public status | 改变文件公开状态
	@handler changePublicStatus
	post /file/status (StatusCodeReq) returns (BaseDataInfo)

	// Download file | 下载文件
	@handler downloadFile
	get /file/download/:id (IDPathReq)

	// Generate the pre-signed url for the downloaded file | 生成下载文件预签名url
	@handler generateDownloadPreSignedUrl
	post /file/download/presignedurl/generate (GenerateFileDownloadPreSignedUrlReq) returns (GenerateFileDownloadPreSignedUrlResp)

	// Generate a new file with watermark | 生成带水印的新文件
	@handler generateWatermarkedFile
	post /file/watermark/generate (GenerateWatermarkedFileReq) returns (GenerateWatermarkedFileResp)
}
