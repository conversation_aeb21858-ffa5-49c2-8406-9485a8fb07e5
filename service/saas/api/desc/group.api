

type (
    // The response data of group information | Group信息
    GroupInfo {
        BaseIDInfo

        // Status
        Status bool `json:"status,optional"`

        // Sort
        Sort uint32 `json:"sort,optional"`

        // TenantId
        TenantId string `json:"tenantId,optional"`

        // GroupTypeId
        GroupTypeId string `json:"groupTypeId,optional"`

        // GroupTypeName
        GroupTypeName string `json:"groupTypeName,optional"`

        // Name
        Name string `json:"name,optional"`

        // Code
        Code string `json:"code,optional"`

        // Remark
        Remark string `json:"remark,optional"`
    }

        // The response data of group information | Group信息
    GroupCreateReq {
        // Status
        Status bool `json:"status,optional"`

        // Sort
        Sort uint32 `json:"sort,optional"`

        // TenantId
        TenantId string `json:"tenantId,optional"`

        // GroupTypeId
        GroupTypeId string `json:"groupTypeId,optional"`

        // Name
        Name string `json:"name,optional"`

        // Code
        Code string `json:"code,optional"`

        // Remark
        Remark string `json:"remark,optional"`
    }

        // The response data of group list | Group列表数据
    GroupListResp {
        BaseDataInfo

        // Group list data | Group列表数据
        Data GroupListInfo `json:"data"`
    }

        // Group list data | Group列表数据
    GroupListInfo {
        BaseListInfo

        // The API list data | Group列表数据
        Data []GroupInfo `json:"data"`
    }

        // Get group list request params | Group列表请求参数
    GroupListReq {
        PageInfo

        // Name
        Name string `json:"name,optional"`

        // Code
        Code string `json:"code,optional"`

        // GroupTypeId
        GroupTypeId string `json:"groupTypeId,optional"`

        // TenantId
        TenantId string `json:"tenantId,optional"`

        // Remark
        Remark string `json:"remark,optional"`

        CommonSearchInfo
    }

        // Group information response | Group信息返回体
    GroupInfoResp {
        BaseDataInfo

        // Group information | Group数据
        Data GroupInfo `json:"data"`
    }

        // Get group by ID request params | 通过ID获取Group请求参数
    GetGroupUsersReq {
        // GroupId | GroupId
        GroupId string `path:"groupId"`

        CommonSearchInfo
    }

        // Update group users request params | 更新Group用户请求参数
    UpdataGroupUsersReq {
        // GroupId | GroupId
        GroupId string `json:"groupId"`

        // UserIds | 用户ID列表
        UserIds []string `json:"userIds"`
        Kind    string   `json:"kind,optional"`
    }

        // GroupUsersList list data | GroupUsersList列表数据
    GroupUsersList {
        Data []UserInfo `json:"data"`
    }

        // Get group users response | 获取Group用户返回体
    GetGroupUsersResp {
        BaseDataInfo

        // GroupUsersList list data | GroupUsersList列表数据
        Data GroupUsersList `json:"data"`
    }
)

@server(
    // jwt: Auth
    group: group
    middleware: AuthMiddleware,Authority,TraceMiddleware
    prefix: /saas/api/v1
)

service phoenix {
    // Create group information | 创建Group
    @handler createGroup
    post /group/create (GroupCreateReq) returns (BaseDataInfo)

    // Update group information | 更新Group
    @handler updateGroup
    post /group/update (GroupInfo) returns (BaseDataInfo)

    // Delete group information | 删除Group信息
    @handler deleteGroup
    post /group/delete (IDsReq) returns (BaseDataInfo)

    // Get group list | 获取Group列表
    @handler getGroupList
    get /group/list (GroupListReq) returns (GroupListResp)

    // Get group by ID | 通过ID获取Group
    @handler getGroupById
    get /group/:id (IDPathReq) returns (GroupInfoResp)

    // Get group users | 获取Group用户
    @handler getGroupUsers
    get /group/users/:groupId (GetGroupUsersReq) returns (GetGroupUsersResp)

    // Update group users | 更新Group用户
    @handler updateGroupUsers
    post /group/update_users (UpdataGroupUsersReq) returns (BaseDataInfo)
}
