

type (
    // The response data of organization information | Organization信息
    OrganizationInfo {
        BaseIDInfo

        // Status | 状态 1 正常 2 禁用
        Status bool `json:"status,optional"`

        // Sort | 排序
        Sort uint32 `json:"sort,optional"`

        // TenantId | 租户ID
        TenantId string `json:"tenantId,optional"`

        // Name | 组织架构名称
        Name string `json:"name,optional"`

        // Ancestors | 祖级列表
        Ancestors string `json:"ancestors,optional"`

        // Code | 组织架构编码
        Code string `json:"code,optional"`

        // NodeType | 组织架构类型 （单位或部门）0 单位 1 部门
        NodeType uint32 `json:"nodeType,optional"`

        // Leader | 部门负责人
        Leader string `json:"leader,optional"`

        // Phone | 部门负责人电话
        Phone string `json:"phone,optional"`

        // Email | 部门负责人邮箱
        Email string `json:"email,optional"`

        // Remark | 备注
        Remark string `json:"remark,optional"`

        // ParentId | 父级ID
        ParentId string `json:"parentId,optional"`

        // Children | 子级
        Children []OrganizationInfo `json:"children,optional"`

        UserCount int64 `json:"userCount,optional"`

        // 组织领导
        OrganizationLeaders []OrganizationLeaders `json:"organizationLeaders,optional"`
        // 组织管理员
        OrganizationAdmins []OrganizationAdmins `json:"organizationAdmins,optional"`
    }

    OrganizationLeaders {
        LeaderID string `json:"leaderId"`
        LeaderName string `json:"leaderName"`
    }
    OrganizationAdmins {
        AdminID string `json:"adminId"`
        AdminName string `json:"adminName"`
    }

        // The create request data of organization information | Organization 创建信息
    OrganizationCreateReq {
        // Status | 状态 1 正常 2 禁用
        Status bool `json:"status"`

        // Sort | 排序
        Sort uint32 `json:"sort,optional"`

        // TenantId | 租户ID
        TenantId string `json:"tenantId,optional"`

        // Name | 组织架构名称
        Name string `json:"name"`

        // Code | 组织架构编码
        Code string `json:"code"`

        // NodeType | 组织架构类型 （单位或部门）0 单位 1 部门
        NodeType uint32 `json:"nodeType,optional"`

        // Leader | 部门负责人
        Leader string `json:"leader,optional"`

        // Phone | 部门负责人电话
        Phone string `json:"phone,optional"`

        // Email | 部门负责人邮箱
        Email string `json:"email,optional"`

        // Remark | 备注
        Remark string `json:"remark,optional"`

        // ParentId | 父级ID
        ParentId string `json:"parentId,optional"`
    }

    OrganizationUpdateReq {
        IDReq
        OrganizationCreateReq
    }

        // The response data of organization list | Organization列表数据
    OrganizationListResp {
        BaseDataInfo

        // Organization list data | Organization列表数据
        Data OrganizationListInfo `json:"data"`
    }

        // Organization list data | Organization列表数据
    OrganizationListInfo {
        BaseListInfo

        // The API list data | Organization列表数据
        Data []OrganizationInfo `json:"data"`
    }

        // Get organization list request params | Organization列表请求参数
    OrganizationListReq {
        PageInfo

        // Name
        Name string `json:"name,optional"`

        // Ancestors
        Ancestors string `json:"ancestors,optional"`

        // Code
        Code string `json:"code,optional"`

        // TenantId
        TenantId string `json:"tenantId,optional"`

        // Extra
        Extra string `json:"extra,optional"`

        ParentID string `form:"parentId,optional"`

        NoParent bool `form:"noParent,optional"`

        CommonSearchInfo
    }

        // Organization information response | Organization信息返回体
    OrganizationInfoResp {
        BaseDataInfo

        // Organization information | Organization数据
        Data OrganizationInfo `json:"data"`
    }

        // Organization users request | OrganizationUsers请求体
    GetOrganizationUsersReq {
        OrganizationId string `json:"organizationId,optional" path:"organizationId"`

        CommonSearchInfo
    }

    UpdateOrganizationUsersReq {
        OrganizationId string `json:"organizationId"`
        UserIds []string `json:"userIds"`
    }

    OrganizationListByNodeTypesReq {
        NodeTypes []uint32 `json:"nodeTypes"`
    }

    OrganizationListByNodeTypesResp {
        BaseDataInfo
        Data []OrganizationInfo `json:"data"`
    }

    OrganizationListByCodesReq {
        Codes []string `json:"codes"`
    }

    OrganizationListByCodesResp {
        BaseDataInfo
        Data []OrganizationInfo `json:"data"`
    }

    GetOrgsAndUsersReq {
        OrgID string `form:"orgId"`
    }

    GetOrgsAndUsersResp {
        BaseDataInfo
        Data []GetOrgsAndUsersInfo `json:"data"`
    }

    GetOrgsAndUsersInfo {
        OrgID string `json:"orgId"`
        UserID string `json:"userId"`
    }
)

@server(
    // jwt: Auth
    group: organization
    middleware: AuthMiddleware,Authority,TraceMiddleware
    prefix: /saas/api/v1
)

service phoenix {
    // Create organization information | 创建Organization
    @handler createOrganization
    post /organization/create (OrganizationCreateReq) returns (BaseDataInfo)

    // Update organization information | 更新Organization
    @handler updateOrganization
    post /organization/update (OrganizationUpdateReq) returns (BaseDataInfo)

    // Delete organization information | 删除Organization信息
    @handler deleteOrganization
    post /organization/delete (IDsReq) returns (BaseDataInfo)

    // Get organization tree list | 获取 Organization 树状列表
    @handler getOrganizationTreeList
    get /organization/tree/list (OrganizationListReq) returns (OrganizationListResp)

    // Get organization list | 获取Organization列表
    @handler getOrganizationList
    get /organization/list (OrganizationListReq) returns (OrganizationListResp)

    // Get organization list | 获取Organization列表
    @handler getOrganizationListByNodeTypes
    post /organization/list/by_node_types (OrganizationListByNodeTypesReq) returns (OrganizationListByNodeTypesResp)

    // Get organization by ID | 通过ID获取Organization
    @handler getOrganizationById
    get /organization/:id (IDPathReq) returns (OrganizationInfoResp)

    // Update organization users information | 更新OrganizationUsers
    @handler updateOrganizationUsers
    post /organization/update_users (UpdateOrganizationUsersReq) returns (BaseDataInfo)

    // 根据 codes 获取 organization 列表
    @handler getOrganizationListByCodes
    post /organization/list/bycodes (OrganizationListByCodesReq) returns (OrganizationListByCodesResp)
    // Get organization users information | 获取组织和用户
    @handler getAllOrganizationsAndUsersByOrgId
    get /organization/all-organizations-users (GetOrgsAndUsersReq) returns (GetOrgsAndUsersResp)


}
