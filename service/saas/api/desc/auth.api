

type (
    LoginReq {
        // User name | 用户名
        Username string `json:"username" validate:"required"`
        // Password | 密码
        Password string `json:"password" validate:"required"`

        // Captcha ID which store in redis | 验证码编号, 存在redis中
        // Required: true
        // Max length: 20
        //        CaptchaId  string `json:"captchaId"  validate:"required,len=20"`
        CaptchaId string `json:"captchaId,optional"`

        // The Captcha which users input | 用户输入的验证码
        // Required: true
        // Max length: 5
        Captcha string `json:"captcha,optional"`

        // Login method | 登录方式
        Method int64 `json:"method"`

        // Mobile | 手机号
        Mobile string `json:"mobile"`

        // DeviceNo | 设备号
        DeviceNo string `json:"deviceNo,optional"`

        // DeviceKind | 设备类型
        DeviceKind int64 `json:"deviceKind"`

        // DeviceKey ｜ 设备标识
        DeviceKey string `json:"deviceKey,optional"`

        // Imei | Imei
        Imei string `json:"imei,optional"`
    }
    SuccessLoginUserInfo {
        BaseIDInfo

        // Status
        Status bool `json:"status,optional"`

        // DefaultTenantId | 默认租户ID
        DefaultTenantId string `json:"defaultTenantId,optional"`

        // Username | 用户名
        Username string `json:"username,optional"`

        // Nickname | 昵称
        Nickname string `json:"nickname,optional"`

        // Mobile | 手机号
        Mobile string `json:"mobile,optional"`

        // Email | 邮箱
        Email string `json:"email,optional"`

        // Gender
        Gender string `json:"gender,optional"`

        // Post | 职务
        Post string `json:"post,optional"`

        // Avatar | 头像
        Avatar AvatarInfo `json:"avatar,optional"`

        // Kind 用户类型
        Kind string `json:"kind,optional"`
    }

    SuccessLoginInfo {
        // AccessToken information | AccessToken
        AccessToken string `json:"accessToken"`
        // Expire information | 过期时间
        Expire uint64 `json:"expire"`
        // User information | User数据
        User SuccessLoginUserInfo `json:"user"`
        // 插件信息
        SystemPlugins []SystemPlugin `json:"systemPlugins,optional"`
        // 租户信息
        TenantInfos []TenantInfo `json:"tenantInfos,optional"`
    }
        //  LoginResp information |  User+Token信息
    LoginResp {
        BaseDataInfo
        // Data information | Data数据
        Data SuccessLoginInfo `json:"data"`
    }

        //  TokenCheck request information | 令牌校验请求信息
    TokenCheckReq {
        // Token information | Token
        Token string `json:"token"`
    }

        //  TokenCheck information | 令牌校验结果
    TokenCheckInfo {
        IsValid bool `json:"isValid"`
        //        User UserInfoWithExtraInfo `json:"user,optional"`
    }

        //  TokenCheck response information | 令牌校验响应信息
    TokenCheckResp {
        BaseDataInfo
        Data TokenCheckInfo `json:"data"`
    }

        //  TokenRefresh request information | 令牌刷新请求信息
    TokenRefreshReq {
        // TenantId information | 租户ID(可选, 替换当前登录的租户时使用)
        TenantId string `json:"tenantId,optional"`
    }

    AuthenticateReq {

        // token | 验证令牌
        Token string `json:"token"`
    }

    AuthenticateResult {
        // is valid | 令牌是否有效
        IsValid bool `json:"isValid"`
        // is created | 是否为新建用户
        IsCreated bool `json:"isCreated"`
        // UserID | 系统用户ID
        UserID string `json:"userId"`
        // TenantID | 默认租户ID
        TenantID string `json:"tenantId"`
        // sso User info | SSO 用户信息
        User SuccessLoginUserInfo `json:"user"`
    }

    AuthenticateResp {
        BaseDataInfo
        Data AuthenticateResult `json:"data"`
    }

    PasswordFreeLoginReq{
        UserID string `json:"userId,optional"`
        Mobile string `json:"mobile,optional"`
        Username string `json:"username,optional"`
        PlatUserId string `json:"platUserId,optional"`
        PlatCode string `json:"platCode,optional"`
        IsVirtualUser bool `json:"isVirtualUser,optional"`
    }
)

@server(
    group: auth
    prefix: /saas/api/v1
)

service phoenix {
    // Login | 登录
    @handler Login
    post /auth/login (LoginReq) returns (LoginResp)

    // TokenCheck | token校验
    @handler TokenCheck
    post /auth/token_check (TokenCheckReq) returns (TokenCheckResp)

    // authenticate | 认证
    @doc "authenticate | 认证"
    @handler authenticate
    post /auth/authenticate (AuthenticateReq) returns (AuthenticateResp)
}


@server(
    // jwt: Auth
    group: auth
    middleware: AuthMiddleware,Authority,TraceMiddleware
    prefix: /saas/api/v1
)

service phoenix {

    // TokenRefresh | token刷新
    @handler TokenRefresh
    post /auth/token_refresh (TokenRefreshReq) returns (LoginResp)

}


@server(
    // jwt: Auth
    group: auth
    middleware: TraceMiddleware
    prefix: /saas/inside/api/v1
)

service phoenix {
    // TokenRefresh | token刷新
    @handler passwordFreeLogin
    post /auth/password-free-login (PasswordFreeLoginReq) returns (LoginResp)
}