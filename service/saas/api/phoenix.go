package main

import (
	"context"
	"flag"
	"fmt"
	"phoenix/service/saas/api/bootstrap"
	"phoenix/service/saas/api/internal/config"
	"phoenix/service/saas/utils/async_logger"

	"gitee.com/damengde/gzconfigcenter"
	"github.com/zeromicro/go-zero/core/logx"
)

var (
	configFile    = flag.String("f", "etc/phoenix.yaml", "the config file")
	etcdEndpoints = flag.String("etcd", "", "etcd endpoints")
	etcdUsername  = flag.String("etcd-user", "", "etcd username")
	etcdPassword  = flag.String("etcd-pass", "", "etcd password")
	configKey     = flag.String("config-key", "/services/config/phoenixapi/default", "config key")
)

func main() {
	// 获取cli参数
	flag.Parse()

	// 创建配置管理器
	configManager, err := gzconfigcenter.NewConfigManager[config.Config](gzconfigcenter.EtcdConfig{
		Endpoints: gzconfigcenter.SplitStringIgnoreEmpty(*etcdEndpoints, ","),
		Username:  *etcdUsername,
		Password:  *etcdPassword,
		ConfigKey: *configKey,
	}, *configFile)
	if err != nil {
		panic(fmt.Sprintf("create config manager error: %v", err))
	}
	ctx := context.WithoutCancel(context.Background())
	defer ctx.Done()

	// 初始化http sercet
	s, svcCtx, _ := bootstrap.NewServer(ctx, configManager)
	// 初始化日志处理器
	OperationLogClient := async_logger.NewOperationLogProcessor(svcCtx.GormDB, svcCtx.Redis, svcCtx.Config.AsyncLoggerConf)
	OperationLogClient.StartOperationLogProcessor()
	defer s.Stop()

	// 启动Kafka消费者
	// queue.Init(svcCtx.Config, svcCtx.GormDB, svcCtx.IDGenerator)

	// 启动服务
	fmt.Printf("Starting server at %s:%d...\n", configManager.GetConfig().Host, configManager.GetConfig().Port)
	logx.Infof("Service config:%#v", configManager.GetConfig())
	s.Start()
}
