Name: phoenix
Host: 0.0.0.0
Port: 10011
Timeout: 30000
MaxBytes: ********* # 200MB 上传文件的总大小
Auth:
  AccessSecret: auwkjbdoaushgodn12heoiash
  AccessExpire: 259200
  OpenTokenAuth: false
  OpenHeaderForwardAuth: true

Redis:
  Host: "***************:6379"
  Type: node
  Pass: ""

Captcha:
  KeyLong: 5
  ImgWidth: 240
  ImgHeight: 80

StargateRPC:
  # Endpoints:
  #   - 127.0.0.1:11012
  Etcd:
    Hosts:
      - **************:23791
    Key: stargate.rpc
  NonBlock: true


DatabaseConf:
  Type: mysql
  Host: **************
  Port: 33061
  DBName: phoenix
  Username: developer
  Password: "Zj123456."
  MaxOpenConn: 100
  SSLMode: disable
  CacheTime: 5
Log:
  ServiceName: phoenix
  Mode: console
  Path: ./logs
  Encoding: json
  Level: info
  Compress: false
  KeepDays: 7
  StackCoolDownMillis: 100

AsyncLoggerConf:
  LogSavingTiming: 5  # 日志处理器定时(秒)
  BatchSize: 100       # 每批量插入数据库上限(条)

FileServerConf:
  DownloadUrlPrefix: /galaxy/api/v1/file/pre/download?fileId=
  OpenDownloadUrlPrefix: /file/resource/public/  # public path for every one access e.g. nginx path
  StorageStrategy: "minio"   # "minio"或者"local"

MinioConf:
  Endpoint: "**************:9000"  # MinIO服务地址
  AccessKeyID: "zjsoft"          # MinIO访问密钥ID
  SecretAccessKey: "DCZsRMOZFi"      # MinIO访问密钥
  UseSSL: false                      # 是否使用SSL
  BucketName: "test"              # 存储桶名称
  PreSignedExpiry: 3600       # 预签名URL过期时间（秒）

UploadConf:
  MaxImageSize: *********  # 200 mb
  MaxVideoSize: ********* # 200 mb
  MaxAudioSize: *********  # 200 mb
  MaxOtherSize: *********  # 200 mb
  StorePath: ./temp # public path for every one access e.g. nginx path
  FileEncryptSkipSuffix : [".jpg", ".png", ".jpeg", ".webp"]

# 账号锁定时间（秒）
AccountLockDuration: 3600


SnowflakeConf: 
  Node: 1 # 节点
  Epoch: ************* # 元年
  NodeBits: 1 # 节点占位
  StepBits: 5 # 步骤占位

KafkaConf:
  Brokers:
    - **************:9092
  MinBytes: 1
  MaxBytes: 10e6
  Consumers:
    - Topic: data-export-topic
      GroupID: data-export-group
      Key: data_export_record

WorkflowConf:
  FlowEventPushTopic: "flow_event_push_%s"
