syntax = "proto3";

package stargate;
option go_package = "./stargate";

// =====================
// ====== MESSAGES =====
// =====================

// 来自 sso.api
message AvatarInfo {
  string id = 1;
  string url = 2;
}

message SystemPlugin {
  string id = 1;
  string name = 2;
  string code = 3;
}

message PhoenixTenantInfo {
  string id = 1;
  string name = 2;
}

message UserInfo {
  string id = 1;
  bool status = 2;
  string default_tenant_id = 3;
  string username = 4;
  string nickname = 5;
  string mobile = 6;
  string email = 7;
  string gender = 8;
  string post = 9;
  AvatarInfo avatar = 10;
  string kind = 11;
}

message FreePwdAuthReq {
  string token = 1;
  string client_id = 2;
  string user_code = 3;
  int64 ts = 4;
  string req_id = 5;
  string sign = 6;
  string phone_number = 7;
}

message FreePwdAuthResp {
  string access_token = 1;
  uint64 expire = 2;
  UserInfo user = 3;
  repeated SystemPlugin system_plugins = 4;
  repeated PhoenixTenantInfo tenant_infos = 5;
}

// 来自 auth.api
message SendCaptchaReq {
  string img_captcha_id = 1;
  string img_captcha = 2;
  string account = 3;
  string kind = 4;
}

message SendCaptchaResp {
}

message GenerateImgCaptchaReq {
}

message GenerateImgCaptchaResp {
  string captcha_id = 1;
  string captcha_base64 = 2;
}

message LoginUserInfo {
  string id = 1;
  string default_tenant_id = 2;
  string username = 3;
  string nickname = 4;
  string mobile = 5;
  string email = 6;
  string gender = 7;
  string post = 8;
  string avatar_url = 9;
  bool is_admin = 10;
  string device_kind = 11;
  bool is_virtual_user = 12;
}

message LoginReq {
  string account = 1;
  string password = 2;
  string captcha_id = 3;
  string captcha = 4;
  string login_kind = 5;
  string device_kind = 6;
  string ip = 7;
}

message LoginResp {
  string access_token = 1;
  int64 expire = 2;
  LoginUserInfo user = 3;
}

message TokenAuthenticationReq {
  string access_token = 1; // 令牌
}

message TokenAuthenticationResp {
  LoginUserInfo user = 1; // 用户信息
}

message ResetPasswordReq {
  string account = 1; // 账号
  string password = 2; // 新密码
  string captcha = 3; // 验证码
}

message ResetPasswordResp {
}

message ChangePasswordReq {
  string account = 1; // 账号
  string old_password = 2; // 旧密码
  string new_password = 3; // 新密码
  string captcha = 4; // 验证码
}

message ChangePasswordResp {
}

message LogoutReq {
  string access_token = 1; // 令牌
}
message LogoutResp {
}

message GetOnlineUserTokenIDsReq {
}

message GetOnlineUserTokenIDsResp {
  repeated string token_ids = 1; // 令牌id
}


// =====================
// ====== SERVICES =====
// =====================

// sso分组
service Sso {
  // 用户免密认证
  rpc FreePwdAuth(FreePwdAuthReq) returns (FreePwdAuthResp);
}

// auth分组
service Auth {
  // 发送验证码
  rpc SendCaptcha(SendCaptchaReq) returns (SendCaptchaResp);

  // 生成图片验证码
  rpc GenerateImgCaptcha(GenerateImgCaptchaReq) returns (GenerateImgCaptchaResp);

  // 用户登录
  rpc Login(LoginReq) returns (LoginResp);

  // Token认证
  rpc TokenAuthentication(TokenAuthenticationReq) returns (TokenAuthenticationResp);

  // 重置密码
  rpc ResetPassword(ResetPasswordReq) returns (ResetPasswordResp);

  // 修改密码
  rpc ChangePassword(ChangePasswordReq) returns (ChangePasswordResp);

  // 登出
  rpc Logout(LogoutReq) returns (LogoutResp);
}

// 用户分组
service User {
  // 获取在线令牌id
  rpc GetOnlineUserTokenIDs(GetOnlineUserTokenIDsReq) returns (GetOnlineUserTokenIDsResp);
}
