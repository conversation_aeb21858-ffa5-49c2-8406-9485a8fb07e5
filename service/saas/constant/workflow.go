package constant

// 工作流业务参数
const (
	WorkflowBusinessParamsOrganizationID            = "organizationId"
	WorkflowBusinessParamsSponsorID                 = "sponsorId"
	WorkflowBusinessParamsTenantID                  = "tenantId" // 租户ID
	WorkflowBusinessParamsWorkflowID                = "workflowId"
	WorkflowBusinessParamsWorkflowTemplateID        = "workflowTemplateId"        // 工作流模板ID
	WorkflowBusinessParamsWorkflowTemplateVersionID = "workflowTemplateVersionId" // 流程版本ID
	WorkflowNodeBusinessParamsCCIds                 = "ccIds"                     // 抄送人ID列表
	WorkflowNodeBusinessParamsCCKind                = "ccKind"                    // 抄送人类型
	WorkflowNodeBusinessParamsTimeout               = "timeout"                   // 超时时间
)

// 工作流事件类型
const (
	WorkflowEventPassed       = "passed"       // 通过
	WorkflowEventRejected     = "rejected"     // 拒绝
	WorkflowEventCanceled     = "canceled"     // 取消/撤销
	WorkflowEventStartSuccess = "startSuccess" // 启动成功
	WorkflowEventStartFailed  = "startFailed"  // 启动失败
)

// 模板节点类型
const (
	WorkflowTemplateNodeKindCustom     = "custom"     // 自定义类型节点
	WorkflowTemplateNodeKindDepartment = "department" // 部门类型节点
)

// 抄送人或审批人类型
const (
	WorkflowTemplateNodeReviewerKindDesignate      = "designate"       // 指定用户
	WorkflowTemplateNodeReviewerKindPosition       = "position"        // 岗位
	WorkflowTemplateNodeReviewerKindRole           = "role"            // 角色
	WorkflowTemplateNodeReviewerKindCustom         = "custom"          // 自定义
	WorkflowTemplateNodeReviewerKindGroupDesignate = "group_designate" // 集团指定
)
