apigo: # new the project api | 修改或新增api
	goctl api go -api ./service/saas/api/desc/all.api -dir ./service/saas/api  --style go_zero
	@echo "Generate Api Code successfully"

test: # Run test for the project | 运行项目测试
	go test -v --cover ./service/saas/api/internal/...
build:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -ldflags="-s -w" -o phoenixapi ./service/saas/api/phoenix.go

build-arm:
	CGO_ENABLED=0 GOOS=linux GOARCH=arm64 go build -ldflags="-s -w" -o phoenixapi ./service/saas/api/phoenix.go

# 部署到79
deploy79: build
	./goblin --name phoenixapi --host **************

# 部署到242
deploy242: build
	./goblin --name phoenixapi --host *************** --remote_path /data/project --supervisor=false


clean-api:
	rm -rf phoenixapi


ents:
	go run -mod=mod entgo.io/ent/cmd/ent generate --template glob="./service/saas/model/ent/template/*.tmpl" --feature intercept,sql/upsert ./service/saas/model/ent/schema

# 添加源码上游
add-source-upstream:
	git remote add upstream http://***************/xinghe-project-team/paperless-project/back-end/phoenix.git

grpcgo: 
	goctl rpc protoc ./service/saas/rpc/phoenix.proto --go_out=./service/saas/rpc/pb --go-grpc_out=./service/saas/rpc/pb --zrpc_out=./service/saas/rpc --style go_zero -m


# 部署到150
deploy150: build
	./goblin --name phoenixapi --host *************** --password Zj123456,,

grpcgoapi:
	protoc --go_out ./service/saas/adapter/grpc/pb --go-grpc_out ./service/saas/adapter/grpc/pb ./service/saas/protos/$(proto).proto		