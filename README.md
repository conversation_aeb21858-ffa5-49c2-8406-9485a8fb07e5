# Phoenix (SaaS 核心服务)

## 描述
该仓库包含 SaaS 平台的核心服务模块，命名为 `Phoenix`。它为整个 SaaS 生态系统提供基础功能，包括用户管理、认证、授权、文件处理以及与各种后端服务的集成。

## 功能
- **用户与组织管理**: 全面处理用户账户、角色和组织结构。
- **认证与授权**: 使用 JWT 进行安全的用户认证，并由 Casbin 提供细粒度的访问控制。
- **文件管理**: 集成 Minio 进行对象存储，支持文件上传、下载和安全访问。
- **API 与 RPC 服务**: 使用 `go-zero` 构建，提供高性能和可扩展的 API 和 gRPC 服务。
- **数据库集成**: 利用 Ent ORM 与 MySQL 进行健壮且类型安全的数据库交互。
- **消息队列**: 集成 Kafka 进行异步通信和事件处理。
- **验证码服务**: 内置验证码生成功能，增强安全性。
- **国际化 (i18n)**: 支持多种语言（由 `i18n` 目录暗示）。
- **SM4 加密**: 支持中国国家标准加密算法。
- **工作流管理**: 提供全面的工作流管理功能，包括：
  - **模板管理**: 预设模板的保存、列表、详情、绑定；工作流模板的更新、列表、版本管理、详情和启用。
  - **任务管理**: 待办、已办、发起、抄送任务的列表、详情、发起、审批、抄送已阅和撤销。
  - **流程监控**: 流程监控列表、节点审批人的添加和更新。

## 使用的技术
- **Go**: 编程语言
- **go-zero**: Web 和 RPC 框架
- **Ent**: Go 语言的实体框架 (ORM)
- **MySQL**: 主要数据库
- **Redis**: 缓存和会话管理
- **Apache Kafka**: 消息代理
- **Minio**: 对象存储
- **Etcd**: 服务发现
- **Casbin**: 授权库
- **JWT**: 用于认证的 JSON Web Tokens
- **goctl**: `go-zero` 的代码生成工具
## 前提条件
在开始之前，请确保您已安装以下软件：
- **Go**: 1.23.4 或更高版本。
- **Git**: 用于克隆仓库。

## 设置

### 1. 克隆仓库
```bash
git clone <repository_url>
cd phoenix-zhong-yi-testing # 或您的项目目录名称
```

### 2. 配置 Go 私有仓库环境
本项目使用私有 Go 模块。您需要配置 Go 环境以允许从 `gitlab.zhijiasoft.com` 获取模块。
```bash
go env -w GOPRIVATE="gitlab.zhijiasoft.com"
# 如果您遇到私有模块的 sumdb 验证问题，可能需要禁用它：
# go env -w GOSUMDB=off
```

### 3. 安装代码生成工具
安装 `goctl` (用于 go-zero 代码生成) 和 `ent` (用于 Ent ORM 代码生成)。
```bash
go install github.com/zeromicro/go-zero/tools/goctl@latest
go install entgo.io/ent/cmd/ent@latest
```

### 4. 安装 Go 依赖项
```bash
go mod tidy
```



## 开发工作流程

### 项目初始化
项目严重依赖代码生成。请按照以下步骤生成必要的代码：

#### 1. 生成 Ent 方法
修改 `model/ent/schema/*.go` 文件后：
```bash
make ents
# 或者对于 Windows：
# make ents-windows
```

#### 2. 生成 RPC 代码
修改 `service/saas/rpc/phoenix.proto` (或其他 `.proto` 文件) 后：
```bash
make grpcgo
```

#### 3. 生成 API 代码
修改 `service/saas/api/desc/all.api` (或其他 `.api` 文件) 后：
```bash
make apigo
```

#### 4. 生成 gRPC 适配器代码
修改 `service/saas/protos/*.proto` 文件后，用于生成适配器层的 gRPC 代码：
```bash
make grpcgoapi proto=<proto_file_name_without_extension>
```

#### 5. 生成 Swagger 文档
```bash
make gen-swagger
# 在本地提供 Swagger 服务：
# make serve-swagger
```


### 一般开发步骤
- 编写/修改 `.api` 文件以定义 API。
- 运行 `make apigo` 生成样板代码。
- 编写/修改 `model/ent/schema/*.go` 以进行数据库模式更改。
- 运行 `make ents` 生成 Ent ORM 代码。
- 在 API`internal/logic` 目录中实现业务逻辑。
- 根据需要调整 `service context`。

## 运行项目

### 构建 API 服务
```bash
make build
# 或者对于 ARM 架构：
# make build-arm
```


### 运行 API 服务
构建后，您可以运行可执行文件：
```bash
./phoenixapi
```
API 服务通常会在 `http://0.0.0.0:10011` 运行，如 `phoenix.yaml` 中配置。

## 部署

本项目使用 `goblin` 工具进行部署。请确保您已安装并配置 `goblin`。

### 部署到 192.168.110.79
```bash
make deploy79
```

### 部署到 192.168.110.242
```bash
make deploy242
```

### 部署到 192.168.110.150 
```bash
make deploy150
```



## 测试
运行项目测试：
```bash
make test
```
此命令运行 `service/saas/api/internal/...` 包的测试。您也可以使用 `go test` 运行特定测试。

## 实用命令

### 清理 API 构建产物
```bash
make clean-api
```

### 添加源码上游
```bash
make add-source-upstream
```

## API 响应格式

### 成功数据响应格式
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "id": 1,
    "name": "test",
    "age": 18
  }
}
```

### 错误数据响应格式
```json
{
  "code": 500,
  "msg": "error"
}
```

### 列表数据响应格式
```json
{
  "code": 0,
  "msg": "success",
  "data": {
    "data": [
      {
        "id": 1,
        "name": "test",
        "age": 18
      }
    ],
    "total": 1
  }
}
```

